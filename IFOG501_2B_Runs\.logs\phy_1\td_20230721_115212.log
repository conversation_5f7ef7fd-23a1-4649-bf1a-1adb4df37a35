============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 11:52:12 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1624 instances
RUN-0007 : 365 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2194 nets
RUN-1001 : 1637 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1622 instances, 365 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7782, tnet num: 2192, tinst num: 1622, tnode num: 11022, tedge num: 13164.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.279450s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 576134
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1622.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 549608, overlap = 20.25
PHY-3002 : Step(2): len = 441418, overlap = 15.75
PHY-3002 : Step(3): len = 366239, overlap = 20.25
PHY-3002 : Step(4): len = 347580, overlap = 18
PHY-3002 : Step(5): len = 338593, overlap = 18
PHY-3002 : Step(6): len = 326830, overlap = 20.25
PHY-3002 : Step(7): len = 319104, overlap = 20.25
PHY-3002 : Step(8): len = 313017, overlap = 20.25
PHY-3002 : Step(9): len = 305724, overlap = 20.25
PHY-3002 : Step(10): len = 297350, overlap = 20.25
PHY-3002 : Step(11): len = 292265, overlap = 20.25
PHY-3002 : Step(12): len = 286359, overlap = 20.25
PHY-3002 : Step(13): len = 279340, overlap = 20.25
PHY-3002 : Step(14): len = 273295, overlap = 20.25
PHY-3002 : Step(15): len = 268037, overlap = 20.25
PHY-3002 : Step(16): len = 262125, overlap = 20.25
PHY-3002 : Step(17): len = 256974, overlap = 20.25
PHY-3002 : Step(18): len = 251222, overlap = 20.25
PHY-3002 : Step(19): len = 245689, overlap = 20.25
PHY-3002 : Step(20): len = 240427, overlap = 20.25
PHY-3002 : Step(21): len = 235619, overlap = 20.25
PHY-3002 : Step(22): len = 229072, overlap = 20.25
PHY-3002 : Step(23): len = 223598, overlap = 20.25
PHY-3002 : Step(24): len = 219645, overlap = 20.25
PHY-3002 : Step(25): len = 214498, overlap = 20.25
PHY-3002 : Step(26): len = 208799, overlap = 20.25
PHY-3002 : Step(27): len = 205677, overlap = 20.25
PHY-3002 : Step(28): len = 200401, overlap = 20.25
PHY-3002 : Step(29): len = 191847, overlap = 20.25
PHY-3002 : Step(30): len = 186843, overlap = 20.25
PHY-3002 : Step(31): len = 184700, overlap = 20.25
PHY-3002 : Step(32): len = 176431, overlap = 20.25
PHY-3002 : Step(33): len = 144239, overlap = 20.25
PHY-3002 : Step(34): len = 139771, overlap = 20.25
PHY-3002 : Step(35): len = 137769, overlap = 20.25
PHY-3002 : Step(36): len = 132832, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000114535
PHY-3002 : Step(37): len = 133848, overlap = 6.75
PHY-3002 : Step(38): len = 132481, overlap = 9
PHY-3002 : Step(39): len = 130746, overlap = 13.5
PHY-3002 : Step(40): len = 127558, overlap = 11.25
PHY-3002 : Step(41): len = 120528, overlap = 4.5
PHY-3002 : Step(42): len = 119513, overlap = 4.5
PHY-3002 : Step(43): len = 115174, overlap = 9
PHY-3002 : Step(44): len = 114094, overlap = 9
PHY-3002 : Step(45): len = 111455, overlap = 4.5
PHY-3002 : Step(46): len = 110536, overlap = 6.75
PHY-3002 : Step(47): len = 107006, overlap = 11.25
PHY-3002 : Step(48): len = 98797.2, overlap = 9
PHY-3002 : Step(49): len = 95856.6, overlap = 9
PHY-3002 : Step(50): len = 95525, overlap = 11.25
PHY-3002 : Step(51): len = 92523.6, overlap = 11.25
PHY-3002 : Step(52): len = 91784.3, overlap = 6.75
PHY-3002 : Step(53): len = 89942.2, overlap = 4.5
PHY-3002 : Step(54): len = 87678.7, overlap = 9
PHY-3002 : Step(55): len = 85398, overlap = 9
PHY-3002 : Step(56): len = 84687.8, overlap = 9
PHY-3002 : Step(57): len = 82566.7, overlap = 6.75
PHY-3002 : Step(58): len = 80578.8, overlap = 6.75
PHY-3002 : Step(59): len = 79322.7, overlap = 6.75
PHY-3002 : Step(60): len = 77778.7, overlap = 11.25
PHY-3002 : Step(61): len = 76833.7, overlap = 13.5
PHY-3002 : Step(62): len = 75956.4, overlap = 4.5
PHY-3002 : Step(63): len = 72528.9, overlap = 4.5
PHY-3002 : Step(64): len = 71568.6, overlap = 4.5
PHY-3002 : Step(65): len = 69932.5, overlap = 9
PHY-3002 : Step(66): len = 69136.2, overlap = 9
PHY-3002 : Step(67): len = 67960, overlap = 11.25
PHY-3002 : Step(68): len = 67385.9, overlap = 11.25
PHY-3002 : Step(69): len = 67179.7, overlap = 11.25
PHY-3002 : Step(70): len = 66433.5, overlap = 4.5
PHY-3002 : Step(71): len = 65426.8, overlap = 9
PHY-3002 : Step(72): len = 64998.4, overlap = 11.25
PHY-3002 : Step(73): len = 64896.3, overlap = 6.75
PHY-3002 : Step(74): len = 64528.7, overlap = 6.75
PHY-3002 : Step(75): len = 63355.2, overlap = 6.75
PHY-3002 : Step(76): len = 62204.2, overlap = 11.25
PHY-3002 : Step(77): len = 61814.5, overlap = 11.25
PHY-3002 : Step(78): len = 61447.2, overlap = 6.75
PHY-3002 : Step(79): len = 60974.9, overlap = 6.75
PHY-3002 : Step(80): len = 59684.1, overlap = 9
PHY-3002 : Step(81): len = 58534, overlap = 9
PHY-3002 : Step(82): len = 58296.7, overlap = 6.75
PHY-3002 : Step(83): len = 58051.8, overlap = 6.75
PHY-3002 : Step(84): len = 57565.7, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00022907
PHY-3002 : Step(85): len = 57503.7, overlap = 6.75
PHY-3002 : Step(86): len = 57544.2, overlap = 6.75
PHY-3002 : Step(87): len = 57550, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00045814
PHY-3002 : Step(88): len = 57561.1, overlap = 6.75
PHY-3002 : Step(89): len = 57547.8, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005936s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (263.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063827s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(90): len = 60504.8, overlap = 6.3125
PHY-3002 : Step(91): len = 59395.3, overlap = 5.625
PHY-3002 : Step(92): len = 59072, overlap = 5.5
PHY-3002 : Step(93): len = 58359.4, overlap = 4.9375
PHY-3002 : Step(94): len = 57623.6, overlap = 4.875
PHY-3002 : Step(95): len = 56525.6, overlap = 4.625
PHY-3002 : Step(96): len = 55595.4, overlap = 4.25
PHY-3002 : Step(97): len = 54725.7, overlap = 3.8125
PHY-3002 : Step(98): len = 53650.4, overlap = 3.8125
PHY-3002 : Step(99): len = 52604.1, overlap = 3.34375
PHY-3002 : Step(100): len = 52091.1, overlap = 3.3125
PHY-3002 : Step(101): len = 51487.7, overlap = 3.1875
PHY-3002 : Step(102): len = 50617.6, overlap = 3
PHY-3002 : Step(103): len = 49700.5, overlap = 1.9375
PHY-3002 : Step(104): len = 49486.4, overlap = 2.625
PHY-3002 : Step(105): len = 48815.9, overlap = 2.3125
PHY-3002 : Step(106): len = 47912.3, overlap = 3.0625
PHY-3002 : Step(107): len = 46882.9, overlap = 3.25
PHY-3002 : Step(108): len = 46627, overlap = 3.5625
PHY-3002 : Step(109): len = 46330.7, overlap = 3.9375
PHY-3002 : Step(110): len = 45578, overlap = 12.625
PHY-3002 : Step(111): len = 44853.9, overlap = 14.625
PHY-3002 : Step(112): len = 43966.7, overlap = 16.1875
PHY-3002 : Step(113): len = 43660, overlap = 17.3438
PHY-3002 : Step(114): len = 43268, overlap = 17.4062
PHY-3002 : Step(115): len = 43013, overlap = 18.0938
PHY-3002 : Step(116): len = 42831.7, overlap = 17.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00011262
PHY-3002 : Step(117): len = 42626.7, overlap = 18.0312
PHY-3002 : Step(118): len = 42647.3, overlap = 18.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00022524
PHY-3002 : Step(119): len = 42661.8, overlap = 17.9375
PHY-3002 : Step(120): len = 42715.5, overlap = 17.9062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063772s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.82292e-05
PHY-3002 : Step(121): len = 42603.3, overlap = 70.5312
PHY-3002 : Step(122): len = 43146.6, overlap = 69.2188
PHY-3002 : Step(123): len = 43455.4, overlap = 68.5625
PHY-3002 : Step(124): len = 43630.5, overlap = 66.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.64584e-05
PHY-3002 : Step(125): len = 43656.1, overlap = 66.8438
PHY-3002 : Step(126): len = 44959.6, overlap = 65.4375
PHY-3002 : Step(127): len = 45143, overlap = 64.6875
PHY-3002 : Step(128): len = 45069.4, overlap = 64.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000112917
PHY-3002 : Step(129): len = 45434.5, overlap = 55.9688
PHY-3002 : Step(130): len = 45690.5, overlap = 56.1562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000202076
PHY-3002 : Step(131): len = 46172.2, overlap = 50.1875
PHY-3002 : Step(132): len = 47268.3, overlap = 48.6562
PHY-3002 : Step(133): len = 48529.8, overlap = 43.7812
PHY-3002 : Step(134): len = 48341, overlap = 43.5312
PHY-3002 : Step(135): len = 48073.4, overlap = 44.2812
PHY-3002 : Step(136): len = 47954.7, overlap = 41.875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000404152
PHY-3002 : Step(137): len = 47947.1, overlap = 41.5938
PHY-3002 : Step(138): len = 48726.3, overlap = 36.1562
PHY-3002 : Step(139): len = 49680, overlap = 29.5312
PHY-3002 : Step(140): len = 49611.5, overlap = 29.5312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7782, tnet num: 2192, tinst num: 1622, tnode num: 11022, tedge num: 13164.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 87.91 peak overflow 3.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2194.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52424, over cnt = 231(0%), over = 942, worst = 17
PHY-1001 : End global iterations;  0.099199s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.5%)

PHY-1001 : Congestion index: top1 = 40.93, top5 = 24.72, top10 = 16.10, top15 = 11.46.
PHY-1001 : End incremental global routing;  0.150452s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (83.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069240s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1583 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 1628 instances, 365 luts, 991 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 49657.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7806, tnet num: 2198, tinst num: 1628, tnode num: 11064, tedge num: 13200.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.313958s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(141): len = 49808, overlap = 0.5
PHY-3002 : Step(142): len = 49865.5, overlap = 0.6875
PHY-3002 : Step(143): len = 49880.1, overlap = 0.6875
PHY-3002 : Step(144): len = 49899.1, overlap = 0.5
PHY-3002 : Step(145): len = 49899.1, overlap = 0.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064207s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (73.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000246674
PHY-3002 : Step(146): len = 49909.7, overlap = 29.6562
PHY-3002 : Step(147): len = 49909.7, overlap = 29.6562
PHY-3001 : Final: Len = 49909.7, Over = 29.6562
PHY-3001 : End incremental placement;  0.482254s wall, 0.453125s user + 0.093750s system = 0.546875s CPU (113.4%)

OPT-1001 : Total overflow 88.16 peak overflow 3.41
OPT-1001 : End high-fanout net optimization;  0.736551s wall, 0.687500s user + 0.109375s system = 0.796875s CPU (108.2%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1680/2200.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52720, over cnt = 231(0%), over = 940, worst = 17
PHY-1002 : len = 59008, over cnt = 142(0%), over = 293, worst = 10
PHY-1002 : len = 61944, over cnt = 58(0%), over = 65, worst = 3
PHY-1002 : len = 62608, over cnt = 24(0%), over = 26, worst = 2
PHY-1002 : len = 63216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.086960s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (125.8%)

PHY-1001 : Congestion index: top1 = 37.52, top5 = 24.61, top10 = 17.80, top15 = 13.17.
OPT-1001 : End congestion update;  0.130427s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (119.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061529s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.194934s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (112.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 220.
OPT-1001 : End physical optimization;  1.212260s wall, 1.281250s user + 0.109375s system = 1.390625s CPU (114.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 365 LUT to BLE ...
SYN-4008 : Packed 365 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 809 remaining SEQ's ...
SYN-4005 : Packed 99 SEQ with LUT/SLICE
SYN-4006 : 103 single LUT's are left
SYN-4006 : 710 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1075/1407 primitive instances ...
PHY-3001 : End packing;  0.051510s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-1001 : 1479 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50011.4, Over = 57
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2031, tinst num: 841, tnode num: 8947, tedge num: 11587.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.320478s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (102.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.52174e-05
PHY-3002 : Step(148): len = 49358.6, overlap = 57.75
PHY-3002 : Step(149): len = 49279.1, overlap = 59
PHY-3002 : Step(150): len = 48758.8, overlap = 60.75
PHY-3002 : Step(151): len = 48714.4, overlap = 61
PHY-3002 : Step(152): len = 48437.9, overlap = 62
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.04348e-05
PHY-3002 : Step(153): len = 49155.4, overlap = 60
PHY-3002 : Step(154): len = 49594.7, overlap = 59
PHY-3002 : Step(155): len = 49707.9, overlap = 57.75
PHY-3002 : Step(156): len = 50336.2, overlap = 55.25
PHY-3002 : Step(157): len = 50903.8, overlap = 53
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00010087
PHY-3002 : Step(158): len = 51018.8, overlap = 51.75
PHY-3002 : Step(159): len = 51406.9, overlap = 49.25
PHY-3002 : Step(160): len = 52242.4, overlap = 48.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.137296s wall, 0.078125s user + 0.078125s system = 0.156250s CPU (113.8%)

PHY-3001 : Trial Legalized: Len = 64464.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.053419s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000334323
PHY-3002 : Step(161): len = 62249.6, overlap = 5.25
PHY-3002 : Step(162): len = 60671.7, overlap = 9
PHY-3002 : Step(163): len = 59248.9, overlap = 15.25
PHY-3002 : Step(164): len = 58394.4, overlap = 21
PHY-3002 : Step(165): len = 57896.7, overlap = 23.5
PHY-3002 : Step(166): len = 57567.1, overlap = 24.5
PHY-3002 : Step(167): len = 57200.1, overlap = 25.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000668647
PHY-3002 : Step(168): len = 57790.7, overlap = 25.75
PHY-3002 : Step(169): len = 57964.3, overlap = 25.5
PHY-3002 : Step(170): len = 58101.1, overlap = 26.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00133729
PHY-3002 : Step(171): len = 58305.4, overlap = 26.5
PHY-3002 : Step(172): len = 58533, overlap = 26.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005656s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62868.9, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005759s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 1, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 63040.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2031, tinst num: 841, tnode num: 8947, tedge num: 11587.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 125/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70104, over cnt = 146(0%), over = 233, worst = 8
PHY-1002 : len = 70640, over cnt = 96(0%), over = 115, worst = 4
PHY-1002 : len = 71984, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 72096, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 72368, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125089s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (162.4%)

PHY-1001 : Congestion index: top1 = 33.38, top5 = 24.05, top10 = 18.66, top15 = 14.65.
PHY-1001 : End incremental global routing;  0.177418s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (149.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062125s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.271927s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (132.2%)

OPT-1001 : Current memory(MB): used = 219, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1818/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72368, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006826s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.38, top5 = 24.05, top10 = 18.66, top15 = 14.65.
OPT-1001 : End congestion update;  0.054477s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052586s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.108745s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (100.6%)

OPT-1001 : Current memory(MB): used = 221, reserve = 186, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050114s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1818/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72368, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008053s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.38, top5 = 24.05, top10 = 18.66, top15 = 14.65.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053012s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.840046s wall, 0.875000s user + 0.046875s system = 0.921875s CPU (109.7%)

RUN-1003 : finish command "place" in  6.077542s wall, 8.593750s user + 2.953125s system = 11.546875s CPU (190.0%)

RUN-1004 : used memory is 201 MB, reserved memory is 165 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-1001 : 1479 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2031, tinst num: 841, tnode num: 8947, tedge num: 11587.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68624, over cnt = 144(0%), over = 245, worst = 8
PHY-1002 : len = 69712, over cnt = 102(0%), over = 123, worst = 5
PHY-1002 : len = 71288, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 71464, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.147836s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (116.3%)

PHY-1001 : Congestion index: top1 = 32.82, top5 = 23.72, top10 = 18.40, top15 = 14.47.
PHY-1001 : End global routing;  0.199969s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (101.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 203, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.308291s wall, 3.265625s user + 0.046875s system = 3.312500s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34984, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.350460s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 534.
PHY-1001 : End phase 1; 1.356744s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180536, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End initial routed; 1.028891s wall, 1.750000s user + 0.093750s system = 1.843750s CPU (179.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1798(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.443   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.379160s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 537, reserve = 506, peak = 537.
PHY-1001 : End phase 2; 1.408139s wall, 2.125000s user + 0.093750s system = 2.218750s CPU (157.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180536, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014421s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180568, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023828s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (262.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180608, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021814s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (214.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1798(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.443   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.371682s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (96.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.182015s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.0%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.735981s wall, 0.765625s user + 0.031250s system = 0.796875s CPU (108.3%)

PHY-1003 : Routed, final wirelength = 180608
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.009975s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (156.6%)

PHY-1001 : End detail routing;  7.006393s wall, 7.671875s user + 0.218750s system = 7.890625s CPU (112.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2031, tinst num: 841, tnode num: 8947, tedge num: 11587.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.939109s wall, 8.593750s user + 0.234375s system = 8.828125s CPU (111.2%)

RUN-1004 : used memory is 504 MB, reserved memory is 473 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      823   out of  19600    4.20%
#reg                     1080   out of  19600    5.51%
#le                      1533
  #lut only               453   out of   1533   29.55%
  #reg only               710   out of   1533   46.31%
  #lut&reg                370   out of   1533   24.14%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         473
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1533   |598     |225     |1111    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1133   |296     |132     |930     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |27      |6       |27      |0       |0       |
|    demodu                  |Demodulation                                     |533    |108     |57      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |14      |0       |30      |0       |0       |
|    integ                   |Integration                                      |136    |29      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |93     |33      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |85      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |106    |94      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |23     |20      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |48     |46      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1443  
    #2          2       310   
    #3          3       112   
    #4          4        13   
    #5        5-10       83   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2031, tinst num: 841, tnode num: 8947, tedge num: 11587.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 841
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2033, pip num: 14707
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1297 valid insts, and 39051 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.116811s wall, 17.484375s user + 0.109375s system = 17.593750s CPU (564.5%)

RUN-1004 : used memory is 545 MB, reserved memory is 514 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_115212.log"
