============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 15:16:36 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1628 instances
RUN-0007 : 369 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2198 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1642 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1626 instances, 369 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7793, tnet num: 2196, tinst num: 1626, tnode num: 11033, tedge num: 13179.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.274907s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (96.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 578148
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1626.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 551705, overlap = 20.25
PHY-3002 : Step(2): len = 441452, overlap = 13.5
PHY-3002 : Step(3): len = 366932, overlap = 15.75
PHY-3002 : Step(4): len = 348684, overlap = 15.75
PHY-3002 : Step(5): len = 339758, overlap = 15.75
PHY-3002 : Step(6): len = 329609, overlap = 18
PHY-3002 : Step(7): len = 322178, overlap = 20.25
PHY-3002 : Step(8): len = 315198, overlap = 20.25
PHY-3002 : Step(9): len = 308774, overlap = 20.25
PHY-3002 : Step(10): len = 301342, overlap = 20.25
PHY-3002 : Step(11): len = 295053, overlap = 20.25
PHY-3002 : Step(12): len = 289703, overlap = 20.25
PHY-3002 : Step(13): len = 282688, overlap = 20.25
PHY-3002 : Step(14): len = 276703, overlap = 20.25
PHY-3002 : Step(15): len = 271842, overlap = 20.25
PHY-3002 : Step(16): len = 266299, overlap = 20.25
PHY-3002 : Step(17): len = 258544, overlap = 20.25
PHY-3002 : Step(18): len = 253416, overlap = 20.25
PHY-3002 : Step(19): len = 249724, overlap = 20.25
PHY-3002 : Step(20): len = 237903, overlap = 20.25
PHY-3002 : Step(21): len = 230810, overlap = 20.25
PHY-3002 : Step(22): len = 228468, overlap = 20.25
PHY-3002 : Step(23): len = 220536, overlap = 20.25
PHY-3002 : Step(24): len = 193123, overlap = 20.25
PHY-3002 : Step(25): len = 189559, overlap = 20.25
PHY-3002 : Step(26): len = 187421, overlap = 20.25
PHY-3002 : Step(27): len = 159828, overlap = 18
PHY-3002 : Step(28): len = 155117, overlap = 20.25
PHY-3002 : Step(29): len = 153238, overlap = 20.25
PHY-3002 : Step(30): len = 147820, overlap = 20.25
PHY-3002 : Step(31): len = 142553, overlap = 20.25
PHY-3002 : Step(32): len = 140267, overlap = 20.25
PHY-3002 : Step(33): len = 137960, overlap = 20.25
PHY-3002 : Step(34): len = 135046, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113371
PHY-3002 : Step(35): len = 136001, overlap = 6.75
PHY-3002 : Step(36): len = 134247, overlap = 9
PHY-3002 : Step(37): len = 132072, overlap = 15.75
PHY-3002 : Step(38): len = 130572, overlap = 13.5
PHY-3002 : Step(39): len = 127707, overlap = 6.75
PHY-3002 : Step(40): len = 125734, overlap = 2.25
PHY-3002 : Step(41): len = 121426, overlap = 9
PHY-3002 : Step(42): len = 119839, overlap = 6.75
PHY-3002 : Step(43): len = 117156, overlap = 2.25
PHY-3002 : Step(44): len = 114624, overlap = 2.25
PHY-3002 : Step(45): len = 110005, overlap = 9
PHY-3002 : Step(46): len = 109330, overlap = 9
PHY-3002 : Step(47): len = 106287, overlap = 6.75
PHY-3002 : Step(48): len = 103337, overlap = 4.5
PHY-3002 : Step(49): len = 100714, overlap = 4.5
PHY-3002 : Step(50): len = 100142, overlap = 9
PHY-3002 : Step(51): len = 98224, overlap = 9
PHY-3002 : Step(52): len = 96046.1, overlap = 6.75
PHY-3002 : Step(53): len = 92186.2, overlap = 6.75
PHY-3002 : Step(54): len = 91219.1, overlap = 6.75
PHY-3002 : Step(55): len = 89803.5, overlap = 11.25
PHY-3002 : Step(56): len = 88167.2, overlap = 9
PHY-3002 : Step(57): len = 86332.3, overlap = 4.5
PHY-3002 : Step(58): len = 81540.5, overlap = 6.75
PHY-3002 : Step(59): len = 79043.4, overlap = 4.5
PHY-3002 : Step(60): len = 77948.2, overlap = 2.25
PHY-3002 : Step(61): len = 76754.7, overlap = 9
PHY-3002 : Step(62): len = 76078.1, overlap = 9
PHY-3002 : Step(63): len = 75703, overlap = 6.75
PHY-3002 : Step(64): len = 75551.4, overlap = 6.75
PHY-3002 : Step(65): len = 73683.5, overlap = 11.25
PHY-3002 : Step(66): len = 72571.8, overlap = 11.25
PHY-3002 : Step(67): len = 72351.7, overlap = 11.25
PHY-3002 : Step(68): len = 71777.1, overlap = 6.75
PHY-3002 : Step(69): len = 71160, overlap = 6.75
PHY-3002 : Step(70): len = 70707.5, overlap = 9
PHY-3002 : Step(71): len = 69241.6, overlap = 9
PHY-3002 : Step(72): len = 67876.2, overlap = 6.75
PHY-3002 : Step(73): len = 65010.2, overlap = 4.5
PHY-3002 : Step(74): len = 63761.3, overlap = 9
PHY-3002 : Step(75): len = 62653.2, overlap = 11.25
PHY-3002 : Step(76): len = 62137.2, overlap = 9
PHY-3002 : Step(77): len = 61743.3, overlap = 6.75
PHY-3002 : Step(78): len = 61195.6, overlap = 6.75
PHY-3002 : Step(79): len = 60174.6, overlap = 9
PHY-3002 : Step(80): len = 59807.8, overlap = 9
PHY-3002 : Step(81): len = 59451.7, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000226742
PHY-3002 : Step(82): len = 59666.9, overlap = 6.75
PHY-3002 : Step(83): len = 59668.6, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000453484
PHY-3002 : Step(84): len = 59471, overlap = 6.75
PHY-3002 : Step(85): len = 59365.2, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007257s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070643s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(86): len = 62147.8, overlap = 5.53125
PHY-3002 : Step(87): len = 61127.4, overlap = 6.3125
PHY-3002 : Step(88): len = 60722.1, overlap = 5.59375
PHY-3002 : Step(89): len = 59786.4, overlap = 5.03125
PHY-3002 : Step(90): len = 58980.6, overlap = 5
PHY-3002 : Step(91): len = 57721.8, overlap = 5.125
PHY-3002 : Step(92): len = 56517.6, overlap = 4.875
PHY-3002 : Step(93): len = 55809.4, overlap = 5.3125
PHY-3002 : Step(94): len = 54165.9, overlap = 4.5625
PHY-3002 : Step(95): len = 53221, overlap = 4.375
PHY-3002 : Step(96): len = 52320.2, overlap = 4.5
PHY-3002 : Step(97): len = 51559.5, overlap = 4.1875
PHY-3002 : Step(98): len = 50738.6, overlap = 3.3125
PHY-3002 : Step(99): len = 49940, overlap = 3.5625
PHY-3002 : Step(100): len = 49346.4, overlap = 3.625
PHY-3002 : Step(101): len = 48808.6, overlap = 3.6875
PHY-3002 : Step(102): len = 48003.7, overlap = 4.4375
PHY-3002 : Step(103): len = 47571.5, overlap = 4.5
PHY-3002 : Step(104): len = 47316.3, overlap = 4.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000655706
PHY-3002 : Step(105): len = 47257.6, overlap = 4.5
PHY-3002 : Step(106): len = 47247.3, overlap = 4.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00131141
PHY-3002 : Step(107): len = 47195.2, overlap = 4.6875
PHY-3002 : Step(108): len = 47195.2, overlap = 4.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063154s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.27569e-05
PHY-3002 : Step(109): len = 47347.2, overlap = 49.0938
PHY-3002 : Step(110): len = 47600.8, overlap = 47.0625
PHY-3002 : Step(111): len = 48064.8, overlap = 45.2188
PHY-3002 : Step(112): len = 48245.2, overlap = 45.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000145514
PHY-3002 : Step(113): len = 48255, overlap = 45.1562
PHY-3002 : Step(114): len = 48288.8, overlap = 44.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000288174
PHY-3002 : Step(115): len = 48879.5, overlap = 43.5312
PHY-3002 : Step(116): len = 49354, overlap = 41.25
PHY-3002 : Step(117): len = 51334.1, overlap = 37.875
PHY-3002 : Step(118): len = 52117.8, overlap = 37.0625
PHY-3002 : Step(119): len = 52085, overlap = 33.9375
PHY-3002 : Step(120): len = 51853.8, overlap = 31.9375
PHY-3002 : Step(121): len = 51266.7, overlap = 31
PHY-3002 : Step(122): len = 51023.2, overlap = 32.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000576347
PHY-3002 : Step(123): len = 51645.8, overlap = 30.625
PHY-3002 : Step(124): len = 51600.8, overlap = 30.6875
PHY-3002 : Step(125): len = 51600.8, overlap = 30.6875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00115269
PHY-3002 : Step(126): len = 51956.7, overlap = 30.4062
PHY-3002 : Step(127): len = 52231.6, overlap = 29.875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00230539
PHY-3002 : Step(128): len = 52636.6, overlap = 29.2812
PHY-3002 : Step(129): len = 52745.4, overlap = 29.5
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00461078
PHY-3002 : Step(130): len = 53215.9, overlap = 28.9375
PHY-3002 : Step(131): len = 53621.7, overlap = 29.625
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.00922156
PHY-3002 : Step(132): len = 53477.8, overlap = 28.7188
PHY-3002 : Step(133): len = 53477.8, overlap = 28.7188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7793, tnet num: 2196, tinst num: 1626, tnode num: 11033, tedge num: 13179.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.97 peak overflow 3.16
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57608, over cnt = 247(0%), over = 906, worst = 17
PHY-1001 : End global iterations;  0.058485s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (160.3%)

PHY-1001 : Congestion index: top1 = 40.45, top5 = 24.90, top10 = 16.90, top15 = 12.21.
PHY-1001 : End incremental global routing;  0.107836s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (144.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070850s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.209945s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (126.5%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1711/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57608, over cnt = 247(0%), over = 906, worst = 17
PHY-1002 : len = 61656, over cnt = 177(0%), over = 452, worst = 16
PHY-1002 : len = 65048, over cnt = 62(0%), over = 148, worst = 12
PHY-1002 : len = 66408, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 66584, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.075399s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (145.1%)

PHY-1001 : Congestion index: top1 = 35.26, top5 = 24.37, top10 = 18.02, top15 = 13.53.
OPT-1001 : End congestion update;  0.117440s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (119.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060504s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.180434s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.6%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.681806s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (110.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 117 SEQ with LUT/SLICE
SYN-4006 : 96 single LUT's are left
SYN-4006 : 686 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1055/1387 primitive instances ...
PHY-3001 : End packing;  0.046499s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 834 instances
RUN-1001 : 392 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1484 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 832 instances, 785 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53344.8, Over = 55.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2028, tinst num: 832, tnode num: 8913, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.306328s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (96.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.2918e-05
PHY-3002 : Step(134): len = 52160.6, overlap = 58.25
PHY-3002 : Step(135): len = 51764.6, overlap = 61.5
PHY-3002 : Step(136): len = 51171.9, overlap = 63
PHY-3002 : Step(137): len = 51054.3, overlap = 64
PHY-3002 : Step(138): len = 51042.5, overlap = 65.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.58359e-05
PHY-3002 : Step(139): len = 51577.9, overlap = 63.25
PHY-3002 : Step(140): len = 52007.7, overlap = 61.75
PHY-3002 : Step(141): len = 52387.2, overlap = 61
PHY-3002 : Step(142): len = 52702.5, overlap = 59.5
PHY-3002 : Step(143): len = 52932.7, overlap = 58.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000131672
PHY-3002 : Step(144): len = 53708.3, overlap = 57.75
PHY-3002 : Step(145): len = 54305.3, overlap = 56
PHY-3002 : Step(146): len = 55019, overlap = 55
PHY-3002 : Step(147): len = 55345.2, overlap = 52.75
PHY-3002 : Step(148): len = 55397.9, overlap = 53
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.086029s wall, 0.140625s user + 0.093750s system = 0.234375s CPU (272.4%)

PHY-3001 : Trial Legalized: Len = 69423.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050321s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (124.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000737418
PHY-3002 : Step(149): len = 66298.2, overlap = 6.25
PHY-3002 : Step(150): len = 63576.2, overlap = 11.25
PHY-3002 : Step(151): len = 62002.5, overlap = 15.75
PHY-3002 : Step(152): len = 60984.2, overlap = 22
PHY-3002 : Step(153): len = 60305.2, overlap = 25.25
PHY-3002 : Step(154): len = 59815.8, overlap = 27.75
PHY-3002 : Step(155): len = 59425.9, overlap = 30
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00147484
PHY-3002 : Step(156): len = 59766.7, overlap = 29.75
PHY-3002 : Step(157): len = 59795.3, overlap = 29.75
PHY-3002 : Step(158): len = 59810, overlap = 29.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00294967
PHY-3002 : Step(159): len = 59956, overlap = 29.75
PHY-3002 : Step(160): len = 60046.4, overlap = 29.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004981s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64299.1, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005770s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (270.8%)

PHY-3001 : 11 instances has been re-located, deltaX = 5, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 64441.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2028, tinst num: 832, tnode num: 8913, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 68/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70832, over cnt = 134(0%), over = 205, worst = 6
PHY-1002 : len = 71808, over cnt = 83(0%), over = 102, worst = 3
PHY-1002 : len = 72816, over cnt = 17(0%), over = 18, worst = 2
PHY-1002 : len = 73096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132258s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (141.8%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.78, top10 = 17.68, top15 = 13.97.
PHY-1001 : End incremental global routing;  0.186903s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (125.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059392s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.277003s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (118.5%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1792/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005960s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.78, top10 = 17.68, top15 = 13.97.
OPT-1001 : End congestion update;  0.050581s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053212s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.105540s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (103.6%)

OPT-1001 : Current memory(MB): used = 222, reserve = 187, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048167s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1792/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005745s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.78, top10 = 17.68, top15 = 13.97.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048276s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.844214s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (105.5%)

RUN-1003 : finish command "place" in  5.380147s wall, 8.875000s user + 2.578125s system = 11.453125s CPU (212.9%)

RUN-1004 : used memory is 203 MB, reserved memory is 167 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 834 instances
RUN-1001 : 392 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1484 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2028, tinst num: 832, tnode num: 8913, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 392 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70456, over cnt = 136(0%), over = 204, worst = 6
PHY-1002 : len = 71456, over cnt = 85(0%), over = 101, worst = 3
PHY-1002 : len = 72664, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 72760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118421s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (105.6%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.76, top10 = 17.65, top15 = 13.91.
PHY-1001 : End global routing;  0.166662s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 206, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 470, peak = 501.
PHY-1001 : End build detailed router design. 3.270834s wall, 3.203125s user + 0.046875s system = 3.250000s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33440, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.283690s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End phase 1; 1.289706s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (100.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 185768, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End initial routed; 1.088381s wall, 1.890625s user + 0.046875s system = 1.937500s CPU (178.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1794(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.153   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363420s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.2%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.451937s wall, 2.265625s user + 0.046875s system = 2.312500s CPU (159.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 185768, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014916s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 185776, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024309s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (64.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 185808, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.021904s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (142.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 185808, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.042334s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (110.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1794(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.153   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.370052s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.181019s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.9%)

PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End phase 3; 0.771945s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.2%)

PHY-1003 : Routed, final wirelength = 185808
PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End export database. 0.009757s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (160.1%)

PHY-1001 : End detail routing;  6.981035s wall, 7.718750s user + 0.109375s system = 7.828125s CPU (112.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2028, tinst num: 832, tnode num: 8913, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.865158s wall, 8.609375s user + 0.109375s system = 8.718750s CPU (110.9%)

RUN-1004 : used memory is 526 MB, reserved memory is 497 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      821   out of  19600    4.19%
#reg                     1074   out of  19600    5.48%
#le                      1507
  #lut only               433   out of   1507   28.73%
  #reg only               686   out of   1507   45.52%
  #lut&reg                388   out of   1507   25.75%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1507   |596     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1121   |308     |132     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |530    |123     |57      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |2       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |87     |30      |21      |83      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |96      |29      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |92     |80      |7       |48      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |19     |18      |0       |13      |0       |0       |
|    U2                      |Ctrl_Data                                        |38     |34      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |79      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1448  
    #2          2       308   
    #3          3       109   
    #4          4        13   
    #5        5-10       79   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6562, tnet num: 2028, tinst num: 832, tnode num: 8913, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 832
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2030, pip num: 14853
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1317 valid insts, and 39302 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.220533s wall, 18.078125s user + 0.156250s system = 18.234375s CPU (566.2%)

RUN-1004 : used memory is 549 MB, reserved memory is 519 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_151636.log"
