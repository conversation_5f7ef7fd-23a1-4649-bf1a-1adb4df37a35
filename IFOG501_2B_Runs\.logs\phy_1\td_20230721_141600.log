============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 14:16:00 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1633 instances
RUN-0007 : 374 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2203 nets
RUN-1001 : 1642 nets have 2 pins
RUN-1001 : 447 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1631 instances, 374 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7802, tnet num: 2201, tinst num: 1631, tnode num: 11042, tedge num: 13186.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.279382s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (95.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 575754
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1631.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 549107, overlap = 20.25
PHY-3002 : Step(2): len = 441116, overlap = 13.5
PHY-3002 : Step(3): len = 368207, overlap = 20.25
PHY-3002 : Step(4): len = 349408, overlap = 18
PHY-3002 : Step(5): len = 340322, overlap = 18
PHY-3002 : Step(6): len = 330062, overlap = 20.25
PHY-3002 : Step(7): len = 322462, overlap = 20.25
PHY-3002 : Step(8): len = 315799, overlap = 20.25
PHY-3002 : Step(9): len = 308325, overlap = 20.25
PHY-3002 : Step(10): len = 301022, overlap = 20.25
PHY-3002 : Step(11): len = 295720, overlap = 20.25
PHY-3002 : Step(12): len = 288330, overlap = 20.25
PHY-3002 : Step(13): len = 282624, overlap = 20.25
PHY-3002 : Step(14): len = 277695, overlap = 20.25
PHY-3002 : Step(15): len = 269829, overlap = 20.25
PHY-3002 : Step(16): len = 263636, overlap = 20.25
PHY-3002 : Step(17): len = 259548, overlap = 20.25
PHY-3002 : Step(18): len = 253024, overlap = 20.25
PHY-3002 : Step(19): len = 245696, overlap = 20.25
PHY-3002 : Step(20): len = 241595, overlap = 20.25
PHY-3002 : Step(21): len = 236419, overlap = 20.25
PHY-3002 : Step(22): len = 228699, overlap = 20.25
PHY-3002 : Step(23): len = 224295, overlap = 20.25
PHY-3002 : Step(24): len = 219986, overlap = 20.25
PHY-3002 : Step(25): len = 213818, overlap = 20.25
PHY-3002 : Step(26): len = 208662, overlap = 20.25
PHY-3002 : Step(27): len = 205467, overlap = 20.25
PHY-3002 : Step(28): len = 200274, overlap = 20.25
PHY-3002 : Step(29): len = 191525, overlap = 20.25
PHY-3002 : Step(30): len = 187655, overlap = 20.25
PHY-3002 : Step(31): len = 185289, overlap = 20.25
PHY-3002 : Step(32): len = 142903, overlap = 20.25
PHY-3002 : Step(33): len = 132358, overlap = 20.25
PHY-3002 : Step(34): len = 131169, overlap = 20.25
PHY-3002 : Step(35): len = 127008, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106577
PHY-3002 : Step(36): len = 129299, overlap = 6.75
PHY-3002 : Step(37): len = 128396, overlap = 6.75
PHY-3002 : Step(38): len = 126012, overlap = 11.25
PHY-3002 : Step(39): len = 122532, overlap = 11.25
PHY-3002 : Step(40): len = 119451, overlap = 6.75
PHY-3002 : Step(41): len = 114329, overlap = 2.25
PHY-3002 : Step(42): len = 113766, overlap = 4.5
PHY-3002 : Step(43): len = 110462, overlap = 11.25
PHY-3002 : Step(44): len = 106335, overlap = 11.25
PHY-3002 : Step(45): len = 104582, overlap = 11.25
PHY-3002 : Step(46): len = 103060, overlap = 6.75
PHY-3002 : Step(47): len = 100552, overlap = 9
PHY-3002 : Step(48): len = 98413.8, overlap = 9
PHY-3002 : Step(49): len = 95325.4, overlap = 6.75
PHY-3002 : Step(50): len = 94648, overlap = 6.75
PHY-3002 : Step(51): len = 93138.9, overlap = 9
PHY-3002 : Step(52): len = 91543.4, overlap = 9
PHY-3002 : Step(53): len = 88599.9, overlap = 9
PHY-3002 : Step(54): len = 87268.9, overlap = 11.25
PHY-3002 : Step(55): len = 85602.2, overlap = 11.25
PHY-3002 : Step(56): len = 84615.8, overlap = 6.75
PHY-3002 : Step(57): len = 82645.1, overlap = 6.75
PHY-3002 : Step(58): len = 81934.2, overlap = 9
PHY-3002 : Step(59): len = 80276.4, overlap = 11.25
PHY-3002 : Step(60): len = 79250.7, overlap = 9
PHY-3002 : Step(61): len = 76796.9, overlap = 9
PHY-3002 : Step(62): len = 75175.9, overlap = 11.25
PHY-3002 : Step(63): len = 74413.5, overlap = 6.75
PHY-3002 : Step(64): len = 73263.3, overlap = 11.25
PHY-3002 : Step(65): len = 70497.5, overlap = 11.25
PHY-3002 : Step(66): len = 69936.2, overlap = 11.25
PHY-3002 : Step(67): len = 68234.3, overlap = 9
PHY-3002 : Step(68): len = 67214.6, overlap = 6.75
PHY-3002 : Step(69): len = 66482.8, overlap = 6.75
PHY-3002 : Step(70): len = 65435.1, overlap = 9
PHY-3002 : Step(71): len = 63979.2, overlap = 9
PHY-3002 : Step(72): len = 63237, overlap = 6.75
PHY-3002 : Step(73): len = 62059.9, overlap = 9
PHY-3002 : Step(74): len = 61157.7, overlap = 9
PHY-3002 : Step(75): len = 60782.3, overlap = 9
PHY-3002 : Step(76): len = 59311.4, overlap = 11.25
PHY-3002 : Step(77): len = 57998, overlap = 9
PHY-3002 : Step(78): len = 57356.3, overlap = 9
PHY-3002 : Step(79): len = 56575.3, overlap = 9
PHY-3002 : Step(80): len = 55982.8, overlap = 9
PHY-3002 : Step(81): len = 55719.9, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000213154
PHY-3002 : Step(82): len = 56005.3, overlap = 6.75
PHY-3002 : Step(83): len = 56071.1, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000426307
PHY-3002 : Step(84): len = 55970.4, overlap = 6.75
PHY-3002 : Step(85): len = 55977.6, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007223s wall, 0.015625s user + 0.046875s system = 0.062500s CPU (865.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.073003s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(86): len = 59285.9, overlap = 9.1875
PHY-3002 : Step(87): len = 58255.8, overlap = 9.25
PHY-3002 : Step(88): len = 57753, overlap = 9.4375
PHY-3002 : Step(89): len = 56758.9, overlap = 8.75
PHY-3002 : Step(90): len = 56479.3, overlap = 8.25
PHY-3002 : Step(91): len = 55377.7, overlap = 7.5625
PHY-3002 : Step(92): len = 54702.5, overlap = 7.28125
PHY-3002 : Step(93): len = 53667.9, overlap = 6.625
PHY-3002 : Step(94): len = 52873.3, overlap = 6.15625
PHY-3002 : Step(95): len = 52365.5, overlap = 5.34375
PHY-3002 : Step(96): len = 52132.1, overlap = 4.59375
PHY-3002 : Step(97): len = 51607.7, overlap = 4.09375
PHY-3002 : Step(98): len = 51257, overlap = 3.84375
PHY-3002 : Step(99): len = 51011.1, overlap = 3.59375
PHY-3002 : Step(100): len = 50690.6, overlap = 3.5625
PHY-3002 : Step(101): len = 49118.8, overlap = 4.34375
PHY-3002 : Step(102): len = 47625.3, overlap = 6.96875
PHY-3002 : Step(103): len = 47253.7, overlap = 7.28125
PHY-3002 : Step(104): len = 46914.8, overlap = 8.65625
PHY-3002 : Step(105): len = 46743.1, overlap = 8.34375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0003865
PHY-3002 : Step(106): len = 46647.8, overlap = 8.6875
PHY-3002 : Step(107): len = 46692.9, overlap = 8.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000772999
PHY-3002 : Step(108): len = 46657.3, overlap = 8.5625
PHY-3002 : Step(109): len = 46908.7, overlap = 7.59375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059085s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.19108e-05
PHY-3002 : Step(110): len = 47034, overlap = 50.4062
PHY-3002 : Step(111): len = 47564.5, overlap = 50.3438
PHY-3002 : Step(112): len = 48184.9, overlap = 50.125
PHY-3002 : Step(113): len = 48387, overlap = 49.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000143822
PHY-3002 : Step(114): len = 48496.1, overlap = 49.4688
PHY-3002 : Step(115): len = 48507.9, overlap = 48.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000255778
PHY-3002 : Step(116): len = 49179.3, overlap = 47.5
PHY-3002 : Step(117): len = 49488.8, overlap = 46.4688
PHY-3002 : Step(118): len = 51771.8, overlap = 43.2188
PHY-3002 : Step(119): len = 52390.1, overlap = 42.0625
PHY-3002 : Step(120): len = 51944.6, overlap = 41.5625
PHY-3002 : Step(121): len = 51326.7, overlap = 38.0312
PHY-3002 : Step(122): len = 50814.7, overlap = 33.625
PHY-3002 : Step(123): len = 50400.6, overlap = 30.25
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7802, tnet num: 2201, tinst num: 1631, tnode num: 11042, tedge num: 13186.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 95.25 peak overflow 2.47
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2203.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53928, over cnt = 258(0%), over = 1013, worst = 16
PHY-1001 : End global iterations;  0.057242s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.2%)

PHY-1001 : Congestion index: top1 = 41.79, top5 = 24.87, top10 = 16.14, top15 = 11.46.
PHY-1001 : End incremental global routing;  0.109073s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (114.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069998s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.209460s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (111.9%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1672/2203.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53928, over cnt = 258(0%), over = 1013, worst = 16
PHY-1002 : len = 59600, over cnt = 179(0%), over = 466, worst = 12
PHY-1002 : len = 64360, over cnt = 48(0%), over = 60, worst = 4
PHY-1002 : len = 65184, over cnt = 12(0%), over = 16, worst = 4
PHY-1002 : len = 65504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.092310s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (118.5%)

PHY-1001 : Congestion index: top1 = 36.21, top5 = 25.16, top10 = 17.84, top15 = 13.13.
OPT-1001 : End congestion update;  0.134573s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057538s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.194708s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.3%)

OPT-1001 : Current memory(MB): used = 216, reserve = 181, peak = 216.
OPT-1001 : End physical optimization;  0.705232s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (104.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 103 SEQ with LUT/SLICE
SYN-4006 : 110 single LUT's are left
SYN-4006 : 700 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1074/1406 primitive instances ...
PHY-3001 : End packing;  0.047145s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2035 nets
RUN-1001 : 1484 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 839 instances, 792 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50677.8, Over = 62
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2033, tinst num: 839, tnode num: 8920, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.299440s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.9008e-05
PHY-3002 : Step(124): len = 50230.9, overlap = 65
PHY-3002 : Step(125): len = 49947.4, overlap = 66.5
PHY-3002 : Step(126): len = 49546.8, overlap = 68
PHY-3002 : Step(127): len = 49598.7, overlap = 68.75
PHY-3002 : Step(128): len = 49723.4, overlap = 67
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.8016e-05
PHY-3002 : Step(129): len = 49811.5, overlap = 67.25
PHY-3002 : Step(130): len = 49924.6, overlap = 67
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.24762e-05
PHY-3002 : Step(131): len = 50781.5, overlap = 66
PHY-3002 : Step(132): len = 51970.1, overlap = 62.25
PHY-3002 : Step(133): len = 52471.3, overlap = 61.5
PHY-3002 : Step(134): len = 52874.3, overlap = 63.75
PHY-3002 : Step(135): len = 53190.5, overlap = 60.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.100164s wall, 0.109375s user + 0.156250s system = 0.265625s CPU (265.2%)

PHY-3001 : Trial Legalized: Len = 68022.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054861s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000406558
PHY-3002 : Step(136): len = 65208.9, overlap = 4.75
PHY-3002 : Step(137): len = 62887.2, overlap = 11
PHY-3002 : Step(138): len = 61007.8, overlap = 14.75
PHY-3002 : Step(139): len = 59565.1, overlap = 19.5
PHY-3002 : Step(140): len = 58866.6, overlap = 23.75
PHY-3002 : Step(141): len = 58452.8, overlap = 27.25
PHY-3002 : Step(142): len = 58179.2, overlap = 30.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000813115
PHY-3002 : Step(143): len = 58674.5, overlap = 30.25
PHY-3002 : Step(144): len = 58833.5, overlap = 29.75
PHY-3002 : Step(145): len = 58803.3, overlap = 29.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00162623
PHY-3002 : Step(146): len = 58998.8, overlap = 29.75
PHY-3002 : Step(147): len = 59122.3, overlap = 29.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005019s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (311.3%)

PHY-3001 : Legalized: Len = 64062.8, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006214s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 2, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 64092.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2033, tinst num: 839, tnode num: 8920, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 134/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71264, over cnt = 135(0%), over = 208, worst = 7
PHY-1002 : len = 72072, over cnt = 86(0%), over = 102, worst = 3
PHY-1002 : len = 72976, over cnt = 25(0%), over = 29, worst = 3
PHY-1002 : len = 73432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110057s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (142.0%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.21, top10 = 18.23, top15 = 14.29.
PHY-1001 : End incremental global routing;  0.160828s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (126.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060184s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.250936s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (118.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1808/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73432, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005652s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.21, top10 = 18.23, top15 = 14.29.
OPT-1001 : End congestion update;  0.050510s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054766s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 801 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 839 instances, 792 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64155.8, Over = 0
PHY-3001 : End spreading;  0.004826s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (323.8%)

PHY-3001 : Final: Len = 64155.8, Over = 0
PHY-3001 : End incremental legalization;  0.033534s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.2%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150951s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.2%)

OPT-1001 : Current memory(MB): used = 223, reserve = 188, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049855s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (125.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1804/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.46, top5 = 23.20, top10 = 18.24, top15 = 14.30.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052774s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.857795s wall, 0.937500s user + 0.000000s system = 0.937500s CPU (109.3%)

RUN-1003 : finish command "place" in  5.092548s wall, 8.812500s user + 2.218750s system = 11.031250s CPU (216.6%)

RUN-1004 : used memory is 203 MB, reserved memory is 167 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2035 nets
RUN-1001 : 1484 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2033, tinst num: 839, tnode num: 8920, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69968, over cnt = 149(0%), over = 239, worst = 7
PHY-1002 : len = 71368, over cnt = 64(0%), over = 72, worst = 3
PHY-1002 : len = 72248, over cnt = 5(0%), over = 7, worst = 3
PHY-1002 : len = 72344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119414s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (143.9%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 22.85, top10 = 17.96, top15 = 14.09.
PHY-1001 : End global routing;  0.170876s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (128.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 206, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 499, reserve = 469, peak = 499.
PHY-1001 : End build detailed router design. 3.191312s wall, 3.140625s user + 0.062500s system = 3.203125s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34448, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.243872s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End phase 1; 1.249652s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184240, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End initial routed; 1.062004s wall, 1.781250s user + 0.078125s system = 1.859375s CPU (175.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.483   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.372027s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (96.6%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.434119s wall, 2.140625s user + 0.078125s system = 2.218750s CPU (154.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184240, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014846s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184288, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.022225s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (70.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184336, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.017803s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (87.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.483   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.384614s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.178109s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (105.3%)

PHY-1001 : Current memory(MB): used = 549, reserve = 519, peak = 549.
PHY-1001 : End phase 3; 0.739170s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (99.4%)

PHY-1003 : Routed, final wirelength = 184336
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.009297s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (168.1%)

PHY-1001 : End detail routing;  6.807502s wall, 7.453125s user + 0.156250s system = 7.609375s CPU (111.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2033, tinst num: 839, tnode num: 8920, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.706398s wall, 8.359375s user + 0.203125s system = 8.562500s CPU (111.1%)

RUN-1004 : used memory is 503 MB, reserved memory is 473 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      829   out of  19600    4.23%
#reg                     1074   out of  19600    5.48%
#le                      1529
  #lut only               455   out of   1529   29.76%
  #reg only               700   out of   1529   45.78%
  #lut&reg                374   out of   1529   24.46%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1529   |604     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1128   |301     |132     |919     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |20      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |531    |121     |57      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |32     |16      |0       |32      |0       |0       |
|    integ                   |Integration                                      |138    |18      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |91     |29      |21      |87      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |88      |29      |250     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |111    |99      |7       |53      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |22     |21      |0       |14      |0       |0       |
|    U2                      |Ctrl_Data                                        |54     |50      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |77      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1448  
    #2          2       313   
    #3          3       103   
    #4          4        20   
    #5        5-10       78   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2033, tinst num: 839, tnode num: 8920, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 839
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2035, pip num: 14741
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1281 valid insts, and 39066 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.054434s wall, 17.515625s user + 0.109375s system = 17.625000s CPU (577.0%)

RUN-1004 : used memory is 545 MB, reserved memory is 512 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_141600.log"
