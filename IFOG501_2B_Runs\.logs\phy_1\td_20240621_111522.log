============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 11:15:22 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-5007 WARNING: empty statement in sequential block in ../../Src_al/CtrlData.v(268)
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 25 trigger nets, 25 data nets.
KIT-1004 : Chipwatcher code = 1001011011010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 1831/10 useful/useless nets, 1061/5 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 1604/18 useful/useless nets, 1391/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1017 : Remove 2 const input seq instances
SYN-1002 :     cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[0]$bus_nodes/din_r1_reg
SYN-1002 :     cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[0]$bus_nodes/din_r2_reg
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1016 : Merged 1 instances.
SYN-1015 : Optimize round 1, 283 better
SYN-1014 : Optimize round 2
SYN-1032 : 1398/15 useful/useless nets, 1185/16 useful/useless insts
SYN-1016 : Merged 2 instances.
SYN-1015 : Optimize round 2, 42 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 1 better
SYN-1014 : Optimize round 2
SYN-1015 : Optimize round 2, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1419/156 useful/useless nets, 1228/30 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 207 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 6 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 1786/5 useful/useless nets, 1595/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6563, tnet num: 1786, tinst num: 1594, tnode num: 8289, tedge num: 10034.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1786 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 173 (3.55), #lev = 7 (1.75)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 171 (3.58), #lev = 6 (1.88)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 382 instances into 171 LUTs, name keeping = 73%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 277 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 WARNING: The kept net uart/Macc_data[14] will be merged to another kept net CtrlData/Macc_data[14]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (180 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1114 instances
RUN-0007 : 413 luts, 506 seqs, 97 mslices, 62 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1312 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 757 nets have 2 pins
RUN-1001 : 409 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 39 nets have [11 - 20] pins
RUN-1001 : 23 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     111     
RUN-1001 :   No   |  No   |  Yes  |     99      
RUN-1001 :   No   |  Yes  |  No   |     71      
RUN-1001 :   Yes  |  No   |  No   |     49      
RUN-1001 :   Yes  |  No   |  Yes  |     176     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   6   |     10     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 15
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1112 instances, 413 luts, 506 seqs, 159 slices, 24 macros(159 instances: 97 mslices 62 lslices)
PHY-0007 : Cell area utilization is 3%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5546, tnet num: 1310, tinst num: 1112, tnode num: 7281, tedge num: 9196.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.111869s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (97.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 334032
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1112.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 3%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 251740, overlap = 51.75
PHY-3002 : Step(2): len = 204056, overlap = 51.75
PHY-3002 : Step(3): len = 180726, overlap = 51.75
PHY-3002 : Step(4): len = 163040, overlap = 51.75
PHY-3002 : Step(5): len = 147043, overlap = 51.75
PHY-3002 : Step(6): len = 133224, overlap = 51.75
PHY-3002 : Step(7): len = 116209, overlap = 51.75
PHY-3002 : Step(8): len = 99844.2, overlap = 52.6875
PHY-3002 : Step(9): len = 90014.3, overlap = 52.5625
PHY-3002 : Step(10): len = 81416.5, overlap = 53.1875
PHY-3002 : Step(11): len = 70700.7, overlap = 54.1875
PHY-3002 : Step(12): len = 64368.3, overlap = 53.3125
PHY-3002 : Step(13): len = 57589.6, overlap = 55.9062
PHY-3002 : Step(14): len = 52793, overlap = 51.75
PHY-3002 : Step(15): len = 48011.3, overlap = 51.75
PHY-3002 : Step(16): len = 45379.8, overlap = 51.75
PHY-3002 : Step(17): len = 42719.3, overlap = 51.75
PHY-3002 : Step(18): len = 40676.6, overlap = 51.75
PHY-3002 : Step(19): len = 40114.5, overlap = 51.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.4452e-06
PHY-3002 : Step(20): len = 40947.5, overlap = 45
PHY-3002 : Step(21): len = 41973.8, overlap = 51.75
PHY-3002 : Step(22): len = 40178.1, overlap = 51.75
PHY-3002 : Step(23): len = 40150.7, overlap = 51.75
PHY-3002 : Step(24): len = 38508.5, overlap = 51.75
PHY-3002 : Step(25): len = 38448.3, overlap = 45
PHY-3002 : Step(26): len = 36643.6, overlap = 36
PHY-3002 : Step(27): len = 35187.2, overlap = 49.5
PHY-3002 : Step(28): len = 34384.4, overlap = 49.5
PHY-3002 : Step(29): len = 34047.8, overlap = 49.5
PHY-3002 : Step(30): len = 33285.1, overlap = 49.5
PHY-3002 : Step(31): len = 32534, overlap = 47.25
PHY-3002 : Step(32): len = 31655.8, overlap = 51.75
PHY-3002 : Step(33): len = 31000.8, overlap = 47.25
PHY-3002 : Step(34): len = 30634, overlap = 45
PHY-3002 : Step(35): len = 30158.8, overlap = 42.75
PHY-3002 : Step(36): len = 29618.7, overlap = 40.5
PHY-3002 : Step(37): len = 29241, overlap = 33.9375
PHY-3002 : Step(38): len = 28740.3, overlap = 34.0625
PHY-3002 : Step(39): len = 28353.4, overlap = 38.5625
PHY-3002 : Step(40): len = 28025.8, overlap = 36.3125
PHY-3002 : Step(41): len = 27547.4, overlap = 33.875
PHY-3002 : Step(42): len = 27248.1, overlap = 33.8125
PHY-3002 : Step(43): len = 27055.2, overlap = 33.75
PHY-3002 : Step(44): len = 26939.6, overlap = 33.8125
PHY-3002 : Step(45): len = 26548.7, overlap = 36
PHY-3002 : Step(46): len = 26216.5, overlap = 34.75
PHY-3002 : Step(47): len = 26313, overlap = 41.2188
PHY-3002 : Step(48): len = 25977, overlap = 44.4062
PHY-3002 : Step(49): len = 25672.3, overlap = 40.1562
PHY-3002 : Step(50): len = 25538.7, overlap = 42.1875
PHY-3002 : Step(51): len = 25241.9, overlap = 49.125
PHY-3002 : Step(52): len = 25138.5, overlap = 46.9688
PHY-3002 : Step(53): len = 25379.7, overlap = 47.7188
PHY-3002 : Step(54): len = 25128.7, overlap = 44.1875
PHY-3002 : Step(55): len = 24949.2, overlap = 44.1562
PHY-3002 : Step(56): len = 24730.8, overlap = 43.8125
PHY-3002 : Step(57): len = 24462, overlap = 46.1875
PHY-3002 : Step(58): len = 24227.2, overlap = 43.4375
PHY-3002 : Step(59): len = 24166.7, overlap = 44.1875
PHY-3002 : Step(60): len = 24080.3, overlap = 43.875
PHY-3002 : Step(61): len = 24043.6, overlap = 46.6875
PHY-3002 : Step(62): len = 23550.6, overlap = 45.9688
PHY-3002 : Step(63): len = 23257.5, overlap = 45.3438
PHY-3002 : Step(64): len = 23045.3, overlap = 47.5938
PHY-3002 : Step(65): len = 23235.6, overlap = 43.5625
PHY-3002 : Step(66): len = 22639.3, overlap = 41.5625
PHY-3002 : Step(67): len = 22439.2, overlap = 40.9062
PHY-3002 : Step(68): len = 22255.9, overlap = 41.2188
PHY-3002 : Step(69): len = 22259.1, overlap = 44.7188
PHY-3002 : Step(70): len = 21942.6, overlap = 45.3125
PHY-3002 : Step(71): len = 21669.3, overlap = 45.3125
PHY-3002 : Step(72): len = 21317.2, overlap = 45.5938
PHY-3002 : Step(73): len = 21174.5, overlap = 45.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.89041e-06
PHY-3002 : Step(74): len = 21214.6, overlap = 45.3125
PHY-3002 : Step(75): len = 21172.3, overlap = 45.125
PHY-3002 : Step(76): len = 21153, overlap = 45.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 1.77808e-05
PHY-3002 : Step(77): len = 21043.2, overlap = 45.0625
PHY-3002 : Step(78): len = 21010, overlap = 45.2188
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005425s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.030627s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.53884e-05
PHY-3002 : Step(79): len = 25161.8, overlap = 20.0938
PHY-3002 : Step(80): len = 25184.9, overlap = 20
PHY-3002 : Step(81): len = 25217.4, overlap = 20.125
PHY-3002 : Step(82): len = 25298.7, overlap = 17.125
PHY-3002 : Step(83): len = 25370.6, overlap = 17.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000190777
PHY-3002 : Step(84): len = 25289, overlap = 17.125
PHY-3002 : Step(85): len = 25289, overlap = 17.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000381554
PHY-3002 : Step(86): len = 25502.6, overlap = 16.6875
PHY-3002 : Step(87): len = 25502.6, overlap = 16.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.031068s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.7079e-05
PHY-3002 : Step(88): len = 25621.6, overlap = 45.7812
PHY-3002 : Step(89): len = 25705.3, overlap = 45.7188
PHY-3002 : Step(90): len = 26827.6, overlap = 32.4375
PHY-3002 : Step(91): len = 27302.8, overlap = 27.75
PHY-3002 : Step(92): len = 27285, overlap = 27
PHY-3002 : Step(93): len = 27345.2, overlap = 27.2188
PHY-3002 : Step(94): len = 27073.3, overlap = 27.7188
PHY-3002 : Step(95): len = 26946.4, overlap = 26.0938
PHY-3002 : Step(96): len = 26814.8, overlap = 23.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.4158e-05
PHY-3002 : Step(97): len = 26644.4, overlap = 24.5312
PHY-3002 : Step(98): len = 26620, overlap = 24.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000108316
PHY-3002 : Step(99): len = 26686.4, overlap = 25.0625
PHY-3002 : Step(100): len = 26686.4, overlap = 25.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000216632
PHY-3002 : Step(101): len = 27148.4, overlap = 23.4062
PHY-3002 : Step(102): len = 27283.3, overlap = 23.1562
PHY-3002 : Step(103): len = 27521.6, overlap = 22.4062
PHY-3002 : Step(104): len = 27646.2, overlap = 23.8125
PHY-3002 : Step(105): len = 27843.4, overlap = 23.5938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5546, tnet num: 1310, tinst num: 1112, tnode num: 7281, tedge num: 9196.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 60.19 peak overflow 2.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1312.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 34464, over cnt = 173(0%), over = 616, worst = 24
PHY-1001 : End global iterations;  0.053597s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (262.4%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 18.35, top10 = 11.42, top15 = 8.01.
PHY-1001 : End incremental global routing;  0.099070s wall, 0.109375s user + 0.062500s system = 0.171875s CPU (173.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1310 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.034162s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1100 has valid locations, 23 needs to be replaced
PHY-3001 : design contains 1134 instances, 413 luts, 528 seqs, 159 slices, 24 macros(159 instances: 97 mslices 62 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 28164.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5634, tnet num: 1332, tinst num: 1134, tnode num: 7435, tedge num: 9328.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1332 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.127322s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(106): len = 28146.9, overlap = 0.375
PHY-3002 : Step(107): len = 28146.9, overlap = 0.375
PHY-3002 : Step(108): len = 28222.7, overlap = 0.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1332 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.030231s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00144183
PHY-3002 : Step(109): len = 28240.6, overlap = 23.6562
PHY-3002 : Step(110): len = 28240.6, overlap = 23.6562
PHY-3001 : Final: Len = 28240.6, Over = 23.6562
PHY-3001 : End incremental placement;  0.227960s wall, 0.250000s user + 0.046875s system = 0.296875s CPU (130.2%)

OPT-1001 : Total overflow 60.94 peak overflow 2.41
OPT-1001 : End high-fanout net optimization;  0.386667s wall, 0.437500s user + 0.109375s system = 0.546875s CPU (141.4%)

OPT-1001 : Current memory(MB): used = 192, reserve = 145, peak = 192.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 892/1334.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 35080, over cnt = 175(0%), over = 626, worst = 24
PHY-1002 : len = 39032, over cnt = 114(0%), over = 223, worst = 13
PHY-1002 : len = 40856, over cnt = 48(0%), over = 81, worst = 7
PHY-1002 : len = 41232, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 41392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095661s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (98.0%)

PHY-1001 : Congestion index: top1 = 28.64, top5 = 18.83, top10 = 12.51, top15 = 9.00.
OPT-1001 : End congestion update;  0.136961s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (91.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1332 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.028846s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.3%)

OPT-0007 : Start: WNS 4043 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.165989s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (94.1%)

OPT-1001 : Current memory(MB): used = 190, reserve = 144, peak = 192.
OPT-1001 : End physical optimization;  0.656435s wall, 0.687500s user + 0.125000s system = 0.812500s CPU (123.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 413 LUT to BLE ...
SYN-4008 : Packed 413 LUT and 172 SEQ to BLE.
SYN-4003 : Packing 356 remaining SEQ's ...
SYN-4005 : Packed 164 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 192 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 605/932 primitive instances ...
PHY-3001 : End packing;  0.031169s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 528 instances
RUN-1001 : 246 mslices, 246 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1164 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 578 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 33 nets have [11 - 20] pins
RUN-1001 : 24 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 526 instances, 492 slices, 24 macros(159 instances: 97 mslices 62 lslices)
PHY-3001 : Cell area utilization is 6%
PHY-3001 : After packing: Len = 29125.2, Over = 32.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4853, tnet num: 1162, tinst num: 526, tnode num: 6163, tedge num: 8345.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.122173s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (102.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.58767e-05
PHY-3002 : Step(111): len = 28825.6, overlap = 33
PHY-3002 : Step(112): len = 28975.6, overlap = 33
PHY-3002 : Step(113): len = 29060.8, overlap = 32.5
PHY-3002 : Step(114): len = 29133, overlap = 32.25
PHY-3002 : Step(115): len = 29173.9, overlap = 30.25
PHY-3002 : Step(116): len = 29200.6, overlap = 29.25
PHY-3002 : Step(117): len = 29230.9, overlap = 29.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000111753
PHY-3002 : Step(118): len = 29162.3, overlap = 29.25
PHY-3002 : Step(119): len = 29253.7, overlap = 29.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000223507
PHY-3002 : Step(120): len = 29793.5, overlap = 29.75
PHY-3002 : Step(121): len = 30368.7, overlap = 28.75
PHY-3002 : Step(122): len = 30581.5, overlap = 27.25
PHY-3002 : Step(123): len = 30515.7, overlap = 24.75
PHY-3002 : Step(124): len = 30512.9, overlap = 26.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.069174s wall, 0.062500s user + 0.093750s system = 0.156250s CPU (225.9%)

PHY-3001 : Trial Legalized: Len = 39847.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.027080s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00427786
PHY-3002 : Step(125): len = 38309.2, overlap = 1.5
PHY-3002 : Step(126): len = 36768.7, overlap = 4
PHY-3002 : Step(127): len = 35671.3, overlap = 4.75
PHY-3002 : Step(128): len = 34447.2, overlap = 5.75
PHY-3002 : Step(129): len = 33929.5, overlap = 8.25
PHY-3002 : Step(130): len = 33216.1, overlap = 11
PHY-3002 : Step(131): len = 32934.2, overlap = 13.25
PHY-3002 : Step(132): len = 32687.7, overlap = 13.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00855572
PHY-3002 : Step(133): len = 32770.1, overlap = 14.25
PHY-3002 : Step(134): len = 32761.9, overlap = 14.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0171114
PHY-3002 : Step(135): len = 32760.9, overlap = 14.25
PHY-3002 : Step(136): len = 32697.5, overlap = 14.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004596s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (340.0%)

PHY-3001 : Legalized: Len = 36815.3, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.003967s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 36855.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4853, tnet num: 1162, tinst num: 526, tnode num: 6163, tedge num: 8345.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 44/1164.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 45256, over cnt = 132(0%), over = 213, worst = 6
PHY-1002 : len = 46096, over cnt = 68(0%), over = 87, worst = 3
PHY-1002 : len = 47016, over cnt = 19(0%), over = 22, worst = 2
PHY-1002 : len = 47208, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 47328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113306s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (110.3%)

PHY-1001 : Congestion index: top1 = 26.66, top5 = 19.42, top10 = 13.93, top15 = 10.13.
PHY-1001 : End incremental global routing;  0.158985s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (108.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032776s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (143.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.212905s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (110.1%)

OPT-1001 : Current memory(MB): used = 193, reserve = 147, peak = 193.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 999/1164.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 47328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003765s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 26.66, top5 = 19.42, top10 = 13.93, top15 = 10.13.
OPT-1001 : End congestion update;  0.044477s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.029670s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.3%)

OPT-0007 : Start: WNS 4027 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.074316s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.1%)

OPT-1001 : Current memory(MB): used = 195, reserve = 149, peak = 195.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.029218s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (107.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 999/1164.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 47328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003848s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 26.66, top5 = 19.42, top10 = 13.93, top15 = 10.13.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.031878s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (98.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4027 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 26.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4027ps with logic level 6 
OPT-1001 : End physical optimization;  0.513307s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (103.5%)

RUN-1003 : finish command "place" in  3.645742s wall, 5.625000s user + 2.109375s system = 7.734375s CPU (212.1%)

RUN-1004 : used memory is 186 MB, reserved memory is 140 MB, peak memory is 196 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 528 instances
RUN-1001 : 246 mslices, 246 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1164 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 578 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 33 nets have [11 - 20] pins
RUN-1001 : 24 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4853, tnet num: 1162, tinst num: 526, tnode num: 6163, tedge num: 8345.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 246 mslices, 246 lslices, 8 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 44640, over cnt = 133(0%), over = 215, worst = 6
PHY-1002 : len = 45384, over cnt = 90(0%), over = 121, worst = 6
PHY-1002 : len = 46576, over cnt = 15(0%), over = 21, worst = 3
PHY-1002 : len = 46896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.108648s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (115.1%)

PHY-1001 : Congestion index: top1 = 26.75, top5 = 19.42, top10 = 13.85, top15 = 10.05.
PHY-1001 : End global routing;  0.152142s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (113.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 214, reserve = 169, peak = 214.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 474, reserve = 433, peak = 474.
PHY-1001 : End build detailed router design. 3.132982s wall, 3.093750s user + 0.046875s system = 3.140625s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 25760, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.592577s wall, 0.578125s user + 0.015625s system = 0.593750s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 506, reserve = 466, peak = 506.
PHY-1001 : End phase 1; 0.598346s wall, 0.578125s user + 0.015625s system = 0.593750s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 86% nets.
PHY-1022 : len = 156504, over cnt = 47(0%), over = 47, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 507, reserve = 466, peak = 507.
PHY-1001 : End initial routed; 1.228400s wall, 1.578125s user + 0.109375s system = 1.687500s CPU (137.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1013(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.500   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.166367s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (93.9%)

PHY-1001 : Current memory(MB): used = 507, reserve = 466, peak = 507.
PHY-1001 : End phase 2; 1.394850s wall, 1.734375s user + 0.109375s system = 1.843750s CPU (132.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 156504, over cnt = 47(0%), over = 47, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.009168s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 156264, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029226s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (106.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 156304, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019030s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (82.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1013(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.500   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.158548s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 12 feed throughs used by 11 nets
PHY-1001 : End commit to database; 0.134269s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.7%)

PHY-1001 : Current memory(MB): used = 520, reserve = 479, peak = 520.
PHY-1001 : End phase 3; 0.458107s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (98.9%)

PHY-1003 : Routed, final wirelength = 156304
PHY-1001 : Current memory(MB): used = 520, reserve = 479, peak = 520.
PHY-1001 : End export database. 0.008640s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  5.769647s wall, 6.046875s user + 0.171875s system = 6.218750s CPU (107.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4853, tnet num: 1162, tinst num: 526, tnode num: 6163, tedge num: 8345.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  6.254627s wall, 6.546875s user + 0.171875s system = 6.718750s CPU (107.4%)

RUN-1004 : used memory is 478 MB, reserved memory is 437 MB, peak memory is 520 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      744   out of  19600    3.80%
#reg                      532   out of  19600    2.71%
#le                       936
  #lut only               404   out of    936   43.16%
  #reg only               192   out of    936   20.51%
  #lut&reg                340   out of    936   36.32%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  23
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     3
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    232
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         100
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       NONE    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+---------------------------------------------------------------------------------------------------------+
|Instance                            |Module         |le     |lut     |ripple  |seq     |bram    |dsp     |
+---------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B     |936    |585     |159     |537     |23      |0       |
|  CLK120                            |global_clock   |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData       |91     |73      |8       |58      |0       |0       |
|    usms                            |Time_1ms       |29     |14      |5       |19      |0       |0       |
|  SPIM                              |SPI_MASTER     |205    |121     |21      |134     |0       |0       |
|  wendu                             |DS18B20        |173    |130     |43      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER |464    |258     |87      |297     |0       |0       |
|    wrapper_cwc_top                 |cwc_top        |464    |258     |87      |297     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int    |174    |80      |0       |171     |0       |0       |
|        reg_inst                    |register       |172    |78      |0       |169     |0       |0       |
|        tap_inst                    |tap            |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger        |290    |178     |87      |126     |0       |0       |
|        bus_inst                    |bus_top        |73     |47      |26      |21      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det        |73     |47      |26      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl       |134    |86      |29      |78      |0       |0       |
+---------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout    Nets  
    #1         1       569   
    #2         2       268   
    #3         3       132   
    #4         4        38   
    #5        5-10     102   
    #6       11-50      34   
    #7       51-100     2    
  Average     2.91           

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4853, tnet num: 1162, tinst num: 526, tnode num: 6163, tedge num: 8345.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 588953dbe18b1871915f908614aff20f758057a2d1993c4931f4cee582d18853 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 526
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1164, pip num: 11676
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1117 valid insts, and 31987 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010011011001011011010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.733354s wall, 13.562500s user + 0.062500s system = 13.625000s CPU (498.5%)

RUN-1004 : used memory is 491 MB, reserved memory is 451 MB, peak memory is 641 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_111522.log"
