<?xml version="1.0" encoding="UTF-8"?>
<!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
<All_Bram_Infos>
    <Ucode>01100111</Ucode>
    <AL_PHY_BRAM>
        <INST_1>
            <rid>0X0004</rid>
            <wid>0X0004</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_1</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>0</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_1>
        <INST_2>
            <rid>0X0005</rid>
            <wid>0X0005</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_11</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>9</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_2>
        <INST_3>
            <rid>0X0006</rid>
            <wid>0X0006</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_21</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>18</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_3>
        <INST_4>
            <rid>0X0007</rid>
            <wid>0X0007</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_31</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>27</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_4>
        <INST_5>
            <rid>0X0008</rid>
            <wid>0X0008</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_41</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>1024</address_offset>
                <data_offset>0</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_5>
        <INST_6>
            <rid>0X0009</rid>
            <wid>0X0009</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_51</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>1024</address_offset>
                <data_offset>9</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_6>
        <INST_7>
            <rid>0X000A</rid>
            <wid>0X000A</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_61</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>1024</address_offset>
                <data_offset>18</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_7>
        <INST_8>
            <rid>0X000B</rid>
            <wid>0X000B</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_71</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>1024</address_offset>
                <data_offset>27</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_8>
        <INST_9>
            <rid>0X000C</rid>
            <wid>0X000C</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_81</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>2048</address_offset>
                <data_offset>0</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_9>
        <INST_10>
            <rid>0X000D</rid>
            <wid>0X000D</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_91</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>2048</address_offset>
                <data_offset>9</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_10>
        <INST_11>
            <rid>0X000E</rid>
            <wid>0X000E</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_101</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>2048</address_offset>
                <data_offset>18</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_11>
        <INST_12>
            <rid>0X000F</rid>
            <wid>0X000F</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_111</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>2048</address_offset>
                <data_offset>27</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_12>
        <INST_13>
            <rid>0X0010</rid>
            <wid>0X0010</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_121</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>3072</address_offset>
                <data_offset>0</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_13>
        <INST_14>
            <rid>0X0011</rid>
            <wid>0X0011</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_131</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>3072</address_offset>
                <data_offset>9</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_14>
        <INST_15>
            <rid>0X0012</rid>
            <wid>0X0012</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_141</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>3072</address_offset>
                <data_offset>18</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_15>
        <INST_16>
            <rid>0X0013</rid>
            <wid>0X0013</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_151</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>3072</address_offset>
                <data_offset>27</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_16>
        <INST_17>
            <rid>0X0014</rid>
            <wid>0X0014</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_161</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>4096</address_offset>
                <data_offset>0</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_17>
        <INST_18>
            <rid>0X0015</rid>
            <wid>0X0015</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_171</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>4096</address_offset>
                <data_offset>9</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_18>
        <INST_19>
            <rid>0X0016</rid>
            <wid>0X0016</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_181</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>4096</address_offset>
                <data_offset>18</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_19>
        <INST_20>
            <rid>0X0017</rid>
            <wid>0X0017</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_191</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>4096</address_offset>
                <data_offset>27</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_20>
        <INST_21>
            <rid>0X0018</rid>
            <wid>0X0018</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_201</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>5120</address_offset>
                <data_offset>0</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_21>
        <INST_22>
            <rid>0X0019</rid>
            <wid>0X0019</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_211</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>5120</address_offset>
                <data_offset>9</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_22>
        <INST_23>
            <rid>0X001A</rid>
            <wid>0X001A</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_221</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>5120</address_offset>
                <data_offset>18</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_23>
        <INST_24>
            <rid>0X001B</rid>
            <wid>0X001B</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_231</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>5120</address_offset>
                <data_offset>27</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_24>
        <INST_25>
            <rid>0X001C</rid>
            <wid>0X001C</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_241</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>6144</address_offset>
                <data_offset>0</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_25>
        <INST_26>
            <rid>0X001D</rid>
            <wid>0X001D</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_251</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>6144</address_offset>
                <data_offset>9</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_26>
        <INST_27>
            <rid>0X001E</rid>
            <wid>0X001E</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_261</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>6144</address_offset>
                <data_offset>18</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_27>
        <INST_28>
            <rid>0X001F</rid>
            <wid>0X001F</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_271</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>6144</address_offset>
                <data_offset>27</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_28>
        <INST_29>
            <rid>0X0020</rid>
            <wid>0X0020</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_281</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>7168</address_offset>
                <data_offset>0</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_29>
        <INST_30>
            <rid>0X0021</rid>
            <wid>0X0021</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_291</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>7168</address_offset>
                <data_offset>9</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_30>
        <INST_31>
            <rid>0X0022</rid>
            <wid>0X0022</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_301</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>7168</address_offset>
                <data_offset>18</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_31>
        <INST_32>
            <rid>0X0023</rid>
            <wid>0X0023</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_311</name>
            <width_a>9</width_a>
            <width_b>9</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>7168</address_offset>
                <data_offset>27</data_offset>
                <depth>1024</depth>
                <width>9</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>9</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>1024</depth>
                    <mode_type>126</mode_type>
                    <width>9</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_32>
        <INST_33>
            <rid>0X0024</rid>
            <wid>0X0024</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_321</name>
            <width_a>1</width_a>
            <width_b>1</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>36</data_offset>
                <depth>8192</depth>
                <width>1</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>1</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>8192</depth>
                    <mode_type>126</mode_type>
                    <width>1</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_33>
        <INST_34>
            <rid>0X0025</rid>
            <wid>0X0025</wid>
            <is_debuggable>y</is_debuggable>
            <is_initialize>y</is_initialize>
            <model_type>AL_PHY_BRAM</model_type>
            <name>auto_chipwatcher_0_logicbram_syn_323</name>
            <width_a>1</width_a>
            <width_b>1</width_b>
            <logic_name>auto_chipwatcher_0_logicbram</logic_name>
            <logic_width>38</logic_width>
            <logic_depth>8192</logic_depth>
            <sub_bid_info>
                <address_offset>0</address_offset>
                <data_offset>37</data_offset>
                <depth>8192</depth>
                <width>1</width>
                <num_section>1</num_section>
                <section_size>38</section_size>
                <width_per_section>1</width_per_section>
                <bytes_in_per_section>1</bytes_in_per_section>
                <working_mode>
                    <address_step>1</address_step>
                    <depth>8192</depth>
                    <mode_type>126</mode_type>
                    <width>1</width>
                    <num_byte>1</num_byte>
                    <ecc>0</ecc>
                </working_mode>
            </sub_bid_info>
        </INST_34>
    </AL_PHY_BRAM>
</All_Bram_Infos>
