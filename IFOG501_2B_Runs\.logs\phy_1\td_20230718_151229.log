============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 15:12:29 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 20 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1644 instances
RUN-0007 : 372 luts, 999 seqs, 143 mslices, 81 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2221 nets
RUN-1001 : 1687 nets have 2 pins
RUN-1001 : 419 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     262     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1642 instances, 372 luts, 999 seqs, 224 slices, 27 macros(224 instances: 143 mslices 81 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7828, tnet num: 2219, tinst num: 1642, tnode num: 11025, tedge num: 13284.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.278545s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 597862
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1642.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 478028, overlap = 13.5
PHY-3002 : Step(2): len = 391532, overlap = 13.5
PHY-3002 : Step(3): len = 375789, overlap = 11.25
PHY-3002 : Step(4): len = 364662, overlap = 13.5
PHY-3002 : Step(5): len = 355332, overlap = 18
PHY-3002 : Step(6): len = 348883, overlap = 13.5
PHY-3002 : Step(7): len = 341381, overlap = 18
PHY-3002 : Step(8): len = 332558, overlap = 13.5
PHY-3002 : Step(9): len = 324124, overlap = 15.75
PHY-3002 : Step(10): len = 317988, overlap = 15.75
PHY-3002 : Step(11): len = 307200, overlap = 15.75
PHY-3002 : Step(12): len = 297782, overlap = 15.75
PHY-3002 : Step(13): len = 292800, overlap = 18
PHY-3002 : Step(14): len = 278527, overlap = 15.75
PHY-3002 : Step(15): len = 267507, overlap = 18
PHY-3002 : Step(16): len = 263628, overlap = 18
PHY-3002 : Step(17): len = 251274, overlap = 18
PHY-3002 : Step(18): len = 217961, overlap = 15.75
PHY-3002 : Step(19): len = 212708, overlap = 18
PHY-3002 : Step(20): len = 210863, overlap = 18
PHY-3002 : Step(21): len = 198516, overlap = 15.75
PHY-3002 : Step(22): len = 190106, overlap = 18
PHY-3002 : Step(23): len = 186906, overlap = 18
PHY-3002 : Step(24): len = 182523, overlap = 18
PHY-3002 : Step(25): len = 176907, overlap = 18
PHY-3002 : Step(26): len = 173226, overlap = 18
PHY-3002 : Step(27): len = 164903, overlap = 18
PHY-3002 : Step(28): len = 161814, overlap = 18
PHY-3002 : Step(29): len = 154861, overlap = 18
PHY-3002 : Step(30): len = 152242, overlap = 18
PHY-3002 : Step(31): len = 147433, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00013596
PHY-3002 : Step(32): len = 146429, overlap = 9
PHY-3002 : Step(33): len = 145278, overlap = 6.75
PHY-3002 : Step(34): len = 143513, overlap = 11.25
PHY-3002 : Step(35): len = 141427, overlap = 11.25
PHY-3002 : Step(36): len = 134509, overlap = 9
PHY-3002 : Step(37): len = 129820, overlap = 6.75
PHY-3002 : Step(38): len = 127907, overlap = 11.25
PHY-3002 : Step(39): len = 126340, overlap = 4.5
PHY-3002 : Step(40): len = 122971, overlap = 4.5
PHY-3002 : Step(41): len = 118564, overlap = 9
PHY-3002 : Step(42): len = 116144, overlap = 9
PHY-3002 : Step(43): len = 115363, overlap = 9
PHY-3002 : Step(44): len = 112648, overlap = 11.25
PHY-3002 : Step(45): len = 110251, overlap = 9
PHY-3002 : Step(46): len = 107312, overlap = 9
PHY-3002 : Step(47): len = 105745, overlap = 6.75
PHY-3002 : Step(48): len = 103802, overlap = 9
PHY-3002 : Step(49): len = 101650, overlap = 9.0625
PHY-3002 : Step(50): len = 98488.7, overlap = 9.25
PHY-3002 : Step(51): len = 97236, overlap = 9.25
PHY-3002 : Step(52): len = 95006.4, overlap = 9.1875
PHY-3002 : Step(53): len = 92742, overlap = 13.6875
PHY-3002 : Step(54): len = 90167.2, overlap = 11.5625
PHY-3002 : Step(55): len = 87870.3, overlap = 11.4375
PHY-3002 : Step(56): len = 85743.2, overlap = 11.25
PHY-3002 : Step(57): len = 84639.9, overlap = 11.3125
PHY-3002 : Step(58): len = 81450.3, overlap = 9.6875
PHY-3002 : Step(59): len = 78092.8, overlap = 7.125
PHY-3002 : Step(60): len = 76320.4, overlap = 4.875
PHY-3002 : Step(61): len = 75796.8, overlap = 6.75
PHY-3002 : Step(62): len = 73979.8, overlap = 13.5
PHY-3002 : Step(63): len = 73588.4, overlap = 11.25
PHY-3002 : Step(64): len = 72982.6, overlap = 9
PHY-3002 : Step(65): len = 72042.4, overlap = 6.75
PHY-3002 : Step(66): len = 71253.9, overlap = 9.1875
PHY-3002 : Step(67): len = 69504.8, overlap = 13.5625
PHY-3002 : Step(68): len = 66070.4, overlap = 13.5
PHY-3002 : Step(69): len = 65373, overlap = 6.75
PHY-3002 : Step(70): len = 64811.5, overlap = 6.75
PHY-3002 : Step(71): len = 64195.2, overlap = 11.25
PHY-3002 : Step(72): len = 63784.7, overlap = 9
PHY-3002 : Step(73): len = 63298.4, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000271919
PHY-3002 : Step(74): len = 63315.3, overlap = 9
PHY-3002 : Step(75): len = 63304.7, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000543839
PHY-3002 : Step(76): len = 63133.6, overlap = 9
PHY-3002 : Step(77): len = 63006.8, overlap = 11.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005949s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059690s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(78): len = 64989.4, overlap = 6.3125
PHY-3002 : Step(79): len = 63335.6, overlap = 6.6875
PHY-3002 : Step(80): len = 61641, overlap = 5.8125
PHY-3002 : Step(81): len = 60612.5, overlap = 7.1875
PHY-3002 : Step(82): len = 59568.8, overlap = 7.125
PHY-3002 : Step(83): len = 58179, overlap = 7
PHY-3002 : Step(84): len = 56582.1, overlap = 7.1875
PHY-3002 : Step(85): len = 55325.6, overlap = 7.8125
PHY-3002 : Step(86): len = 54447.4, overlap = 7.8125
PHY-3002 : Step(87): len = 53997.7, overlap = 8.375
PHY-3002 : Step(88): len = 53512.5, overlap = 8.0625
PHY-3002 : Step(89): len = 52854.8, overlap = 8.3125
PHY-3002 : Step(90): len = 51892.1, overlap = 7.4375
PHY-3002 : Step(91): len = 51320.5, overlap = 6.5
PHY-3002 : Step(92): len = 50656.5, overlap = 6.6875
PHY-3002 : Step(93): len = 49987.9, overlap = 6.5
PHY-3002 : Step(94): len = 49431.4, overlap = 6.75
PHY-3002 : Step(95): len = 48599.5, overlap = 7.1875
PHY-3002 : Step(96): len = 48387.3, overlap = 7.6875
PHY-3002 : Step(97): len = 48053, overlap = 8.0625
PHY-3002 : Step(98): len = 47674, overlap = 8.9375
PHY-3002 : Step(99): len = 46572.2, overlap = 10.3125
PHY-3002 : Step(100): len = 46384, overlap = 10
PHY-3002 : Step(101): len = 46024.5, overlap = 10.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000294557
PHY-3002 : Step(102): len = 45795.4, overlap = 10.25
PHY-3002 : Step(103): len = 45738.4, overlap = 10.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000589114
PHY-3002 : Step(104): len = 45730.8, overlap = 11
PHY-3002 : Step(105): len = 46052.4, overlap = 10.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062374s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.30752e-05
PHY-3002 : Step(106): len = 45841.9, overlap = 57.6562
PHY-3002 : Step(107): len = 46466.1, overlap = 53.5312
PHY-3002 : Step(108): len = 46599.5, overlap = 57.5
PHY-3002 : Step(109): len = 46705.6, overlap = 56.3438
PHY-3002 : Step(110): len = 46941.4, overlap = 55.875
PHY-3002 : Step(111): len = 47408.7, overlap = 52.1562
PHY-3002 : Step(112): len = 47285.3, overlap = 52.2188
PHY-3002 : Step(113): len = 47182.4, overlap = 52.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00016615
PHY-3002 : Step(114): len = 47368, overlap = 52.2812
PHY-3002 : Step(115): len = 47688.5, overlap = 47.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000332301
PHY-3002 : Step(116): len = 48109.6, overlap = 46
PHY-3002 : Step(117): len = 49331.3, overlap = 40.5
PHY-3002 : Step(118): len = 50538.3, overlap = 32.4688
PHY-3002 : Step(119): len = 50266.6, overlap = 32.0938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000664601
PHY-3002 : Step(120): len = 50646.2, overlap = 31.5312
PHY-3002 : Step(121): len = 51006.8, overlap = 35.5938
PHY-3002 : Step(122): len = 51511.5, overlap = 32.5312
PHY-3002 : Step(123): len = 52005.7, overlap = 36.625
PHY-3002 : Step(124): len = 52088.5, overlap = 35.6875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.0013292
PHY-3002 : Step(125): len = 51981.8, overlap = 35.9375
PHY-3002 : Step(126): len = 51677.5, overlap = 35.9375
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00265841
PHY-3002 : Step(127): len = 51765.7, overlap = 35.75
PHY-3002 : Step(128): len = 51889.9, overlap = 35.4375
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00531681
PHY-3002 : Step(129): len = 52107, overlap = 31.5
PHY-3002 : Step(130): len = 52272.3, overlap = 31.125
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.0106336
PHY-3002 : Step(131): len = 52217.6, overlap = 35.3125
PHY-3002 : Step(132): len = 52217.6, overlap = 35.3125
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0176051
PHY-3002 : Step(133): len = 52280.3, overlap = 35
PHY-3002 : Step(134): len = 52320.2, overlap = 30.5
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.0352102
PHY-3002 : Step(135): len = 52370.7, overlap = 30.5
PHY-3002 : Step(136): len = 52370.7, overlap = 30.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7828, tnet num: 2219, tinst num: 1642, tnode num: 11025, tedge num: 13284.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.69 peak overflow 3.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2221.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55872, over cnt = 258(0%), over = 1029, worst = 20
PHY-1001 : End global iterations;  0.080046s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (117.1%)

PHY-1001 : Congestion index: top1 = 44.12, top5 = 25.83, top10 = 16.91, top15 = 12.01.
PHY-1001 : End incremental global routing;  0.132308s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065189s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.227633s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (96.1%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1724/2221.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55872, over cnt = 258(0%), over = 1029, worst = 20
PHY-1002 : len = 63136, over cnt = 153(0%), over = 287, worst = 13
PHY-1002 : len = 65648, over cnt = 32(0%), over = 49, worst = 13
PHY-1002 : len = 66288, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 66520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.089502s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (157.1%)

PHY-1001 : Congestion index: top1 = 39.18, top5 = 25.81, top10 = 18.64, top15 = 13.67.
OPT-1001 : End congestion update;  0.132224s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (141.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057247s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.192065s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (130.2%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : End physical optimization;  0.692438s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (119.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 372 LUT to BLE ...
SYN-4008 : Packed 372 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 810 remaining SEQ's ...
SYN-4005 : Packed 115 SEQ with LUT/SLICE
SYN-4006 : 95 single LUT's are left
SYN-4006 : 695 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1067/1398 primitive instances ...
PHY-3001 : End packing;  0.050220s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 840 instances
RUN-1001 : 395 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2046 nets
RUN-1001 : 1524 nets have 2 pins
RUN-1001 : 401 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 838 instances, 791 slices, 27 macros(224 instances: 143 mslices 81 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52612.4, Over = 57
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6602, tnet num: 2044, tinst num: 838, tnode num: 8913, tedge num: 11661.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.297449s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.57846e-05
PHY-3002 : Step(137): len = 51693.9, overlap = 59
PHY-3002 : Step(138): len = 51090.6, overlap = 60
PHY-3002 : Step(139): len = 50633.6, overlap = 62.5
PHY-3002 : Step(140): len = 50367.9, overlap = 63.5
PHY-3002 : Step(141): len = 50297.4, overlap = 62.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.15693e-05
PHY-3002 : Step(142): len = 50604.4, overlap = 61.75
PHY-3002 : Step(143): len = 50975.7, overlap = 60.75
PHY-3002 : Step(144): len = 51500, overlap = 58
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000143139
PHY-3002 : Step(145): len = 52591.8, overlap = 54
PHY-3002 : Step(146): len = 54258.4, overlap = 48.75
PHY-3002 : Step(147): len = 54476.4, overlap = 47.75
PHY-3002 : Step(148): len = 54530, overlap = 47.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.085291s wall, 0.078125s user + 0.156250s system = 0.234375s CPU (274.8%)

PHY-3001 : Trial Legalized: Len = 67109.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050866s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000712629
PHY-3002 : Step(149): len = 64397.8, overlap = 6.5
PHY-3002 : Step(150): len = 61475.7, overlap = 12.25
PHY-3002 : Step(151): len = 59755.3, overlap = 17.25
PHY-3002 : Step(152): len = 58934.9, overlap = 19.25
PHY-3002 : Step(153): len = 58367.4, overlap = 25
PHY-3002 : Step(154): len = 58012.7, overlap = 25.25
PHY-3002 : Step(155): len = 57721.5, overlap = 26.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00142526
PHY-3002 : Step(156): len = 58158.9, overlap = 25.25
PHY-3002 : Step(157): len = 58271.9, overlap = 25.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00285052
PHY-3002 : Step(158): len = 58418.6, overlap = 23.75
PHY-3002 : Step(159): len = 58485, overlap = 23.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004798s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62710.7, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005538s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (282.2%)

PHY-3001 : 6 instances has been re-located, deltaX = 2, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 62882.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6602, tnet num: 2044, tinst num: 838, tnode num: 8913, tedge num: 11661.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 110/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69560, over cnt = 160(0%), over = 225, worst = 6
PHY-1002 : len = 70544, over cnt = 65(0%), over = 84, worst = 5
PHY-1002 : len = 71408, over cnt = 12(0%), over = 13, worst = 2
PHY-1002 : len = 71536, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 71648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.147517s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (95.3%)

PHY-1001 : Congestion index: top1 = 33.30, top5 = 23.65, top10 = 18.40, top15 = 14.44.
PHY-1001 : End incremental global routing;  0.198055s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062070s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.291096s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.0%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1811/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006044s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (258.5%)

PHY-1001 : Congestion index: top1 = 33.30, top5 = 23.65, top10 = 18.40, top15 = 14.44.
OPT-1001 : End congestion update;  0.052573s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049643s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.4%)

OPT-0007 : Start: WNS 1019 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1019 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.104906s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (89.4%)

OPT-1001 : Current memory(MB): used = 226, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050744s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1811/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005716s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (273.4%)

PHY-1001 : Congestion index: top1 = 33.30, top5 = 23.65, top10 = 18.40, top15 = 14.44.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058071s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1019 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1019ps with logic level 5 
RUN-1001 :       #2 path slack 1034ps with logic level 5 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.866911s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.1%)

RUN-1003 : finish command "place" in  5.263272s wall, 8.546875s user + 3.000000s system = 11.546875s CPU (219.4%)

RUN-1004 : used memory is 208 MB, reserved memory is 171 MB, peak memory is 227 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 840 instances
RUN-1001 : 395 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2046 nets
RUN-1001 : 1524 nets have 2 pins
RUN-1001 : 401 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6602, tnet num: 2044, tinst num: 838, tnode num: 8913, tedge num: 11661.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 395 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68424, over cnt = 154(0%), over = 227, worst = 6
PHY-1002 : len = 69328, over cnt = 62(0%), over = 90, worst = 6
PHY-1002 : len = 70312, over cnt = 14(0%), over = 16, worst = 2
PHY-1002 : len = 70552, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70584, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.147826s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (95.1%)

PHY-1001 : Congestion index: top1 = 32.80, top5 = 23.24, top10 = 18.14, top15 = 14.24.
PHY-1001 : End global routing;  0.199324s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (94.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 244, reserve = 209, peak = 251.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 502, reserve = 471, peak = 502.
PHY-1001 : End build detailed router design. 3.345448s wall, 3.328125s user + 0.015625s system = 3.343750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34512, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.280989s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End phase 1; 1.286786s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 43% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184136, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 537.
PHY-1001 : End initial routed; 1.090402s wall, 1.875000s user + 0.125000s system = 2.000000s CPU (183.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1813(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.137   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366978s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.2%)

PHY-1001 : Current memory(MB): used = 538, reserve = 506, peak = 538.
PHY-1001 : End phase 2; 1.457512s wall, 2.250000s user + 0.125000s system = 2.375000s CPU (162.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184136, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014665s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (106.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184152, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025573s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (122.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184200, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021551s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (145.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1813(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.137   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.371167s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (96.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.183011s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.5%)

PHY-1001 : Current memory(MB): used = 552, reserve = 521, peak = 552.
PHY-1001 : End phase 3; 0.724885s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.2%)

PHY-1003 : Routed, final wirelength = 184200
PHY-1001 : Current memory(MB): used = 553, reserve = 521, peak = 553.
PHY-1001 : End export database. 0.009983s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (156.5%)

PHY-1001 : End detail routing;  7.000206s wall, 7.765625s user + 0.140625s system = 7.906250s CPU (112.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6602, tnet num: 2044, tinst num: 838, tnode num: 8913, tedge num: 11661.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.920099s wall, 8.687500s user + 0.140625s system = 8.828125s CPU (111.5%)

RUN-1004 : used memory is 506 MB, reserved memory is 477 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      826   out of  19600    4.21%
#reg                     1048   out of  19600    5.35%
#le                      1521
  #lut only               473   out of   1521   31.10%
  #reg only               695   out of   1521   45.69%
  #lut&reg                353   out of   1521   23.21%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         466
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1521   |602     |224     |1079    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1137   |320     |139     |900     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |30     |21      |9       |21      |0       |0       |
|    demodu                  |Demodulation                                     |543    |130     |58      |435     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |61      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |3       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |12      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |14      |0       |29      |0       |0       |
|    integ                   |Integration                                      |139    |15      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |60     |23      |14      |56      |0       |1       |
|    rs422                   |Rs422Output                                      |338    |109     |39      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |22      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |95     |86      |7       |51      |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |26      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |19     |18      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |42     |42      |0       |18      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1488  
    #2          2       278   
    #3          3       110   
    #4          4        13   
    #5        5-10       82   
    #6        11-50      31   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6602, tnet num: 2044, tinst num: 838, tnode num: 8913, tedge num: 11661.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 838
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2046, pip num: 14785
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1280 valid insts, and 39309 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.177592s wall, 17.250000s user + 0.093750s system = 17.343750s CPU (545.8%)

RUN-1004 : used memory is 548 MB, reserved memory is 515 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_151229.log"
