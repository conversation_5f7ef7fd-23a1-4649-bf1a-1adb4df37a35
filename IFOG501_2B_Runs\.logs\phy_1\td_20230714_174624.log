============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 14 17:46:24 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(72)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../al_ip/pll_2.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/pll_2.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'extlock', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(131)
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(132)
RUN-1001 : Project manager successfully analyzed 16 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {u_pll_2/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {u_pll_2/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins u_pll_2/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name u_pll_2/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 377957122048"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 386547056640"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../cwc.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 7 view nodes, 74 trigger nets, 74 data nets.
KIT-1004 : Chipwatcher code = 0011010101101111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=198) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=198) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0110111) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=198)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=198)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=7,BUS_DIN_NUM=74,BUS_CTRL_NUM=176,BUS_WIDTH='{32'sb01,32'sb01110,32'sb0110111,32'sb01,32'sb01,32'sb01,32'sb01},BUS_DIN_POS='{32'sb0,32'sb01,32'sb01111,32'sb01000110,32'sb01000111,32'sb01001000,32'sb01001001},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb0100110,32'sb010011000,32'sb010011110,32'sb010100100,32'sb010101010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0110111)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 4328/27 useful/useless nets, 2548/19 useful/useless insts
SYN-1016 : Merged 36 instances.
SYN-1032 : 3845/26 useful/useless nets, 3143/22 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 574 better
SYN-1014 : Optimize round 2
SYN-1032 : 3401/30 useful/useless nets, 2699/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 3433/294 useful/useless nets, 2763/78 useful/useless insts
SYN-1016 : Merged 30 instances.
SYN-2571 : Optimize after map_dsp, round 1, 402 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 69 instances.
SYN-2501 : Optimize round 1, 140 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 4051/4 useful/useless nets, 3381/3 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 15233, tnet num: 4051, tinst num: 3380, tnode num: 20425, tedge num: 24136.
TMR-2508 : Levelizing timing graph completed, there are 131 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 4051 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 351 (3.27), #lev = 7 (1.58)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 346 (3.27), #lev = 7 (1.63)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 737 instances into 346 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 569 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 182 adder to BLE ...
SYN-4008 : Packed 182 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  1.058624s wall, 1.015625s user + 0.046875s system = 1.062500s CPU (100.4%)

RUN-1004 : used memory is 183 MB, reserved memory is 145 MB, peak memory is 219 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  2.032338s wall, 1.968750s user + 0.078125s system = 2.046875s CPU (100.7%)

RUN-1004 : used memory is 183 MB, reserved memory is 145 MB, peak memory is 219 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (412 clock/control pins, 0 other pins).
SYN-4027 : Net TxTransmit_dup_1 is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4027 : Net RxTransmit_dup_1 is clkc0 of pll u_pll_2/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll u_pll_2/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4025 : Tag rtl::Net RxTransmit_dup_1 as clock net
SYN-4025 : Tag rtl::Net TxTransmit_dup_1 as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 5 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2737 instances
RUN-0007 : 711 luts, 1582 seqs, 240 mslices, 121 lslices, 34 pads, 37 brams, 5 dsps
RUN-1001 : There are total 3409 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 2127 nets have 2 pins
RUN-1001 : 1046 nets have [3 - 5] pins
RUN-1001 : 166 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 11 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     257     
RUN-1001 :   No   |  No   |  Yes  |     361     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     315     
RUN-1001 :   Yes  |  No   |  Yes  |     539     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    5    |  20   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 29
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2735 instances, 711 luts, 1582 seqs, 361 slices, 40 macros(361 instances: 240 mslices 121 lslices)
PHY-0007 : Cell area utilization is 8%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13813, tnet num: 3407, tinst num: 2735, tnode num: 19438, tedge num: 23254.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3407 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.390828s wall, 0.359375s user + 0.031250s system = 0.390625s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 900463
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2735.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 760633, overlap = 87.75
PHY-3002 : Step(2): len = 608852, overlap = 90
PHY-3002 : Step(3): len = 573254, overlap = 85.5
PHY-3002 : Step(4): len = 554839, overlap = 92.25
PHY-3002 : Step(5): len = 540861, overlap = 90
PHY-3002 : Step(6): len = 486575, overlap = 94.5
PHY-3002 : Step(7): len = 444539, overlap = 94.5
PHY-3002 : Step(8): len = 427935, overlap = 94.5
PHY-3002 : Step(9): len = 418022, overlap = 94.5
PHY-3002 : Step(10): len = 406140, overlap = 94.5
PHY-3002 : Step(11): len = 394420, overlap = 94.5
PHY-3002 : Step(12): len = 389715, overlap = 94.5
PHY-3002 : Step(13): len = 371346, overlap = 94.5
PHY-3002 : Step(14): len = 354671, overlap = 94.5625
PHY-3002 : Step(15): len = 348606, overlap = 94.75
PHY-3002 : Step(16): len = 339033, overlap = 95.375
PHY-3002 : Step(17): len = 312852, overlap = 94.7812
PHY-3002 : Step(18): len = 306745, overlap = 96.5625
PHY-3002 : Step(19): len = 299888, overlap = 93.3125
PHY-3002 : Step(20): len = 292097, overlap = 95.25
PHY-3002 : Step(21): len = 286144, overlap = 93.1875
PHY-3002 : Step(22): len = 269193, overlap = 94.875
PHY-3002 : Step(23): len = 261505, overlap = 92.25
PHY-3002 : Step(24): len = 256894, overlap = 94.5
PHY-3002 : Step(25): len = 247934, overlap = 92.25
PHY-3002 : Step(26): len = 241902, overlap = 94.5
PHY-3002 : Step(27): len = 235448, overlap = 92.25
PHY-3002 : Step(28): len = 229693, overlap = 94.5
PHY-3002 : Step(29): len = 218298, overlap = 88.0625
PHY-3002 : Step(30): len = 205985, overlap = 90.75
PHY-3002 : Step(31): len = 201888, overlap = 89.5
PHY-3002 : Step(32): len = 195723, overlap = 91.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.36634e-05
PHY-3002 : Step(33): len = 204038, overlap = 83.25
PHY-3002 : Step(34): len = 208576, overlap = 83.6562
PHY-3002 : Step(35): len = 200359, overlap = 72.9375
PHY-3002 : Step(36): len = 197757, overlap = 75.2812
PHY-3002 : Step(37): len = 195449, overlap = 66.4062
PHY-3002 : Step(38): len = 191282, overlap = 75.2188
PHY-3002 : Step(39): len = 187539, overlap = 75.3125
PHY-3002 : Step(40): len = 184097, overlap = 80.4688
PHY-3002 : Step(41): len = 178290, overlap = 72.6562
PHY-3002 : Step(42): len = 172554, overlap = 66.5312
PHY-3002 : Step(43): len = 170100, overlap = 73.25
PHY-3002 : Step(44): len = 166272, overlap = 73.3438
PHY-3002 : Step(45): len = 162391, overlap = 78.2188
PHY-3002 : Step(46): len = 156459, overlap = 71.75
PHY-3002 : Step(47): len = 152523, overlap = 77.5
PHY-3002 : Step(48): len = 149052, overlap = 80.125
PHY-3002 : Step(49): len = 143454, overlap = 80.375
PHY-3002 : Step(50): len = 139097, overlap = 77.875
PHY-3002 : Step(51): len = 137616, overlap = 80.5
PHY-3002 : Step(52): len = 134209, overlap = 81.8125
PHY-3002 : Step(53): len = 130182, overlap = 80.5312
PHY-3002 : Step(54): len = 126941, overlap = 81.4688
PHY-3002 : Step(55): len = 124956, overlap = 87.3438
PHY-3002 : Step(56): len = 121124, overlap = 92.1875
PHY-3002 : Step(57): len = 118332, overlap = 94.4375
PHY-3002 : Step(58): len = 115981, overlap = 96.9688
PHY-3002 : Step(59): len = 112570, overlap = 102.531
PHY-3002 : Step(60): len = 111246, overlap = 101.312
PHY-3002 : Step(61): len = 108134, overlap = 100.969
PHY-3002 : Step(62): len = 105629, overlap = 99.8438
PHY-3002 : Step(63): len = 101943, overlap = 98.3438
PHY-3002 : Step(64): len = 100209, overlap = 99.0938
PHY-3002 : Step(65): len = 99224.8, overlap = 109.625
PHY-3002 : Step(66): len = 97196.6, overlap = 112.031
PHY-3002 : Step(67): len = 95630.3, overlap = 112.812
PHY-3002 : Step(68): len = 94483.1, overlap = 113.812
PHY-3002 : Step(69): len = 91719.1, overlap = 110.719
PHY-3002 : Step(70): len = 90094.4, overlap = 103.125
PHY-3002 : Step(71): len = 89071.6, overlap = 103.688
PHY-3002 : Step(72): len = 87005.9, overlap = 111.688
PHY-3002 : Step(73): len = 86344.8, overlap = 115.969
PHY-3002 : Step(74): len = 84796.2, overlap = 117.188
PHY-3002 : Step(75): len = 83070.6, overlap = 114.281
PHY-3002 : Step(76): len = 82464.2, overlap = 114.25
PHY-3002 : Step(77): len = 81956.8, overlap = 112.562
PHY-3002 : Step(78): len = 81498.5, overlap = 112.875
PHY-3002 : Step(79): len = 81054.7, overlap = 116.906
PHY-3002 : Step(80): len = 80802.9, overlap = 116.844
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 6.73268e-05
PHY-3002 : Step(81): len = 81121.6, overlap = 116.938
PHY-3002 : Step(82): len = 81293.6, overlap = 117.25
PHY-3002 : Step(83): len = 81235.2, overlap = 117.438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000134654
PHY-3002 : Step(84): len = 81692.4, overlap = 117.438
PHY-3002 : Step(85): len = 81909.5, overlap = 108.406
PHY-3002 : Step(86): len = 81973.8, overlap = 97.2188
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012239s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3407 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.102168s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (76.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.15116e-05
PHY-3002 : Step(87): len = 87456.7, overlap = 69.2812
PHY-3002 : Step(88): len = 86461.7, overlap = 72.5625
PHY-3002 : Step(89): len = 86020.7, overlap = 58.7812
PHY-3002 : Step(90): len = 86103.7, overlap = 58.5312
PHY-3002 : Step(91): len = 86166.1, overlap = 53.9062
PHY-3002 : Step(92): len = 85229.9, overlap = 48.25
PHY-3002 : Step(93): len = 84344.5, overlap = 53.2812
PHY-3002 : Step(94): len = 83183.7, overlap = 54.25
PHY-3002 : Step(95): len = 82140.6, overlap = 58.125
PHY-3002 : Step(96): len = 81636.5, overlap = 59.1875
PHY-3002 : Step(97): len = 81279.8, overlap = 56.0625
PHY-3002 : Step(98): len = 80525.1, overlap = 62.0625
PHY-3002 : Step(99): len = 79536.5, overlap = 59.875
PHY-3002 : Step(100): len = 79545.7, overlap = 60.125
PHY-3002 : Step(101): len = 79142.7, overlap = 59.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 2.30233e-05
PHY-3002 : Step(102): len = 78747.6, overlap = 59.5312
PHY-3002 : Step(103): len = 78742.3, overlap = 59.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 4.60466e-05
PHY-3002 : Step(104): len = 78464, overlap = 59.5625
PHY-3002 : Step(105): len = 78536.3, overlap = 58.9688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 9.20932e-05
PHY-3002 : Step(106): len = 78488, overlap = 58.5938
PHY-3002 : Step(107): len = 81745.9, overlap = 48.8125
PHY-3002 : Step(108): len = 82325, overlap = 42.125
PHY-3002 : Step(109): len = 82079.2, overlap = 38.625
PHY-3002 : Step(110): len = 82378.9, overlap = 36.6875
PHY-3002 : Step(111): len = 82104.5, overlap = 33.2812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000184186
PHY-3002 : Step(112): len = 81722.9, overlap = 33.2188
PHY-3002 : Step(113): len = 81551.6, overlap = 32.7188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.000368373
PHY-3002 : Step(114): len = 81737.4, overlap = 31.6875
PHY-3002 : Step(115): len = 83481.7, overlap = 20.4062
PHY-3002 : Step(116): len = 83996.5, overlap = 19.1562
PHY-3002 : Step(117): len = 83359.7, overlap = 18.5938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3407 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.121111s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.06309e-05
PHY-3002 : Step(118): len = 83345.4, overlap = 104.969
PHY-3002 : Step(119): len = 83791.1, overlap = 96.8125
PHY-3002 : Step(120): len = 84880, overlap = 88.0625
PHY-3002 : Step(121): len = 84610.5, overlap = 89.7188
PHY-3002 : Step(122): len = 84913.9, overlap = 87.2812
PHY-3002 : Step(123): len = 85345.3, overlap = 85.5312
PHY-3002 : Step(124): len = 85224.8, overlap = 82.3438
PHY-3002 : Step(125): len = 85239.9, overlap = 79.9375
PHY-3002 : Step(126): len = 85596.6, overlap = 75.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000141262
PHY-3002 : Step(127): len = 85530.6, overlap = 75.1562
PHY-3002 : Step(128): len = 85990.8, overlap = 73.2812
PHY-3002 : Step(129): len = 86590.2, overlap = 71.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000282524
PHY-3002 : Step(130): len = 87057.2, overlap = 68.75
PHY-3002 : Step(131): len = 87913.9, overlap = 66.1875
PHY-3002 : Step(132): len = 89508.2, overlap = 59.625
PHY-3002 : Step(133): len = 90602.5, overlap = 55.625
PHY-3002 : Step(134): len = 92112.1, overlap = 48.5625
PHY-3002 : Step(135): len = 92997.5, overlap = 40.2812
PHY-3002 : Step(136): len = 92363.8, overlap = 40.5938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13813, tnet num: 3407, tinst num: 2735, tnode num: 19438, tedge num: 23254.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 120.38 peak overflow 2.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/3409.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 112408, over cnt = 468(1%), over = 1667, worst = 21
PHY-1001 : End global iterations;  0.177317s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (123.4%)

PHY-1001 : Congestion index: top1 = 48.28, top5 = 34.58, top10 = 26.46, top15 = 21.27.
PHY-1001 : End incremental global routing;  0.241290s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (116.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3407 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.117863s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (92.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 2695 has valid locations, 33 needs to be replaced
PHY-3001 : design contains 2767 instances, 711 luts, 1614 seqs, 361 slices, 40 macros(361 instances: 240 mslices 121 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 92688.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13941, tnet num: 3439, tinst num: 2767, tnode num: 19662, tedge num: 23446.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3439 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.451343s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(137): len = 93330.3, overlap = 0.375
PHY-3002 : Step(138): len = 93892.8, overlap = 0.375
PHY-3002 : Step(139): len = 94189.2, overlap = 0.375
PHY-3002 : Step(140): len = 94005.9, overlap = 0.375
PHY-3002 : Step(141): len = 93880.1, overlap = 0.375
PHY-3002 : Step(142): len = 93880.1, overlap = 0.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3439 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.101539s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000396237
PHY-3002 : Step(143): len = 93988.7, overlap = 41.3438
PHY-3002 : Step(144): len = 94030.2, overlap = 41.0312
PHY-3001 : Final: Len = 94030.2, Over = 41.0312
PHY-3001 : End incremental placement;  0.762772s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (127.0%)

OPT-1001 : Total overflow 120.75 peak overflow 2.59
OPT-1001 : End high-fanout net optimization;  1.187962s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (119.7%)

OPT-1001 : Current memory(MB): used = 265, reserve = 227, peak = 265.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2640/3441.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 114384, over cnt = 460(1%), over = 1653, worst = 21
PHY-1002 : len = 123240, over cnt = 318(0%), over = 774, worst = 15
PHY-1002 : len = 129840, over cnt = 66(0%), over = 163, worst = 14
PHY-1002 : len = 132304, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 132392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.219388s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (128.2%)

PHY-1001 : Congestion index: top1 = 42.20, top5 = 33.74, top10 = 27.44, top15 = 22.99.
OPT-1001 : End congestion update;  0.274573s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (125.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3439 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.100606s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (93.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.381011s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (118.9%)

OPT-1001 : Current memory(MB): used = 262, reserve = 223, peak = 265.
OPT-1001 : End physical optimization;  1.962847s wall, 2.171875s user + 0.078125s system = 2.250000s CPU (114.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 711 LUT to BLE ...
SYN-4008 : Packed 711 LUT and 240 SEQ to BLE.
SYN-4003 : Packing 1374 remaining SEQ's ...
SYN-4005 : Packed 434 SEQ with LUT/SLICE
SYN-4006 : 83 single LUT's are left
SYN-4006 : 940 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1651/2161 primitive instances ...
PHY-3001 : End packing;  0.102142s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (91.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1331 instances
RUN-1001 : 624 mslices, 624 lslices, 34 pads, 37 brams, 5 dsps
RUN-1001 : There are total 3219 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1919 nets have 2 pins
RUN-1001 : 1065 nets have [3 - 5] pins
RUN-1001 : 167 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 31 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
PHY-3001 : design contains 1329 instances, 1248 slices, 40 macros(361 instances: 240 mslices 121 lslices)
PHY-3001 : Cell area utilization is 16%
PHY-3001 : After packing: Len = 95743.4, Over = 75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 16%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11840, tnet num: 3217, tinst num: 1329, tnode num: 15910, tedge num: 20462.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.438643s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.90291e-05
PHY-3002 : Step(145): len = 94486, overlap = 71
PHY-3002 : Step(146): len = 93680.9, overlap = 76.75
PHY-3002 : Step(147): len = 93377.8, overlap = 80.5
PHY-3002 : Step(148): len = 92759.5, overlap = 85.75
PHY-3002 : Step(149): len = 92051.3, overlap = 86.25
PHY-3002 : Step(150): len = 91498.4, overlap = 87.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.80583e-05
PHY-3002 : Step(151): len = 91965.6, overlap = 83.5
PHY-3002 : Step(152): len = 93104.6, overlap = 82
PHY-3002 : Step(153): len = 94128.1, overlap = 79.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000156117
PHY-3002 : Step(154): len = 95016.2, overlap = 75.5
PHY-3002 : Step(155): len = 96987.6, overlap = 71
PHY-3002 : Step(156): len = 97448.1, overlap = 70.5
PHY-3002 : Step(157): len = 96890.5, overlap = 71.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.157771s wall, 0.156250s user + 0.250000s system = 0.406250s CPU (257.5%)

PHY-3001 : Trial Legalized: Len = 116713
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 16%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.088501s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000411404
PHY-3002 : Step(158): len = 112434, overlap = 10.25
PHY-3002 : Step(159): len = 108479, overlap = 17.25
PHY-3002 : Step(160): len = 105483, overlap = 28
PHY-3002 : Step(161): len = 103746, overlap = 31.75
PHY-3002 : Step(162): len = 102507, overlap = 37.5
PHY-3002 : Step(163): len = 101670, overlap = 44.25
PHY-3002 : Step(164): len = 101227, overlap = 47
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000822808
PHY-3002 : Step(165): len = 101980, overlap = 44
PHY-3002 : Step(166): len = 102382, overlap = 42.75
PHY-3002 : Step(167): len = 102176, overlap = 44.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00164562
PHY-3002 : Step(168): len = 102497, overlap = 42.25
PHY-3002 : Step(169): len = 102916, overlap = 42
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005561s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (281.0%)

PHY-3001 : Legalized: Len = 110267, Over = 0
PHY-3001 : Spreading special nets. 38 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.010577s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (147.7%)

PHY-3001 : 55 instances has been re-located, deltaX = 27, deltaY = 31, maxDist = 2.
PHY-3001 : Final: Len = 111751, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11840, tnet num: 3217, tinst num: 1329, tnode num: 15910, tedge num: 20462.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 198/3219.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 134536, over cnt = 371(1%), over = 634, worst = 6
PHY-1002 : len = 137304, over cnt = 212(0%), over = 293, worst = 5
PHY-1002 : len = 140096, over cnt = 57(0%), over = 82, worst = 5
PHY-1002 : len = 141256, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 141400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.313503s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (109.6%)

PHY-1001 : Congestion index: top1 = 40.17, top5 = 32.64, top10 = 27.02, top15 = 23.13.
PHY-1001 : End incremental global routing;  0.384540s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (105.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.109282s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (100.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.551861s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (104.8%)

OPT-1001 : Current memory(MB): used = 261, reserve = 222, peak = 265.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2819/3219.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 141400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.013069s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 40.17, top5 = 32.64, top10 = 27.02, top15 = 23.13.
OPT-1001 : End congestion update;  0.077071s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.100889s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1290 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 1329 instances, 1248 slices, 40 macros(361 instances: 240 mslices 121 lslices)
PHY-3001 : Cell area utilization is 16%
PHY-3001 : Initial: Len = 111733, Over = 0
PHY-3001 : End spreading;  0.008265s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (189.0%)

PHY-3001 : Final: Len = 111733, Over = 0
PHY-3001 : End incremental legalization;  0.057022s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.2%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.258047s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (145.3%)

OPT-1001 : Current memory(MB): used = 267, reserve = 229, peak = 267.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.116250s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (94.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2816/3219.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 141376, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 141376, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 141360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.050912s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (153.5%)

PHY-1001 : Congestion index: top1 = 40.17, top5 = 32.64, top10 = 27.02, top15 = 23.13.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.093230s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 39.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.555651s wall, 1.781250s user + 0.031250s system = 1.812500s CPU (116.5%)

RUN-1003 : finish command "place" in  8.638231s wall, 14.656250s user + 3.031250s system = 17.687500s CPU (204.8%)

RUN-1004 : used memory is 243 MB, reserved memory is 203 MB, peak memory is 268 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1331 instances
RUN-1001 : 624 mslices, 624 lslices, 34 pads, 37 brams, 5 dsps
RUN-1001 : There are total 3219 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1919 nets have 2 pins
RUN-1001 : 1065 nets have [3 - 5] pins
RUN-1001 : 167 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 31 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11840, tnet num: 3217, tinst num: 1329, tnode num: 15910, tedge num: 20462.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 624 mslices, 624 lslices, 34 pads, 37 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 131928, over cnt = 397(1%), over = 668, worst = 6
PHY-1002 : len = 134728, over cnt = 247(0%), over = 333, worst = 5
PHY-1002 : len = 138240, over cnt = 41(0%), over = 48, worst = 3
PHY-1002 : len = 139056, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 139088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.319784s wall, 0.359375s user + 0.062500s system = 0.421875s CPU (131.9%)

PHY-1001 : Congestion index: top1 = 39.89, top5 = 32.31, top10 = 26.78, top15 = 22.93.
PHY-1001 : End global routing;  0.390529s wall, 0.421875s user + 0.062500s system = 0.484375s CPU (124.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 283, reserve = 244, peak = 295.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net RxTransmit_dup_1 will be routed on clock mesh
PHY-1001 : net TxTransmit_dup_1 will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 547, reserve = 510, peak = 547.
PHY-1001 : End build detailed router design. 3.544792s wall, 3.500000s user + 0.046875s system = 3.546875s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 47608, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.324624s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 579, reserve = 544, peak = 579.
PHY-1001 : End phase 1; 1.331058s wall, 1.296875s user + 0.031250s system = 1.328125s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 69% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 397352, over cnt = 83(0%), over = 83, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 580, reserve = 545, peak = 580.
PHY-1001 : End initial routed; 4.241333s wall, 5.906250s user + 0.171875s system = 6.078125s CPU (143.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2861(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.217   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.580992s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 584, reserve = 549, peak = 584.
PHY-1001 : End phase 2; 4.822408s wall, 6.484375s user + 0.171875s system = 6.656250s CPU (138.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 397352, over cnt = 83(0%), over = 83, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.025476s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (61.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 396768, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.065323s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 396408, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.066666s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (140.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 396424, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.081357s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.0%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 396408, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.033126s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (141.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2861(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.217   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.559102s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (100.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-5014 WARNING: Detail route doesn't find pib for u_pll_2/pll_inst.fbclk[0]
PHY-1001 : 17 feed throughs used by 17 nets
PHY-1001 : End commit to database; 0.378577s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 602, reserve = 567, peak = 602.
PHY-1001 : End phase 3; 1.379510s wall, 1.359375s user + 0.031250s system = 1.390625s CPU (100.8%)

PHY-1003 : Routed, final wirelength = 396408
PHY-1001 : Current memory(MB): used = 602, reserve = 567, peak = 602.
PHY-1001 : End export database. 0.014217s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.9%)

PHY-1001 : End detail routing;  11.302527s wall, 12.875000s user + 0.281250s system = 13.156250s CPU (116.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11840, tnet num: 3217, tinst num: 1329, tnode num: 15910, tedge num: 20462.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  12.747976s wall, 14.359375s user + 0.343750s system = 14.703125s CPU (115.3%)

RUN-1004 : used memory is 573 MB, reserved memory is 537 MB, peak memory is 602 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1436   out of  19600    7.33%
#reg                     1692   out of  19600    8.63%
#le                      2376
  #lut only               684   out of   2376   28.79%
  #reg only               940   out of   2376   39.56%
  #lut&reg                752   out of   2376   31.65%
#dsp                        5   out of     29   17.24%
#bram                      37   out of     64   57.81%
  #bram9k                  37
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    17
  #treg                     1
#pll                        2   out of      4   50.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                        Fanout
#1        TxTransmit_dup_1     GCLK               pll                CLK120/pll_inst.clkc0         616
#2        config_inst_syn_9    GCLK               config             config_inst.jtck              224
#3        RxTransmit_dup_1     GCLK               pll                u_pll_2/pll_inst.clkc0        114
#4        wendu/clk_us         GCLK               lslice             wendu/clk_us_reg_syn_12.q1    39
#5        clk_in_dup_1         GCLK               io                 clk_in_syn_2.di               16


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       NONE    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |2376   |1075    |361     |1722    |37      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1136   |334     |141     |933     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |36     |27      |9       |24      |0       |0       |
|    demodu                          |Demodulation                                     |542    |119     |63      |437     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |166    |58      |25      |137     |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |52     |2       |0       |52      |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |15      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |31     |13      |0       |31      |0       |0       |
|    integ                           |Integration                                      |136    |22      |14      |110     |0       |0       |
|    modu                            |Modulation                                       |93     |32      |21      |89      |0       |1       |
|    rs422                           |Rs422Output                                      |307    |117     |29      |253     |0       |4       |
|    trans                           |SquareWaveGenerator                              |22     |17      |5       |20      |0       |0       |
|  u_pll_2                           |pll_2                                            |0      |0       |0       |0       |0       |0       |
|  u_uart                            |UART_Control                                     |81     |69      |7       |53      |0       |0       |
|    U0                              |speed_select_Tx                                  |33     |26      |7       |14      |0       |0       |
|    U1                              |uart_tx                                          |18     |14      |0       |18      |0       |0       |
|    U2                              |Ctrl_Data                                        |30     |29      |0       |21      |0       |0       |
|  wendu                             |DS18B20                                          |215    |170     |45      |73      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |847    |463     |131     |603     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |847    |463     |131     |603     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |398    |175     |0       |398     |0       |0       |
|        reg_inst                    |register                                         |395    |172     |0       |395     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |449    |288     |131     |205     |0       |0       |
|        bus_inst                    |bus_top                                          |233    |157     |74      |91      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |48     |32      |16      |18      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det                                          |176    |116     |58      |64      |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det                                          |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[5]$bus_nodes |bus_det                                          |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[6]$bus_nodes |bus_det                                          |3      |3       |0       |3       |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |132    |88      |29      |80      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1885  
    #2          2       735   
    #3          3       246   
    #4          4        84   
    #5        5-10      174   
    #6        11-50      47   
    #7       51-100      1    
    #8       101-500     5    
  Average     2.38            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11840, tnet num: 3217, tinst num: 1329, tnode num: 15910, tedge num: 20462.
TMR-2508 : Levelizing timing graph completed, there are 67 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 3217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 5 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 4. Number of clock nets = 5 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 91c5aad0b6483ec18d27e049aa6ffcc9d608bcb1c365cbd3b0a7ba223cb30a83 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1329
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 3219, pip num: 28720
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 17
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1885 valid insts, and 73642 bits set as '1'.
BIT-1004 : the usercode register value: 00000000000100100011010101101111
BIT-1004 : PLL setting string = 1001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.439421s wall, 32.468750s user + 0.093750s system = 32.562500s CPU (598.6%)

RUN-1004 : used memory is 605 MB, reserved memory is 568 MB, peak memory is 723 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230714_174624.log"
