============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 11:59:51 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/ADDA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 59 trigger nets, 59 data nets.
KIT-1004 : Chipwatcher code = 1101011101011001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=156) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=156) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3980/20 useful/useless nets, 2379/9 useful/useless insts
SYN-1016 : Merged 24 instances.
SYN-1032 : 3593/18 useful/useless nets, 2887/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 474 better
SYN-1014 : Optimize round 2
SYN-1032 : 3212/45 useful/useless nets, 2506/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 3284/442 useful/useless nets, 2630/64 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 575 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 58 instances.
SYN-2501 : Optimize round 1, 118 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 3792/5 useful/useless nets, 3138/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 14699, tnet num: 3792, tinst num: 3137, tnode num: 19656, tedge num: 23566.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3792 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 285 (3.29), #lev = 7 (1.55)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 285 (3.32), #lev = 6 (1.63)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 599 instances into 285 LUTs, name keeping = 70%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 481 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 164 adder to BLE ...
SYN-4008 : Packed 164 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.799929s wall, 1.718750s user + 0.062500s system = 1.781250s CPU (99.0%)

RUN-1004 : used memory is 179 MB, reserved memory is 141 MB, peak memory is 215 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (332 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2588 instances
RUN-0007 : 665 luts, 1473 seqs, 233 mslices, 114 lslices, 34 pads, 57 brams, 5 dsps
RUN-1001 : There are total 3243 nets
RUN-1001 : 2143 nets have 2 pins
RUN-1001 : 886 nets have [3 - 5] pins
RUN-1001 : 117 nets have [6 - 10] pins
RUN-1001 : 53 nets have [11 - 20] pins
RUN-1001 : 33 nets have [21 - 99] pins
RUN-1001 : 11 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     271     
RUN-1001 :   No   |  No   |  Yes  |     345     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     457     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    5    |  17   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 26
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2586 instances, 665 luts, 1473 seqs, 347 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-0007 : Cell area utilization is 7%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13512, tnet num: 3241, tinst num: 2586, tnode num: 18816, tedge num: 22875.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.392611s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (103.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 820136
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2586.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 706513, overlap = 135
PHY-3002 : Step(2): len = 530972, overlap = 135
PHY-3002 : Step(3): len = 494828, overlap = 132.75
PHY-3002 : Step(4): len = 475333, overlap = 135
PHY-3002 : Step(5): len = 459112, overlap = 137.25
PHY-3002 : Step(6): len = 446599, overlap = 135
PHY-3002 : Step(7): len = 431337, overlap = 132.75
PHY-3002 : Step(8): len = 419555, overlap = 135
PHY-3002 : Step(9): len = 411383, overlap = 135
PHY-3002 : Step(10): len = 394398, overlap = 137.25
PHY-3002 : Step(11): len = 385242, overlap = 137.25
PHY-3002 : Step(12): len = 378078, overlap = 137.25
PHY-3002 : Step(13): len = 364611, overlap = 137.25
PHY-3002 : Step(14): len = 352068, overlap = 135
PHY-3002 : Step(15): len = 347390, overlap = 135
PHY-3002 : Step(16): len = 334872, overlap = 132.75
PHY-3002 : Step(17): len = 322099, overlap = 132.75
PHY-3002 : Step(18): len = 316326, overlap = 132.75
PHY-3002 : Step(19): len = 310076, overlap = 132.75
PHY-3002 : Step(20): len = 279293, overlap = 130.5
PHY-3002 : Step(21): len = 269263, overlap = 130.5
PHY-3002 : Step(22): len = 265999, overlap = 130.5
PHY-3002 : Step(23): len = 235008, overlap = 135
PHY-3002 : Step(24): len = 225730, overlap = 135
PHY-3002 : Step(25): len = 220657, overlap = 135
PHY-3002 : Step(26): len = 216290, overlap = 135
PHY-3002 : Step(27): len = 213004, overlap = 135
PHY-3002 : Step(28): len = 207043, overlap = 135
PHY-3002 : Step(29): len = 197590, overlap = 135
PHY-3002 : Step(30): len = 192931, overlap = 135
PHY-3002 : Step(31): len = 189302, overlap = 135
PHY-3002 : Step(32): len = 183701, overlap = 135
PHY-3002 : Step(33): len = 178158, overlap = 135.375
PHY-3002 : Step(34): len = 174824, overlap = 135.438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.08306e-06
PHY-3002 : Step(35): len = 175743, overlap = 135.188
PHY-3002 : Step(36): len = 171897, overlap = 130.688
PHY-3002 : Step(37): len = 170866, overlap = 130.75
PHY-3002 : Step(38): len = 169582, overlap = 126
PHY-3002 : Step(39): len = 165462, overlap = 130.625
PHY-3002 : Step(40): len = 164252, overlap = 130.625
PHY-3002 : Step(41): len = 161586, overlap = 128.25
PHY-3002 : Step(42): len = 157115, overlap = 128.25
PHY-3002 : Step(43): len = 155384, overlap = 130.5
PHY-3002 : Step(44): len = 152303, overlap = 128.25
PHY-3002 : Step(45): len = 145900, overlap = 128.5
PHY-3002 : Step(46): len = 142950, overlap = 128.688
PHY-3002 : Step(47): len = 140695, overlap = 129.438
PHY-3002 : Step(48): len = 137148, overlap = 134.125
PHY-3002 : Step(49): len = 134974, overlap = 132
PHY-3002 : Step(50): len = 132592, overlap = 129.875
PHY-3002 : Step(51): len = 130517, overlap = 130.312
PHY-3002 : Step(52): len = 126942, overlap = 135.062
PHY-3002 : Step(53): len = 123241, overlap = 135.5
PHY-3002 : Step(54): len = 121633, overlap = 133.125
PHY-3002 : Step(55): len = 118920, overlap = 133.188
PHY-3002 : Step(56): len = 117529, overlap = 133.25
PHY-3002 : Step(57): len = 112078, overlap = 131.5
PHY-3002 : Step(58): len = 108997, overlap = 134.125
PHY-3002 : Step(59): len = 107249, overlap = 134.375
PHY-3002 : Step(60): len = 105837, overlap = 134.562
PHY-3002 : Step(61): len = 104889, overlap = 134.562
PHY-3002 : Step(62): len = 104650, overlap = 134.5
PHY-3002 : Step(63): len = 104004, overlap = 134.438
PHY-3002 : Step(64): len = 102756, overlap = 134.438
PHY-3002 : Step(65): len = 101693, overlap = 134.438
PHY-3002 : Step(66): len = 99601.6, overlap = 132.062
PHY-3002 : Step(67): len = 97223.1, overlap = 134.375
PHY-3002 : Step(68): len = 95602.2, overlap = 134.438
PHY-3002 : Step(69): len = 94311.4, overlap = 134.5
PHY-3002 : Step(70): len = 93042.4, overlap = 130.562
PHY-3002 : Step(71): len = 86015, overlap = 131.062
PHY-3002 : Step(72): len = 84897.1, overlap = 129.062
PHY-3002 : Step(73): len = 83265, overlap = 132.062
PHY-3002 : Step(74): len = 82816.4, overlap = 132.469
PHY-3002 : Step(75): len = 82459, overlap = 137.031
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.16613e-06
PHY-3002 : Step(76): len = 82416.6, overlap = 132.25
PHY-3002 : Step(77): len = 82649.6, overlap = 129.75
PHY-3002 : Step(78): len = 82434.9, overlap = 132
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 1.63323e-05
PHY-3002 : Step(79): len = 83018.5, overlap = 127.438
PHY-3002 : Step(80): len = 83311.1, overlap = 127.438
PHY-3002 : Step(81): len = 82981.4, overlap = 127.438
PHY-3002 : Step(82): len = 83052.7, overlap = 127.438
PHY-3002 : Step(83): len = 82754.8, overlap = 120.562
PHY-3002 : Step(84): len = 82603.9, overlap = 120.625
PHY-3002 : Step(85): len = 81900.2, overlap = 120.625
PHY-3002 : Step(86): len = 81567.4, overlap = 122.875
PHY-3002 : Step(87): len = 81138.7, overlap = 122.688
PHY-3002 : Step(88): len = 80840.9, overlap = 120.438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 3.26645e-05
PHY-3002 : Step(89): len = 81148.7, overlap = 122.688
PHY-3002 : Step(90): len = 81516.6, overlap = 120.438
PHY-3002 : Step(91): len = 81897.6, overlap = 120.438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 6.5329e-05
PHY-3002 : Step(92): len = 82955.4, overlap = 115.938
PHY-3002 : Step(93): len = 83698.8, overlap = 111.438
PHY-3002 : Step(94): len = 84742.5, overlap = 111.25
PHY-3002 : Step(95): len = 85214.3, overlap = 111.188
PHY-3002 : Step(96): len = 85018, overlap = 113.438
PHY-3002 : Step(97): len = 84626, overlap = 117.938
PHY-3002 : Step(98): len = 84482.5, overlap = 117.938
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.000130658
PHY-3002 : Step(99): len = 85060, overlap = 113.5
PHY-3002 : Step(100): len = 85569.1, overlap = 113.5
PHY-3002 : Step(101): len = 86132.9, overlap = 111.25
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.000261316
PHY-3002 : Step(102): len = 86344.2, overlap = 111.25
PHY-3002 : Step(103): len = 86624.7, overlap = 111.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011223s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (139.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.098769s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.20673e-05
PHY-3002 : Step(104): len = 104334, overlap = 26.0312
PHY-3002 : Step(105): len = 102689, overlap = 26.7188
PHY-3002 : Step(106): len = 101920, overlap = 30.0625
PHY-3002 : Step(107): len = 101084, overlap = 26.2812
PHY-3002 : Step(108): len = 100253, overlap = 30.8438
PHY-3002 : Step(109): len = 98334.4, overlap = 30.8438
PHY-3002 : Step(110): len = 97810, overlap = 32.6875
PHY-3002 : Step(111): len = 96763.4, overlap = 35.5
PHY-3002 : Step(112): len = 95446.2, overlap = 37.625
PHY-3002 : Step(113): len = 93507.8, overlap = 36.4375
PHY-3002 : Step(114): len = 92469.3, overlap = 36.9688
PHY-3002 : Step(115): len = 91549.9, overlap = 37.2188
PHY-3002 : Step(116): len = 90701.2, overlap = 38.4062
PHY-3002 : Step(117): len = 89723, overlap = 38.8125
PHY-3002 : Step(118): len = 89069, overlap = 39
PHY-3002 : Step(119): len = 88188.6, overlap = 39.4688
PHY-3002 : Step(120): len = 88176.9, overlap = 39.4375
PHY-3002 : Step(121): len = 87293.7, overlap = 37.125
PHY-3002 : Step(122): len = 86283.5, overlap = 36.8438
PHY-3002 : Step(123): len = 85902.8, overlap = 36.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000124135
PHY-3002 : Step(124): len = 85599.7, overlap = 37.4062
PHY-3002 : Step(125): len = 85677.2, overlap = 37.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000248269
PHY-3002 : Step(126): len = 85593.6, overlap = 37.2812
PHY-3002 : Step(127): len = 85593.6, overlap = 37.2812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.110345s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.37444e-05
PHY-3002 : Step(128): len = 85778.4, overlap = 91.0312
PHY-3002 : Step(129): len = 86330.1, overlap = 85.5
PHY-3002 : Step(130): len = 87390.8, overlap = 82.6562
PHY-3002 : Step(131): len = 87735.7, overlap = 77.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000107489
PHY-3002 : Step(132): len = 87621.8, overlap = 77.3438
PHY-3002 : Step(133): len = 88691.4, overlap = 61.5312
PHY-3002 : Step(134): len = 89306.8, overlap = 61.3125
PHY-3002 : Step(135): len = 89641.6, overlap = 60.0938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000214978
PHY-3002 : Step(136): len = 89646.8, overlap = 57.5938
PHY-3002 : Step(137): len = 90793.3, overlap = 53.5625
PHY-3002 : Step(138): len = 92073.4, overlap = 49.6562
PHY-3002 : Step(139): len = 93120.3, overlap = 50.0938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000429955
PHY-3002 : Step(140): len = 93089.3, overlap = 47.3125
PHY-3002 : Step(141): len = 92983.9, overlap = 47.375
PHY-3002 : Step(142): len = 93184.5, overlap = 43.25
PHY-3002 : Step(143): len = 93470, overlap = 42.3125
PHY-3002 : Step(144): len = 93772.7, overlap = 41.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13512, tnet num: 3241, tinst num: 2586, tnode num: 18816, tedge num: 22875.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 143.22 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/3243.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 117656, over cnt = 469(1%), over = 1701, worst = 18
PHY-1001 : End global iterations;  0.243646s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (141.1%)

PHY-1001 : Congestion index: top1 = 46.49, top5 = 32.54, top10 = 24.68, top15 = 20.06.
PHY-1001 : End incremental global routing;  0.333661s wall, 0.343750s user + 0.078125s system = 0.421875s CPU (126.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.172838s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.4%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 2546 has valid locations, 53 needs to be replaced
PHY-3001 : design contains 2638 instances, 665 luts, 1525 seqs, 347 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 94403.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13720, tnet num: 3293, tinst num: 2638, tnode num: 19180, tedge num: 23187.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.611441s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(145): len = 95005.4, overlap = 1
PHY-3002 : Step(146): len = 95464.1, overlap = 1.0625
PHY-3002 : Step(147): len = 95781.4, overlap = 1.125
PHY-3002 : Step(148): len = 95887, overlap = 1.125
PHY-3002 : Step(149): len = 96102.5, overlap = 1.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.149269s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000764565
PHY-3002 : Step(150): len = 95924.1, overlap = 41.125
PHY-3002 : Step(151): len = 95824, overlap = 41.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00152913
PHY-3002 : Step(152): len = 95805.2, overlap = 41.125
PHY-3002 : Step(153): len = 95805.2, overlap = 41.125
PHY-3001 : Final: Len = 95805.2, Over = 41.125
PHY-3001 : End incremental placement;  0.990380s wall, 1.328125s user + 0.218750s system = 1.546875s CPU (156.2%)

OPT-1001 : Total overflow 144.34 peak overflow 3.09
OPT-1001 : End high-fanout net optimization;  1.590527s wall, 1.937500s user + 0.296875s system = 2.234375s CPU (140.5%)

OPT-1001 : Current memory(MB): used = 261, reserve = 221, peak = 262.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2518/3295.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 120296, over cnt = 471(1%), over = 1690, worst = 18
PHY-1002 : len = 129240, over cnt = 305(0%), over = 725, worst = 18
PHY-1002 : len = 135224, over cnt = 86(0%), over = 161, worst = 15
PHY-1002 : len = 136960, over cnt = 19(0%), over = 36, worst = 6
PHY-1002 : len = 136936, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.386680s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (105.1%)

PHY-1001 : Congestion index: top1 = 41.34, top5 = 30.74, top10 = 24.79, top15 = 21.16.
OPT-1001 : End congestion update;  0.471932s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (105.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.150662s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.3%)

OPT-0007 : Start: WNS -11848 TNS -284852 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -11848 TNS -284852 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.628678s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (104.4%)

OPT-1001 : Current memory(MB): used = 262, reserve = 221, peak = 262.
OPT-1001 : End physical optimization;  2.684878s wall, 3.156250s user + 0.296875s system = 3.453125s CPU (128.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 665 LUT to BLE ...
SYN-4008 : Packed 665 LUT and 247 SEQ to BLE.
SYN-4003 : Packing 1278 remaining SEQ's ...
SYN-4005 : Packed 362 SEQ with LUT/SLICE
SYN-4006 : 93 single LUT's are left
SYN-4006 : 916 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1581/2089 primitive instances ...
PHY-3001 : End packing;  0.160913s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1300 instances
RUN-1001 : 599 mslices, 598 lslices, 34 pads, 57 brams, 5 dsps
RUN-1001 : There are total 3063 nets
RUN-1001 : 1920 nets have 2 pins
RUN-1001 : 928 nets have [3 - 5] pins
RUN-1001 : 122 nets have [6 - 10] pins
RUN-1001 : 51 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
PHY-3001 : design contains 1298 instances, 1197 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Cell area utilization is 15%
PHY-3001 : After packing: Len = 97319, Over = 77.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11736, tnet num: 3061, tinst num: 1298, tnode num: 15675, tedge num: 20407.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.676669s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.90865e-05
PHY-3002 : Step(154): len = 96141.9, overlap = 79.25
PHY-3002 : Step(155): len = 95258.8, overlap = 79.25
PHY-3002 : Step(156): len = 94663.8, overlap = 80
PHY-3002 : Step(157): len = 93921.6, overlap = 79
PHY-3002 : Step(158): len = 93607, overlap = 77.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.8173e-05
PHY-3002 : Step(159): len = 94040.3, overlap = 76.25
PHY-3002 : Step(160): len = 95367.6, overlap = 73.25
PHY-3002 : Step(161): len = 96161.8, overlap = 76.25
PHY-3002 : Step(162): len = 95873.2, overlap = 76
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000156346
PHY-3002 : Step(163): len = 96617.9, overlap = 74.5
PHY-3002 : Step(164): len = 96940.2, overlap = 73.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.276786s wall, 0.359375s user + 0.421875s system = 0.781250s CPU (282.3%)

PHY-3001 : Trial Legalized: Len = 116020
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.150223s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000596149
PHY-3002 : Step(165): len = 112363, overlap = 8
PHY-3002 : Step(166): len = 109800, overlap = 15
PHY-3002 : Step(167): len = 106858, overlap = 21.25
PHY-3002 : Step(168): len = 104822, overlap = 24
PHY-3002 : Step(169): len = 103832, overlap = 26.5
PHY-3002 : Step(170): len = 103028, overlap = 29.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0011923
PHY-3002 : Step(171): len = 103387, overlap = 28.75
PHY-3002 : Step(172): len = 103622, overlap = 31
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00238459
PHY-3002 : Step(173): len = 103589, overlap = 31.5
PHY-3002 : Step(174): len = 103602, overlap = 32.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011339s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (137.8%)

PHY-3001 : Legalized: Len = 110929, Over = 0
PHY-3001 : Spreading special nets. 33 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.020115s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (155.4%)

PHY-3001 : 47 instances has been re-located, deltaX = 33, deltaY = 28, maxDist = 3.
PHY-3001 : Final: Len = 111793, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11736, tnet num: 3061, tinst num: 1298, tnode num: 15675, tedge num: 20407.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 141/3063.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 138920, over cnt = 363(1%), over = 616, worst = 8
PHY-1002 : len = 141632, over cnt = 202(0%), over = 270, worst = 5
PHY-1002 : len = 143744, over cnt = 51(0%), over = 74, worst = 3
PHY-1002 : len = 144224, over cnt = 32(0%), over = 41, worst = 3
PHY-1002 : len = 144824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.485718s wall, 0.609375s user + 0.046875s system = 0.656250s CPU (135.1%)

PHY-1001 : Congestion index: top1 = 34.25, top5 = 27.88, top10 = 23.71, top15 = 20.92.
PHY-1001 : End incremental global routing;  0.590811s wall, 0.718750s user + 0.046875s system = 0.765625s CPU (129.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.160922s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (106.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1258 has valid locations, 8 needs to be replaced
PHY-3001 : design contains 1305 instances, 1204 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 113816
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11798, tnet num: 3067, tinst num: 1305, tnode num: 15751, tedge num: 20482.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.701571s wall, 0.687500s user + 0.015625s system = 0.703125s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(175): len = 113189, overlap = 0
PHY-3002 : Step(176): len = 112971, overlap = 0
PHY-3002 : Step(177): len = 112894, overlap = 0.25
PHY-3002 : Step(178): len = 112854, overlap = 0
PHY-3002 : Step(179): len = 112818, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.127510s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (110.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.78993e-05
PHY-3002 : Step(180): len = 112810, overlap = 0.25
PHY-3002 : Step(181): len = 112810, overlap = 0.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006670s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (234.3%)

PHY-3001 : Legalized: Len = 112828, Over = 0
PHY-3001 : End spreading;  0.011972s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (130.5%)

PHY-3001 : Final: Len = 112828, Over = 0
PHY-3001 : End incremental placement;  1.041734s wall, 1.140625s user + 0.140625s system = 1.281250s CPU (123.0%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  1.897984s wall, 2.171875s user + 0.187500s system = 2.359375s CPU (124.3%)

OPT-1001 : Current memory(MB): used = 270, reserve = 230, peak = 270.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2670/3069.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 145792, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 145816, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 145832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.071443s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (153.1%)

PHY-1001 : Congestion index: top1 = 34.25, top5 = 27.93, top10 = 23.75, top15 = 20.98.
OPT-1001 : End congestion update;  0.166538s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (112.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.127435s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (110.4%)

OPT-0007 : Start: WNS -11848 TNS -284402 NUM_FEPS 28
OPT-1001 : End path based optimization;  0.294679s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (111.3%)

OPT-1001 : Current memory(MB): used = 270, reserve = 230, peak = 270.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.128565s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2684/3069.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 145832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.016433s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (95.1%)

PHY-1001 : Congestion index: top1 = 34.25, top5 = 27.93, top10 = 23.75, top15 = 20.98.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.124463s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (100.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -11848 TNS -284402 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -11848ps with logic level 2 
RUN-1001 :       #2 path slack -11848ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 3069 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 3069 nets
OPT-1001 : End physical optimization;  3.251199s wall, 3.562500s user + 0.187500s system = 3.750000s CPU (115.3%)

RUN-1003 : finish command "place" in  12.269249s wall, 18.984375s user + 4.109375s system = 23.093750s CPU (188.2%)

RUN-1004 : used memory is 245 MB, reserved memory is 205 MB, peak memory is 270 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1307 instances
RUN-1001 : 599 mslices, 605 lslices, 34 pads, 57 brams, 5 dsps
RUN-1001 : There are total 3069 nets
RUN-1001 : 1916 nets have 2 pins
RUN-1001 : 929 nets have [3 - 5] pins
RUN-1001 : 127 nets have [6 - 10] pins
RUN-1001 : 55 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11798, tnet num: 3067, tinst num: 1305, tnode num: 15751, tedge num: 20482.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 599 mslices, 605 lslices, 34 pads, 57 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 138336, over cnt = 373(1%), over = 646, worst = 8
PHY-1002 : len = 141008, over cnt = 229(0%), over = 332, worst = 4
PHY-1002 : len = 144248, over cnt = 40(0%), over = 59, worst = 3
PHY-1002 : len = 144936, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 145032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.468561s wall, 0.640625s user + 0.093750s system = 0.734375s CPU (156.7%)

PHY-1001 : Congestion index: top1 = 34.44, top5 = 27.93, top10 = 23.73, top15 = 20.91.
PHY-1001 : End global routing;  0.561852s wall, 0.734375s user + 0.093750s system = 0.828125s CPU (147.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 283, reserve = 244, peak = 296.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 542, reserve = 505, peak = 542.
PHY-1001 : End build detailed router design. 4.751188s wall, 4.703125s user + 0.046875s system = 4.750000s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 47272, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.029151s wall, 2.031250s user + 0.015625s system = 2.046875s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 575, reserve = 539, peak = 575.
PHY-1001 : End phase 1; 2.035247s wall, 2.046875s user + 0.015625s system = 2.062500s CPU (101.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 68% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 456440, over cnt = 108(0%), over = 108, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 576, reserve = 540, peak = 576.
PHY-1001 : End initial routed; 9.156640s wall, 11.015625s user + 0.187500s system = 11.203125s CPU (122.3%)

PHY-1001 : Update timing.....
PHY-1001 : 12/2728(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.118   |  -222.139  |  28   
RUN-1001 :   Hold   |   0.080   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.731155s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (98.3%)

PHY-1001 : Current memory(MB): used = 578, reserve = 542, peak = 579.
PHY-1001 : End phase 2; 9.887933s wall, 11.734375s user + 0.187500s system = 11.921875s CPU (120.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS -9.135ns STNS -222.172ns FEP 28.
PHY-1001 : End OPT Iter 1; 0.040104s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.9%)

PHY-1022 : len = 456344, over cnt = 108(0%), over = 108, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.072210s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 455248, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.174446s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (116.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 455320, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.070647s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 454880, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.036427s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.8%)

PHY-1001 : Update timing.....
PHY-1001 : 12/2728(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.135   |  -222.172  |  28   
RUN-1001 :   Hold   |   0.080   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.720166s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 16 feed throughs used by 15 nets
PHY-1001 : End commit to database; 0.524299s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (101.3%)

PHY-1001 : Current memory(MB): used = 595, reserve = 559, peak = 595.
PHY-1001 : End phase 3; 1.801233s wall, 1.828125s user + 0.000000s system = 1.828125s CPU (101.5%)

PHY-1001 : ===== Detail Route Phase 4 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS -9.135ns STNS -222.172ns FEP 28.
PHY-1001 : End OPT Iter 1; 0.052990s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.5%)

PHY-1022 : len = 454880, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End optimize timing; 0.085305s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.6%)

PHY-0007 : Phase: 4; Congestion: {, , , }; Timing: {-9.135ns, -222.172ns, 28}
PHY-1001 : Update timing.....
PHY-1001 : 12/2728(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.135   |  -222.172  |  28   
RUN-1001 :   Hold   |   0.080   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.707167s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (101.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 16 feed throughs used by 15 nets
PHY-1001 : End commit to database; 0.514422s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 596, reserve = 560, peak = 596.
PHY-1001 : End phase 4; 1.309637s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.2%)

PHY-1003 : Routed, final wirelength = 454880
PHY-1001 : Current memory(MB): used = 596, reserve = 561, peak = 596.
PHY-1001 : End export database. 0.015903s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.2%)

PHY-1001 : End detail routing;  20.068216s wall, 21.906250s user + 0.250000s system = 22.156250s CPU (110.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11798, tnet num: 3067, tinst num: 1305, tnode num: 15751, tedge num: 20482.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  22.048375s wall, 24.062500s user + 0.343750s system = 24.406250s CPU (110.7%)

RUN-1004 : used memory is 570 MB, reserved memory is 533 MB, peak memory is 597 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1377   out of  19600    7.03%
#reg                     1607   out of  19600    8.20%
#le                      2293
  #lut only               686   out of   2293   29.92%
  #reg only               916   out of   2293   39.95%
  #lut&reg                691   out of   2293   30.14%
#dsp                        5   out of     29   17.24%
#bram                      57   out of     64   89.06%
  #bram9k                  57
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                           Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0            651
#2        config_inst_syn_9               GCLK               config             config_inst.jtck                 180
#3        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3            108
#4        wendu/clk_us                    GCLK               mslice             u_uart/U0/cnt_b[11]_syn_33.q0    39
#5        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                    11
#6        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4            1
#7        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                  1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |2293   |1030    |347     |1638    |57      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1119   |304     |133     |923     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |24     |18      |6       |21      |0       |0       |
|    demodu                          |Demodulation                                     |540    |115     |58      |434     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |55     |0       |0       |55      |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |23     |10      |0       |23      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |18      |0       |27      |0       |0       |
|    integ                           |Integration                                      |138    |28      |14      |112     |0       |0       |
|    modu                            |Modulation                                       |91     |24      |21      |87      |0       |1       |
|    rs422                           |Rs422Output                                      |307    |105     |29      |251     |0       |4       |
|    trans                           |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                            |UART_Control                                     |112    |101     |7       |62      |0       |0       |
|    U0                              |speed_select_Tx                                  |36     |29      |7       |16      |0       |0       |
|    U1                              |uart_tx                                          |16     |14      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data                                        |60     |58      |0       |30      |0       |0       |
|  wendu                             |DS18B20                                          |206    |161     |45      |74      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |758    |420     |121     |510     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |758    |420     |121     |510     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |325    |156     |0       |325     |0       |0       |
|        reg_inst                    |register                                         |322    |153     |0       |322     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |433    |264     |121     |185     |0       |0       |
|        bus_inst                    |bus_top                                          |185    |121     |64      |58      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |100    |66      |34      |32      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det                                          |46     |30      |16      |15      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det                                          |38     |24      |14      |10      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |144    |82      |29      |91      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1880  
    #2          2       563   
    #3          3       287   
    #4          4        79   
    #5        5-10      130   
    #6        11-50      69   
    #7       51-100      15   
    #8       101-500     3    
  Average     2.55            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11798, tnet num: 3067, tinst num: 1305, tnode num: 15751, tedge num: 20482.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 3067 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 449d723a5d69124cfb6b5e2627f2e596a1f1996c62c154540ae03241ff9a8d54 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1305
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 3069, pip num: 29696
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 16
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1942 valid insts, and 75723 bits set as '1'.
BIT-1004 : the usercode register value: 00000000111010011101011101011001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  9.328140s wall, 54.781250s user + 0.234375s system = 55.015625s CPU (589.8%)

RUN-1004 : used memory is 603 MB, reserved memory is 567 MB, peak memory is 718 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_115951.log"
