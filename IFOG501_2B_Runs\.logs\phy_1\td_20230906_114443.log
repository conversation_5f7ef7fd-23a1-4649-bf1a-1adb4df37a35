============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Sep  6 11:44:43 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1621 instances
RUN-0007 : 367 luts, 984 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2191 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1634 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1619 instances, 367 luts, 984 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7773, tnet num: 2189, tinst num: 1619, tnode num: 11013, tedge num: 13152.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.282612s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 627904
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1619.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 522179, overlap = 20.25
PHY-3002 : Step(2): len = 483125, overlap = 20.25
PHY-3002 : Step(3): len = 396887, overlap = 20.25
PHY-3002 : Step(4): len = 343553, overlap = 15.75
PHY-3002 : Step(5): len = 314009, overlap = 18
PHY-3002 : Step(6): len = 296236, overlap = 20.25
PHY-3002 : Step(7): len = 288706, overlap = 20.25
PHY-3002 : Step(8): len = 282660, overlap = 20.25
PHY-3002 : Step(9): len = 272685, overlap = 20.25
PHY-3002 : Step(10): len = 265999, overlap = 20.25
PHY-3002 : Step(11): len = 259045, overlap = 20.25
PHY-3002 : Step(12): len = 252351, overlap = 20.25
PHY-3002 : Step(13): len = 244546, overlap = 20.25
PHY-3002 : Step(14): len = 239624, overlap = 20.25
PHY-3002 : Step(15): len = 233314, overlap = 20.25
PHY-3002 : Step(16): len = 225353, overlap = 20.25
PHY-3002 : Step(17): len = 219946, overlap = 20.25
PHY-3002 : Step(18): len = 215643, overlap = 20.25
PHY-3002 : Step(19): len = 208997, overlap = 20.25
PHY-3002 : Step(20): len = 204583, overlap = 20.25
PHY-3002 : Step(21): len = 200514, overlap = 20.25
PHY-3002 : Step(22): len = 194541, overlap = 20.25
PHY-3002 : Step(23): len = 190429, overlap = 20.25
PHY-3002 : Step(24): len = 187001, overlap = 20.25
PHY-3002 : Step(25): len = 182110, overlap = 20.25
PHY-3002 : Step(26): len = 177332, overlap = 20.25
PHY-3002 : Step(27): len = 173266, overlap = 20.25
PHY-3002 : Step(28): len = 169313, overlap = 20.25
PHY-3002 : Step(29): len = 166218, overlap = 20.25
PHY-3002 : Step(30): len = 160776, overlap = 20.25
PHY-3002 : Step(31): len = 152781, overlap = 20.25
PHY-3002 : Step(32): len = 149872, overlap = 20.25
PHY-3002 : Step(33): len = 147324, overlap = 20.25
PHY-3002 : Step(34): len = 138648, overlap = 20.25
PHY-3002 : Step(35): len = 125195, overlap = 20.25
PHY-3002 : Step(36): len = 123421, overlap = 20.25
PHY-3002 : Step(37): len = 120255, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100135
PHY-3002 : Step(38): len = 122097, overlap = 13.5
PHY-3002 : Step(39): len = 122217, overlap = 11.25
PHY-3002 : Step(40): len = 120152, overlap = 11.25
PHY-3002 : Step(41): len = 117904, overlap = 15.75
PHY-3002 : Step(42): len = 115767, overlap = 15.75
PHY-3002 : Step(43): len = 114525, overlap = 6.75
PHY-3002 : Step(44): len = 110504, overlap = 11.25
PHY-3002 : Step(45): len = 108865, overlap = 11.25
PHY-3002 : Step(46): len = 106311, overlap = 13.5
PHY-3002 : Step(47): len = 103458, overlap = 9
PHY-3002 : Step(48): len = 95986.3, overlap = 9
PHY-3002 : Step(49): len = 94523.3, overlap = 13.5
PHY-3002 : Step(50): len = 93094.2, overlap = 13.5
PHY-3002 : Step(51): len = 92214.8, overlap = 13.5
PHY-3002 : Step(52): len = 91995.5, overlap = 13.5
PHY-3002 : Step(53): len = 91013.8, overlap = 9
PHY-3002 : Step(54): len = 90133.6, overlap = 9
PHY-3002 : Step(55): len = 82663.9, overlap = 15.75
PHY-3002 : Step(56): len = 81618.7, overlap = 15.875
PHY-3002 : Step(57): len = 80384.3, overlap = 15.875
PHY-3002 : Step(58): len = 78153.3, overlap = 11.5
PHY-3002 : Step(59): len = 78054.4, overlap = 9.375
PHY-3002 : Step(60): len = 77705.6, overlap = 9.5
PHY-3002 : Step(61): len = 76732.5, overlap = 9.75
PHY-3002 : Step(62): len = 76072.1, overlap = 12.0625
PHY-3002 : Step(63): len = 74548.2, overlap = 12.25
PHY-3002 : Step(64): len = 72920.1, overlap = 12.25
PHY-3002 : Step(65): len = 70573, overlap = 12
PHY-3002 : Step(66): len = 69013.8, overlap = 11.875
PHY-3002 : Step(67): len = 67405, overlap = 14.25
PHY-3002 : Step(68): len = 66854.4, overlap = 12.125
PHY-3002 : Step(69): len = 65978.6, overlap = 9.6875
PHY-3002 : Step(70): len = 63338.1, overlap = 10.1875
PHY-3002 : Step(71): len = 61605.8, overlap = 12.5625
PHY-3002 : Step(72): len = 60329.9, overlap = 10.4375
PHY-3002 : Step(73): len = 59790.8, overlap = 10.375
PHY-3002 : Step(74): len = 59131, overlap = 14.8125
PHY-3002 : Step(75): len = 59060, overlap = 10.25
PHY-3002 : Step(76): len = 58657.4, overlap = 10.125
PHY-3002 : Step(77): len = 56905.5, overlap = 12.6875
PHY-3002 : Step(78): len = 55813, overlap = 17.25
PHY-3002 : Step(79): len = 55080.8, overlap = 15.0625
PHY-3002 : Step(80): len = 54367.2, overlap = 15.125
PHY-3002 : Step(81): len = 54143.7, overlap = 15.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00020027
PHY-3002 : Step(82): len = 54464.2, overlap = 15.125
PHY-3002 : Step(83): len = 54515.4, overlap = 15.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000400539
PHY-3002 : Step(84): len = 54404.9, overlap = 12.875
PHY-3002 : Step(85): len = 54441.3, overlap = 12.875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005715s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (273.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063789s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(86): len = 57708.3, overlap = 12.1562
PHY-3002 : Step(87): len = 56709.1, overlap = 12.4375
PHY-3002 : Step(88): len = 56024.7, overlap = 13.25
PHY-3002 : Step(89): len = 55130.1, overlap = 17.7812
PHY-3002 : Step(90): len = 54758.9, overlap = 17.0312
PHY-3002 : Step(91): len = 53651.9, overlap = 17.2188
PHY-3002 : Step(92): len = 52703.3, overlap = 17.0625
PHY-3002 : Step(93): len = 51955.5, overlap = 17.0938
PHY-3002 : Step(94): len = 50958.5, overlap = 17.9062
PHY-3002 : Step(95): len = 50402.5, overlap = 17.9062
PHY-3002 : Step(96): len = 49966.7, overlap = 17.9688
PHY-3002 : Step(97): len = 49796.1, overlap = 16.9062
PHY-3002 : Step(98): len = 49611.5, overlap = 16.9688
PHY-3002 : Step(99): len = 49210.3, overlap = 17.125
PHY-3002 : Step(100): len = 48599.8, overlap = 17.5625
PHY-3002 : Step(101): len = 48384.9, overlap = 18.0312
PHY-3002 : Step(102): len = 48150.1, overlap = 18.4062
PHY-3002 : Step(103): len = 47712.7, overlap = 19.625
PHY-3002 : Step(104): len = 47697.9, overlap = 19.6875
PHY-3002 : Step(105): len = 47260, overlap = 19.5938
PHY-3002 : Step(106): len = 46953.5, overlap = 19.4375
PHY-3002 : Step(107): len = 46916.6, overlap = 19.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00142744
PHY-3002 : Step(108): len = 46908.7, overlap = 19.5625
PHY-3002 : Step(109): len = 46960.3, overlap = 19.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00285487
PHY-3002 : Step(110): len = 46912.3, overlap = 19.5
PHY-3002 : Step(111): len = 46869, overlap = 19.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063621s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.24906e-05
PHY-3002 : Step(112): len = 47577.1, overlap = 61.3438
PHY-3002 : Step(113): len = 48281.2, overlap = 60.2188
PHY-3002 : Step(114): len = 48460.6, overlap = 59.4688
PHY-3002 : Step(115): len = 48149.9, overlap = 59.8125
PHY-3002 : Step(116): len = 48197.5, overlap = 55.75
PHY-3002 : Step(117): len = 48452, overlap = 52.9062
PHY-3002 : Step(118): len = 49113.2, overlap = 51.5312
PHY-3002 : Step(119): len = 48626.7, overlap = 50.0625
PHY-3002 : Step(120): len = 48708.6, overlap = 49.3125
PHY-3002 : Step(121): len = 48655, overlap = 49.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000144981
PHY-3002 : Step(122): len = 48839.5, overlap = 48.9062
PHY-3002 : Step(123): len = 48839.5, overlap = 48.9062
PHY-3002 : Step(124): len = 48942.1, overlap = 44.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000265043
PHY-3002 : Step(125): len = 49697, overlap = 39.5938
PHY-3002 : Step(126): len = 50725.7, overlap = 38.4688
PHY-3002 : Step(127): len = 52051.9, overlap = 35.9062
PHY-3002 : Step(128): len = 52844.7, overlap = 33.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7773, tnet num: 2189, tinst num: 1619, tnode num: 11013, tedge num: 13152.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 91.34 peak overflow 3.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2191.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56360, over cnt = 256(0%), over = 1052, worst = 21
PHY-1001 : End global iterations;  0.057246s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (300.2%)

PHY-1001 : Congestion index: top1 = 44.03, top5 = 25.70, top10 = 16.43, top15 = 11.62.
PHY-1001 : End incremental global routing;  0.109715s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (199.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065669s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.204991s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (152.4%)

OPT-1001 : Current memory(MB): used = 215, reserve = 179, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1696/2191.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56360, over cnt = 256(0%), over = 1052, worst = 21
PHY-1002 : len = 62840, over cnt = 194(0%), over = 521, worst = 19
PHY-1002 : len = 68520, over cnt = 52(0%), over = 100, worst = 9
PHY-1002 : len = 69680, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 69992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095619s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (130.7%)

PHY-1001 : Congestion index: top1 = 39.25, top5 = 26.46, top10 = 18.72, top15 = 13.72.
OPT-1001 : End congestion update;  0.137888s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (124.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056190s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196511s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.3%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.675735s wall, 0.875000s user + 0.062500s system = 0.937500s CPU (138.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 104 SEQ with LUT/SLICE
SYN-4006 : 101 single LUT's are left
SYN-4006 : 699 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1066/1394 primitive instances ...
PHY-3001 : End packing;  0.048264s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 835 instances
RUN-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2024 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1475 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 833 instances, 786 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52128.2, Over = 62.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2022, tinst num: 833, tnode num: 8901, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.294836s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.84924e-05
PHY-3002 : Step(129): len = 51534.9, overlap = 61.5
PHY-3002 : Step(130): len = 51274.6, overlap = 62.75
PHY-3002 : Step(131): len = 50643, overlap = 66.5
PHY-3002 : Step(132): len = 50361.7, overlap = 67
PHY-3002 : Step(133): len = 50359.9, overlap = 66.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.69848e-05
PHY-3002 : Step(134): len = 50454.5, overlap = 66.75
PHY-3002 : Step(135): len = 51283.1, overlap = 64.75
PHY-3002 : Step(136): len = 51965.1, overlap = 61.25
PHY-3002 : Step(137): len = 52258.9, overlap = 58
PHY-3002 : Step(138): len = 52058.5, overlap = 58.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00011397
PHY-3002 : Step(139): len = 52867.2, overlap = 55.25
PHY-3002 : Step(140): len = 54100.4, overlap = 50.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.111642s wall, 0.125000s user + 0.156250s system = 0.281250s CPU (251.9%)

PHY-3001 : Trial Legalized: Len = 69969
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050490s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000623866
PHY-3002 : Step(141): len = 66874.1, overlap = 5
PHY-3002 : Step(142): len = 64397.1, overlap = 10
PHY-3002 : Step(143): len = 62583.8, overlap = 13
PHY-3002 : Step(144): len = 61562.8, overlap = 15.5
PHY-3002 : Step(145): len = 60602.4, overlap = 19.75
PHY-3002 : Step(146): len = 60065.9, overlap = 22.25
PHY-3002 : Step(147): len = 59439.4, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00124773
PHY-3002 : Step(148): len = 59897.7, overlap = 22
PHY-3002 : Step(149): len = 59979.3, overlap = 22.25
PHY-3002 : Step(150): len = 59965.8, overlap = 22.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00249546
PHY-3002 : Step(151): len = 60148.1, overlap = 22
PHY-3002 : Step(152): len = 60217.8, overlap = 22
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004934s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 65829.9, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005431s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (287.7%)

PHY-3001 : 6 instances has been re-located, deltaX = 2, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 65881.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2022, tinst num: 833, tnode num: 8901, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 72/2024.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72304, over cnt = 115(0%), over = 172, worst = 4
PHY-1002 : len = 73176, over cnt = 43(0%), over = 45, worst = 2
PHY-1002 : len = 73672, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 73736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131730s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (130.5%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.84, top10 = 17.79, top15 = 14.03.
PHY-1001 : End incremental global routing;  0.188021s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (116.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064633s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.282903s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (110.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1802/2024.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006192s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (252.3%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.84, top10 = 17.79, top15 = 14.03.
OPT-1001 : End congestion update;  0.052686s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048256s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 795 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 833 instances, 786 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65835.2, Over = 0
PHY-3001 : End spreading;  0.004842s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 65835.2, Over = 0
PHY-3001 : End incremental legalization;  0.033734s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.148488s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (105.2%)

OPT-1001 : Current memory(MB): used = 224, reserve = 188, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052304s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1795/2024.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73696, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73696, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.027829s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (112.3%)

PHY-1001 : Congestion index: top1 = 31.85, top5 = 22.87, top10 = 17.81, top15 = 14.03.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058071s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.413793
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.920737s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (103.5%)

RUN-1003 : finish command "place" in  5.311953s wall, 8.843750s user + 2.656250s system = 11.500000s CPU (216.5%)

RUN-1004 : used memory is 204 MB, reserved memory is 168 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 835 instances
RUN-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2024 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1475 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2022, tinst num: 833, tnode num: 8901, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71576, over cnt = 131(0%), over = 185, worst = 4
PHY-1002 : len = 72520, over cnt = 56(0%), over = 58, worst = 2
PHY-1002 : len = 73120, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 73248, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121749s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (179.7%)

PHY-1001 : Congestion index: top1 = 31.85, top5 = 22.73, top10 = 17.74, top15 = 13.96.
PHY-1001 : End global routing;  0.177052s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (150.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 205, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.207392s wall, 3.140625s user + 0.062500s system = 3.203125s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33544, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.291346s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 532, reserve = 503, peak = 532.
PHY-1001 : End phase 1; 1.297088s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181464, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End initial routed; 1.032843s wall, 1.531250s user + 0.156250s system = 1.687500s CPU (163.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1791(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.156   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.377274s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End phase 2; 1.410198s wall, 1.906250s user + 0.156250s system = 2.062500s CPU (146.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181464, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014460s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181384, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.038482s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (81.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181472, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.030672s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (101.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181488, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021963s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (71.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1791(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.156   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.365446s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.165817s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.2%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 550.
PHY-1001 : End phase 3; 0.763064s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (98.3%)

PHY-1003 : Routed, final wirelength = 181488
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.011546s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (135.3%)

PHY-1001 : End detail routing;  6.869419s wall, 7.312500s user + 0.218750s system = 7.531250s CPU (109.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2022, tinst num: 833, tnode num: 8901, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.770937s wall, 8.250000s user + 0.265625s system = 8.515625s CPU (109.6%)

RUN-1004 : used memory is 525 MB, reserved memory is 496 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      815   out of  19600    4.16%
#reg                     1074   out of  19600    5.48%
#le                      1514
  #lut only               440   out of   1514   29.06%
  #reg only               699   out of   1514   46.17%
  #lut&reg                375   out of   1514   24.77%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         472
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1514   |594     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1124   |305     |129     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |19      |7       |19      |0       |0       |
|    demodu                  |Demodulation                                     |526    |119     |53      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |164    |61      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |53     |0       |0       |53      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |18      |0       |28      |0       |0       |
|    integ                   |Integration                                      |137    |15      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |90     |29      |21      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |97      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |95     |79      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |30     |22      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |20      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |42     |37      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1439  
    #2          2       312   
    #3          3        97   
    #4          4        20   
    #5        5-10       81   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2022, tinst num: 833, tnode num: 8901, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 833
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2024, pip num: 14701
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1323 valid insts, and 39007 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.111035s wall, 17.593750s user + 0.031250s system = 17.625000s CPU (566.5%)

RUN-1004 : used memory is 548 MB, reserved memory is 516 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230906_114443.log"
