============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 11:37:59 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1626 instances
RUN-0007 : 367 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2196 nets
RUN-1001 : 1637 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1624 instances, 367 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7788, tnet num: 2194, tinst num: 1624, tnode num: 11028, tedge num: 13172.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.277257s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (95.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 578342
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1624.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 551553, overlap = 20.25
PHY-3002 : Step(2): len = 441974, overlap = 18
PHY-3002 : Step(3): len = 370295, overlap = 15.75
PHY-3002 : Step(4): len = 350810, overlap = 15.75
PHY-3002 : Step(5): len = 341873, overlap = 15.75
PHY-3002 : Step(6): len = 331457, overlap = 18
PHY-3002 : Step(7): len = 323985, overlap = 20.25
PHY-3002 : Step(8): len = 317128, overlap = 20.25
PHY-3002 : Step(9): len = 310318, overlap = 20.25
PHY-3002 : Step(10): len = 302832, overlap = 20.25
PHY-3002 : Step(11): len = 297104, overlap = 20.25
PHY-3002 : Step(12): len = 291804, overlap = 20.25
PHY-3002 : Step(13): len = 284807, overlap = 20.25
PHY-3002 : Step(14): len = 278905, overlap = 20.25
PHY-3002 : Step(15): len = 273674, overlap = 20.25
PHY-3002 : Step(16): len = 267956, overlap = 20.25
PHY-3002 : Step(17): len = 262184, overlap = 20.25
PHY-3002 : Step(18): len = 257111, overlap = 20.25
PHY-3002 : Step(19): len = 251079, overlap = 20.25
PHY-3002 : Step(20): len = 245794, overlap = 20.25
PHY-3002 : Step(21): len = 241480, overlap = 20.25
PHY-3002 : Step(22): len = 234948, overlap = 20.25
PHY-3002 : Step(23): len = 229423, overlap = 20.25
PHY-3002 : Step(24): len = 225999, overlap = 20.25
PHY-3002 : Step(25): len = 220064, overlap = 20.25
PHY-3002 : Step(26): len = 210126, overlap = 20.25
PHY-3002 : Step(27): len = 206729, overlap = 20.25
PHY-3002 : Step(28): len = 203556, overlap = 20.25
PHY-3002 : Step(29): len = 194081, overlap = 20.25
PHY-3002 : Step(30): len = 183109, overlap = 20.25
PHY-3002 : Step(31): len = 180745, overlap = 20.25
PHY-3002 : Step(32): len = 176630, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000130202
PHY-3002 : Step(33): len = 177883, overlap = 13.5
PHY-3002 : Step(34): len = 175903, overlap = 9
PHY-3002 : Step(35): len = 172971, overlap = 15.75
PHY-3002 : Step(36): len = 169470, overlap = 13.5
PHY-3002 : Step(37): len = 163823, overlap = 9
PHY-3002 : Step(38): len = 161026, overlap = 2.25
PHY-3002 : Step(39): len = 156951, overlap = 6.75
PHY-3002 : Step(40): len = 154510, overlap = 6.75
PHY-3002 : Step(41): len = 150759, overlap = 9
PHY-3002 : Step(42): len = 147643, overlap = 4.5
PHY-3002 : Step(43): len = 143744, overlap = 4.5
PHY-3002 : Step(44): len = 140965, overlap = 11.25
PHY-3002 : Step(45): len = 136409, overlap = 4.5
PHY-3002 : Step(46): len = 134791, overlap = 4.5
PHY-3002 : Step(47): len = 131645, overlap = 4.5
PHY-3002 : Step(48): len = 128869, overlap = 9
PHY-3002 : Step(49): len = 124703, overlap = 4.5
PHY-3002 : Step(50): len = 123593, overlap = 4.5
PHY-3002 : Step(51): len = 121291, overlap = 4.5
PHY-3002 : Step(52): len = 117241, overlap = 11.25
PHY-3002 : Step(53): len = 112462, overlap = 6.75
PHY-3002 : Step(54): len = 111419, overlap = 4.5
PHY-3002 : Step(55): len = 109512, overlap = 4.5
PHY-3002 : Step(56): len = 106895, overlap = 6.75
PHY-3002 : Step(57): len = 104329, overlap = 4.5
PHY-3002 : Step(58): len = 97958.8, overlap = 11.25
PHY-3002 : Step(59): len = 95056.9, overlap = 6.75
PHY-3002 : Step(60): len = 94432.4, overlap = 6.75
PHY-3002 : Step(61): len = 92880.6, overlap = 9
PHY-3002 : Step(62): len = 91954, overlap = 4.5
PHY-3002 : Step(63): len = 91559.6, overlap = 4.5
PHY-3002 : Step(64): len = 89381.3, overlap = 9
PHY-3002 : Step(65): len = 87372.7, overlap = 9
PHY-3002 : Step(66): len = 84864.8, overlap = 9
PHY-3002 : Step(67): len = 84421.4, overlap = 4.5
PHY-3002 : Step(68): len = 83153.9, overlap = 4.5
PHY-3002 : Step(69): len = 80076.2, overlap = 11.25
PHY-3002 : Step(70): len = 78957.4, overlap = 9
PHY-3002 : Step(71): len = 78138.4, overlap = 9
PHY-3002 : Step(72): len = 76981.4, overlap = 4.5
PHY-3002 : Step(73): len = 76512.4, overlap = 4.5
PHY-3002 : Step(74): len = 74510.6, overlap = 11.25
PHY-3002 : Step(75): len = 73780.8, overlap = 6.75
PHY-3002 : Step(76): len = 73323, overlap = 6.75
PHY-3002 : Step(77): len = 71885.9, overlap = 6.75
PHY-3002 : Step(78): len = 70427.8, overlap = 11.25
PHY-3002 : Step(79): len = 68672.7, overlap = 6.75
PHY-3002 : Step(80): len = 65440.4, overlap = 9
PHY-3002 : Step(81): len = 64264.5, overlap = 6.75
PHY-3002 : Step(82): len = 63308.3, overlap = 6.75
PHY-3002 : Step(83): len = 62698.5, overlap = 9
PHY-3002 : Step(84): len = 62763.8, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000260403
PHY-3002 : Step(85): len = 62760.4, overlap = 6.75
PHY-3002 : Step(86): len = 62863.5, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000520806
PHY-3002 : Step(87): len = 62785.5, overlap = 6.75
PHY-3002 : Step(88): len = 62763.5, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005359s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064313s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(89): len = 65367.6, overlap = 5.5
PHY-3002 : Step(90): len = 64297.1, overlap = 5.625
PHY-3002 : Step(91): len = 63638, overlap = 5.8125
PHY-3002 : Step(92): len = 62456.6, overlap = 4.9375
PHY-3002 : Step(93): len = 61414.5, overlap = 5.1875
PHY-3002 : Step(94): len = 60229.5, overlap = 6.125
PHY-3002 : Step(95): len = 59072, overlap = 6.09375
PHY-3002 : Step(96): len = 58448.7, overlap = 6.625
PHY-3002 : Step(97): len = 56370, overlap = 8
PHY-3002 : Step(98): len = 55929.3, overlap = 7.65625
PHY-3002 : Step(99): len = 55448.3, overlap = 7.8125
PHY-3002 : Step(100): len = 54651.8, overlap = 7.09375
PHY-3002 : Step(101): len = 53806.9, overlap = 6.4375
PHY-3002 : Step(102): len = 53254.4, overlap = 6.09375
PHY-3002 : Step(103): len = 52710.1, overlap = 5.75
PHY-3002 : Step(104): len = 52107.2, overlap = 5.5
PHY-3002 : Step(105): len = 51345.7, overlap = 5.25
PHY-3002 : Step(106): len = 50404.2, overlap = 3.75
PHY-3002 : Step(107): len = 50109.8, overlap = 3.4375
PHY-3002 : Step(108): len = 49261.1, overlap = 3.625
PHY-3002 : Step(109): len = 49132, overlap = 4.0625
PHY-3002 : Step(110): len = 48441.6, overlap = 3.875
PHY-3002 : Step(111): len = 47925.7, overlap = 4.0625
PHY-3002 : Step(112): len = 47668.4, overlap = 4.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00051525
PHY-3002 : Step(113): len = 47596.6, overlap = 4.375
PHY-3002 : Step(114): len = 47606.2, overlap = 4.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0010305
PHY-3002 : Step(115): len = 47652.8, overlap = 4.25
PHY-3002 : Step(116): len = 47763.1, overlap = 4.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066542s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.33769e-05
PHY-3002 : Step(117): len = 47801.8, overlap = 48.0312
PHY-3002 : Step(118): len = 48157.4, overlap = 47.25
PHY-3002 : Step(119): len = 48394.4, overlap = 48.7188
PHY-3002 : Step(120): len = 48220, overlap = 46.7188
PHY-3002 : Step(121): len = 48270.4, overlap = 46.875
PHY-3002 : Step(122): len = 48302, overlap = 46.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000146754
PHY-3002 : Step(123): len = 48260.4, overlap = 46.7188
PHY-3002 : Step(124): len = 48628.6, overlap = 46.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000272642
PHY-3002 : Step(125): len = 49022.7, overlap = 46.375
PHY-3002 : Step(126): len = 49437.7, overlap = 45.0625
PHY-3002 : Step(127): len = 51069.4, overlap = 41.5625
PHY-3002 : Step(128): len = 50986.2, overlap = 40.6875
PHY-3002 : Step(129): len = 51001.1, overlap = 40.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7788, tnet num: 2194, tinst num: 1624, tnode num: 11028, tedge num: 13172.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 110.44 peak overflow 3.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2196.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53704, over cnt = 239(0%), over = 1135, worst = 20
PHY-1001 : End global iterations;  0.062725s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (124.6%)

PHY-1001 : Congestion index: top1 = 45.56, top5 = 25.95, top10 = 16.24, top15 = 11.43.
PHY-1001 : End incremental global routing;  0.112654s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (124.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071960s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (86.9%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1585 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 1629 instances, 367 luts, 990 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 51171
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7808, tnet num: 2199, tinst num: 1629, tnode num: 11063, tedge num: 13202.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.301896s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(130): len = 51272.5, overlap = 2.71875
PHY-3002 : Step(131): len = 51323.7, overlap = 2.71875
PHY-3002 : Step(132): len = 51451.9, overlap = 2.96875
PHY-3002 : Step(133): len = 51460.7, overlap = 3.03125
PHY-3002 : Step(134): len = 51437.2, overlap = 2.96875
PHY-3002 : Step(135): len = 51437.2, overlap = 2.96875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063029s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (124.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000186375
PHY-3002 : Step(136): len = 51452.9, overlap = 40.75
PHY-3002 : Step(137): len = 51469.5, overlap = 40.625
PHY-3001 : Final: Len = 51469.5, Over = 40.625
PHY-3001 : End incremental placement;  0.464835s wall, 0.468750s user + 0.093750s system = 0.562500s CPU (121.0%)

OPT-1001 : Total overflow 110.38 peak overflow 3.00
OPT-1001 : End high-fanout net optimization;  0.686519s wall, 0.687500s user + 0.109375s system = 0.796875s CPU (116.1%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1681/2201.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54080, over cnt = 239(0%), over = 1134, worst = 20
PHY-1002 : len = 62456, over cnt = 188(0%), over = 422, worst = 17
PHY-1002 : len = 66512, over cnt = 51(0%), over = 80, worst = 12
PHY-1002 : len = 67576, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 67944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.099499s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (109.9%)

PHY-1001 : Congestion index: top1 = 39.20, top5 = 25.91, top10 = 18.64, top15 = 13.62.
OPT-1001 : End congestion update;  0.143880s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (119.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060674s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.207079s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (113.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 221.
OPT-1001 : End physical optimization;  1.168959s wall, 1.187500s user + 0.109375s system = 1.296875s CPU (110.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 808 remaining SEQ's ...
SYN-4005 : Packed 100 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 708 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1075/1407 primitive instances ...
PHY-3001 : End packing;  0.049712s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 845 instances
RUN-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2034 nets
RUN-1001 : 1472 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 843 instances, 796 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50969.6, Over = 72.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 843, tnode num: 8954, tedge num: 11577.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.328347s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.27871e-05
PHY-3002 : Step(138): len = 50600.1, overlap = 72.25
PHY-3002 : Step(139): len = 50561.2, overlap = 72
PHY-3002 : Step(140): len = 50043.1, overlap = 75
PHY-3002 : Step(141): len = 50057.2, overlap = 76.75
PHY-3002 : Step(142): len = 49779.6, overlap = 77.25
PHY-3002 : Step(143): len = 49779.6, overlap = 77.25
PHY-3002 : Step(144): len = 49712.8, overlap = 76.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.55742e-05
PHY-3002 : Step(145): len = 50147.8, overlap = 75
PHY-3002 : Step(146): len = 50498.4, overlap = 72.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.35168e-05
PHY-3002 : Step(147): len = 50902.4, overlap = 70.5
PHY-3002 : Step(148): len = 52387.1, overlap = 65.75
PHY-3002 : Step(149): len = 54565.3, overlap = 56.5
PHY-3002 : Step(150): len = 54034.6, overlap = 57.75
PHY-3002 : Step(151): len = 53675.7, overlap = 58
PHY-3002 : Step(152): len = 53577.3, overlap = 58
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00014992
PHY-3002 : Step(153): len = 54836.8, overlap = 54
PHY-3002 : Step(154): len = 55597.1, overlap = 52.5
PHY-3002 : Step(155): len = 55436.1, overlap = 52.5
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00029984
PHY-3002 : Step(156): len = 57246.9, overlap = 50.5
PHY-3002 : Step(157): len = 58605.4, overlap = 48
PHY-3002 : Step(158): len = 58191.1, overlap = 47
PHY-3002 : Step(159): len = 57777, overlap = 47.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.128219s wall, 0.062500s user + 0.062500s system = 0.125000s CPU (97.5%)

PHY-3001 : Trial Legalized: Len = 69719.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050465s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000771777
PHY-3002 : Step(160): len = 67435.4, overlap = 3.75
PHY-3002 : Step(161): len = 65103, overlap = 8.75
PHY-3002 : Step(162): len = 63126.9, overlap = 14.25
PHY-3002 : Step(163): len = 61564.5, overlap = 18.25
PHY-3002 : Step(164): len = 60773.3, overlap = 22.75
PHY-3002 : Step(165): len = 60408.4, overlap = 23.75
PHY-3002 : Step(166): len = 60008.4, overlap = 27.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00154355
PHY-3002 : Step(167): len = 60301.1, overlap = 27.25
PHY-3002 : Step(168): len = 60417.4, overlap = 27.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00308711
PHY-3002 : Step(169): len = 60488.7, overlap = 27.25
PHY-3002 : Step(170): len = 60543.9, overlap = 27.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005308s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (294.4%)

PHY-3001 : Legalized: Len = 65073, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006045s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 1, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 65235, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 843, tnode num: 8954, tedge num: 11577.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 38/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71048, over cnt = 129(0%), over = 195, worst = 7
PHY-1002 : len = 71944, over cnt = 80(0%), over = 99, worst = 3
PHY-1002 : len = 72848, over cnt = 16(0%), over = 18, worst = 2
PHY-1002 : len = 73136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106768s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (117.1%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.39, top10 = 17.70, top15 = 13.97.
PHY-1001 : End incremental global routing;  0.158630s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (128.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061574s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.250972s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (112.1%)

OPT-1001 : Current memory(MB): used = 222, reserve = 188, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1795/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006087s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (256.7%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.39, top10 = 17.70, top15 = 13.97.
OPT-1001 : End congestion update;  0.051876s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054350s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.107833s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (101.4%)

OPT-1001 : Current memory(MB): used = 224, reserve = 190, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048879s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (127.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1795/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005816s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (268.6%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.39, top10 = 17.70, top15 = 13.97.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047669s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.413793
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.819249s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (104.9%)

RUN-1003 : finish command "place" in  6.005042s wall, 8.093750s user + 2.625000s system = 10.718750s CPU (178.5%)

RUN-1004 : used memory is 205 MB, reserved memory is 170 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 845 instances
RUN-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2034 nets
RUN-1001 : 1472 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 843, tnode num: 8954, tedge num: 11577.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70728, over cnt = 129(0%), over = 196, worst = 7
PHY-1002 : len = 71608, over cnt = 82(0%), over = 102, worst = 3
PHY-1002 : len = 72616, over cnt = 15(0%), over = 18, worst = 2
PHY-1002 : len = 72872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117207s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (173.3%)

PHY-1001 : Congestion index: top1 = 31.01, top5 = 22.45, top10 = 17.67, top15 = 13.93.
PHY-1001 : End global routing;  0.167282s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (149.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 205, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : Current memory(MB): used = 503, reserve = 472, peak = 503.
PHY-1001 : End build detailed router design. 3.264765s wall, 3.250000s user + 0.015625s system = 3.265625s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34704, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.255626s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 536, reserve = 506, peak = 537.
PHY-1001 : End phase 1; 1.261315s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 185376, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 537, reserve = 507, peak = 537.
PHY-1001 : End initial routed; 1.067412s wall, 2.062500s user + 0.031250s system = 2.093750s CPU (196.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1799(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.556   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.375862s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 538, reserve = 508, peak = 538.
PHY-1001 : End phase 2; 1.443359s wall, 2.437500s user + 0.031250s system = 2.468750s CPU (171.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 185376, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015550s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (100.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 185248, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031720s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (98.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 185272, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020054s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (77.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1799(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.556   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.378551s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.172441s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End phase 3; 0.736156s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.8%)

PHY-1003 : Routed, final wirelength = 185272
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.009780s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (159.8%)

PHY-1001 : End detail routing;  6.900027s wall, 7.890625s user + 0.046875s system = 7.937500s CPU (115.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 843, tnode num: 8954, tedge num: 11577.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.799826s wall, 8.843750s user + 0.078125s system = 8.921875s CPU (114.4%)

RUN-1004 : used memory is 526 MB, reserved memory is 498 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      822   out of  19600    4.19%
#reg                     1079   out of  19600    5.51%
#le                      1530
  #lut only               451   out of   1530   29.48%
  #reg only               708   out of   1530   46.27%
  #lut&reg                371   out of   1530   24.25%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         483
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1530   |597     |225     |1110    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1126   |292     |132     |926     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |25      |6       |27      |0       |0       |
|    demodu                  |Demodulation                                     |530    |124     |57      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |47     |0       |0       |47      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |15      |0       |30      |0       |0       |
|    integ                   |Integration                                      |138    |15      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |90     |32      |21      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |308    |77      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |19      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |117    |104     |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |22      |0       |14      |0       |0       |
|    U2                      |Ctrl_Data                                        |58     |54      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |204    |159     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1436  
    #2          2       321   
    #3          3       107   
    #4          4        15   
    #5        5-10       83   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 843, tnode num: 8954, tedge num: 11577.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 843
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2034, pip num: 14776
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1321 valid insts, and 39141 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.244706s wall, 18.796875s user + 0.078125s system = 18.875000s CPU (581.7%)

RUN-1004 : used memory is 552 MB, reserved memory is 521 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_113759.log"
