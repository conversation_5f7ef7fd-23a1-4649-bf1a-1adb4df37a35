============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 17:50:32 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1521 instances
RUN-0007 : 369 luts, 905 seqs, 123 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2050 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1532 nets have 2 pins
RUN-1001 : 406 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     253     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1519 instances, 369 luts, 905 seqs, 198 slices, 22 macros(198 instances: 123 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7239, tnet num: 2048, tinst num: 1519, tnode num: 10173, tedge num: 12227.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.300128s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (104.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 536717
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1519.
PHY-3001 : End clustering;  0.000043s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 442608, overlap = 15.75
PHY-3002 : Step(2): len = 410116, overlap = 18
PHY-3002 : Step(3): len = 396356, overlap = 15.75
PHY-3002 : Step(4): len = 378558, overlap = 20.25
PHY-3002 : Step(5): len = 371544, overlap = 20.25
PHY-3002 : Step(6): len = 347555, overlap = 15.75
PHY-3002 : Step(7): len = 338269, overlap = 20.25
PHY-3002 : Step(8): len = 332703, overlap = 20.25
PHY-3002 : Step(9): len = 327216, overlap = 20.25
PHY-3002 : Step(10): len = 302006, overlap = 20.25
PHY-3002 : Step(11): len = 282511, overlap = 18
PHY-3002 : Step(12): len = 274373, overlap = 18
PHY-3002 : Step(13): len = 269552, overlap = 18
PHY-3002 : Step(14): len = 261684, overlap = 18
PHY-3002 : Step(15): len = 258296, overlap = 18
PHY-3002 : Step(16): len = 252659, overlap = 20.25
PHY-3002 : Step(17): len = 248005, overlap = 20.25
PHY-3002 : Step(18): len = 242754, overlap = 20.25
PHY-3002 : Step(19): len = 238756, overlap = 20.25
PHY-3002 : Step(20): len = 232956, overlap = 20.25
PHY-3002 : Step(21): len = 229929, overlap = 20.25
PHY-3002 : Step(22): len = 221616, overlap = 20.25
PHY-3002 : Step(23): len = 216354, overlap = 20.25
PHY-3002 : Step(24): len = 213126, overlap = 20.25
PHY-3002 : Step(25): len = 208624, overlap = 20.25
PHY-3002 : Step(26): len = 185585, overlap = 18
PHY-3002 : Step(27): len = 180581, overlap = 20.25
PHY-3002 : Step(28): len = 178004, overlap = 20.25
PHY-3002 : Step(29): len = 127245, overlap = 20.25
PHY-3002 : Step(30): len = 122018, overlap = 20.25
PHY-3002 : Step(31): len = 119541, overlap = 20.25
PHY-3002 : Step(32): len = 117025, overlap = 20.25
PHY-3002 : Step(33): len = 114581, overlap = 20.25
PHY-3002 : Step(34): len = 112683, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.75963e-05
PHY-3002 : Step(35): len = 112852, overlap = 13.5
PHY-3002 : Step(36): len = 112017, overlap = 15.75
PHY-3002 : Step(37): len = 110691, overlap = 15.75
PHY-3002 : Step(38): len = 109859, overlap = 15.75
PHY-3002 : Step(39): len = 107028, overlap = 13.5
PHY-3002 : Step(40): len = 103542, overlap = 13.5
PHY-3002 : Step(41): len = 101285, overlap = 11.25
PHY-3002 : Step(42): len = 100677, overlap = 11.25
PHY-3002 : Step(43): len = 98388.2, overlap = 15.75
PHY-3002 : Step(44): len = 97040.9, overlap = 15.75
PHY-3002 : Step(45): len = 94993.9, overlap = 15.75
PHY-3002 : Step(46): len = 92269.5, overlap = 15.75
PHY-3002 : Step(47): len = 89747.5, overlap = 15.75
PHY-3002 : Step(48): len = 89234.8, overlap = 15.75
PHY-3002 : Step(49): len = 85679, overlap = 13.5
PHY-3002 : Step(50): len = 82929.7, overlap = 13.75
PHY-3002 : Step(51): len = 81631.6, overlap = 9.5625
PHY-3002 : Step(52): len = 80768.9, overlap = 12.0625
PHY-3002 : Step(53): len = 79166.6, overlap = 12.125
PHY-3002 : Step(54): len = 78423.1, overlap = 12.125
PHY-3002 : Step(55): len = 77980.7, overlap = 14.25
PHY-3002 : Step(56): len = 77482.1, overlap = 14.25
PHY-3002 : Step(57): len = 75852.8, overlap = 18.75
PHY-3002 : Step(58): len = 75200.6, overlap = 18.8125
PHY-3002 : Step(59): len = 74207.7, overlap = 16.625
PHY-3002 : Step(60): len = 72987.9, overlap = 14.625
PHY-3002 : Step(61): len = 71368.5, overlap = 14.8125
PHY-3002 : Step(62): len = 69154.7, overlap = 14.625
PHY-3002 : Step(63): len = 68831.5, overlap = 14.375
PHY-3002 : Step(64): len = 67068.2, overlap = 14.6875
PHY-3002 : Step(65): len = 66821.1, overlap = 14.6875
PHY-3002 : Step(66): len = 62418, overlap = 18.125
PHY-3002 : Step(67): len = 60309.7, overlap = 18.125
PHY-3002 : Step(68): len = 59884.1, overlap = 18.1875
PHY-3002 : Step(69): len = 58889.1, overlap = 16
PHY-3002 : Step(70): len = 58710.8, overlap = 15.9375
PHY-3002 : Step(71): len = 58520.7, overlap = 13.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000195193
PHY-3002 : Step(72): len = 58790.1, overlap = 13.625
PHY-3002 : Step(73): len = 58786.5, overlap = 13.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000390385
PHY-3002 : Step(74): len = 58896.7, overlap = 13.625
PHY-3002 : Step(75): len = 58902.4, overlap = 13.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006910s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (226.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057578s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000924259
PHY-3002 : Step(76): len = 63297.6, overlap = 14.7188
PHY-3002 : Step(77): len = 63172.8, overlap = 13.5312
PHY-3002 : Step(78): len = 62108, overlap = 13.3438
PHY-3002 : Step(79): len = 61534.9, overlap = 13.0938
PHY-3002 : Step(80): len = 59316.8, overlap = 10.5312
PHY-3002 : Step(81): len = 58472.2, overlap = 11.7188
PHY-3002 : Step(82): len = 57731.4, overlap = 11.7188
PHY-3002 : Step(83): len = 57303.4, overlap = 11.7188
PHY-3002 : Step(84): len = 56983.1, overlap = 11.7812
PHY-3002 : Step(85): len = 56416.3, overlap = 11.4062
PHY-3002 : Step(86): len = 55276.2, overlap = 12.375
PHY-3002 : Step(87): len = 54703.3, overlap = 10.9375
PHY-3002 : Step(88): len = 54162.3, overlap = 10.125
PHY-3002 : Step(89): len = 53272.2, overlap = 10.5938
PHY-3002 : Step(90): len = 52221.5, overlap = 10.8125
PHY-3002 : Step(91): len = 51673.9, overlap = 10.8438
PHY-3002 : Step(92): len = 51243.8, overlap = 10.0312
PHY-3002 : Step(93): len = 49800.1, overlap = 10.1562
PHY-3002 : Step(94): len = 48718.5, overlap = 12.1562
PHY-3002 : Step(95): len = 48155.2, overlap = 12.7812
PHY-3002 : Step(96): len = 47681.6, overlap = 12.4062
PHY-3002 : Step(97): len = 47200.5, overlap = 12.6562
PHY-3002 : Step(98): len = 46853.8, overlap = 12.375
PHY-3002 : Step(99): len = 46743.3, overlap = 12.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00184852
PHY-3002 : Step(100): len = 46626.6, overlap = 12.5625
PHY-3002 : Step(101): len = 46647.9, overlap = 12.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062707s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.33246e-05
PHY-3002 : Step(102): len = 46997.6, overlap = 45.75
PHY-3002 : Step(103): len = 47768, overlap = 42.1562
PHY-3002 : Step(104): len = 48516.2, overlap = 45.5938
PHY-3002 : Step(105): len = 48163.9, overlap = 44.9688
PHY-3002 : Step(106): len = 47939, overlap = 44.7812
PHY-3002 : Step(107): len = 47488, overlap = 42.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000166649
PHY-3002 : Step(108): len = 48893.1, overlap = 30.8438
PHY-3002 : Step(109): len = 49507.7, overlap = 32.7188
PHY-3002 : Step(110): len = 49771.6, overlap = 32.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000333298
PHY-3002 : Step(111): len = 49986.1, overlap = 32.625
PHY-3002 : Step(112): len = 50136, overlap = 32.75
PHY-3002 : Step(113): len = 51348.9, overlap = 28.375
PHY-3002 : Step(114): len = 51591.2, overlap = 26.7188
PHY-3002 : Step(115): len = 51720.4, overlap = 25.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7239, tnet num: 2048, tinst num: 1519, tnode num: 10173, tedge num: 12227.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 81.62 peak overflow 3.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2050.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53816, over cnt = 248(0%), over = 1085, worst = 16
PHY-1001 : End global iterations;  0.102144s wall, 0.109375s user + 0.046875s system = 0.156250s CPU (153.0%)

PHY-1001 : Congestion index: top1 = 44.38, top5 = 25.26, top10 = 15.94, top15 = 11.25.
PHY-1001 : End incremental global routing;  0.158918s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (127.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073413s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260706s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (119.9%)

OPT-1001 : Current memory(MB): used = 213, reserve = 175, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1596/2050.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53816, over cnt = 248(0%), over = 1085, worst = 16
PHY-1002 : len = 61328, over cnt = 180(0%), over = 468, worst = 16
PHY-1002 : len = 66480, over cnt = 44(0%), over = 49, worst = 6
PHY-1002 : len = 67024, over cnt = 18(0%), over = 18, worst = 1
PHY-1002 : len = 67968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146193s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (138.9%)

PHY-1001 : Congestion index: top1 = 38.00, top5 = 25.29, top10 = 18.10, top15 = 13.28.
OPT-1001 : End congestion update;  0.213274s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (124.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072762s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.289289s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (113.4%)

OPT-1001 : Current memory(MB): used = 216, reserve = 178, peak = 216.
OPT-1001 : End physical optimization;  0.874604s wall, 0.906250s user + 0.062500s system = 0.968750s CPU (110.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 173 SEQ to BLE.
SYN-4003 : Packing 732 remaining SEQ's ...
SYN-4005 : Packed 115 SEQ with LUT/SLICE
SYN-4006 : 101 single LUT's are left
SYN-4006 : 617 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 986/1267 primitive instances ...
PHY-3001 : End packing;  0.068009s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 770 instances
RUN-1001 : 361 mslices, 360 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1885 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1362 nets have 2 pins
RUN-1001 : 411 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 768 instances, 721 slices, 22 macros(198 instances: 123 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 51832.4, Over = 50
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6118, tnet num: 1883, tinst num: 768, tnode num: 8237, tedge num: 10748.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.353614s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.55766e-05
PHY-3002 : Step(116): len = 51477.1, overlap = 50.75
PHY-3002 : Step(117): len = 50931.9, overlap = 49.5
PHY-3002 : Step(118): len = 50346.5, overlap = 52
PHY-3002 : Step(119): len = 50277.7, overlap = 53.5
PHY-3002 : Step(120): len = 50110.6, overlap = 52.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.11531e-05
PHY-3002 : Step(121): len = 50520.5, overlap = 53.25
PHY-3002 : Step(122): len = 51143.1, overlap = 52.5
PHY-3002 : Step(123): len = 51502.1, overlap = 51.25
PHY-3002 : Step(124): len = 51765.4, overlap = 50.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000142306
PHY-3002 : Step(125): len = 52248.7, overlap = 50.75
PHY-3002 : Step(126): len = 53318.1, overlap = 52
PHY-3002 : Step(127): len = 53794.2, overlap = 49
PHY-3002 : Step(128): len = 54047.2, overlap = 48.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090643s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (189.6%)

PHY-3001 : Trial Legalized: Len = 66221.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.053754s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00112156
PHY-3002 : Step(129): len = 63294.3, overlap = 4.75
PHY-3002 : Step(130): len = 61529.5, overlap = 7.75
PHY-3002 : Step(131): len = 59928.8, overlap = 10.25
PHY-3002 : Step(132): len = 58985.3, overlap = 15.25
PHY-3002 : Step(133): len = 58100.2, overlap = 17
PHY-3002 : Step(134): len = 57606.2, overlap = 19.25
PHY-3002 : Step(135): len = 57373.5, overlap = 20
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00224312
PHY-3002 : Step(136): len = 57670.9, overlap = 18.75
PHY-3002 : Step(137): len = 57765.2, overlap = 18.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00448625
PHY-3002 : Step(138): len = 57809.2, overlap = 18.75
PHY-3002 : Step(139): len = 57886, overlap = 18.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004842s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 61492.7, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006416s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (243.5%)

PHY-3001 : 8 instances has been re-located, deltaX = 3, deltaY = 7, maxDist = 3.
PHY-3001 : Final: Len = 61676.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6118, tnet num: 1883, tinst num: 768, tnode num: 8237, tedge num: 10748.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 73/1885.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68384, over cnt = 147(0%), over = 234, worst = 7
PHY-1002 : len = 69352, over cnt = 78(0%), over = 102, worst = 4
PHY-1002 : len = 70472, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.157375s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (109.2%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 22.99, top10 = 17.73, top15 = 13.69.
PHY-1001 : End incremental global routing;  0.224063s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (104.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.100403s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (93.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.363003s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.3%)

OPT-1001 : Current memory(MB): used = 218, reserve = 180, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1683/1885.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010008s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (156.1%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 22.99, top10 = 17.73, top15 = 13.69.
OPT-1001 : End congestion update;  0.073495s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069975s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (111.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 730 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 768 instances, 721 slices, 22 macros(198 instances: 123 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 61846.4, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.008894s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (175.7%)

PHY-3001 : 1 instances has been re-located, deltaX = 0, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 61830.4, Over = 0
PHY-3001 : End incremental legalization;  0.045404s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 4 cells processed and 300 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.204700s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (99.2%)

OPT-1001 : Current memory(MB): used = 222, reserve = 185, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058104s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1668/1885.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70728, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 70744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.025735s wall, 0.046875s user + 0.062500s system = 0.109375s CPU (425.0%)

PHY-1001 : Congestion index: top1 = 32.13, top5 = 23.00, top10 = 17.71, top15 = 13.71.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053361s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.105393s wall, 1.093750s user + 0.109375s system = 1.203125s CPU (108.8%)

RUN-1003 : finish command "place" in  5.519840s wall, 8.437500s user + 2.328125s system = 10.765625s CPU (195.0%)

RUN-1004 : used memory is 200 MB, reserved memory is 162 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 770 instances
RUN-1001 : 361 mslices, 360 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1885 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1362 nets have 2 pins
RUN-1001 : 411 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6118, tnet num: 1883, tinst num: 768, tnode num: 8237, tedge num: 10748.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 361 mslices, 360 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1883 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67672, over cnt = 143(0%), over = 220, worst = 7
PHY-1002 : len = 68752, over cnt = 82(0%), over = 107, worst = 4
PHY-1002 : len = 70000, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 70064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.167991s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (139.5%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 22.86, top10 = 17.51, top15 = 13.59.
PHY-1001 : End global routing;  0.227873s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (130.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 200, peak = 238.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 496, reserve = 463, peak = 496.
PHY-1001 : End build detailed router design. 4.023205s wall, 3.921875s user + 0.078125s system = 4.000000s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31616, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.687136s wall, 1.625000s user + 0.031250s system = 1.656250s CPU (98.2%)

PHY-1001 : Current memory(MB): used = 529, reserve = 498, peak = 530.
PHY-1001 : End phase 1; 1.694463s wall, 1.640625s user + 0.031250s system = 1.671875s CPU (98.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 175752, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 498, peak = 530.
PHY-1001 : End initial routed; 1.407270s wall, 2.109375s user + 0.156250s system = 2.265625s CPU (161.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1672(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.336   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.671   |   8   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.435152s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (96.9%)

PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 532.
PHY-1001 : End phase 2; 1.842523s wall, 2.531250s user + 0.156250s system = 2.687500s CPU (145.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 175752, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014921s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 175784, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031943s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (146.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 175824, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.030895s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (101.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1672(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.336   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.671   |   8   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.457639s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (99.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.215809s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (94.1%)

PHY-1001 : Current memory(MB): used = 545, reserve = 512, peak = 545.
PHY-1001 : End phase 3; 0.887276s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (100.4%)

PHY-1003 : Routed, final wirelength = 175824
PHY-1001 : Current memory(MB): used = 545, reserve = 512, peak = 545.
PHY-1001 : End export database. 0.009510s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (164.3%)

PHY-1001 : End detail routing;  8.690342s wall, 9.234375s user + 0.265625s system = 9.500000s CPU (109.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6118, tnet num: 1883, tinst num: 768, tnode num: 8237, tedge num: 10748.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[21] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[23] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[24] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[25] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[4] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[6] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15.sr slack -89ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6162, tnet num: 1905, tinst num: 790, tnode num: 8281, tedge num: 10792.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.575400s wall, 3.640625s user + 0.140625s system = 3.781250s CPU (105.8%)

RUN-1003 : finish command "route" in  12.937968s wall, 13.593750s user + 0.421875s system = 14.015625s CPU (108.3%)

RUN-1004 : used memory is 543 MB, reserved memory is 512 MB, peak memory is 545 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      817   out of  19600    4.17%
#reg                      961   out of  19600    4.90%
#le                      1434
  #lut only               473   out of   1434   32.98%
  #reg only               617   out of   1434   43.03%
  #lut&reg                344   out of   1434   23.99%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         420
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         99
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1434   |619     |198     |992     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1026   |307     |106     |809     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |21      |7       |22      |0       |0       |
|    demodu                  |Demodulation                                     |476    |127     |44      |349     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |59     |30      |6       |49      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |7       |0       |17      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |7       |0       |16      |0       |0       |
|    integ                   |Integration                                      |134    |32      |14      |108     |0       |0       |
|    modu                    |Modulation                                       |62     |26      |7       |60      |0       |1       |
|    rs422                   |Rs422Output                                      |304    |84      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |22     |17      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |119    |108     |7       |60      |0       |0       |
|    U0                      |speed_select_Tx                                  |32     |25      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |23     |23      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |64     |60      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |67      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1348  
    #2          2       276   
    #3          3       117   
    #4          4        18   
    #5        5-10       75   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6162, tnet num: 1905, tinst num: 790, tnode num: 8281, tedge num: 10792.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 790
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1907, pip num: 14116
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1289 valid insts, and 37314 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.341218s wall, 17.703125s user + 0.109375s system = 17.812500s CPU (533.1%)

RUN-1004 : used memory is 549 MB, reserved memory is 514 MB, peak memory is 686 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_175032.log"
