============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 14:45:15 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/DA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 1 view nodes, 12 trigger nets, 12 data nets.
KIT-1004 : Chipwatcher code = 1010000110011100
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=1,BUS_DIN_NUM=12,BUS_CTRL_NUM=28,BUS_WIDTH='{32'sb01100},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=50) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=50) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=1,BUS_DIN_NUM=12,BUS_CTRL_NUM=28,BUS_WIDTH='{32'sb01100},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=12,BUS_CTRL_NUM=28,BUS_WIDTH='{32'sb01100},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=1,BUS_DIN_NUM=12,BUS_CTRL_NUM=28,BUS_WIDTH='{32'sb01100},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=50)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=50)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=1,BUS_DIN_NUM=12,BUS_CTRL_NUM=28,BUS_WIDTH='{32'sb01100},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=12,BUS_CTRL_NUM=28,BUS_WIDTH='{32'sb01100},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2807/7 useful/useless nets, 1743/2 useful/useless insts
SYN-1016 : Merged 12 instances.
SYN-1032 : 2656/2 useful/useless nets, 1592/2 useful/useless insts
SYN-1032 : 2636/20 useful/useless nets, 1996/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 206 better
SYN-1014 : Optimize round 2
SYN-1032 : 2502/15 useful/useless nets, 1862/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2514/80 useful/useless nets, 1884/17 useful/useless insts
SYN-1016 : Merged 9 instances.
SYN-2571 : Optimize after map_dsp, round 1, 106 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 12 instances.
SYN-2501 : Optimize round 1, 24 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 2826/5 useful/useless nets, 2196/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9995, tnet num: 2826, tinst num: 2195, tnode num: 13400, tedge num: 16232.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2826 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 135 (3.76), #lev = 8 (1.94)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 136 (3.74), #lev = 7 (2.27)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 306 instances into 136 LUTs, name keeping = 75%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 203 DFF/LATCH to SEQ ...
SYN-4009 : Pack 6 carry chain into lslice
SYN-4007 : Packing 97 adder to BLE ...
SYN-4008 : Packed 97 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.583892s wall, 1.468750s user + 0.093750s system = 1.562500s CPU (98.6%)

RUN-1004 : used memory is 162 MB, reserved memory is 119 MB, peak memory is 192 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (119 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1872 instances
RUN-0007 : 503 luts, 1031 seqs, 182 mslices, 96 lslices, 34 pads, 15 brams, 4 dsps
RUN-1001 : There are total 2504 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1767 nets have 2 pins
RUN-1001 : 570 nets have [3 - 5] pins
RUN-1001 : 98 nets have [6 - 10] pins
RUN-1001 : 36 nets have [11 - 20] pins
RUN-1001 : 25 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     192     
RUN-1001 :   No   |  No   |  Yes  |     210     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     275     
RUN-1001 :   Yes  |  No   |  Yes  |     244     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  15   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 25
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1870 instances, 503 luts, 1031 seqs, 278 slices, 33 macros(278 instances: 182 mslices 96 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9314, tnet num: 2502, tinst num: 1870, tnode num: 12850, tedge num: 15850.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2502 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.447092s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (101.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 664584
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1870.
PHY-3001 : End clustering;  0.000034s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 571635, overlap = 40.5
PHY-3002 : Step(2): len = 529423, overlap = 38.25
PHY-3002 : Step(3): len = 510574, overlap = 36
PHY-3002 : Step(4): len = 497307, overlap = 33.75
PHY-3002 : Step(5): len = 487077, overlap = 36
PHY-3002 : Step(6): len = 430662, overlap = 40.5
PHY-3002 : Step(7): len = 389584, overlap = 33.75
PHY-3002 : Step(8): len = 378160, overlap = 40.5
PHY-3002 : Step(9): len = 372299, overlap = 36
PHY-3002 : Step(10): len = 360079, overlap = 29.25
PHY-3002 : Step(11): len = 348440, overlap = 33.75
PHY-3002 : Step(12): len = 342966, overlap = 31.5
PHY-3002 : Step(13): len = 328141, overlap = 36
PHY-3002 : Step(14): len = 310679, overlap = 33.75
PHY-3002 : Step(15): len = 304620, overlap = 33.75
PHY-3002 : Step(16): len = 297411, overlap = 31.5
PHY-3002 : Step(17): len = 270531, overlap = 33.75
PHY-3002 : Step(18): len = 264943, overlap = 33.75
PHY-3002 : Step(19): len = 260467, overlap = 38.25
PHY-3002 : Step(20): len = 237558, overlap = 29.25
PHY-3002 : Step(21): len = 230702, overlap = 29.25
PHY-3002 : Step(22): len = 226472, overlap = 36
PHY-3002 : Step(23): len = 218237, overlap = 36
PHY-3002 : Step(24): len = 215811, overlap = 36
PHY-3002 : Step(25): len = 203729, overlap = 38.25
PHY-3002 : Step(26): len = 192220, overlap = 42.75
PHY-3002 : Step(27): len = 188855, overlap = 42.75
PHY-3002 : Step(28): len = 184425, overlap = 42.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.45678e-05
PHY-3002 : Step(29): len = 185262, overlap = 42.75
PHY-3002 : Step(30): len = 183572, overlap = 36
PHY-3002 : Step(31): len = 181153, overlap = 33.75
PHY-3002 : Step(32): len = 178150, overlap = 33.75
PHY-3002 : Step(33): len = 171527, overlap = 31.5
PHY-3002 : Step(34): len = 167385, overlap = 33.75
PHY-3002 : Step(35): len = 163666, overlap = 29.25
PHY-3002 : Step(36): len = 160687, overlap = 29.25
PHY-3002 : Step(37): len = 155670, overlap = 31.5
PHY-3002 : Step(38): len = 150215, overlap = 31.5
PHY-3002 : Step(39): len = 146517, overlap = 36
PHY-3002 : Step(40): len = 144793, overlap = 33.75
PHY-3002 : Step(41): len = 137151, overlap = 33.75
PHY-3002 : Step(42): len = 128733, overlap = 27
PHY-3002 : Step(43): len = 125688, overlap = 29.25
PHY-3002 : Step(44): len = 124629, overlap = 29.25
PHY-3002 : Step(45): len = 117945, overlap = 36
PHY-3002 : Step(46): len = 115984, overlap = 38.25
PHY-3002 : Step(47): len = 114578, overlap = 33.75
PHY-3002 : Step(48): len = 112004, overlap = 33.75
PHY-3002 : Step(49): len = 110019, overlap = 33.75
PHY-3002 : Step(50): len = 107173, overlap = 34.25
PHY-3002 : Step(51): len = 105382, overlap = 34.3125
PHY-3002 : Step(52): len = 103954, overlap = 32.125
PHY-3002 : Step(53): len = 101301, overlap = 34.6875
PHY-3002 : Step(54): len = 98335.6, overlap = 36.9375
PHY-3002 : Step(55): len = 97272, overlap = 34.6875
PHY-3002 : Step(56): len = 94212.8, overlap = 32.4375
PHY-3002 : Step(57): len = 91099.6, overlap = 34.4375
PHY-3002 : Step(58): len = 88902.4, overlap = 32.0625
PHY-3002 : Step(59): len = 87918.1, overlap = 32.0625
PHY-3002 : Step(60): len = 86543.4, overlap = 34.3125
PHY-3002 : Step(61): len = 82947, overlap = 31.5
PHY-3002 : Step(62): len = 79585.9, overlap = 31.5
PHY-3002 : Step(63): len = 78884.9, overlap = 33.75
PHY-3002 : Step(64): len = 77730.6, overlap = 33.75
PHY-3002 : Step(65): len = 77110.9, overlap = 33.75
PHY-3002 : Step(66): len = 76846.3, overlap = 33.75
PHY-3002 : Step(67): len = 76685.3, overlap = 31.5
PHY-3002 : Step(68): len = 76241.6, overlap = 31.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.91356e-05
PHY-3002 : Step(69): len = 76675.5, overlap = 33.75
PHY-3002 : Step(70): len = 76828, overlap = 33.75
PHY-3002 : Step(71): len = 76642.8, overlap = 36
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000178271
PHY-3002 : Step(72): len = 77452.9, overlap = 36
PHY-3002 : Step(73): len = 77625, overlap = 36
PHY-3002 : Step(74): len = 77645.4, overlap = 36
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013155s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (237.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2502 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.105456s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (88.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(75): len = 89899.9, overlap = 12.2188
PHY-3002 : Step(76): len = 90139.7, overlap = 12.1562
PHY-3002 : Step(77): len = 88658.8, overlap = 12.2188
PHY-3002 : Step(78): len = 87449.3, overlap = 13.6562
PHY-3002 : Step(79): len = 84468.4, overlap = 12.5
PHY-3002 : Step(80): len = 82821.1, overlap = 21.7188
PHY-3002 : Step(81): len = 81446.2, overlap = 23.1562
PHY-3002 : Step(82): len = 80147.3, overlap = 25.0625
PHY-3002 : Step(83): len = 76657.1, overlap = 26.0312
PHY-3002 : Step(84): len = 75224.7, overlap = 26.7188
PHY-3002 : Step(85): len = 73942.8, overlap = 24.3438
PHY-3002 : Step(86): len = 72545.9, overlap = 23.1562
PHY-3002 : Step(87): len = 69854.2, overlap = 26.3438
PHY-3002 : Step(88): len = 68576, overlap = 29.5938
PHY-3002 : Step(89): len = 67876.3, overlap = 29.4688
PHY-3002 : Step(90): len = 67440.3, overlap = 29.8438
PHY-3002 : Step(91): len = 67115, overlap = 32
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00231664
PHY-3002 : Step(92): len = 66795.6, overlap = 31.7812
PHY-3002 : Step(93): len = 66575, overlap = 31.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00463329
PHY-3002 : Step(94): len = 66493.1, overlap = 31.5938
PHY-3002 : Step(95): len = 66180.6, overlap = 31.5938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2502 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.116130s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (107.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.91418e-05
PHY-3002 : Step(96): len = 66133.5, overlap = 73.4688
PHY-3002 : Step(97): len = 67399.8, overlap = 64.2812
PHY-3002 : Step(98): len = 68732.3, overlap = 57.25
PHY-3002 : Step(99): len = 68939.2, overlap = 59.0312
PHY-3002 : Step(100): len = 69208.3, overlap = 58.4062
PHY-3002 : Step(101): len = 69058.7, overlap = 57.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000118284
PHY-3002 : Step(102): len = 68910.2, overlap = 49.0312
PHY-3002 : Step(103): len = 69225.9, overlap = 49.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000236567
PHY-3002 : Step(104): len = 69237.1, overlap = 47.9062
PHY-3002 : Step(105): len = 70523.9, overlap = 43.875
PHY-3002 : Step(106): len = 71284.2, overlap = 40.3125
PHY-3002 : Step(107): len = 71123.8, overlap = 36.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000473134
PHY-3002 : Step(108): len = 70930, overlap = 34.3438
PHY-3002 : Step(109): len = 70944, overlap = 34.3125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000946269
PHY-3002 : Step(110): len = 71300.2, overlap = 33.5312
PHY-3002 : Step(111): len = 71620, overlap = 32.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9314, tnet num: 2502, tinst num: 1870, tnode num: 12850, tedge num: 15850.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 101.34 peak overflow 2.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2504.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 78768, over cnt = 306(0%), over = 1331, worst = 24
PHY-1001 : End global iterations;  0.145809s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (182.2%)

PHY-1001 : Congestion index: top1 = 45.69, top5 = 28.81, top10 = 20.03, top15 = 14.70.
PHY-1001 : End incremental global routing;  0.220232s wall, 0.281250s user + 0.062500s system = 0.343750s CPU (156.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2502 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.101488s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (107.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.371651s wall, 0.437500s user + 0.062500s system = 0.500000s CPU (134.5%)

OPT-1001 : Current memory(MB): used = 227, reserve = 184, peak = 227.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1853/2504.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 78768, over cnt = 306(0%), over = 1331, worst = 24
PHY-1002 : len = 85736, over cnt = 206(0%), over = 637, worst = 18
PHY-1002 : len = 92024, over cnt = 59(0%), over = 150, worst = 13
PHY-1002 : len = 93712, over cnt = 3(0%), over = 4, worst = 2
PHY-1002 : len = 93832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.177129s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (141.1%)

PHY-1001 : Congestion index: top1 = 40.19, top5 = 28.39, top10 = 21.25, top15 = 16.44.
OPT-1001 : End congestion update;  0.239419s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (130.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2502 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.102176s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (107.0%)

OPT-0007 : Start: WNS -2897 TNS -38139 NUM_FEPS 23
OPT-0007 : Iter 1: improved WNS -2897 TNS -38139 NUM_FEPS 23 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.345566s wall, 0.390625s user + 0.031250s system = 0.421875s CPU (122.1%)

OPT-1001 : Current memory(MB): used = 231, reserve = 188, peak = 231.
OPT-1001 : End physical optimization;  1.158977s wall, 1.265625s user + 0.109375s system = 1.375000s CPU (118.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 503 LUT to BLE ...
SYN-4008 : Packed 503 LUT and 227 SEQ to BLE.
SYN-4003 : Packing 804 remaining SEQ's ...
SYN-4005 : Packed 215 SEQ with LUT/SLICE
SYN-4006 : 97 single LUT's are left
SYN-4006 : 589 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1092/1464 primitive instances ...
PHY-3001 : End packing;  0.078287s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (99.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 915 instances
RUN-1001 : 428 mslices, 427 lslices, 34 pads, 15 brams, 4 dsps
RUN-1001 : There are total 2286 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1556 nets have 2 pins
RUN-1001 : 563 nets have [3 - 5] pins
RUN-1001 : 99 nets have [6 - 10] pins
RUN-1001 : 39 nets have [11 - 20] pins
RUN-1001 : 23 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 913 instances, 855 slices, 33 macros(278 instances: 182 mslices 96 lslices)
PHY-3001 : Cell area utilization is 11%
PHY-3001 : After packing: Len = 72399.6, Over = 64.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7850, tnet num: 2284, tinst num: 913, tnode num: 10367, tedge num: 13851.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2284 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.419742s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (96.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.90755e-05
PHY-3002 : Step(112): len = 71385.8, overlap = 63.5
PHY-3002 : Step(113): len = 70801.1, overlap = 62.75
PHY-3002 : Step(114): len = 70498.8, overlap = 61.25
PHY-3002 : Step(115): len = 70104, overlap = 64
PHY-3002 : Step(116): len = 69853.4, overlap = 67.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.81511e-05
PHY-3002 : Step(117): len = 70200.1, overlap = 65.25
PHY-3002 : Step(118): len = 70973.7, overlap = 62
PHY-3002 : Step(119): len = 71646.2, overlap = 59.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000116302
PHY-3002 : Step(120): len = 72344, overlap = 56
PHY-3002 : Step(121): len = 73268.9, overlap = 51.25
PHY-3002 : Step(122): len = 73864, overlap = 50
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.181460s wall, 0.078125s user + 0.281250s system = 0.359375s CPU (198.0%)

PHY-3001 : Trial Legalized: Len = 89654.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2284 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.077537s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000737215
PHY-3002 : Step(123): len = 86328.7, overlap = 6.25
PHY-3002 : Step(124): len = 82813.3, overlap = 11
PHY-3002 : Step(125): len = 80501.7, overlap = 14.5
PHY-3002 : Step(126): len = 79380.4, overlap = 16.75
PHY-3002 : Step(127): len = 78675.2, overlap = 19.75
PHY-3002 : Step(128): len = 78211.3, overlap = 20.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00147443
PHY-3002 : Step(129): len = 78564.8, overlap = 20.25
PHY-3002 : Step(130): len = 78609.6, overlap = 20
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00294886
PHY-3002 : Step(131): len = 78662.5, overlap = 20.25
PHY-3002 : Step(132): len = 78662.5, overlap = 20.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008063s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 83989, Over = 0
PHY-3001 : Spreading special nets. 10 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.008825s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 16 instances has been re-located, deltaX = 5, deltaY = 13, maxDist = 2.
PHY-3001 : Final: Len = 84389, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7850, tnet num: 2284, tinst num: 913, tnode num: 10367, tedge num: 13851.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 100/2286.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 96672, over cnt = 209(0%), over = 323, worst = 8
PHY-1002 : len = 98104, over cnt = 83(0%), over = 101, worst = 4
PHY-1002 : len = 98488, over cnt = 52(0%), over = 62, worst = 3
PHY-1002 : len = 99280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.244924s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (102.1%)

PHY-1001 : Congestion index: top1 = 33.38, top5 = 26.10, top10 = 20.86, top15 = 16.97.
PHY-1001 : End incremental global routing;  0.319082s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (102.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2284 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.092999s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.461646s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 232, reserve = 190, peak = 232.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1997/2286.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 99280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.011358s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (137.6%)

PHY-1001 : Congestion index: top1 = 33.38, top5 = 26.10, top10 = 20.86, top15 = 16.97.
OPT-1001 : End congestion update;  0.077079s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2284 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.098176s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (111.4%)

OPT-0007 : Start: WNS -2897 TNS -38289 NUM_FEPS 23
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 874 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 913 instances, 855 slices, 33 macros(278 instances: 182 mslices 96 lslices)
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Initial: Len = 84444.6, Over = 0
PHY-3001 : End spreading;  0.007767s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (201.2%)

PHY-3001 : Final: Len = 84444.6, Over = 0
PHY-3001 : End incremental legalization;  0.063296s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (123.4%)

OPT-0007 : Iter 1: improved WNS -2897 TNS -38289 NUM_FEPS 23 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS -2897 TNS -38289 NUM_FEPS 23 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.259998s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (108.2%)

OPT-1001 : Current memory(MB): used = 236, reserve = 195, peak = 236.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2284 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074209s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1989/2286.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 99336, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 99344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.023040s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (135.6%)

PHY-1001 : Congestion index: top1 = 33.38, top5 = 26.08, top10 = 20.85, top15 = 16.97.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2284 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.079487s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -2897 TNS -38289 NUM_FEPS 23
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -2897ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2286 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2286 nets
OPT-1001 : End physical optimization;  1.424680s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (102.0%)

RUN-1003 : finish command "place" in  8.926772s wall, 12.515625s user + 4.218750s system = 16.734375s CPU (187.5%)

RUN-1004 : used memory is 224 MB, reserved memory is 182 MB, peak memory is 236 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 915 instances
RUN-1001 : 428 mslices, 427 lslices, 34 pads, 15 brams, 4 dsps
RUN-1001 : There are total 2286 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1556 nets have 2 pins
RUN-1001 : 563 nets have [3 - 5] pins
RUN-1001 : 99 nets have [6 - 10] pins
RUN-1001 : 39 nets have [11 - 20] pins
RUN-1001 : 23 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7850, tnet num: 2284, tinst num: 913, tnode num: 10367, tedge num: 13851.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 428 mslices, 427 lslices, 34 pads, 15 brams, 4 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2284 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 95200, over cnt = 216(0%), over = 336, worst = 8
PHY-1002 : len = 96896, over cnt = 93(0%), over = 113, worst = 4
PHY-1002 : len = 97808, over cnt = 34(0%), over = 40, worst = 4
PHY-1002 : len = 98152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.244606s wall, 0.296875s user + 0.062500s system = 0.359375s CPU (146.9%)

PHY-1001 : Congestion index: top1 = 33.75, top5 = 26.03, top10 = 20.70, top15 = 16.79.
PHY-1001 : End global routing;  0.312586s wall, 0.359375s user + 0.062500s system = 0.421875s CPU (135.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 251, reserve = 209, peak = 267.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 511, reserve = 473, peak = 511.
PHY-1001 : End build detailed router design. 4.341876s wall, 4.250000s user + 0.078125s system = 4.328125s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 37720, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.330904s wall, 2.281250s user + 0.046875s system = 2.328125s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 543, reserve = 506, peak = 543.
PHY-1001 : End phase 1; 2.337425s wall, 2.281250s user + 0.046875s system = 2.328125s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 87% nets.
PHY-1022 : len = 251496, over cnt = 53(0%), over = 53, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 544, reserve = 507, peak = 544.
PHY-1001 : End initial routed; 3.263211s wall, 4.203125s user + 0.218750s system = 4.421875s CPU (135.5%)

PHY-1001 : Update timing.....
PHY-1001 : 10/2004(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.349   |  -0.833   |   5   
RUN-1001 :   Hold   |  -0.120   |  -0.249   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.491529s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (101.7%)

PHY-1001 : Current memory(MB): used = 546, reserve = 508, peak = 546.
PHY-1001 : End phase 2; 3.754868s wall, 4.687500s user + 0.234375s system = 4.921875s CPU (131.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 5 pins with SWNS 0.120ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.045866s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.2%)

PHY-1022 : len = 251512, over cnt = 56(0%), over = 56, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.067311s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 251000, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.091963s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (118.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 251016, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.024523s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (127.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2004(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.120   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.249   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.496116s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (100.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 9 nets
PHY-1001 : End commit to database; 0.337331s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (92.6%)

PHY-1001 : Current memory(MB): used = 563, reserve = 526, peak = 563.
PHY-1001 : End phase 3; 1.213783s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (100.4%)

PHY-1003 : Routed, final wirelength = 251016
PHY-1001 : Current memory(MB): used = 564, reserve = 526, peak = 564.
PHY-1001 : End export database. 0.015818s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.8%)

PHY-1001 : End detail routing;  11.918775s wall, 12.671875s user + 0.406250s system = 13.078125s CPU (109.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7850, tnet num: 2284, tinst num: 913, tnode num: 10367, tedge num: 13851.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[19] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[32] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7876, tnet num: 2297, tinst num: 926, tnode num: 10393, tedge num: 13877.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  4.657557s wall, 4.828125s user + 0.234375s system = 5.062500s CPU (108.7%)

RUN-1003 : finish command "route" in  17.418018s wall, 18.359375s user + 0.718750s system = 19.078125s CPU (109.5%)

RUN-1004 : used memory is 540 MB, reserved memory is 503 MB, peak memory is 564 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1085   out of  19600    5.54%
#reg                     1105   out of  19600    5.64%
#le                      1674
  #lut only               569   out of   1674   33.99%
  #reg only               589   out of   1674   35.19%
  #lut&reg                516   out of   1674   30.82%
#dsp                        4   out of     29   13.79%
#bram                      15   out of     64   23.44%
  #bram9k                  15
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         374
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         99
#3        config_inst_syn_9               GCLK               config             config_inst.jtck              65
#4        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di               59
#5        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    37
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       NONE    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       NONE    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       NONE    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       NONE    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       NONE    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       NONE    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       NONE    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       NONE    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       NONE    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       NONE    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       NONE    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       NONE    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       NONE    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       NONE    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1674   |807     |278     |1122    |15      |4       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |947    |319     |114     |743     |4       |4       |
|    ctrl_signal                     |SignalGenerator                                  |22     |16      |6       |18      |0       |0       |
|    demodu                          |Demodulation                                     |452    |135     |44      |348     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |56     |29      |6       |46      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |4       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |18     |11      |0       |18      |0       |0       |
|    integ                           |Integration                                      |138    |17      |14      |111     |0       |0       |
|    modu                            |Modulation                                       |3      |2       |0       |3       |0       |0       |
|    rs422                           |Rs422Output                                      |313    |135     |45      |245     |0       |4       |
|    trans                           |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                            |UART_Control                                     |133    |118     |7       |54      |0       |0       |
|    U0                              |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                              |uart_tx                                          |24     |18      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data                                        |72     |72      |0       |22      |0       |0       |
|  wendu                             |DS18B20                                          |166    |121     |45      |82      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |361    |215     |79      |203     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |361    |215     |79      |203     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |114    |58      |0       |114     |0       |0       |
|        reg_inst                    |register                                         |110    |54      |0       |110     |0       |0       |
|        tap_inst                    |tap                                              |4      |4       |0       |4       |0       |0       |
|      trigger_inst                  |trigger                                          |247    |157     |79      |89      |0       |0       |
|        bus_inst                    |bus_top                                          |39     |19      |14      |11      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |39     |19      |14      |11      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |118    |85      |33      |54      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1534  
    #2          2       376   
    #3          3       174   
    #4          4        13   
    #5        5-10      104   
    #6        11-50      52   
    #7       51-100      1    
    #8       101-500     1    
  Average     2.17            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7876, tnet num: 2297, tinst num: 926, tnode num: 10393, tedge num: 13877.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2297 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 04312f8f040dfe5053e7f92ae50ddb0352fd5d1fe1f262041b5cb1b005015455 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 926
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2299, pip num: 18677
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1556 valid insts, and 49610 bits set as '1'.
BIT-1004 : the usercode register value: 00000000110111011010000110011100
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.840327s wall, 29.390625s user + 0.109375s system = 29.500000s CPU (505.1%)

RUN-1004 : used memory is 537 MB, reserved memory is 500 MB, peak memory is 685 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231129_144515.log"
