============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Oct 31 14:17:38 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1543 instances
RUN-0007 : 369 luts, 921 seqs, 129 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2084 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1539 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     269     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1541 instances, 369 luts, 921 seqs, 204 slices, 22 macros(204 instances: 129 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7361, tnet num: 2082, tinst num: 1541, tnode num: 10351, tedge num: 12456.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2082 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283662s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540799
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1541.
PHY-3001 : End clustering;  0.000066s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 461509, overlap = 13.5
PHY-3002 : Step(2): len = 415265, overlap = 20.25
PHY-3002 : Step(3): len = 389177, overlap = 18
PHY-3002 : Step(4): len = 367750, overlap = 15.75
PHY-3002 : Step(5): len = 361760, overlap = 15.75
PHY-3002 : Step(6): len = 355843, overlap = 20.25
PHY-3002 : Step(7): len = 343759, overlap = 15.75
PHY-3002 : Step(8): len = 327790, overlap = 20.25
PHY-3002 : Step(9): len = 322835, overlap = 15.75
PHY-3002 : Step(10): len = 315937, overlap = 20.25
PHY-3002 : Step(11): len = 301956, overlap = 18
PHY-3002 : Step(12): len = 295190, overlap = 15.75
PHY-3002 : Step(13): len = 290480, overlap = 15.75
PHY-3002 : Step(14): len = 273353, overlap = 13.5
PHY-3002 : Step(15): len = 267192, overlap = 13.5
PHY-3002 : Step(16): len = 264004, overlap = 11.25
PHY-3002 : Step(17): len = 254968, overlap = 13.5
PHY-3002 : Step(18): len = 245570, overlap = 13.5
PHY-3002 : Step(19): len = 243001, overlap = 13.5
PHY-3002 : Step(20): len = 237142, overlap = 13.5
PHY-3002 : Step(21): len = 225164, overlap = 13.5
PHY-3002 : Step(22): len = 219506, overlap = 11.25
PHY-3002 : Step(23): len = 216983, overlap = 13.5
PHY-3002 : Step(24): len = 210927, overlap = 11.25
PHY-3002 : Step(25): len = 205630, overlap = 13.5
PHY-3002 : Step(26): len = 200979, overlap = 11.25
PHY-3002 : Step(27): len = 196366, overlap = 13.5
PHY-3002 : Step(28): len = 191798, overlap = 11.25
PHY-3002 : Step(29): len = 186598, overlap = 13.5
PHY-3002 : Step(30): len = 179683, overlap = 11.25
PHY-3002 : Step(31): len = 176604, overlap = 13.5
PHY-3002 : Step(32): len = 172166, overlap = 11.25
PHY-3002 : Step(33): len = 166896, overlap = 13.5
PHY-3002 : Step(34): len = 161263, overlap = 11.25
PHY-3002 : Step(35): len = 158199, overlap = 15.75
PHY-3002 : Step(36): len = 152963, overlap = 13.5
PHY-3002 : Step(37): len = 146125, overlap = 15.75
PHY-3002 : Step(38): len = 141328, overlap = 13.5
PHY-3002 : Step(39): len = 139675, overlap = 15.75
PHY-3002 : Step(40): len = 129449, overlap = 13.5
PHY-3002 : Step(41): len = 109448, overlap = 13.5
PHY-3002 : Step(42): len = 106382, overlap = 13.5
PHY-3002 : Step(43): len = 105153, overlap = 13.5
PHY-3002 : Step(44): len = 102982, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.73499e-05
PHY-3002 : Step(45): len = 102542, overlap = 13.5
PHY-3002 : Step(46): len = 102728, overlap = 13.5
PHY-3002 : Step(47): len = 102336, overlap = 11.25
PHY-3002 : Step(48): len = 101709, overlap = 15.75
PHY-3002 : Step(49): len = 101149, overlap = 15.75
PHY-3002 : Step(50): len = 99476.6, overlap = 13.5
PHY-3002 : Step(51): len = 95263, overlap = 11.25
PHY-3002 : Step(52): len = 94724.2, overlap = 13.5
PHY-3002 : Step(53): len = 92161.5, overlap = 13.5
PHY-3002 : Step(54): len = 89208.1, overlap = 13.5
PHY-3002 : Step(55): len = 88445.8, overlap = 13.5
PHY-3002 : Step(56): len = 87196, overlap = 11.25
PHY-3002 : Step(57): len = 85797.5, overlap = 11.25
PHY-3002 : Step(58): len = 84898.3, overlap = 13.5
PHY-3002 : Step(59): len = 83168.6, overlap = 13.5
PHY-3002 : Step(60): len = 81461.7, overlap = 11.25
PHY-3002 : Step(61): len = 79279.9, overlap = 13.5
PHY-3002 : Step(62): len = 78143.8, overlap = 13.5
PHY-3002 : Step(63): len = 76595.9, overlap = 13.625
PHY-3002 : Step(64): len = 75788.8, overlap = 11.375
PHY-3002 : Step(65): len = 74923, overlap = 11.25
PHY-3002 : Step(66): len = 73594.4, overlap = 11.375
PHY-3002 : Step(67): len = 70005.7, overlap = 14.4375
PHY-3002 : Step(68): len = 69041.3, overlap = 12.4375
PHY-3002 : Step(69): len = 68254.9, overlap = 12.375
PHY-3002 : Step(70): len = 67550.5, overlap = 12.9375
PHY-3002 : Step(71): len = 66347.1, overlap = 12.8125
PHY-3002 : Step(72): len = 65984.2, overlap = 13
PHY-3002 : Step(73): len = 65297.5, overlap = 12.9375
PHY-3002 : Step(74): len = 64509.9, overlap = 15.0625
PHY-3002 : Step(75): len = 63998.2, overlap = 17.25
PHY-3002 : Step(76): len = 63710.7, overlap = 12.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0001947
PHY-3002 : Step(77): len = 63956.6, overlap = 12.8125
PHY-3002 : Step(78): len = 64025.6, overlap = 12.9375
PHY-3002 : Step(79): len = 64013.4, overlap = 12.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.0003894
PHY-3002 : Step(80): len = 64130.5, overlap = 12.9375
PHY-3002 : Step(81): len = 64156.2, overlap = 13
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006775s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2082 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059561s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00139373
PHY-3002 : Step(82): len = 68376.2, overlap = 16.0312
PHY-3002 : Step(83): len = 67592.2, overlap = 16.1562
PHY-3002 : Step(84): len = 65982.2, overlap = 15.9375
PHY-3002 : Step(85): len = 65167, overlap = 15.6562
PHY-3002 : Step(86): len = 62409.2, overlap = 12.8438
PHY-3002 : Step(87): len = 60612.2, overlap = 12.2812
PHY-3002 : Step(88): len = 59900.3, overlap = 12.2812
PHY-3002 : Step(89): len = 59295.5, overlap = 12.2812
PHY-3002 : Step(90): len = 58002.8, overlap = 12.2188
PHY-3002 : Step(91): len = 57651.7, overlap = 15.2812
PHY-3002 : Step(92): len = 56805.5, overlap = 15.6562
PHY-3002 : Step(93): len = 56077.6, overlap = 15.9062
PHY-3002 : Step(94): len = 54958.5, overlap = 16.1875
PHY-3002 : Step(95): len = 53995, overlap = 13.8438
PHY-3002 : Step(96): len = 53178.6, overlap = 12.9062
PHY-3002 : Step(97): len = 52840.2, overlap = 13.0312
PHY-3002 : Step(98): len = 52495.5, overlap = 13.5312
PHY-3002 : Step(99): len = 52447.8, overlap = 12.5625
PHY-3002 : Step(100): len = 52193.5, overlap = 13.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2082 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057507s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000131666
PHY-3002 : Step(101): len = 52492.2, overlap = 34.2188
PHY-3002 : Step(102): len = 53003.3, overlap = 33.9062
PHY-3002 : Step(103): len = 53437, overlap = 33.5938
PHY-3002 : Step(104): len = 53724.8, overlap = 34.4062
PHY-3002 : Step(105): len = 53036.7, overlap = 33.5938
PHY-3002 : Step(106): len = 52942.5, overlap = 33.6875
PHY-3002 : Step(107): len = 52353.1, overlap = 34.7188
PHY-3002 : Step(108): len = 51988.1, overlap = 35.5
PHY-3002 : Step(109): len = 51620.4, overlap = 34.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000263332
PHY-3002 : Step(110): len = 51792.2, overlap = 35.6875
PHY-3002 : Step(111): len = 52068, overlap = 34.8125
PHY-3002 : Step(112): len = 52543.3, overlap = 33.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000526664
PHY-3002 : Step(113): len = 52921.3, overlap = 30.4688
PHY-3002 : Step(114): len = 53692.8, overlap = 27.875
PHY-3002 : Step(115): len = 53959.8, overlap = 27.1562
PHY-3002 : Step(116): len = 53743.2, overlap = 26.875
PHY-3002 : Step(117): len = 53525.3, overlap = 26.9688
PHY-3002 : Step(118): len = 52931.3, overlap = 27.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7361, tnet num: 2082, tinst num: 1541, tnode num: 10351, tedge num: 12456.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.78 peak overflow 2.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2084.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56112, over cnt = 246(0%), over = 985, worst = 19
PHY-1001 : End global iterations;  0.083631s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (168.1%)

PHY-1001 : Congestion index: top1 = 42.72, top5 = 25.85, top10 = 16.23, top15 = 11.45.
PHY-1001 : End incremental global routing;  0.134861s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (127.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2082 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064435s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.228129s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (116.4%)

OPT-1001 : Current memory(MB): used = 210, reserve = 173, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1571/2084.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56112, over cnt = 246(0%), over = 985, worst = 19
PHY-1002 : len = 62216, over cnt = 161(0%), over = 438, worst = 14
PHY-1002 : len = 66536, over cnt = 28(0%), over = 32, worst = 3
PHY-1002 : len = 66904, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 66952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087689s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.9%)

PHY-1001 : Congestion index: top1 = 35.82, top5 = 25.07, top10 = 17.72, top15 = 13.02.
OPT-1001 : End congestion update;  0.132561s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2082 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055057s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.190674s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.3%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : End physical optimization;  0.686403s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (122.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 173 SEQ to BLE.
SYN-4003 : Packing 748 remaining SEQ's ...
SYN-4005 : Packed 116 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 632 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1001/1288 primitive instances ...
PHY-3001 : End packing;  0.046308s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 790 instances
RUN-1001 : 371 mslices, 370 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1919 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1373 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 788 instances, 741 slices, 22 macros(204 instances: 129 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 53081, Over = 53
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6232, tnet num: 1917, tinst num: 788, tnode num: 8407, tedge num: 10964.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.299419s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.82165e-05
PHY-3002 : Step(119): len = 52615.3, overlap = 54.25
PHY-3002 : Step(120): len = 52048.2, overlap = 52.75
PHY-3002 : Step(121): len = 51598, overlap = 52.25
PHY-3002 : Step(122): len = 51473.9, overlap = 53
PHY-3002 : Step(123): len = 51295.5, overlap = 54.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.6433e-05
PHY-3002 : Step(124): len = 51780.3, overlap = 55
PHY-3002 : Step(125): len = 52072.4, overlap = 54
PHY-3002 : Step(126): len = 52065.3, overlap = 54
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000112866
PHY-3002 : Step(127): len = 52992, overlap = 53.25
PHY-3002 : Step(128): len = 53615.4, overlap = 50.5
PHY-3002 : Step(129): len = 54027.8, overlap = 50.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075796s wall, 0.078125s user + 0.078125s system = 0.156250s CPU (206.1%)

PHY-3001 : Trial Legalized: Len = 68642
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049144s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000703028
PHY-3002 : Step(130): len = 65260, overlap = 8.5
PHY-3002 : Step(131): len = 62316, overlap = 13.5
PHY-3002 : Step(132): len = 60171.5, overlap = 13.25
PHY-3002 : Step(133): len = 59144.7, overlap = 15.25
PHY-3002 : Step(134): len = 58515.4, overlap = 16
PHY-3002 : Step(135): len = 58006.3, overlap = 16.5
PHY-3002 : Step(136): len = 57745.7, overlap = 18.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00140606
PHY-3002 : Step(137): len = 57981.8, overlap = 19.25
PHY-3002 : Step(138): len = 58153.7, overlap = 17.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00281211
PHY-3002 : Step(139): len = 58245.4, overlap = 16.75
PHY-3002 : Step(140): len = 58278, overlap = 17
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005384s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63197.3, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005848s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (267.2%)

PHY-3001 : 12 instances has been re-located, deltaX = 0, deltaY = 15, maxDist = 2.
PHY-3001 : Final: Len = 63413.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6232, tnet num: 1917, tinst num: 788, tnode num: 8407, tedge num: 10964.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 83/1919.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70616, over cnt = 149(0%), over = 224, worst = 7
PHY-1002 : len = 71536, over cnt = 96(0%), over = 116, worst = 3
PHY-1002 : len = 72520, over cnt = 22(0%), over = 25, worst = 2
PHY-1002 : len = 72896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134139s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (128.1%)

PHY-1001 : Congestion index: top1 = 31.88, top5 = 22.84, top10 = 17.91, top15 = 13.95.
PHY-1001 : End incremental global routing;  0.188468s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (124.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059353s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.278775s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (117.7%)

OPT-1001 : Current memory(MB): used = 215, reserve = 180, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1686/1919.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006427s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (243.1%)

PHY-1001 : Congestion index: top1 = 31.88, top5 = 22.84, top10 = 17.91, top15 = 13.95.
OPT-1001 : End congestion update;  0.054790s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050091s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 750 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 788 instances, 741 slices, 22 macros(204 instances: 129 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63508, Over = 0
PHY-3001 : End spreading;  0.004944s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63508, Over = 0
PHY-3001 : End incremental legalization;  0.033593s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.0%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.153352s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (163.0%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050049s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1674/1919.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008640s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (180.8%)

PHY-1001 : Congestion index: top1 = 31.90, top5 = 22.82, top10 = 17.88, top15 = 13.94.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051003s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.877797s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (115.7%)

RUN-1003 : finish command "place" in  5.012980s wall, 8.187500s user + 2.359375s system = 10.546875s CPU (210.4%)

RUN-1004 : used memory is 196 MB, reserved memory is 160 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 790 instances
RUN-1001 : 371 mslices, 370 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1919 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1373 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6232, tnet num: 1917, tinst num: 788, tnode num: 8407, tedge num: 10964.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 371 mslices, 370 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69808, over cnt = 156(0%), over = 223, worst = 7
PHY-1002 : len = 70752, over cnt = 105(0%), over = 123, worst = 3
PHY-1002 : len = 72248, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 72416, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.141526s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (132.5%)

PHY-1001 : Congestion index: top1 = 31.57, top5 = 22.65, top10 = 17.78, top15 = 13.83.
PHY-1001 : End global routing;  0.192031s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (122.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 201, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 495, reserve = 463, peak = 495.
PHY-1001 : End build detailed router design. 3.185986s wall, 3.109375s user + 0.046875s system = 3.156250s CPU (99.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34264, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.390741s wall, 1.390625s user + 0.000000s system = 1.390625s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 527, reserve = 496, peak = 527.
PHY-1001 : End phase 1; 1.397569s wall, 1.406250s user + 0.000000s system = 1.406250s CPU (100.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 188608, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 497, peak = 530.
PHY-1001 : End initial routed; 1.537781s wall, 2.312500s user + 0.062500s system = 2.375000s CPU (154.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1700(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.391   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.348   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.351375s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.3%)

PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 532.
PHY-1001 : End phase 2; 1.889243s wall, 2.671875s user + 0.062500s system = 2.734375s CPU (144.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 188608, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015270s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 188552, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.022991s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (68.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 188616, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023914s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1700(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.391   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.348   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.361010s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.170405s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 546, reserve = 514, peak = 546.
PHY-1001 : End phase 3; 0.724049s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.3%)

PHY-1003 : Routed, final wirelength = 188616
PHY-1001 : Current memory(MB): used = 546, reserve = 515, peak = 546.
PHY-1001 : End export database. 0.010564s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (147.9%)

PHY-1001 : End detail routing;  7.385857s wall, 8.109375s user + 0.109375s system = 8.218750s CPU (111.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6232, tnet num: 1917, tinst num: 788, tnode num: 8407, tedge num: 10964.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/reg1_syn_45.sr slack -89ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6258, tnet num: 1930, tinst num: 801, tnode num: 8433, tedge num: 10990.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.055360s wall, 3.062500s user + 0.203125s system = 3.265625s CPU (106.9%)

RUN-1003 : finish command "route" in  10.980962s wall, 11.734375s user + 0.328125s system = 12.062500s CPU (109.8%)

RUN-1004 : used memory is 521 MB, reserved memory is 490 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      809   out of  19600    4.13%
#reg                      989   out of  19600    5.05%
#le                      1441
  #lut only               452   out of   1441   31.37%
  #reg only               632   out of   1441   43.86%
  #lut&reg                357   out of   1441   24.77%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         439
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1441   |605     |204     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1041   |301     |112     |838     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |20      |7       |21      |0       |0       |
|    demodu                  |Demodulation                                     |453    |113     |44      |348     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |58     |34      |6       |48      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |11      |0       |17      |0       |0       |
|    integ                   |Integration                                      |134    |32      |14      |108     |0       |0       |
|    modu                    |Modulation                                       |92     |24      |13      |90      |0       |1       |
|    rs422                   |Rs422Output                                      |305    |87      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |109    |98      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |29     |21      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |25     |22      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |55      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1350  
    #2          2       297   
    #3          3       119   
    #4          4        18   
    #5        5-10       74   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6258, tnet num: 1930, tinst num: 801, tnode num: 8433, tedge num: 10990.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 801
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1932, pip num: 14523
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1343 valid insts, and 38222 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.180675s wall, 17.968750s user + 0.062500s system = 18.031250s CPU (566.9%)

RUN-1004 : used memory is 518 MB, reserved memory is 486 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231031_141738.log"
