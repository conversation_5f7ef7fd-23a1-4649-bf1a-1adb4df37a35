============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 13:54:42 2023

   Run on =     TLH-022
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  4.840815s wall, 4.828125s user + 0.187500s system = 5.015625s CPU (103.6%)

RUN-1004 : used memory is 600 MB, reserved memory is 727 MB, peak memory is 649 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.417399s wall, 1.421875s user + 0.046875s system = 1.468750s CPU (103.6%)

RUN-1004 : used memory is 602 MB, reserved memory is 726 MB, peak memory is 649 MB
GUI-1001 : User opens ChipWatcher ...
PRG-1000 : <!-- HMAC is: 49759bdc91fa3d490dd78e6f9c47b6890adccf0ccd7626ec02eb16b44c7534e7 -->
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 8 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  2.224360s wall, 2.140625s user + 0.171875s system = 2.312500s CPU (104.0%)

RUN-1004 : used memory is 606 MB, reserved memory is 723 MB, peak memory is 649 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 15 view nodes, 26 trigger nets, 26 data nets.
GUI-1001 : Import sim/DA.cwc success!
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.600190s wall, 0.015625s user + 0.093750s system = 0.109375s CPU (6.8%)

RUN-1004 : used memory is 617 MB, reserved memory is 726 MB, peak memory is 649 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.791526s wall, 0.125000s user + 0.140625s system = 0.265625s CPU (14.8%)

RUN-1004 : used memory is 617 MB, reserved memory is 726 MB, peak memory is 649 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000001100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000010111010111010111010111010111010111010111010111010111010111010111010111010111010111001000000000000000000000
GUI-001 : Delete DA_DATA[0] successfully
GUI-001 : Delete DA_DATA[1] successfully
GUI-001 : Delete DA_DATA[2] successfully
GUI-001 : Delete DA_DATA[3] successfully
GUI-001 : Delete DA_DATA[4] successfully
GUI-001 : Delete DA_DATA[5] successfully
GUI-001 : Delete DA_DATA[6] successfully
GUI-001 : Delete DA_DATA[7] successfully
GUI-001 : Delete DA_DATA[8] successfully
GUI-001 : Delete DA_DATA[9] successfully
GUI-001 : Delete DA_DATA[10] successfully
GUI-001 : Delete DA_DATA[11] successfully
GUI-001 : Delete DA_DATA[12] successfully
GUI-001 : Delete DA_DATA[13] successfully
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
PRG-1000 : <!-- HMAC is: 2e398b8939066e1814c625de9913476711279e3712f1554d83490137c7cf8e1e -->
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
RUN-1001 : open_run syn_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/syn_1/IFOG501_2B_rtl.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 9 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.829923s wall, 1.875000s user + 0.031250s system = 1.906250s CPU (104.2%)

RUN-1004 : used memory is 598 MB, reserved memory is 747 MB, peak memory is 649 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 1 view nodes, 12 trigger nets, 12 data nets.
GUI-1001 : Import sim/DA.cwc success!
PRG-1000 : <!-- HMAC is: 661b4ddbb41c74a6d904c55df2a61cabfda1f06131433866acea30c0ceacd1ef -->
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.750779s wall, 1.687500s user + 0.140625s system = 1.828125s CPU (104.4%)

RUN-1004 : used memory is 600 MB, reserved memory is 746 MB, peak memory is 649 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
GUI-1001 : Import sim/DA.cwc success!
GUI-8501 ERROR: Bit file code (0001010001101011) does not match with the ChipWatcher's (0111110101101110).
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.604327s wall, 0.078125s user + 0.109375s system = 0.187500s CPU (11.7%)

RUN-1004 : used memory is 599 MB, reserved memory is 744 MB, peak memory is 649 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.794167s wall, 0.156250s user + 0.156250s system = 0.312500s CPU (17.4%)

RUN-1004 : used memory is 599 MB, reserved memory is 744 MB, peak memory is 649 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 8 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  2.026435s wall, 1.984375s user + 0.109375s system = 2.093750s CPU (103.3%)

RUN-1004 : used memory is 625 MB, reserved memory is 761 MB, peak memory is 649 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
GUI-1001 : Import sim/DA.cwc success!
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.593832s wall, 0.078125s user + 0.078125s system = 0.156250s CPU (9.8%)

RUN-1004 : used memory is 629 MB, reserved memory is 764 MB, peak memory is 649 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.781142s wall, 0.187500s user + 0.078125s system = 0.265625s CPU (14.9%)

RUN-1004 : used memory is 629 MB, reserved memory is 764 MB, peak memory is 649 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
PRG-1000 : <!-- HMAC is: 709fe0e0ea541cdab37adc0c153edfd4f8862ac5bb027f209ed4ab7c5c55a803 -->
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
PRG-1000 : <!-- HMAC is: 709fe0e0ea541cdab37adc0c153edfd4f8862ac5bb027f209ed4ab7c5c55a803 -->
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 11 feed throughs used by 11 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  2.123809s wall, 2.125000s user + 0.046875s system = 2.171875s CPU (102.3%)

RUN-1004 : used memory is 625 MB, reserved memory is 769 MB, peak memory is 649 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
GUI-1001 : Import sim/DA.cwc success!
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.621696s wall, 0.078125s user + 0.187500s system = 0.265625s CPU (16.4%)

RUN-1004 : used memory is 630 MB, reserved memory is 772 MB, peak memory is 649 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.809272s wall, 0.171875s user + 0.203125s system = 0.375000s CPU (20.7%)

RUN-1004 : used memory is 630 MB, reserved memory is 772 MB, peak memory is 649 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.823711s wall, 1.812500s user + 0.171875s system = 1.984375s CPU (108.8%)

RUN-1004 : used memory is 625 MB, reserved memory is 764 MB, peak memory is 649 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
GUI-1001 : Import sim/DA.cwc success!
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.615968s wall, 0.156250s user + 0.078125s system = 0.234375s CPU (14.5%)

RUN-1004 : used memory is 630 MB, reserved memory is 770 MB, peak memory is 649 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.800320s wall, 0.250000s user + 0.078125s system = 0.328125s CPU (18.2%)

RUN-1004 : used memory is 630 MB, reserved memory is 770 MB, peak memory is 649 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 10 feed throughs used by 10 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.928332s wall, 1.812500s user + 0.093750s system = 1.906250s CPU (98.9%)

RUN-1004 : used memory is 640 MB, reserved memory is 776 MB, peak memory is 649 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
GUI-1001 : Import sim/DA.cwc success!
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.581924s wall, 0.031250s user + 0.062500s system = 0.093750s CPU (5.9%)

RUN-1004 : used memory is 634 MB, reserved memory is 777 MB, peak memory is 650 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.770168s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (15.0%)

RUN-1004 : used memory is 634 MB, reserved memory is 777 MB, peak memory is 650 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 15 feed throughs used by 14 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.829373s wall, 1.843750s user + 0.093750s system = 1.937500s CPU (105.9%)

RUN-1004 : used memory is 633 MB, reserved memory is 786 MB, peak memory is 651 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
GUI-1001 : Import sim/DA.cwc success!
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.608491s wall, 0.093750s user + 0.062500s system = 0.156250s CPU (9.7%)

RUN-1004 : used memory is 630 MB, reserved memory is 782 MB, peak memory is 652 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.799115s wall, 0.203125s user + 0.078125s system = 0.281250s CPU (15.6%)

RUN-1004 : used memory is 630 MB, reserved memory is 782 MB, peak memory is 652 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 8 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  2.868540s wall, 2.812500s user + 0.250000s system = 3.062500s CPU (106.8%)

RUN-1004 : used memory is 634 MB, reserved memory is 775 MB, peak memory is 652 MB
RUN-1002 : start command "config_chipwatcher -sync sim/DA.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
GUI-1001 : Import sim/DA.cwc success!
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.575288s wall, 0.015625s user + 0.062500s system = 0.078125s CPU (5.0%)

RUN-1004 : used memory is 636 MB, reserved memory is 777 MB, peak memory is 653 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.761111s wall, 0.125000s user + 0.078125s system = 0.203125s CPU (11.5%)

RUN-1004 : used memory is 636 MB, reserved memory is 777 MB, peak memory is 653 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000001000000
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
SYN-2541 : Attrs-to-init for 24 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_163
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_165
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_167
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_169
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_173
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_175
KIT-1004 : ChipWatcher: write ctrl reg value: 0110111111111111000000000000011011111111111111000000000000000010000000000000000000000000
GUI-1001 : User closes ChipWatcher ...
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
