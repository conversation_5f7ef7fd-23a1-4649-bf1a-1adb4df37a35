============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 18:05:42 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1646 instances
RUN-0007 : 379 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2217 nets
RUN-1001 : 1655 nets have 2 pins
RUN-1001 : 448 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1644 instances, 379 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7847, tnet num: 2215, tinst num: 1644, tnode num: 11088, tedge num: 13249.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.276839s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (95.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 618410
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1644.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 486501, overlap = 20.25
PHY-3002 : Step(2): len = 364194, overlap = 18
PHY-3002 : Step(3): len = 338803, overlap = 18
PHY-3002 : Step(4): len = 327760, overlap = 18
PHY-3002 : Step(5): len = 317303, overlap = 20.25
PHY-3002 : Step(6): len = 311486, overlap = 20.25
PHY-3002 : Step(7): len = 304464, overlap = 18
PHY-3002 : Step(8): len = 297577, overlap = 18
PHY-3002 : Step(9): len = 289697, overlap = 18
PHY-3002 : Step(10): len = 284923, overlap = 18
PHY-3002 : Step(11): len = 276961, overlap = 20.25
PHY-3002 : Step(12): len = 271461, overlap = 20.25
PHY-3002 : Step(13): len = 265978, overlap = 20.25
PHY-3002 : Step(14): len = 261555, overlap = 20.25
PHY-3002 : Step(15): len = 254521, overlap = 20.25
PHY-3002 : Step(16): len = 250873, overlap = 20.25
PHY-3002 : Step(17): len = 244814, overlap = 20.25
PHY-3002 : Step(18): len = 239342, overlap = 20.25
PHY-3002 : Step(19): len = 233971, overlap = 20.25
PHY-3002 : Step(20): len = 230515, overlap = 20.25
PHY-3002 : Step(21): len = 223350, overlap = 20.25
PHY-3002 : Step(22): len = 219209, overlap = 20.25
PHY-3002 : Step(23): len = 214738, overlap = 20.25
PHY-3002 : Step(24): len = 210386, overlap = 20.25
PHY-3002 : Step(25): len = 202116, overlap = 20.25
PHY-3002 : Step(26): len = 199172, overlap = 20.25
PHY-3002 : Step(27): len = 195689, overlap = 20.25
PHY-3002 : Step(28): len = 181391, overlap = 20.25
PHY-3002 : Step(29): len = 167624, overlap = 20.25
PHY-3002 : Step(30): len = 166427, overlap = 20.25
PHY-3002 : Step(31): len = 160148, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000107964
PHY-3002 : Step(32): len = 161615, overlap = 11.25
PHY-3002 : Step(33): len = 160130, overlap = 13.5
PHY-3002 : Step(34): len = 156843, overlap = 13.5
PHY-3002 : Step(35): len = 151714, overlap = 11.25
PHY-3002 : Step(36): len = 147953, overlap = 9
PHY-3002 : Step(37): len = 145010, overlap = 9
PHY-3002 : Step(38): len = 141782, overlap = 9
PHY-3002 : Step(39): len = 137636, overlap = 11.25
PHY-3002 : Step(40): len = 134741, overlap = 6.75
PHY-3002 : Step(41): len = 130875, overlap = 6.75
PHY-3002 : Step(42): len = 129100, overlap = 11.25
PHY-3002 : Step(43): len = 123304, overlap = 11.25
PHY-3002 : Step(44): len = 120188, overlap = 6.75
PHY-3002 : Step(45): len = 117882, overlap = 6.75
PHY-3002 : Step(46): len = 116761, overlap = 9
PHY-3002 : Step(47): len = 108157, overlap = 13.5
PHY-3002 : Step(48): len = 106566, overlap = 6.75
PHY-3002 : Step(49): len = 104128, overlap = 6.75
PHY-3002 : Step(50): len = 102801, overlap = 13.5
PHY-3002 : Step(51): len = 101487, overlap = 11.25
PHY-3002 : Step(52): len = 100762, overlap = 6.75
PHY-3002 : Step(53): len = 98200, overlap = 6.75
PHY-3002 : Step(54): len = 96483, overlap = 4.5
PHY-3002 : Step(55): len = 95616.2, overlap = 6.75
PHY-3002 : Step(56): len = 94697.8, overlap = 11.25
PHY-3002 : Step(57): len = 94158.1, overlap = 9
PHY-3002 : Step(58): len = 93829.1, overlap = 4.5
PHY-3002 : Step(59): len = 92239.8, overlap = 9
PHY-3002 : Step(60): len = 87513.9, overlap = 11.25
PHY-3002 : Step(61): len = 86591.5, overlap = 11.25
PHY-3002 : Step(62): len = 86017.8, overlap = 6.75
PHY-3002 : Step(63): len = 85565.2, overlap = 2.25
PHY-3002 : Step(64): len = 82566.5, overlap = 4.5
PHY-3002 : Step(65): len = 80171.1, overlap = 6.75
PHY-3002 : Step(66): len = 78531.2, overlap = 9
PHY-3002 : Step(67): len = 77587.4, overlap = 9
PHY-3002 : Step(68): len = 77136.4, overlap = 11.25
PHY-3002 : Step(69): len = 75580.7, overlap = 9
PHY-3002 : Step(70): len = 74964.5, overlap = 9
PHY-3002 : Step(71): len = 72340.9, overlap = 9
PHY-3002 : Step(72): len = 71474.7, overlap = 9
PHY-3002 : Step(73): len = 70696.5, overlap = 9
PHY-3002 : Step(74): len = 69853.7, overlap = 4.5
PHY-3002 : Step(75): len = 69548.8, overlap = 4.5
PHY-3002 : Step(76): len = 68686.4, overlap = 4.5
PHY-3002 : Step(77): len = 68003, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000215929
PHY-3002 : Step(78): len = 68402.1, overlap = 4.5
PHY-3002 : Step(79): len = 68538.1, overlap = 4.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000431857
PHY-3002 : Step(80): len = 68400.2, overlap = 4.5
PHY-3002 : Step(81): len = 68427.8, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008514s wall, 0.000000s user + 0.046875s system = 0.046875s CPU (550.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.075254s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (103.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(82): len = 70899.8, overlap = 4.21875
PHY-3002 : Step(83): len = 69271, overlap = 4.34375
PHY-3002 : Step(84): len = 67933.8, overlap = 4.21875
PHY-3002 : Step(85): len = 65881.9, overlap = 4.125
PHY-3002 : Step(86): len = 64036.5, overlap = 4.375
PHY-3002 : Step(87): len = 62280.2, overlap = 7.1875
PHY-3002 : Step(88): len = 60790, overlap = 7.4375
PHY-3002 : Step(89): len = 59656.2, overlap = 7.4375
PHY-3002 : Step(90): len = 57028.7, overlap = 7.25
PHY-3002 : Step(91): len = 55020.2, overlap = 7.875
PHY-3002 : Step(92): len = 54276.1, overlap = 9.5
PHY-3002 : Step(93): len = 53563.9, overlap = 8.9375
PHY-3002 : Step(94): len = 52912.2, overlap = 7.6875
PHY-3002 : Step(95): len = 52441.2, overlap = 9.75
PHY-3002 : Step(96): len = 52002.2, overlap = 10.4375
PHY-3002 : Step(97): len = 51411.5, overlap = 10.75
PHY-3002 : Step(98): len = 50848.1, overlap = 10.5625
PHY-3002 : Step(99): len = 50370.2, overlap = 11.0625
PHY-3002 : Step(100): len = 50039.9, overlap = 10.75
PHY-3002 : Step(101): len = 49917.9, overlap = 10.75
PHY-3002 : Step(102): len = 49326.1, overlap = 10.625
PHY-3002 : Step(103): len = 48265.3, overlap = 10.75
PHY-3002 : Step(104): len = 47680.7, overlap = 11.0625
PHY-3002 : Step(105): len = 47445.5, overlap = 11
PHY-3002 : Step(106): len = 47045.2, overlap = 9.78125
PHY-3002 : Step(107): len = 46542.2, overlap = 9.34375
PHY-3002 : Step(108): len = 46074.9, overlap = 9.46875
PHY-3002 : Step(109): len = 45584.9, overlap = 10.3438
PHY-3002 : Step(110): len = 45443.4, overlap = 10.125
PHY-3002 : Step(111): len = 45228.1, overlap = 10.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00024964
PHY-3002 : Step(112): len = 45002, overlap = 10.9375
PHY-3002 : Step(113): len = 44927.6, overlap = 10.9375
PHY-3002 : Step(114): len = 45024.7, overlap = 10.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00049928
PHY-3002 : Step(115): len = 44948.9, overlap = 10.5
PHY-3002 : Step(116): len = 45034.2, overlap = 10.4375
PHY-3002 : Step(117): len = 45078.9, overlap = 10.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063259s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.62066e-05
PHY-3002 : Step(118): len = 45287.7, overlap = 56.5
PHY-3002 : Step(119): len = 45557, overlap = 56.25
PHY-3002 : Step(120): len = 46341.9, overlap = 54.1875
PHY-3002 : Step(121): len = 46580.5, overlap = 53.9375
PHY-3002 : Step(122): len = 46598.7, overlap = 53.6875
PHY-3002 : Step(123): len = 46684.6, overlap = 53.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000152413
PHY-3002 : Step(124): len = 46813.9, overlap = 54.0312
PHY-3002 : Step(125): len = 46992.3, overlap = 54.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000282437
PHY-3002 : Step(126): len = 47229, overlap = 53.75
PHY-3002 : Step(127): len = 48351.1, overlap = 50.5625
PHY-3002 : Step(128): len = 49164, overlap = 48.0625
PHY-3002 : Step(129): len = 49087.4, overlap = 44.5938
PHY-3002 : Step(130): len = 49027.2, overlap = 40.9375
PHY-3002 : Step(131): len = 49023.2, overlap = 35.4062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7847, tnet num: 2215, tinst num: 1644, tnode num: 11088, tedge num: 13249.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.47 peak overflow 3.72
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52624, over cnt = 243(0%), over = 1249, worst = 24
PHY-1001 : End global iterations;  0.053502s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (175.2%)

PHY-1001 : Congestion index: top1 = 48.66, top5 = 26.76, top10 = 16.37, top15 = 11.50.
PHY-1001 : End incremental global routing;  0.101579s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (123.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069895s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.202759s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.6%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1682/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52624, over cnt = 243(0%), over = 1249, worst = 24
PHY-1002 : len = 60984, over cnt = 192(0%), over = 561, worst = 15
PHY-1002 : len = 66968, over cnt = 41(0%), over = 61, worst = 9
PHY-1002 : len = 67456, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 67888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104155s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (120.0%)

PHY-1001 : Congestion index: top1 = 42.37, top5 = 26.87, top10 = 18.99, top15 = 13.91.
OPT-1001 : End congestion update;  0.146164s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (106.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059057s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.207776s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (105.3%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.682126s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (116.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 379 LUT to BLE ...
SYN-4008 : Packed 379 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 102 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 701 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1080/1413 primitive instances ...
PHY-3001 : End packing;  0.053145s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 845 instances
RUN-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 843 instances, 796 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49170, Over = 62.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 843, tnode num: 8964, tedge num: 11611.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.317089s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.33273e-05
PHY-3002 : Step(132): len = 48719.5, overlap = 63
PHY-3002 : Step(133): len = 48503.1, overlap = 64.25
PHY-3002 : Step(134): len = 48244.8, overlap = 67
PHY-3002 : Step(135): len = 48432.5, overlap = 69.25
PHY-3002 : Step(136): len = 48350.4, overlap = 69
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.66547e-05
PHY-3002 : Step(137): len = 48573.9, overlap = 68.75
PHY-3002 : Step(138): len = 48756.5, overlap = 67.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.54873e-05
PHY-3002 : Step(139): len = 49186.4, overlap = 68
PHY-3002 : Step(140): len = 50432.8, overlap = 62.25
PHY-3002 : Step(141): len = 52132.4, overlap = 59.75
PHY-3002 : Step(142): len = 51895.9, overlap = 59.5
PHY-3002 : Step(143): len = 51842.6, overlap = 59
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.064453s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (266.7%)

PHY-3001 : Trial Legalized: Len = 66124.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050219s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000322748
PHY-3002 : Step(144): len = 63353.7, overlap = 7.75
PHY-3002 : Step(145): len = 61710.8, overlap = 13.75
PHY-3002 : Step(146): len = 59995.5, overlap = 20
PHY-3002 : Step(147): len = 58894.2, overlap = 22.5
PHY-3002 : Step(148): len = 58236.7, overlap = 23
PHY-3002 : Step(149): len = 57786.2, overlap = 25.5
PHY-3002 : Step(150): len = 57224.1, overlap = 24.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000645496
PHY-3002 : Step(151): len = 57991.5, overlap = 24
PHY-3002 : Step(152): len = 58198.3, overlap = 23.5
PHY-3002 : Step(153): len = 58248.8, overlap = 24.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00129099
PHY-3002 : Step(154): len = 58456.3, overlap = 24.5
PHY-3002 : Step(155): len = 58577.7, overlap = 23.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004782s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63019.1, Over = 0
PHY-3001 : Spreading special nets. 10 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005764s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 19 instances has been re-located, deltaX = 4, deltaY = 15, maxDist = 1.
PHY-3001 : Final: Len = 63181.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 843, tnode num: 8964, tedge num: 11611.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 68/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70032, over cnt = 151(0%), over = 240, worst = 7
PHY-1002 : len = 71176, over cnt = 92(0%), over = 116, worst = 4
PHY-1002 : len = 72104, over cnt = 27(0%), over = 34, worst = 3
PHY-1002 : len = 72520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127253s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (110.5%)

PHY-1001 : Congestion index: top1 = 33.02, top5 = 23.48, top10 = 18.46, top15 = 14.47.
PHY-1001 : End incremental global routing;  0.178651s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (105.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061545s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.270492s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (104.0%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1808/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005812s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (268.8%)

PHY-1001 : Congestion index: top1 = 33.02, top5 = 23.48, top10 = 18.46, top15 = 14.47.
OPT-1001 : End congestion update;  0.050659s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050487s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 805 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 843 instances, 796 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63216, Over = 0
PHY-3001 : End spreading;  0.005870s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63216, Over = 0
PHY-3001 : End incremental legalization;  0.037203s wall, 0.062500s user + 0.031250s system = 0.093750s CPU (252.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.152340s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (184.6%)

OPT-1001 : Current memory(MB): used = 224, reserve = 188, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054303s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1800/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72584, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72568, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72584, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.026363s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (177.8%)

PHY-1001 : Congestion index: top1 = 33.04, top5 = 23.49, top10 = 18.46, top15 = 14.46.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051577s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.913352s wall, 1.015625s user + 0.062500s system = 1.078125s CPU (118.0%)

RUN-1003 : finish command "place" in  5.219527s wall, 8.421875s user + 2.312500s system = 10.734375s CPU (205.7%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 845 instances
RUN-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 843, tnode num: 8964, tedge num: 11611.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69248, over cnt = 147(0%), over = 231, worst = 7
PHY-1002 : len = 70352, over cnt = 88(0%), over = 108, worst = 3
PHY-1002 : len = 71432, over cnt = 12(0%), over = 15, worst = 3
PHY-1002 : len = 71640, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.152040s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (92.5%)

PHY-1001 : Congestion index: top1 = 32.35, top5 = 23.15, top10 = 18.26, top15 = 14.30.
PHY-1001 : End global routing;  0.200623s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (93.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 203, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 498, reserve = 466, peak = 498.
PHY-1001 : End build detailed router design. 3.271395s wall, 3.250000s user + 0.015625s system = 3.265625s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34104, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.206440s wall, 1.218750s user + 0.000000s system = 1.218750s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 530, reserve = 500, peak = 530.
PHY-1001 : End phase 1; 1.212572s wall, 1.218750s user + 0.000000s system = 1.218750s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179648, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 532.
PHY-1001 : End initial routed; 1.039415s wall, 2.203125s user + 0.125000s system = 2.328125s CPU (224.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1806(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.516   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.359905s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End phase 2; 1.399405s wall, 2.562500s user + 0.125000s system = 2.687500s CPU (192.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179648, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014218s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179672, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024133s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (64.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179720, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020160s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (77.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1806(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.516   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366837s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 0 feed throughs used by 0 nets
PHY-1001 : End commit to database; 0.176848s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.2%)

PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End phase 3; 0.723687s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (99.3%)

PHY-1003 : Routed, final wirelength = 179720
PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End export database. 0.011163s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (140.0%)

PHY-1001 : End detail routing;  6.796668s wall, 7.937500s user + 0.156250s system = 8.093750s CPU (119.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 843, tnode num: 8964, tedge num: 11611.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.740133s wall, 8.890625s user + 0.156250s system = 9.046875s CPU (116.9%)

RUN-1004 : used memory is 524 MB, reserved memory is 497 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      836   out of  19600    4.27%
#reg                     1075   out of  19600    5.48%
#le                      1537
  #lut only               462   out of   1537   30.06%
  #reg only               701   out of   1537   45.61%
  #lut&reg                374   out of   1537   24.33%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         480
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1537   |610     |226     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1141   |308     |136     |921     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |32     |23      |9       |21      |0       |0       |
|    demodu                  |Demodulation                                     |541    |129     |58      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |159    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |47     |2       |0       |47      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |139    |15      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |83     |34      |21      |79      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |82      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |108    |97      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |31     |23      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |56     |56      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1453  
    #2          2       313   
    #3          3       105   
    #4          4        18   
    #5        5-10       79   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 843, tnode num: 8964, tedge num: 11611.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 843
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2042, pip num: 14717
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1275 valid insts, and 39114 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.006923s wall, 17.640625s user + 0.062500s system = 17.703125s CPU (588.7%)

RUN-1004 : used memory is 545 MB, reserved memory is 511 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_180542.log"
