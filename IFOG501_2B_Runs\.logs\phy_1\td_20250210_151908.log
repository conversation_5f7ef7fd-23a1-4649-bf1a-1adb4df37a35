============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Feb 10 15:19:08 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 38 trigger nets, 38 data nets.
KIT-1004 : Chipwatcher code = 0101111100011010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2525/23 useful/useless nets, 1558/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 2210/20 useful/useless nets, 1965/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 378 better
SYN-1014 : Optimize round 2
SYN-1032 : 1906/45 useful/useless nets, 1661/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1954/295 useful/useless nets, 1742/43 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 383 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 36 instances.
SYN-2501 : Optimize round 1, 74 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2382/5 useful/useless nets, 2170/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 9320, tnet num: 2382, tinst num: 2169, tnode num: 11773, tedge num: 14334.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 514 instances into 223 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 372 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 120 adder to BLE ...
SYN-4008 : Packed 120 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.136838s wall, 0.953125s user + 0.156250s system = 1.109375s CPU (97.6%)

RUN-1004 : used memory is 142 MB, reserved memory is 100 MB, peak memory is 165 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (255 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1585 instances
RUN-0007 : 673 luts, 679 seqs, 116 mslices, 70 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1804 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 1058 nets have 2 pins
RUN-1001 : 554 nets have [3 - 5] pins
RUN-1001 : 93 nets have [6 - 10] pins
RUN-1001 : 60 nets have [11 - 20] pins
RUN-1001 : 23 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     31      
RUN-1001 :   No   |  No   |  Yes  |     332     
RUN-1001 :   No   |  Yes  |  No   |      8      
RUN-1001 :   Yes  |  No   |  No   |      0      
RUN-1001 :   Yes  |  No   |  Yes  |     308     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 12
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1583 instances, 673 luts, 679 seqs, 186 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8053, tnet num: 1802, tinst num: 1583, tnode num: 10601, tedge num: 13307.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1802 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.147049s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (95.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 417641
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1583.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 317024, overlap = 76.5
PHY-3002 : Step(2): len = 264649, overlap = 76.5
PHY-3002 : Step(3): len = 229812, overlap = 76.5
PHY-3002 : Step(4): len = 206428, overlap = 76.5
PHY-3002 : Step(5): len = 184199, overlap = 76.5
PHY-3002 : Step(6): len = 161627, overlap = 76.5
PHY-3002 : Step(7): len = 141848, overlap = 76.5
PHY-3002 : Step(8): len = 126623, overlap = 76.5
PHY-3002 : Step(9): len = 114506, overlap = 79.6875
PHY-3002 : Step(10): len = 100919, overlap = 82.3125
PHY-3002 : Step(11): len = 90036.3, overlap = 82.25
PHY-3002 : Step(12): len = 80272.5, overlap = 81.6562
PHY-3002 : Step(13): len = 73090.6, overlap = 80.2188
PHY-3002 : Step(14): len = 67023.5, overlap = 80.4062
PHY-3002 : Step(15): len = 63370.2, overlap = 80.4062
PHY-3002 : Step(16): len = 55843.3, overlap = 80.7188
PHY-3002 : Step(17): len = 52260.4, overlap = 82.5312
PHY-3002 : Step(18): len = 48039.6, overlap = 82.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.04615e-06
PHY-3002 : Step(19): len = 48064.1, overlap = 76.0312
PHY-3002 : Step(20): len = 48227.2, overlap = 73.7812
PHY-3002 : Step(21): len = 46068.4, overlap = 76.3125
PHY-3002 : Step(22): len = 45870.8, overlap = 76.1562
PHY-3002 : Step(23): len = 45531.8, overlap = 76.1562
PHY-3002 : Step(24): len = 44361.2, overlap = 76.4062
PHY-3002 : Step(25): len = 44264.7, overlap = 76.75
PHY-3002 : Step(26): len = 43799.2, overlap = 76.0625
PHY-3002 : Step(27): len = 43199.9, overlap = 76.1875
PHY-3002 : Step(28): len = 42704.3, overlap = 71.5938
PHY-3002 : Step(29): len = 41728.2, overlap = 71.125
PHY-3002 : Step(30): len = 40900, overlap = 75.75
PHY-3002 : Step(31): len = 39621.6, overlap = 76.0938
PHY-3002 : Step(32): len = 38702.4, overlap = 75.5625
PHY-3002 : Step(33): len = 38339.3, overlap = 77.4375
PHY-3002 : Step(34): len = 37430.6, overlap = 79.9062
PHY-3002 : Step(35): len = 37268.5, overlap = 79.9375
PHY-3002 : Step(36): len = 36940, overlap = 79.4062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 6.09229e-06
PHY-3002 : Step(37): len = 36864, overlap = 79.0625
PHY-3002 : Step(38): len = 36848, overlap = 79.8125
PHY-3002 : Step(39): len = 36893.5, overlap = 82.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 1.21846e-05
PHY-3002 : Step(40): len = 37090.1, overlap = 79.5625
PHY-3002 : Step(41): len = 37179.6, overlap = 79.5625
PHY-3002 : Step(42): len = 37200.7, overlap = 74.9688
PHY-3002 : Step(43): len = 37097.2, overlap = 74.9688
PHY-3002 : Step(44): len = 37062, overlap = 74.7812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 2.43692e-05
PHY-3002 : Step(45): len = 37201.3, overlap = 74.5
PHY-3002 : Step(46): len = 37262.5, overlap = 74.2812
PHY-3002 : Step(47): len = 37321.8, overlap = 74.2812
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 4.87383e-05
PHY-3002 : Step(48): len = 37398.1, overlap = 74.4062
PHY-3002 : Step(49): len = 37425.1, overlap = 74.0312
PHY-3002 : Step(50): len = 37547, overlap = 74.0312
PHY-3002 : Step(51): len = 37558.6, overlap = 74.6562
PHY-3002 : Step(52): len = 37479.1, overlap = 74.9062
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 9.74766e-05
PHY-3002 : Step(53): len = 37416.3, overlap = 75.0625
PHY-3002 : Step(54): len = 37386.8, overlap = 75.25
PHY-3002 : Step(55): len = 37357, overlap = 74.8125
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.000194953
PHY-3002 : Step(56): len = 37375.2, overlap = 74.875
PHY-3002 : Step(57): len = 37368.4, overlap = 74.875
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000389907
PHY-3002 : Step(58): len = 37355, overlap = 75.1875
PHY-3002 : Step(59): len = 37348.1, overlap = 74.9688
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.000779813
PHY-3002 : Step(60): len = 37327.7, overlap = 74.8438
PHY-3002 : Step(61): len = 37327.7, overlap = 74.8438
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.00126174
PHY-3002 : Step(62): len = 37297, overlap = 74.6562
PHY-3002 : Step(63): len = 37287.1, overlap = 74.5625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007666s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (407.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1802 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052982s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.14763e-05
PHY-3002 : Step(64): len = 43007.2, overlap = 24.0938
PHY-3002 : Step(65): len = 43076.4, overlap = 24.0312
PHY-3002 : Step(66): len = 42847.3, overlap = 24.75
PHY-3002 : Step(67): len = 42857.5, overlap = 24.8438
PHY-3002 : Step(68): len = 42664.4, overlap = 18.375
PHY-3002 : Step(69): len = 42631.7, overlap = 12.5938
PHY-3002 : Step(70): len = 42671.1, overlap = 14.2812
PHY-3002 : Step(71): len = 42449.5, overlap = 17.9375
PHY-3002 : Step(72): len = 41919.4, overlap = 22.75
PHY-3002 : Step(73): len = 41625.9, overlap = 23.4688
PHY-3002 : Step(74): len = 41145.7, overlap = 23.7188
PHY-3002 : Step(75): len = 40960.4, overlap = 22.9688
PHY-3002 : Step(76): len = 40577.4, overlap = 22.9062
PHY-3002 : Step(77): len = 40308.8, overlap = 22.5
PHY-3002 : Step(78): len = 40066.8, overlap = 20.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.29525e-05
PHY-3002 : Step(79): len = 39847, overlap = 20.7188
PHY-3002 : Step(80): len = 39804, overlap = 20.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000125905
PHY-3002 : Step(81): len = 39764.1, overlap = 20.0312
PHY-3002 : Step(82): len = 39759.7, overlap = 20.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1802 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046319s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.53587e-05
PHY-3002 : Step(83): len = 40736.7, overlap = 71.8125
PHY-3002 : Step(84): len = 41274.4, overlap = 71.625
PHY-3002 : Step(85): len = 41540.8, overlap = 70.375
PHY-3002 : Step(86): len = 42354.4, overlap = 69.3125
PHY-3002 : Step(87): len = 43296.7, overlap = 61.9062
PHY-3002 : Step(88): len = 43344.5, overlap = 61.0312
PHY-3002 : Step(89): len = 43334.1, overlap = 61.4062
PHY-3002 : Step(90): len = 43428.9, overlap = 61.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000110717
PHY-3002 : Step(91): len = 43439, overlap = 59.5
PHY-3002 : Step(92): len = 43503.6, overlap = 59
PHY-3002 : Step(93): len = 44075.7, overlap = 57.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000221435
PHY-3002 : Step(94): len = 44324.2, overlap = 55.5312
PHY-3002 : Step(95): len = 44762.7, overlap = 54.0938
PHY-3002 : Step(96): len = 45904, overlap = 56
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00044287
PHY-3002 : Step(97): len = 45933.6, overlap = 51.8438
PHY-3002 : Step(98): len = 46212, overlap = 52.1562
PHY-3002 : Step(99): len = 46730.3, overlap = 50.875
PHY-3002 : Step(100): len = 46506.7, overlap = 47.75
PHY-3002 : Step(101): len = 46459.6, overlap = 48.1562
PHY-3002 : Step(102): len = 46247.2, overlap = 47.625
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00088574
PHY-3002 : Step(103): len = 46512.9, overlap = 47.625
PHY-3002 : Step(104): len = 46512.9, overlap = 47.625
PHY-3002 : Step(105): len = 46432.7, overlap = 46
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8053, tnet num: 1802, tinst num: 1583, tnode num: 10601, tedge num: 13307.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 101.59 peak overflow 3.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1804.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 61128, over cnt = 260(0%), over = 915, worst = 16
PHY-1001 : End global iterations;  0.093963s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (116.4%)

PHY-1001 : Congestion index: top1 = 38.30, top5 = 24.63, top10 = 16.92, top15 = 12.35.
PHY-1001 : End incremental global routing;  0.143240s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (109.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1802 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055027s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1571 has valid locations, 34 needs to be replaced
PHY-3001 : design contains 1616 instances, 673 luts, 712 seqs, 186 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 46749.6
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8185, tnet num: 1835, tinst num: 1616, tnode num: 10832, tedge num: 13505.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1835 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.165676s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(106): len = 46913, overlap = 1.65625
PHY-3002 : Step(107): len = 46913, overlap = 1.65625
PHY-3002 : Step(108): len = 46950.7, overlap = 1.65625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1835 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046267s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000585532
PHY-3002 : Step(109): len = 46950.7, overlap = 46.125
PHY-3002 : Step(110): len = 46950.7, overlap = 46.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00117106
PHY-3002 : Step(111): len = 47002.2, overlap = 46.1875
PHY-3002 : Step(112): len = 47002.2, overlap = 46.1875
PHY-3001 : Final: Len = 47002.2, Over = 46.1875
PHY-3001 : End incremental placement;  0.324995s wall, 0.390625s user + 0.125000s system = 0.515625s CPU (158.7%)

OPT-1001 : Total overflow 102.22 peak overflow 3.41
OPT-1001 : End high-fanout net optimization;  0.559061s wall, 0.671875s user + 0.156250s system = 0.828125s CPU (148.1%)

OPT-1001 : Current memory(MB): used = 202, reserve = 158, peak = 202.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1264/1837.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 62320, over cnt = 259(0%), over = 903, worst = 16
PHY-1002 : len = 69032, over cnt = 165(0%), over = 309, worst = 10
PHY-1002 : len = 70232, over cnt = 73(0%), over = 129, worst = 9
PHY-1002 : len = 72384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113916s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (109.7%)

PHY-1001 : Congestion index: top1 = 35.54, top5 = 25.02, top10 = 18.29, top15 = 13.85.
OPT-1001 : End congestion update;  0.158408s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1835 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047460s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.8%)

OPT-0007 : Start: WNS 3919 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.206109s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (98.6%)

OPT-1001 : Current memory(MB): used = 200, reserve = 156, peak = 202.
OPT-1001 : End physical optimization;  0.914848s wall, 1.031250s user + 0.156250s system = 1.187500s CPU (129.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 673 LUT to BLE ...
SYN-4008 : Packed 673 LUT and 284 SEQ to BLE.
SYN-4003 : Packing 428 remaining SEQ's ...
SYN-4005 : Packed 219 SEQ with LUT/SLICE
SYN-4006 : 197 single LUT's are left
SYN-4006 : 209 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 882/1249 primitive instances ...
PHY-3001 : End packing;  0.050680s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 725 instances
RUN-1001 : 339 mslices, 339 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1558 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 770 nets have 2 pins
RUN-1001 : 595 nets have [3 - 5] pins
RUN-1001 : 94 nets have [6 - 10] pins
RUN-1001 : 58 nets have [11 - 20] pins
RUN-1001 : 29 nets have [21 - 99] pins
RUN-1001 : 6 nets have 100+ pins
PHY-3001 : design contains 723 instances, 678 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 47405.8, Over = 57.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7025, tnet num: 1556, tinst num: 723, tnode num: 8979, tedge num: 12044.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.166721s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.14823e-05
PHY-3002 : Step(113): len = 46277.9, overlap = 54
PHY-3002 : Step(114): len = 45718.6, overlap = 52.25
PHY-3002 : Step(115): len = 45121.8, overlap = 54.75
PHY-3002 : Step(116): len = 44896.1, overlap = 56.25
PHY-3002 : Step(117): len = 44857.6, overlap = 56.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000122965
PHY-3002 : Step(118): len = 45307.9, overlap = 54.5
PHY-3002 : Step(119): len = 45725.6, overlap = 52.75
PHY-3002 : Step(120): len = 45922.7, overlap = 52.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000245929
PHY-3002 : Step(121): len = 46650.2, overlap = 49.75
PHY-3002 : Step(122): len = 47146.1, overlap = 48.25
PHY-3002 : Step(123): len = 47450.3, overlap = 48.5
PHY-3002 : Step(124): len = 47037.9, overlap = 50
PHY-3002 : Step(125): len = 46845.7, overlap = 50.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.087272s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (196.9%)

PHY-3001 : Trial Legalized: Len = 63126.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.040513s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0085905
PHY-3002 : Step(126): len = 59299.7, overlap = 8.5
PHY-3002 : Step(127): len = 58375.3, overlap = 8.5
PHY-3002 : Step(128): len = 56965.7, overlap = 10.75
PHY-3002 : Step(129): len = 55889.8, overlap = 13.75
PHY-3002 : Step(130): len = 54812.5, overlap = 15.5
PHY-3002 : Step(131): len = 54125.3, overlap = 19
PHY-3002 : Step(132): len = 53350.1, overlap = 19
PHY-3002 : Step(133): len = 52760.8, overlap = 20
PHY-3002 : Step(134): len = 52219, overlap = 21.25
PHY-3002 : Step(135): len = 51702.8, overlap = 22.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0152031
PHY-3002 : Step(136): len = 51755.8, overlap = 22
PHY-3002 : Step(137): len = 51650.9, overlap = 21.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0304063
PHY-3002 : Step(138): len = 51655.7, overlap = 21.25
PHY-3002 : Step(139): len = 51616.9, overlap = 21.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004915s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 57110.3, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004560s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 7 instances has been re-located, deltaX = 2, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 57196.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7025, tnet num: 1556, tinst num: 723, tnode num: 8979, tedge num: 12044.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/1558.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 75648, over cnt = 211(0%), over = 332, worst = 6
PHY-1002 : len = 76600, over cnt = 120(0%), over = 168, worst = 3
PHY-1002 : len = 77456, over cnt = 62(0%), over = 93, worst = 3
PHY-1002 : len = 78024, over cnt = 24(0%), over = 41, worst = 3
PHY-1002 : len = 78336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.186579s wall, 0.171875s user + 0.062500s system = 0.234375s CPU (125.6%)

PHY-1001 : Congestion index: top1 = 29.14, top5 = 22.76, top10 = 18.50, top15 = 14.81.
PHY-1001 : End incremental global routing;  0.238926s wall, 0.250000s user + 0.062500s system = 0.312500s CPU (130.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052253s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.320931s wall, 0.328125s user + 0.062500s system = 0.390625s CPU (121.7%)

OPT-1001 : Current memory(MB): used = 204, reserve = 160, peak = 204.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1348/1558.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 78336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006548s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 29.14, top5 = 22.76, top10 = 18.50, top15 = 14.81.
OPT-1001 : End congestion update;  0.052847s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043274s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (108.3%)

OPT-0007 : Start: WNS 3775 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.096454s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.2%)

OPT-1001 : Current memory(MB): used = 206, reserve = 162, peak = 206.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.039695s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1348/1558.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 78336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006883s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 29.14, top5 = 22.76, top10 = 18.50, top15 = 14.81.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.040414s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (116.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3775 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 28.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3775ps with logic level 6 
RUN-1001 :       #2 path slack 3829ps with logic level 6 
OPT-1001 : End physical optimization;  0.707591s wall, 0.703125s user + 0.062500s system = 0.765625s CPU (108.2%)

RUN-1003 : finish command "place" in  4.624684s wall, 7.328125s user + 2.640625s system = 9.968750s CPU (215.6%)

RUN-1004 : used memory is 196 MB, reserved memory is 153 MB, peak memory is 207 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 725 instances
RUN-1001 : 339 mslices, 339 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1558 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 770 nets have 2 pins
RUN-1001 : 595 nets have [3 - 5] pins
RUN-1001 : 94 nets have [6 - 10] pins
RUN-1001 : 58 nets have [11 - 20] pins
RUN-1001 : 29 nets have [21 - 99] pins
RUN-1001 : 6 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7025, tnet num: 1556, tinst num: 723, tnode num: 8979, tedge num: 12044.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 339 mslices, 339 lslices, 8 pads, 34 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74632, over cnt = 214(0%), over = 339, worst = 6
PHY-1002 : len = 75704, over cnt = 129(0%), over = 177, worst = 3
PHY-1002 : len = 77136, over cnt = 32(0%), over = 48, worst = 3
PHY-1002 : len = 77664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.193954s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (112.8%)

PHY-1001 : Congestion index: top1 = 29.16, top5 = 22.73, top10 = 18.49, top15 = 14.78.
PHY-1001 : End global routing;  0.243494s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (109.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 224, reserve = 182, peak = 224.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 485, reserve = 446, peak = 485.
PHY-1001 : End build detailed router design. 3.131964s wall, 3.109375s user + 0.031250s system = 3.140625s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32536, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.598715s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 517, reserve = 479, peak = 517.
PHY-1001 : End phase 1; 0.604862s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (98.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 299992, over cnt = 64(0%), over = 64, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 519, reserve = 480, peak = 520.
PHY-1001 : End initial routed; 3.552698s wall, 4.296875s user + 0.078125s system = 4.375000s CPU (123.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1386(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.445   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.224956s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.2%)

PHY-1001 : Current memory(MB): used = 522, reserve = 482, peak = 522.
PHY-1001 : End phase 2; 3.777741s wall, 4.515625s user + 0.078125s system = 4.593750s CPU (121.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 299992, over cnt = 64(0%), over = 64, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013479s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (115.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 295856, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.291030s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 295848, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.031508s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (99.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 292000, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.062139s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1386(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.445   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.226509s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (103.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 27 feed throughs used by 9 nets
PHY-1001 : End commit to database; 0.223359s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.9%)

PHY-1001 : Current memory(MB): used = 536, reserve = 497, peak = 536.
PHY-1001 : End phase 3; 0.965403s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (100.3%)

PHY-1003 : Routed, final wirelength = 292000
PHY-1001 : Current memory(MB): used = 536, reserve = 497, peak = 536.
PHY-1001 : End export database. 0.010071s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (155.1%)

PHY-1001 : End detail routing;  8.667032s wall, 9.359375s user + 0.125000s system = 9.484375s CPU (109.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7025, tnet num: 1556, tinst num: 723, tnode num: 8979, tedge num: 12044.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  9.343739s wall, 10.046875s user + 0.140625s system = 10.187500s CPU (109.0%)

RUN-1004 : used memory is 492 MB, reserved memory is 451 MB, peak memory is 536 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                     1069   out of  19600    5.45%
#reg                      716   out of  19600    3.65%
#le                      1278
  #lut only               562   out of   1278   43.97%
  #reg only               209   out of   1278   16.35%
  #lut&reg                507   out of   1278   39.67%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    332
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         141
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
   miso        INPUT         A4        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         A8        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         A6        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8            NONE       TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1278   |883     |186     |722     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |123    |109     |11      |60      |0       |0       |
|    usms                            |Time_1ms        |35     |29      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |224    |177     |23      |131     |0       |0       |
|  uart                              |UART_Control    |128    |112     |4       |58      |0       |0       |
|    U0                              |speed_select_Tx |28     |24      |4       |16      |0       |0       |
|    U1                              |uart_tx         |24     |19      |0       |19      |0       |0       |
|    U2                              |Ctrl_Data       |76     |69      |0       |23      |0       |0       |
|  wendu                             |DS18B20         |180    |137     |43      |46      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |590    |330     |99      |399     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |590    |330     |99      |399     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |250    |113     |0       |243     |0       |0       |
|        reg_inst                    |register        |247    |110     |0       |240     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |340    |217     |99      |156     |0       |0       |
|        bus_inst                    |bus_top         |119    |74      |42      |47      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |16     |10      |6       |8       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |25     |15      |10      |9       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |74     |45      |26      |26      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |136    |86      |29      |79      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       761   
    #2          2       346   
    #3          3       190   
    #4          4        59   
    #5        5-10      102   
    #6        11-50      76   
    #7       51-100      3    
    #8       101-500     2    
  Average     3.23            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7025, tnet num: 1556, tinst num: 723, tnode num: 8979, tedge num: 12044.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 723
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1558, pip num: 18400
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 27
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1691 valid insts, and 48682 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011001110101111100011010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.795104s wall, 22.828125s user + 0.093750s system = 22.921875s CPU (604.0%)

RUN-1004 : used memory is 509 MB, reserved memory is 473 MB, peak memory is 653 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250210_151908.log"
