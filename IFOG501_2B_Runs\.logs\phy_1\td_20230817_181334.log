============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug 17 18:13:34 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1631 instances
RUN-0007 : 376 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2201 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1639 nets have 2 pins
RUN-1001 : 446 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1629 instances, 376 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7803, tnet num: 2199, tinst num: 1629, tnode num: 11043, tedge num: 13193.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.300589s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 596227
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1629.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 473285, overlap = 20.25
PHY-3002 : Step(2): len = 436202, overlap = 20.25
PHY-3002 : Step(3): len = 395617, overlap = 20.25
PHY-3002 : Step(4): len = 368110, overlap = 20.25
PHY-3002 : Step(5): len = 358621, overlap = 20.25
PHY-3002 : Step(6): len = 313788, overlap = 20.25
PHY-3002 : Step(7): len = 291417, overlap = 20.25
PHY-3002 : Step(8): len = 276540, overlap = 20.25
PHY-3002 : Step(9): len = 269145, overlap = 20.25
PHY-3002 : Step(10): len = 264760, overlap = 20.25
PHY-3002 : Step(11): len = 259940, overlap = 20.25
PHY-3002 : Step(12): len = 251055, overlap = 20.25
PHY-3002 : Step(13): len = 242490, overlap = 20.25
PHY-3002 : Step(14): len = 238799, overlap = 20.25
PHY-3002 : Step(15): len = 232909, overlap = 20.25
PHY-3002 : Step(16): len = 226041, overlap = 20.25
PHY-3002 : Step(17): len = 220584, overlap = 20.25
PHY-3002 : Step(18): len = 217607, overlap = 20.25
PHY-3002 : Step(19): len = 209644, overlap = 20.25
PHY-3002 : Step(20): len = 205108, overlap = 20.25
PHY-3002 : Step(21): len = 201302, overlap = 20.25
PHY-3002 : Step(22): len = 197087, overlap = 20.25
PHY-3002 : Step(23): len = 190152, overlap = 20.25
PHY-3002 : Step(24): len = 187587, overlap = 20.25
PHY-3002 : Step(25): len = 182047, overlap = 20.25
PHY-3002 : Step(26): len = 175229, overlap = 20.25
PHY-3002 : Step(27): len = 170059, overlap = 20.25
PHY-3002 : Step(28): len = 168482, overlap = 20.25
PHY-3002 : Step(29): len = 142935, overlap = 20.25
PHY-3002 : Step(30): len = 130556, overlap = 20.25
PHY-3002 : Step(31): len = 128636, overlap = 20.25
PHY-3002 : Step(32): len = 121208, overlap = 20.25
PHY-3002 : Step(33): len = 111172, overlap = 20.25
PHY-3002 : Step(34): len = 108300, overlap = 20.25
PHY-3002 : Step(35): len = 104794, overlap = 20.25
PHY-3002 : Step(36): len = 103632, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000104815
PHY-3002 : Step(37): len = 106143, overlap = 13.5
PHY-3002 : Step(38): len = 106376, overlap = 11.25
PHY-3002 : Step(39): len = 105015, overlap = 11.25
PHY-3002 : Step(40): len = 102976, overlap = 15.75
PHY-3002 : Step(41): len = 101397, overlap = 15.75
PHY-3002 : Step(42): len = 101189, overlap = 13.5
PHY-3002 : Step(43): len = 99998.8, overlap = 9
PHY-3002 : Step(44): len = 97994.6, overlap = 15.75
PHY-3002 : Step(45): len = 94546.3, overlap = 15.75
PHY-3002 : Step(46): len = 93108.2, overlap = 9
PHY-3002 : Step(47): len = 89827, overlap = 13.5
PHY-3002 : Step(48): len = 89569.8, overlap = 11.25
PHY-3002 : Step(49): len = 86864.2, overlap = 15.75
PHY-3002 : Step(50): len = 83445.2, overlap = 15.75
PHY-3002 : Step(51): len = 81202.2, overlap = 13.5
PHY-3002 : Step(52): len = 80697.3, overlap = 13.5
PHY-3002 : Step(53): len = 79400, overlap = 13.5
PHY-3002 : Step(54): len = 77843.3, overlap = 11.25
PHY-3002 : Step(55): len = 74553.4, overlap = 11.25
PHY-3002 : Step(56): len = 73324.8, overlap = 11.25
PHY-3002 : Step(57): len = 73315.3, overlap = 11.25
PHY-3002 : Step(58): len = 72242, overlap = 9
PHY-3002 : Step(59): len = 71872.1, overlap = 11.25
PHY-3002 : Step(60): len = 69211.6, overlap = 11.25
PHY-3002 : Step(61): len = 66700.4, overlap = 13.625
PHY-3002 : Step(62): len = 64538.6, overlap = 15.875
PHY-3002 : Step(63): len = 64105.6, overlap = 15.875
PHY-3002 : Step(64): len = 63153.3, overlap = 9.0625
PHY-3002 : Step(65): len = 62400.7, overlap = 11.375
PHY-3002 : Step(66): len = 61832.6, overlap = 11.5
PHY-3002 : Step(67): len = 61382.7, overlap = 11.5
PHY-3002 : Step(68): len = 60901.2, overlap = 9.25
PHY-3002 : Step(69): len = 60381.6, overlap = 9
PHY-3002 : Step(70): len = 58907.2, overlap = 11.5
PHY-3002 : Step(71): len = 58385, overlap = 11.5
PHY-3002 : Step(72): len = 57451.2, overlap = 13.5625
PHY-3002 : Step(73): len = 56747.6, overlap = 15.875
PHY-3002 : Step(74): len = 55513, overlap = 13.625
PHY-3002 : Step(75): len = 54608.6, overlap = 13.625
PHY-3002 : Step(76): len = 53780.3, overlap = 13.625
PHY-3002 : Step(77): len = 53548.7, overlap = 13.6875
PHY-3002 : Step(78): len = 53064, overlap = 13.6875
PHY-3002 : Step(79): len = 52382.4, overlap = 13.5
PHY-3002 : Step(80): len = 51986.7, overlap = 13.5
PHY-3002 : Step(81): len = 51772.5, overlap = 13.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00020963
PHY-3002 : Step(82): len = 51654, overlap = 13.6875
PHY-3002 : Step(83): len = 51584.5, overlap = 13.6875
PHY-3002 : Step(84): len = 51542.6, overlap = 13.6875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000419261
PHY-3002 : Step(85): len = 51661.4, overlap = 11.4375
PHY-3002 : Step(86): len = 51655.4, overlap = 11.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006810s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (229.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064102s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000987652
PHY-3002 : Step(87): len = 54569.2, overlap = 5.25
PHY-3002 : Step(88): len = 53900, overlap = 5.1875
PHY-3002 : Step(89): len = 53264.5, overlap = 5.21875
PHY-3002 : Step(90): len = 52569, overlap = 6.5
PHY-3002 : Step(91): len = 52310.3, overlap = 6.6875
PHY-3002 : Step(92): len = 51219.9, overlap = 6.5625
PHY-3002 : Step(93): len = 50741, overlap = 6.4375
PHY-3002 : Step(94): len = 49875.3, overlap = 6.25
PHY-3002 : Step(95): len = 49366.2, overlap = 7.8125
PHY-3002 : Step(96): len = 49425.9, overlap = 8.5625
PHY-3002 : Step(97): len = 49308.4, overlap = 8.15625
PHY-3002 : Step(98): len = 48966.7, overlap = 8.09375
PHY-3002 : Step(99): len = 48581.8, overlap = 8.21875
PHY-3002 : Step(100): len = 48424.3, overlap = 8.46875
PHY-3002 : Step(101): len = 47998.7, overlap = 9.09375
PHY-3002 : Step(102): len = 47883.5, overlap = 10.2812
PHY-3002 : Step(103): len = 47667.9, overlap = 11.7812
PHY-3002 : Step(104): len = 47535.1, overlap = 11.6562
PHY-3002 : Step(105): len = 47569.7, overlap = 11.7188
PHY-3002 : Step(106): len = 47420.7, overlap = 11.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0019753
PHY-3002 : Step(107): len = 47309.2, overlap = 11.7812
PHY-3002 : Step(108): len = 47309.2, overlap = 11.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00395061
PHY-3002 : Step(109): len = 47323, overlap = 11.6562
PHY-3002 : Step(110): len = 47323, overlap = 11.6562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065656s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000114571
PHY-3002 : Step(111): len = 48015.3, overlap = 57
PHY-3002 : Step(112): len = 48609.8, overlap = 56.875
PHY-3002 : Step(113): len = 48390.4, overlap = 54.875
PHY-3002 : Step(114): len = 48438.6, overlap = 54.375
PHY-3002 : Step(115): len = 48267.4, overlap = 53.9688
PHY-3002 : Step(116): len = 48295.2, overlap = 53.4062
PHY-3002 : Step(117): len = 48206.1, overlap = 52.2188
PHY-3002 : Step(118): len = 48262.6, overlap = 52.9375
PHY-3002 : Step(119): len = 48289.4, overlap = 52.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000229141
PHY-3002 : Step(120): len = 48232.5, overlap = 51.9062
PHY-3002 : Step(121): len = 48632.2, overlap = 43.125
PHY-3002 : Step(122): len = 48851.1, overlap = 42.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000458282
PHY-3002 : Step(123): len = 49317, overlap = 40.0938
PHY-3002 : Step(124): len = 49919.5, overlap = 38.625
PHY-3002 : Step(125): len = 50937.1, overlap = 37.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000916565
PHY-3002 : Step(126): len = 51137.4, overlap = 32.3125
PHY-3002 : Step(127): len = 51523.3, overlap = 31.5938
PHY-3002 : Step(128): len = 52460.1, overlap = 29.8125
PHY-3002 : Step(129): len = 52264.6, overlap = 30.8438
PHY-3002 : Step(130): len = 52285.1, overlap = 31.1875
PHY-3002 : Step(131): len = 52214.5, overlap = 30.875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00183313
PHY-3002 : Step(132): len = 52457.4, overlap = 30.7812
PHY-3002 : Step(133): len = 52457.4, overlap = 30.7812
PHY-3002 : Step(134): len = 52499.3, overlap = 30.4688
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00353336
PHY-3002 : Step(135): len = 53072.1, overlap = 30.0625
PHY-3002 : Step(136): len = 53427.5, overlap = 29.5
PHY-3002 : Step(137): len = 53596.8, overlap = 27.25
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00706671
PHY-3002 : Step(138): len = 53166.1, overlap = 27.7188
PHY-3002 : Step(139): len = 53059.2, overlap = 27.3125
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.0118383
PHY-3002 : Step(140): len = 53161.5, overlap = 27.0938
PHY-3002 : Step(141): len = 53056.9, overlap = 26.8125
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0191544
PHY-3002 : Step(142): len = 53197.7, overlap = 26.75
PHY-3002 : Step(143): len = 53197.7, overlap = 26.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7803, tnet num: 2199, tinst num: 1629, tnode num: 11043, tedge num: 13193.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 79.06 peak overflow 2.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2201.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57384, over cnt = 248(0%), over = 1003, worst = 15
PHY-1001 : End global iterations;  0.058776s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (186.1%)

PHY-1001 : Congestion index: top1 = 40.73, top5 = 25.18, top10 = 16.40, top15 = 11.71.
PHY-1001 : End incremental global routing;  0.110260s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (141.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073198s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.215773s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (123.1%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1713/2201.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57384, over cnt = 248(0%), over = 1003, worst = 15
PHY-1002 : len = 63936, over cnt = 152(0%), over = 290, worst = 9
PHY-1002 : len = 66352, over cnt = 41(0%), over = 70, worst = 9
PHY-1002 : len = 66800, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 67056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.097941s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (207.4%)

PHY-1001 : Congestion index: top1 = 36.64, top5 = 24.63, top10 = 17.74, top15 = 13.12.
OPT-1001 : End congestion update;  0.144635s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (172.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064137s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.211289s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (155.3%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : End physical optimization;  0.725684s wall, 0.781250s user + 0.109375s system = 0.890625s CPU (122.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1078/1406 primitive instances ...
PHY-3001 : End packing;  0.046811s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1482 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 839 instances, 792 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53152, Over = 58.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6580, tnet num: 2031, tinst num: 839, tnode num: 8935, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.325859s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.22936e-05
PHY-3002 : Step(144): len = 52460.7, overlap = 59.25
PHY-3002 : Step(145): len = 52056, overlap = 58.25
PHY-3002 : Step(146): len = 51740.5, overlap = 59.75
PHY-3002 : Step(147): len = 51454.8, overlap = 57
PHY-3002 : Step(148): len = 51299.1, overlap = 55.5
PHY-3002 : Step(149): len = 51148.9, overlap = 55.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.45872e-05
PHY-3002 : Step(150): len = 51508.2, overlap = 51.75
PHY-3002 : Step(151): len = 51706.8, overlap = 51.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000108432
PHY-3002 : Step(152): len = 52322.5, overlap = 50.5
PHY-3002 : Step(153): len = 53805.1, overlap = 47.25
PHY-3002 : Step(154): len = 54517.3, overlap = 44.75
PHY-3002 : Step(155): len = 54548.2, overlap = 45.25
PHY-3002 : Step(156): len = 54551.5, overlap = 44
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.098953s wall, 0.093750s user + 0.093750s system = 0.187500s CPU (189.5%)

PHY-3001 : Trial Legalized: Len = 67169.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055721s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000660436
PHY-3002 : Step(157): len = 64870.6, overlap = 7.5
PHY-3002 : Step(158): len = 62636.8, overlap = 12.5
PHY-3002 : Step(159): len = 60709.1, overlap = 17.25
PHY-3002 : Step(160): len = 60019.1, overlap = 17.25
PHY-3002 : Step(161): len = 59360.5, overlap = 20.25
PHY-3002 : Step(162): len = 58883.2, overlap = 23
PHY-3002 : Step(163): len = 58599.2, overlap = 25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00132087
PHY-3002 : Step(164): len = 58858.1, overlap = 23.25
PHY-3002 : Step(165): len = 58959.2, overlap = 23.25
PHY-3002 : Step(166): len = 58848.6, overlap = 24
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00264175
PHY-3002 : Step(167): len = 59118.5, overlap = 23.25
PHY-3002 : Step(168): len = 59283.1, overlap = 22.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005283s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63812.2, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005963s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (262.1%)

PHY-3001 : 11 instances has been re-located, deltaX = 3, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 63874.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6580, tnet num: 2031, tinst num: 839, tnode num: 8935, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 106/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70120, over cnt = 119(0%), over = 185, worst = 8
PHY-1002 : len = 71096, over cnt = 55(0%), over = 69, worst = 5
PHY-1002 : len = 71792, over cnt = 16(0%), over = 16, worst = 1
PHY-1002 : len = 72120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.141166s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (110.7%)

PHY-1001 : Congestion index: top1 = 32.13, top5 = 22.76, top10 = 17.52, top15 = 13.76.
PHY-1001 : End incremental global routing;  0.201254s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (108.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.090207s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (86.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.323331s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1788/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010991s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (142.2%)

PHY-1001 : Congestion index: top1 = 32.13, top5 = 22.76, top10 = 17.52, top15 = 13.76.
OPT-1001 : End congestion update;  0.059627s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057240s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 801 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 839 instances, 792 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64325, Over = 0
PHY-3001 : End spreading;  0.005492s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64325, Over = 0
PHY-3001 : End incremental legalization;  0.037071s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.3%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.169122s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 225, reserve = 189, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052408s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1774/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72576, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 72576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.016921s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (92.3%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 22.75, top10 = 17.53, top15 = 13.80.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056120s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.977048s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (105.5%)

RUN-1003 : finish command "place" in  6.169966s wall, 8.765625s user + 3.671875s system = 12.437500s CPU (201.6%)

RUN-1004 : used memory is 205 MB, reserved memory is 167 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1482 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6580, tnet num: 2031, tinst num: 839, tnode num: 8935, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69824, over cnt = 127(0%), over = 188, worst = 8
PHY-1002 : len = 70672, over cnt = 62(0%), over = 76, worst = 5
PHY-1002 : len = 71488, over cnt = 13(0%), over = 13, worst = 1
PHY-1002 : len = 71728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.171315s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (164.2%)

PHY-1001 : Congestion index: top1 = 31.92, top5 = 22.51, top10 = 17.32, top15 = 13.65.
PHY-1001 : End global routing;  0.257936s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (145.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 205, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 503, reserve = 470, peak = 503.
PHY-1001 : End build detailed router design. 4.489249s wall, 4.421875s user + 0.093750s system = 4.515625s CPU (100.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33320, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.801823s wall, 1.781250s user + 0.015625s system = 1.796875s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 1; 1.808023s wall, 1.781250s user + 0.015625s system = 1.796875s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184576, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End initial routed; 1.610890s wall, 3.156250s user + 0.171875s system = 3.328125s CPU (206.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.681   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.525814s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (98.1%)

PHY-1001 : Current memory(MB): used = 539, reserve = 507, peak = 539.
PHY-1001 : End phase 2; 2.136834s wall, 3.671875s user + 0.171875s system = 3.843750s CPU (179.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184576, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.020846s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (149.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184536, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.040853s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (76.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184624, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.035714s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 184664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.028464s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (54.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.681   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.563098s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.235532s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 553, reserve = 521, peak = 553.
PHY-1001 : End phase 3; 1.086948s wall, 1.062500s user + 0.031250s system = 1.093750s CPU (100.6%)

PHY-1003 : Routed, final wirelength = 184664
PHY-1001 : Current memory(MB): used = 554, reserve = 521, peak = 554.
PHY-1001 : End export database. 0.011287s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (138.4%)

PHY-1001 : End detail routing;  9.792293s wall, 11.203125s user + 0.328125s system = 11.531250s CPU (117.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6580, tnet num: 2031, tinst num: 839, tnode num: 8935, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  10.911703s wall, 12.421875s user + 0.328125s system = 12.750000s CPU (116.8%)

RUN-1004 : used memory is 529 MB, reserved memory is 499 MB, peak memory is 554 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      821   out of  19600    4.19%
#reg                     1074   out of  19600    5.48%
#le                      1523
  #lut only               449   out of   1523   29.48%
  #reg only               702   out of   1523   46.09%
  #lut&reg                372   out of   1523   24.43%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1523   |600     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1117   |296     |128     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |520    |120     |53      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |4       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |141    |17      |14      |115     |0       |0       |
|    modu                    |Modulation                                       |90     |25      |21      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |92      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |109    |93      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |51     |47      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1446  
    #2          2       311   
    #3          3       108   
    #4          4        15   
    #5        5-10       80   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6580, tnet num: 2031, tinst num: 839, tnode num: 8935, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 839
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2033, pip num: 14830
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1354 valid insts, and 39259 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.582932s wall, 20.156250s user + 0.281250s system = 20.437500s CPU (570.4%)

RUN-1004 : used memory is 551 MB, reserved memory is 519 MB, peak memory is 674 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230817_181334.log"
