============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Sep  6 11:54:54 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1621 instances
RUN-0007 : 363 luts, 984 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2191 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1640 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 69 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1619 instances, 363 luts, 984 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7739, tnet num: 2189, tinst num: 1619, tnode num: 10979, tedge num: 13084.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283631s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 581244
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1619.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 554473, overlap = 20.25
PHY-3002 : Step(2): len = 457757, overlap = 15.75
PHY-3002 : Step(3): len = 393078, overlap = 15.75
PHY-3002 : Step(4): len = 377261, overlap = 11.25
PHY-3002 : Step(5): len = 362401, overlap = 18
PHY-3002 : Step(6): len = 353337, overlap = 18
PHY-3002 : Step(7): len = 342456, overlap = 18
PHY-3002 : Step(8): len = 331533, overlap = 18
PHY-3002 : Step(9): len = 324657, overlap = 18
PHY-3002 : Step(10): len = 315293, overlap = 15.75
PHY-3002 : Step(11): len = 304189, overlap = 15.75
PHY-3002 : Step(12): len = 298059, overlap = 15.75
PHY-3002 : Step(13): len = 291083, overlap = 18
PHY-3002 : Step(14): len = 282943, overlap = 18
PHY-3002 : Step(15): len = 276871, overlap = 18
PHY-3002 : Step(16): len = 271790, overlap = 18
PHY-3002 : Step(17): len = 264848, overlap = 20.25
PHY-3002 : Step(18): len = 257280, overlap = 20.25
PHY-3002 : Step(19): len = 253163, overlap = 20.25
PHY-3002 : Step(20): len = 246538, overlap = 20.25
PHY-3002 : Step(21): len = 238616, overlap = 20.25
PHY-3002 : Step(22): len = 234895, overlap = 20.25
PHY-3002 : Step(23): len = 230846, overlap = 20.25
PHY-3002 : Step(24): len = 219266, overlap = 20.25
PHY-3002 : Step(25): len = 212960, overlap = 20.25
PHY-3002 : Step(26): len = 210859, overlap = 20.25
PHY-3002 : Step(27): len = 201289, overlap = 20.25
PHY-3002 : Step(28): len = 183064, overlap = 20.25
PHY-3002 : Step(29): len = 179768, overlap = 20.25
PHY-3002 : Step(30): len = 176433, overlap = 20.25
PHY-3002 : Step(31): len = 147557, overlap = 20.25
PHY-3002 : Step(32): len = 141126, overlap = 20.25
PHY-3002 : Step(33): len = 139627, overlap = 20.25
PHY-3002 : Step(34): len = 136980, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000126823
PHY-3002 : Step(35): len = 138680, overlap = 6.75
PHY-3002 : Step(36): len = 137671, overlap = 9
PHY-3002 : Step(37): len = 135323, overlap = 13.5
PHY-3002 : Step(38): len = 132854, overlap = 9
PHY-3002 : Step(39): len = 131032, overlap = 2.25
PHY-3002 : Step(40): len = 124797, overlap = 6.75
PHY-3002 : Step(41): len = 119534, overlap = 9
PHY-3002 : Step(42): len = 119527, overlap = 4.5
PHY-3002 : Step(43): len = 118162, overlap = 4.5
PHY-3002 : Step(44): len = 115230, overlap = 9
PHY-3002 : Step(45): len = 108690, overlap = 9
PHY-3002 : Step(46): len = 108398, overlap = 4.5
PHY-3002 : Step(47): len = 106474, overlap = 4.5
PHY-3002 : Step(48): len = 103770, overlap = 9
PHY-3002 : Step(49): len = 103587, overlap = 9
PHY-3002 : Step(50): len = 103071, overlap = 4.5
PHY-3002 : Step(51): len = 101249, overlap = 4.5
PHY-3002 : Step(52): len = 100275, overlap = 4.5
PHY-3002 : Step(53): len = 98322.8, overlap = 9
PHY-3002 : Step(54): len = 95910.1, overlap = 9
PHY-3002 : Step(55): len = 94642.9, overlap = 9
PHY-3002 : Step(56): len = 93659.1, overlap = 4.5
PHY-3002 : Step(57): len = 92737.1, overlap = 6.75
PHY-3002 : Step(58): len = 90344.7, overlap = 6.75
PHY-3002 : Step(59): len = 88860.9, overlap = 4.5
PHY-3002 : Step(60): len = 86399.8, overlap = 6.75
PHY-3002 : Step(61): len = 83945.6, overlap = 9
PHY-3002 : Step(62): len = 82505.1, overlap = 9
PHY-3002 : Step(63): len = 82195.5, overlap = 6.75
PHY-3002 : Step(64): len = 78871.2, overlap = 6.75
PHY-3002 : Step(65): len = 76684.6, overlap = 4.5
PHY-3002 : Step(66): len = 74923.1, overlap = 11.25
PHY-3002 : Step(67): len = 74576.9, overlap = 11.25
PHY-3002 : Step(68): len = 74538, overlap = 4.5
PHY-3002 : Step(69): len = 73822.5, overlap = 4.5
PHY-3002 : Step(70): len = 72311.1, overlap = 9
PHY-3002 : Step(71): len = 71147.5, overlap = 9
PHY-3002 : Step(72): len = 70420.8, overlap = 6.75
PHY-3002 : Step(73): len = 69648.1, overlap = 2.5
PHY-3002 : Step(74): len = 68003.7, overlap = 4.5
PHY-3002 : Step(75): len = 66768.6, overlap = 9
PHY-3002 : Step(76): len = 65714.4, overlap = 9
PHY-3002 : Step(77): len = 64251.8, overlap = 4.5
PHY-3002 : Step(78): len = 63801.6, overlap = 9
PHY-3002 : Step(79): len = 62636.3, overlap = 9
PHY-3002 : Step(80): len = 62600.5, overlap = 4.5
PHY-3002 : Step(81): len = 62145.4, overlap = 2.25
PHY-3002 : Step(82): len = 59117.1, overlap = 6.75
PHY-3002 : Step(83): len = 58031.3, overlap = 9
PHY-3002 : Step(84): len = 57794.1, overlap = 9
PHY-3002 : Step(85): len = 57873.9, overlap = 9
PHY-3002 : Step(86): len = 57553.7, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000253647
PHY-3002 : Step(87): len = 57647.5, overlap = 4.5
PHY-3002 : Step(88): len = 57677.9, overlap = 4.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000507294
PHY-3002 : Step(89): len = 57596.4, overlap = 4.5
PHY-3002 : Step(90): len = 57575.8, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007606s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (616.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061740s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(91): len = 59999.2, overlap = 6.25
PHY-3002 : Step(92): len = 59141.9, overlap = 6.625
PHY-3002 : Step(93): len = 58681.9, overlap = 7.3125
PHY-3002 : Step(94): len = 57388.9, overlap = 7.75
PHY-3002 : Step(95): len = 56636.5, overlap = 6.5625
PHY-3002 : Step(96): len = 55562.5, overlap = 5.1875
PHY-3002 : Step(97): len = 54798.4, overlap = 5.0625
PHY-3002 : Step(98): len = 54116.6, overlap = 4.4375
PHY-3002 : Step(99): len = 52910.3, overlap = 5
PHY-3002 : Step(100): len = 52307.8, overlap = 6
PHY-3002 : Step(101): len = 51923.1, overlap = 6.125
PHY-3002 : Step(102): len = 51604.2, overlap = 6.9375
PHY-3002 : Step(103): len = 50576.3, overlap = 5.25
PHY-3002 : Step(104): len = 50397.4, overlap = 5
PHY-3002 : Step(105): len = 49874.7, overlap = 3.0625
PHY-3002 : Step(106): len = 48829.4, overlap = 3.1875
PHY-3002 : Step(107): len = 47773.4, overlap = 3.125
PHY-3002 : Step(108): len = 47114.6, overlap = 3.25
PHY-3002 : Step(109): len = 46372.1, overlap = 5.375
PHY-3002 : Step(110): len = 46107.8, overlap = 8.4375
PHY-3002 : Step(111): len = 45797.8, overlap = 8.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000303174
PHY-3002 : Step(112): len = 45734.9, overlap = 8.6875
PHY-3002 : Step(113): len = 45630, overlap = 12.1562
PHY-3002 : Step(114): len = 45544.7, overlap = 12.5312
PHY-3002 : Step(115): len = 45643.3, overlap = 13.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000606349
PHY-3002 : Step(116): len = 45513.1, overlap = 13.4375
PHY-3002 : Step(117): len = 45538.6, overlap = 13.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065868s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (118.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.2424e-05
PHY-3002 : Step(118): len = 45846.4, overlap = 62.9375
PHY-3002 : Step(119): len = 46457.8, overlap = 62.1562
PHY-3002 : Step(120): len = 46492.5, overlap = 60.7188
PHY-3002 : Step(121): len = 46464.2, overlap = 58.8438
PHY-3002 : Step(122): len = 46756.4, overlap = 58.75
PHY-3002 : Step(123): len = 46916.8, overlap = 57.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000104848
PHY-3002 : Step(124): len = 47215.6, overlap = 56.875
PHY-3002 : Step(125): len = 47708.3, overlap = 56.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000209696
PHY-3002 : Step(126): len = 47723.6, overlap = 55.7188
PHY-3002 : Step(127): len = 48846.2, overlap = 47.5
PHY-3002 : Step(128): len = 49641.8, overlap = 44.5625
PHY-3002 : Step(129): len = 49564.2, overlap = 43.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7739, tnet num: 2189, tinst num: 1619, tnode num: 10979, tedge num: 13084.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 101.47 peak overflow 2.97
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2191.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53936, over cnt = 268(0%), over = 1247, worst = 20
PHY-1001 : End global iterations;  0.073861s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (105.8%)

PHY-1001 : Congestion index: top1 = 46.59, top5 = 27.08, top10 = 16.82, top15 = 11.78.
PHY-1001 : End incremental global routing;  0.123797s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (113.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067332s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.219935s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (106.6%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/2191.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53936, over cnt = 268(0%), over = 1247, worst = 20
PHY-1002 : len = 61176, over cnt = 223(0%), over = 647, worst = 15
PHY-1002 : len = 67360, over cnt = 46(0%), over = 51, worst = 3
PHY-1002 : len = 68104, over cnt = 20(0%), over = 21, worst = 2
PHY-1002 : len = 68920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104855s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (104.3%)

PHY-1001 : Congestion index: top1 = 39.72, top5 = 26.67, top10 = 19.28, top15 = 14.05.
OPT-1001 : End congestion update;  0.149961s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (114.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064668s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.217101s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.0%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : End physical optimization;  0.716284s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (104.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 363 LUT to BLE ...
SYN-4008 : Packed 363 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 84 SEQ with LUT/SLICE
SYN-4006 : 119 single LUT's are left
SYN-4006 : 719 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1082/1414 primitive instances ...
PHY-3001 : End packing;  0.050966s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2024 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49415.6, Over = 71
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6515, tnet num: 2022, tinst num: 841, tnode num: 8855, tedge num: 11452.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.311561s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.8942e-05
PHY-3002 : Step(130): len = 48864.7, overlap = 74.75
PHY-3002 : Step(131): len = 48127.1, overlap = 78.25
PHY-3002 : Step(132): len = 48175.6, overlap = 79.5
PHY-3002 : Step(133): len = 47885.8, overlap = 78.5
PHY-3002 : Step(134): len = 47737.4, overlap = 79.25
PHY-3002 : Step(135): len = 47757.4, overlap = 77.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.78839e-05
PHY-3002 : Step(136): len = 48247.1, overlap = 78
PHY-3002 : Step(137): len = 49015.4, overlap = 76.75
PHY-3002 : Step(138): len = 49327.6, overlap = 75.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.57678e-05
PHY-3002 : Step(139): len = 50247.7, overlap = 73.25
PHY-3002 : Step(140): len = 51234.8, overlap = 67.5
PHY-3002 : Step(141): len = 51968.2, overlap = 64.75
PHY-3002 : Step(142): len = 52125.2, overlap = 61.75
PHY-3002 : Step(143): len = 52003, overlap = 60.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.079145s wall, 0.062500s user + 0.078125s system = 0.140625s CPU (177.7%)

PHY-3001 : Trial Legalized: Len = 65401.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050623s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00038505
PHY-3002 : Step(144): len = 62885.4, overlap = 6.25
PHY-3002 : Step(145): len = 60700.3, overlap = 12.75
PHY-3002 : Step(146): len = 58738.2, overlap = 23.25
PHY-3002 : Step(147): len = 57907.4, overlap = 27
PHY-3002 : Step(148): len = 57469.1, overlap = 27.5
PHY-3002 : Step(149): len = 57271.2, overlap = 30
PHY-3002 : Step(150): len = 57152, overlap = 30
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000770099
PHY-3002 : Step(151): len = 57666.3, overlap = 29.75
PHY-3002 : Step(152): len = 57974.3, overlap = 28
PHY-3002 : Step(153): len = 58070.8, overlap = 27.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0015402
PHY-3002 : Step(154): len = 58239.6, overlap = 28
PHY-3002 : Step(155): len = 58389.5, overlap = 27.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005049s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62304.9, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005504s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (283.9%)

PHY-3001 : 9 instances has been re-located, deltaX = 2, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 62352.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6515, tnet num: 2022, tinst num: 841, tnode num: 8855, tedge num: 11452.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 55/2024.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68480, over cnt = 158(0%), over = 244, worst = 7
PHY-1002 : len = 69592, over cnt = 81(0%), over = 105, worst = 5
PHY-1002 : len = 70608, over cnt = 15(0%), over = 17, worst = 3
PHY-1002 : len = 70840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.137063s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (125.4%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 22.59, top10 = 17.78, top15 = 14.02.
PHY-1001 : End incremental global routing;  0.191938s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (114.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059527s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.281317s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (111.1%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1791/2024.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005711s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 22.59, top10 = 17.78, top15 = 14.02.
OPT-1001 : End congestion update;  0.052646s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050116s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 803 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62384.6, Over = 0
PHY-3001 : End spreading;  0.004983s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62384.6, Over = 0
PHY-3001 : End incremental legalization;  0.033563s wall, 0.031250s user + 0.046875s system = 0.078125s CPU (232.8%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149030s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (136.3%)

OPT-1001 : Current memory(MB): used = 226, reserve = 189, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047357s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1784/2024.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70888, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70888, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.016036s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.4%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 22.59, top10 = 17.79, top15 = 14.03.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053190s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.892616s wall, 0.921875s user + 0.046875s system = 0.968750s CPU (108.5%)

RUN-1003 : finish command "place" in  5.448162s wall, 8.718750s user + 3.109375s system = 11.828125s CPU (217.1%)

RUN-1004 : used memory is 202 MB, reserved memory is 166 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2024 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6515, tnet num: 2022, tinst num: 841, tnode num: 8855, tedge num: 11452.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68152, over cnt = 150(0%), over = 236, worst = 7
PHY-1002 : len = 69256, over cnt = 75(0%), over = 99, worst = 5
PHY-1002 : len = 70400, over cnt = 4(0%), over = 5, worst = 2
PHY-1002 : len = 70496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.136924s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (159.8%)

PHY-1001 : Congestion index: top1 = 31.92, top5 = 22.41, top10 = 17.70, top15 = 13.93.
PHY-1001 : End global routing;  0.185050s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (143.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 206, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 468, peak = 500.
PHY-1001 : End build detailed router design. 3.222206s wall, 3.187500s user + 0.031250s system = 3.218750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34608, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.270156s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.275843s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180752, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End initial routed; 1.049460s wall, 1.843750s user + 0.109375s system = 1.953125s CPU (186.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1788(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.120   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.368922s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (88.9%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.418482s wall, 2.171875s user + 0.109375s system = 2.281250s CPU (160.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180752, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014147s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (110.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180552, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028773s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.028980s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (53.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1788(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.120   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.356667s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.170942s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End phase 3; 0.721588s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 180664
PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End export database. 0.011274s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.823923s wall, 7.546875s user + 0.140625s system = 7.687500s CPU (112.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6515, tnet num: 2022, tinst num: 841, tnode num: 8855, tedge num: 11452.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.725416s wall, 8.500000s user + 0.156250s system = 8.656250s CPU (112.0%)

RUN-1004 : used memory is 502 MB, reserved memory is 470 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      819   out of  19600    4.18%
#reg                     1074   out of  19600    5.48%
#le                      1538
  #lut only               464   out of   1538   30.17%
  #reg only               719   out of   1538   46.75%
  #lut&reg                355   out of   1538   23.08%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                               Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                                472
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                                108
#3        wendu/clk_us                    GCLK               mslice             signal_process/ctrl_signal/modulate_reg_syn_32.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                                        11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                                1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                                      1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1538   |594     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1136   |291     |133     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |41     |34      |7       |22      |0       |0       |
|    demodu                  |Demodulation                                     |529    |110     |57      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |0       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |15      |0       |30      |0       |0       |
|    integ                   |Integration                                      |139    |14      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |86     |31      |21      |82      |0       |1       |
|    rs422                   |Rs422Output                                      |318    |84      |29      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |23     |18      |5       |17      |0       |0       |
|  u_uart                    |UART_Control                                     |116    |102     |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |33     |26      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |24     |20      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |59     |56      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |205    |160     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1447  
    #2          2       307   
    #3          3       101   
    #4          4        21   
    #5        5-10       73   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6515, tnet num: 2022, tinst num: 841, tnode num: 8855, tedge num: 11452.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 841
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2024, pip num: 14557
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1273 valid insts, and 38607 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.023283s wall, 17.000000s user + 0.093750s system = 17.093750s CPU (565.4%)

RUN-1004 : used memory is 521 MB, reserved memory is 489 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230906_115454.log"
