`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/26/2022 
// Design Name: 	SPI
// Module Name:    	CtrlData 
// Project Name: 	8K21
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	SPI数据处理
// Revision 1.01 - File Created
// Additional Comments: 
// /*synthesis keep*/    
//////////////////////////////////////////////////////////////////////////////////
module CtrlData
(
    input 				clk,		//120Mhz
    input 				rst_n,
	input				valid,
	input				ARM_INT,
	input				finish,		//SPI传输结束信号
	input 		[31:0]	SPI_Data,	//从SPI IP读数据的端口
	output reg			start,		//开始启动SPI模块
	output reg			transmit/*synthesis keep*/,		
    output reg			cs_dacc,	//片选,低电平有效
	output reg	[3:0]	tx_state,	//数据处理状态机，为了好在SPI_MASTER模块处理多种位宽传输
    output reg 	[31:0] 	Data_SPI, 	//向SPI IP写数据的端口
	output reg	[15:0]	temp_data,   
	output reg	[23:0]	Macc_data/*synthesis keep*/
);

// wire	[35:0]	CONTROL0;
// wire	[79:0]	TRIG0;

wire			clk_1us;	
wire			clk_1ms;
reg				reg_us;
reg				reg_ms;
reg				start_tm;	//ms start single
reg		[9:0]	cnt_time;		//ms 计数器
reg				data_ready;
reg				rd_valid;	//Finish reading a frame of data
reg		[1:0]	rd_validdy;
reg		[2:0]	state_cnt;
reg		[3:0]	data_cnt; 
//reg		[23:0]	Macc_data;

reg		[7:0]	divisor;
reg		[27:0]	dividend;
wire	[27:0]	quotient;

reg		[1:0]	rd_valid_dy;
reg		[31:0]	ARM_200HZ_dy;
reg		[7:0]	data_cntdy;
reg		[27:0]	dacc_data_dy;
//TX state
localparam [3:0]  TX_idele   = 4'd0;
localparam [3:0]  TX_state1  = 4'd1;
localparam [3:0]  TX_state2  = 4'd2;
localparam [3:0]  TX_state3  = 4'd3;
localparam [3:0]  TX_state4  = 4'd4;
localparam [3:0]  TX_state5  = 4'd5;
localparam [3:0]  TX_state6  = 4'd6;
localparam [3:0]  TX_state7  = 4'd7;
localparam [3:0]  TX_state8  = 4'd8;
localparam [3:0]  TX_state9  = 4'd9;
localparam [3:0]  TX_state10 = 4'd10;
localparam [3:0]  TX_state11 = 4'd11;
localparam [3:0]  TX_state12 = 4'd12;
//SPI数据格式：R/W+Inc+6bit地址+8bit数据
//R/W 设置读或者写。 1：读数据 0：写数据
//Inc 设置寄存器内部指针是否自动增加。1：自动增加 0：不增加
localparam [15:0] SPI_CONFIG_2A_7F		= 16'h2A7F;//芯片上电后，首先给寄存器0x2A 写入 0x7F ，使芯片工作，然后才能读取数据。
localparam [23:0] SPI_CONFIG_F6_00_00	= 24'hF60000;//test
localparam [15:0] SPI_CONFIG_B6_00		= 16'hB600;//CHECK
localparam [15:0] SPI_CONFIG_B8_00		= 16'hB800;//CHECK
localparam [23:0] SPI_CONFIG_FA_00_00	= 24'hFA0000;
localparam [31:0] SPI_CONFIG_FC_00_00_00= 32'hFC000000;

Time_1ms usms
(
	.clk(clk),	//100Mhz
	.rst_n(rst_n),   
	.start_tm(start_tm),//   
	.clk_1ms(clk_1ms),//1ms
	.clk_1us(clk_1us)	//1us
);

always @(posedge clk or negedge rst_n)begin
    if(rst_n == 1'b0)begin
		start	 <= 1'b0;
		cs_dacc  <= 1'b1;
		rd_valid <= 1'b0;
		start_tm <= 1'b0;
        cnt_time <= 10'd0;
		data_cnt <= 4'd0;
        tx_state <= TX_idele;
		Data_SPI <= 32'd0;
    end
	else begin
		case(tx_state)
			TX_idele:begin
						start 	 <= 1'b0;
						cs_dacc  <= 1'b1;	
						rd_valid <= 1'b0;
						start_tm <= 1'b0;	
						cnt_time <= 10'd0;
						data_cnt <= 4'd0;
						tx_state <= TX_state1;
						Data_SPI <= 32'd0;	
					end
			TX_state1:begin //延迟10Ms
						start 	 <= 1'b0;
						cs_dacc  <= 1'b1;
						start_tm <= 1'b0;							
						if(~reg_ms&clk_1ms)begin//clk_1ms posedge
							if(cnt_time == 10'd9)begin	
								cnt_time <= 10'd0;
								tx_state <= TX_state2;
							end
							else begin	
								tx_state <= TX_state1;
								cnt_time <= cnt_time+1'b1;
							end
						end
						else begin	
							cnt_time <= cnt_time;
							tx_state <= tx_state;						
						end
					end
			TX_state2:begin //芯片上电后，首先写寄存器地址0x2A
						Data_SPI <= SPI_CONFIG_2A_7F;
						if(finish)begin
							cs_dacc  <= 1'b1;
							start 	 <= 1'b0;
							start_tm <= 1'b0;
							tx_state <= TX_state3;
						end
						else begin
							cs_dacc  <= 1'b0;
							start 	 <= 1'b1;
							start_tm <= 1'b1;
							tx_state <= TX_state2;	
						end
					end
			TX_state3:begin //芯片上电后，首先写寄存器地址0x2A
						Data_SPI <= SPI_CONFIG_2A_7F;
						if(finish)begin
							cs_dacc  <= 1'b1;
							start 	 <= 1'b0;
							start_tm <= 1'b0;
							tx_state <= TX_state4;
						end
						else begin
							cs_dacc  <= 1'b0;
							start 	 <= 1'b1;
							start_tm <= 1'b1;
							tx_state <= TX_state3;	
						end
					end				
			TX_state4:begin //延迟20Ms
						start 	 <= 1'b0;
						cs_dacc  <= 1'b1;
						start_tm <= 1'b0;							
						if(~reg_ms&clk_1ms)begin//clk_1ms posedge
							if(cnt_time == 10'd19)begin	
								cnt_time <= 10'd0;
								tx_state <= TX_state5;
							end
							else begin	
								tx_state <= TX_state3;
								cnt_time <= cnt_time+1'b1;
							end
						end
						else begin	
							cnt_time <= cnt_time;
							tx_state <= tx_state;						
						end
					end
			TX_state5:begin //地址：0X38,
						Data_SPI <= {16'd0,SPI_CONFIG_B8_00};
						if(finish)begin
							cs_dacc  <= 1'b1;
							start 	 <= 1'b0;
							start_tm <= 1'b0;
							tx_state <= TX_state6;
						end
						else begin
							cs_dacc  <= 1'b0;
							start 	 <= 1'b1;
							start_tm <= 1'b1;
							tx_state <= TX_state5;	
						end
					end 
			TX_state6:begin  //地址：0X3A,读取温度值
						Data_SPI <= {8'd0,SPI_CONFIG_FA_00_00};
						if(finish)begin
							cs_dacc  <= 1'b1;
							start 	 <= 1'b0;
							start_tm <= 1'b0;
							rd_valid <= 1'b0;
							tx_state <= TX_state7;
						end
						else begin
							cs_dacc  <= 1'b0;
							start 	 <= 1'b1;
							start_tm <= 1'b1;
							rd_valid <= 1'b0;
							tx_state <= TX_state6;	
						end
					end
			TX_state7:begin  //地址：0X3C，读取加速度计值
						Data_SPI <= SPI_CONFIG_FC_00_00_00;
						if(finish)begin
							cs_dacc  <= 1'b1;
							start 	 <= 1'b0;
							start_tm <= 1'b0;
							rd_valid <= 1'b1;
							tx_state <= TX_state6;
						end
						else begin
							cs_dacc  <= 1'b0;
							start 	 <= 1'b1;
							start_tm <= 1'b1;
							rd_valid <= 1'b0;
							tx_state <= TX_state7;	
						end
					end
			default: begin
						start 	 <= 1'b0;
						cs_dacc  <= 1'b1;
						rd_valid <= 1'b0;
						tx_state <= TX_idele;
						Data_SPI <= 32'd0;
					end
		endcase
	end 
end

//数据处理，在指定条件下，从总线上获取数据
always @(posedge clk or negedge rst_n) begin
	if(!rst_n)
		temp_data <= 16'd0;
	else if((tx_state==TX_state6)&&(valid==1'b1))
		temp_data <= {SPI_Data[7:0],SPI_Data[15:8]};
	else 
		temp_data <= temp_data; 
end

always @(posedge clk or negedge rst_n) begin
	if(!rst_n)
		Macc_data <= 24'd0;
	else if((tx_state==TX_state7)&&(valid==1'b1))
		Macc_data <= {SPI_Data[7:0],SPI_Data[15:8],SPI_Data[23:16]};
	else 
		Macc_data <= Macc_data; 
end

//ARM_200HZ:Delay two beats to generate rising edge signal
always @(posedge clk)begin
	ARM_200HZ_dy <= {ARM_200HZ_dy[30:0],ARM_INT};
end

//ARM_200HZ:Delay two beats to generate rising edge signal
always @(posedge clk)begin
	transmit <= (ARM_200HZ_dy[1:0]==2'b01)?1'b1:1'b0;
end

//rd_valid:Delay two beats to generate rising edge signal
always @(posedge clk)begin
	rd_valid_dy <= {rd_valid_dy[0],rd_valid};
end

//基于PPS的200hz时钟的上升沿到来时，HD6089开始累计次数
always @(posedge clk)begin
	if(rd_valid_dy == 2'b01)
		data_cntdy <= data_cntdy + 1'b1;
	else if(ARM_200HZ_dy[7:6] == 2'b01)
		data_cntdy <= 8'd0;
	else 
		data_cntdy <= data_cntdy;
end

//基于PPS的200hz时钟的上升沿到来时，dacc_data累加
always @(posedge clk)begin
	if(rd_valid_dy == 2'b01)
		dacc_data_dy <= dacc_data_dy + Macc_data;
	else if(ARM_200HZ_dy[7:6] == 2'b01)
		dacc_data_dy <= Macc_data;
	else 
		dacc_data_dy <= dacc_data_dy;
end

//保存加计累加次数
always @(posedge clk)begin
	if(ARM_200HZ_dy[1:0] == 2'b01)
		divisor <= data_cntdy;
	else 
		divisor <= divisor;
end

//保存加计累加值
always @(posedge clk)begin
	if(ARM_200HZ_dy[1:0] == 2'b01)
		dividend <= dacc_data_dy;
	else 
		dividend <= dividend;
end


/* div_gen div (
	.rfd(), 
	.clk(clk), 
	.dividend(dividend), 
	.quotient(quotient), 
	.divisor(divisor), 
	.fractional()
);
 */
//保存加计累加值
/* always @(posedge clk)begin
	if(ARM_200HZ_dy[31:30] == 2'b01)
		Macc_data <= quotient;
	else 
		Macc_data <= Macc_data;
end */

 // chipscope_icon ICON
 // (
	// .CONTROL0(CONTROL0)
 // );
 
 // chipscope_ila ILA  
 // (
	// .CLK(clk), 
	// .CONTROL(CONTROL0), 
	// .TRIG0(TRIG0)
// );
 
// assign TRIG0[15:0] 	= temp_data;
// assign TRIG0[39:16] = Macc_data;
// assign TRIG0[71:40] = SPI_Data;
// assign TRIG0[74:72] = tx_state;
// assign TRIG0[75] 	= valid;

endmodule

