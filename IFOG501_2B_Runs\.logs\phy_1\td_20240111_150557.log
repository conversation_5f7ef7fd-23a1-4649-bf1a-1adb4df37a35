============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jan 11 15:05:57 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1518 instances
RUN-0007 : 370 luts, 894 seqs, 130 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2059 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1542 nets have 2 pins
RUN-1001 : 405 nets have [3 - 5] pins
RUN-1001 : 68 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1516 instances, 370 luts, 894 seqs, 205 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7260, tnet num: 2057, tinst num: 1516, tnode num: 10192, tedge num: 12289.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.273841s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (97.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 548061
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1516.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 457714, overlap = 13.5
PHY-3002 : Step(2): len = 424086, overlap = 20.25
PHY-3002 : Step(3): len = 410127, overlap = 15.75
PHY-3002 : Step(4): len = 391491, overlap = 20.25
PHY-3002 : Step(5): len = 384088, overlap = 20.25
PHY-3002 : Step(6): len = 363209, overlap = 18
PHY-3002 : Step(7): len = 352337, overlap = 20.25
PHY-3002 : Step(8): len = 346106, overlap = 20.25
PHY-3002 : Step(9): len = 339227, overlap = 20.25
PHY-3002 : Step(10): len = 327401, overlap = 20.25
PHY-3002 : Step(11): len = 319480, overlap = 20.25
PHY-3002 : Step(12): len = 314347, overlap = 20.25
PHY-3002 : Step(13): len = 303030, overlap = 20.25
PHY-3002 : Step(14): len = 295914, overlap = 20.25
PHY-3002 : Step(15): len = 291007, overlap = 18
PHY-3002 : Step(16): len = 283628, overlap = 18
PHY-3002 : Step(17): len = 276350, overlap = 18
PHY-3002 : Step(18): len = 272290, overlap = 18
PHY-3002 : Step(19): len = 265166, overlap = 18
PHY-3002 : Step(20): len = 258927, overlap = 18
PHY-3002 : Step(21): len = 254804, overlap = 18
PHY-3002 : Step(22): len = 249734, overlap = 18
PHY-3002 : Step(23): len = 243971, overlap = 18
PHY-3002 : Step(24): len = 238276, overlap = 18
PHY-3002 : Step(25): len = 234261, overlap = 18
PHY-3002 : Step(26): len = 229420, overlap = 20.25
PHY-3002 : Step(27): len = 222783, overlap = 20.25
PHY-3002 : Step(28): len = 217030, overlap = 20.25
PHY-3002 : Step(29): len = 213909, overlap = 20.25
PHY-3002 : Step(30): len = 209259, overlap = 20.25
PHY-3002 : Step(31): len = 194374, overlap = 18
PHY-3002 : Step(32): len = 187664, overlap = 20.25
PHY-3002 : Step(33): len = 186031, overlap = 20.25
PHY-3002 : Step(34): len = 150545, overlap = 18
PHY-3002 : Step(35): len = 134375, overlap = 18
PHY-3002 : Step(36): len = 132915, overlap = 20.25
PHY-3002 : Step(37): len = 129489, overlap = 20.25
PHY-3002 : Step(38): len = 121024, overlap = 20.25
PHY-3002 : Step(39): len = 116166, overlap = 20.25
PHY-3002 : Step(40): len = 115052, overlap = 20.25
PHY-3002 : Step(41): len = 106937, overlap = 18
PHY-3002 : Step(42): len = 103015, overlap = 18
PHY-3002 : Step(43): len = 100324, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.22165e-05
PHY-3002 : Step(44): len = 100826, overlap = 15.75
PHY-3002 : Step(45): len = 100145, overlap = 15.75
PHY-3002 : Step(46): len = 99092.7, overlap = 15.75
PHY-3002 : Step(47): len = 98344.1, overlap = 13.5
PHY-3002 : Step(48): len = 97733.6, overlap = 13.5
PHY-3002 : Step(49): len = 94949, overlap = 13.5
PHY-3002 : Step(50): len = 93473.5, overlap = 13.5
PHY-3002 : Step(51): len = 92181.4, overlap = 13.5
PHY-3002 : Step(52): len = 90717.8, overlap = 11.25
PHY-3002 : Step(53): len = 88907.3, overlap = 13.5
PHY-3002 : Step(54): len = 86863.8, overlap = 13.5
PHY-3002 : Step(55): len = 85785.9, overlap = 15.75
PHY-3002 : Step(56): len = 83268.9, overlap = 18
PHY-3002 : Step(57): len = 81709.1, overlap = 13.5
PHY-3002 : Step(58): len = 80544.5, overlap = 13.5
PHY-3002 : Step(59): len = 79002.6, overlap = 11.25
PHY-3002 : Step(60): len = 77755.5, overlap = 9
PHY-3002 : Step(61): len = 75321.7, overlap = 11.6875
PHY-3002 : Step(62): len = 73866.9, overlap = 14.125
PHY-3002 : Step(63): len = 71752.8, overlap = 16.6875
PHY-3002 : Step(64): len = 70686.2, overlap = 16.8125
PHY-3002 : Step(65): len = 69301.7, overlap = 17.1875
PHY-3002 : Step(66): len = 67088.5, overlap = 15.1875
PHY-3002 : Step(67): len = 65767.3, overlap = 15.1875
PHY-3002 : Step(68): len = 65212.8, overlap = 15.375
PHY-3002 : Step(69): len = 62920.5, overlap = 13.375
PHY-3002 : Step(70): len = 62432, overlap = 14.0625
PHY-3002 : Step(71): len = 61219.4, overlap = 13.9375
PHY-3002 : Step(72): len = 60073.7, overlap = 18.4375
PHY-3002 : Step(73): len = 59907.1, overlap = 18.4375
PHY-3002 : Step(74): len = 59562.6, overlap = 16.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000184433
PHY-3002 : Step(75): len = 59662.1, overlap = 16.375
PHY-3002 : Step(76): len = 59632.8, overlap = 16.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000368866
PHY-3002 : Step(77): len = 59705.1, overlap = 16.375
PHY-3002 : Step(78): len = 59699.2, overlap = 14.125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007303s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058045s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00761557
PHY-3002 : Step(79): len = 63803.2, overlap = 14.3125
PHY-3002 : Step(80): len = 62910.4, overlap = 13
PHY-3002 : Step(81): len = 61881.8, overlap = 12.3438
PHY-3002 : Step(82): len = 60399.6, overlap = 12.5938
PHY-3002 : Step(83): len = 59642.2, overlap = 10.6562
PHY-3002 : Step(84): len = 58597.9, overlap = 11.9062
PHY-3002 : Step(85): len = 57615.4, overlap = 11.5312
PHY-3002 : Step(86): len = 55731.2, overlap = 13.5
PHY-3002 : Step(87): len = 54404.6, overlap = 13.5
PHY-3002 : Step(88): len = 53448.2, overlap = 14.8438
PHY-3002 : Step(89): len = 52544.8, overlap = 14.5625
PHY-3002 : Step(90): len = 52052.9, overlap = 14.3125
PHY-3002 : Step(91): len = 51373.2, overlap = 12.1875
PHY-3002 : Step(92): len = 51079.6, overlap = 14
PHY-3002 : Step(93): len = 50029.4, overlap = 14.125
PHY-3002 : Step(94): len = 49676.5, overlap = 14
PHY-3002 : Step(95): len = 49378.8, overlap = 14.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057333s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000104628
PHY-3002 : Step(96): len = 49278.9, overlap = 46.375
PHY-3002 : Step(97): len = 49388.8, overlap = 47.5938
PHY-3002 : Step(98): len = 50985.7, overlap = 44.0938
PHY-3002 : Step(99): len = 51571, overlap = 42.6562
PHY-3002 : Step(100): len = 50932.6, overlap = 42.9062
PHY-3002 : Step(101): len = 50705.8, overlap = 42.7812
PHY-3002 : Step(102): len = 50411.7, overlap = 42.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000209257
PHY-3002 : Step(103): len = 50809.2, overlap = 38
PHY-3002 : Step(104): len = 51592.6, overlap = 32.9062
PHY-3002 : Step(105): len = 51872.5, overlap = 32.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000418513
PHY-3002 : Step(106): len = 52243.4, overlap = 35.5938
PHY-3002 : Step(107): len = 53014.7, overlap = 33.8438
PHY-3002 : Step(108): len = 54069.9, overlap = 29.5312
PHY-3002 : Step(109): len = 53396.4, overlap = 29.25
PHY-3002 : Step(110): len = 52984.8, overlap = 29.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7260, tnet num: 2057, tinst num: 1516, tnode num: 10192, tedge num: 12289.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.25 peak overflow 2.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2059.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56048, over cnt = 244(0%), over = 1031, worst = 17
PHY-1001 : End global iterations;  0.080628s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (135.7%)

PHY-1001 : Congestion index: top1 = 45.06, top5 = 26.40, top10 = 16.36, top15 = 11.54.
PHY-1001 : End incremental global routing;  0.133441s wall, 0.109375s user + 0.062500s system = 0.171875s CPU (128.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065292s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.227900s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (116.6%)

OPT-1001 : Current memory(MB): used = 210, reserve = 173, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1566/2059.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56048, over cnt = 244(0%), over = 1031, worst = 17
PHY-1002 : len = 63544, over cnt = 126(0%), over = 272, worst = 15
PHY-1002 : len = 66528, over cnt = 34(0%), over = 51, worst = 5
PHY-1002 : len = 67184, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 67328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.099074s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (126.2%)

PHY-1001 : Congestion index: top1 = 38.21, top5 = 25.68, top10 = 18.06, top15 = 13.26.
OPT-1001 : End congestion update;  0.143030s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (109.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2057 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055060s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.201237s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 213, reserve = 176, peak = 213.
OPT-1001 : End physical optimization;  0.695914s wall, 0.640625s user + 0.109375s system = 0.750000s CPU (107.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 723 remaining SEQ's ...
SYN-4005 : Packed 107 SEQ with LUT/SLICE
SYN-4006 : 113 single LUT's are left
SYN-4006 : 616 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 986/1274 primitive instances ...
PHY-3001 : End packing;  0.045646s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 775 instances
RUN-1001 : 363 mslices, 363 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1896 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1375 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 773 instances, 726 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52887.4, Over = 52.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6145, tnet num: 1894, tinst num: 773, tnode num: 8280, tedge num: 10804.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1894 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.294519s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.99884e-05
PHY-3002 : Step(111): len = 52199.1, overlap = 55.25
PHY-3002 : Step(112): len = 51252.7, overlap = 58.75
PHY-3002 : Step(113): len = 50737.1, overlap = 57.5
PHY-3002 : Step(114): len = 50738.1, overlap = 57
PHY-3002 : Step(115): len = 50639.5, overlap = 55.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.99768e-05
PHY-3002 : Step(116): len = 50975.6, overlap = 53.25
PHY-3002 : Step(117): len = 51364.3, overlap = 54
PHY-3002 : Step(118): len = 52075.3, overlap = 56.25
PHY-3002 : Step(119): len = 52545.7, overlap = 57.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000119954
PHY-3002 : Step(120): len = 52801.3, overlap = 55
PHY-3002 : Step(121): len = 53105.9, overlap = 53.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.063071s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (272.5%)

PHY-3001 : Trial Legalized: Len = 67395.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1894 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048045s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000591627
PHY-3002 : Step(122): len = 63731.7, overlap = 5.75
PHY-3002 : Step(123): len = 61263.5, overlap = 12.75
PHY-3002 : Step(124): len = 59209.3, overlap = 16.75
PHY-3002 : Step(125): len = 58295.2, overlap = 18
PHY-3002 : Step(126): len = 57683.4, overlap = 20.25
PHY-3002 : Step(127): len = 57293.4, overlap = 20
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00118325
PHY-3002 : Step(128): len = 57615.7, overlap = 21.5
PHY-3002 : Step(129): len = 57744.4, overlap = 21.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00236651
PHY-3002 : Step(130): len = 57854.2, overlap = 20.5
PHY-3002 : Step(131): len = 57911.9, overlap = 21
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005031s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62468.6, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005557s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (281.2%)

PHY-3001 : 14 instances has been re-located, deltaX = 2, deltaY = 12, maxDist = 1.
PHY-3001 : Final: Len = 62582.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6145, tnet num: 1894, tinst num: 773, tnode num: 8280, tedge num: 10804.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 53/1896.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68720, over cnt = 143(0%), over = 218, worst = 7
PHY-1002 : len = 69528, over cnt = 93(0%), over = 125, worst = 4
PHY-1002 : len = 70816, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 70848, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70880, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130985s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (107.4%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.63, top10 = 17.87, top15 = 13.86.
PHY-1001 : End incremental global routing;  0.181027s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1894 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056703s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.267475s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (105.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1674/1896.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70880, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007187s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.63, top10 = 17.87, top15 = 13.86.
OPT-1001 : End congestion update;  0.054408s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1894 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054443s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.110760s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.7%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1894 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054742s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1674/1896.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70880, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007473s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.63, top10 = 17.87, top15 = 13.86.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1894 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055682s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.137931
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.822210s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (102.6%)

RUN-1003 : finish command "place" in  4.948881s wall, 7.031250s user + 3.062500s system = 10.093750s CPU (204.0%)

RUN-1004 : used memory is 214 MB, reserved memory is 178 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 775 instances
RUN-1001 : 363 mslices, 363 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1896 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1375 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6145, tnet num: 1894, tinst num: 773, tnode num: 8280, tedge num: 10804.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 363 mslices, 363 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1894 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68168, over cnt = 151(0%), over = 226, worst = 7
PHY-1002 : len = 69104, over cnt = 85(0%), over = 108, worst = 3
PHY-1002 : len = 70272, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.142246s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (120.8%)

PHY-1001 : Congestion index: top1 = 31.59, top5 = 22.59, top10 = 17.81, top15 = 13.80.
PHY-1001 : End global routing;  0.190226s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (115.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 198, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 495, reserve = 463, peak = 495.
PHY-1001 : End build detailed router design. 3.270364s wall, 3.171875s user + 0.093750s system = 3.265625s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32360, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.625857s wall, 1.609375s user + 0.031250s system = 1.640625s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 527, reserve = 496, peak = 527.
PHY-1001 : End phase 1; 1.634022s wall, 1.609375s user + 0.031250s system = 1.640625s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184288, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 498, peak = 530.
PHY-1001 : End initial routed; 1.537231s wall, 2.578125s user + 0.125000s system = 2.703125s CPU (175.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1677(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.311   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.400   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363613s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.1%)

PHY-1001 : Current memory(MB): used = 531, reserve = 499, peak = 531.
PHY-1001 : End phase 2; 1.900955s wall, 2.953125s user + 0.125000s system = 3.078125s CPU (161.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184288, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016660s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (93.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184280, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031759s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (98.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184280, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.026601s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (58.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 184224, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.022366s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (69.9%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 184272, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.023486s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (133.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1677(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.311   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.400   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.398966s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (101.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.165147s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.6%)

PHY-1001 : Current memory(MB): used = 546, reserve = 514, peak = 546.
PHY-1001 : End phase 3; 0.818408s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.3%)

PHY-1003 : Routed, final wirelength = 184272
PHY-1001 : Current memory(MB): used = 546, reserve = 514, peak = 546.
PHY-1001 : End export database. 0.010254s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (152.4%)

PHY-1001 : End detail routing;  7.811997s wall, 8.734375s user + 0.250000s system = 8.984375s CPU (115.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6145, tnet num: 1894, tinst num: 773, tnode num: 8280, tedge num: 10804.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[3] slack -88ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6175, tnet num: 1909, tinst num: 788, tnode num: 8310, tedge num: 10834.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.021152s wall, 3.109375s user + 0.187500s system = 3.296875s CPU (109.1%)

RUN-1003 : finish command "route" in  11.356132s wall, 12.375000s user + 0.468750s system = 12.843750s CPU (113.1%)

RUN-1004 : used memory is 521 MB, reserved memory is 490 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      814   out of  19600    4.15%
#reg                      963   out of  19600    4.91%
#le                      1430
  #lut only               467   out of   1430   32.66%
  #reg only               616   out of   1430   43.08%
  #lut&reg                347   out of   1430   24.27%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         426
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1430   |609     |205     |994     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1028   |297     |115     |808     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |45     |36      |9       |24      |0       |0       |
|    demodu                  |Demodulation                                     |454    |111     |44      |345     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |29      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |9       |0       |17      |0       |0       |
|    integ                   |Integration                                      |138    |22      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |65     |30      |14      |61      |0       |1       |
|    rs422                   |Rs422Output                                      |302    |79      |29      |249     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |19      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |117    |110     |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |33     |26      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |22     |22      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |62     |62      |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1354  
    #2          2       270   
    #3          3       122   
    #4          4        16   
    #5        5-10       77   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6175, tnet num: 1909, tinst num: 788, tnode num: 8310, tedge num: 10834.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1909 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 788
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1911, pip num: 14313
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1305 valid insts, and 38002 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.152526s wall, 17.687500s user + 0.046875s system = 17.734375s CPU (562.5%)

RUN-1004 : used memory is 519 MB, reserved memory is 489 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240111_150557.log"
