============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 14:52:31 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/DA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0111110101101110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3196/12 useful/useless nets, 1966/4 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 2965/16 useful/useless nets, 2301/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 289 better
SYN-1014 : Optimize round 2
SYN-1032 : 2748/30 useful/useless nets, 2084/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2772/157 useful/useless nets, 2131/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 26 instances.
SYN-2501 : Optimize round 1, 54 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 3153/5 useful/useless nets, 2512/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11480, tnet num: 3153, tinst num: 2511, tnode num: 15395, tedge num: 18487.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3153 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 180 (3.53), #lev = 8 (1.78)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 177 (3.57), #lev = 7 (1.90)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 397 instances into 177 LUTs, name keeping = 76%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 298 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.853084s wall, 1.734375s user + 0.125000s system = 1.859375s CPU (100.3%)

RUN-1004 : used memory is 170 MB, reserved memory is 128 MB, peak memory is 202 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (185 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2118 instances
RUN-0007 : 551 luts, 1204 seqs, 179 mslices, 110 lslices, 34 pads, 28 brams, 5 dsps
RUN-1001 : There are total 2761 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1902 nets have 2 pins
RUN-1001 : 680 nets have [3 - 5] pins
RUN-1001 : 108 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 38 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     269     
RUN-1001 :   No   |  No   |  Yes  |     225     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     310     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  17   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 27
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2116 instances, 551 luts, 1204 seqs, 289 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10637, tnet num: 2759, tinst num: 2116, tnode num: 14764, tedge num: 18021.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.438007s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 751554
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2116.
PHY-3001 : End clustering;  0.000033s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 651318, overlap = 72
PHY-3002 : Step(2): len = 619460, overlap = 67.5
PHY-3002 : Step(3): len = 584994, overlap = 72
PHY-3002 : Step(4): len = 563327, overlap = 72
PHY-3002 : Step(5): len = 545167, overlap = 72
PHY-3002 : Step(6): len = 497194, overlap = 72
PHY-3002 : Step(7): len = 447378, overlap = 69.75
PHY-3002 : Step(8): len = 436330, overlap = 67.5
PHY-3002 : Step(9): len = 427303, overlap = 69.75
PHY-3002 : Step(10): len = 413923, overlap = 72
PHY-3002 : Step(11): len = 405365, overlap = 72
PHY-3002 : Step(12): len = 399671, overlap = 69.75
PHY-3002 : Step(13): len = 388907, overlap = 69.75
PHY-3002 : Step(14): len = 380542, overlap = 69.75
PHY-3002 : Step(15): len = 372840, overlap = 69.75
PHY-3002 : Step(16): len = 367519, overlap = 72
PHY-3002 : Step(17): len = 338428, overlap = 69.75
PHY-3002 : Step(18): len = 313076, overlap = 69.75
PHY-3002 : Step(19): len = 307638, overlap = 69.75
PHY-3002 : Step(20): len = 304426, overlap = 69.75
PHY-3002 : Step(21): len = 276883, overlap = 72
PHY-3002 : Step(22): len = 262939, overlap = 72
PHY-3002 : Step(23): len = 260260, overlap = 72
PHY-3002 : Step(24): len = 252717, overlap = 72
PHY-3002 : Step(25): len = 241729, overlap = 69.75
PHY-3002 : Step(26): len = 237277, overlap = 69.75
PHY-3002 : Step(27): len = 233617, overlap = 69.75
PHY-3002 : Step(28): len = 224116, overlap = 69.75
PHY-3002 : Step(29): len = 219326, overlap = 69.75
PHY-3002 : Step(30): len = 215712, overlap = 69.75
PHY-3002 : Step(31): len = 211058, overlap = 69.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.14976e-05
PHY-3002 : Step(32): len = 213000, overlap = 74.25
PHY-3002 : Step(33): len = 209395, overlap = 67.5
PHY-3002 : Step(34): len = 206321, overlap = 56.25
PHY-3002 : Step(35): len = 206096, overlap = 56.25
PHY-3002 : Step(36): len = 201838, overlap = 60.75
PHY-3002 : Step(37): len = 195768, overlap = 60.75
PHY-3002 : Step(38): len = 195389, overlap = 60.75
PHY-3002 : Step(39): len = 193413, overlap = 54
PHY-3002 : Step(40): len = 190621, overlap = 51.75
PHY-3002 : Step(41): len = 187226, overlap = 51.75
PHY-3002 : Step(42): len = 184397, overlap = 51.75
PHY-3002 : Step(43): len = 181833, overlap = 51.75
PHY-3002 : Step(44): len = 176303, overlap = 51.75
PHY-3002 : Step(45): len = 169682, overlap = 54
PHY-3002 : Step(46): len = 166931, overlap = 56.25
PHY-3002 : Step(47): len = 165185, overlap = 54
PHY-3002 : Step(48): len = 159436, overlap = 51.75
PHY-3002 : Step(49): len = 149478, overlap = 54
PHY-3002 : Step(50): len = 147425, overlap = 51.75
PHY-3002 : Step(51): len = 145134, overlap = 51.75
PHY-3002 : Step(52): len = 143694, overlap = 51.75
PHY-3002 : Step(53): len = 142191, overlap = 56.25
PHY-3002 : Step(54): len = 137436, overlap = 56.25
PHY-3002 : Step(55): len = 133294, overlap = 54
PHY-3002 : Step(56): len = 131565, overlap = 54
PHY-3002 : Step(57): len = 130502, overlap = 51.75
PHY-3002 : Step(58): len = 127970, overlap = 51.875
PHY-3002 : Step(59): len = 126901, overlap = 52.0625
PHY-3002 : Step(60): len = 117839, overlap = 54.9375
PHY-3002 : Step(61): len = 114234, overlap = 55.1875
PHY-3002 : Step(62): len = 113699, overlap = 55.6562
PHY-3002 : Step(63): len = 112610, overlap = 54.9062
PHY-3002 : Step(64): len = 111826, overlap = 54.9062
PHY-3002 : Step(65): len = 111162, overlap = 52.5312
PHY-3002 : Step(66): len = 110697, overlap = 52.6562
PHY-3002 : Step(67): len = 108727, overlap = 52.7812
PHY-3002 : Step(68): len = 106808, overlap = 50.5938
PHY-3002 : Step(69): len = 104241, overlap = 57.4062
PHY-3002 : Step(70): len = 102462, overlap = 57.375
PHY-3002 : Step(71): len = 101143, overlap = 59.6562
PHY-3002 : Step(72): len = 99463.5, overlap = 55.2188
PHY-3002 : Step(73): len = 97077.3, overlap = 53.1875
PHY-3002 : Step(74): len = 95607.8, overlap = 55.7812
PHY-3002 : Step(75): len = 94503.1, overlap = 49.25
PHY-3002 : Step(76): len = 92940.2, overlap = 50.5312
PHY-3002 : Step(77): len = 89508.6, overlap = 53
PHY-3002 : Step(78): len = 88495.8, overlap = 48.5
PHY-3002 : Step(79): len = 87821.6, overlap = 55
PHY-3002 : Step(80): len = 87466.1, overlap = 52.75
PHY-3002 : Step(81): len = 87319.6, overlap = 45.75
PHY-3002 : Step(82): len = 86449.8, overlap = 45.5625
PHY-3002 : Step(83): len = 85859.6, overlap = 50.0625
PHY-3002 : Step(84): len = 84866.7, overlap = 45.5625
PHY-3002 : Step(85): len = 83822.4, overlap = 47.8125
PHY-3002 : Step(86): len = 82951.4, overlap = 50.125
PHY-3002 : Step(87): len = 81755, overlap = 45.5
PHY-3002 : Step(88): len = 80336.6, overlap = 45
PHY-3002 : Step(89): len = 79400.4, overlap = 49.5
PHY-3002 : Step(90): len = 78995.7, overlap = 51.75
PHY-3002 : Step(91): len = 78501.9, overlap = 51.75
PHY-3002 : Step(92): len = 77796.8, overlap = 54.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000102995
PHY-3002 : Step(93): len = 77955.3, overlap = 49.75
PHY-3002 : Step(94): len = 78138.7, overlap = 49.75
PHY-3002 : Step(95): len = 78042.3, overlap = 52
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000205991
PHY-3002 : Step(96): len = 78147.7, overlap = 54.25
PHY-3002 : Step(97): len = 78176.5, overlap = 54.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.016228s wall, 0.000000s user + 0.046875s system = 0.046875s CPU (288.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.110655s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000355736
PHY-3002 : Step(98): len = 88305.6, overlap = 17.2812
PHY-3002 : Step(99): len = 88414.5, overlap = 17.3438
PHY-3002 : Step(100): len = 87224.4, overlap = 15.6562
PHY-3002 : Step(101): len = 87541.3, overlap = 18
PHY-3002 : Step(102): len = 87644.8, overlap = 18.1875
PHY-3002 : Step(103): len = 86412.6, overlap = 19.1875
PHY-3002 : Step(104): len = 86308.1, overlap = 18.7188
PHY-3002 : Step(105): len = 84355.5, overlap = 18.625
PHY-3002 : Step(106): len = 82788.3, overlap = 17.5938
PHY-3002 : Step(107): len = 81691.9, overlap = 17.4375
PHY-3002 : Step(108): len = 79428.3, overlap = 17.0625
PHY-3002 : Step(109): len = 78545.7, overlap = 18.1875
PHY-3002 : Step(110): len = 77779.3, overlap = 19.9062
PHY-3002 : Step(111): len = 76163.2, overlap = 26.125
PHY-3002 : Step(112): len = 74325.5, overlap = 24.5312
PHY-3002 : Step(113): len = 73824.1, overlap = 24.0625
PHY-3002 : Step(114): len = 73196.8, overlap = 24.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000711473
PHY-3002 : Step(115): len = 73177.2, overlap = 24.25
PHY-3002 : Step(116): len = 73025.7, overlap = 24.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00142295
PHY-3002 : Step(117): len = 72829.8, overlap = 24.7188
PHY-3002 : Step(118): len = 72844.6, overlap = 24.6562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.117077s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (106.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.42846e-05
PHY-3002 : Step(119): len = 73264.6, overlap = 68.3125
PHY-3002 : Step(120): len = 74715.3, overlap = 57.7812
PHY-3002 : Step(121): len = 74719.9, overlap = 60
PHY-3002 : Step(122): len = 74022.9, overlap = 58.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.85691e-05
PHY-3002 : Step(123): len = 74188.3, overlap = 56.6562
PHY-3002 : Step(124): len = 74643.8, overlap = 55.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000177138
PHY-3002 : Step(125): len = 74441.1, overlap = 52.1562
PHY-3002 : Step(126): len = 75371, overlap = 51.1562
PHY-3002 : Step(127): len = 77379.5, overlap = 45.9375
PHY-3002 : Step(128): len = 77818.1, overlap = 44.0625
PHY-3002 : Step(129): len = 77887.7, overlap = 43.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000354276
PHY-3002 : Step(130): len = 77617.5, overlap = 43.2812
PHY-3002 : Step(131): len = 77372.4, overlap = 40.5312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000708553
PHY-3002 : Step(132): len = 77709.9, overlap = 42.2812
PHY-3002 : Step(133): len = 78236, overlap = 41.6875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00141711
PHY-3002 : Step(134): len = 78810.6, overlap = 40.7188
PHY-3002 : Step(135): len = 78682.2, overlap = 41.8438
PHY-3002 : Step(136): len = 78438, overlap = 41.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10637, tnet num: 2759, tinst num: 2116, tnode num: 14764, tedge num: 18021.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 112.34 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2761.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 88104, over cnt = 339(0%), over = 1393, worst = 23
PHY-1001 : End global iterations;  0.173589s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (108.0%)

PHY-1001 : Congestion index: top1 = 49.91, top5 = 30.18, top10 = 21.41, top15 = 16.25.
PHY-1001 : End incremental global routing;  0.248612s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (106.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.126841s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.435564s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (104.0%)

OPT-1001 : Current memory(MB): used = 238, reserve = 196, peak = 238.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2138/2761.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 88104, over cnt = 339(0%), over = 1393, worst = 23
PHY-1002 : len = 100016, over cnt = 231(0%), over = 485, worst = 16
PHY-1002 : len = 103944, over cnt = 74(0%), over = 110, worst = 14
PHY-1002 : len = 105544, over cnt = 12(0%), over = 16, worst = 4
PHY-1002 : len = 105792, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.229280s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (136.3%)

PHY-1001 : Congestion index: top1 = 42.18, top5 = 29.01, top10 = 22.72, top15 = 18.27.
OPT-1001 : End congestion update;  0.295682s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (126.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2759 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.092980s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (84.0%)

OPT-0007 : Start: WNS -2897 TNS -54924 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2897 TNS -54924 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.393299s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (119.2%)

OPT-1001 : Current memory(MB): used = 241, reserve = 199, peak = 241.
OPT-1001 : End physical optimization;  1.256726s wall, 1.421875s user + 0.015625s system = 1.437500s CPU (114.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 551 LUT to BLE ...
SYN-4008 : Packed 551 LUT and 230 SEQ to BLE.
SYN-4003 : Packing 974 remaining SEQ's ...
SYN-4005 : Packed 224 SEQ with LUT/SLICE
SYN-4006 : 134 single LUT's are left
SYN-4006 : 750 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1301/1698 primitive instances ...
PHY-3001 : End packing;  0.082816s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (94.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1058 instances
RUN-1001 : 492 mslices, 492 lslices, 34 pads, 28 brams, 5 dsps
RUN-1001 : There are total 2540 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1682 nets have 2 pins
RUN-1001 : 678 nets have [3 - 5] pins
RUN-1001 : 111 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
PHY-3001 : design contains 1056 instances, 984 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : After packing: Len = 79554.8, Over = 65.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9074, tnet num: 2538, tinst num: 1056, tnode num: 12057, tedge num: 15894.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2538 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.510297s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.59654e-05
PHY-3002 : Step(137): len = 78606.5, overlap = 65.75
PHY-3002 : Step(138): len = 77922.3, overlap = 63.75
PHY-3002 : Step(139): len = 77612.5, overlap = 67
PHY-3002 : Step(140): len = 77276.3, overlap = 64.75
PHY-3002 : Step(141): len = 77410.4, overlap = 65.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.19308e-05
PHY-3002 : Step(142): len = 77817.6, overlap = 64.25
PHY-3002 : Step(143): len = 78174.5, overlap = 66.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000143862
PHY-3002 : Step(144): len = 79445.4, overlap = 62.5
PHY-3002 : Step(145): len = 79798.9, overlap = 63
PHY-3002 : Step(146): len = 80965, overlap = 60.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.181992s wall, 0.125000s user + 0.218750s system = 0.343750s CPU (188.9%)

PHY-3001 : Trial Legalized: Len = 99239.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2538 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.100912s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00136297
PHY-3002 : Step(147): len = 94744.2, overlap = 7
PHY-3002 : Step(148): len = 91940.5, overlap = 9.75
PHY-3002 : Step(149): len = 90091.4, overlap = 12.75
PHY-3002 : Step(150): len = 88301.4, overlap = 16.5
PHY-3002 : Step(151): len = 87537, overlap = 19.75
PHY-3002 : Step(152): len = 87204, overlap = 19.5
PHY-3002 : Step(153): len = 86827.9, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00272594
PHY-3002 : Step(154): len = 86841.5, overlap = 20
PHY-3002 : Step(155): len = 86788.1, overlap = 21.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00545188
PHY-3002 : Step(156): len = 86873, overlap = 20.75
PHY-3002 : Step(157): len = 86841, overlap = 20.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008249s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 92688.7, Over = 0
PHY-3001 : Spreading special nets. 28 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.014532s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 40 instances has been re-located, deltaX = 15, deltaY = 32, maxDist = 2.
PHY-3001 : Final: Len = 93650.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9074, tnet num: 2538, tinst num: 1056, tnode num: 12057, tedge num: 15894.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 132/2540.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 108480, over cnt = 224(0%), over = 353, worst = 5
PHY-1002 : len = 110040, over cnt = 92(0%), over = 121, worst = 4
PHY-1002 : len = 111200, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 111280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.265258s wall, 0.312500s user + 0.078125s system = 0.390625s CPU (147.3%)

PHY-1001 : Congestion index: top1 = 33.86, top5 = 25.46, top10 = 21.06, top15 = 18.03.
PHY-1001 : End incremental global routing;  0.351112s wall, 0.390625s user + 0.078125s system = 0.468750s CPU (133.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2538 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.114141s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (95.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.525663s wall, 0.562500s user + 0.078125s system = 0.640625s CPU (121.9%)

OPT-1001 : Current memory(MB): used = 245, reserve = 202, peak = 245.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2205/2540.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 111280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.011569s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (135.1%)

PHY-1001 : Congestion index: top1 = 33.86, top5 = 25.46, top10 = 21.06, top15 = 18.03.
OPT-1001 : End congestion update;  0.083416s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (112.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2538 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.090971s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (85.9%)

OPT-0007 : Start: WNS -2897 TNS -55474 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2897 TNS -55474 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.177538s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (105.6%)

OPT-1001 : Current memory(MB): used = 246, reserve = 204, peak = 246.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2538 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.082969s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (94.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2205/2540.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 111280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010288s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (151.9%)

PHY-1001 : Congestion index: top1 = 33.86, top5 = 25.46, top10 = 21.06, top15 = 18.03.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2538 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.084476s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -2897 TNS -55474 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.448276
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -2897ps with logic level 2 
RUN-1001 :       #2 path slack -2897ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2540 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2540 nets
OPT-1001 : End physical optimization;  1.464630s wall, 1.546875s user + 0.093750s system = 1.640625s CPU (112.0%)

RUN-1003 : finish command "place" in  10.016516s wall, 15.546875s user + 5.078125s system = 20.625000s CPU (205.9%)

RUN-1004 : used memory is 233 MB, reserved memory is 190 MB, peak memory is 247 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1058 instances
RUN-1001 : 492 mslices, 492 lslices, 34 pads, 28 brams, 5 dsps
RUN-1001 : There are total 2540 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1682 nets have 2 pins
RUN-1001 : 678 nets have [3 - 5] pins
RUN-1001 : 111 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9074, tnet num: 2538, tinst num: 1056, tnode num: 12057, tedge num: 15894.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 492 mslices, 492 lslices, 34 pads, 28 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2538 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 106680, over cnt = 227(0%), over = 363, worst = 6
PHY-1002 : len = 108448, over cnt = 97(0%), over = 130, worst = 4
PHY-1002 : len = 110112, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 110192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.258716s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (114.7%)

PHY-1001 : Congestion index: top1 = 33.25, top5 = 25.23, top10 = 20.87, top15 = 17.83.
PHY-1001 : End global routing;  0.328617s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (114.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 263, reserve = 222, peak = 274.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 524, reserve = 486, peak = 524.
PHY-1001 : End build detailed router design. 4.571951s wall, 4.453125s user + 0.109375s system = 4.562500s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 41168, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.351879s wall, 2.343750s user + 0.000000s system = 2.343750s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 557, reserve = 520, peak = 557.
PHY-1001 : End phase 1; 2.358402s wall, 2.343750s user + 0.000000s system = 2.343750s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 325504, over cnt = 54(0%), over = 54, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 557, reserve = 520, peak = 558.
PHY-1001 : End initial routed; 4.186225s wall, 5.843750s user + 0.156250s system = 6.000000s CPU (143.3%)

PHY-1001 : Update timing.....
PHY-1001 : 3/2250(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.029   |  -0.029   |   1   
RUN-1001 :   Hold   |  -1.417   |  -22.783  |  40   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.551496s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (102.0%)

PHY-1001 : Current memory(MB): used = 559, reserve = 521, peak = 559.
PHY-1001 : End phase 2; 4.737857s wall, 6.406250s user + 0.156250s system = 6.562500s CPU (138.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS 0.158ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.016413s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (95.2%)

PHY-1022 : len = 325504, over cnt = 55(0%), over = 55, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.040311s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 325000, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.130003s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 325032, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.043225s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (108.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 325016, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.035721s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2250(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.158   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.417   |  -22.783  |  40   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.561679s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.324794s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 576, reserve = 539, peak = 576.
PHY-1001 : End phase 3; 1.304941s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.4%)

PHY-1003 : Routed, final wirelength = 325016
PHY-1001 : Current memory(MB): used = 576, reserve = 539, peak = 576.
PHY-1001 : End export database. 0.012676s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (123.3%)

PHY-1001 : End detail routing;  13.236141s wall, 14.750000s user + 0.281250s system = 15.031250s CPU (113.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9074, tnet num: 2538, tinst num: 1056, tnode num: 12057, tedge num: 15894.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  14.732350s wall, 16.281250s user + 0.296875s system = 16.578125s CPU (112.5%)

RUN-1004 : used memory is 540 MB, reserved memory is 507 MB, peak memory is 576 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1137   out of  19600    5.80%
#reg                     1259   out of  19600    6.42%
#le                      1887
  #lut only               628   out of   1887   33.28%
  #reg only               750   out of   1887   39.75%
  #lut&reg                509   out of   1887   26.97%
#dsp                        5   out of     29   17.24%
#bram                      28   out of     64   43.75%
  #bram9k                  28
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                  Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                   438
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                   102
#3        config_inst_syn_9               GCLK               config             config_inst.jtck                        101
#4        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di                         81
#5        wendu/clk_us                    GCLK               lslice             signal_process/demodu/reg1_syn_89.q0    39
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                           11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                   1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1887   |848     |289     |1290    |28      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1004   |264     |105     |805     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |33     |27      |6       |24      |0       |0       |
|    demodu                          |Demodulation                                     |431    |83      |44      |347     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |56     |28      |6       |47      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |6       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |20     |10      |0       |20      |0       |0       |
|    integ                           |Integration                                      |140    |15      |14      |113     |0       |0       |
|    modu                            |Modulation                                       |59     |23      |7       |57      |0       |1       |
|    rs422                           |Rs422Output                                      |310    |90      |29      |246     |0       |4       |
|    trans                           |SquareWaveGenerator                              |31     |26      |5       |18      |0       |0       |
|  u_uart                            |UART_Control                                     |132    |116     |7       |63      |0       |0       |
|    U0                              |speed_select_Tx                                  |33     |25      |7       |18      |0       |0       |
|    U1                              |uart_tx                                          |18     |13      |0       |17      |0       |0       |
|    U2                              |Ctrl_Data                                        |81     |78      |0       |28      |0       |0       |
|  wendu                             |DS18B20                                          |197    |152     |45      |71      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |459    |270     |91      |284     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |459    |270     |91      |284     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |170    |84      |0       |169     |0       |0       |
|        reg_inst                    |register                                         |167    |81      |0       |166     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |289    |186     |91      |115     |0       |0       |
|        bus_inst                    |bus_top                                          |85     |44      |30      |29      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |45     |21      |16      |13      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |40     |23      |14      |16      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |111    |82      |29      |59      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1647  
    #2          2       446   
    #3          3       220   
    #4          4        12   
    #5        5-10      117   
    #6        11-50      50   
    #7       51-100      3    
    #8       101-500     1    
  Average     2.29            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9074, tnet num: 2538, tinst num: 1056, tnode num: 12057, tedge num: 15894.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2538 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 8c0bef7d7f4a23ca399149266bad52a96c0817f4101cbf2ed9a6d4b976597300 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1056
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2540, pip num: 22135
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1723 valid insts, and 57500 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000111110101101110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  6.517257s wall, 33.843750s user + 0.140625s system = 33.984375s CPU (521.5%)

RUN-1004 : used memory is 551 MB, reserved memory is 516 MB, peak memory is 697 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231129_145231.log"
