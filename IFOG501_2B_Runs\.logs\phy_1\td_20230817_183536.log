============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug 17 18:35:36 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1628 instances
RUN-0007 : 369 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2198 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1642 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1626 instances, 369 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7793, tnet num: 2196, tinst num: 1626, tnode num: 11033, tedge num: 13179.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283809s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (93.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 578528
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1626.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 552108, overlap = 20.25
PHY-3002 : Step(2): len = 441808, overlap = 13.5
PHY-3002 : Step(3): len = 367291, overlap = 15.75
PHY-3002 : Step(4): len = 349040, overlap = 15.75
PHY-3002 : Step(5): len = 340104, overlap = 15.75
PHY-3002 : Step(6): len = 329976, overlap = 18
PHY-3002 : Step(7): len = 322524, overlap = 20.25
PHY-3002 : Step(8): len = 315547, overlap = 20.25
PHY-3002 : Step(9): len = 308801, overlap = 20.25
PHY-3002 : Step(10): len = 300508, overlap = 20.25
PHY-3002 : Step(11): len = 294841, overlap = 20.25
PHY-3002 : Step(12): len = 289403, overlap = 20.25
PHY-3002 : Step(13): len = 283026, overlap = 20.25
PHY-3002 : Step(14): len = 276608, overlap = 20.25
PHY-3002 : Step(15): len = 271186, overlap = 20.25
PHY-3002 : Step(16): len = 265973, overlap = 20.25
PHY-3002 : Step(17): len = 260275, overlap = 20.25
PHY-3002 : Step(18): len = 254601, overlap = 20.25
PHY-3002 : Step(19): len = 249027, overlap = 20.25
PHY-3002 : Step(20): len = 243855, overlap = 20.25
PHY-3002 : Step(21): len = 238948, overlap = 20.25
PHY-3002 : Step(22): len = 233378, overlap = 20.25
PHY-3002 : Step(23): len = 228526, overlap = 20.25
PHY-3002 : Step(24): len = 223987, overlap = 20.25
PHY-3002 : Step(25): len = 218926, overlap = 20.25
PHY-3002 : Step(26): len = 212091, overlap = 20.25
PHY-3002 : Step(27): len = 208804, overlap = 20.25
PHY-3002 : Step(28): len = 204631, overlap = 20.25
PHY-3002 : Step(29): len = 191518, overlap = 20.25
PHY-3002 : Step(30): len = 184040, overlap = 20.25
PHY-3002 : Step(31): len = 182353, overlap = 20.25
PHY-3002 : Step(32): len = 159693, overlap = 18
PHY-3002 : Step(33): len = 134551, overlap = 20.25
PHY-3002 : Step(34): len = 133178, overlap = 20.25
PHY-3002 : Step(35): len = 125636, overlap = 15.75
PHY-3002 : Step(36): len = 114578, overlap = 18
PHY-3002 : Step(37): len = 111598, overlap = 18
PHY-3002 : Step(38): len = 109588, overlap = 18
PHY-3002 : Step(39): len = 107714, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.16657e-05
PHY-3002 : Step(40): len = 109385, overlap = 11.25
PHY-3002 : Step(41): len = 108855, overlap = 11.25
PHY-3002 : Step(42): len = 107148, overlap = 13.5
PHY-3002 : Step(43): len = 106274, overlap = 11.25
PHY-3002 : Step(44): len = 103458, overlap = 9
PHY-3002 : Step(45): len = 101233, overlap = 2.25
PHY-3002 : Step(46): len = 97872.9, overlap = 4.5
PHY-3002 : Step(47): len = 97211.8, overlap = 9
PHY-3002 : Step(48): len = 93745.4, overlap = 11.25
PHY-3002 : Step(49): len = 92599.3, overlap = 11.25
PHY-3002 : Step(50): len = 90867.6, overlap = 11.25
PHY-3002 : Step(51): len = 88617.8, overlap = 9
PHY-3002 : Step(52): len = 86283.4, overlap = 6.75
PHY-3002 : Step(53): len = 85413.3, overlap = 9
PHY-3002 : Step(54): len = 84018.1, overlap = 9
PHY-3002 : Step(55): len = 82514.3, overlap = 11.25
PHY-3002 : Step(56): len = 79622.7, overlap = 11.25
PHY-3002 : Step(57): len = 78530.3, overlap = 11.25
PHY-3002 : Step(58): len = 76686.2, overlap = 11.25
PHY-3002 : Step(59): len = 75282.9, overlap = 6.75
PHY-3002 : Step(60): len = 74388.4, overlap = 6.75
PHY-3002 : Step(61): len = 72846.8, overlap = 11.25
PHY-3002 : Step(62): len = 68148, overlap = 13.5
PHY-3002 : Step(63): len = 67456, overlap = 11.25
PHY-3002 : Step(64): len = 65997.4, overlap = 9
PHY-3002 : Step(65): len = 65174.9, overlap = 6.75
PHY-3002 : Step(66): len = 65147.5, overlap = 6.75
PHY-3002 : Step(67): len = 64234.3, overlap = 11.25
PHY-3002 : Step(68): len = 61595.5, overlap = 11.25
PHY-3002 : Step(69): len = 60887.6, overlap = 11.25
PHY-3002 : Step(70): len = 60470.6, overlap = 11.25
PHY-3002 : Step(71): len = 60156.4, overlap = 9
PHY-3002 : Step(72): len = 59548.5, overlap = 9
PHY-3002 : Step(73): len = 59540.6, overlap = 6.75
PHY-3002 : Step(74): len = 59545.5, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000163331
PHY-3002 : Step(75): len = 59230.1, overlap = 6.75
PHY-3002 : Step(76): len = 59104.2, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000326663
PHY-3002 : Step(77): len = 59425.7, overlap = 6.75
PHY-3002 : Step(78): len = 59465.7, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007418s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (421.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068848s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(79): len = 61948.3, overlap = 5.53125
PHY-3002 : Step(80): len = 60699.3, overlap = 5.25
PHY-3002 : Step(81): len = 60359.3, overlap = 4.5
PHY-3002 : Step(82): len = 59530.6, overlap = 4.9375
PHY-3002 : Step(83): len = 58406.6, overlap = 4.8125
PHY-3002 : Step(84): len = 57394.2, overlap = 4.4375
PHY-3002 : Step(85): len = 56233.3, overlap = 3.8125
PHY-3002 : Step(86): len = 55530.5, overlap = 3.9375
PHY-3002 : Step(87): len = 53942.3, overlap = 3.625
PHY-3002 : Step(88): len = 53344.7, overlap = 4.5625
PHY-3002 : Step(89): len = 52916.3, overlap = 4.4375
PHY-3002 : Step(90): len = 52257.7, overlap = 4.5
PHY-3002 : Step(91): len = 51471.8, overlap = 3.875
PHY-3002 : Step(92): len = 50924.6, overlap = 3.0625
PHY-3002 : Step(93): len = 50792.7, overlap = 3.125
PHY-3002 : Step(94): len = 50223.4, overlap = 3.3125
PHY-3002 : Step(95): len = 49098.9, overlap = 3.4375
PHY-3002 : Step(96): len = 48227.5, overlap = 4.125
PHY-3002 : Step(97): len = 47434.3, overlap = 3.75
PHY-3002 : Step(98): len = 46891.9, overlap = 5.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000565679
PHY-3002 : Step(99): len = 47002, overlap = 6.4375
PHY-3002 : Step(100): len = 46990.9, overlap = 7.375
PHY-3002 : Step(101): len = 46958.7, overlap = 7.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00113136
PHY-3002 : Step(102): len = 46823, overlap = 7.625
PHY-3002 : Step(103): len = 46750.6, overlap = 7.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069388s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.68218e-05
PHY-3002 : Step(104): len = 47097.8, overlap = 53.8438
PHY-3002 : Step(105): len = 47062.5, overlap = 53.2812
PHY-3002 : Step(106): len = 47422.7, overlap = 53.75
PHY-3002 : Step(107): len = 47741.8, overlap = 52.7188
PHY-3002 : Step(108): len = 47855.3, overlap = 52.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000133644
PHY-3002 : Step(109): len = 48185.9, overlap = 50.6562
PHY-3002 : Step(110): len = 48360.5, overlap = 50.9062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000267287
PHY-3002 : Step(111): len = 48458.2, overlap = 50.1875
PHY-3002 : Step(112): len = 49029.6, overlap = 46.25
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7793, tnet num: 2196, tinst num: 1626, tnode num: 11033, tedge num: 13179.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 111.53 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52688, over cnt = 258(0%), over = 1133, worst = 23
PHY-1001 : End global iterations;  0.091050s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (120.1%)

PHY-1001 : Congestion index: top1 = 47.24, top5 = 26.54, top10 = 16.27, top15 = 11.42.
PHY-1001 : End incremental global routing;  0.142035s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (110.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.093829s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (99.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.273690s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (108.5%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1665/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52688, over cnt = 258(0%), over = 1133, worst = 23
PHY-1002 : len = 59728, over cnt = 193(0%), over = 591, worst = 17
PHY-1002 : len = 62584, over cnt = 105(0%), over = 323, worst = 17
PHY-1002 : len = 67264, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 67664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122314s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (115.0%)

PHY-1001 : Congestion index: top1 = 41.53, top5 = 26.58, top10 = 18.90, top15 = 13.74.
OPT-1001 : End congestion update;  0.166090s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068912s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.239316s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (104.5%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.851744s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (102.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 98 SEQ with LUT/SLICE
SYN-4006 : 113 single LUT's are left
SYN-4006 : 705 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1074/1406 primitive instances ...
PHY-3001 : End packing;  0.056946s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1485 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 839 instances, 792 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49034.8, Over = 75.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6564, tnet num: 2028, tinst num: 839, tnode num: 8913, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.392495s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.93382e-05
PHY-3002 : Step(113): len = 48566.4, overlap = 74.25
PHY-3002 : Step(114): len = 48273.4, overlap = 76.75
PHY-3002 : Step(115): len = 48101.1, overlap = 78.5
PHY-3002 : Step(116): len = 48033.1, overlap = 79.5
PHY-3002 : Step(117): len = 48114, overlap = 79.25
PHY-3002 : Step(118): len = 48158.3, overlap = 78.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.86764e-05
PHY-3002 : Step(119): len = 48421.9, overlap = 76.75
PHY-3002 : Step(120): len = 48767.9, overlap = 74.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.08293e-05
PHY-3002 : Step(121): len = 49576.9, overlap = 73.25
PHY-3002 : Step(122): len = 50616.5, overlap = 71.25
PHY-3002 : Step(123): len = 51419.1, overlap = 65.25
PHY-3002 : Step(124): len = 52448.2, overlap = 62
PHY-3002 : Step(125): len = 53436.5, overlap = 58.5
PHY-3002 : Step(126): len = 53610.7, overlap = 56.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.073382s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (212.9%)

PHY-3001 : Trial Legalized: Len = 69860
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.053834s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000694097
PHY-3002 : Step(127): len = 66825.3, overlap = 5
PHY-3002 : Step(128): len = 64098.8, overlap = 13.75
PHY-3002 : Step(129): len = 62236.7, overlap = 16.25
PHY-3002 : Step(130): len = 60967.8, overlap = 19
PHY-3002 : Step(131): len = 60346.6, overlap = 24
PHY-3002 : Step(132): len = 60001.3, overlap = 24.75
PHY-3002 : Step(133): len = 59715.7, overlap = 26.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00138819
PHY-3002 : Step(134): len = 60019.8, overlap = 25.75
PHY-3002 : Step(135): len = 60179.3, overlap = 24.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00277639
PHY-3002 : Step(136): len = 60377.5, overlap = 24.25
PHY-3002 : Step(137): len = 60518.7, overlap = 24.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004944s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (316.1%)

PHY-3001 : Legalized: Len = 65638.8, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006240s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 6, deltaY = 7, maxDist = 2.
PHY-3001 : Final: Len = 65842.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6564, tnet num: 2028, tinst num: 839, tnode num: 8913, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 27/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72232, over cnt = 159(0%), over = 247, worst = 6
PHY-1002 : len = 72680, over cnt = 86(0%), over = 146, worst = 6
PHY-1002 : len = 74672, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 74704, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.159115s wall, 0.250000s user + 0.125000s system = 0.375000s CPU (235.7%)

PHY-1001 : Congestion index: top1 = 31.92, top5 = 23.52, top10 = 18.19, top15 = 14.40.
PHY-1001 : End incremental global routing;  0.228448s wall, 0.312500s user + 0.125000s system = 0.437500s CPU (191.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.097690s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (96.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.359695s wall, 0.453125s user + 0.125000s system = 0.578125s CPU (160.7%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1803/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74704, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005957s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (262.3%)

PHY-1001 : Congestion index: top1 = 31.92, top5 = 23.52, top10 = 18.19, top15 = 14.40.
OPT-1001 : End congestion update;  0.064075s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061068s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.126903s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.5%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052418s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1803/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74704, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006094s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.92, top5 = 23.52, top10 = 18.19, top15 = 14.40.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068798s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.482759
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.007570s wall, 1.125000s user + 0.125000s system = 1.250000s CPU (124.1%)

RUN-1003 : finish command "place" in  5.349695s wall, 7.484375s user + 2.531250s system = 10.015625s CPU (187.2%)

RUN-1004 : used memory is 204 MB, reserved memory is 168 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1485 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6564, tnet num: 2028, tinst num: 839, tnode num: 8913, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71912, over cnt = 160(0%), over = 245, worst = 6
PHY-1002 : len = 72344, over cnt = 89(0%), over = 149, worst = 6
PHY-1002 : len = 74352, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 74384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.162186s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (163.8%)

PHY-1001 : Congestion index: top1 = 31.88, top5 = 23.49, top10 = 18.13, top15 = 14.33.
PHY-1001 : End global routing;  0.218117s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (150.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 205, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 498, reserve = 468, peak = 498.
PHY-1001 : End build detailed router design. 3.668586s wall, 3.625000s user + 0.031250s system = 3.656250s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34072, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.542565s wall, 1.515625s user + 0.046875s system = 1.562500s CPU (101.3%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.549365s wall, 1.531250s user + 0.046875s system = 1.578125s CPU (101.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186968, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End initial routed; 1.306298s wall, 2.390625s user + 0.218750s system = 2.609375s CPU (199.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1794(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.274   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.442145s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (102.5%)

PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End phase 2; 1.748565s wall, 2.843750s user + 0.218750s system = 3.062500s CPU (175.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186968, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017987s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186840, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.039779s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (157.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186840, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.023909s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 186856, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.022470s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (69.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1794(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.274   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.405204s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.202123s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End phase 3; 0.864092s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 186856
PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End export database. 0.010167s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (153.7%)

PHY-1001 : End detail routing;  8.041983s wall, 9.062500s user + 0.312500s system = 9.375000s CPU (116.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6564, tnet num: 2028, tinst num: 839, tnode num: 8913, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  9.094254s wall, 10.187500s user + 0.359375s system = 10.546875s CPU (116.0%)

RUN-1004 : used memory is 520 MB, reserved memory is 488 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      822   out of  19600    4.19%
#reg                     1074   out of  19600    5.48%
#le                      1527
  #lut only               453   out of   1527   29.67%
  #reg only               705   out of   1527   46.17%
  #lut&reg                369   out of   1527   24.17%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         472
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1527   |597     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1122   |295     |132     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |530    |128     |57      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |0       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |15      |0       |26      |0       |0       |
|    integ                   |Integration                                      |140    |15      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |88     |33      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |79      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |22      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |112    |95      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |22     |17      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |50      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1449  
    #2          2       307   
    #3          3       104   
    #4          4        18   
    #5        5-10       79   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6564, tnet num: 2028, tinst num: 839, tnode num: 8913, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 839
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2030, pip num: 14800
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1300 valid insts, and 39207 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.399818s wall, 18.625000s user + 0.140625s system = 18.765625s CPU (552.0%)

RUN-1004 : used memory is 544 MB, reserved memory is 510 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230817_183536.log"
