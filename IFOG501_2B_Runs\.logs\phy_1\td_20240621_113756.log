============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 11:37:56 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(93)
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(103)
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(93)
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(103)
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 25 trigger nets, 25 data nets.
KIT-1004 : Chipwatcher code = 1001011011010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2183/10 useful/useless nets, 1382/5 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 1956/18 useful/useless nets, 1712/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 282 better
SYN-1014 : Optimize round 2
SYN-1032 : 1761/15 useful/useless nets, 1517/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1785/156 useful/useless nets, 1563/30 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 207 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 2153/5 useful/useless nets, 1931/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8011, tnet num: 2153, tinst num: 1930, tnode num: 10028, tedge num: 12338.
TMR-2509 : Cut connection from add0_syn_124.fci to add0_syn_124.fco.
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fco.
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.fco.
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fco.
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.fco.
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_1.a to count_b[15]_syn_1.o.
TMR-2509 : Cut connection from count_b[15]_syn_1.b to count_b[15]_syn_1.o.
TMR-2509 : Cut connection from count_b[15]_syn_1.d to count_b[15]_syn_1.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.b to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.c to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.d to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.e to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.b to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.c to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.d to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[13]_syn_1.b to count_b[13]_syn_1.o.
TMR-2509 : Cut connection from count_b[15]_syn_7.a to count_b[15]_syn_7.o.
TMR-2509 : Cut connection from count_b[15]_syn_7.b to count_b[15]_syn_7.o.
TMR-2509 : Cut connection from count_b[15]_syn_7.d to count_b[15]_syn_7.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_3.a to ARM_INT_n7_syn_3.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_3.b to ARM_INT_n7_syn_3.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_3.d to ARM_INT_n7_syn_3.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_7.b to ARM_INT_n7_syn_7.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_7.d to ARM_INT_n7_syn_7.o.
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[0].
TMR-2509 : Cut connection from add0_syn_126.b[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[1] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_127.a[0] to add0_syn_127.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_9.c to count_b[15]_syn_9.o.
TMR-2509 : Cut connection from count_b[15]_syn_9.d to count_b[15]_syn_9.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_5.c to ARM_INT_n7_syn_5.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_5.d to ARM_INT_n7_syn_5.o.
TMR-2509 : Cut connection from count_b[0]_syn_1.b to count_b[0]_syn_1.o.
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_5.a to count_b[15]_syn_5.o.
TMR-2509 : Cut connection from count_b[15]_syn_5.b to count_b[15]_syn_5.o.
TMR-2509 : Cut connection from count_b[15]_syn_5.c to count_b[15]_syn_5.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_1.a to ARM_INT_n7_syn_1.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_1.b to ARM_INT_n7_syn_1.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_1.c to ARM_INT_n7_syn_1.o.
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.a[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fx[0].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[0].
TMR-2507 : Eliminate loop in the timing graph, delete 65 tedges.
TMR-2508 : Levelizing timing graph completed, there are 87 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2153 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 175 (3.57), #lev = 7 (1.77)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 174 (3.59), #lev = 6 (1.89)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 395 instances into 174 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 280 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net count[19] will be merged to another kept net count_b1[19]
SYN-5055 WARNING: The kept net count[18] will be merged to another kept net count_b[18]
SYN-5055 WARNING: The kept net count[17] will be merged to another kept net count_b[17]
SYN-5055 WARNING: The kept net count[16] will be merged to another kept net count_b[16]
SYN-5055 WARNING: The kept net count[15] will be merged to another kept net count_b[15]
SYN-5055 WARNING: The kept net count[14] will be merged to another kept net count_b1[14]
SYN-5055 WARNING: The kept net count[13] will be merged to another kept net count_b[13]
SYN-5055 WARNING: The kept net count[12] will be merged to another kept net count_b1[12]
SYN-5055 WARNING: The kept net count[11] will be merged to another kept net count_b1[11]
SYN-5055 WARNING: The kept net count[10] will be merged to another kept net count_b1[10]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (180 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "ARM_INT_n7" drives clk pins.
SYN-4025 : Tag rtl::Net ARM_INT_n7 as clock net
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 4 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net ARM_INT_n7 to drive 2 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1417 instances
RUN-0007 : 606 luts, 600 seqs, 108 mslices, 66 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1646 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 952 nets have 2 pins
RUN-1001 : 505 nets have [3 - 5] pins
RUN-1001 : 98 nets have [6 - 10] pins
RUN-1001 : 55 nets have [11 - 20] pins
RUN-1001 : 25 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     147     
RUN-1001 :   No   |  No   |  Yes  |     104     
RUN-1001 :   No   |  Yes  |  No   |     84      
RUN-1001 :   Yes  |  No   |  No   |     89      
RUN-1001 :   Yes  |  No   |  Yes  |     176     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |   7   |     13     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 19
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1415 instances, 606 luts, 600 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6922, tnet num: 1644, tinst num: 1415, tnode num: 8902, tedge num: 11376.
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.a[1] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_5.a to count_b[15]_syn_5.o.
TMR-2509 : Cut connection from count_b[15]_syn_5.d to count_b[15]_syn_5.o.
TMR-2509 : Cut connection from count_b[15]_syn_5.b to count_b[15]_syn_5.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.a to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.c to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.d to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[13]_syn_1.b to count_b[13]_syn_1.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_3.b to ARM_INT_n7_syn_3.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_3.d to ARM_INT_n7_syn_3.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_3.a to ARM_INT_n7_syn_3.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_7.b to ARM_INT_n7_syn_7.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_7.d to ARM_INT_n7_syn_7.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_7.a to ARM_INT_n7_syn_7.o.
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from count_b[15]_syn_7.d to count_b[15]_syn_7.o.
TMR-2509 : Cut connection from count_b[15]_syn_7.a to count_b[15]_syn_7.o.
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[0].
TMR-2509 : Cut connection from add0_syn_126.b[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[1] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_127.a[0] to add0_syn_127.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_5.c to ARM_INT_n7_syn_5.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_5.d to ARM_INT_n7_syn_5.o.
TMR-2509 : Cut connection from count_b[15]_syn_9.c to count_b[15]_syn_9.o.
TMR-2509 : Cut connection from count_b[15]_syn_9.d to count_b[15]_syn_9.o.
TMR-2509 : Cut connection from count_b[15]_syn_1.c to count_b[15]_syn_1.o.
TMR-2509 : Cut connection from count_b[15]_syn_1.d to count_b[15]_syn_1.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.b to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.d to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.c to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.e to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[0]_syn_1.b to count_b[0]_syn_1.o.
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_1.a to ARM_INT_n7_syn_1.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_1.b to ARM_INT_n7_syn_1.o.
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.a[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from count_b[8]_syn_1.b to count_b[8]_syn_1.o.
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[0].
TMR-2507 : Eliminate loop in the timing graph, delete 69 tedges.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1644 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.129042s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (96.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 399848
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1415.
PHY-3001 : End clustering;  0.000027s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 318277, overlap = 51.75
PHY-3002 : Step(2): len = 261232, overlap = 51.75
PHY-3002 : Step(3): len = 229224, overlap = 51.75
PHY-3002 : Step(4): len = 200569, overlap = 51.75
PHY-3002 : Step(5): len = 174045, overlap = 51.75
PHY-3002 : Step(6): len = 155528, overlap = 51.75
PHY-3002 : Step(7): len = 133851, overlap = 51.75
PHY-3002 : Step(8): len = 116439, overlap = 51.75
PHY-3002 : Step(9): len = 103687, overlap = 51.75
PHY-3002 : Step(10): len = 92475.9, overlap = 51.75
PHY-3002 : Step(11): len = 84177, overlap = 51.75
PHY-3002 : Step(12): len = 75621.2, overlap = 51.75
PHY-3002 : Step(13): len = 70027.8, overlap = 51.75
PHY-3002 : Step(14): len = 64253, overlap = 51.75
PHY-3002 : Step(15): len = 59721.4, overlap = 51.75
PHY-3002 : Step(16): len = 55973.4, overlap = 47.25
PHY-3002 : Step(17): len = 51826.3, overlap = 47.25
PHY-3002 : Step(18): len = 49890.3, overlap = 47.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.00026e-05
PHY-3002 : Step(19): len = 49563.1, overlap = 38.25
PHY-3002 : Step(20): len = 48569, overlap = 45
PHY-3002 : Step(21): len = 46191.7, overlap = 45
PHY-3002 : Step(22): len = 44972.6, overlap = 29.25
PHY-3002 : Step(23): len = 43786.4, overlap = 31.5
PHY-3002 : Step(24): len = 42107.9, overlap = 36.25
PHY-3002 : Step(25): len = 41436.3, overlap = 29.4375
PHY-3002 : Step(26): len = 41425.8, overlap = 29.375
PHY-3002 : Step(27): len = 40547.6, overlap = 31.9375
PHY-3002 : Step(28): len = 39624.5, overlap = 32.0625
PHY-3002 : Step(29): len = 39293.6, overlap = 34.3125
PHY-3002 : Step(30): len = 38141.4, overlap = 30.25
PHY-3002 : Step(31): len = 37416.2, overlap = 33.0625
PHY-3002 : Step(32): len = 36501.1, overlap = 33.125
PHY-3002 : Step(33): len = 36052.9, overlap = 33.25
PHY-3002 : Step(34): len = 35296.7, overlap = 33.3125
PHY-3002 : Step(35): len = 35237.3, overlap = 33.3125
PHY-3002 : Step(36): len = 34951.2, overlap = 31.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.00053e-05
PHY-3002 : Step(37): len = 34931.4, overlap = 31.0625
PHY-3002 : Step(38): len = 34912.8, overlap = 31.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.00105e-05
PHY-3002 : Step(39): len = 34922.3, overlap = 31.0625
PHY-3002 : Step(40): len = 34956.5, overlap = 31.0625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004874s wall, 0.000000s user + 0.046875s system = 0.046875s CPU (961.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1644 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038143s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (81.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(41): len = 37629.9, overlap = 11.2812
PHY-3002 : Step(42): len = 37636.2, overlap = 11.4688
PHY-3002 : Step(43): len = 37651.7, overlap = 11.7188
PHY-3002 : Step(44): len = 37490.3, overlap = 11.5625
PHY-3002 : Step(45): len = 37472.9, overlap = 11.5312
PHY-3002 : Step(46): len = 37262.9, overlap = 11.6875
PHY-3002 : Step(47): len = 37086.8, overlap = 11.7812
PHY-3002 : Step(48): len = 36875.3, overlap = 11.8438
PHY-3002 : Step(49): len = 36831, overlap = 11.1875
PHY-3002 : Step(50): len = 36697.8, overlap = 8.4375
PHY-3002 : Step(51): len = 36359, overlap = 7.28125
PHY-3002 : Step(52): len = 36224.4, overlap = 7.1875
PHY-3002 : Step(53): len = 35856, overlap = 7.28125
PHY-3002 : Step(54): len = 35669.8, overlap = 7.53125
PHY-3002 : Step(55): len = 35410.9, overlap = 7.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000872567
PHY-3002 : Step(56): len = 35344.3, overlap = 7.15625
PHY-3002 : Step(57): len = 35451.4, overlap = 6.6875
PHY-3002 : Step(58): len = 35537, overlap = 6.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00174513
PHY-3002 : Step(59): len = 35432.4, overlap = 6.4375
PHY-3002 : Step(60): len = 35432.4, overlap = 6.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1644 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037022s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.43541e-05
PHY-3002 : Step(61): len = 35764.7, overlap = 60.125
PHY-3002 : Step(62): len = 35854.4, overlap = 59.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000108708
PHY-3002 : Step(63): len = 36045.4, overlap = 55.9688
PHY-3002 : Step(64): len = 36342.8, overlap = 54.6562
PHY-3002 : Step(65): len = 37367.3, overlap = 49.8125
PHY-3002 : Step(66): len = 38138.6, overlap = 45.0625
PHY-3002 : Step(67): len = 37953.7, overlap = 41.1875
PHY-3002 : Step(68): len = 37950.7, overlap = 40.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000217416
PHY-3002 : Step(69): len = 37828.7, overlap = 40.125
PHY-3002 : Step(70): len = 37891.2, overlap = 39.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000434833
PHY-3002 : Step(71): len = 38488.8, overlap = 38.3125
PHY-3002 : Step(72): len = 38804.9, overlap = 35.4688
PHY-3002 : Step(73): len = 39153.6, overlap = 34.0938
PHY-3002 : Step(74): len = 39221.1, overlap = 32.9688
PHY-3002 : Step(75): len = 39256.9, overlap = 31.9688
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000869665
PHY-3002 : Step(76): len = 39355.1, overlap = 32.0312
PHY-3002 : Step(77): len = 39355.1, overlap = 32.0312
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00173933
PHY-3002 : Step(78): len = 39988.8, overlap = 28.8125
PHY-3002 : Step(79): len = 40171.5, overlap = 27.6562
PHY-3002 : Step(80): len = 40304.2, overlap = 25.2188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6922, tnet num: 1644, tinst num: 1415, tnode num: 8902, tedge num: 11376.
TMR-2509 : Cut connection from add0_syn_124.fci to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_1.c to ARM_INT_n7_syn_1.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_1.d to ARM_INT_n7_syn_1.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_1.b to ARM_INT_n7_syn_1.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_7.c to ARM_INT_n7_syn_7.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_7.d to ARM_INT_n7_syn_7.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_7.a to ARM_INT_n7_syn_7.o.
TMR-2509 : Cut connection from add0_syn_124.fci to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_124.fci to add0_syn_124.fco.
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.fco.
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fco.
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_7.b to count_b[15]_syn_7.o.
TMR-2509 : Cut connection from count_b[15]_syn_7.a to count_b[15]_syn_7.o.
TMR-2509 : Cut connection from count_b[15]_syn_7.c to count_b[15]_syn_7.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.a to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.b to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[15]_syn_11.d to count_b[15]_syn_11.o.
TMR-2509 : Cut connection from count_b[13]_syn_1.b to count_b[13]_syn_1.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_3.b to ARM_INT_n7_syn_3.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_3.a to ARM_INT_n7_syn_3.o.
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_3.b to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.d to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.c to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from count_b[15]_syn_3.e to count_b[15]_syn_3.o.
TMR-2509 : Cut connection from add0_syn_126.b[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[1] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_127.a[0] to add0_syn_127.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_5.c to ARM_INT_n7_syn_5.o.
TMR-2509 : Cut connection from ARM_INT_n7_syn_5.d to ARM_INT_n7_syn_5.o.
TMR-2509 : Cut connection from count_b[15]_syn_9.c to count_b[15]_syn_9.o.
TMR-2509 : Cut connection from count_b[15]_syn_9.d to count_b[15]_syn_9.o.
TMR-2509 : Cut connection from count_b[0]_syn_1.b to count_b[0]_syn_1.o.
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[0].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.a[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_5.b to count_b[15]_syn_5.o.
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fx[0].
TMR-2507 : Eliminate loop in the timing graph, delete 61 tedges.
TMR-2508 : Levelizing timing graph completed, there are 41 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 78.62 peak overflow 2.53
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1646.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 46000, over cnt = 200(0%), over = 710, worst = 17
PHY-1001 : End global iterations;  0.072104s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.3%)

PHY-1001 : Congestion index: top1 = 34.48, top5 = 21.19, top10 = 13.40, top15 = 9.56.
PHY-1001 : End incremental global routing;  0.117255s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (93.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1644 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043633s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (107.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.185855s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (92.5%)

OPT-1001 : Current memory(MB): used = 192, reserve = 146, peak = 192.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1124/1646.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 46000, over cnt = 200(0%), over = 710, worst = 17
PHY-1002 : len = 50648, over cnt = 147(0%), over = 292, worst = 17
PHY-1002 : len = 53528, over cnt = 37(0%), over = 52, worst = 5
PHY-1002 : len = 53896, over cnt = 16(0%), over = 26, worst = 4
PHY-1002 : len = 54408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.100499s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (93.3%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 21.62, top10 = 14.88, top15 = 10.85.
OPT-1001 : End congestion update;  0.141815s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (110.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1644 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.036913s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.7%)

OPT-0007 : Start: WNS 3101 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.178943s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.8%)

OPT-1001 : Current memory(MB): used = 195, reserve = 149, peak = 195.
OPT-1001 : End physical optimization;  0.488157s wall, 0.468750s user + 0.031250s system = 0.500000s CPU (102.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 606 LUT to BLE ...
SYN-4008 : Packed 606 LUT and 229 SEQ to BLE.
SYN-4003 : Packing 371 remaining SEQ's ...
SYN-4005 : Packed 206 SEQ with LUT/SLICE
SYN-4006 : 195 single LUT's are left
SYN-4006 : 165 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 771/1156 primitive instances ...
PHY-3001 : End packing;  0.044695s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 631 instances
RUN-1001 : 297 mslices, 297 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1420 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 719 nets have 2 pins
RUN-1001 : 505 nets have [3 - 5] pins
RUN-1001 : 112 nets have [6 - 10] pins
RUN-1001 : 47 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 3 nets have 100+ pins
PHY-3001 : design contains 629 instances, 594 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 40360.6, Over = 36.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6012, tnet num: 1418, tinst num: 629, tnode num: 7449, tedge num: 10274.
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_122.b[0] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from count_b[13]_syn_9.a[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.b[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.c[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.c[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[0] to add0_syn_122.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.a[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.a[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.c[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.c[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.mi[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.b[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.d[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_34.c[0] to count_b[15]_syn_34.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.a[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.c[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_127.a[0] to add0_syn_127.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.b[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[1] to count_b[13]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.a[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[0] to count_b[0]_syn_9.f[0].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.d[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.b[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fx[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.d[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[1] to count_b[0]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[0].
TMR-2507 : Eliminate loop in the timing graph, delete 92 tedges.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1418 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.145561s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.14124e-05
PHY-3002 : Step(81): len = 39513.9, overlap = 37.5
PHY-3002 : Step(82): len = 39161.7, overlap = 39.5
PHY-3002 : Step(83): len = 38810.6, overlap = 40.5
PHY-3002 : Step(84): len = 38903.7, overlap = 41.5
PHY-3002 : Step(85): len = 39210.7, overlap = 39.5
PHY-3002 : Step(86): len = 39095.5, overlap = 40.75
PHY-3002 : Step(87): len = 39303.4, overlap = 42.25
PHY-3002 : Step(88): len = 38967, overlap = 42.5
PHY-3002 : Step(89): len = 38849.3, overlap = 42.5
PHY-3002 : Step(90): len = 38616.8, overlap = 43.5
PHY-3002 : Step(91): len = 38614.5, overlap = 43.75
PHY-3002 : Step(92): len = 38659.1, overlap = 43.75
PHY-3002 : Step(93): len = 38298.6, overlap = 43.5
PHY-3002 : Step(94): len = 38013.1, overlap = 43.75
PHY-3002 : Step(95): len = 37820.6, overlap = 44.75
PHY-3002 : Step(96): len = 37999.6, overlap = 45
PHY-3002 : Step(97): len = 37370.4, overlap = 47.5
PHY-3002 : Step(98): len = 37074.8, overlap = 48
PHY-3002 : Step(99): len = 36920.1, overlap = 47.75
PHY-3002 : Step(100): len = 36858.9, overlap = 46
PHY-3002 : Step(101): len = 37195.9, overlap = 44.75
PHY-3002 : Step(102): len = 36955.8, overlap = 43
PHY-3002 : Step(103): len = 36841.2, overlap = 43
PHY-3002 : Step(104): len = 36730.4, overlap = 46.75
PHY-3002 : Step(105): len = 36905.9, overlap = 42.5
PHY-3002 : Step(106): len = 36620.8, overlap = 44.25
PHY-3002 : Step(107): len = 36742.9, overlap = 42.25
PHY-3002 : Step(108): len = 36794.8, overlap = 43
PHY-3002 : Step(109): len = 36427.4, overlap = 43
PHY-3002 : Step(110): len = 36471.1, overlap = 43.5
PHY-3002 : Step(111): len = 36123.8, overlap = 45
PHY-3002 : Step(112): len = 36038.6, overlap = 46.75
PHY-3002 : Step(113): len = 36070.4, overlap = 47.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000182825
PHY-3002 : Step(114): len = 36208.8, overlap = 46
PHY-3002 : Step(115): len = 36680.2, overlap = 44.25
PHY-3002 : Step(116): len = 36940, overlap = 43
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00036565
PHY-3002 : Step(117): len = 37781.2, overlap = 42.25
PHY-3002 : Step(118): len = 38348.1, overlap = 39.75
PHY-3002 : Step(119): len = 38361.3, overlap = 39.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.069224s wall, 0.062500s user + 0.140625s system = 0.203125s CPU (293.4%)

PHY-3001 : Trial Legalized: Len = 51840.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1418 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.033618s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0438382
PHY-3002 : Step(120): len = 50710.8, overlap = 0.5
PHY-3002 : Step(121): len = 49202.6, overlap = 2.25
PHY-3002 : Step(122): len = 48442.8, overlap = 4.75
PHY-3002 : Step(123): len = 47206.3, overlap = 5.75
PHY-3002 : Step(124): len = 46630.7, overlap = 6.25
PHY-3002 : Step(125): len = 45702.2, overlap = 5.75
PHY-3002 : Step(126): len = 45251.2, overlap = 8.5
PHY-3002 : Step(127): len = 44947.5, overlap = 9.25
PHY-3002 : Step(128): len = 44655.1, overlap = 9.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0876765
PHY-3002 : Step(129): len = 44629.9, overlap = 9.5
PHY-3002 : Step(130): len = 44408.4, overlap = 9.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.155336
PHY-3002 : Step(131): len = 44378.6, overlap = 9.75
PHY-3002 : Step(132): len = 44277.1, overlap = 10
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004764s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 47923.7, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004015s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 2 instances has been re-located, deltaX = 0, deltaY = 0, maxDist = 0.
PHY-3001 : Final: Len = 47957.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6012, tnet num: 1418, tinst num: 629, tnode num: 7449, tedge num: 10274.
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.b[1] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_127.a[0] to add0_syn_127.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.d[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.b[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.a[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.b[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_34.c[0] to count_b[15]_syn_34.f[0].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.c[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.b[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.d[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[1] to count_b[13]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.a[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.d[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.d[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.c[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.c[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.mi[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[0] to count_b[0]_syn_9.f[0].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.d[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.b[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fx[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.d[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[1] to count_b[0]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[0].
TMR-2507 : Eliminate loop in the timing graph, delete 92 tedges.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 4/1420.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57280, over cnt = 189(0%), over = 291, worst = 7
PHY-1002 : len = 58528, over cnt = 99(0%), over = 127, worst = 3
PHY-1002 : len = 59960, over cnt = 14(0%), over = 17, worst = 2
PHY-1002 : len = 60080, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 60216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.158523s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.6%)

PHY-1001 : Congestion index: top1 = 30.22, top5 = 21.17, top10 = 16.25, top15 = 12.44.
PHY-1001 : End incremental global routing;  0.207011s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (98.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1418 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.042153s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (111.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.275234s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (96.5%)

OPT-1001 : Current memory(MB): used = 199, reserve = 153, peak = 199.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1245/1420.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005158s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.22, top5 = 21.17, top10 = 16.25, top15 = 12.44.
OPT-1001 : End congestion update;  0.047251s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1418 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.039723s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.0%)

OPT-0007 : Start: WNS 4202 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.087164s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.6%)

OPT-1001 : Current memory(MB): used = 201, reserve = 156, peak = 201.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1418 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.037327s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1245/1420.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004800s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.22, top5 = 21.17, top10 = 16.25, top15 = 12.44.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1418 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.038205s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (122.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4202 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 29.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4202ps with logic level 7 
RUN-1001 :       #2 path slack 4219ps with logic level 7 
RUN-1001 :       #3 path slack 4280ps with logic level 7 
RUN-1001 :       #4 path slack 4302ps with logic level 7 
OPT-1001 : End physical optimization;  0.630767s wall, 0.640625s user + 0.015625s system = 0.656250s CPU (104.0%)

RUN-1003 : finish command "place" in  3.832448s wall, 5.531250s user + 2.609375s system = 8.140625s CPU (212.4%)

RUN-1004 : used memory is 185 MB, reserved memory is 139 MB, peak memory is 202 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 631 instances
RUN-1001 : 297 mslices, 297 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1420 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 719 nets have 2 pins
RUN-1001 : 505 nets have [3 - 5] pins
RUN-1001 : 112 nets have [6 - 10] pins
RUN-1001 : 47 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 3 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6012, tnet num: 1418, tinst num: 629, tnode num: 7449, tedge num: 10274.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.c[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.b[1] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_127.a[0] to add0_syn_127.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.d[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.b[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.a[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.b[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_34.c[0] to count_b[15]_syn_34.f[0].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.b[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.d[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[1] to count_b[13]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.a[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.d[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.d[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.c[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.c[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.mi[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[0] to count_b[0]_syn_9.f[0].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.d[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.b[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fx[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.d[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[1] to count_b[0]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[0].
TMR-2507 : Eliminate loop in the timing graph, delete 93 tedges.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 297 mslices, 297 lslices, 8 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1418 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57176, over cnt = 191(0%), over = 291, worst = 7
PHY-1002 : len = 58400, over cnt = 102(0%), over = 128, worst = 3
PHY-1002 : len = 59576, over cnt = 25(0%), over = 30, worst = 3
PHY-1002 : len = 60104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.151806s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (133.8%)

PHY-1001 : Congestion index: top1 = 30.06, top5 = 21.10, top10 = 16.24, top15 = 12.45.
PHY-1001 : End global routing;  0.197675s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (126.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 219, reserve = 174, peak = 219.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : clock net ARM_INT_n7_syn_9 will be merged with clock ARM_INT_n7
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 477, reserve = 436, peak = 477.
PHY-1001 : End build detailed router design. 3.108164s wall, 3.046875s user + 0.062500s system = 3.109375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29520, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.849947s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 509, reserve = 469, peak = 509.
PHY-1001 : End phase 1; 0.856041s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 187976, over cnt = 40(0%), over = 40, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 510, reserve = 469, peak = 510.
PHY-1001 : End initial routed; 1.593291s wall, 2.046875s user + 0.140625s system = 2.187500s CPU (137.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1256(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.368   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.189235s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (107.3%)

PHY-1001 : Current memory(MB): used = 511, reserve = 471, peak = 511.
PHY-1001 : End phase 2; 1.782611s wall, 2.250000s user + 0.140625s system = 2.390625s CPU (134.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 187976, over cnt = 40(0%), over = 40, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.012994s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 187880, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031906s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 187856, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025204s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (124.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1256(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.368   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.193137s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (97.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 12 feed throughs used by 12 nets
PHY-1001 : End commit to database; 0.172153s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 527, reserve = 486, peak = 527.
PHY-1001 : End phase 3; 0.555463s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (98.5%)

PHY-1003 : Routed, final wirelength = 187856
PHY-1001 : Current memory(MB): used = 527, reserve = 486, peak = 527.
PHY-1001 : End export database. 0.009559s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (163.5%)

PHY-1001 : End detail routing;  6.491134s wall, 6.890625s user + 0.203125s system = 7.093750s CPU (109.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6012, tnet num: 1418, tinst num: 629, tnode num: 7449, tedge num: 10274.
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_123.a[1] to add0_syn_123.fco.
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.d[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.b[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.a[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.d[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_34.c[0] to count_b[15]_syn_34.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.a[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.d[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.c[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.a[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_127.a[0] to add0_syn_127.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.d[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.b[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[1] to count_b[13]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.a[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.d[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.d[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.c[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.c[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.mi[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[0] to count_b[0]_syn_9.f[0].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.fci to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[1] to count_b[0]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.b[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from count_b[8]_syn_9.c[0] to count_b[8]_syn_9.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[0].
TMR-2507 : Eliminate loop in the timing graph, delete 92 tedges.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.086190s wall, 7.500000s user + 0.234375s system = 7.734375s CPU (109.1%)

RUN-1004 : used memory is 482 MB, reserved memory is 442 MB, peak memory is 527 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      976   out of  19600    4.98%
#reg                      604   out of  19600    3.08%
#le                      1141
  #lut only               537   out of   1141   47.06%
  #reg only               165   out of   1141   14.46%
  #lut&reg                439   out of   1141   38.48%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  23
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    276
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         98
#3        ARM_INT_n7           GCLK               mslice             ARM_INT_n7_syn_18.f0     2
#4        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1141   |802     |174     |610     |23      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |117    |95      |11      |61      |0       |0       |
|    usms                            |Time_1ms        |27     |12      |5       |17      |0       |0       |
|  SPIM                              |SPI_MASTER      |191    |126     |23      |132     |0       |0       |
|  uart                              |UART_Control    |129    |119     |4       |53      |0       |0       |
|    U0                              |speed_select_Tx |23     |13      |4       |17      |0       |0       |
|    U1                              |uart_tx         |17     |17      |0       |15      |0       |0       |
|    U2                              |Ctrl_Data       |89     |89      |0       |21      |0       |0       |
|  wendu                             |DS18B20         |212    |169     |43      |75      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |460    |267     |87      |280     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |460    |267     |87      |280     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |182    |87      |0       |175     |0       |0       |
|        reg_inst                    |register        |180    |85      |0       |173     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |278    |180     |87      |105     |0       |0       |
|        bus_inst                    |bus_top         |77     |51      |26      |28      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |75     |49      |26      |26      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |113    |84      |29      |53      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout    Nets  
    #1         1       710   
    #2         2       305   
    #3         3       170   
    #4         4        29   
    #5        5-10     114   
    #6       11-50      69   
    #7       51-100     3    
  Average     2.99           

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6012, tnet num: 1418, tinst num: 629, tnode num: 7449, tedge num: 10274.
TMR-2509 : Cut connection from count_b[0]_syn_9.d[0] to count_b[0]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[0] to count_b[0]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.b[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.d[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.d[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.a[1] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.a[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_32.mi[0] to count_b[15]_syn_32.fx[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.b[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.d[0] to count_b[15]_syn_37.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_34.c[0] to count_b[15]_syn_34.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_37.a[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.c[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_37.d[1] to count_b[15]_syn_37.f[1].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[1] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.c[1] to ARM_INT_n7_syn_18.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[0] to ARM_INT_n7_syn_18.Q_q[0]_D.
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.b[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_18.d[0] to ARM_INT_n7_syn_18.f[0].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fx[1].
TMR-2509 : Cut connection from add0_syn_126.fci to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.fco.
TMR-2509 : Cut connection from add0_syn_127.a[0] to add0_syn_127.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.d[0] to count_b[13]_syn_9.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.b[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[0] to count_b[15]_syn_40.f[0].
TMR-2509 : Cut connection from count_b[13]_syn_9.c[1] to count_b[13]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[1] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fx[1].
TMR-2509 : Cut connection from add0_syn_125.fci to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.fco.
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from add0_syn_126.a[0] to add0_syn_126.f[0].
TMR-2509 : Cut connection from count_b[15]_syn_40.c[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.d[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from count_b[15]_syn_40.a[1] to count_b[15]_syn_40.f[1].
TMR-2509 : Cut connection from add0_syn_126.b[0] to add0_syn_126.fx[0].
TMR-2509 : Cut connection from count_b[0]_syn_9.c[1] to count_b[0]_syn_9.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.a[1] to add0_syn_122.f[1].
TMR-2509 : Cut connection from add0_syn_122.b[1] to add0_syn_122.fx[1].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.a[0] to add0_syn_123.f[0].
TMR-2509 : Cut connection from add0_syn_123.b[0] to add0_syn_123.fx[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.c[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.b[1] to ARM_INT_n7_syn_20.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.f[1].
TMR-2509 : Cut connection from add0_syn_123.b[1] to add0_syn_123.fx[1].
TMR-2509 : Cut connection from add0_syn_124.a[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.a[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from ARM_INT_n7_syn_20.b[0] to ARM_INT_n7_syn_20.f[0].
TMR-2509 : Cut connection from add0_syn_124.b[0] to add0_syn_124.fx[0].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.a[1] to add0_syn_124.f[1].
TMR-2509 : Cut connection from add0_syn_124.b[1] to add0_syn_124.fx[1].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.a[0] to add0_syn_125.f[0].
TMR-2509 : Cut connection from add0_syn_125.b[0] to add0_syn_125.fx[0].
TMR-2507 : Eliminate loop in the timing graph, delete 80 tedges.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1418 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 4 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		ARM_INT_n7_syn_9
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 588953dbe18b1871915f908614aff20f758057a2d1993c4931f4cee582d18853 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 629
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1420, pip num: 14174
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1306 valid insts, and 39426 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010011011001011011010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.058371s wall, 16.640625s user + 0.046875s system = 16.687500s CPU (545.6%)

RUN-1004 : used memory is 495 MB, reserved memory is 454 MB, peak memory is 646 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_113756.log"
