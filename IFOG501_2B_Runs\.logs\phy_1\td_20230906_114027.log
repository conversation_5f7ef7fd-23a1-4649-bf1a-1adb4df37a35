============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Sep  6 11:40:27 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1640 instances
RUN-0007 : 374 luts, 991 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2210 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1652 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1638 instances, 374 luts, 991 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7826, tnet num: 2208, tinst num: 1638, tnode num: 11064, tedge num: 13220.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.291132s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 593523
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1638.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 463395, overlap = 20.25
PHY-3002 : Step(2): len = 351781, overlap = 13.5
PHY-3002 : Step(3): len = 326008, overlap = 18
PHY-3002 : Step(4): len = 315436, overlap = 20.25
PHY-3002 : Step(5): len = 306975, overlap = 20.25
PHY-3002 : Step(6): len = 298377, overlap = 18
PHY-3002 : Step(7): len = 292579, overlap = 18
PHY-3002 : Step(8): len = 287931, overlap = 18
PHY-3002 : Step(9): len = 278310, overlap = 15.75
PHY-3002 : Step(10): len = 270563, overlap = 20.25
PHY-3002 : Step(11): len = 266736, overlap = 20.25
PHY-3002 : Step(12): len = 260630, overlap = 20.25
PHY-3002 : Step(13): len = 251501, overlap = 20.25
PHY-3002 : Step(14): len = 247435, overlap = 20.25
PHY-3002 : Step(15): len = 243076, overlap = 20.25
PHY-3002 : Step(16): len = 237036, overlap = 20.25
PHY-3002 : Step(17): len = 231202, overlap = 20.25
PHY-3002 : Step(18): len = 227120, overlap = 20.25
PHY-3002 : Step(19): len = 222069, overlap = 18
PHY-3002 : Step(20): len = 217040, overlap = 18
PHY-3002 : Step(21): len = 212440, overlap = 18
PHY-3002 : Step(22): len = 207770, overlap = 18
PHY-3002 : Step(23): len = 201981, overlap = 18
PHY-3002 : Step(24): len = 198894, overlap = 18
PHY-3002 : Step(25): len = 194662, overlap = 18
PHY-3002 : Step(26): len = 180018, overlap = 18
PHY-3002 : Step(27): len = 173212, overlap = 18
PHY-3002 : Step(28): len = 171736, overlap = 18
PHY-3002 : Step(29): len = 146378, overlap = 18
PHY-3002 : Step(30): len = 124386, overlap = 18
PHY-3002 : Step(31): len = 122661, overlap = 18
PHY-3002 : Step(32): len = 117484, overlap = 15.75
PHY-3002 : Step(33): len = 112949, overlap = 20.25
PHY-3002 : Step(34): len = 110552, overlap = 20.25
PHY-3002 : Step(35): len = 108909, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.40073e-05
PHY-3002 : Step(36): len = 109761, overlap = 20.25
PHY-3002 : Step(37): len = 109217, overlap = 11.25
PHY-3002 : Step(38): len = 107822, overlap = 15.75
PHY-3002 : Step(39): len = 106575, overlap = 13.5
PHY-3002 : Step(40): len = 104695, overlap = 13.5
PHY-3002 : Step(41): len = 102652, overlap = 9
PHY-3002 : Step(42): len = 98922.7, overlap = 9
PHY-3002 : Step(43): len = 97834.5, overlap = 13.5
PHY-3002 : Step(44): len = 95432.4, overlap = 13.5
PHY-3002 : Step(45): len = 94331.2, overlap = 6.75
PHY-3002 : Step(46): len = 90813.4, overlap = 11.25
PHY-3002 : Step(47): len = 90012, overlap = 11.25
PHY-3002 : Step(48): len = 88690.7, overlap = 11.25
PHY-3002 : Step(49): len = 86379.8, overlap = 9
PHY-3002 : Step(50): len = 84112.4, overlap = 9
PHY-3002 : Step(51): len = 81938.6, overlap = 13.5625
PHY-3002 : Step(52): len = 79921.3, overlap = 9
PHY-3002 : Step(53): len = 79403.8, overlap = 9
PHY-3002 : Step(54): len = 78643, overlap = 9
PHY-3002 : Step(55): len = 77990.2, overlap = 9
PHY-3002 : Step(56): len = 76769.4, overlap = 11.25
PHY-3002 : Step(57): len = 75551.3, overlap = 11.25
PHY-3002 : Step(58): len = 75068.3, overlap = 11.25
PHY-3002 : Step(59): len = 73422, overlap = 13.5
PHY-3002 : Step(60): len = 68890.9, overlap = 12.25
PHY-3002 : Step(61): len = 67508.6, overlap = 12.25
PHY-3002 : Step(62): len = 66393.6, overlap = 10
PHY-3002 : Step(63): len = 65710.2, overlap = 9.75
PHY-3002 : Step(64): len = 65634, overlap = 9.75
PHY-3002 : Step(65): len = 65128.5, overlap = 11.75
PHY-3002 : Step(66): len = 64509.4, overlap = 9.5
PHY-3002 : Step(67): len = 64200.5, overlap = 11.5
PHY-3002 : Step(68): len = 63817.3, overlap = 11.25
PHY-3002 : Step(69): len = 62453.5, overlap = 11.25
PHY-3002 : Step(70): len = 61866.3, overlap = 11.25
PHY-3002 : Step(71): len = 61338.8, overlap = 11.25
PHY-3002 : Step(72): len = 61075.8, overlap = 11.25
PHY-3002 : Step(73): len = 60240.7, overlap = 9
PHY-3002 : Step(74): len = 58783.3, overlap = 11.25
PHY-3002 : Step(75): len = 57130, overlap = 9
PHY-3002 : Step(76): len = 56067.4, overlap = 12
PHY-3002 : Step(77): len = 55147.4, overlap = 12
PHY-3002 : Step(78): len = 54773.1, overlap = 12
PHY-3002 : Step(79): len = 54486.2, overlap = 12
PHY-3002 : Step(80): len = 54139.8, overlap = 12
PHY-3002 : Step(81): len = 53903.7, overlap = 9.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000168015
PHY-3002 : Step(82): len = 53896.1, overlap = 7.5
PHY-3002 : Step(83): len = 53878.1, overlap = 7.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000336029
PHY-3002 : Step(84): len = 53939, overlap = 7.5
PHY-3002 : Step(85): len = 54013.5, overlap = 7.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006922s wall, 0.000000s user + 0.031250s system = 0.031250s CPU (451.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.078272s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (79.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(86): len = 58166.5, overlap = 8.375
PHY-3002 : Step(87): len = 57474, overlap = 8.25
PHY-3002 : Step(88): len = 56997.8, overlap = 8.4375
PHY-3002 : Step(89): len = 56370, overlap = 9.25
PHY-3002 : Step(90): len = 55896.2, overlap = 9.3125
PHY-3002 : Step(91): len = 54791.6, overlap = 8.5
PHY-3002 : Step(92): len = 54199.6, overlap = 8.125
PHY-3002 : Step(93): len = 53513.7, overlap = 8.8125
PHY-3002 : Step(94): len = 52876.8, overlap = 8.875
PHY-3002 : Step(95): len = 51753, overlap = 9
PHY-3002 : Step(96): len = 51283.2, overlap = 9.25
PHY-3002 : Step(97): len = 50846.2, overlap = 11.4062
PHY-3002 : Step(98): len = 50676.2, overlap = 11.4062
PHY-3002 : Step(99): len = 50038.7, overlap = 12.2188
PHY-3002 : Step(100): len = 49755.7, overlap = 10.9375
PHY-3002 : Step(101): len = 49712.7, overlap = 10.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00520977
PHY-3002 : Step(102): len = 49581, overlap = 10.5
PHY-3002 : Step(103): len = 49083.8, overlap = 10.25
PHY-3002 : Step(104): len = 48980.3, overlap = 10.25
PHY-3002 : Step(105): len = 48822.5, overlap = 9.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071668s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101739
PHY-3002 : Step(106): len = 49349.4, overlap = 63.5
PHY-3002 : Step(107): len = 49447.4, overlap = 56.0625
PHY-3002 : Step(108): len = 50350.6, overlap = 56.5938
PHY-3002 : Step(109): len = 50450.5, overlap = 52.5
PHY-3002 : Step(110): len = 49777.9, overlap = 51.4688
PHY-3002 : Step(111): len = 49779.9, overlap = 51.0938
PHY-3002 : Step(112): len = 49591.9, overlap = 50.25
PHY-3002 : Step(113): len = 49647, overlap = 49.9688
PHY-3002 : Step(114): len = 49673.7, overlap = 50
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203477
PHY-3002 : Step(115): len = 49521.6, overlap = 50.4688
PHY-3002 : Step(116): len = 49636.8, overlap = 50.5312
PHY-3002 : Step(117): len = 49759.9, overlap = 50.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000406955
PHY-3002 : Step(118): len = 50051.6, overlap = 46
PHY-3002 : Step(119): len = 50278.6, overlap = 46
PHY-3002 : Step(120): len = 51828.4, overlap = 42
PHY-3002 : Step(121): len = 52671.5, overlap = 38.8125
PHY-3002 : Step(122): len = 52348.8, overlap = 39.7188
PHY-3002 : Step(123): len = 52286.1, overlap = 39.6875
PHY-3002 : Step(124): len = 51629.8, overlap = 39.7812
PHY-3002 : Step(125): len = 51339.1, overlap = 39.9062
PHY-3002 : Step(126): len = 51287.2, overlap = 40.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00081391
PHY-3002 : Step(127): len = 51443.3, overlap = 40.8438
PHY-3002 : Step(128): len = 51794.8, overlap = 40.5625
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00162782
PHY-3002 : Step(129): len = 51881.6, overlap = 39.625
PHY-3002 : Step(130): len = 51881.6, overlap = 39.625
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00325564
PHY-3002 : Step(131): len = 52320, overlap = 38.2812
PHY-3002 : Step(132): len = 52542.3, overlap = 33.4688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7826, tnet num: 2208, tinst num: 1638, tnode num: 11064, tedge num: 13220.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.81 peak overflow 5.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2210.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56616, over cnt = 250(0%), over = 1044, worst = 17
PHY-1001 : End global iterations;  0.088133s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.4%)

PHY-1001 : Congestion index: top1 = 41.42, top5 = 24.69, top10 = 16.36, top15 = 11.73.
PHY-1001 : End incremental global routing;  0.144991s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (97.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068139s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.7%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1599 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1640 instances, 374 luts, 993 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 52734
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7834, tnet num: 2210, tinst num: 1640, tnode num: 11078, tedge num: 13232.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2210 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.426449s wall, 0.390625s user + 0.031250s system = 0.421875s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(133): len = 52756.8, overlap = 2.34375
PHY-3002 : Step(134): len = 52762.2, overlap = 2.46875
PHY-3002 : Step(135): len = 52753.6, overlap = 2.53125
PHY-3002 : Step(136): len = 52753.6, overlap = 2.53125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2210 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072506s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00191962
PHY-3002 : Step(137): len = 52772.8, overlap = 33.4688
PHY-3002 : Step(138): len = 52772.8, overlap = 33.4688
PHY-3001 : Final: Len = 52772.8, Over = 33.4688
PHY-3001 : End incremental placement;  0.610232s wall, 0.671875s user + 0.109375s system = 0.781250s CPU (128.0%)

OPT-1001 : Total overflow 85.88 peak overflow 5.41
OPT-1001 : End high-fanout net optimization;  0.885206s wall, 0.953125s user + 0.125000s system = 1.078125s CPU (121.8%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1716/2212.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56728, over cnt = 250(0%), over = 1040, worst = 17
PHY-1002 : len = 61640, over cnt = 183(0%), over = 518, worst = 11
PHY-1002 : len = 67368, over cnt = 32(0%), over = 35, worst = 3
PHY-1002 : len = 67896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107715s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (130.6%)

PHY-1001 : Congestion index: top1 = 36.51, top5 = 24.79, top10 = 18.12, top15 = 13.51.
OPT-1001 : End congestion update;  0.155167s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (120.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2210 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.088976s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (87.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.249557s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (112.7%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 221.
OPT-1001 : End physical optimization;  1.488339s wall, 1.562500s user + 0.140625s system = 1.703125s CPU (114.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 104 SEQ with LUT/SLICE
SYN-4006 : 104 single LUT's are left
SYN-4006 : 701 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1075/1408 primitive instances ...
PHY-3001 : End packing;  0.060468s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 844 instances
RUN-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2039 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 842 instances, 795 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52876.4, Over = 59.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6593, tnet num: 2037, tinst num: 842, tnode num: 8937, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.362232s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (86.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.41948e-05
PHY-3002 : Step(139): len = 52493.4, overlap = 59.5
PHY-3002 : Step(140): len = 52293, overlap = 60.5
PHY-3002 : Step(141): len = 52091.6, overlap = 61.5
PHY-3002 : Step(142): len = 51794.6, overlap = 62.25
PHY-3002 : Step(143): len = 51720.7, overlap = 61.75
PHY-3002 : Step(144): len = 51729.9, overlap = 62
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.83897e-05
PHY-3002 : Step(145): len = 52318.4, overlap = 62.25
PHY-3002 : Step(146): len = 52714.1, overlap = 62
PHY-3002 : Step(147): len = 52891.7, overlap = 60.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000136779
PHY-3002 : Step(148): len = 54069.1, overlap = 58
PHY-3002 : Step(149): len = 55272.3, overlap = 52.75
PHY-3002 : Step(150): len = 55238.3, overlap = 52.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.105634s wall, 0.031250s user + 0.171875s system = 0.203125s CPU (192.3%)

PHY-3001 : Trial Legalized: Len = 67004.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060822s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00082176
PHY-3002 : Step(151): len = 64336.8, overlap = 5.25
PHY-3002 : Step(152): len = 62135.5, overlap = 12
PHY-3002 : Step(153): len = 60754, overlap = 18.75
PHY-3002 : Step(154): len = 60028.6, overlap = 21
PHY-3002 : Step(155): len = 59558.6, overlap = 22.25
PHY-3002 : Step(156): len = 59231.8, overlap = 22.25
PHY-3002 : Step(157): len = 59005.5, overlap = 23.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00164352
PHY-3002 : Step(158): len = 59350.5, overlap = 23
PHY-3002 : Step(159): len = 59408.4, overlap = 22.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00328704
PHY-3002 : Step(160): len = 59494.1, overlap = 22.25
PHY-3002 : Step(161): len = 59519.5, overlap = 22.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004934s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (316.7%)

PHY-3001 : Legalized: Len = 63768.2, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005498s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 63872.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6593, tnet num: 2037, tinst num: 842, tnode num: 8937, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 171/2039.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71024, over cnt = 143(0%), over = 221, worst = 7
PHY-1002 : len = 71568, over cnt = 69(0%), over = 113, worst = 7
PHY-1002 : len = 72976, over cnt = 7(0%), over = 8, worst = 2
PHY-1002 : len = 73112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138322s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (169.4%)

PHY-1001 : Congestion index: top1 = 32.18, top5 = 22.85, top10 = 18.08, top15 = 14.27.
PHY-1001 : End incremental global routing;  0.202423s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (146.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065595s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.316693s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (128.3%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1804/2039.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006370s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (245.3%)

PHY-1001 : Congestion index: top1 = 32.18, top5 = 22.85, top10 = 18.08, top15 = 14.27.
OPT-1001 : End congestion update;  0.054659s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070363s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.126685s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (111.0%)

OPT-1001 : Current memory(MB): used = 222, reserve = 187, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056519s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1804/2039.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.014452s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.1%)

PHY-1001 : Congestion index: top1 = 32.18, top5 = 22.85, top10 = 18.08, top15 = 14.27.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056109s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.024537s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (108.3%)

RUN-1003 : finish command "place" in  6.496270s wall, 9.390625s user + 3.453125s system = 12.843750s CPU (197.7%)

RUN-1004 : used memory is 203 MB, reserved memory is 167 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 844 instances
RUN-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2039 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6593, tnet num: 2037, tinst num: 842, tnode num: 8937, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69368, over cnt = 163(0%), over = 251, worst = 7
PHY-1002 : len = 69968, over cnt = 86(0%), over = 137, worst = 7
PHY-1002 : len = 71752, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.167834s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (121.0%)

PHY-1001 : Congestion index: top1 = 31.08, top5 = 22.22, top10 = 17.61, top15 = 13.96.
PHY-1001 : End global routing;  0.217337s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (122.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 207, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 469, peak = 501.
PHY-1001 : End build detailed router design. 3.887501s wall, 3.796875s user + 0.109375s system = 3.906250s CPU (100.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33952, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.570939s wall, 1.531250s user + 0.015625s system = 1.546875s CPU (98.5%)

PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End phase 1; 1.576713s wall, 1.546875s user + 0.015625s system = 1.562500s CPU (99.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179200, over cnt = 39(0%), over = 39, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End initial routed; 1.244772s wall, 2.218750s user + 0.234375s system = 2.453125s CPU (197.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1802(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.394   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.433009s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (97.4%)

PHY-1001 : Current memory(MB): used = 537, reserve = 506, peak = 537.
PHY-1001 : End phase 2; 1.677869s wall, 2.640625s user + 0.234375s system = 2.875000s CPU (171.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179200, over cnt = 39(0%), over = 39, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.020642s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (151.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178912, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033539s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178888, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.029715s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 178896, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.023647s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (66.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1802(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.394   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.433524s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (100.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.191140s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (89.9%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.872481s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (98.5%)

PHY-1003 : Routed, final wirelength = 178896
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.011432s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (136.7%)

PHY-1001 : End detail routing;  8.243976s wall, 9.078125s user + 0.359375s system = 9.437500s CPU (114.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6593, tnet num: 2037, tinst num: 842, tnode num: 8937, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  9.305722s wall, 10.125000s user + 0.406250s system = 10.531250s CPU (113.2%)

RUN-1004 : used memory is 504 MB, reserved memory is 473 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      831   out of  19600    4.24%
#reg                     1076   out of  19600    5.49%
#le                      1532
  #lut only               456   out of   1532   29.77%
  #reg only               701   out of   1532   45.76%
  #lut&reg                375   out of   1532   24.48%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1532   |605     |226     |1107    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1139   |310     |134     |921     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |19      |7       |19      |0       |0       |
|    demodu                  |Demodulation                                     |539    |127     |58      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |0       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |16      |0       |28      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |90     |30      |21      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |91      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |101    |88      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |18     |15      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |47     |45      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |75      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1447  
    #2          2       316   
    #3          3       105   
    #4          4        17   
    #5        5-10       80   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6593, tnet num: 2037, tinst num: 842, tnode num: 8937, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 842
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2039, pip num: 14679
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1304 valid insts, and 39041 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.237750s wall, 18.453125s user + 0.109375s system = 18.562500s CPU (573.3%)

RUN-1004 : used memory is 522 MB, reserved memory is 492 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230906_114027.log"
