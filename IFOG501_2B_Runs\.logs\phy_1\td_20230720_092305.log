============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jul 20 09:23:05 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1655 instances
RUN-0007 : 379 luts, 1016 seqs, 138 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2196 nets
RUN-1001 : 1636 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1653 instances, 379 luts, 1016 seqs, 211 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7827, tnet num: 2194, tinst num: 1653, tnode num: 11088, tedge num: 13214.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.303889s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (97.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 541221
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1653.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 499721, overlap = 18
PHY-3002 : Step(2): len = 411972, overlap = 15.75
PHY-3002 : Step(3): len = 344517, overlap = 11.25
PHY-3002 : Step(4): len = 327532, overlap = 13.5
PHY-3002 : Step(5): len = 320323, overlap = 15.75
PHY-3002 : Step(6): len = 310323, overlap = 18
PHY-3002 : Step(7): len = 302132, overlap = 18
PHY-3002 : Step(8): len = 294010, overlap = 18
PHY-3002 : Step(9): len = 287235, overlap = 20.25
PHY-3002 : Step(10): len = 276883, overlap = 20.25
PHY-3002 : Step(11): len = 272073, overlap = 20.25
PHY-3002 : Step(12): len = 265975, overlap = 20.25
PHY-3002 : Step(13): len = 261311, overlap = 20.25
PHY-3002 : Step(14): len = 253904, overlap = 20.25
PHY-3002 : Step(15): len = 249679, overlap = 20.25
PHY-3002 : Step(16): len = 244799, overlap = 20.25
PHY-3002 : Step(17): len = 240548, overlap = 20.25
PHY-3002 : Step(18): len = 233731, overlap = 20.25
PHY-3002 : Step(19): len = 229760, overlap = 20.25
PHY-3002 : Step(20): len = 225299, overlap = 20.25
PHY-3002 : Step(21): len = 219727, overlap = 20.25
PHY-3002 : Step(22): len = 213670, overlap = 20.25
PHY-3002 : Step(23): len = 211304, overlap = 20.25
PHY-3002 : Step(24): len = 205106, overlap = 20.25
PHY-3002 : Step(25): len = 200020, overlap = 20.25
PHY-3002 : Step(26): len = 196358, overlap = 20.25
PHY-3002 : Step(27): len = 193308, overlap = 20.25
PHY-3002 : Step(28): len = 183367, overlap = 20.25
PHY-3002 : Step(29): len = 179911, overlap = 20.25
PHY-3002 : Step(30): len = 177245, overlap = 20.25
PHY-3002 : Step(31): len = 167710, overlap = 20.25
PHY-3002 : Step(32): len = 158948, overlap = 20.25
PHY-3002 : Step(33): len = 157870, overlap = 20.25
PHY-3002 : Step(34): len = 145604, overlap = 20.25
PHY-3002 : Step(35): len = 113255, overlap = 20.25
PHY-3002 : Step(36): len = 111814, overlap = 20.25
PHY-3002 : Step(37): len = 109307, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.24257e-05
PHY-3002 : Step(38): len = 111833, overlap = 11.25
PHY-3002 : Step(39): len = 111146, overlap = 9
PHY-3002 : Step(40): len = 109228, overlap = 13.5
PHY-3002 : Step(41): len = 106647, overlap = 13.5
PHY-3002 : Step(42): len = 103374, overlap = 15.75
PHY-3002 : Step(43): len = 102287, overlap = 6.75
PHY-3002 : Step(44): len = 99758.2, overlap = 15.75
PHY-3002 : Step(45): len = 97677.7, overlap = 11.25
PHY-3002 : Step(46): len = 95641.4, overlap = 11.25
PHY-3002 : Step(47): len = 95333.3, overlap = 6.75
PHY-3002 : Step(48): len = 92703.8, overlap = 13.5
PHY-3002 : Step(49): len = 91265.5, overlap = 11.25
PHY-3002 : Step(50): len = 88328.2, overlap = 11.25
PHY-3002 : Step(51): len = 87705.2, overlap = 13.5
PHY-3002 : Step(52): len = 86468.6, overlap = 11.25
PHY-3002 : Step(53): len = 84519, overlap = 13.5
PHY-3002 : Step(54): len = 79010.3, overlap = 15.75
PHY-3002 : Step(55): len = 77837.4, overlap = 11.25
PHY-3002 : Step(56): len = 76990.7, overlap = 9
PHY-3002 : Step(57): len = 76430.2, overlap = 11.25
PHY-3002 : Step(58): len = 75931.9, overlap = 11.25
PHY-3002 : Step(59): len = 74707.2, overlap = 11.25
PHY-3002 : Step(60): len = 73255.7, overlap = 15.75
PHY-3002 : Step(61): len = 71275, overlap = 9
PHY-3002 : Step(62): len = 70020.7, overlap = 9
PHY-3002 : Step(63): len = 67931.2, overlap = 13.5
PHY-3002 : Step(64): len = 66323.5, overlap = 11.25
PHY-3002 : Step(65): len = 65370.1, overlap = 11.25
PHY-3002 : Step(66): len = 65011.7, overlap = 11.25
PHY-3002 : Step(67): len = 64309.3, overlap = 9
PHY-3002 : Step(68): len = 63522.5, overlap = 11.25
PHY-3002 : Step(69): len = 62968.9, overlap = 9
PHY-3002 : Step(70): len = 61796.2, overlap = 11.25
PHY-3002 : Step(71): len = 61369.1, overlap = 11.25
PHY-3002 : Step(72): len = 61319.6, overlap = 11.25
PHY-3002 : Step(73): len = 60938, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000184851
PHY-3002 : Step(74): len = 60768.8, overlap = 11.25
PHY-3002 : Step(75): len = 60824.8, overlap = 11.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000369703
PHY-3002 : Step(76): len = 60998, overlap = 11.25
PHY-3002 : Step(77): len = 61028.7, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006238s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068224s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(78): len = 64630.4, overlap = 1.125
PHY-3002 : Step(79): len = 63257.3, overlap = 1.125
PHY-3002 : Step(80): len = 62755.4, overlap = 1.1875
PHY-3002 : Step(81): len = 61823.5, overlap = 1.0625
PHY-3002 : Step(82): len = 60834.4, overlap = 1.5625
PHY-3002 : Step(83): len = 59364.6, overlap = 2.125
PHY-3002 : Step(84): len = 58345.6, overlap = 2.5
PHY-3002 : Step(85): len = 57210.4, overlap = 3.0625
PHY-3002 : Step(86): len = 56317.3, overlap = 3.75
PHY-3002 : Step(87): len = 54461.7, overlap = 5.4375
PHY-3002 : Step(88): len = 53631.4, overlap = 5.4375
PHY-3002 : Step(89): len = 53266.6, overlap = 5.4375
PHY-3002 : Step(90): len = 52950.9, overlap = 5.1875
PHY-3002 : Step(91): len = 52643.8, overlap = 4.4375
PHY-3002 : Step(92): len = 52191.4, overlap = 4.6875
PHY-3002 : Step(93): len = 52057.5, overlap = 4.8125
PHY-3002 : Step(94): len = 51487.5, overlap = 6.3125
PHY-3002 : Step(95): len = 50505.3, overlap = 9.125
PHY-3002 : Step(96): len = 49650.7, overlap = 9.84375
PHY-3002 : Step(97): len = 49112.7, overlap = 9.3125
PHY-3002 : Step(98): len = 48596.6, overlap = 8.875
PHY-3002 : Step(99): len = 48193.7, overlap = 9.4375
PHY-3002 : Step(100): len = 47651.6, overlap = 6.8125
PHY-3002 : Step(101): len = 47471.7, overlap = 6.875
PHY-3002 : Step(102): len = 47136.1, overlap = 7.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000292629
PHY-3002 : Step(103): len = 47000.3, overlap = 7.9375
PHY-3002 : Step(104): len = 46964.5, overlap = 9.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000585258
PHY-3002 : Step(105): len = 46879.6, overlap = 9.0625
PHY-3002 : Step(106): len = 47091.9, overlap = 9.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071511s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.23084e-05
PHY-3002 : Step(107): len = 47196.5, overlap = 61.4688
PHY-3002 : Step(108): len = 47292, overlap = 60.9688
PHY-3002 : Step(109): len = 47592.7, overlap = 60.5312
PHY-3002 : Step(110): len = 47767.5, overlap = 60.0625
PHY-3002 : Step(111): len = 48229, overlap = 60.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000144617
PHY-3002 : Step(112): len = 48358.4, overlap = 60.3438
PHY-3002 : Step(113): len = 49060.9, overlap = 53.6875
PHY-3002 : Step(114): len = 49299.4, overlap = 50.9375
PHY-3002 : Step(115): len = 49542.9, overlap = 51.0625
PHY-3002 : Step(116): len = 49711.4, overlap = 50.2812
PHY-3002 : Step(117): len = 49832.7, overlap = 49.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000289234
PHY-3002 : Step(118): len = 50049.5, overlap = 45.9688
PHY-3002 : Step(119): len = 50116.8, overlap = 45.7812
PHY-3002 : Step(120): len = 51053, overlap = 44.7188
PHY-3002 : Step(121): len = 51441.2, overlap = 42.5938
PHY-3002 : Step(122): len = 51417.4, overlap = 40.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7827, tnet num: 2194, tinst num: 1653, tnode num: 11088, tedge num: 13214.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 94.31 peak overflow 2.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2196.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56056, over cnt = 265(0%), over = 1169, worst = 22
PHY-1001 : End global iterations;  0.076080s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.7%)

PHY-1001 : Congestion index: top1 = 47.31, top5 = 26.70, top10 = 17.06, top15 = 12.04.
PHY-1001 : End incremental global routing;  0.127789s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (97.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077543s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (80.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.238927s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (98.1%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1738/2196.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56056, over cnt = 265(0%), over = 1169, worst = 22
PHY-1002 : len = 64272, over cnt = 185(0%), over = 430, worst = 16
PHY-1002 : len = 68568, over cnt = 43(0%), over = 47, worst = 2
PHY-1002 : len = 69448, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 69528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113821s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (123.5%)

PHY-1001 : Congestion index: top1 = 40.28, top5 = 26.15, top10 = 18.81, top15 = 13.98.
OPT-1001 : End congestion update;  0.160759s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (106.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063065s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.226684s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (103.4%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.761230s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (98.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 379 LUT to BLE ...
SYN-4008 : Packed 379 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 102 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 725 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1104/1422 primitive instances ...
PHY-3001 : End packing;  0.057429s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2021 nets
RUN-1001 : 1470 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 839 instances, 792 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51498.6, Over = 69.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2019, tinst num: 839, tnode num: 8926, tedge num: 11557.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.336578s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.83336e-05
PHY-3002 : Step(123): len = 51101.4, overlap = 72.5
PHY-3002 : Step(124): len = 50895.5, overlap = 72.75
PHY-3002 : Step(125): len = 50511.8, overlap = 71.25
PHY-3002 : Step(126): len = 50266.9, overlap = 72.5
PHY-3002 : Step(127): len = 50255.3, overlap = 72.5
PHY-3002 : Step(128): len = 50044.8, overlap = 73
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.66672e-05
PHY-3002 : Step(129): len = 50367.4, overlap = 72.75
PHY-3002 : Step(130): len = 50548.1, overlap = 72
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.97615e-05
PHY-3002 : Step(131): len = 50988.3, overlap = 70.25
PHY-3002 : Step(132): len = 52176.6, overlap = 65
PHY-3002 : Step(133): len = 53120.9, overlap = 59.75
PHY-3002 : Step(134): len = 53238.8, overlap = 58.5
PHY-3002 : Step(135): len = 53440.8, overlap = 53.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.099816s wall, 0.125000s user + 0.140625s system = 0.265625s CPU (266.1%)

PHY-3001 : Trial Legalized: Len = 67461.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059156s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000609958
PHY-3002 : Step(136): len = 64099.4, overlap = 7.25
PHY-3002 : Step(137): len = 62042.7, overlap = 12.75
PHY-3002 : Step(138): len = 60380.6, overlap = 18.75
PHY-3002 : Step(139): len = 59141.6, overlap = 21.5
PHY-3002 : Step(140): len = 58499.6, overlap = 25.25
PHY-3002 : Step(141): len = 58019.3, overlap = 27
PHY-3002 : Step(142): len = 57623.2, overlap = 29.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00121992
PHY-3002 : Step(143): len = 57963.4, overlap = 27.75
PHY-3002 : Step(144): len = 58076.4, overlap = 28.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00243983
PHY-3002 : Step(145): len = 58267.1, overlap = 27
PHY-3002 : Step(146): len = 58332.2, overlap = 26.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005208s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62576.2, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005827s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 5 instances has been re-located, deltaX = 1, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 62648.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2019, tinst num: 839, tnode num: 8926, tedge num: 11557.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 72/2021.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69112, over cnt = 163(0%), over = 244, worst = 6
PHY-1002 : len = 70072, over cnt = 107(0%), over = 123, worst = 3
PHY-1002 : len = 71144, over cnt = 21(0%), over = 21, worst = 1
PHY-1002 : len = 71464, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.144859s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (118.6%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.91, top10 = 17.75, top15 = 14.12.
PHY-1001 : End incremental global routing;  0.201573s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (116.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073752s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.306393s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (107.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2021.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007326s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (213.3%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.91, top10 = 17.75, top15 = 14.12.
OPT-1001 : End congestion update;  0.060049s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054368s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 801 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 839 instances, 792 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62680.6, Over = 0
PHY-3001 : End spreading;  0.005796s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62680.6, Over = 0
PHY-3001 : End incremental legalization;  0.037771s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (82.7%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.166218s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (103.4%)

OPT-1001 : Current memory(MB): used = 225, reserve = 191, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054511s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1779/2021.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71544, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71544, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.031161s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.3%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 22.91, top10 = 17.76, top15 = 14.13.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056286s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.990963s wall, 1.015625s user + 0.078125s system = 1.093750s CPU (110.4%)

RUN-1003 : finish command "place" in  5.890600s wall, 9.437500s user + 2.937500s system = 12.375000s CPU (210.1%)

RUN-1004 : used memory is 201 MB, reserved memory is 166 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2021 nets
RUN-1001 : 1470 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2019, tinst num: 839, tnode num: 8926, tedge num: 11557.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68456, over cnt = 168(0%), over = 243, worst = 6
PHY-1002 : len = 69632, over cnt = 97(0%), over = 112, worst = 3
PHY-1002 : len = 70752, over cnt = 17(0%), over = 17, worst = 1
PHY-1002 : len = 71024, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71072, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.152837s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (122.7%)

PHY-1001 : Congestion index: top1 = 31.88, top5 = 22.90, top10 = 17.72, top15 = 14.06.
PHY-1001 : End global routing;  0.208847s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (127.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 204, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 497, reserve = 466, peak = 497.
PHY-1001 : End build detailed router design. 3.433365s wall, 3.421875s user + 0.015625s system = 3.437500s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33192, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.502457s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 530, reserve = 499, peak = 531.
PHY-1001 : End phase 1; 1.510326s wall, 1.515625s user + 0.000000s system = 1.515625s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 54% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179704, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 532.
PHY-1001 : End initial routed; 1.205344s wall, 2.515625s user + 0.062500s system = 2.578125s CPU (213.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1799(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.122   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.411906s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (98.6%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End phase 2; 1.617361s wall, 2.921875s user + 0.062500s system = 2.984375s CPU (184.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179704, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016876s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (92.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179648, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026298s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (178.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179704, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.029258s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (160.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 179736, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.026348s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (118.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1799(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.122   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.411650s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (102.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.188693s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End phase 3; 0.834903s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (104.8%)

PHY-1003 : Routed, final wirelength = 179736
PHY-1001 : Current memory(MB): used = 549, reserve = 519, peak = 549.
PHY-1001 : End export database. 0.011193s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.604198s wall, 8.921875s user + 0.078125s system = 9.000000s CPU (118.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2019, tinst num: 839, tnode num: 8926, tedge num: 11557.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.595195s wall, 9.953125s user + 0.093750s system = 10.046875s CPU (116.9%)

RUN-1004 : used memory is 503 MB, reserved memory is 475 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      807   out of  19600    4.12%
#reg                     1075   out of  19600    5.48%
#le                      1532
  #lut only               457   out of   1532   29.83%
  #reg only               725   out of   1532   47.32%
  #lut&reg                350   out of   1532   22.85%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         468
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    46
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1532   |596     |211     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1152   |302     |130     |927     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |22      |9       |21      |0       |0       |
|    demodu                  |Demodulation                                     |537    |127     |58      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |159    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |2       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |14      |0       |27      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |102    |24      |15      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |91      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |21      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |108    |96      |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |50     |50      |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1434  
    #2          2       305   
    #3          3       114   
    #4          4        12   
    #5        5-10       84   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2019, tinst num: 839, tnode num: 8926, tedge num: 11557.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 839
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2021, pip num: 14779
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1281 valid insts, and 38908 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.428396s wall, 18.781250s user + 0.015625s system = 18.796875s CPU (548.3%)

RUN-1004 : used memory is 517 MB, reserved memory is 489 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230720_092305.log"
