============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jul 20 09:25:56 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1646 instances
RUN-0007 : 379 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2217 nets
RUN-1001 : 1656 nets have 2 pins
RUN-1001 : 444 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1644 instances, 379 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7848, tnet num: 2215, tinst num: 1644, tnode num: 11087, tedge num: 13251.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.284203s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (104.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 618696
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1644.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 491484, overlap = 18
PHY-3002 : Step(2): len = 377863, overlap = 15.75
PHY-3002 : Step(3): len = 356376, overlap = 13.5
PHY-3002 : Step(4): len = 345244, overlap = 15.75
PHY-3002 : Step(5): len = 335989, overlap = 18
PHY-3002 : Step(6): len = 325528, overlap = 20.25
PHY-3002 : Step(7): len = 318663, overlap = 20.25
PHY-3002 : Step(8): len = 310957, overlap = 18
PHY-3002 : Step(9): len = 300378, overlap = 18
PHY-3002 : Step(10): len = 294629, overlap = 18
PHY-3002 : Step(11): len = 288708, overlap = 20.25
PHY-3002 : Step(12): len = 278930, overlap = 20.25
PHY-3002 : Step(13): len = 273701, overlap = 20.25
PHY-3002 : Step(14): len = 269449, overlap = 20.25
PHY-3002 : Step(15): len = 262606, overlap = 20.25
PHY-3002 : Step(16): len = 257288, overlap = 20.25
PHY-3002 : Step(17): len = 252586, overlap = 20.25
PHY-3002 : Step(18): len = 247186, overlap = 20.25
PHY-3002 : Step(19): len = 240699, overlap = 20.25
PHY-3002 : Step(20): len = 236637, overlap = 20.25
PHY-3002 : Step(21): len = 231529, overlap = 20.25
PHY-3002 : Step(22): len = 226557, overlap = 20.25
PHY-3002 : Step(23): len = 221744, overlap = 20.25
PHY-3002 : Step(24): len = 218009, overlap = 20.25
PHY-3002 : Step(25): len = 209303, overlap = 20.25
PHY-3002 : Step(26): len = 204473, overlap = 20.25
PHY-3002 : Step(27): len = 202113, overlap = 20.25
PHY-3002 : Step(28): len = 191140, overlap = 20.25
PHY-3002 : Step(29): len = 172279, overlap = 20.25
PHY-3002 : Step(30): len = 169701, overlap = 20.25
PHY-3002 : Step(31): len = 166708, overlap = 20.25
PHY-3002 : Step(32): len = 133697, overlap = 18
PHY-3002 : Step(33): len = 128018, overlap = 18
PHY-3002 : Step(34): len = 126385, overlap = 18
PHY-3002 : Step(35): len = 122344, overlap = 15.75
PHY-3002 : Step(36): len = 119191, overlap = 18
PHY-3002 : Step(37): len = 115879, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.55734e-05
PHY-3002 : Step(38): len = 117474, overlap = 13.5
PHY-3002 : Step(39): len = 116567, overlap = 11.25
PHY-3002 : Step(40): len = 115350, overlap = 13.5
PHY-3002 : Step(41): len = 113825, overlap = 15.75
PHY-3002 : Step(42): len = 111194, overlap = 9
PHY-3002 : Step(43): len = 108439, overlap = 4.5
PHY-3002 : Step(44): len = 104845, overlap = 6.75
PHY-3002 : Step(45): len = 102613, overlap = 13.5
PHY-3002 : Step(46): len = 99997.6, overlap = 11.25
PHY-3002 : Step(47): len = 97939.8, overlap = 6.75
PHY-3002 : Step(48): len = 94950, overlap = 6.75
PHY-3002 : Step(49): len = 93394, overlap = 11.25
PHY-3002 : Step(50): len = 91358.9, overlap = 9
PHY-3002 : Step(51): len = 90036.1, overlap = 11.25
PHY-3002 : Step(52): len = 87234.2, overlap = 9
PHY-3002 : Step(53): len = 86643, overlap = 11.25
PHY-3002 : Step(54): len = 83325.5, overlap = 13.5
PHY-3002 : Step(55): len = 82202.8, overlap = 11.25
PHY-3002 : Step(56): len = 82029.5, overlap = 11.25
PHY-3002 : Step(57): len = 81262.1, overlap = 6.75
PHY-3002 : Step(58): len = 80208.3, overlap = 11.25
PHY-3002 : Step(59): len = 78542.8, overlap = 13.5
PHY-3002 : Step(60): len = 77432.9, overlap = 11.25
PHY-3002 : Step(61): len = 76965.7, overlap = 11.25
PHY-3002 : Step(62): len = 75612.7, overlap = 4.5
PHY-3002 : Step(63): len = 73360.4, overlap = 6.75
PHY-3002 : Step(64): len = 72468.1, overlap = 9
PHY-3002 : Step(65): len = 70174.5, overlap = 11.25
PHY-3002 : Step(66): len = 69294.3, overlap = 11.25
PHY-3002 : Step(67): len = 68849.1, overlap = 11.25
PHY-3002 : Step(68): len = 68060.9, overlap = 5
PHY-3002 : Step(69): len = 66743.3, overlap = 5
PHY-3002 : Step(70): len = 65896.2, overlap = 9.5
PHY-3002 : Step(71): len = 64449.9, overlap = 11.75
PHY-3002 : Step(72): len = 63141.4, overlap = 11.75
PHY-3002 : Step(73): len = 62890.7, overlap = 11.75
PHY-3002 : Step(74): len = 62554.1, overlap = 11.75
PHY-3002 : Step(75): len = 61959.7, overlap = 7.25
PHY-3002 : Step(76): len = 61529.3, overlap = 9.5
PHY-3002 : Step(77): len = 60858.4, overlap = 9.5
PHY-3002 : Step(78): len = 60314.1, overlap = 9.5
PHY-3002 : Step(79): len = 59864.7, overlap = 11.5
PHY-3002 : Step(80): len = 59386.2, overlap = 6.75
PHY-3002 : Step(81): len = 58429.7, overlap = 11.25
PHY-3002 : Step(82): len = 57534.8, overlap = 11.25
PHY-3002 : Step(83): len = 57402.2, overlap = 9
PHY-3002 : Step(84): len = 57054.6, overlap = 6.75
PHY-3002 : Step(85): len = 56181.5, overlap = 6.75
PHY-3002 : Step(86): len = 54848.8, overlap = 9.25
PHY-3002 : Step(87): len = 54202.3, overlap = 11.5
PHY-3002 : Step(88): len = 54105.4, overlap = 11.5
PHY-3002 : Step(89): len = 54167.2, overlap = 9.5
PHY-3002 : Step(90): len = 53567, overlap = 12.5
PHY-3002 : Step(91): len = 52605.2, overlap = 10.25
PHY-3002 : Step(92): len = 52135.4, overlap = 10.25
PHY-3002 : Step(93): len = 52231.2, overlap = 12.5
PHY-3002 : Step(94): len = 51920.6, overlap = 10.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000151147
PHY-3002 : Step(95): len = 51806.7, overlap = 10.25
PHY-3002 : Step(96): len = 51822.8, overlap = 10.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000302294
PHY-3002 : Step(97): len = 52009.6, overlap = 8
PHY-3002 : Step(98): len = 52072.3, overlap = 8
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007144s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.078606s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00226406
PHY-3002 : Step(99): len = 54991, overlap = 11.125
PHY-3002 : Step(100): len = 54065.1, overlap = 11.25
PHY-3002 : Step(101): len = 53895.2, overlap = 11.0625
PHY-3002 : Step(102): len = 53377.8, overlap = 12.125
PHY-3002 : Step(103): len = 52872.1, overlap = 10.125
PHY-3002 : Step(104): len = 52354.9, overlap = 8.9375
PHY-3002 : Step(105): len = 51851.1, overlap = 8.5625
PHY-3002 : Step(106): len = 51070.2, overlap = 9.3125
PHY-3002 : Step(107): len = 50598.8, overlap = 10.125
PHY-3002 : Step(108): len = 50023.2, overlap = 9.0625
PHY-3002 : Step(109): len = 49744.6, overlap = 10.4375
PHY-3002 : Step(110): len = 49520.6, overlap = 10.5938
PHY-3002 : Step(111): len = 49193.3, overlap = 10.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067921s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000150229
PHY-3002 : Step(112): len = 49369.9, overlap = 55.0312
PHY-3002 : Step(113): len = 49818, overlap = 55.875
PHY-3002 : Step(114): len = 49816.8, overlap = 53.5938
PHY-3002 : Step(115): len = 50069.9, overlap = 52.6562
PHY-3002 : Step(116): len = 50180.2, overlap = 50.9375
PHY-3002 : Step(117): len = 50153.6, overlap = 49.4062
PHY-3002 : Step(118): len = 50236.3, overlap = 48.375
PHY-3002 : Step(119): len = 50146.3, overlap = 48.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000300457
PHY-3002 : Step(120): len = 50111.9, overlap = 47.375
PHY-3002 : Step(121): len = 50697.2, overlap = 43.125
PHY-3002 : Step(122): len = 51119.1, overlap = 43.7812
PHY-3002 : Step(123): len = 51362.3, overlap = 43.1875
PHY-3002 : Step(124): len = 51602.1, overlap = 40.9375
PHY-3002 : Step(125): len = 51587.1, overlap = 40.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000600915
PHY-3002 : Step(126): len = 51556.3, overlap = 41.0625
PHY-3002 : Step(127): len = 51587.9, overlap = 41
PHY-3002 : Step(128): len = 51640.6, overlap = 40.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00120183
PHY-3002 : Step(129): len = 52139.3, overlap = 39.6875
PHY-3002 : Step(130): len = 52660.8, overlap = 39.1875
PHY-3002 : Step(131): len = 52884, overlap = 39.3125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00240366
PHY-3002 : Step(132): len = 52690.7, overlap = 39.3125
PHY-3002 : Step(133): len = 52690.7, overlap = 39.3125
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00425176
PHY-3002 : Step(134): len = 53019.2, overlap = 38.9375
PHY-3002 : Step(135): len = 53128.9, overlap = 38.125
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00850353
PHY-3002 : Step(136): len = 53215.5, overlap = 37.7812
PHY-3002 : Step(137): len = 53259.4, overlap = 37.6562
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.0137587
PHY-3002 : Step(138): len = 53344.3, overlap = 37.9062
PHY-3002 : Step(139): len = 53382.9, overlap = 38.1562
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0275174
PHY-3002 : Step(140): len = 53434.7, overlap = 34.5625
PHY-3002 : Step(141): len = 53429.8, overlap = 33.9688
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.0445232
PHY-3002 : Step(142): len = 53497, overlap = 33.7188
PHY-3002 : Step(143): len = 53497, overlap = 33.7188
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.0890464
PHY-3002 : Step(144): len = 53512.2, overlap = 33.4688
PHY-3002 : Step(145): len = 53512.2, overlap = 33.4688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7848, tnet num: 2215, tinst num: 1644, tnode num: 11087, tedge num: 13251.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 97.53 peak overflow 5.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56312, over cnt = 239(0%), over = 997, worst = 14
PHY-1001 : End global iterations;  0.092207s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (118.6%)

PHY-1001 : Congestion index: top1 = 40.13, top5 = 25.00, top10 = 16.48, top15 = 11.72.
PHY-1001 : End incremental global routing;  0.149607s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068700s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.258278s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (102.8%)

OPT-1001 : Current memory(MB): used = 214, reserve = 179, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1709/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56312, over cnt = 239(0%), over = 997, worst = 14
PHY-1002 : len = 60760, over cnt = 153(0%), over = 444, worst = 12
PHY-1002 : len = 65504, over cnt = 30(0%), over = 69, worst = 10
PHY-1002 : len = 66064, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 66080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109340s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (114.3%)

PHY-1001 : Congestion index: top1 = 35.17, top5 = 24.66, top10 = 17.73, top15 = 13.26.
OPT-1001 : End congestion update;  0.161268s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (106.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073666s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.240843s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (103.8%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.852254s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (104.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 379 LUT to BLE ...
SYN-4008 : Packed 379 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 104 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 699 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1078/1411 primitive instances ...
PHY-3001 : End packing;  0.062101s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 846 instances
RUN-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-1001 : 1485 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 844 instances, 797 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53760, Over = 58.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6607, tnet num: 2040, tinst num: 844, tnode num: 8949, tedge num: 11602.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.422663s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.4436e-05
PHY-3002 : Step(146): len = 52711.8, overlap = 62
PHY-3002 : Step(147): len = 52173.6, overlap = 59.5
PHY-3002 : Step(148): len = 51845.2, overlap = 59.25
PHY-3002 : Step(149): len = 51875, overlap = 60
PHY-3002 : Step(150): len = 51776.2, overlap = 60.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.8872e-05
PHY-3002 : Step(151): len = 51999.4, overlap = 59.5
PHY-3002 : Step(152): len = 52337.4, overlap = 58.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000129467
PHY-3002 : Step(153): len = 52801, overlap = 57.5
PHY-3002 : Step(154): len = 54571.2, overlap = 51
PHY-3002 : Step(155): len = 55490.2, overlap = 48.25
PHY-3002 : Step(156): len = 54821.1, overlap = 46.75
PHY-3002 : Step(157): len = 54821.1, overlap = 46.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.114859s wall, 0.109375s user + 0.156250s system = 0.265625s CPU (231.3%)

PHY-3001 : Trial Legalized: Len = 68405.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064209s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000725423
PHY-3002 : Step(158): len = 65969.9, overlap = 5
PHY-3002 : Step(159): len = 63597.5, overlap = 12.25
PHY-3002 : Step(160): len = 62168.4, overlap = 15.75
PHY-3002 : Step(161): len = 61205.2, overlap = 19.5
PHY-3002 : Step(162): len = 60071.2, overlap = 22
PHY-3002 : Step(163): len = 59571.8, overlap = 24
PHY-3002 : Step(164): len = 59388.1, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00145085
PHY-3002 : Step(165): len = 59718, overlap = 23
PHY-3002 : Step(166): len = 59828.5, overlap = 22.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00290169
PHY-3002 : Step(167): len = 60024.7, overlap = 22.5
PHY-3002 : Step(168): len = 60125.1, overlap = 23
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004935s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (316.6%)

PHY-3001 : Legalized: Len = 64969.6, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005853s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 9, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 65123.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6607, tnet num: 2040, tinst num: 844, tnode num: 8949, tedge num: 11602.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 130/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72296, over cnt = 150(0%), over = 216, worst = 7
PHY-1002 : len = 72968, over cnt = 85(0%), over = 104, worst = 3
PHY-1002 : len = 73992, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 74096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.149155s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (104.8%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.91, top10 = 17.75, top15 = 14.23.
PHY-1001 : End incremental global routing;  0.220843s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (106.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.079925s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.332715s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (103.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 185, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1808/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.016675s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (93.7%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.91, top10 = 17.75, top15 = 14.23.
OPT-1001 : End congestion update;  0.067974s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068224s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.137774s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 186, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049091s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1808/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008617s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (181.3%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.91, top10 = 17.75, top15 = 14.23.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063798s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.012409s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (100.3%)

RUN-1003 : finish command "place" in  6.087846s wall, 9.593750s user + 3.093750s system = 12.687500s CPU (208.4%)

RUN-1004 : used memory is 210 MB, reserved memory is 174 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 846 instances
RUN-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-1001 : 1485 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6607, tnet num: 2040, tinst num: 844, tnode num: 8949, tedge num: 11602.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71040, over cnt = 137(0%), over = 219, worst = 6
PHY-1002 : len = 72064, over cnt = 77(0%), over = 97, worst = 3
PHY-1002 : len = 73224, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.182624s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (171.1%)

PHY-1001 : Congestion index: top1 = 31.23, top5 = 22.45, top10 = 17.53, top15 = 14.06.
PHY-1001 : End global routing;  0.241671s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (148.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 203, peak = 251.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 501, reserve = 470, peak = 501.
PHY-1001 : End build detailed router design. 4.002357s wall, 3.921875s user + 0.078125s system = 4.000000s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34384, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.558234s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 533, reserve = 504, peak = 533.
PHY-1001 : End phase 1; 1.563845s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (98.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184456, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 505, peak = 534.
PHY-1001 : End initial routed; 1.283077s wall, 2.218750s user + 0.171875s system = 2.390625s CPU (186.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1806(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.400   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.436644s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 537, reserve = 506, peak = 537.
PHY-1001 : End phase 2; 1.719814s wall, 2.656250s user + 0.171875s system = 2.828125s CPU (164.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184456, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015328s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184376, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 1; 0.033558s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (186.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1806(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.400   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.436997s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.234929s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.851194s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (104.6%)

PHY-1003 : Routed, final wirelength = 184376
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.013893s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (112.5%)

PHY-1001 : End detail routing;  8.365426s wall, 9.218750s user + 0.281250s system = 9.500000s CPU (113.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6607, tnet num: 2040, tinst num: 844, tnode num: 8949, tedge num: 11602.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  9.557430s wall, 10.484375s user + 0.328125s system = 10.812500s CPU (113.1%)

RUN-1004 : used memory is 526 MB, reserved memory is 495 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      836   out of  19600    4.27%
#reg                     1075   out of  19600    5.48%
#le                      1535
  #lut only               460   out of   1535   29.97%
  #reg only               699   out of   1535   45.54%
  #lut&reg                376   out of   1535   24.50%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1535   |610     |226     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1139   |308     |136     |928     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |30     |21      |9       |21      |0       |0       |
|    demodu                  |Demodulation                                     |538    |128     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |4       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |94     |39      |21      |90      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |85      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |23     |18      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |102    |91      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |29     |21      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |51     |51      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |217    |172     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1449  
    #2          2       313   
    #3          3       112   
    #4          4        12   
    #5        5-10       82   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6607, tnet num: 2040, tinst num: 844, tnode num: 8949, tedge num: 11602.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 844
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2042, pip num: 14821
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1312 valid insts, and 39356 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.522522s wall, 18.984375s user + 0.078125s system = 19.062500s CPU (541.2%)

RUN-1004 : used memory is 551 MB, reserved memory is 520 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230720_092556.log"
