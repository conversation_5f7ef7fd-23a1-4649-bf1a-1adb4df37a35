/************************************************************\
 **     Copyright (c) 2012-2023 Anlogic Inc.
 **  All Right Reserved.\
\************************************************************/
/************************************************************\
 ** Log	:	This file is generated by Anlogic IP Generator.
 ** File	:	D:/Project/IFOG501_2B_20M_PLL/al_ip/pll_phase_ctrl.v
 ** Date	:	2023 07 14
 ** TD version	:	5.6.71036
\************************************************************/

///////////////////////////////////////////////////////////////////////////////
//	Input frequency:             20.000MHz
//	Clock multiplication factor: 48
//	Clock division factor:       1
//	Clock information:
//		Clock name	| Frequency 	| Phase shift
//		C0        	| 120.000000MHZ	| 0  DEG     
//		C1        	| 60.000000 MHZ	| 0  DEG     
///////////////////////////////////////////////////////////////////////////////
`timescale 1 ns / 100 fs

module pll_phase_ctrl (
  refclk,
  reset,
  psclk,
  psdown,
  psstep,
  psclksel,
  extlock,
  psdone,
  clk0_out,
  clk1_out 
);

  input refclk;
  input reset;
  input psclk;
  input psdown;
  input psstep;
  input [2:0] psclksel;
  output extlock;
  output psdone;
  output clk0_out;
  output clk1_out;


  EG_PHY_PLL #(
    .DPHASE_SOURCE("ENABLE"),
    .DYNCFG("DISABLE"),
    .FIN("20.000"),
    .FEEDBK_MODE("NOCOMP"),
    .FEEDBK_PATH("VCO_PHASE_0"),
    .STDBY_ENABLE("DISABLE"),
    .PLLRST_ENA("ENABLE"),
    .SYNC_ENABLE("DISABLE"),
    .GMC_GAIN(2),
    .ICP_CURRENT(9),
    .KVCO(2),
    .LPF_CAPACITOR(1),
    .LPF_RESISTOR(8),
    .REFCLK_DIV(1),
    .FBCLK_DIV(48),
    .CLKC0_ENABLE("ENABLE"),
    .CLKC0_DIV(8),
    .CLKC0_CPHASE(7),
    .CLKC0_FPHASE(0),
    .CLKC1_ENABLE("ENABLE"),
    .CLKC1_DIV(16),
    .CLKC1_CPHASE(15),
    .CLKC1_FPHASE(0) 
  ) pll_inst (
    .refclk(refclk),
    .reset(reset),
    .stdby(1'b0),
    .extlock(extlock),
    .load_reg(1'b0),
    .psclk(psclk),
    .psdown(psdown),
    .psstep(psstep),
    .psclksel(psclksel),
    .psdone(psdone),
    .dclk(1'b0),
    .dcs(1'b0),
    .dwe(1'b0),
    .di(8'b00000000),
    .daddr(6'b000000),
    .do({open, open, open, open, open, open, open, open}),
    .fbclk(1'b0),
    .clkc({open, open, open, clk1_out, clk0_out}) 
  );

endmodule

