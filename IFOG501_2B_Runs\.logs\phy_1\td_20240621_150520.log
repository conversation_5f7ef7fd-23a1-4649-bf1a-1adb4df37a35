============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 15:05:20 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 38 trigger nets, 38 data nets.
KIT-1004 : Chipwatcher code = 0101111100011010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2442/23 useful/useless nets, 1478/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 2127/20 useful/useless nets, 1885/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 378 better
SYN-1014 : Optimize round 2
SYN-1032 : 1823/45 useful/useless nets, 1581/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1871/295 useful/useless nets, 1662/43 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 383 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 36 instances.
SYN-2501 : Optimize round 1, 74 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2299/5 useful/useless nets, 2090/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8863, tnet num: 2299, tinst num: 2089, tnode num: 11117, tedge num: 13569.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2299 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 514 instances into 223 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 372 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 120 adder to BLE ...
SYN-4008 : Packed 120 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.127712s wall, 1.062500s user + 0.046875s system = 1.109375s CPU (98.4%)

RUN-1004 : used memory is 143 MB, reserved memory is 98 MB, peak memory is 166 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (255 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1505 instances
RUN-0007 : 594 luts, 678 seqs, 116 mslices, 70 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1721 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 979 nets have 2 pins
RUN-1001 : 551 nets have [3 - 5] pins
RUN-1001 : 93 nets have [6 - 10] pins
RUN-1001 : 62 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     160     
RUN-1001 :   No   |  No   |  Yes  |     118     
RUN-1001 :   No   |  Yes  |  No   |     92      
RUN-1001 :   Yes  |  No   |  No   |     57      
RUN-1001 :   Yes  |  No   |  Yes  |     251     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 18
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1503 instances, 594 luts, 678 seqs, 186 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7596, tnet num: 1719, tinst num: 1503, tnode num: 9945, tedge num: 12542.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1719 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.139499s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 398703
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1503.
PHY-3001 : End clustering;  0.000022s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 321365, overlap = 76.5
PHY-3002 : Step(2): len = 259158, overlap = 76.5
PHY-3002 : Step(3): len = 223568, overlap = 76.5
PHY-3002 : Step(4): len = 199029, overlap = 76.5
PHY-3002 : Step(5): len = 177107, overlap = 76.5
PHY-3002 : Step(6): len = 159166, overlap = 76.5
PHY-3002 : Step(7): len = 144022, overlap = 76.5
PHY-3002 : Step(8): len = 131320, overlap = 76.5
PHY-3002 : Step(9): len = 115475, overlap = 76.5
PHY-3002 : Step(10): len = 104071, overlap = 76.5
PHY-3002 : Step(11): len = 93421.4, overlap = 76.5
PHY-3002 : Step(12): len = 85224.5, overlap = 74.25
PHY-3002 : Step(13): len = 80156.4, overlap = 72
PHY-3002 : Step(14): len = 72092.3, overlap = 76.5
PHY-3002 : Step(15): len = 67862.6, overlap = 76.5
PHY-3002 : Step(16): len = 63541.7, overlap = 77.5625
PHY-3002 : Step(17): len = 57492.3, overlap = 73.7812
PHY-3002 : Step(18): len = 54356.4, overlap = 74.0312
PHY-3002 : Step(19): len = 52471.6, overlap = 74.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.38458e-06
PHY-3002 : Step(20): len = 54119.5, overlap = 67.75
PHY-3002 : Step(21): len = 55770.5, overlap = 70.25
PHY-3002 : Step(22): len = 52780.2, overlap = 67.9375
PHY-3002 : Step(23): len = 51737, overlap = 70.2812
PHY-3002 : Step(24): len = 50808.2, overlap = 70.375
PHY-3002 : Step(25): len = 50594.2, overlap = 70.25
PHY-3002 : Step(26): len = 48277.8, overlap = 75.1562
PHY-3002 : Step(27): len = 47359.9, overlap = 71
PHY-3002 : Step(28): len = 46844.5, overlap = 66.4062
PHY-3002 : Step(29): len = 46264.4, overlap = 73.25
PHY-3002 : Step(30): len = 45295.9, overlap = 71
PHY-3002 : Step(31): len = 45064.5, overlap = 71
PHY-3002 : Step(32): len = 44349.7, overlap = 66.5
PHY-3002 : Step(33): len = 43281.2, overlap = 68.75
PHY-3002 : Step(34): len = 42347, overlap = 73.25
PHY-3002 : Step(35): len = 41503, overlap = 75.7188
PHY-3002 : Step(36): len = 40547.2, overlap = 75.8125
PHY-3002 : Step(37): len = 39945.2, overlap = 75.875
PHY-3002 : Step(38): len = 39211.4, overlap = 75.6875
PHY-3002 : Step(39): len = 38825.4, overlap = 75.5312
PHY-3002 : Step(40): len = 38163, overlap = 73.5
PHY-3002 : Step(41): len = 37516.1, overlap = 65.875
PHY-3002 : Step(42): len = 36882.6, overlap = 65.3125
PHY-3002 : Step(43): len = 36486.4, overlap = 69.875
PHY-3002 : Step(44): len = 35920.3, overlap = 68.5
PHY-3002 : Step(45): len = 35015.4, overlap = 74.3125
PHY-3002 : Step(46): len = 34428.1, overlap = 74.6875
PHY-3002 : Step(47): len = 34203.6, overlap = 72.6875
PHY-3002 : Step(48): len = 34192.1, overlap = 73
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 6.76916e-06
PHY-3002 : Step(49): len = 34015.7, overlap = 66.4062
PHY-3002 : Step(50): len = 34185.7, overlap = 66.3125
PHY-3002 : Step(51): len = 34185.7, overlap = 66.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 1.35383e-05
PHY-3002 : Step(52): len = 34196.5, overlap = 66.625
PHY-3002 : Step(53): len = 34218.1, overlap = 66.625
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 2.70766e-05
PHY-3002 : Step(54): len = 34240.2, overlap = 66.625
PHY-3002 : Step(55): len = 34288.9, overlap = 66.6875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 5.41533e-05
PHY-3002 : Step(56): len = 34318.3, overlap = 66.625
PHY-3002 : Step(57): len = 34318.3, overlap = 66.625
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.000108307
PHY-3002 : Step(58): len = 34378.2, overlap = 66.6875
PHY-3002 : Step(59): len = 34444.2, overlap = 68.8125
PHY-3002 : Step(60): len = 34663.4, overlap = 62.0625
PHY-3002 : Step(61): len = 34795.7, overlap = 62.0625
PHY-3002 : Step(62): len = 34847.1, overlap = 62
PHY-3002 : Step(63): len = 34835.7, overlap = 61.8125
PHY-3002 : Step(64): len = 34801.7, overlap = 61.6875
PHY-3002 : Step(65): len = 34759.5, overlap = 61.875
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.000180335
PHY-3002 : Step(66): len = 34720, overlap = 61.875
PHY-3002 : Step(67): len = 34688.7, overlap = 61.8125
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000329613
PHY-3002 : Step(68): len = 34671.8, overlap = 61.8125
PHY-3002 : Step(69): len = 34649.2, overlap = 61.8125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006537s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (239.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1719 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.043702s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (107.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.83398e-05
PHY-3002 : Step(70): len = 38674.7, overlap = 24.2812
PHY-3002 : Step(71): len = 38678.4, overlap = 24.1875
PHY-3002 : Step(72): len = 38790.7, overlap = 25.5
PHY-3002 : Step(73): len = 38946.5, overlap = 25
PHY-3002 : Step(74): len = 39197.8, overlap = 24.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.66796e-05
PHY-3002 : Step(75): len = 38936.5, overlap = 24.875
PHY-3002 : Step(76): len = 38914.9, overlap = 25.3125
PHY-3002 : Step(77): len = 38895.2, overlap = 25.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000153359
PHY-3002 : Step(78): len = 39037, overlap = 24.4062
PHY-3002 : Step(79): len = 39037, overlap = 24.4062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1719 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.043642s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (107.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.66121e-05
PHY-3002 : Step(80): len = 39350.7, overlap = 63.1562
PHY-3002 : Step(81): len = 39350.7, overlap = 63.1562
PHY-3002 : Step(82): len = 39424, overlap = 60.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.32242e-05
PHY-3002 : Step(83): len = 40496.6, overlap = 58.2812
PHY-3002 : Step(84): len = 40945.8, overlap = 58.2812
PHY-3002 : Step(85): len = 41160.5, overlap = 55.9062
PHY-3002 : Step(86): len = 41600.7, overlap = 57.0938
PHY-3002 : Step(87): len = 42618.5, overlap = 58.0312
PHY-3002 : Step(88): len = 42603.8, overlap = 58.75
PHY-3002 : Step(89): len = 42406.5, overlap = 57.8438
PHY-3002 : Step(90): len = 42268.8, overlap = 56.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000146448
PHY-3002 : Step(91): len = 42236.4, overlap = 56.4688
PHY-3002 : Step(92): len = 42236.4, overlap = 56.4688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000292897
PHY-3002 : Step(93): len = 43464.8, overlap = 55.7188
PHY-3002 : Step(94): len = 43728.7, overlap = 55
PHY-3002 : Step(95): len = 43731.5, overlap = 49.9062
PHY-3002 : Step(96): len = 43919.2, overlap = 49.25
PHY-3002 : Step(97): len = 43988.6, overlap = 48.9062
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000585794
PHY-3002 : Step(98): len = 44108.8, overlap = 47.6562
PHY-3002 : Step(99): len = 44227.6, overlap = 46.8125
PHY-3002 : Step(100): len = 44680.3, overlap = 41.5
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00117159
PHY-3002 : Step(101): len = 44744.8, overlap = 42.2188
PHY-3002 : Step(102): len = 44814.9, overlap = 42.9062
PHY-3002 : Step(103): len = 44918.7, overlap = 40.4062
PHY-3002 : Step(104): len = 44947, overlap = 40.25
PHY-3002 : Step(105): len = 44551.8, overlap = 40.3125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7596, tnet num: 1719, tinst num: 1503, tnode num: 9945, tedge num: 12542.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.44 peak overflow 3.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1721.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54576, over cnt = 234(0%), over = 807, worst = 17
PHY-1001 : End global iterations;  0.079319s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (157.6%)

PHY-1001 : Congestion index: top1 = 38.06, top5 = 22.76, top10 = 15.45, top15 = 11.24.
PHY-1001 : End incremental global routing;  0.130609s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (131.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1719 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050319s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1491 has valid locations, 34 needs to be replaced
PHY-3001 : design contains 1536 instances, 594 luts, 711 seqs, 186 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 44974
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7728, tnet num: 1752, tinst num: 1536, tnode num: 10176, tedge num: 12740.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1752 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.159774s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(106): len = 44919.7, overlap = 0.125
PHY-3002 : Step(107): len = 44919.7, overlap = 0.125
PHY-3002 : Step(108): len = 45047.3, overlap = 0.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1752 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.044291s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00205907
PHY-3002 : Step(109): len = 45075.5, overlap = 40.625
PHY-3002 : Step(110): len = 45075.5, overlap = 40.625
PHY-3001 : Final: Len = 45075.5, Over = 40.625
PHY-3001 : End incremental placement;  0.283963s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (110.0%)

OPT-1001 : Total overflow 90.44 peak overflow 3.34
OPT-1001 : End high-fanout net optimization;  0.500402s wall, 0.515625s user + 0.062500s system = 0.578125s CPU (115.5%)

OPT-1001 : Current memory(MB): used = 204, reserve = 158, peak = 204.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1161/1754.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55648, over cnt = 231(0%), over = 801, worst = 17
PHY-1002 : len = 60416, over cnt = 148(0%), over = 342, worst = 11
PHY-1002 : len = 63616, over cnt = 49(0%), over = 77, worst = 5
PHY-1002 : len = 64224, over cnt = 19(0%), over = 25, worst = 5
PHY-1002 : len = 64840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114820s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (136.1%)

PHY-1001 : Congestion index: top1 = 35.45, top5 = 23.50, top10 = 16.74, top15 = 12.53.
OPT-1001 : End congestion update;  0.158541s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (118.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1752 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044643s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.0%)

OPT-0007 : Start: WNS 3001 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.203436s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.2%)

OPT-1001 : Current memory(MB): used = 202, reserve = 156, peak = 204.
OPT-1001 : End physical optimization;  0.851765s wall, 0.890625s user + 0.062500s system = 0.953125s CPU (111.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 594 LUT to BLE ...
SYN-4008 : Packed 594 LUT and 199 SEQ to BLE.
SYN-4003 : Packing 512 remaining SEQ's ...
SYN-4005 : Packed 250 SEQ with LUT/SLICE
SYN-4006 : 171 single LUT's are left
SYN-4006 : 262 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 856/1223 primitive instances ...
PHY-3001 : End packing;  0.047596s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 711 instances
RUN-1001 : 332 mslices, 332 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1558 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 773 nets have 2 pins
RUN-1001 : 594 nets have [3 - 5] pins
RUN-1001 : 99 nets have [6 - 10] pins
RUN-1001 : 55 nets have [11 - 20] pins
RUN-1001 : 26 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 709 instances, 664 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-3001 : Cell area utilization is 8%
PHY-3001 : After packing: Len = 45691.8, Over = 55.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6765, tnet num: 1556, tinst num: 709, tnode num: 8573, tedge num: 11505.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.165090s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (104.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.59274e-05
PHY-3002 : Step(111): len = 44828.4, overlap = 57.25
PHY-3002 : Step(112): len = 44462.3, overlap = 57.75
PHY-3002 : Step(113): len = 44383.8, overlap = 57.25
PHY-3002 : Step(114): len = 44591.8, overlap = 55
PHY-3002 : Step(115): len = 44774.4, overlap = 53.5
PHY-3002 : Step(116): len = 44703.3, overlap = 52
PHY-3002 : Step(117): len = 44379.7, overlap = 50
PHY-3002 : Step(118): len = 44249.7, overlap = 48.5
PHY-3002 : Step(119): len = 43928, overlap = 44.75
PHY-3002 : Step(120): len = 43696.6, overlap = 43
PHY-3002 : Step(121): len = 43302.8, overlap = 43.5
PHY-3002 : Step(122): len = 43083.6, overlap = 47.5
PHY-3002 : Step(123): len = 42753.7, overlap = 50.5
PHY-3002 : Step(124): len = 42527.2, overlap = 51.75
PHY-3002 : Step(125): len = 42556.3, overlap = 49.75
PHY-3002 : Step(126): len = 42345.9, overlap = 52.25
PHY-3002 : Step(127): len = 42046.2, overlap = 53
PHY-3002 : Step(128): len = 41825.5, overlap = 50
PHY-3002 : Step(129): len = 41618.9, overlap = 46.5
PHY-3002 : Step(130): len = 41731.6, overlap = 48.5
PHY-3002 : Step(131): len = 42029.9, overlap = 47.5
PHY-3002 : Step(132): len = 41773, overlap = 45.5
PHY-3002 : Step(133): len = 41417.9, overlap = 47
PHY-3002 : Step(134): len = 41382.2, overlap = 48
PHY-3002 : Step(135): len = 41002.9, overlap = 50.25
PHY-3002 : Step(136): len = 40724.4, overlap = 50.25
PHY-3002 : Step(137): len = 40686.1, overlap = 49
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000151855
PHY-3002 : Step(138): len = 40842, overlap = 48.5
PHY-3002 : Step(139): len = 41095.5, overlap = 48.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00030371
PHY-3002 : Step(140): len = 41659.4, overlap = 46
PHY-3002 : Step(141): len = 42099.4, overlap = 45.75
PHY-3002 : Step(142): len = 42141.4, overlap = 45.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.084391s wall, 0.046875s user + 0.093750s system = 0.140625s CPU (166.6%)

PHY-3001 : Trial Legalized: Len = 57143.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.041441s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (113.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00991289
PHY-3002 : Step(143): len = 55186.1, overlap = 2
PHY-3002 : Step(144): len = 51899.7, overlap = 7.25
PHY-3002 : Step(145): len = 50575.1, overlap = 9.5
PHY-3002 : Step(146): len = 49685.8, overlap = 12.75
PHY-3002 : Step(147): len = 48788.9, overlap = 12.5
PHY-3002 : Step(148): len = 48259.3, overlap = 14.75
PHY-3002 : Step(149): len = 47778.3, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0198258
PHY-3002 : Step(150): len = 47786.3, overlap = 15.75
PHY-3002 : Step(151): len = 47596.1, overlap = 16
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0396516
PHY-3002 : Step(152): len = 47571.9, overlap = 15.75
PHY-3002 : Step(153): len = 47494.1, overlap = 16
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005179s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (301.7%)

PHY-3001 : Legalized: Len = 52082.1, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005124s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 2, deltaY = 5, maxDist = 2.
PHY-3001 : Final: Len = 52470.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6765, tnet num: 1556, tinst num: 709, tnode num: 8573, tedge num: 11505.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 9/1558.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65416, over cnt = 207(0%), over = 343, worst = 5
PHY-1002 : len = 66712, over cnt = 128(0%), over = 192, worst = 4
PHY-1002 : len = 68480, over cnt = 27(0%), over = 38, worst = 4
PHY-1002 : len = 68816, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 68928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.181088s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.2%)

PHY-1001 : Congestion index: top1 = 30.04, top5 = 22.52, top10 = 18.02, top15 = 14.11.
PHY-1001 : End incremental global routing;  0.232782s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (107.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051218s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.314512s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (104.3%)

OPT-1001 : Current memory(MB): used = 204, reserve = 159, peak = 204.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1363/1558.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005632s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.04, top5 = 22.52, top10 = 18.02, top15 = 14.11.
OPT-1001 : End congestion update;  0.051827s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047913s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.8%)

OPT-0007 : Start: WNS 3865 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.099948s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (93.8%)

OPT-1001 : Current memory(MB): used = 206, reserve = 162, peak = 206.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046502s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1363/1558.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68928, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006164s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (253.5%)

PHY-1001 : Congestion index: top1 = 30.04, top5 = 22.52, top10 = 18.02, top15 = 14.11.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049779s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3865 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 29.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3865ps with logic level 7 
RUN-1001 :       #2 path slack 3919ps with logic level 6 
OPT-1001 : End physical optimization;  0.721864s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (103.9%)

RUN-1003 : finish command "place" in  4.677923s wall, 7.609375s user + 2.734375s system = 10.343750s CPU (221.1%)

RUN-1004 : used memory is 196 MB, reserved memory is 151 MB, peak memory is 208 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 711 instances
RUN-1001 : 332 mslices, 332 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1558 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 773 nets have 2 pins
RUN-1001 : 594 nets have [3 - 5] pins
RUN-1001 : 99 nets have [6 - 10] pins
RUN-1001 : 55 nets have [11 - 20] pins
RUN-1001 : 26 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6765, tnet num: 1556, tinst num: 709, tnode num: 8573, tedge num: 11505.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 332 mslices, 332 lslices, 8 pads, 34 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65080, over cnt = 203(0%), over = 345, worst = 5
PHY-1002 : len = 66624, over cnt = 117(0%), over = 172, worst = 4
PHY-1002 : len = 68416, over cnt = 22(0%), over = 30, worst = 4
PHY-1002 : len = 68672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.173079s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (135.4%)

PHY-1001 : Congestion index: top1 = 30.00, top5 = 22.53, top10 = 18.00, top15 = 14.06.
PHY-1001 : End global routing;  0.223309s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (125.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 225, reserve = 180, peak = 225.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 484, reserve = 443, peak = 484.
PHY-1001 : End build detailed router design. 3.160635s wall, 3.093750s user + 0.078125s system = 3.171875s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32464, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.610831s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 516, reserve = 476, peak = 516.
PHY-1001 : End phase 1; 0.617604s wall, 0.578125s user + 0.031250s system = 0.609375s CPU (98.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 252120, over cnt = 65(0%), over = 65, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 519, reserve = 477, peak = 520.
PHY-1001 : End initial routed; 3.475786s wall, 4.296875s user + 0.125000s system = 4.421875s CPU (127.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1386(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.806   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.381663s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (90.1%)

PHY-1001 : Current memory(MB): used = 521, reserve = 479, peak = 521.
PHY-1001 : End phase 2; 3.857624s wall, 4.640625s user + 0.125000s system = 4.765625s CPU (123.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 252120, over cnt = 65(0%), over = 65, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.020375s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (153.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 251408, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.119246s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (91.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 251464, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.076234s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 251488, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.032881s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (237.6%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 251504, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.035609s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1386(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.806   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.518009s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 12 feed throughs used by 12 nets
PHY-1001 : End commit to database; 0.407830s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 536, reserve = 495, peak = 536.
PHY-1001 : End phase 3; 1.373524s wall, 1.390625s user + 0.046875s system = 1.437500s CPU (104.7%)

PHY-1003 : Routed, final wirelength = 251504
PHY-1001 : Current memory(MB): used = 536, reserve = 495, peak = 536.
PHY-1001 : End export database. 0.011773s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (132.7%)

PHY-1001 : End detail routing;  9.419641s wall, 10.109375s user + 0.281250s system = 10.390625s CPU (110.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6765, tnet num: 1556, tinst num: 709, tnode num: 8573, tedge num: 11505.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  10.357750s wall, 11.078125s user + 0.312500s system = 11.390625s CPU (110.0%)

RUN-1004 : used memory is 493 MB, reserved memory is 453 MB, peak memory is 536 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      984   out of  19600    5.02%
#reg                      714   out of  19600    3.64%
#le                      1246
  #lut only               532   out of   1246   42.70%
  #reg only               262   out of   1246   21.03%
  #lut&reg                452   out of   1246   36.28%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    326
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         137
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1246   |798     |186     |720     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |116    |94      |11      |61      |0       |0       |
|    usms                            |Time_1ms        |27     |12      |5       |17      |0       |0       |
|  SPIM                              |SPI_MASTER      |192    |125     |23      |132     |0       |0       |
|  uart                              |UART_Control    |120    |102     |4       |57      |0       |0       |
|    U0                              |speed_select_Tx |20     |14      |4       |14      |0       |0       |
|    U1                              |uart_tx         |22     |18      |0       |18      |0       |0       |
|    U2                              |Ctrl_Data       |78     |70      |0       |25      |0       |0       |
|  wendu                             |DS18B20         |174    |132     |41      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |607    |324     |99      |401     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |607    |324     |99      |401     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |247    |103     |0       |246     |0       |0       |
|        reg_inst                    |register        |245    |101     |0       |244     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |360    |221     |99      |155     |0       |0       |
|        bus_inst                    |bus_top         |119    |76      |42      |39      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |14     |8       |6       |2       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |28     |18      |10      |11      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |72     |45      |26      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |137    |85      |29      |79      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       764   
    #2          2       354   
    #3          3       176   
    #4          4        64   
    #5        5-10      104   
    #6        11-50      73   
    #7       51-100      3    
    #8       101-500     1    
  Average     3.07            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6765, tnet num: 1556, tinst num: 709, tnode num: 8573, tedge num: 11505.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1556 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 709
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1558, pip num: 17042
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1429 valid insts, and 45506 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011001110101111100011010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.649773s wall, 19.625000s user + 0.171875s system = 19.796875s CPU (542.4%)

RUN-1004 : used memory is 508 MB, reserved memory is 467 MB, peak memory is 655 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_150520.log"
