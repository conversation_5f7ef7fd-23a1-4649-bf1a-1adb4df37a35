============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 14:25:24 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/ADDA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 59 trigger nets, 59 data nets.
KIT-1004 : Chipwatcher code = 1101011101011001
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=156) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=156) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=156)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=59,BUS_CTRL_NUM=134,BUS_WIDTH='{32'sb01,32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb0100001,32'sb0101111},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01001010,32'sb01101010})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3979/20 useful/useless nets, 2379/9 useful/useless insts
SYN-1016 : Merged 24 instances.
SYN-1032 : 3592/18 useful/useless nets, 2887/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 474 better
SYN-1014 : Optimize round 2
SYN-1032 : 3211/45 useful/useless nets, 2506/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 3283/442 useful/useless nets, 2630/64 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 575 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 58 instances.
SYN-2501 : Optimize round 1, 118 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 3791/5 useful/useless nets, 3138/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 14698, tnet num: 3791, tinst num: 3137, tnode num: 19655, tedge num: 23564.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3791 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 285 (3.29), #lev = 7 (1.55)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 285 (3.32), #lev = 6 (1.63)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 599 instances into 285 LUTs, name keeping = 70%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 481 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 164 adder to BLE ...
SYN-4008 : Packed 164 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.805747s wall, 1.656250s user + 0.140625s system = 1.796875s CPU (99.5%)

RUN-1004 : used memory is 178 MB, reserved memory is 143 MB, peak memory is 214 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (332 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2588 instances
RUN-0007 : 665 luts, 1473 seqs, 233 mslices, 114 lslices, 34 pads, 57 brams, 5 dsps
RUN-1001 : There are total 3243 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 2142 nets have 2 pins
RUN-1001 : 886 nets have [3 - 5] pins
RUN-1001 : 117 nets have [6 - 10] pins
RUN-1001 : 53 nets have [11 - 20] pins
RUN-1001 : 33 nets have [21 - 99] pins
RUN-1001 : 11 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     271     
RUN-1001 :   No   |  No   |  Yes  |     345     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     457     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    5    |  17   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 26
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2586 instances, 665 luts, 1473 seqs, 347 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-0007 : Cell area utilization is 7%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13511, tnet num: 3241, tinst num: 2586, tnode num: 18815, tedge num: 22874.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.372817s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 820118
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2586.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 706492, overlap = 135
PHY-3002 : Step(2): len = 530947, overlap = 135
PHY-3002 : Step(3): len = 494807, overlap = 132.75
PHY-3002 : Step(4): len = 475311, overlap = 135
PHY-3002 : Step(5): len = 459091, overlap = 137.25
PHY-3002 : Step(6): len = 446578, overlap = 135
PHY-3002 : Step(7): len = 431316, overlap = 132.75
PHY-3002 : Step(8): len = 419533, overlap = 135
PHY-3002 : Step(9): len = 411362, overlap = 135
PHY-3002 : Step(10): len = 394377, overlap = 137.25
PHY-3002 : Step(11): len = 385221, overlap = 137.25
PHY-3002 : Step(12): len = 378059, overlap = 137.25
PHY-3002 : Step(13): len = 364592, overlap = 137.25
PHY-3002 : Step(14): len = 352049, overlap = 135
PHY-3002 : Step(15): len = 347371, overlap = 135
PHY-3002 : Step(16): len = 334852, overlap = 132.75
PHY-3002 : Step(17): len = 322078, overlap = 132.75
PHY-3002 : Step(18): len = 316307, overlap = 132.75
PHY-3002 : Step(19): len = 310057, overlap = 132.75
PHY-3002 : Step(20): len = 279277, overlap = 130.5
PHY-3002 : Step(21): len = 269236, overlap = 130.5
PHY-3002 : Step(22): len = 265976, overlap = 130.5
PHY-3002 : Step(23): len = 233510, overlap = 135
PHY-3002 : Step(24): len = 223546, overlap = 132.75
PHY-3002 : Step(25): len = 218262, overlap = 135
PHY-3002 : Step(26): len = 213813, overlap = 135
PHY-3002 : Step(27): len = 210548, overlap = 135
PHY-3002 : Step(28): len = 205494, overlap = 135
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.43166e-06
PHY-3002 : Step(29): len = 206107, overlap = 130.5
PHY-3002 : Step(30): len = 202605, overlap = 126
PHY-3002 : Step(31): len = 201461, overlap = 126
PHY-3002 : Step(32): len = 198870, overlap = 130.5
PHY-3002 : Step(33): len = 196323, overlap = 126
PHY-3002 : Step(34): len = 194030, overlap = 121.5
PHY-3002 : Step(35): len = 185648, overlap = 135
PHY-3002 : Step(36): len = 181125, overlap = 135
PHY-3002 : Step(37): len = 178394, overlap = 130.625
PHY-3002 : Step(38): len = 173115, overlap = 121.5
PHY-3002 : Step(39): len = 168907, overlap = 126
PHY-3002 : Step(40): len = 166353, overlap = 126
PHY-3002 : Step(41): len = 160677, overlap = 121.5
PHY-3002 : Step(42): len = 157371, overlap = 121.5
PHY-3002 : Step(43): len = 153007, overlap = 126
PHY-3002 : Step(44): len = 148965, overlap = 126
PHY-3002 : Step(45): len = 146512, overlap = 126
PHY-3002 : Step(46): len = 142249, overlap = 126.938
PHY-3002 : Step(47): len = 131983, overlap = 123.438
PHY-3002 : Step(48): len = 129373, overlap = 126
PHY-3002 : Step(49): len = 127380, overlap = 126.125
PHY-3002 : Step(50): len = 122480, overlap = 124.25
PHY-3002 : Step(51): len = 121253, overlap = 126.5
PHY-3002 : Step(52): len = 119406, overlap = 124.188
PHY-3002 : Step(53): len = 117999, overlap = 122.125
PHY-3002 : Step(54): len = 114822, overlap = 121.688
PHY-3002 : Step(55): len = 110235, overlap = 119.688
PHY-3002 : Step(56): len = 107949, overlap = 122.25
PHY-3002 : Step(57): len = 105801, overlap = 127.188
PHY-3002 : Step(58): len = 103097, overlap = 129.438
PHY-3002 : Step(59): len = 101067, overlap = 125.125
PHY-3002 : Step(60): len = 98490.7, overlap = 120.938
PHY-3002 : Step(61): len = 96636.3, overlap = 119.062
PHY-3002 : Step(62): len = 95210.3, overlap = 121.5
PHY-3002 : Step(63): len = 92668.9, overlap = 119.875
PHY-3002 : Step(64): len = 90015, overlap = 120.062
PHY-3002 : Step(65): len = 88364, overlap = 118.125
PHY-3002 : Step(66): len = 85289.4, overlap = 115.812
PHY-3002 : Step(67): len = 84161.4, overlap = 118.062
PHY-3002 : Step(68): len = 83230, overlap = 122.5
PHY-3002 : Step(69): len = 82238.1, overlap = 125.375
PHY-3002 : Step(70): len = 81787.8, overlap = 125.312
PHY-3002 : Step(71): len = 80522, overlap = 122.875
PHY-3002 : Step(72): len = 79564.6, overlap = 122.938
PHY-3002 : Step(73): len = 78887, overlap = 125.438
PHY-3002 : Step(74): len = 77379.9, overlap = 123.062
PHY-3002 : Step(75): len = 76224.7, overlap = 123
PHY-3002 : Step(76): len = 72573.1, overlap = 125.062
PHY-3002 : Step(77): len = 71923.9, overlap = 127.625
PHY-3002 : Step(78): len = 71355.7, overlap = 127.688
PHY-3002 : Step(79): len = 70915.8, overlap = 127.812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.08633e-05
PHY-3002 : Step(80): len = 71273.7, overlap = 130.062
PHY-3002 : Step(81): len = 71388.9, overlap = 127.938
PHY-3002 : Step(82): len = 71260.2, overlap = 127.938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 2.17266e-05
PHY-3002 : Step(83): len = 72196.3, overlap = 123.438
PHY-3002 : Step(84): len = 72917.9, overlap = 123.438
PHY-3002 : Step(85): len = 72842, overlap = 125.5
PHY-3002 : Step(86): len = 72781.2, overlap = 123.25
PHY-3002 : Step(87): len = 72762.1, overlap = 123.062
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 4.34533e-05
PHY-3002 : Step(88): len = 73009.9, overlap = 122.938
PHY-3002 : Step(89): len = 73574.2, overlap = 122.938
PHY-3002 : Step(90): len = 73884.3, overlap = 125.188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 8.69066e-05
PHY-3002 : Step(91): len = 74850, overlap = 120.75
PHY-3002 : Step(92): len = 75758.6, overlap = 118.5
PHY-3002 : Step(93): len = 76973.8, overlap = 118.625
PHY-3002 : Step(94): len = 77168, overlap = 118.625
PHY-3002 : Step(95): len = 76873.3, overlap = 118.75
PHY-3002 : Step(96): len = 76605.5, overlap = 118.688
PHY-3002 : Step(97): len = 76435.8, overlap = 118.688
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.013148s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.104805s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (104.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.81674e-05
PHY-3002 : Step(98): len = 94014.2, overlap = 34.9062
PHY-3002 : Step(99): len = 92598.3, overlap = 34.1562
PHY-3002 : Step(100): len = 92349.1, overlap = 33.6875
PHY-3002 : Step(101): len = 92252.9, overlap = 33.9688
PHY-3002 : Step(102): len = 92150.3, overlap = 34.5938
PHY-3002 : Step(103): len = 91031.1, overlap = 34.0312
PHY-3002 : Step(104): len = 90201.4, overlap = 35.2188
PHY-3002 : Step(105): len = 88959.1, overlap = 36.6562
PHY-3002 : Step(106): len = 88214.8, overlap = 36.875
PHY-3002 : Step(107): len = 87786.9, overlap = 38.8125
PHY-3002 : Step(108): len = 86872.3, overlap = 38.75
PHY-3002 : Step(109): len = 86023.1, overlap = 39.125
PHY-3002 : Step(110): len = 85259.2, overlap = 38.9062
PHY-3002 : Step(111): len = 84593, overlap = 39.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.63348e-05
PHY-3002 : Step(112): len = 84315.7, overlap = 39.1562
PHY-3002 : Step(113): len = 83835.9, overlap = 40.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00019267
PHY-3002 : Step(114): len = 83854, overlap = 40.5
PHY-3002 : Step(115): len = 84036.9, overlap = 38.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.096315s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (113.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.06636e-05
PHY-3002 : Step(116): len = 83852.6, overlap = 95.3438
PHY-3002 : Step(117): len = 84445.5, overlap = 83.0938
PHY-3002 : Step(118): len = 84996.6, overlap = 80.4688
PHY-3002 : Step(119): len = 85166.7, overlap = 80.5625
PHY-3002 : Step(120): len = 85289.1, overlap = 80.3125
PHY-3002 : Step(121): len = 85355.8, overlap = 78.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000121327
PHY-3002 : Step(122): len = 85544.4, overlap = 78.0938
PHY-3002 : Step(123): len = 87775.2, overlap = 71.3438
PHY-3002 : Step(124): len = 88480.2, overlap = 69.7812
PHY-3002 : Step(125): len = 88441.7, overlap = 66.9688
PHY-3002 : Step(126): len = 88391.7, overlap = 64.0625
PHY-3002 : Step(127): len = 88391.7, overlap = 64.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000242654
PHY-3002 : Step(128): len = 89218.1, overlap = 61.5938
PHY-3002 : Step(129): len = 89532.2, overlap = 60.3125
PHY-3002 : Step(130): len = 90561.9, overlap = 58.0312
PHY-3002 : Step(131): len = 90697.8, overlap = 55.7188
PHY-3002 : Step(132): len = 90955.5, overlap = 54.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13511, tnet num: 3241, tinst num: 2586, tnode num: 18815, tedge num: 22874.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 150.56 peak overflow 2.94
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/3243.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 112240, over cnt = 435(1%), over = 1638, worst = 24
PHY-1001 : End global iterations;  0.158914s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (108.2%)

PHY-1001 : Congestion index: top1 = 46.92, top5 = 31.94, top10 = 24.06, top15 = 19.44.
PHY-1001 : End incremental global routing;  0.220985s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (106.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3241 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.111241s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.3%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 2546 has valid locations, 53 needs to be replaced
PHY-3001 : design contains 2638 instances, 665 luts, 1525 seqs, 347 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 91539.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13719, tnet num: 3293, tinst num: 2638, tnode num: 19179, tedge num: 23186.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.434156s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(133): len = 92161.6, overlap = 9.96875
PHY-3002 : Step(134): len = 92884.2, overlap = 9.96875
PHY-3002 : Step(135): len = 93118.8, overlap = 10.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00166823
PHY-3002 : Step(136): len = 93192.8, overlap = 9.96875
PHY-3002 : Step(137): len = 93217.1, overlap = 10.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00333646
PHY-3002 : Step(138): len = 93127.6, overlap = 10.0312
PHY-3002 : Step(139): len = 93127.6, overlap = 10.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.097644s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (96.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000597025
PHY-3002 : Step(140): len = 93063.3, overlap = 54.875
PHY-3002 : Step(141): len = 93025, overlap = 54.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00119405
PHY-3002 : Step(142): len = 93049.8, overlap = 54.875
PHY-3002 : Step(143): len = 93049.8, overlap = 54.875
PHY-3001 : Final: Len = 93049.8, Over = 54.875
PHY-3001 : End incremental placement;  0.718347s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (124.0%)

OPT-1001 : Total overflow 151.44 peak overflow 2.94
OPT-1001 : End high-fanout net optimization;  1.118309s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (116.0%)

OPT-1001 : Current memory(MB): used = 260, reserve = 221, peak = 260.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2475/3295.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 115176, over cnt = 436(1%), over = 1632, worst = 24
PHY-1002 : len = 123904, over cnt = 359(1%), over = 885, worst = 13
PHY-1002 : len = 132992, over cnt = 111(0%), over = 205, worst = 8
PHY-1002 : len = 134408, over cnt = 48(0%), over = 64, worst = 4
PHY-1002 : len = 135696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.199714s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (133.0%)

PHY-1001 : Congestion index: top1 = 41.90, top5 = 31.37, top10 = 25.40, top15 = 21.39.
OPT-1001 : End congestion update;  0.254781s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (122.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3293 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.098166s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (111.4%)

OPT-0007 : Start: WNS -12498 TNS -290702 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -12498 TNS -290702 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.356849s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (118.2%)

OPT-1001 : Current memory(MB): used = 260, reserve = 221, peak = 260.
OPT-1001 : End physical optimization;  1.842820s wall, 2.046875s user + 0.062500s system = 2.109375s CPU (114.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 665 LUT to BLE ...
SYN-4008 : Packed 665 LUT and 247 SEQ to BLE.
SYN-4003 : Packing 1278 remaining SEQ's ...
SYN-4005 : Packed 349 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 929 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1594/2102 primitive instances ...
PHY-3001 : End packing;  0.093771s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1306 instances
RUN-1001 : 601 mslices, 602 lslices, 34 pads, 57 brams, 5 dsps
RUN-1001 : There are total 3063 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1918 nets have 2 pins
RUN-1001 : 927 nets have [3 - 5] pins
RUN-1001 : 123 nets have [6 - 10] pins
RUN-1001 : 52 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
PHY-3001 : design contains 1304 instances, 1203 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Cell area utilization is 15%
PHY-3001 : After packing: Len = 94314, Over = 95.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11771, tnet num: 3061, tinst num: 1304, tnode num: 15738, tedge num: 20469.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.450544s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.82449e-05
PHY-3002 : Step(144): len = 93496.9, overlap = 94.75
PHY-3002 : Step(145): len = 92849.9, overlap = 93.5
PHY-3002 : Step(146): len = 92404.7, overlap = 93.75
PHY-3002 : Step(147): len = 91783.2, overlap = 92.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.64899e-05
PHY-3002 : Step(148): len = 92109.2, overlap = 90.75
PHY-3002 : Step(149): len = 92340.7, overlap = 88.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.117747s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (146.0%)

PHY-3001 : Trial Legalized: Len = 118569
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.087082s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000288055
PHY-3002 : Step(150): len = 113896, overlap = 10.25
PHY-3002 : Step(151): len = 111412, overlap = 19.75
PHY-3002 : Step(152): len = 109658, overlap = 24.25
PHY-3002 : Step(153): len = 108233, overlap = 29.25
PHY-3002 : Step(154): len = 107425, overlap = 32.25
PHY-3002 : Step(155): len = 106773, overlap = 32.25
PHY-3002 : Step(156): len = 106305, overlap = 32
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00057611
PHY-3002 : Step(157): len = 106801, overlap = 31.5
PHY-3002 : Step(158): len = 107473, overlap = 31
PHY-3002 : Step(159): len = 107659, overlap = 30.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00115222
PHY-3002 : Step(160): len = 108220, overlap = 31
PHY-3002 : Step(161): len = 108354, overlap = 30.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006221s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 115673, Over = 0
PHY-3001 : Spreading special nets. 38 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.010787s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (144.9%)

PHY-3001 : 54 instances has been re-located, deltaX = 27, deltaY = 30, maxDist = 2.
PHY-3001 : Final: Len = 116639, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11771, tnet num: 3061, tinst num: 1304, tnode num: 15738, tedge num: 20469.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 166/3063.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 145000, over cnt = 348(0%), over = 596, worst = 6
PHY-1002 : len = 147248, over cnt = 204(0%), over = 302, worst = 5
PHY-1002 : len = 149568, over cnt = 72(0%), over = 107, worst = 4
PHY-1002 : len = 151072, over cnt = 8(0%), over = 11, worst = 3
PHY-1002 : len = 151336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.315748s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (128.7%)

PHY-1001 : Congestion index: top1 = 36.08, top5 = 29.48, top10 = 25.11, top15 = 22.06.
PHY-1001 : End incremental global routing;  0.383082s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (126.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.105621s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (88.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1264 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 1309 instances, 1208 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 117427
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11795, tnet num: 3065, tinst num: 1309, tnode num: 15772, tedge num: 20502.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.460510s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (98.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(162): len = 117249, overlap = 0
PHY-3002 : Step(163): len = 117167, overlap = 0
PHY-3002 : Step(164): len = 117145, overlap = 0.25
PHY-3002 : Step(165): len = 117127, overlap = 0.25
PHY-3002 : Step(166): len = 117110, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.087831s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (88.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(167): len = 117126, overlap = 0.75
PHY-3002 : Step(168): len = 117111, overlap = 0.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003895s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 117125, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007518s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 2 instances has been re-located, deltaX = 2, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 117133, Over = 0
PHY-3001 : End incremental placement;  0.664134s wall, 0.859375s user + 0.062500s system = 0.921875s CPU (138.8%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  1.216194s wall, 1.484375s user + 0.078125s system = 1.562500s CPU (128.5%)

OPT-1001 : Current memory(MB): used = 262, reserve = 225, peak = 263.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2690/3067.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 151608, over cnt = 9(0%), over = 12, worst = 3
PHY-1002 : len = 151648, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 151664, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 151664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.069912s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.4%)

PHY-1001 : Congestion index: top1 = 36.34, top5 = 29.54, top10 = 25.15, top15 = 22.09.
OPT-1001 : End congestion update;  0.130198s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (96.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.085655s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (109.5%)

OPT-0007 : Start: WNS -12498 TNS -290852 NUM_FEPS 28
OPT-1001 : End path based optimization;  0.216293s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.1%)

OPT-1001 : Current memory(MB): used = 262, reserve = 225, peak = 263.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.084923s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2703/3067.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 151664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.012603s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (124.0%)

PHY-1001 : Congestion index: top1 = 36.34, top5 = 29.54, top10 = 25.15, top15 = 22.09.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.085557s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (109.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -12498 TNS -290852 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 35.931034
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -12498ps with logic level 2 
RUN-1001 :       #2 path slack -12498ps with logic level 2 
RUN-1001 :       #3 path slack -12498ps with logic level 2 
RUN-1001 :       #4 path slack -12498ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 3067 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 3067 nets
OPT-1001 : End physical optimization;  2.089008s wall, 2.359375s user + 0.078125s system = 2.437500s CPU (116.7%)

RUN-1003 : finish command "place" in  8.987484s wall, 14.625000s user + 2.843750s system = 17.468750s CPU (194.4%)

RUN-1004 : used memory is 249 MB, reserved memory is 213 MB, peak memory is 263 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1311 instances
RUN-1001 : 601 mslices, 607 lslices, 34 pads, 57 brams, 5 dsps
RUN-1001 : There are total 3067 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1917 nets have 2 pins
RUN-1001 : 927 nets have [3 - 5] pins
RUN-1001 : 125 nets have [6 - 10] pins
RUN-1001 : 55 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11795, tnet num: 3065, tinst num: 1309, tnode num: 15772, tedge num: 20502.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 601 mslices, 607 lslices, 34 pads, 57 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 143680, over cnt = 348(0%), over = 609, worst = 6
PHY-1002 : len = 146104, over cnt = 209(0%), over = 304, worst = 5
PHY-1002 : len = 148416, over cnt = 75(0%), over = 118, worst = 4
PHY-1002 : len = 150112, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 150208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.326729s wall, 0.390625s user + 0.031250s system = 0.421875s CPU (129.1%)

PHY-1001 : Congestion index: top1 = 36.03, top5 = 29.38, top10 = 24.95, top15 = 21.86.
PHY-1001 : End global routing;  0.392562s wall, 0.453125s user + 0.031250s system = 0.484375s CPU (123.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 279, reserve = 240, peak = 299.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 540, reserve = 505, peak = 540.
PHY-1001 : End build detailed router design. 3.374823s wall, 3.328125s user + 0.046875s system = 3.375000s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 47384, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.602022s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 572, reserve = 539, peak = 572.
PHY-1001 : End phase 1; 1.609333s wall, 1.609375s user + 0.000000s system = 1.609375s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 470264, over cnt = 76(0%), over = 76, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 573, reserve = 540, peak = 574.
PHY-1001 : End initial routed; 7.118421s wall, 8.859375s user + 0.203125s system = 9.062500s CPU (127.3%)

PHY-1001 : Update timing.....
PHY-1001 : 12/2725(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.370   |  -223.815  |  28   
RUN-1001 :   Hold   |   0.080   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.554593s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 575, reserve = 541, peak = 575.
PHY-1001 : End phase 2; 7.673110s wall, 9.421875s user + 0.203125s system = 9.625000s CPU (125.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS -9.508ns STNS -223.953ns FEP 28.
PHY-1001 : End OPT Iter 1; 0.027118s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.2%)

PHY-1022 : len = 469688, over cnt = 77(0%), over = 77, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.051911s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 469128, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.113401s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (82.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 469016, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.040004s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (117.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 469056, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.030289s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.2%)

PHY-1001 : Update timing.....
PHY-1001 : 12/2725(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.508   |  -223.953  |  28   
RUN-1001 :   Hold   |   0.080   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.549605s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 15 feed throughs used by 14 nets
PHY-1001 : End commit to database; 0.372524s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 594, reserve = 560, peak = 594.
PHY-1001 : End phase 3; 1.303618s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (98.3%)

PHY-1001 : ===== Detail Route Phase 4 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS -9.508ns STNS -223.953ns FEP 28.
PHY-1001 : End OPT Iter 1; 0.035683s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (131.4%)

PHY-1022 : len = 469200, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.059400s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.2%)

PHY-0007 : Phase: 4; Congestion: {, , , }; Timing: {-9.508ns, -223.953ns, 28}
PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 469056, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 1; 0.026395s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (118.4%)

PHY-1001 : Update timing.....
PHY-1001 : 12/2725(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.508   |  -223.953  |  28   
RUN-1001 :   Hold   |   0.080   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.540261s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (101.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 15 feed throughs used by 14 nets
PHY-1001 : End commit to database; 0.387670s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (96.7%)

PHY-1001 : Current memory(MB): used = 595, reserve = 562, peak = 595.
PHY-1001 : End phase 4; 1.032312s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.9%)

PHY-1003 : Routed, final wirelength = 469056
PHY-1001 : Current memory(MB): used = 595, reserve = 562, peak = 595.
PHY-1001 : End export database. 0.013527s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  15.210140s wall, 16.890625s user + 0.250000s system = 17.140625s CPU (112.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11795, tnet num: 3065, tinst num: 1309, tnode num: 15772, tedge num: 20502.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  16.628152s wall, 18.359375s user + 0.281250s system = 18.640625s CPU (112.1%)

RUN-1004 : used memory is 569 MB, reserved memory is 538 MB, peak memory is 595 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1370   out of  19600    6.99%
#reg                     1607   out of  19600    8.20%
#le                      2299
  #lut only               692   out of   2299   30.10%
  #reg only               929   out of   2299   40.41%
  #lut&reg                678   out of   2299   29.49%
#dsp                        5   out of     29   17.24%
#bram                      57   out of     64   89.06%
  #bram9k                  57
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                           Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0            656
#2        config_inst_syn_9               GCLK               config             config_inst.jtck                 181
#3        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3            108
#4        wendu/clk_us                    GCLK               lslice             u_uart/U0/cnt_b[11]_syn_31.q0    40
#5        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                    11
#6        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4            1
#7        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                  1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |2299   |1023    |347     |1638    |57      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1120   |292     |133     |919     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |23     |17      |6       |20      |0       |0       |
|    demodu                          |Demodulation                                     |544    |116     |58      |438     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |160    |60      |20      |128     |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |56     |2       |0       |56      |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |12      |0       |25      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |23     |14      |0       |23      |0       |0       |
|    integ                           |Integration                                      |137    |33      |14      |111     |0       |0       |
|    modu                            |Modulation                                       |88     |23      |21      |84      |0       |1       |
|    rs422                           |Rs422Output                                      |304    |84      |29      |247     |0       |4       |
|    trans                           |SquareWaveGenerator                              |24     |19      |5       |19      |0       |0       |
|  u_uart                            |UART_Control                                     |114    |107     |7       |64      |0       |0       |
|    U0                              |speed_select_Tx                                  |37     |30      |7       |18      |0       |0       |
|    U1                              |uart_tx                                          |15     |15      |0       |15      |0       |0       |
|    U2                              |Ctrl_Data                                        |62     |62      |0       |31      |0       |0       |
|  wendu                             |DS18B20                                          |211    |166     |45      |75      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |754    |414     |121     |509     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |754    |414     |121     |509     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |322    |147     |0       |322     |0       |0       |
|        reg_inst                    |register                                         |319    |144     |0       |319     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |432    |267     |121     |187     |0       |0       |
|        bus_inst                    |bus_top                                          |183    |119     |64      |56      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |99     |65      |34      |31      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det                                          |46     |30      |16      |14      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det                                          |38     |24      |14      |11      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |152    |90      |29      |93      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1881  
    #2          2       562   
    #3          3       285   
    #4          4        80   
    #5        5-10      130   
    #6        11-50      67   
    #7       51-100      15   
    #8       101-500     3    
  Average     2.55            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11795, tnet num: 3065, tinst num: 1309, tnode num: 15772, tedge num: 20502.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 3065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 449d723a5d69124cfb6b5e2627f2e596a1f1996c62c154540ae03241ff9a8d54 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1309
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 3067, pip num: 30054
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 15
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1941 valid insts, and 76345 bits set as '1'.
BIT-1004 : the usercode register value: 00000000111010011101011101011001
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.614208s wall, 33.703125s user + 0.062500s system = 33.765625s CPU (601.4%)

RUN-1004 : used memory is 603 MB, reserved memory is 568 MB, peak memory is 718 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_142524.log"
