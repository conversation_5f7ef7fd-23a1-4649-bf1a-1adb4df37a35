standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      806   out of  19600    4.11%
#reg                      672   out of  19600    3.43%
#le                      1048
  #lut only               376   out of   1048   35.88%
  #reg only               242   out of   1048   23.09%
  #lut&reg                430   out of   1048   41.03%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     0
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        dq_dup_1             GCLK               pll                CLK120/pll_inst.clkc0    297
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         140
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
   miso        INPUT         A4        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         A8        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         A6        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8            NONE       NONE    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1048   |660     |146     |677     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |100    |78      |11      |64      |0       |0       |
|    usms                            |Time_1ms        |26     |12      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |202    |129     |23      |129     |0       |0       |
|  uart                              |UART_Control    |117    |106     |7       |55      |0       |0       |
|    U0                              |speed_select_Tx |23     |14      |7       |16      |0       |0       |
|    U1                              |uart_tx         |29     |27      |0       |17      |0       |0       |
|    U2                              |Ctrl_Data       |65     |65      |0       |22      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |597    |330     |99      |403     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |597    |330     |99      |403     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |250    |119     |0       |244     |0       |0       |
|        reg_inst                    |register        |247    |116     |0       |241     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |347    |211     |99      |159     |0       |0       |
|        bus_inst                    |bus_top         |121    |76      |42      |42      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |15     |8       |6       |4       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |29     |19      |10      |12      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |72     |44      |26      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |141    |85      |29      |86      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       645   
    #2          2       332   
    #3          3       177   
    #4          4        52   
    #5        5-10       66   
    #6        11-50      70   
    #7       51-100      3    
    #8       101-500     1    
  Average     3.15            
