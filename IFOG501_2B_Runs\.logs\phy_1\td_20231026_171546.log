============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 17:15:46 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1449 instances
RUN-0007 : 375 luts, 827 seqs, 133 mslices, 66 lslices, 34 pads, 4 brams, 4 dsps
RUN-1001 : There are total 1954 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1453 nets have 2 pins
RUN-1001 : 389 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     190     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     275     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  12   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 18
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1447 instances, 375 luts, 827 seqs, 199 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6949, tnet num: 1952, tinst num: 1447, tnode num: 9702, tedge num: 11780.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1952 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.252505s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (105.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 518155
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1447.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 461346, overlap = 18
PHY-3002 : Step(2): len = 416955, overlap = 13.5
PHY-3002 : Step(3): len = 399741, overlap = 11.25
PHY-3002 : Step(4): len = 383010, overlap = 13.5
PHY-3002 : Step(5): len = 362115, overlap = 4.5
PHY-3002 : Step(6): len = 350099, overlap = 9
PHY-3002 : Step(7): len = 334915, overlap = 6.75
PHY-3002 : Step(8): len = 328631, overlap = 9
PHY-3002 : Step(9): len = 317280, overlap = 11.25
PHY-3002 : Step(10): len = 309884, overlap = 11.25
PHY-3002 : Step(11): len = 304908, overlap = 11.25
PHY-3002 : Step(12): len = 298122, overlap = 13.5
PHY-3002 : Step(13): len = 291134, overlap = 13.5
PHY-3002 : Step(14): len = 285910, overlap = 15.75
PHY-3002 : Step(15): len = 280882, overlap = 15.75
PHY-3002 : Step(16): len = 273197, overlap = 15.75
PHY-3002 : Step(17): len = 266390, overlap = 15.75
PHY-3002 : Step(18): len = 262343, overlap = 15.75
PHY-3002 : Step(19): len = 256787, overlap = 18
PHY-3002 : Step(20): len = 249049, overlap = 18
PHY-3002 : Step(21): len = 243981, overlap = 18
PHY-3002 : Step(22): len = 240114, overlap = 18
PHY-3002 : Step(23): len = 234004, overlap = 18
PHY-3002 : Step(24): len = 225208, overlap = 18
PHY-3002 : Step(25): len = 221579, overlap = 18
PHY-3002 : Step(26): len = 217198, overlap = 18
PHY-3002 : Step(27): len = 206432, overlap = 18
PHY-3002 : Step(28): len = 201063, overlap = 18
PHY-3002 : Step(29): len = 198368, overlap = 18
PHY-3002 : Step(30): len = 184224, overlap = 15.75
PHY-3002 : Step(31): len = 173884, overlap = 18
PHY-3002 : Step(32): len = 171639, overlap = 18
PHY-3002 : Step(33): len = 165924, overlap = 18
PHY-3002 : Step(34): len = 124722, overlap = 13.5
PHY-3002 : Step(35): len = 120499, overlap = 18
PHY-3002 : Step(36): len = 119655, overlap = 18
PHY-3002 : Step(37): len = 114716, overlap = 18
PHY-3002 : Step(38): len = 109409, overlap = 18
PHY-3002 : Step(39): len = 106829, overlap = 18
PHY-3002 : Step(40): len = 103357, overlap = 18
PHY-3002 : Step(41): len = 100469, overlap = 18
PHY-3002 : Step(42): len = 98551.8, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.46834e-05
PHY-3002 : Step(43): len = 98373, overlap = 11.25
PHY-3002 : Step(44): len = 98223.7, overlap = 13.5
PHY-3002 : Step(45): len = 97518, overlap = 11.25
PHY-3002 : Step(46): len = 96310.1, overlap = 11.25
PHY-3002 : Step(47): len = 94167.3, overlap = 15.75
PHY-3002 : Step(48): len = 91478.8, overlap = 13.5
PHY-3002 : Step(49): len = 90308.5, overlap = 9
PHY-3002 : Step(50): len = 87738, overlap = 9
PHY-3002 : Step(51): len = 86810.1, overlap = 11.25
PHY-3002 : Step(52): len = 79119.5, overlap = 16.1875
PHY-3002 : Step(53): len = 76380.4, overlap = 13.625
PHY-3002 : Step(54): len = 75548.4, overlap = 11.4375
PHY-3002 : Step(55): len = 74291.3, overlap = 11.6875
PHY-3002 : Step(56): len = 72909.1, overlap = 11.875
PHY-3002 : Step(57): len = 72587.6, overlap = 12
PHY-3002 : Step(58): len = 70705.8, overlap = 12.1875
PHY-3002 : Step(59): len = 68356.3, overlap = 14.8125
PHY-3002 : Step(60): len = 68007.2, overlap = 10.4375
PHY-3002 : Step(61): len = 65939.4, overlap = 13.25
PHY-3002 : Step(62): len = 63986.9, overlap = 15.75
PHY-3002 : Step(63): len = 63118.7, overlap = 16.1875
PHY-3002 : Step(64): len = 61003.3, overlap = 16.9375
PHY-3002 : Step(65): len = 59002.3, overlap = 17.2188
PHY-3002 : Step(66): len = 58916.7, overlap = 15.4688
PHY-3002 : Step(67): len = 58289.7, overlap = 13.1562
PHY-3002 : Step(68): len = 57521, overlap = 15.3438
PHY-3002 : Step(69): len = 57439.9, overlap = 15.3438
PHY-3002 : Step(70): len = 57127.6, overlap = 15.3438
PHY-3002 : Step(71): len = 56249.2, overlap = 17.6875
PHY-3002 : Step(72): len = 55803.1, overlap = 17.7812
PHY-3002 : Step(73): len = 55638.6, overlap = 18.0625
PHY-3002 : Step(74): len = 53462.5, overlap = 18.375
PHY-3002 : Step(75): len = 52395.3, overlap = 18.1562
PHY-3002 : Step(76): len = 51507.8, overlap = 18.0312
PHY-3002 : Step(77): len = 51229.2, overlap = 15.8438
PHY-3002 : Step(78): len = 50554.7, overlap = 15.75
PHY-3002 : Step(79): len = 49215.3, overlap = 17.9375
PHY-3002 : Step(80): len = 49030.7, overlap = 17.9062
PHY-3002 : Step(81): len = 48027.6, overlap = 17.4688
PHY-3002 : Step(82): len = 47657.9, overlap = 14.875
PHY-3002 : Step(83): len = 47484.8, overlap = 14.875
PHY-3002 : Step(84): len = 47080.9, overlap = 14.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000149367
PHY-3002 : Step(85): len = 47260.9, overlap = 14.375
PHY-3002 : Step(86): len = 47242.6, overlap = 14.375
PHY-3002 : Step(87): len = 47199.5, overlap = 14.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000298734
PHY-3002 : Step(88): len = 47340.4, overlap = 14.375
PHY-3002 : Step(89): len = 47359.4, overlap = 14.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005583s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1952 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054357s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(90): len = 50069.2, overlap = 17.8125
PHY-3002 : Step(91): len = 50292.4, overlap = 16.8438
PHY-3002 : Step(92): len = 49403.6, overlap = 15.75
PHY-3002 : Step(93): len = 49566.3, overlap = 15.4688
PHY-3002 : Step(94): len = 49465.9, overlap = 16.7188
PHY-3002 : Step(95): len = 48383, overlap = 17.0312
PHY-3002 : Step(96): len = 47913.8, overlap = 17.375
PHY-3002 : Step(97): len = 46612.5, overlap = 13.5625
PHY-3002 : Step(98): len = 46221.4, overlap = 13.0625
PHY-3002 : Step(99): len = 45662.9, overlap = 12.9375
PHY-3002 : Step(100): len = 44797.1, overlap = 15
PHY-3002 : Step(101): len = 43642.2, overlap = 14.375
PHY-3002 : Step(102): len = 43306.6, overlap = 15.5312
PHY-3002 : Step(103): len = 42757, overlap = 15.5938
PHY-3002 : Step(104): len = 42395, overlap = 15.6875
PHY-3002 : Step(105): len = 41811.1, overlap = 16.5312
PHY-3002 : Step(106): len = 41662.8, overlap = 16.875
PHY-3002 : Step(107): len = 41199.2, overlap = 17.5312
PHY-3002 : Step(108): len = 40848.2, overlap = 15.875
PHY-3002 : Step(109): len = 40547.2, overlap = 18.3438
PHY-3002 : Step(110): len = 40164.2, overlap = 18.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000250268
PHY-3002 : Step(111): len = 40228.2, overlap = 18.5625
PHY-3002 : Step(112): len = 40177.5, overlap = 17.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000500535
PHY-3002 : Step(113): len = 40010.1, overlap = 17.9375
PHY-3002 : Step(114): len = 40215.7, overlap = 18.4062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1952 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054063s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.02005e-05
PHY-3002 : Step(115): len = 40484, overlap = 59.1875
PHY-3002 : Step(116): len = 41285.5, overlap = 54.9375
PHY-3002 : Step(117): len = 41673.4, overlap = 55.7188
PHY-3002 : Step(118): len = 41621.9, overlap = 50.125
PHY-3002 : Step(119): len = 41811, overlap = 50.0938
PHY-3002 : Step(120): len = 42013.6, overlap = 52.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000160401
PHY-3002 : Step(121): len = 42442.9, overlap = 50.9375
PHY-3002 : Step(122): len = 42741, overlap = 50.3438
PHY-3002 : Step(123): len = 43557.1, overlap = 48.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000320802
PHY-3002 : Step(124): len = 44047, overlap = 45.8438
PHY-3002 : Step(125): len = 44830.6, overlap = 41.0625
PHY-3002 : Step(126): len = 45591.9, overlap = 38.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000641604
PHY-3002 : Step(127): len = 45555.2, overlap = 38.0625
PHY-3002 : Step(128): len = 45583, overlap = 37.2812
PHY-3002 : Step(129): len = 45886.2, overlap = 35.9688
PHY-3002 : Step(130): len = 45907.8, overlap = 35.5938
PHY-3002 : Step(131): len = 45808.8, overlap = 35.5
PHY-3002 : Step(132): len = 45457.5, overlap = 35.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6949, tnet num: 1952, tinst num: 1447, tnode num: 9702, tedge num: 11780.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 80.72 peak overflow 2.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1954.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49016, over cnt = 224(0%), over = 940, worst = 22
PHY-1001 : End global iterations;  0.070361s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.0%)

PHY-1001 : Congestion index: top1 = 42.35, top5 = 24.44, top10 = 15.23, top15 = 10.69.
PHY-1001 : End incremental global routing;  0.126401s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (98.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1952 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066643s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (93.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.220750s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (99.1%)

OPT-1001 : Current memory(MB): used = 206, reserve = 168, peak = 206.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1458/1954.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49016, over cnt = 224(0%), over = 940, worst = 22
PHY-1002 : len = 54800, over cnt = 138(0%), over = 320, worst = 10
PHY-1002 : len = 58080, over cnt = 21(0%), over = 27, worst = 6
PHY-1002 : len = 58448, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 58528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.091329s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.7%)

PHY-1001 : Congestion index: top1 = 36.88, top5 = 24.09, top10 = 16.84, top15 = 12.12.
OPT-1001 : End congestion update;  0.136377s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (91.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1952 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050997s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.190198s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.6%)

OPT-1001 : Current memory(MB): used = 209, reserve = 171, peak = 209.
OPT-1001 : End physical optimization;  0.668373s wall, 0.750000s user + 0.031250s system = 0.781250s CPU (116.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 656 remaining SEQ's ...
SYN-4005 : Packed 102 SEQ with LUT/SLICE
SYN-4006 : 117 single LUT's are left
SYN-4006 : 554 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 929/1210 primitive instances ...
PHY-3001 : End packing;  0.044149s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (106.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 736 instances
RUN-1001 : 344 mslices, 344 lslices, 34 pads, 4 brams, 4 dsps
RUN-1001 : There are total 1791 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1297 nets have 2 pins
RUN-1001 : 382 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 734 instances, 688 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 45396.2, Over = 56.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5849, tnet num: 1789, tinst num: 734, tnode num: 7836, tedge num: 10299.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1789 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.288495s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.15429e-05
PHY-3002 : Step(133): len = 44661.4, overlap = 55
PHY-3002 : Step(134): len = 44121.7, overlap = 51.25
PHY-3002 : Step(135): len = 43514.3, overlap = 50.25
PHY-3002 : Step(136): len = 43278.1, overlap = 50
PHY-3002 : Step(137): len = 43162.7, overlap = 52.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.30858e-05
PHY-3002 : Step(138): len = 43511.2, overlap = 50.75
PHY-3002 : Step(139): len = 44172.1, overlap = 49.25
PHY-3002 : Step(140): len = 44660, overlap = 48
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000126172
PHY-3002 : Step(141): len = 45062.1, overlap = 48.75
PHY-3002 : Step(142): len = 45777.1, overlap = 48.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.063077s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (222.9%)

PHY-3001 : Trial Legalized: Len = 59803.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1789 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.043324s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (108.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000739268
PHY-3002 : Step(143): len = 55754, overlap = 7.5
PHY-3002 : Step(144): len = 54089.6, overlap = 12
PHY-3002 : Step(145): len = 52638.4, overlap = 13.5
PHY-3002 : Step(146): len = 51468, overlap = 15
PHY-3002 : Step(147): len = 50874.9, overlap = 18
PHY-3002 : Step(148): len = 50669, overlap = 16
PHY-3002 : Step(149): len = 50479.6, overlap = 16.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00147854
PHY-3002 : Step(150): len = 50644.6, overlap = 14.75
PHY-3002 : Step(151): len = 50784.1, overlap = 15.25
PHY-3002 : Step(152): len = 50784.1, overlap = 15.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00295707
PHY-3002 : Step(153): len = 50909.2, overlap = 15.75
PHY-3002 : Step(154): len = 50909.2, overlap = 15.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004922s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (317.4%)

PHY-3001 : Legalized: Len = 54338.8, Over = 0
PHY-3001 : Spreading special nets. 10 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005246s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 15 instances has been re-located, deltaX = 3, deltaY = 12, maxDist = 1.
PHY-3001 : Final: Len = 54534.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5849, tnet num: 1789, tinst num: 734, tnode num: 7836, tedge num: 10299.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 92/1791.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 61544, over cnt = 152(0%), over = 209, worst = 4
PHY-1002 : len = 62176, over cnt = 72(0%), over = 82, worst = 2
PHY-1002 : len = 63040, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 63192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128966s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (121.2%)

PHY-1001 : Congestion index: top1 = 30.99, top5 = 22.93, top10 = 17.61, top15 = 13.26.
PHY-1001 : End incremental global routing;  0.177133s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (114.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1789 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053125s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.257574s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 212, reserve = 174, peak = 212.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1566/1791.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005791s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (269.8%)

PHY-1001 : Congestion index: top1 = 30.99, top5 = 22.93, top10 = 17.61, top15 = 13.26.
OPT-1001 : End congestion update;  0.050047s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1789 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043097s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (108.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.094787s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (98.9%)

OPT-1001 : Current memory(MB): used = 214, reserve = 176, peak = 214.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1789 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.041760s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (74.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1566/1791.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006626s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (235.8%)

PHY-1001 : Congestion index: top1 = 30.99, top5 = 22.93, top10 = 17.61, top15 = 13.26.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1789 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.042181s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (74.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.762119s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (104.6%)

RUN-1003 : finish command "place" in  5.052570s wall, 7.484375s user + 2.781250s system = 10.265625s CPU (203.2%)

RUN-1004 : used memory is 193 MB, reserved memory is 155 MB, peak memory is 214 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 736 instances
RUN-1001 : 344 mslices, 344 lslices, 34 pads, 4 brams, 4 dsps
RUN-1001 : There are total 1791 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1297 nets have 2 pins
RUN-1001 : 382 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5849, tnet num: 1789, tinst num: 734, tnode num: 7836, tedge num: 10299.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 344 mslices, 344 lslices, 34 pads, 4 brams, 4 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1789 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60312, over cnt = 159(0%), over = 214, worst = 4
PHY-1002 : len = 61224, over cnt = 70(0%), over = 79, worst = 2
PHY-1002 : len = 62176, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 62240, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121483s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (141.5%)

PHY-1001 : Congestion index: top1 = 30.65, top5 = 22.73, top10 = 17.38, top15 = 13.10.
PHY-1001 : End global routing;  0.169956s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (128.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 229, reserve = 192, peak = 232.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 491, reserve = 458, peak = 491.
PHY-1001 : End build detailed router design. 3.200324s wall, 3.109375s user + 0.078125s system = 3.187500s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29304, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.300911s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 523, reserve = 491, peak = 523.
PHY-1001 : End phase 1; 1.306681s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 87% nets.
PHY-1022 : len = 166152, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 526, reserve = 492, peak = 526.
PHY-1001 : End initial routed; 1.136087s wall, 1.640625s user + 0.093750s system = 1.734375s CPU (152.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1576(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.318   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.276   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.338780s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (96.9%)

PHY-1001 : Current memory(MB): used = 528, reserve = 495, peak = 528.
PHY-1001 : End phase 2; 1.474959s wall, 1.968750s user + 0.093750s system = 2.062500s CPU (139.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 166152, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013527s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 166104, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.022671s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (68.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 166136, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.018778s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (83.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1576(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.318   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.276   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.318733s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.155163s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 542, reserve = 509, peak = 542.
PHY-1001 : End phase 3; 0.651453s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (100.7%)

PHY-1003 : Routed, final wirelength = 166136
PHY-1001 : Current memory(MB): used = 543, reserve = 509, peak = 543.
PHY-1001 : End export database. 0.010278s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (152.0%)

PHY-1001 : End detail routing;  6.824204s wall, 7.203125s user + 0.203125s system = 7.406250s CPU (108.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5849, tnet num: 1789, tinst num: 734, tnode num: 7836, tedge num: 10299.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[20] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[30] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[3] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5881, tnet num: 1805, tinst num: 750, tnode num: 7868, tedge num: 10331.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.884007s wall, 2.890625s user + 0.140625s system = 3.031250s CPU (105.1%)

RUN-1003 : finish command "route" in  10.208880s wall, 10.625000s user + 0.359375s system = 10.984375s CPU (107.6%)

RUN-1004 : used memory is 519 MB, reserved memory is 485 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      805   out of  19600    4.11%
#reg                      902   out of  19600    4.60%
#le                      1359
  #lut only               457   out of   1359   33.63%
  #reg only               554   out of   1359   40.77%
  #lut&reg                348   out of   1359   25.61%
#dsp                        4   out of     29   13.79%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         374
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       NONE    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       NONE    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       NONE    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       NONE    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       NONE    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       NONE    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       NONE    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       NONE    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       NONE    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       NONE    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       NONE    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       NONE    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       NONE    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       NONE    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1359   |606     |199     |919     |4       |4       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |966    |302     |115     |753     |4       |4       |
|    ctrl_signal             |SignalGenerator                                  |36     |29      |7       |21      |0       |0       |
|    demodu                  |Demodulation                                     |464    |137     |44      |346     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |58     |29      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |8       |0       |17      |0       |0       |
|    integ                   |Integration                                      |137    |21      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |2      |0       |0       |2       |0       |0       |
|    rs422                   |Rs422Output                                      |309    |102     |45      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |18     |13      |5       |17      |0       |0       |
|  u_uart                    |UART_Control                                     |119    |107     |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |17     |14      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |65     |65      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1277  
    #2          2       246   
    #3          3       124   
    #4          4        12   
    #5        5-10       78   
    #6        11-50      26   
    #7       101-500     1    
  Average     1.99            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5881, tnet num: 1805, tinst num: 750, tnode num: 7868, tedge num: 10331.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1805 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 750
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1807, pip num: 13530
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1179 valid insts, and 36105 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.922278s wall, 15.734375s user + 0.031250s system = 15.765625s CPU (539.5%)

RUN-1004 : used memory is 516 MB, reserved memory is 485 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_171546.log"
