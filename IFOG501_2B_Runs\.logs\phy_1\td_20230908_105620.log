============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Sep  8 10:56:20 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1626 instances
RUN-0007 : 362 luts, 1009 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2167 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1613 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 69 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1624 instances, 362 luts, 1009 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7711, tnet num: 2165, tinst num: 1624, tnode num: 10976, tedge num: 13041.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.470456s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (96.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 634666
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1624.
PHY-3001 : End clustering;  0.000042s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 531696, overlap = 20.25
PHY-3002 : Step(2): len = 479821, overlap = 13.5
PHY-3002 : Step(3): len = 404585, overlap = 15.75
PHY-3002 : Step(4): len = 336777, overlap = 13.5
PHY-3002 : Step(5): len = 319192, overlap = 18
PHY-3002 : Step(6): len = 296236, overlap = 18
PHY-3002 : Step(7): len = 289265, overlap = 20.25
PHY-3002 : Step(8): len = 282553, overlap = 20.25
PHY-3002 : Step(9): len = 274011, overlap = 20.25
PHY-3002 : Step(10): len = 265266, overlap = 20.25
PHY-3002 : Step(11): len = 261020, overlap = 20.25
PHY-3002 : Step(12): len = 253039, overlap = 20.25
PHY-3002 : Step(13): len = 239337, overlap = 20.25
PHY-3002 : Step(14): len = 233143, overlap = 20.25
PHY-3002 : Step(15): len = 229877, overlap = 20.25
PHY-3002 : Step(16): len = 213892, overlap = 20.25
PHY-3002 : Step(17): len = 205063, overlap = 20.25
PHY-3002 : Step(18): len = 202613, overlap = 20.25
PHY-3002 : Step(19): len = 193889, overlap = 20.25
PHY-3002 : Step(20): len = 185099, overlap = 20.25
PHY-3002 : Step(21): len = 182350, overlap = 20.25
PHY-3002 : Step(22): len = 176968, overlap = 20.25
PHY-3002 : Step(23): len = 169792, overlap = 18
PHY-3002 : Step(24): len = 165814, overlap = 20.25
PHY-3002 : Step(25): len = 162282, overlap = 20.25
PHY-3002 : Step(26): len = 158603, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000102669
PHY-3002 : Step(27): len = 160500, overlap = 13.5
PHY-3002 : Step(28): len = 158755, overlap = 13.5
PHY-3002 : Step(29): len = 155793, overlap = 15.75
PHY-3002 : Step(30): len = 151062, overlap = 15.75
PHY-3002 : Step(31): len = 147265, overlap = 11.25
PHY-3002 : Step(32): len = 143084, overlap = 13.5
PHY-3002 : Step(33): len = 141052, overlap = 11.25
PHY-3002 : Step(34): len = 137325, overlap = 13.5
PHY-3002 : Step(35): len = 131598, overlap = 6.75
PHY-3002 : Step(36): len = 127935, overlap = 13.5
PHY-3002 : Step(37): len = 126726, overlap = 13.5
PHY-3002 : Step(38): len = 122859, overlap = 11.25
PHY-3002 : Step(39): len = 119927, overlap = 13.5
PHY-3002 : Step(40): len = 117216, overlap = 11.25
PHY-3002 : Step(41): len = 116857, overlap = 11.25
PHY-3002 : Step(42): len = 112916, overlap = 13.5
PHY-3002 : Step(43): len = 107591, overlap = 11.25
PHY-3002 : Step(44): len = 105099, overlap = 11.4375
PHY-3002 : Step(45): len = 103060, overlap = 13.625
PHY-3002 : Step(46): len = 102520, overlap = 13.625
PHY-3002 : Step(47): len = 101524, overlap = 11.375
PHY-3002 : Step(48): len = 100333, overlap = 11.3125
PHY-3002 : Step(49): len = 98874, overlap = 11.3125
PHY-3002 : Step(50): len = 95693.8, overlap = 13.5
PHY-3002 : Step(51): len = 94677.1, overlap = 13.5
PHY-3002 : Step(52): len = 93283.1, overlap = 11.25
PHY-3002 : Step(53): len = 92455.4, overlap = 11.25
PHY-3002 : Step(54): len = 85234.6, overlap = 11.25
PHY-3002 : Step(55): len = 81800, overlap = 4.625
PHY-3002 : Step(56): len = 80809.6, overlap = 6.8125
PHY-3002 : Step(57): len = 79840, overlap = 9.0625
PHY-3002 : Step(58): len = 78741.3, overlap = 9
PHY-3002 : Step(59): len = 78516.3, overlap = 9
PHY-3002 : Step(60): len = 77814.7, overlap = 11.3125
PHY-3002 : Step(61): len = 76779.2, overlap = 9.0625
PHY-3002 : Step(62): len = 74308.8, overlap = 11.4375
PHY-3002 : Step(63): len = 72743.4, overlap = 11.375
PHY-3002 : Step(64): len = 72272.1, overlap = 11.25
PHY-3002 : Step(65): len = 71685.2, overlap = 13.5
PHY-3002 : Step(66): len = 71271.3, overlap = 9.0625
PHY-3002 : Step(67): len = 70695.7, overlap = 9.0625
PHY-3002 : Step(68): len = 70476.7, overlap = 9.0625
PHY-3002 : Step(69): len = 69740.8, overlap = 11.3125
PHY-3002 : Step(70): len = 69450.5, overlap = 13.5625
PHY-3002 : Step(71): len = 68738.1, overlap = 11.3125
PHY-3002 : Step(72): len = 67721.1, overlap = 11.3125
PHY-3002 : Step(73): len = 66869.2, overlap = 13.5
PHY-3002 : Step(74): len = 66387.6, overlap = 13.5
PHY-3002 : Step(75): len = 65734, overlap = 11.25
PHY-3002 : Step(76): len = 64677.8, overlap = 11.25
PHY-3002 : Step(77): len = 63777.1, overlap = 11.25
PHY-3002 : Step(78): len = 62638, overlap = 13.5
PHY-3002 : Step(79): len = 62129.1, overlap = 13.5625
PHY-3002 : Step(80): len = 60082.6, overlap = 11.25
PHY-3002 : Step(81): len = 59066.8, overlap = 11.3125
PHY-3002 : Step(82): len = 58566.4, overlap = 11.3125
PHY-3002 : Step(83): len = 58153.4, overlap = 13.5
PHY-3002 : Step(84): len = 56534.2, overlap = 11.25
PHY-3002 : Step(85): len = 55740.3, overlap = 11.25
PHY-3002 : Step(86): len = 55391.2, overlap = 11.25
PHY-3002 : Step(87): len = 54720.3, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000205338
PHY-3002 : Step(88): len = 54696.8, overlap = 11.25
PHY-3002 : Step(89): len = 54759.2, overlap = 11.25
PHY-3002 : Step(90): len = 54821.3, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000410677
PHY-3002 : Step(91): len = 54777.6, overlap = 9
PHY-3002 : Step(92): len = 54777.8, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.012620s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (247.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.128094s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (85.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 56480.8, overlap = 6.75
PHY-3002 : Step(94): len = 55460.8, overlap = 6.46875
PHY-3002 : Step(95): len = 54857.1, overlap = 6.21875
PHY-3002 : Step(96): len = 54123.9, overlap = 7.34375
PHY-3002 : Step(97): len = 53389.4, overlap = 6.875
PHY-3002 : Step(98): len = 52706.9, overlap = 6.625
PHY-3002 : Step(99): len = 52014.6, overlap = 7.03125
PHY-3002 : Step(100): len = 50926.2, overlap = 6.03125
PHY-3002 : Step(101): len = 50332.6, overlap = 6.21875
PHY-3002 : Step(102): len = 49553, overlap = 11.875
PHY-3002 : Step(103): len = 49026.8, overlap = 12.5625
PHY-3002 : Step(104): len = 48617.7, overlap = 12.9688
PHY-3002 : Step(105): len = 48195, overlap = 13.4062
PHY-3002 : Step(106): len = 47864.8, overlap = 12.5625
PHY-3002 : Step(107): len = 47532.9, overlap = 12.6875
PHY-3002 : Step(108): len = 46970.3, overlap = 12.9375
PHY-3002 : Step(109): len = 46555.7, overlap = 13.7812
PHY-3002 : Step(110): len = 46015.9, overlap = 14.4688
PHY-3002 : Step(111): len = 45748.7, overlap = 15.0312
PHY-3002 : Step(112): len = 45651.1, overlap = 15.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00043634
PHY-3002 : Step(113): len = 45555.2, overlap = 14.875
PHY-3002 : Step(114): len = 45628.5, overlap = 15
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000872681
PHY-3002 : Step(115): len = 45663.1, overlap = 15
PHY-3002 : Step(116): len = 45801.1, overlap = 15.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.122780s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.6477e-05
PHY-3002 : Step(117): len = 46186, overlap = 67.875
PHY-3002 : Step(118): len = 46472.1, overlap = 67.4688
PHY-3002 : Step(119): len = 46609.8, overlap = 65.875
PHY-3002 : Step(120): len = 46869.5, overlap = 64.5312
PHY-3002 : Step(121): len = 47134.6, overlap = 60.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000112954
PHY-3002 : Step(122): len = 47124.4, overlap = 55.6562
PHY-3002 : Step(123): len = 48092.9, overlap = 54.6562
PHY-3002 : Step(124): len = 48608.1, overlap = 49.9688
PHY-3002 : Step(125): len = 48537.8, overlap = 50.25
PHY-3002 : Step(126): len = 48246.3, overlap = 50.5625
PHY-3002 : Step(127): len = 48109.4, overlap = 50.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000225908
PHY-3002 : Step(128): len = 48547.4, overlap = 48.625
PHY-3002 : Step(129): len = 49066.3, overlap = 43.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000451816
PHY-3002 : Step(130): len = 49541.6, overlap = 42.5625
PHY-3002 : Step(131): len = 49877.3, overlap = 40.2188
PHY-3002 : Step(132): len = 51088.9, overlap = 36.5938
PHY-3002 : Step(133): len = 51363.9, overlap = 35.9688
PHY-3002 : Step(134): len = 50751.9, overlap = 35.4062
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000903632
PHY-3002 : Step(135): len = 50831.6, overlap = 34.9062
PHY-3002 : Step(136): len = 50766.7, overlap = 34.375
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00180726
PHY-3002 : Step(137): len = 50758.8, overlap = 33.5938
PHY-3002 : Step(138): len = 50831.4, overlap = 29.75
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00361453
PHY-3002 : Step(139): len = 51028.2, overlap = 29.0938
PHY-3002 : Step(140): len = 51028.2, overlap = 29.0938
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.00722905
PHY-3002 : Step(141): len = 51063.5, overlap = 29
PHY-3002 : Step(142): len = 51094.9, overlap = 28.8125
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0144581
PHY-3002 : Step(143): len = 51144.9, overlap = 28.75
PHY-3002 : Step(144): len = 51144.9, overlap = 28.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7711, tnet num: 2165, tinst num: 1624, tnode num: 10976, tedge num: 13041.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.88 peak overflow 2.16
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2167.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54696, over cnt = 234(0%), over = 933, worst = 15
PHY-1001 : End global iterations;  0.106165s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (117.7%)

PHY-1001 : Congestion index: top1 = 38.58, top5 = 24.03, top10 = 15.77, top15 = 11.39.
PHY-1001 : End incremental global routing;  0.180227s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (121.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.097559s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (96.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.323035s wall, 0.312500s user + 0.046875s system = 0.359375s CPU (111.2%)

OPT-1001 : Current memory(MB): used = 214, reserve = 176, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1638/2167.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54696, over cnt = 234(0%), over = 933, worst = 15
PHY-1002 : len = 60016, over cnt = 162(0%), over = 420, worst = 14
PHY-1002 : len = 62960, over cnt = 51(0%), over = 123, worst = 7
PHY-1002 : len = 64120, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 64184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128514s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (145.9%)

PHY-1001 : Congestion index: top1 = 34.03, top5 = 23.61, top10 = 17.14, top15 = 12.72.
OPT-1001 : End congestion update;  0.188648s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (132.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.083586s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (93.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.275625s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (119.0%)

OPT-1001 : Current memory(MB): used = 217, reserve = 180, peak = 217.
OPT-1001 : End physical optimization;  1.057241s wall, 1.078125s user + 0.078125s system = 1.156250s CPU (109.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 362 LUT to BLE ...
SYN-4008 : Packed 362 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 118 SEQ with LUT/SLICE
SYN-4006 : 87 single LUT's are left
SYN-4006 : 709 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1071/1384 primitive instances ...
PHY-3001 : End packing;  0.074073s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 816 instances
RUN-1001 : 383 mslices, 384 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1999 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1455 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 814 instances, 767 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51149, Over = 57.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6455, tnet num: 1997, tinst num: 814, tnode num: 8802, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.476265s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (101.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.60288e-05
PHY-3002 : Step(145): len = 50766.2, overlap = 58.25
PHY-3002 : Step(146): len = 50588.8, overlap = 58
PHY-3002 : Step(147): len = 50378.1, overlap = 56.25
PHY-3002 : Step(148): len = 50322.1, overlap = 56.25
PHY-3002 : Step(149): len = 50251.8, overlap = 56
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.20576e-05
PHY-3002 : Step(150): len = 50456.3, overlap = 53.75
PHY-3002 : Step(151): len = 50674.1, overlap = 53.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000131502
PHY-3002 : Step(152): len = 51198.2, overlap = 50
PHY-3002 : Step(153): len = 52344.9, overlap = 46
PHY-3002 : Step(154): len = 53055.3, overlap = 44.25
PHY-3002 : Step(155): len = 52842.1, overlap = 44
PHY-3002 : Step(156): len = 52703.2, overlap = 43
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.148786s wall, 0.125000s user + 0.187500s system = 0.312500s CPU (210.0%)

PHY-3001 : Trial Legalized: Len = 63720
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.081125s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000627808
PHY-3002 : Step(157): len = 61649.3, overlap = 6.75
PHY-3002 : Step(158): len = 59816.1, overlap = 12
PHY-3002 : Step(159): len = 58402, overlap = 15.75
PHY-3002 : Step(160): len = 57650.7, overlap = 18
PHY-3002 : Step(161): len = 56905.1, overlap = 20.25
PHY-3002 : Step(162): len = 56449.6, overlap = 22.25
PHY-3002 : Step(163): len = 56030.7, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00125562
PHY-3002 : Step(164): len = 56287.1, overlap = 22.75
PHY-3002 : Step(165): len = 56408.2, overlap = 22.75
PHY-3002 : Step(166): len = 56408.2, overlap = 22.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00251123
PHY-3002 : Step(167): len = 56570.2, overlap = 22.25
PHY-3002 : Step(168): len = 56570.2, overlap = 22.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009241s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 60460.7, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.009175s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (170.3%)

PHY-3001 : 9 instances has been re-located, deltaX = 1, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 60684.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6455, tnet num: 1997, tinst num: 814, tnode num: 8802, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 160/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67480, over cnt = 154(0%), over = 231, worst = 7
PHY-1002 : len = 68280, over cnt = 111(0%), over = 143, worst = 4
PHY-1002 : len = 69648, over cnt = 30(0%), over = 32, worst = 3
PHY-1002 : len = 70152, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.185937s wall, 0.218750s user + 0.062500s system = 0.281250s CPU (151.3%)

PHY-1001 : Congestion index: top1 = 30.54, top5 = 22.98, top10 = 18.12, top15 = 14.16.
PHY-1001 : End incremental global routing;  0.255356s wall, 0.281250s user + 0.062500s system = 0.343750s CPU (134.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080129s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.376731s wall, 0.406250s user + 0.062500s system = 0.468750s CPU (124.4%)

OPT-1001 : Current memory(MB): used = 222, reserve = 183, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1771/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008438s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (185.2%)

PHY-1001 : Congestion index: top1 = 30.54, top5 = 22.98, top10 = 18.12, top15 = 14.16.
OPT-1001 : End congestion update;  0.068138s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074302s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 776 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 814 instances, 767 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 60701, Over = 0
PHY-3001 : End spreading;  0.006446s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (242.4%)

PHY-3001 : Final: Len = 60701, Over = 0
PHY-3001 : End incremental legalization;  0.045988s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.211240s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (88.8%)

OPT-1001 : Current memory(MB): used = 226, reserve = 188, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065150s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1767/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70200, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.019879s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (78.6%)

PHY-1001 : Congestion index: top1 = 30.54, top5 = 22.97, top10 = 18.11, top15 = 14.15.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062232s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.287183s wall, 1.312500s user + 0.109375s system = 1.421875s CPU (110.5%)

RUN-1003 : finish command "place" in  11.593279s wall, 18.593750s user + 7.468750s system = 26.062500s CPU (224.8%)

RUN-1004 : used memory is 206 MB, reserved memory is 167 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 816 instances
RUN-1001 : 383 mslices, 384 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1999 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1455 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6455, tnet num: 1997, tinst num: 814, tnode num: 8802, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 383 mslices, 384 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66328, over cnt = 176(0%), over = 267, worst = 7
PHY-1002 : len = 67576, over cnt = 96(0%), over = 123, worst = 5
PHY-1002 : len = 69048, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 69144, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 69192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.190770s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (122.9%)

PHY-1001 : Congestion index: top1 = 30.75, top5 = 22.74, top10 = 17.88, top15 = 13.97.
PHY-1001 : End global routing;  0.255467s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (122.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 203, peak = 243.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 502, reserve = 469, peak = 502.
PHY-1001 : End build detailed router design. 3.990833s wall, 3.953125s user + 0.031250s system = 3.984375s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33800, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.553051s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 535, reserve = 502, peak = 535.
PHY-1001 : End phase 1; 1.558984s wall, 1.546875s user + 0.000000s system = 1.546875s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 174616, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 503, peak = 536.
PHY-1001 : End initial routed; 1.318733s wall, 2.453125s user + 0.109375s system = 2.562500s CPU (194.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1780(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.628   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.187   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.485840s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End phase 2; 1.804682s wall, 2.937500s user + 0.109375s system = 3.046875s CPU (168.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 174616, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017950s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (87.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 174512, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.038701s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (201.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 174560, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027096s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1780(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.628   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.187   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.473640s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (99.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.203035s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 551, reserve = 518, peak = 551.
PHY-1001 : End phase 3; 0.924291s wall, 0.921875s user + 0.031250s system = 0.953125s CPU (103.1%)

PHY-1003 : Routed, final wirelength = 174560
PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End export database. 0.011220s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (139.3%)

PHY-1001 : End detail routing;  8.513164s wall, 9.593750s user + 0.171875s system = 9.765625s CPU (114.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6455, tnet num: 1997, tinst num: 814, tnode num: 8802, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[32] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[6] slack -52ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6481, tnet num: 2010, tinst num: 827, tnode num: 8828, tedge num: 11386.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.883534s wall, 3.859375s user + 0.234375s system = 4.093750s CPU (105.4%)

RUN-1003 : finish command "route" in  13.131034s wall, 14.234375s user + 0.406250s system = 14.640625s CPU (111.5%)

RUN-1004 : used memory is 528 MB, reserved memory is 494 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      803   out of  19600    4.10%
#reg                     1075   out of  19600    5.48%
#le                      1512
  #lut only               437   out of   1512   28.90%
  #reg only               709   out of   1512   46.89%
  #lut&reg                366   out of   1512   24.21%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         470
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         107
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1512   |597     |206     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1150   |323     |125     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |22      |9       |21      |0       |0       |
|    demodu                  |Demodulation                                     |549    |143     |53      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |62      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |0       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |12      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |16      |0       |29      |0       |0       |
|    integ                   |Integration                                      |135    |20      |14      |109     |0       |0       |
|    modu                    |Modulation                                       |97     |24      |15      |83      |0       |1       |
|    rs422                   |Rs422Output                                      |306    |87      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |32     |27      |5       |23      |0       |0       |
|  u_uart                    |UART_Control                                     |95     |81      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |25      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |39     |37      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1432  
    #2          2       305   
    #3          3       104   
    #4          4        24   
    #5        5-10       73   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6481, tnet num: 2010, tinst num: 827, tnode num: 8828, tedge num: 11386.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 827
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2012, pip num: 14506
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1264 valid insts, and 38376 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.398909s wall, 26.546875s user + 0.109375s system = 26.656250s CPU (493.7%)

RUN-1004 : used memory is 523 MB, reserved memory is 491 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230908_105620.log"
