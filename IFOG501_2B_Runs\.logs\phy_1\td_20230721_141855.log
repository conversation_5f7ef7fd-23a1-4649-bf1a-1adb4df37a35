============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 14:18:55 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1627 instances
RUN-0007 : 368 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2197 nets
RUN-1001 : 1638 nets have 2 pins
RUN-1001 : 445 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1625 instances, 368 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7787, tnet num: 2195, tinst num: 1625, tnode num: 11027, tedge num: 13168.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.297427s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 587442
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1625.
PHY-3001 : End clustering;  0.000055s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 555491, overlap = 20.25
PHY-3002 : Step(2): len = 440670, overlap = 20.25
PHY-3002 : Step(3): len = 376722, overlap = 15.75
PHY-3002 : Step(4): len = 350978, overlap = 15.75
PHY-3002 : Step(5): len = 338155, overlap = 15.75
PHY-3002 : Step(6): len = 331216, overlap = 15.75
PHY-3002 : Step(7): len = 319949, overlap = 15.75
PHY-3002 : Step(8): len = 306572, overlap = 18
PHY-3002 : Step(9): len = 301603, overlap = 18
PHY-3002 : Step(10): len = 293509, overlap = 18
PHY-3002 : Step(11): len = 282603, overlap = 20.25
PHY-3002 : Step(12): len = 278062, overlap = 20.25
PHY-3002 : Step(13): len = 272437, overlap = 20.25
PHY-3002 : Step(14): len = 263233, overlap = 20.25
PHY-3002 : Step(15): len = 257376, overlap = 20.25
PHY-3002 : Step(16): len = 253838, overlap = 20.25
PHY-3002 : Step(17): len = 245352, overlap = 20.25
PHY-3002 : Step(18): len = 238969, overlap = 20.25
PHY-3002 : Step(19): len = 235585, overlap = 20.25
PHY-3002 : Step(20): len = 230261, overlap = 20.25
PHY-3002 : Step(21): len = 222646, overlap = 20.25
PHY-3002 : Step(22): len = 216513, overlap = 20.25
PHY-3002 : Step(23): len = 213892, overlap = 20.25
PHY-3002 : Step(24): len = 206145, overlap = 20.25
PHY-3002 : Step(25): len = 195053, overlap = 20.25
PHY-3002 : Step(26): len = 192664, overlap = 20.25
PHY-3002 : Step(27): len = 188024, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000155561
PHY-3002 : Step(28): len = 189444, overlap = 13.5
PHY-3002 : Step(29): len = 186630, overlap = 9
PHY-3002 : Step(30): len = 183531, overlap = 15.75
PHY-3002 : Step(31): len = 175840, overlap = 9
PHY-3002 : Step(32): len = 171151, overlap = 6.75
PHY-3002 : Step(33): len = 166911, overlap = 15.75
PHY-3002 : Step(34): len = 165915, overlap = 9
PHY-3002 : Step(35): len = 162077, overlap = 6.75
PHY-3002 : Step(36): len = 154489, overlap = 6.75
PHY-3002 : Step(37): len = 150907, overlap = 6.75
PHY-3002 : Step(38): len = 149036, overlap = 6.75
PHY-3002 : Step(39): len = 143716, overlap = 13.5
PHY-3002 : Step(40): len = 138669, overlap = 13.5
PHY-3002 : Step(41): len = 136189, overlap = 11.25
PHY-3002 : Step(42): len = 135706, overlap = 6.75
PHY-3002 : Step(43): len = 133295, overlap = 6.75
PHY-3002 : Step(44): len = 131629, overlap = 6.75
PHY-3002 : Step(45): len = 130092, overlap = 6.75
PHY-3002 : Step(46): len = 127498, overlap = 11.25
PHY-3002 : Step(47): len = 121609, overlap = 9
PHY-3002 : Step(48): len = 119857, overlap = 11.25
PHY-3002 : Step(49): len = 118041, overlap = 9
PHY-3002 : Step(50): len = 116692, overlap = 6.75
PHY-3002 : Step(51): len = 112250, overlap = 6.75
PHY-3002 : Step(52): len = 111026, overlap = 4.5
PHY-3002 : Step(53): len = 108471, overlap = 4.5
PHY-3002 : Step(54): len = 106268, overlap = 11.25
PHY-3002 : Step(55): len = 104541, overlap = 9
PHY-3002 : Step(56): len = 103451, overlap = 6.75
PHY-3002 : Step(57): len = 97664.2, overlap = 11.25
PHY-3002 : Step(58): len = 95246.6, overlap = 6.75
PHY-3002 : Step(59): len = 94529, overlap = 6.75
PHY-3002 : Step(60): len = 93424.8, overlap = 11.25
PHY-3002 : Step(61): len = 92922.5, overlap = 9
PHY-3002 : Step(62): len = 89054, overlap = 9
PHY-3002 : Step(63): len = 83099.2, overlap = 6.75
PHY-3002 : Step(64): len = 81916.9, overlap = 9
PHY-3002 : Step(65): len = 79092.9, overlap = 6.75
PHY-3002 : Step(66): len = 78448.8, overlap = 11.25
PHY-3002 : Step(67): len = 78054.6, overlap = 6.75
PHY-3002 : Step(68): len = 77539, overlap = 6.75
PHY-3002 : Step(69): len = 76257.3, overlap = 6.75
PHY-3002 : Step(70): len = 74351.4, overlap = 6.75
PHY-3002 : Step(71): len = 72943, overlap = 4.5
PHY-3002 : Step(72): len = 72816.2, overlap = 4.5
PHY-3002 : Step(73): len = 72093.5, overlap = 9
PHY-3002 : Step(74): len = 70586.6, overlap = 9
PHY-3002 : Step(75): len = 69090, overlap = 13.5
PHY-3002 : Step(76): len = 66408.2, overlap = 9
PHY-3002 : Step(77): len = 65771, overlap = 9
PHY-3002 : Step(78): len = 65099.3, overlap = 9
PHY-3002 : Step(79): len = 64253, overlap = 9
PHY-3002 : Step(80): len = 64033.7, overlap = 9
PHY-3002 : Step(81): len = 63517.3, overlap = 4.5
PHY-3002 : Step(82): len = 62673.1, overlap = 4.5
PHY-3002 : Step(83): len = 61616.6, overlap = 9
PHY-3002 : Step(84): len = 61226.1, overlap = 6.75
PHY-3002 : Step(85): len = 60571.6, overlap = 6.75
PHY-3002 : Step(86): len = 59816.6, overlap = 6.75
PHY-3002 : Step(87): len = 59345.1, overlap = 13.5
PHY-3002 : Step(88): len = 58138.9, overlap = 11.25
PHY-3002 : Step(89): len = 57742.1, overlap = 6.75
PHY-3002 : Step(90): len = 57189.9, overlap = 9
PHY-3002 : Step(91): len = 56279.1, overlap = 9
PHY-3002 : Step(92): len = 55668.7, overlap = 9
PHY-3002 : Step(93): len = 54873.2, overlap = 6.75
PHY-3002 : Step(94): len = 54358.4, overlap = 9
PHY-3002 : Step(95): len = 53242.6, overlap = 11.25
PHY-3002 : Step(96): len = 52648.1, overlap = 13.5
PHY-3002 : Step(97): len = 51309.2, overlap = 6.75
PHY-3002 : Step(98): len = 50360.8, overlap = 9
PHY-3002 : Step(99): len = 50171, overlap = 9
PHY-3002 : Step(100): len = 49874.2, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000311121
PHY-3002 : Step(101): len = 49938.2, overlap = 6.75
PHY-3002 : Step(102): len = 49948.8, overlap = 4.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000622243
PHY-3002 : Step(103): len = 50043.3, overlap = 4.5
PHY-3002 : Step(104): len = 50040.7, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.018726s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060303s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00348509
PHY-3002 : Step(105): len = 53334.6, overlap = 7.46875
PHY-3002 : Step(106): len = 52734.4, overlap = 7.21875
PHY-3002 : Step(107): len = 52406.6, overlap = 7.25
PHY-3002 : Step(108): len = 51547.1, overlap = 7.53125
PHY-3002 : Step(109): len = 51377, overlap = 7.78125
PHY-3002 : Step(110): len = 50775.6, overlap = 6.78125
PHY-3002 : Step(111): len = 50222.3, overlap = 6.5625
PHY-3002 : Step(112): len = 49950.2, overlap = 5.9375
PHY-3002 : Step(113): len = 48909.2, overlap = 4.9375
PHY-3002 : Step(114): len = 48749.6, overlap = 4.75
PHY-3002 : Step(115): len = 48324.5, overlap = 4.6875
PHY-3002 : Step(116): len = 48199.1, overlap = 4.375
PHY-3002 : Step(117): len = 48105.4, overlap = 3.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00697018
PHY-3002 : Step(118): len = 47870.3, overlap = 3.4375
PHY-3002 : Step(119): len = 47760.5, overlap = 3.4375
PHY-3002 : Step(120): len = 47448.8, overlap = 3.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0139404
PHY-3002 : Step(121): len = 47446.6, overlap = 3.3125
PHY-3002 : Step(122): len = 47349.4, overlap = 4.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058547s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.1023e-05
PHY-3002 : Step(123): len = 47904.9, overlap = 51.0938
PHY-3002 : Step(124): len = 48444.9, overlap = 52.6562
PHY-3002 : Step(125): len = 49452.9, overlap = 44.1875
PHY-3002 : Step(126): len = 49396.8, overlap = 43.2812
PHY-3002 : Step(127): len = 49482.4, overlap = 42.9062
PHY-3002 : Step(128): len = 49540.7, overlap = 43.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000182046
PHY-3002 : Step(129): len = 49477.5, overlap = 42.9688
PHY-3002 : Step(130): len = 49564.2, overlap = 43
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000364092
PHY-3002 : Step(131): len = 49945.9, overlap = 41.625
PHY-3002 : Step(132): len = 50859.9, overlap = 36.5625
PHY-3002 : Step(133): len = 52311.2, overlap = 32
PHY-3002 : Step(134): len = 52675.7, overlap = 26.0312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7787, tnet num: 2195, tinst num: 1625, tnode num: 11027, tedge num: 13168.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.16 peak overflow 3.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2197.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55424, over cnt = 238(0%), over = 1003, worst = 19
PHY-1001 : End global iterations;  0.077009s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (121.7%)

PHY-1001 : Congestion index: top1 = 42.80, top5 = 25.45, top10 = 16.57, top15 = 11.72.
PHY-1001 : End incremental global routing;  0.125264s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (112.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069066s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1586 has valid locations, 2 needs to be replaced
PHY-3001 : design contains 1626 instances, 369 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 52838.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7790, tnet num: 2196, tinst num: 1626, tnode num: 11030, tedge num: 13172.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.306108s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (102.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(135): len = 52788.6, overlap = 0.9375
PHY-3002 : Step(136): len = 52788.6, overlap = 0.9375
PHY-3002 : Step(137): len = 52799.3, overlap = 0.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064483s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (121.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(138): len = 52795.4, overlap = 26.125
PHY-3002 : Step(139): len = 52795.4, overlap = 26.125
PHY-3001 : Final: Len = 52795.4, Over = 26.125
PHY-3001 : End incremental placement;  0.446551s wall, 0.468750s user + 0.031250s system = 0.500000s CPU (112.0%)

OPT-1001 : Total overflow 92.25 peak overflow 3.41
OPT-1001 : End high-fanout net optimization;  0.678140s wall, 0.703125s user + 0.031250s system = 0.734375s CPU (108.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55488, over cnt = 239(0%), over = 1004, worst = 19
PHY-1002 : len = 61512, over cnt = 151(0%), over = 367, worst = 16
PHY-1002 : len = 66112, over cnt = 21(0%), over = 23, worst = 3
PHY-1002 : len = 66440, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 66552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.088822s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (123.1%)

PHY-1001 : Congestion index: top1 = 35.75, top5 = 25.10, top10 = 18.26, top15 = 13.43.
OPT-1001 : End congestion update;  0.135647s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (115.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058009s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196114s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.5%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 220.
OPT-1001 : End physical optimization;  1.160856s wall, 1.203125s user + 0.031250s system = 1.234375s CPU (106.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 108 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 695 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1064/1396 primitive instances ...
PHY-3001 : End packing;  0.052016s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-1001 : 1477 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51994.8, Over = 57.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2028, tinst num: 835, tnode num: 8916, tedge num: 11544.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.320087s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (102.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.65414e-05
PHY-3002 : Step(140): len = 51475, overlap = 62
PHY-3002 : Step(141): len = 50641.1, overlap = 66
PHY-3002 : Step(142): len = 50270.3, overlap = 69.5
PHY-3002 : Step(143): len = 50475.5, overlap = 69.25
PHY-3002 : Step(144): len = 50430.8, overlap = 71.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.30828e-05
PHY-3002 : Step(145): len = 50861.2, overlap = 68
PHY-3002 : Step(146): len = 51475.1, overlap = 62.75
PHY-3002 : Step(147): len = 52166.5, overlap = 58
PHY-3002 : Step(148): len = 52204.9, overlap = 59
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000106166
PHY-3002 : Step(149): len = 52819.9, overlap = 58.25
PHY-3002 : Step(150): len = 54232.6, overlap = 52.25
PHY-3002 : Step(151): len = 54530.8, overlap = 50.5
PHY-3002 : Step(152): len = 54243.5, overlap = 50.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.082090s wall, 0.140625s user + 0.125000s system = 0.265625s CPU (323.6%)

PHY-3001 : Trial Legalized: Len = 66931.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054668s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000607835
PHY-3002 : Step(153): len = 64404.2, overlap = 3.75
PHY-3002 : Step(154): len = 62161.8, overlap = 11.25
PHY-3002 : Step(155): len = 60664.4, overlap = 14.25
PHY-3002 : Step(156): len = 59630, overlap = 18.25
PHY-3002 : Step(157): len = 58941.7, overlap = 19.25
PHY-3002 : Step(158): len = 58644.1, overlap = 21
PHY-3002 : Step(159): len = 58379.5, overlap = 22.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00121567
PHY-3002 : Step(160): len = 58802.9, overlap = 21.5
PHY-3002 : Step(161): len = 58939.5, overlap = 20.5
PHY-3002 : Step(162): len = 58945.1, overlap = 20.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00243134
PHY-3002 : Step(163): len = 59082.7, overlap = 20
PHY-3002 : Step(164): len = 59198.6, overlap = 19.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004952s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63481.2, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006530s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 1, deltaY = 11, maxDist = 2.
PHY-3001 : Final: Len = 63579.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2028, tinst num: 835, tnode num: 8916, tedge num: 11544.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 77/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70152, over cnt = 134(0%), over = 214, worst = 6
PHY-1002 : len = 71144, over cnt = 69(0%), over = 80, worst = 4
PHY-1002 : len = 72128, over cnt = 2(0%), over = 3, worst = 2
PHY-1002 : len = 72184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119089s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (118.1%)

PHY-1001 : Congestion index: top1 = 30.56, top5 = 22.58, top10 = 17.88, top15 = 14.20.
PHY-1001 : End incremental global routing;  0.171354s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (109.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059931s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.261249s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (107.7%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1790/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006147s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.56, top5 = 22.58, top10 = 17.88, top15 = 14.20.
OPT-1001 : End congestion update;  0.052938s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051921s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 797 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63675.8, Over = 0
PHY-3001 : End spreading;  0.004900s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63675.8, Over = 0
PHY-3001 : End incremental legalization;  0.034384s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.9%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.151647s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.0%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047325s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1782/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008509s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.71, top5 = 22.60, top10 = 17.90, top15 = 14.22.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049508s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (126.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.858170s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (105.6%)

RUN-1003 : finish command "place" in  5.703768s wall, 8.890625s user + 2.765625s system = 11.656250s CPU (204.4%)

RUN-1004 : used memory is 204 MB, reserved memory is 168 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-1001 : 1477 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2028, tinst num: 835, tnode num: 8916, tedge num: 11544.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69824, over cnt = 138(0%), over = 217, worst = 6
PHY-1002 : len = 70304, over cnt = 91(0%), over = 138, worst = 6
PHY-1002 : len = 71896, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121385s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (115.9%)

PHY-1001 : Congestion index: top1 = 30.37, top5 = 22.54, top10 = 17.80, top15 = 14.13.
PHY-1001 : End global routing;  0.173966s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 503, reserve = 472, peak = 503.
PHY-1001 : End build detailed router design. 3.269043s wall, 3.265625s user + 0.015625s system = 3.281250s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34352, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.302773s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 535, reserve = 506, peak = 536.
PHY-1001 : End phase 1; 1.308894s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186480, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 507, peak = 536.
PHY-1001 : End initial routed; 1.071635s wall, 2.125000s user + 0.093750s system = 2.218750s CPU (207.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.317   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.385792s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.3%)

PHY-1001 : Current memory(MB): used = 538, reserve = 508, peak = 538.
PHY-1001 : End phase 2; 1.457517s wall, 2.515625s user + 0.093750s system = 2.609375s CPU (179.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186480, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016122s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (96.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186480, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031990s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186504, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.018572s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (84.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.317   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366247s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.178699s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.2%)

PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End phase 3; 0.729834s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (98.5%)

PHY-1003 : Routed, final wirelength = 186504
PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End export database. 0.009624s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.962024s wall, 8.000000s user + 0.109375s system = 8.109375s CPU (116.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2028, tinst num: 835, tnode num: 8916, tedge num: 11544.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.881727s wall, 8.953125s user + 0.109375s system = 9.062500s CPU (115.0%)

RUN-1004 : used memory is 528 MB, reserved memory is 500 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      824   out of  19600    4.20%
#reg                     1074   out of  19600    5.48%
#le                      1519
  #lut only               445   out of   1519   29.30%
  #reg only               695   out of   1519   45.75%
  #lut&reg                379   out of   1519   24.95%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         472
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1519   |599     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1125   |305     |132     |921     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |527    |124     |57      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |24     |10      |0       |24      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |31     |16      |0       |31      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |92     |31      |21      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |91      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |105    |91      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |23     |21      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |46     |42      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |206    |161     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1441  
    #2          2       316   
    #3          3       102   
    #4          4        19   
    #5        5-10       78   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2028, tinst num: 835, tnode num: 8916, tedge num: 11544.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 835
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2030, pip num: 14807
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1343 valid insts, and 39246 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.220658s wall, 18.031250s user + 0.062500s system = 18.093750s CPU (561.8%)

RUN-1004 : used memory is 548 MB, reserved memory is 518 MB, peak memory is 673 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_141855.log"
