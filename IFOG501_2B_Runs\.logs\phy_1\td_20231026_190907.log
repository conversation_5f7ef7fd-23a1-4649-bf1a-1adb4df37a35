============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 19:09:07 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1527 instances
RUN-0007 : 377 luts, 904 seqs, 122 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2055 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1534 nets have 2 pins
RUN-1001 : 405 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     252     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1525 instances, 377 luts, 904 seqs, 197 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7271, tnet num: 2053, tinst num: 1525, tnode num: 10200, tedge num: 12278.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2053 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.262860s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (101.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 536140
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1525.
PHY-3001 : End clustering;  0.000027s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 453192, overlap = 20.25
PHY-3002 : Step(2): len = 420552, overlap = 20.25
PHY-3002 : Step(3): len = 399711, overlap = 18
PHY-3002 : Step(4): len = 379479, overlap = 20.25
PHY-3002 : Step(5): len = 372623, overlap = 18
PHY-3002 : Step(6): len = 362438, overlap = 20.25
PHY-3002 : Step(7): len = 351771, overlap = 18
PHY-3002 : Step(8): len = 345509, overlap = 20.25
PHY-3002 : Step(9): len = 336358, overlap = 18
PHY-3002 : Step(10): len = 323860, overlap = 20.25
PHY-3002 : Step(11): len = 318298, overlap = 18
PHY-3002 : Step(12): len = 310813, overlap = 20.25
PHY-3002 : Step(13): len = 297772, overlap = 18
PHY-3002 : Step(14): len = 290630, overlap = 18
PHY-3002 : Step(15): len = 286222, overlap = 18
PHY-3002 : Step(16): len = 275927, overlap = 18
PHY-3002 : Step(17): len = 267790, overlap = 18
PHY-3002 : Step(18): len = 263986, overlap = 15.75
PHY-3002 : Step(19): len = 256886, overlap = 15.75
PHY-3002 : Step(20): len = 242996, overlap = 15.75
PHY-3002 : Step(21): len = 239217, overlap = 15.75
PHY-3002 : Step(22): len = 235659, overlap = 13.5
PHY-3002 : Step(23): len = 220287, overlap = 15.75
PHY-3002 : Step(24): len = 211342, overlap = 15.75
PHY-3002 : Step(25): len = 209391, overlap = 15.75
PHY-3002 : Step(26): len = 195067, overlap = 13.5
PHY-3002 : Step(27): len = 181896, overlap = 18
PHY-3002 : Step(28): len = 179202, overlap = 15.75
PHY-3002 : Step(29): len = 175330, overlap = 15.75
PHY-3002 : Step(30): len = 164767, overlap = 18
PHY-3002 : Step(31): len = 158988, overlap = 15.75
PHY-3002 : Step(32): len = 156782, overlap = 15.75
PHY-3002 : Step(33): len = 151621, overlap = 15.75
PHY-3002 : Step(34): len = 148029, overlap = 13.5
PHY-3002 : Step(35): len = 144551, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113431
PHY-3002 : Step(36): len = 144069, overlap = 15.75
PHY-3002 : Step(37): len = 143066, overlap = 13.5
PHY-3002 : Step(38): len = 141749, overlap = 13.5
PHY-3002 : Step(39): len = 140118, overlap = 15.75
PHY-3002 : Step(40): len = 138923, overlap = 15.75
PHY-3002 : Step(41): len = 133594, overlap = 13.5
PHY-3002 : Step(42): len = 128967, overlap = 11.25
PHY-3002 : Step(43): len = 127522, overlap = 11.25
PHY-3002 : Step(44): len = 126007, overlap = 11.25
PHY-3002 : Step(45): len = 122980, overlap = 13.5
PHY-3002 : Step(46): len = 120675, overlap = 11.25
PHY-3002 : Step(47): len = 118477, overlap = 11.25
PHY-3002 : Step(48): len = 115808, overlap = 9
PHY-3002 : Step(49): len = 113024, overlap = 13.5
PHY-3002 : Step(50): len = 111429, overlap = 13.5
PHY-3002 : Step(51): len = 104946, overlap = 13.5
PHY-3002 : Step(52): len = 101792, overlap = 13.5
PHY-3002 : Step(53): len = 100329, overlap = 13.5
PHY-3002 : Step(54): len = 98850.8, overlap = 13.5
PHY-3002 : Step(55): len = 96518.8, overlap = 11.25
PHY-3002 : Step(56): len = 92752.1, overlap = 11.25
PHY-3002 : Step(57): len = 90487.9, overlap = 11.25
PHY-3002 : Step(58): len = 89080, overlap = 9
PHY-3002 : Step(59): len = 87734.6, overlap = 9
PHY-3002 : Step(60): len = 85711.9, overlap = 13.5
PHY-3002 : Step(61): len = 84578.7, overlap = 13.5
PHY-3002 : Step(62): len = 81534.3, overlap = 13.5
PHY-3002 : Step(63): len = 79308.5, overlap = 13.5625
PHY-3002 : Step(64): len = 77761.5, overlap = 13.5
PHY-3002 : Step(65): len = 77179.3, overlap = 11.3125
PHY-3002 : Step(66): len = 76063, overlap = 15.9375
PHY-3002 : Step(67): len = 74845.4, overlap = 16.3125
PHY-3002 : Step(68): len = 73811.4, overlap = 11.875
PHY-3002 : Step(69): len = 73217, overlap = 11.75
PHY-3002 : Step(70): len = 72217.3, overlap = 13.9375
PHY-3002 : Step(71): len = 71770.2, overlap = 16
PHY-3002 : Step(72): len = 69946.1, overlap = 14.0625
PHY-3002 : Step(73): len = 67261.6, overlap = 17.625
PHY-3002 : Step(74): len = 66692.2, overlap = 17.75
PHY-3002 : Step(75): len = 65425.5, overlap = 17.9375
PHY-3002 : Step(76): len = 64435.7, overlap = 18.25
PHY-3002 : Step(77): len = 64131.1, overlap = 18.5
PHY-3002 : Step(78): len = 63443.6, overlap = 15.875
PHY-3002 : Step(79): len = 63337.2, overlap = 15.875
PHY-3002 : Step(80): len = 62921.1, overlap = 15.9375
PHY-3002 : Step(81): len = 62355.5, overlap = 15.6875
PHY-3002 : Step(82): len = 61780.9, overlap = 13.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000226862
PHY-3002 : Step(83): len = 62145.8, overlap = 13.4375
PHY-3002 : Step(84): len = 62180.1, overlap = 13.4375
PHY-3002 : Step(85): len = 62179.5, overlap = 13.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000453724
PHY-3002 : Step(86): len = 62008.9, overlap = 13.4375
PHY-3002 : Step(87): len = 61941.8, overlap = 13.4375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005999s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (260.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2053 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058580s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00053707
PHY-3002 : Step(88): len = 66404.2, overlap = 14.1562
PHY-3002 : Step(89): len = 65895.5, overlap = 12.4688
PHY-3002 : Step(90): len = 64138.7, overlap = 9.375
PHY-3002 : Step(91): len = 62550.9, overlap = 9.25
PHY-3002 : Step(92): len = 61235.3, overlap = 9.8125
PHY-3002 : Step(93): len = 59865.1, overlap = 10.875
PHY-3002 : Step(94): len = 58758.3, overlap = 10.7188
PHY-3002 : Step(95): len = 58319.4, overlap = 9.65625
PHY-3002 : Step(96): len = 58163.3, overlap = 11.4375
PHY-3002 : Step(97): len = 58134.8, overlap = 11.2812
PHY-3002 : Step(98): len = 57125.8, overlap = 10.1562
PHY-3002 : Step(99): len = 56201.9, overlap = 10.4062
PHY-3002 : Step(100): len = 54468.2, overlap = 10.2188
PHY-3002 : Step(101): len = 53619.6, overlap = 8.21875
PHY-3002 : Step(102): len = 52959.1, overlap = 9.90625
PHY-3002 : Step(103): len = 52397.6, overlap = 9.09375
PHY-3002 : Step(104): len = 52233.6, overlap = 8.8125
PHY-3002 : Step(105): len = 52118.2, overlap = 8.8125
PHY-3002 : Step(106): len = 52195, overlap = 8.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00107414
PHY-3002 : Step(107): len = 51911.5, overlap = 8.75
PHY-3002 : Step(108): len = 51577.9, overlap = 8.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00214828
PHY-3002 : Step(109): len = 51323.9, overlap = 8.25
PHY-3002 : Step(110): len = 51519.7, overlap = 8.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2053 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055735s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000105538
PHY-3002 : Step(111): len = 51778.5, overlap = 51.3438
PHY-3002 : Step(112): len = 52889.8, overlap = 44.6562
PHY-3002 : Step(113): len = 52881.9, overlap = 45.7188
PHY-3002 : Step(114): len = 52148.4, overlap = 45.9062
PHY-3002 : Step(115): len = 51922.2, overlap = 45.5312
PHY-3002 : Step(116): len = 51534.9, overlap = 43.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000211077
PHY-3002 : Step(117): len = 51745.3, overlap = 43.4375
PHY-3002 : Step(118): len = 52561.6, overlap = 39.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000422153
PHY-3002 : Step(119): len = 52470.1, overlap = 39.5625
PHY-3002 : Step(120): len = 52894.1, overlap = 37.0938
PHY-3002 : Step(121): len = 53627, overlap = 33
PHY-3002 : Step(122): len = 53976.7, overlap = 31.8438
PHY-3002 : Step(123): len = 53656.1, overlap = 31.0312
PHY-3002 : Step(124): len = 53140.2, overlap = 30.9375
PHY-3002 : Step(125): len = 52019.9, overlap = 30.2188
PHY-3002 : Step(126): len = 50974.6, overlap = 31.875
PHY-3002 : Step(127): len = 50544.3, overlap = 31.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7271, tnet num: 2053, tinst num: 1525, tnode num: 10200, tedge num: 12278.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.16 peak overflow 3.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2055.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52976, over cnt = 240(0%), over = 1011, worst = 18
PHY-1001 : End global iterations;  0.076278s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (122.9%)

PHY-1001 : Congestion index: top1 = 41.96, top5 = 25.03, top10 = 15.29, top15 = 10.75.
PHY-1001 : End incremental global routing;  0.125883s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (111.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2053 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059058s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.213003s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (110.0%)

OPT-1001 : Current memory(MB): used = 212, reserve = 174, peak = 212.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1508/2055.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52976, over cnt = 240(0%), over = 1011, worst = 18
PHY-1002 : len = 59016, over cnt = 153(0%), over = 323, worst = 15
PHY-1002 : len = 62384, over cnt = 38(0%), over = 77, worst = 12
PHY-1002 : len = 63384, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 63496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095772s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (163.1%)

PHY-1001 : Congestion index: top1 = 36.59, top5 = 24.29, top10 = 17.11, top15 = 12.43.
OPT-1001 : End congestion update;  0.136577s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (148.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2053 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057166s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196867s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (134.9%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : End physical optimization;  0.662760s wall, 0.703125s user + 0.046875s system = 0.750000s CPU (113.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 377 LUT to BLE ...
SYN-4008 : Packed 377 LUT and 173 SEQ to BLE.
SYN-4003 : Packing 731 remaining SEQ's ...
SYN-4005 : Packed 126 SEQ with LUT/SLICE
SYN-4006 : 96 single LUT's are left
SYN-4006 : 605 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 982/1262 primitive instances ...
PHY-3001 : End packing;  0.045081s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (69.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 767 instances
RUN-1001 : 359 mslices, 359 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1890 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1371 nets have 2 pins
RUN-1001 : 403 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 765 instances, 718 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 51109.6, Over = 58.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6126, tnet num: 1888, tinst num: 765, tnode num: 8232, tedge num: 10753.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.279500s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.23271e-05
PHY-3002 : Step(128): len = 50516.7, overlap = 57.75
PHY-3002 : Step(129): len = 50329.7, overlap = 57.75
PHY-3002 : Step(130): len = 50180.4, overlap = 56.75
PHY-3002 : Step(131): len = 49832.3, overlap = 58.25
PHY-3002 : Step(132): len = 49635.3, overlap = 60
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.46542e-05
PHY-3002 : Step(133): len = 49841.5, overlap = 58.75
PHY-3002 : Step(134): len = 50381.6, overlap = 56.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.93084e-05
PHY-3002 : Step(135): len = 50962.6, overlap = 56.5
PHY-3002 : Step(136): len = 51875.6, overlap = 54
PHY-3002 : Step(137): len = 52749, overlap = 52.5
PHY-3002 : Step(138): len = 52878.1, overlap = 50.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.061220s wall, 0.031250s user + 0.093750s system = 0.125000s CPU (204.2%)

PHY-3001 : Trial Legalized: Len = 67487.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.045520s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000695064
PHY-3002 : Step(139): len = 63637.1, overlap = 9.25
PHY-3002 : Step(140): len = 61876.3, overlap = 13.5
PHY-3002 : Step(141): len = 59680.6, overlap = 14.75
PHY-3002 : Step(142): len = 58583.3, overlap = 16
PHY-3002 : Step(143): len = 58044.4, overlap = 17.5
PHY-3002 : Step(144): len = 57679.1, overlap = 18.75
PHY-3002 : Step(145): len = 57333.1, overlap = 19.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00131933
PHY-3002 : Step(146): len = 57720.7, overlap = 17.75
PHY-3002 : Step(147): len = 57980.9, overlap = 17.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00263865
PHY-3002 : Step(148): len = 58140, overlap = 17
PHY-3002 : Step(149): len = 58235.1, overlap = 17
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005157s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63027.2, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004854s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (321.9%)

PHY-3001 : 8 instances has been re-located, deltaX = 0, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 63079.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6126, tnet num: 1888, tinst num: 765, tnode num: 8232, tedge num: 10753.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 46/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69336, over cnt = 172(0%), over = 248, worst = 7
PHY-1002 : len = 70456, over cnt = 96(0%), over = 114, worst = 3
PHY-1002 : len = 71712, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 71792, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.143022s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (131.1%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.98, top10 = 18.03, top15 = 14.07.
PHY-1001 : End incremental global routing;  0.191859s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (130.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054425s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.273504s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (120.0%)

OPT-1001 : Current memory(MB): used = 219, reserve = 182, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1687/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005105s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.98, top10 = 18.03, top15 = 14.07.
OPT-1001 : End congestion update;  0.048320s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048320s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 727 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 765 instances, 718 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63095.6, Over = 0
PHY-3001 : End spreading;  0.005013s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63095.6, Over = 0
PHY-3001 : End incremental legalization;  0.033159s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.142665s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (120.5%)

OPT-1001 : Current memory(MB): used = 224, reserve = 187, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047144s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1679/1890.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71824, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 71824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.017733s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (88.1%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.99, top10 = 18.04, top15 = 14.06.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046582s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.864980s wall, 0.890625s user + 0.046875s system = 0.937500s CPU (108.4%)

RUN-1003 : finish command "place" in  5.018464s wall, 7.250000s user + 2.453125s system = 9.703125s CPU (193.3%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 767 instances
RUN-1001 : 359 mslices, 359 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1890 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1371 nets have 2 pins
RUN-1001 : 403 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6126, tnet num: 1888, tinst num: 765, tnode num: 8232, tedge num: 10753.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 359 mslices, 359 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1888 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68944, over cnt = 160(0%), over = 241, worst = 7
PHY-1002 : len = 70040, over cnt = 87(0%), over = 105, worst = 3
PHY-1002 : len = 71360, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.139391s wall, 0.093750s user + 0.078125s system = 0.171875s CPU (123.3%)

PHY-1001 : Congestion index: top1 = 31.42, top5 = 22.95, top10 = 17.95, top15 = 14.00.
PHY-1001 : End global routing;  0.190701s wall, 0.156250s user + 0.078125s system = 0.234375s CPU (122.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 203, peak = 240.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 468, peak = 501.
PHY-1001 : End build detailed router design. 3.108951s wall, 3.078125s user + 0.031250s system = 3.109375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31304, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.291430s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 533.
PHY-1001 : End phase 1; 1.298457s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 28% nets.
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 45% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181696, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 501, peak = 535.
PHY-1001 : End initial routed; 1.303543s wall, 2.437500s user + 0.140625s system = 2.578125s CPU (197.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1678(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.331   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.465   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.345104s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 537, reserve = 503, peak = 537.
PHY-1001 : End phase 2; 1.648738s wall, 2.781250s user + 0.140625s system = 2.921875s CPU (177.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181696, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014067s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (111.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181600, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.021723s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (71.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181704, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.020734s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (150.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181688, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.017324s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (90.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1678(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.331   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.465   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.330557s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.167344s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (102.7%)

PHY-1001 : Current memory(MB): used = 551, reserve = 518, peak = 551.
PHY-1001 : End phase 3; 0.706009s wall, 0.687500s user + 0.015625s system = 0.703125s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 181688
PHY-1001 : Current memory(MB): used = 551, reserve = 518, peak = 551.
PHY-1001 : End export database. 0.009841s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (158.8%)

PHY-1001 : End detail routing;  6.949843s wall, 8.015625s user + 0.187500s system = 8.203125s CPU (118.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6126, tnet num: 1888, tinst num: 765, tnode num: 8232, tedge num: 10753.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.dia[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6172, tnet num: 1911, tinst num: 788, tnode num: 8278, tedge num: 10799.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.867601s wall, 2.796875s user + 0.156250s system = 2.953125s CPU (103.0%)

RUN-1003 : finish command "route" in  10.360581s wall, 11.296875s user + 0.437500s system = 11.734375s CPU (113.3%)

RUN-1004 : used memory is 528 MB, reserved memory is 495 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      821   out of  19600    4.19%
#reg                      959   out of  19600    4.89%
#le                      1426
  #lut only               467   out of   1426   32.75%
  #reg only               605   out of   1426   42.43%
  #lut&reg                354   out of   1426   24.82%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         418
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         99
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1426   |624     |197     |990     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1026   |316     |105     |810     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |17      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |474    |149     |44      |345     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |42      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |11      |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |16      |0       |16      |0       |0       |
|    integ                   |Integration                                      |141    |19      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |58     |24      |6       |57      |0       |1       |
|    rs422                   |Rs422Output                                      |310    |93      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |105    |98      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |31     |24      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |21     |21      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |53     |53      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1358  
    #2          2       267   
    #3          3       126   
    #4          4        10   
    #5        5-10       82   
    #6        11-50      26   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6172, tnet num: 1911, tinst num: 788, tnode num: 8278, tedge num: 10799.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1911 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 788
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1913, pip num: 14304
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1300 valid insts, and 37897 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.953902s wall, 16.734375s user + 0.093750s system = 16.828125s CPU (569.7%)

RUN-1004 : used memory is 549 MB, reserved memory is 514 MB, peak memory is 676 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_190907.log"
