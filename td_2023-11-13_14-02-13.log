============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Nov 13 14:02:13 2023

   Run on =     TLH-022
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  4.112430s wall, 4.078125s user + 0.218750s system = 4.296875s CPU (104.5%)

RUN-1004 : used memory is 527 MB, reserved memory is 574 MB, peak memory is 527 MB
GUI-5005 WARNING: Unknown file icon ...
GUI-1001 : User opens ChipWatcher ...
PRG-1000 : <!-- HMAC is: 38974c86c4e54ff81d6e409ece779be010005dbc91ef0060c69536d0c3c10326 -->
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 11 feed throughs used by 10 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.623621s wall, 1.609375s user + 0.046875s system = 1.656250s CPU (102.0%)

RUN-1004 : used memory is 563 MB, reserved memory is 612 MB, peak memory is 564 MB
RUN-1002 : start command "config_chipwatcher -sync sim/111.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 28 trigger nets, 28 data nets.
GUI-1001 : Import sim/111.cwc success!
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.611447s wall, 1.484375s user + 0.171875s system = 1.656250s CPU (102.8%)

RUN-1004 : used memory is 567 MB, reserved memory is 615 MB, peak memory is 568 MB
RUN-1002 : start command "config_chipwatcher -sync sim/111.cwc -dir "
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 28 trigger nets, 28 data nets.
GUI-1001 : Import sim/111.cwc success!
GUI-8501 ERROR: Get bit file Ucode failed.
GUI-8501 ERROR: Bit file code (0000000000000000) does not match with the ChipWatcher's (1011101111010000).
RUN-1002 : start command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.529233s wall, 0.078125s user + 0.046875s system = 0.125000s CPU (8.2%)

RUN-1004 : used memory is 573 MB, reserved memory is 620 MB, peak memory is 589 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit -mode jtag_burst -spd 7 -cable 0 -total_dev 1 -cur_dev 1" in  1.694367s wall, 0.156250s user + 0.062500s system = 0.218750s CPU (12.9%)

RUN-1004 : used memory is 573 MB, reserved memory is 620 MB, peak memory is 589 MB
GUI-1001 : Downloading succeeded!
KIT-1004 : ChipWatcher: write ctrl reg value: 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0020 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0020 successfully.
SYN-2541 : Attrs-to-init for 25 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_181
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_191
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_201
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_211
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_221
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_231
SYN-2541 : Reading sub[24] auto_chipwatcher_0_logicbram_syn_241
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000001100
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-1004 : ChipWatcher: write ctrl reg value: 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000001100
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-1004 : ChipWatcher: write ctrl reg value: 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0020 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0020 successfully.
SYN-2541 : Attrs-to-init for 25 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_181
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_191
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_201
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_211
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_221
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_231
SYN-2541 : Reading sub[24] auto_chipwatcher_0_logicbram_syn_241
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-8424 ERROR: ChipWatcher: read status reg failed.
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0020 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0020 successfully.
SYN-2541 : Attrs-to-init for 25 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_181
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_191
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_201
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_211
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_221
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_231
SYN-2541 : Reading sub[24] auto_chipwatcher_0_logicbram_syn_241
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0020 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0020 successfully.
SYN-2541 : Attrs-to-init for 25 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_181
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_191
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_201
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_211
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_221
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_231
SYN-2541 : Reading sub[24] auto_chipwatcher_0_logicbram_syn_241
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000100
KIT-1004 : ChipWatcher: the value of status register = 110000000000001000
RUN-1002 : start command "rdbk_bram -bram 0X0008 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0008 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0009 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0009 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X000F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X000F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0010 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0010 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0011 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0011 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0012 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0012 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0013 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0013 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0014 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0014 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0015 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0015 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0016 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0016 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0017 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0017 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0018 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0018 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0019 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0019 successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001A -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001A successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001B -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001B successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001C -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001C successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001D -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001D successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001E -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001E successfully.
RUN-1002 : start command "rdbk_bram -bram 0X001F -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X001F successfully.
RUN-1002 : start command "rdbk_bram -bram 0X0020 -cable 0 -total_dev 1 -cur_dev 1"
KIT-1001 : Read back data from BRAM 0X0020 successfully.
SYN-2541 : Attrs-to-init for 25 logic bram(s)
SYN-2541 : Reading sub[0] auto_chipwatcher_0_logicbram_syn_1
SYN-2541 : Reading sub[1] auto_chipwatcher_0_logicbram_syn_11
SYN-2541 : Reading sub[2] auto_chipwatcher_0_logicbram_syn_21
SYN-2541 : Reading sub[3] auto_chipwatcher_0_logicbram_syn_31
SYN-2541 : Reading sub[4] auto_chipwatcher_0_logicbram_syn_41
SYN-2541 : Reading sub[5] auto_chipwatcher_0_logicbram_syn_51
SYN-2541 : Reading sub[6] auto_chipwatcher_0_logicbram_syn_61
SYN-2541 : Reading sub[7] auto_chipwatcher_0_logicbram_syn_71
SYN-2541 : Reading sub[8] auto_chipwatcher_0_logicbram_syn_81
SYN-2541 : Reading sub[9] auto_chipwatcher_0_logicbram_syn_91
SYN-2541 : Reading sub[10] auto_chipwatcher_0_logicbram_syn_101
SYN-2541 : Reading sub[11] auto_chipwatcher_0_logicbram_syn_111
SYN-2541 : Reading sub[12] auto_chipwatcher_0_logicbram_syn_121
SYN-2541 : Reading sub[13] auto_chipwatcher_0_logicbram_syn_131
SYN-2541 : Reading sub[14] auto_chipwatcher_0_logicbram_syn_141
SYN-2541 : Reading sub[15] auto_chipwatcher_0_logicbram_syn_151
SYN-2541 : Reading sub[16] auto_chipwatcher_0_logicbram_syn_161
SYN-2541 : Reading sub[17] auto_chipwatcher_0_logicbram_syn_171
SYN-2541 : Reading sub[18] auto_chipwatcher_0_logicbram_syn_181
SYN-2541 : Reading sub[19] auto_chipwatcher_0_logicbram_syn_191
SYN-2541 : Reading sub[20] auto_chipwatcher_0_logicbram_syn_201
SYN-2541 : Reading sub[21] auto_chipwatcher_0_logicbram_syn_211
SYN-2541 : Reading sub[22] auto_chipwatcher_0_logicbram_syn_221
SYN-2541 : Reading sub[23] auto_chipwatcher_0_logicbram_syn_231
SYN-2541 : Reading sub[24] auto_chipwatcher_0_logicbram_syn_241
KIT-1004 : ChipWatcher: write ctrl reg value: 011011111111111100000000000001101111111111111100000000000000010111010111001000000000000000000000
GUI-1001 : User closes ChipWatcher ...
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.870384s wall, 1.375000s user + 0.437500s system = 1.812500s CPU (96.9%)

RUN-1004 : used memory is 514 MB, reserved memory is 617 MB, peak memory is 591 MB
