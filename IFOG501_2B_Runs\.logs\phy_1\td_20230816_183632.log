============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Aug 16 18:36:32 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1651 instances
RUN-0007 : 384 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2221 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1659 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1649 instances, 384 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7854, tnet num: 2219, tinst num: 1649, tnode num: 11092, tedge num: 13255.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.290926s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 542147
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1649.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 488874, overlap = 18
PHY-3002 : Step(2): len = 405679, overlap = 13.5
PHY-3002 : Step(3): len = 336943, overlap = 13.5
PHY-3002 : Step(4): len = 328015, overlap = 15.75
PHY-3002 : Step(5): len = 320835, overlap = 13.5
PHY-3002 : Step(6): len = 313560, overlap = 15.75
PHY-3002 : Step(7): len = 308276, overlap = 18
PHY-3002 : Step(8): len = 301284, overlap = 18
PHY-3002 : Step(9): len = 294684, overlap = 20.25
PHY-3002 : Step(10): len = 289370, overlap = 20.25
PHY-3002 : Step(11): len = 283038, overlap = 20.25
PHY-3002 : Step(12): len = 276337, overlap = 20.25
PHY-3002 : Step(13): len = 271490, overlap = 20.25
PHY-3002 : Step(14): len = 266071, overlap = 20.25
PHY-3002 : Step(15): len = 258637, overlap = 20.25
PHY-3002 : Step(16): len = 253847, overlap = 20.25
PHY-3002 : Step(17): len = 250242, overlap = 20.25
PHY-3002 : Step(18): len = 241881, overlap = 20.25
PHY-3002 : Step(19): len = 235677, overlap = 20.25
PHY-3002 : Step(20): len = 233004, overlap = 20.25
PHY-3002 : Step(21): len = 225413, overlap = 20.25
PHY-3002 : Step(22): len = 210912, overlap = 20.25
PHY-3002 : Step(23): len = 207820, overlap = 20.25
PHY-3002 : Step(24): len = 203642, overlap = 20.25
PHY-3002 : Step(25): len = 153650, overlap = 20.25
PHY-3002 : Step(26): len = 150624, overlap = 20.5
PHY-3002 : Step(27): len = 148791, overlap = 20.25
PHY-3002 : Step(28): len = 141704, overlap = 20.25
PHY-3002 : Step(29): len = 138259, overlap = 20.25
PHY-3002 : Step(30): len = 135566, overlap = 20.25
PHY-3002 : Step(31): len = 133475, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000150056
PHY-3002 : Step(32): len = 134485, overlap = 11.25
PHY-3002 : Step(33): len = 133483, overlap = 13.5
PHY-3002 : Step(34): len = 132126, overlap = 13.5
PHY-3002 : Step(35): len = 130491, overlap = 15.75
PHY-3002 : Step(36): len = 129372, overlap = 4.5
PHY-3002 : Step(37): len = 125978, overlap = 6.75
PHY-3002 : Step(38): len = 124465, overlap = 6.75
PHY-3002 : Step(39): len = 116323, overlap = 9
PHY-3002 : Step(40): len = 115618, overlap = 6.75
PHY-3002 : Step(41): len = 114197, overlap = 6.75
PHY-3002 : Step(42): len = 113032, overlap = 4.5
PHY-3002 : Step(43): len = 108795, overlap = 9
PHY-3002 : Step(44): len = 105642, overlap = 9
PHY-3002 : Step(45): len = 103636, overlap = 4.5
PHY-3002 : Step(46): len = 103608, overlap = 4.5
PHY-3002 : Step(47): len = 102023, overlap = 6.75
PHY-3002 : Step(48): len = 99841.1, overlap = 6.75
PHY-3002 : Step(49): len = 97360.1, overlap = 4.5
PHY-3002 : Step(50): len = 96212.1, overlap = 6.75
PHY-3002 : Step(51): len = 94145.1, overlap = 6.75
PHY-3002 : Step(52): len = 92901.8, overlap = 4.5
PHY-3002 : Step(53): len = 91175, overlap = 6.75
PHY-3002 : Step(54): len = 87165.7, overlap = 4.5
PHY-3002 : Step(55): len = 85752, overlap = 4.5
PHY-3002 : Step(56): len = 85383.9, overlap = 4.5
PHY-3002 : Step(57): len = 84681.2, overlap = 4.5
PHY-3002 : Step(58): len = 83989.7, overlap = 4.5
PHY-3002 : Step(59): len = 82734.4, overlap = 6.75
PHY-3002 : Step(60): len = 81958.1, overlap = 6.75
PHY-3002 : Step(61): len = 80711.6, overlap = 4.5
PHY-3002 : Step(62): len = 78889.9, overlap = 6.75
PHY-3002 : Step(63): len = 76048.7, overlap = 2.25
PHY-3002 : Step(64): len = 75321.2, overlap = 4.5
PHY-3002 : Step(65): len = 73784.8, overlap = 9
PHY-3002 : Step(66): len = 72468.1, overlap = 6.75
PHY-3002 : Step(67): len = 72036, overlap = 4.5
PHY-3002 : Step(68): len = 71604.8, overlap = 4.5
PHY-3002 : Step(69): len = 71006.8, overlap = 6.75
PHY-3002 : Step(70): len = 70541.7, overlap = 9
PHY-3002 : Step(71): len = 69940.6, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000300112
PHY-3002 : Step(72): len = 70102.7, overlap = 4.5
PHY-3002 : Step(73): len = 70238.8, overlap = 4.5
PHY-3002 : Step(74): len = 70103.2, overlap = 4.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000600223
PHY-3002 : Step(75): len = 70089.1, overlap = 4.5
PHY-3002 : Step(76): len = 70080.3, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009005s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.101169s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(77): len = 72705.6, overlap = 6.84375
PHY-3002 : Step(78): len = 71253.5, overlap = 7.4375
PHY-3002 : Step(79): len = 69843.4, overlap = 6.5
PHY-3002 : Step(80): len = 67582, overlap = 6.1875
PHY-3002 : Step(81): len = 66102.4, overlap = 2.125
PHY-3002 : Step(82): len = 64772.8, overlap = 1.875
PHY-3002 : Step(83): len = 62907.3, overlap = 2.4375
PHY-3002 : Step(84): len = 61788.1, overlap = 2.6875
PHY-3002 : Step(85): len = 60549.7, overlap = 3.25
PHY-3002 : Step(86): len = 58981.2, overlap = 4.3125
PHY-3002 : Step(87): len = 57255.9, overlap = 5.5
PHY-3002 : Step(88): len = 56382.2, overlap = 5.625
PHY-3002 : Step(89): len = 55708.6, overlap = 5.375
PHY-3002 : Step(90): len = 54952.7, overlap = 5.25
PHY-3002 : Step(91): len = 54060.2, overlap = 5.75
PHY-3002 : Step(92): len = 53446, overlap = 5.3125
PHY-3002 : Step(93): len = 52859.2, overlap = 4.5
PHY-3002 : Step(94): len = 52268.6, overlap = 4.1875
PHY-3002 : Step(95): len = 51645.9, overlap = 6.1875
PHY-3002 : Step(96): len = 51004.6, overlap = 5.4375
PHY-3002 : Step(97): len = 50500.3, overlap = 5.3125
PHY-3002 : Step(98): len = 49324.8, overlap = 5.75
PHY-3002 : Step(99): len = 48775.2, overlap = 6.3125
PHY-3002 : Step(100): len = 48154, overlap = 7.5
PHY-3002 : Step(101): len = 48105.8, overlap = 7.375
PHY-3002 : Step(102): len = 47816, overlap = 5.8125
PHY-3002 : Step(103): len = 47541.6, overlap = 5.875
PHY-3002 : Step(104): len = 47328, overlap = 6
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000344922
PHY-3002 : Step(105): len = 47190.2, overlap = 8.75
PHY-3002 : Step(106): len = 47201.3, overlap = 8.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000689843
PHY-3002 : Step(107): len = 47270.6, overlap = 12.0312
PHY-3002 : Step(108): len = 47270.6, overlap = 12.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069775s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.12976e-05
PHY-3002 : Step(109): len = 48011.1, overlap = 67.3438
PHY-3002 : Step(110): len = 49105.5, overlap = 59.9375
PHY-3002 : Step(111): len = 48854.5, overlap = 62.8438
PHY-3002 : Step(112): len = 48486.3, overlap = 58.7188
PHY-3002 : Step(113): len = 48415, overlap = 57.5312
PHY-3002 : Step(114): len = 48941.7, overlap = 57.0312
PHY-3002 : Step(115): len = 49189.4, overlap = 56.1875
PHY-3002 : Step(116): len = 48966.9, overlap = 50.7188
PHY-3002 : Step(117): len = 49092.8, overlap = 48.3438
PHY-3002 : Step(118): len = 49028.6, overlap = 48.5
PHY-3002 : Step(119): len = 49072.6, overlap = 48.7188
PHY-3002 : Step(120): len = 48841.2, overlap = 48.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000142595
PHY-3002 : Step(121): len = 48881, overlap = 48.2188
PHY-3002 : Step(122): len = 48956.3, overlap = 47.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000259973
PHY-3002 : Step(123): len = 49831.2, overlap = 46.2812
PHY-3002 : Step(124): len = 50315.9, overlap = 46.3438
PHY-3002 : Step(125): len = 51006.7, overlap = 44.7812
PHY-3002 : Step(126): len = 50804.1, overlap = 44.0312
PHY-3002 : Step(127): len = 50585.3, overlap = 42.2812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7854, tnet num: 2219, tinst num: 1649, tnode num: 11092, tedge num: 13255.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 96.12 peak overflow 2.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2221.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54840, over cnt = 258(0%), over = 1168, worst = 17
PHY-1001 : End global iterations;  0.077448s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (100.9%)

PHY-1001 : Congestion index: top1 = 45.50, top5 = 25.70, top10 = 16.31, top15 = 11.56.
PHY-1001 : End incremental global routing;  0.130620s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (95.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069049s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.230930s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (94.7%)

OPT-1001 : Current memory(MB): used = 215, reserve = 179, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1688/2221.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54840, over cnt = 258(0%), over = 1168, worst = 17
PHY-1002 : len = 63376, over cnt = 179(0%), over = 384, worst = 13
PHY-1002 : len = 65752, over cnt = 102(0%), over = 142, worst = 7
PHY-1002 : len = 67120, over cnt = 47(0%), over = 53, worst = 2
PHY-1002 : len = 68256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096179s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (113.7%)

PHY-1001 : Congestion index: top1 = 38.51, top5 = 26.44, top10 = 18.52, top15 = 13.61.
OPT-1001 : End congestion update;  0.138600s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (112.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058140s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.199366s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (109.7%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.713297s wall, 0.734375s user + 0.062500s system = 0.796875s CPU (111.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 384 LUT to BLE ...
SYN-4008 : Packed 384 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 80 SEQ with LUT/SLICE
SYN-4006 : 135 single LUT's are left
SYN-4006 : 723 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1107/1440 primitive instances ...
PHY-3001 : End packing;  0.053441s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 860 instances
RUN-1001 : 405 mslices, 406 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2046 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1486 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 858 instances, 811 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50802.8, Over = 67
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6626, tnet num: 2044, tinst num: 858, tnode num: 8976, tedge num: 11628.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.314829s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.04073e-05
PHY-3002 : Step(128): len = 50420.7, overlap = 68.25
PHY-3002 : Step(129): len = 50000.4, overlap = 69.75
PHY-3002 : Step(130): len = 50072.7, overlap = 74.25
PHY-3002 : Step(131): len = 49901, overlap = 75
PHY-3002 : Step(132): len = 49584.1, overlap = 76
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.08146e-05
PHY-3002 : Step(133): len = 49764, overlap = 74.5
PHY-3002 : Step(134): len = 50896.4, overlap = 70.5
PHY-3002 : Step(135): len = 51739.6, overlap = 69.25
PHY-3002 : Step(136): len = 51745.7, overlap = 69
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.16292e-05
PHY-3002 : Step(137): len = 52056.7, overlap = 69.75
PHY-3002 : Step(138): len = 53174.7, overlap = 66
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.077396s wall, 0.062500s user + 0.078125s system = 0.140625s CPU (181.7%)

PHY-3001 : Trial Legalized: Len = 68146.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051678s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000475701
PHY-3002 : Step(139): len = 64662.2, overlap = 10
PHY-3002 : Step(140): len = 62282.1, overlap = 18
PHY-3002 : Step(141): len = 60260.8, overlap = 21.75
PHY-3002 : Step(142): len = 59447.1, overlap = 24
PHY-3002 : Step(143): len = 59084.7, overlap = 24.25
PHY-3002 : Step(144): len = 58885, overlap = 25.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000951403
PHY-3002 : Step(145): len = 59421.5, overlap = 24.5
PHY-3002 : Step(146): len = 59654.3, overlap = 25.5
PHY-3002 : Step(147): len = 59710, overlap = 25.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00190281
PHY-3002 : Step(148): len = 59897.6, overlap = 25
PHY-3002 : Step(149): len = 60033.4, overlap = 25.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004815s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64614.8, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005560s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 0, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 64678.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6626, tnet num: 2044, tinst num: 858, tnode num: 8976, tedge num: 11628.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 37/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70832, over cnt = 122(0%), over = 196, worst = 7
PHY-1002 : len = 71960, over cnt = 44(0%), over = 51, worst = 2
PHY-1002 : len = 72568, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 72680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.137946s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (101.9%)

PHY-1001 : Congestion index: top1 = 31.90, top5 = 22.76, top10 = 17.83, top15 = 14.13.
PHY-1001 : End incremental global routing;  0.189235s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (115.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059824s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.280301s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (105.9%)

OPT-1001 : Current memory(MB): used = 221, reserve = 186, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1800/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005480s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (285.1%)

PHY-1001 : Congestion index: top1 = 31.90, top5 = 22.76, top10 = 17.83, top15 = 14.13.
OPT-1001 : End congestion update;  0.051269s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050034s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 820 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 858 instances, 811 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64743.8, Over = 0
PHY-3001 : End spreading;  0.006141s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64743.8, Over = 0
PHY-3001 : End incremental legalization;  0.035073s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.1%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.148989s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (115.4%)

OPT-1001 : Current memory(MB): used = 226, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049815s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1796/2046.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007301s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (214.0%)

PHY-1001 : Congestion index: top1 = 31.83, top5 = 22.75, top10 = 17.83, top15 = 14.14.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050852s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.881187s wall, 0.875000s user + 0.031250s system = 0.906250s CPU (102.8%)

RUN-1003 : finish command "place" in  5.306560s wall, 8.375000s user + 2.578125s system = 10.953125s CPU (206.4%)

RUN-1004 : used memory is 205 MB, reserved memory is 169 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 860 instances
RUN-1001 : 405 mslices, 406 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2046 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1486 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6626, tnet num: 2044, tinst num: 858, tnode num: 8976, tedge num: 11628.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 405 mslices, 406 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70480, over cnt = 121(0%), over = 193, worst = 7
PHY-1002 : len = 71640, over cnt = 45(0%), over = 52, worst = 2
PHY-1002 : len = 72264, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 72376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.139187s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (112.3%)

PHY-1001 : Congestion index: top1 = 31.98, top5 = 22.72, top10 = 17.78, top15 = 14.07.
PHY-1001 : End global routing;  0.188200s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (107.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 203, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 470, peak = 501.
PHY-1001 : End build detailed router design. 3.195045s wall, 3.140625s user + 0.046875s system = 3.187500s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34368, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.312341s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End phase 1; 1.318197s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 187864, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End initial routed; 1.055197s wall, 1.953125s user + 0.140625s system = 2.093750s CPU (198.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1809(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.254   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.364944s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.5%)

PHY-1001 : Current memory(MB): used = 538, reserve = 506, peak = 538.
PHY-1001 : End phase 2; 1.420229s wall, 2.312500s user + 0.140625s system = 2.453125s CPU (172.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 187864, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015539s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (100.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 187904, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024027s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (130.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 187920, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020370s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (76.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1809(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.254   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.376975s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.179052s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.7%)

PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End phase 3; 0.741746s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (101.1%)

PHY-1003 : Routed, final wirelength = 187920
PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End export database. 0.009583s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (163.0%)

PHY-1001 : End detail routing;  6.869447s wall, 7.718750s user + 0.187500s system = 7.906250s CPU (115.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6626, tnet num: 2044, tinst num: 858, tnode num: 8976, tedge num: 11628.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.793477s wall, 8.625000s user + 0.218750s system = 8.843750s CPU (113.5%)

RUN-1004 : used memory is 526 MB, reserved memory is 495 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      842   out of  19600    4.30%
#reg                     1074   out of  19600    5.48%
#le                      1565
  #lut only               491   out of   1565   31.37%
  #reg only               723   out of   1565   46.20%
  #lut&reg                351   out of   1565   22.43%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1565   |616     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1144   |297     |133     |925     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |20      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |540    |127     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |160    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |13      |0       |25      |0       |0       |
|    integ                   |Integration                                      |139    |16      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |93     |26      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |83      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |118    |102     |7       |50      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |23     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |60     |55      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |220    |175     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1450  
    #2          2       319   
    #3          3       108   
    #4          4        12   
    #5        5-10       83   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6626, tnet num: 2044, tinst num: 858, tnode num: 8976, tedge num: 11628.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2044 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 858
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2046, pip num: 14924
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1339 valid insts, and 39534 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.164959s wall, 18.515625s user + 0.062500s system = 18.578125s CPU (587.0%)

RUN-1004 : used memory is 545 MB, reserved memory is 514 MB, peak memory is 674 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230816_183632.log"
