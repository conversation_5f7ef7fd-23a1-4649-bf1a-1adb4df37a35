============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 09:51:47 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1627 instances
RUN-0007 : 380 luts, 979 seqs, 144 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2183 nets
RUN-1001 : 1649 nets have 2 pins
RUN-1001 : 419 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1625 instances, 380 luts, 979 seqs, 219 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7714, tnet num: 2181, tinst num: 1625, tnode num: 10899, tedge num: 12990.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.288600s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (102.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 542217
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1625.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 448064, overlap = 18
PHY-3002 : Step(2): len = 345656, overlap = 15.75
PHY-3002 : Step(3): len = 325632, overlap = 15.75
PHY-3002 : Step(4): len = 313857, overlap = 20.25
PHY-3002 : Step(5): len = 307411, overlap = 20.25
PHY-3002 : Step(6): len = 303193, overlap = 20.25
PHY-3002 : Step(7): len = 296495, overlap = 20.25
PHY-3002 : Step(8): len = 290864, overlap = 20.25
PHY-3002 : Step(9): len = 283374, overlap = 15.75
PHY-3002 : Step(10): len = 279503, overlap = 15.75
PHY-3002 : Step(11): len = 273250, overlap = 15.75
PHY-3002 : Step(12): len = 268571, overlap = 15.75
PHY-3002 : Step(13): len = 261517, overlap = 15.75
PHY-3002 : Step(14): len = 257666, overlap = 18
PHY-3002 : Step(15): len = 251527, overlap = 18
PHY-3002 : Step(16): len = 245920, overlap = 18
PHY-3002 : Step(17): len = 240515, overlap = 18
PHY-3002 : Step(18): len = 237522, overlap = 18
PHY-3002 : Step(19): len = 226397, overlap = 15.75
PHY-3002 : Step(20): len = 221376, overlap = 18
PHY-3002 : Step(21): len = 218276, overlap = 18
PHY-3002 : Step(22): len = 210283, overlap = 18
PHY-3002 : Step(23): len = 198634, overlap = 15.75
PHY-3002 : Step(24): len = 196510, overlap = 18
PHY-3002 : Step(25): len = 190426, overlap = 15.75
PHY-3002 : Step(26): len = 138556, overlap = 15.75
PHY-3002 : Step(27): len = 134933, overlap = 15.75
PHY-3002 : Step(28): len = 133400, overlap = 15.75
PHY-3002 : Step(29): len = 127195, overlap = 15.75
PHY-3002 : Step(30): len = 123519, overlap = 18
PHY-3002 : Step(31): len = 119751, overlap = 15.75
PHY-3002 : Step(32): len = 117217, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000134548
PHY-3002 : Step(33): len = 117500, overlap = 6.75
PHY-3002 : Step(34): len = 116896, overlap = 6.75
PHY-3002 : Step(35): len = 115601, overlap = 6.75
PHY-3002 : Step(36): len = 114944, overlap = 6.75
PHY-3002 : Step(37): len = 111317, overlap = 11.25
PHY-3002 : Step(38): len = 109158, overlap = 6.75
PHY-3002 : Step(39): len = 108646, overlap = 4.5
PHY-3002 : Step(40): len = 106885, overlap = 4.5
PHY-3002 : Step(41): len = 105001, overlap = 13.5
PHY-3002 : Step(42): len = 101630, overlap = 6.75
PHY-3002 : Step(43): len = 99799.9, overlap = 6.75
PHY-3002 : Step(44): len = 98343.7, overlap = 9
PHY-3002 : Step(45): len = 96335.6, overlap = 13.5
PHY-3002 : Step(46): len = 93713.3, overlap = 11.25
PHY-3002 : Step(47): len = 93360.2, overlap = 9
PHY-3002 : Step(48): len = 91356.8, overlap = 11.25
PHY-3002 : Step(49): len = 89139.1, overlap = 9
PHY-3002 : Step(50): len = 87549.6, overlap = 11.25
PHY-3002 : Step(51): len = 86912.5, overlap = 9
PHY-3002 : Step(52): len = 83882.8, overlap = 9
PHY-3002 : Step(53): len = 80711.3, overlap = 11.25
PHY-3002 : Step(54): len = 78148.6, overlap = 11.25
PHY-3002 : Step(55): len = 77995.4, overlap = 9.0625
PHY-3002 : Step(56): len = 77047.3, overlap = 9.125
PHY-3002 : Step(57): len = 76109.1, overlap = 11.3125
PHY-3002 : Step(58): len = 75398.4, overlap = 9.125
PHY-3002 : Step(59): len = 74936.2, overlap = 11.5625
PHY-3002 : Step(60): len = 74091.6, overlap = 14.25
PHY-3002 : Step(61): len = 73386.3, overlap = 9.625
PHY-3002 : Step(62): len = 70953.4, overlap = 9.625
PHY-3002 : Step(63): len = 68704.1, overlap = 9.5625
PHY-3002 : Step(64): len = 67606.3, overlap = 9.5625
PHY-3002 : Step(65): len = 66805, overlap = 14.1875
PHY-3002 : Step(66): len = 66254.4, overlap = 14
PHY-3002 : Step(67): len = 65986.9, overlap = 9.4375
PHY-3002 : Step(68): len = 65867.8, overlap = 2.625
PHY-3002 : Step(69): len = 65637, overlap = 7.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000269095
PHY-3002 : Step(70): len = 65149, overlap = 9.3125
PHY-3002 : Step(71): len = 64969.9, overlap = 9.25
PHY-3002 : Step(72): len = 64756.2, overlap = 13.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00053819
PHY-3002 : Step(73): len = 64956.8, overlap = 9.25
PHY-3002 : Step(74): len = 64917.9, overlap = 9.1875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006454s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.078648s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(75): len = 68138.3, overlap = 6.3125
PHY-3002 : Step(76): len = 66625.6, overlap = 5.8125
PHY-3002 : Step(77): len = 64756.9, overlap = 5.625
PHY-3002 : Step(78): len = 63028.8, overlap = 5.125
PHY-3002 : Step(79): len = 61117.3, overlap = 3.75
PHY-3002 : Step(80): len = 59869.1, overlap = 3.75
PHY-3002 : Step(81): len = 58854.4, overlap = 3.625
PHY-3002 : Step(82): len = 57476.2, overlap = 3.9375
PHY-3002 : Step(83): len = 54632.3, overlap = 7
PHY-3002 : Step(84): len = 53463.9, overlap = 7.5625
PHY-3002 : Step(85): len = 52431.2, overlap = 10.5625
PHY-3002 : Step(86): len = 51405.1, overlap = 11
PHY-3002 : Step(87): len = 50878, overlap = 11
PHY-3002 : Step(88): len = 50061.1, overlap = 8.8125
PHY-3002 : Step(89): len = 49494.5, overlap = 9.9375
PHY-3002 : Step(90): len = 48971.4, overlap = 10.1875
PHY-3002 : Step(91): len = 48628.5, overlap = 11
PHY-3002 : Step(92): len = 48065.9, overlap = 11.9375
PHY-3002 : Step(93): len = 47901.5, overlap = 12.0938
PHY-3002 : Step(94): len = 47558.8, overlap = 12.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000836872
PHY-3002 : Step(95): len = 47471.9, overlap = 11.7812
PHY-3002 : Step(96): len = 47265.6, overlap = 12.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00167374
PHY-3002 : Step(97): len = 47267.9, overlap = 12.2188
PHY-3002 : Step(98): len = 47374.9, overlap = 12.5312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065833s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (118.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000140198
PHY-3002 : Step(99): len = 47594.9, overlap = 61.3438
PHY-3002 : Step(100): len = 48511.4, overlap = 44.7812
PHY-3002 : Step(101): len = 49570.6, overlap = 46.1562
PHY-3002 : Step(102): len = 50210.6, overlap = 33.4688
PHY-3002 : Step(103): len = 50196.7, overlap = 33.0625
PHY-3002 : Step(104): len = 49877.8, overlap = 34.7188
PHY-3002 : Step(105): len = 49159.2, overlap = 34.9062
PHY-3002 : Step(106): len = 49011.2, overlap = 34.1875
PHY-3002 : Step(107): len = 48923.8, overlap = 34.9062
PHY-3002 : Step(108): len = 48745.2, overlap = 35.9062
PHY-3002 : Step(109): len = 48833.8, overlap = 36.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000280396
PHY-3002 : Step(110): len = 48704.9, overlap = 35.4062
PHY-3002 : Step(111): len = 48701.6, overlap = 34.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000560792
PHY-3002 : Step(112): len = 49271.1, overlap = 34.1562
PHY-3002 : Step(113): len = 49524, overlap = 32.3125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7714, tnet num: 2181, tinst num: 1625, tnode num: 10899, tedge num: 12990.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.94 peak overflow 3.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52792, over cnt = 242(0%), over = 1006, worst = 20
PHY-1001 : End global iterations;  0.062880s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (149.1%)

PHY-1001 : Congestion index: top1 = 42.24, top5 = 24.69, top10 = 15.40, top15 = 10.94.
PHY-1001 : End incremental global routing;  0.117521s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (133.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068726s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (90.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.218004s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (114.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 179, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1613/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52792, over cnt = 242(0%), over = 1006, worst = 20
PHY-1002 : len = 57272, over cnt = 184(0%), over = 517, worst = 16
PHY-1002 : len = 62712, over cnt = 30(0%), over = 31, worst = 2
PHY-1002 : len = 63448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.090974s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (120.2%)

PHY-1001 : Congestion index: top1 = 35.39, top5 = 24.49, top10 = 17.22, top15 = 12.70.
OPT-1001 : End congestion update;  0.140069s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (111.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062145s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.206631s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (105.9%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.710008s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (107.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 95 SEQ with LUT/SLICE
SYN-4006 : 113 single LUT's are left
SYN-4006 : 695 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1075/1401 primitive instances ...
PHY-3001 : End packing;  0.052942s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 838 instances
RUN-1001 : 394 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 836 instances, 789 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49652.6, Over = 59.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2006, tinst num: 836, tnode num: 8785, tedge num: 11362.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.317820s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.54061e-05
PHY-3002 : Step(114): len = 48867.8, overlap = 61.75
PHY-3002 : Step(115): len = 48760.9, overlap = 62.75
PHY-3002 : Step(116): len = 48517.9, overlap = 64
PHY-3002 : Step(117): len = 48681, overlap = 66.25
PHY-3002 : Step(118): len = 48642.7, overlap = 64.5
PHY-3002 : Step(119): len = 48630.7, overlap = 64.25
PHY-3002 : Step(120): len = 48296, overlap = 63.75
PHY-3002 : Step(121): len = 48029.3, overlap = 62.5
PHY-3002 : Step(122): len = 47912.3, overlap = 61.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.08122e-05
PHY-3002 : Step(123): len = 48233.5, overlap = 61.25
PHY-3002 : Step(124): len = 48681.5, overlap = 61.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.9222e-05
PHY-3002 : Step(125): len = 49533, overlap = 60.25
PHY-3002 : Step(126): len = 50491.2, overlap = 58
PHY-3002 : Step(127): len = 50955.2, overlap = 56
PHY-3002 : Step(128): len = 51077.9, overlap = 54.25
PHY-3002 : Step(129): len = 51077.9, overlap = 54.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.105077s wall, 0.125000s user + 0.125000s system = 0.250000s CPU (237.9%)

PHY-3001 : Trial Legalized: Len = 65237.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061644s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000765762
PHY-3002 : Step(130): len = 62627.8, overlap = 9
PHY-3002 : Step(131): len = 60738.8, overlap = 14.5
PHY-3002 : Step(132): len = 59271.3, overlap = 17.75
PHY-3002 : Step(133): len = 58282.5, overlap = 18.25
PHY-3002 : Step(134): len = 57746.9, overlap = 19.75
PHY-3002 : Step(135): len = 57384.4, overlap = 20.25
PHY-3002 : Step(136): len = 57042.6, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00153152
PHY-3002 : Step(137): len = 57443.7, overlap = 20
PHY-3002 : Step(138): len = 57550.3, overlap = 20.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00306305
PHY-3002 : Step(139): len = 57705.9, overlap = 21.5
PHY-3002 : Step(140): len = 57765.3, overlap = 21.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005963s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (262.0%)

PHY-3001 : Legalized: Len = 62502.3, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006151s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 14 instances has been re-located, deltaX = 0, deltaY = 14, maxDist = 1.
PHY-3001 : Final: Len = 62780.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2006, tinst num: 836, tnode num: 8785, tedge num: 11362.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 128/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69896, over cnt = 134(0%), over = 209, worst = 6
PHY-1002 : len = 70792, over cnt = 50(0%), over = 59, worst = 3
PHY-1002 : len = 71376, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 71488, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132984s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (141.0%)

PHY-1001 : Congestion index: top1 = 33.17, top5 = 22.68, top10 = 17.41, top15 = 13.81.
PHY-1001 : End incremental global routing;  0.188514s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (124.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065135s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.285556s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (114.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1775/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006502s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (240.3%)

PHY-1001 : Congestion index: top1 = 33.17, top5 = 22.68, top10 = 17.41, top15 = 13.81.
OPT-1001 : End congestion update;  0.057635s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060566s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 798 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 836 instances, 789 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62805.6, Over = 0
PHY-3001 : End spreading;  0.006295s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62805.6, Over = 0
PHY-3001 : End incremental legalization;  0.040070s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (156.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.174060s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.7%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052999s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1771/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008856s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.12, top5 = 22.67, top10 = 17.43, top15 = 13.82.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054157s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.933143s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (105.5%)

RUN-1003 : finish command "place" in  5.610590s wall, 8.328125s user + 2.656250s system = 10.984375s CPU (195.8%)

RUN-1004 : used memory is 203 MB, reserved memory is 168 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 838 instances
RUN-1001 : 394 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2006, tinst num: 836, tnode num: 8785, tedge num: 11362.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68616, over cnt = 139(0%), over = 237, worst = 6
PHY-1002 : len = 69952, over cnt = 49(0%), over = 58, worst = 3
PHY-1002 : len = 70616, over cnt = 7(0%), over = 9, worst = 2
PHY-1002 : len = 70752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130269s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (143.9%)

PHY-1001 : Congestion index: top1 = 33.12, top5 = 22.50, top10 = 17.20, top15 = 13.66.
PHY-1001 : End global routing;  0.184689s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (126.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 206, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 500, reserve = 470, peak = 500.
PHY-1001 : End build detailed router design. 3.274064s wall, 3.250000s user + 0.015625s system = 3.265625s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33968, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.462799s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 533, reserve = 504, peak = 534.
PHY-1001 : End phase 1; 1.469364s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (98.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179648, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 505, peak = 535.
PHY-1001 : End initial routed; 1.110923s wall, 2.078125s user + 0.171875s system = 2.250000s CPU (202.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1779(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.275   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.381367s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.3%)

PHY-1001 : Current memory(MB): used = 536, reserve = 507, peak = 536.
PHY-1001 : End phase 2; 1.492376s wall, 2.453125s user + 0.171875s system = 2.625000s CPU (175.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179648, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016248s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (96.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179712, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026994s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (57.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179744, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023595s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (66.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1779(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.275   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.384522s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.179382s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.5%)

PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End phase 3; 0.759647s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.8%)

PHY-1003 : Routed, final wirelength = 179744
PHY-1001 : Current memory(MB): used = 552, reserve = 521, peak = 552.
PHY-1001 : End export database. 0.009832s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.184089s wall, 8.109375s user + 0.187500s system = 8.296875s CPU (115.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2006, tinst num: 836, tnode num: 8785, tedge num: 11362.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.125642s wall, 9.078125s user + 0.218750s system = 9.296875s CPU (114.4%)

RUN-1004 : used memory is 528 MB, reserved memory is 499 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      824   out of  19600    4.20%
#reg                     1047   out of  19600    5.34%
#le                      1519
  #lut only               472   out of   1519   31.07%
  #reg only               695   out of   1519   45.75%
  #lut&reg                352   out of   1519   23.17%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         461
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1519   |605     |219     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1108   |292     |126     |896     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |22     |16      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |544    |132     |58      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |14      |0       |27      |0       |0       |
|    integ                   |Integration                                      |139    |16      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |66     |27      |14      |61      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |85      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |21     |16      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |117    |105     |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |59     |59      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1445  
    #2          2       290   
    #3          3       107   
    #4          4        13   
    #5        5-10       81   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2006, tinst num: 836, tnode num: 8785, tedge num: 11362.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 836
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2008, pip num: 14607
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1340 valid insts, and 38763 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.060900s wall, 18.281250s user + 0.031250s system = 18.312500s CPU (598.3%)

RUN-1004 : used memory is 552 MB, reserved memory is 523 MB, peak memory is 673 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_095147.log"
