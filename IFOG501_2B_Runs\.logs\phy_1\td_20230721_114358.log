============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 11:43:58 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1625 instances
RUN-0007 : 366 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2195 nets
RUN-1001 : 1638 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1623 instances, 366 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7784, tnet num: 2193, tinst num: 1623, tnode num: 11024, tedge num: 13166.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.282520s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 578282
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1623.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 551780, overlap = 20.25
PHY-3002 : Step(2): len = 441991, overlap = 13.5
PHY-3002 : Step(3): len = 366031, overlap = 15.75
PHY-3002 : Step(4): len = 347460, overlap = 15.75
PHY-3002 : Step(5): len = 339038, overlap = 15.75
PHY-3002 : Step(6): len = 327302, overlap = 20.25
PHY-3002 : Step(7): len = 320209, overlap = 20.25
PHY-3002 : Step(8): len = 313853, overlap = 20.25
PHY-3002 : Step(9): len = 305782, overlap = 20.25
PHY-3002 : Step(10): len = 298884, overlap = 20.25
PHY-3002 : Step(11): len = 293499, overlap = 20.25
PHY-3002 : Step(12): len = 286532, overlap = 20.25
PHY-3002 : Step(13): len = 279729, overlap = 20.25
PHY-3002 : Step(14): len = 275085, overlap = 20.25
PHY-3002 : Step(15): len = 268879, overlap = 20.25
PHY-3002 : Step(16): len = 262348, overlap = 20.25
PHY-3002 : Step(17): len = 257662, overlap = 20.25
PHY-3002 : Step(18): len = 252132, overlap = 20.25
PHY-3002 : Step(19): len = 245438, overlap = 20.25
PHY-3002 : Step(20): len = 240854, overlap = 20.25
PHY-3002 : Step(21): len = 235532, overlap = 20.25
PHY-3002 : Step(22): len = 229990, overlap = 20.25
PHY-3002 : Step(23): len = 225113, overlap = 20.25
PHY-3002 : Step(24): len = 220711, overlap = 20.25
PHY-3002 : Step(25): len = 214516, overlap = 20.25
PHY-3002 : Step(26): len = 209828, overlap = 20.25
PHY-3002 : Step(27): len = 206836, overlap = 20.25
PHY-3002 : Step(28): len = 198509, overlap = 20.25
PHY-3002 : Step(29): len = 190427, overlap = 20.25
PHY-3002 : Step(30): len = 187723, overlap = 20.25
PHY-3002 : Step(31): len = 183246, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000147091
PHY-3002 : Step(32): len = 184246, overlap = 13.5
PHY-3002 : Step(33): len = 181428, overlap = 6.75
PHY-3002 : Step(34): len = 178717, overlap = 13.5
PHY-3002 : Step(35): len = 172689, overlap = 9
PHY-3002 : Step(36): len = 163625, overlap = 6.75
PHY-3002 : Step(37): len = 161996, overlap = 9
PHY-3002 : Step(38): len = 158439, overlap = 4.5
PHY-3002 : Step(39): len = 155650, overlap = 4.5
PHY-3002 : Step(40): len = 150625, overlap = 9
PHY-3002 : Step(41): len = 148984, overlap = 4.5
PHY-3002 : Step(42): len = 146042, overlap = 11.25
PHY-3002 : Step(43): len = 139836, overlap = 6.75
PHY-3002 : Step(44): len = 136030, overlap = 6.75
PHY-3002 : Step(45): len = 135137, overlap = 6.75
PHY-3002 : Step(46): len = 132410, overlap = 4.5
PHY-3002 : Step(47): len = 129860, overlap = 4.5
PHY-3002 : Step(48): len = 126193, overlap = 4.5
PHY-3002 : Step(49): len = 124269, overlap = 6.75
PHY-3002 : Step(50): len = 121359, overlap = 9
PHY-3002 : Step(51): len = 119130, overlap = 6.75
PHY-3002 : Step(52): len = 115188, overlap = 4.5
PHY-3002 : Step(53): len = 113798, overlap = 4.5
PHY-3002 : Step(54): len = 109609, overlap = 6.75
PHY-3002 : Step(55): len = 107544, overlap = 4.5
PHY-3002 : Step(56): len = 105353, overlap = 4.5
PHY-3002 : Step(57): len = 103547, overlap = 4.5
PHY-3002 : Step(58): len = 101319, overlap = 4.5
PHY-3002 : Step(59): len = 100106, overlap = 4.5
PHY-3002 : Step(60): len = 97114.6, overlap = 4.5
PHY-3002 : Step(61): len = 94554, overlap = 6.75
PHY-3002 : Step(62): len = 92080.8, overlap = 4.5
PHY-3002 : Step(63): len = 91780, overlap = 4.5
PHY-3002 : Step(64): len = 90069.1, overlap = 4.5
PHY-3002 : Step(65): len = 86396.5, overlap = 6.75
PHY-3002 : Step(66): len = 82391.1, overlap = 4.5
PHY-3002 : Step(67): len = 81724.8, overlap = 4.5
PHY-3002 : Step(68): len = 80128.8, overlap = 4.5
PHY-3002 : Step(69): len = 79054.3, overlap = 6.75
PHY-3002 : Step(70): len = 77651.1, overlap = 9
PHY-3002 : Step(71): len = 76729.6, overlap = 9
PHY-3002 : Step(72): len = 76037.4, overlap = 6.75
PHY-3002 : Step(73): len = 74964.1, overlap = 6.75
PHY-3002 : Step(74): len = 73996.1, overlap = 4.5
PHY-3002 : Step(75): len = 73418.7, overlap = 4.5
PHY-3002 : Step(76): len = 73319.4, overlap = 4.5
PHY-3002 : Step(77): len = 73085.1, overlap = 4.5
PHY-3002 : Step(78): len = 72536.9, overlap = 6.75
PHY-3002 : Step(79): len = 71989, overlap = 6.75
PHY-3002 : Step(80): len = 70547.6, overlap = 13.5
PHY-3002 : Step(81): len = 69055.9, overlap = 11.25
PHY-3002 : Step(82): len = 68570.7, overlap = 9
PHY-3002 : Step(83): len = 68204.6, overlap = 9
PHY-3002 : Step(84): len = 67133.9, overlap = 6.75
PHY-3002 : Step(85): len = 65855.8, overlap = 9
PHY-3002 : Step(86): len = 65141.8, overlap = 6.75
PHY-3002 : Step(87): len = 64312.1, overlap = 6.75
PHY-3002 : Step(88): len = 63027.9, overlap = 9
PHY-3002 : Step(89): len = 61756.8, overlap = 9
PHY-3002 : Step(90): len = 60703.5, overlap = 9
PHY-3002 : Step(91): len = 60595.4, overlap = 6.75
PHY-3002 : Step(92): len = 60229, overlap = 6.75
PHY-3002 : Step(93): len = 59221, overlap = 6.75
PHY-3002 : Step(94): len = 58614, overlap = 6.75
PHY-3002 : Step(95): len = 57735.2, overlap = 9
PHY-3002 : Step(96): len = 57024.4, overlap = 9
PHY-3002 : Step(97): len = 56622.7, overlap = 9
PHY-3002 : Step(98): len = 56304.3, overlap = 9
PHY-3002 : Step(99): len = 55435.5, overlap = 11.25
PHY-3002 : Step(100): len = 54901.4, overlap = 9
PHY-3002 : Step(101): len = 54697.3, overlap = 6.75
PHY-3002 : Step(102): len = 54505.6, overlap = 9
PHY-3002 : Step(103): len = 54308, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000294181
PHY-3002 : Step(104): len = 54488, overlap = 6.75
PHY-3002 : Step(105): len = 54494.3, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000588362
PHY-3002 : Step(106): len = 54361.8, overlap = 6.75
PHY-3002 : Step(107): len = 54328.4, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008021s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (194.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063470s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(108): len = 57555.7, overlap = 9.0625
PHY-3002 : Step(109): len = 56718.9, overlap = 9.125
PHY-3002 : Step(110): len = 56342, overlap = 8.625
PHY-3002 : Step(111): len = 55534, overlap = 6.84375
PHY-3002 : Step(112): len = 55099.9, overlap = 7.34375
PHY-3002 : Step(113): len = 54187.2, overlap = 6.3125
PHY-3002 : Step(114): len = 53329.9, overlap = 5.75
PHY-3002 : Step(115): len = 52979.8, overlap = 5.375
PHY-3002 : Step(116): len = 52009.7, overlap = 4.28125
PHY-3002 : Step(117): len = 51801.8, overlap = 4.28125
PHY-3002 : Step(118): len = 51288.7, overlap = 4.34375
PHY-3002 : Step(119): len = 50371.4, overlap = 4.375
PHY-3002 : Step(120): len = 49722.1, overlap = 3.5625
PHY-3002 : Step(121): len = 49178.8, overlap = 3.6875
PHY-3002 : Step(122): len = 48925.2, overlap = 3.6875
PHY-3002 : Step(123): len = 48621.5, overlap = 3.375
PHY-3002 : Step(124): len = 48636.7, overlap = 3.375
PHY-3002 : Step(125): len = 47890.1, overlap = 3.0625
PHY-3002 : Step(126): len = 47431.4, overlap = 3.1875
PHY-3002 : Step(127): len = 47171.8, overlap = 4.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000603795
PHY-3002 : Step(128): len = 47043.1, overlap = 4.75
PHY-3002 : Step(129): len = 46988.7, overlap = 4.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00120759
PHY-3002 : Step(130): len = 47023.1, overlap = 4.5625
PHY-3002 : Step(131): len = 47010.3, overlap = 4.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070779s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (88.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.94233e-05
PHY-3002 : Step(132): len = 47197.8, overlap = 52.4375
PHY-3002 : Step(133): len = 47433.6, overlap = 51.8125
PHY-3002 : Step(134): len = 47889.5, overlap = 51.625
PHY-3002 : Step(135): len = 48428.5, overlap = 50.9688
PHY-3002 : Step(136): len = 48593.9, overlap = 52.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000138847
PHY-3002 : Step(137): len = 48507.2, overlap = 52.4375
PHY-3002 : Step(138): len = 49019.6, overlap = 50.25
PHY-3002 : Step(139): len = 49364.3, overlap = 45.4062
PHY-3002 : Step(140): len = 49602.6, overlap = 43.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000277693
PHY-3002 : Step(141): len = 49844, overlap = 43.3438
PHY-3002 : Step(142): len = 49912.1, overlap = 41.375
PHY-3002 : Step(143): len = 50624.3, overlap = 38.5625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7784, tnet num: 2193, tinst num: 1623, tnode num: 11024, tedge num: 13166.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 106.31 peak overflow 3.12
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2195.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54320, over cnt = 268(0%), over = 1202, worst = 17
PHY-1001 : End global iterations;  0.081245s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (134.6%)

PHY-1001 : Congestion index: top1 = 47.26, top5 = 26.91, top10 = 16.83, top15 = 11.83.
PHY-1001 : End incremental global routing;  0.140587s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (122.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.089973s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (104.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.271554s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (109.3%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1702/2195.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54320, over cnt = 268(0%), over = 1202, worst = 17
PHY-1002 : len = 62432, over cnt = 218(0%), over = 525, worst = 13
PHY-1002 : len = 63856, over cnt = 136(0%), over = 366, worst = 13
PHY-1002 : len = 69512, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 69816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.162789s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (115.2%)

PHY-1001 : Congestion index: top1 = 41.27, top5 = 27.22, top10 = 19.61, top15 = 14.25.
OPT-1001 : End congestion update;  0.214008s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (116.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072749s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.291333s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (112.6%)

OPT-1001 : Current memory(MB): used = 216, reserve = 181, peak = 216.
OPT-1001 : End physical optimization;  0.947627s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (113.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 366 LUT to BLE ...
SYN-4008 : Packed 366 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 103 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1068/1400 primitive instances ...
PHY-3001 : End packing;  0.056558s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 840 instances
RUN-1001 : 396 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2027 nets
RUN-1001 : 1478 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 838 instances, 791 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50308.2, Over = 67.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6558, tnet num: 2025, tinst num: 838, tnode num: 8906, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.408979s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (103.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.03932e-05
PHY-3002 : Step(144): len = 50008.4, overlap = 70.5
PHY-3002 : Step(145): len = 49737.9, overlap = 71.5
PHY-3002 : Step(146): len = 49626.7, overlap = 72.5
PHY-3002 : Step(147): len = 49279.7, overlap = 72.25
PHY-3002 : Step(148): len = 49111.1, overlap = 74
PHY-3002 : Step(149): len = 49028.2, overlap = 74.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.07864e-05
PHY-3002 : Step(150): len = 49783.1, overlap = 69.75
PHY-3002 : Step(151): len = 50553.8, overlap = 66.25
PHY-3002 : Step(152): len = 51078.4, overlap = 65
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.15728e-05
PHY-3002 : Step(153): len = 51830.7, overlap = 64.5
PHY-3002 : Step(154): len = 52463.9, overlap = 62.75
PHY-3002 : Step(155): len = 53694.7, overlap = 56.25
PHY-3002 : Step(156): len = 53984.8, overlap = 55.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.119322s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (144.0%)

PHY-3001 : Trial Legalized: Len = 69174.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058780s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000560283
PHY-3002 : Step(157): len = 66252.2, overlap = 6.25
PHY-3002 : Step(158): len = 63627.2, overlap = 15.75
PHY-3002 : Step(159): len = 61746.1, overlap = 18
PHY-3002 : Step(160): len = 60702.1, overlap = 20.75
PHY-3002 : Step(161): len = 59919.2, overlap = 23.5
PHY-3002 : Step(162): len = 59201.7, overlap = 26.5
PHY-3002 : Step(163): len = 58907.1, overlap = 27.5
PHY-3002 : Step(164): len = 58763.4, overlap = 29.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00112057
PHY-3002 : Step(165): len = 59293.9, overlap = 28
PHY-3002 : Step(166): len = 59402.4, overlap = 28.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00224113
PHY-3002 : Step(167): len = 59702.9, overlap = 27.25
PHY-3002 : Step(168): len = 59777.2, overlap = 27.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004869s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64778.6, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006867s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (227.6%)

PHY-3001 : 11 instances has been re-located, deltaX = 1, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 64912.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6558, tnet num: 2025, tinst num: 838, tnode num: 8906, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 46/2027.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71312, over cnt = 145(0%), over = 208, worst = 4
PHY-1002 : len = 71920, over cnt = 65(0%), over = 82, worst = 3
PHY-1002 : len = 72736, over cnt = 13(0%), over = 16, worst = 2
PHY-1002 : len = 72944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.173652s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (144.0%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.85, top10 = 17.79, top15 = 14.02.
PHY-1001 : End incremental global routing;  0.243936s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (134.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.079593s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.365801s wall, 0.406250s user + 0.046875s system = 0.453125s CPU (123.9%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1790/2027.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007057s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (221.4%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.85, top10 = 17.79, top15 = 14.02.
OPT-1001 : End congestion update;  0.053427s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072366s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 800 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 838 instances, 791 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64915.2, Over = 0
PHY-3001 : End spreading;  0.005437s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (287.4%)

PHY-3001 : Final: Len = 64915.2, Over = 0
PHY-3001 : End incremental legalization;  0.049604s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (157.5%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.187736s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (116.5%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057300s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1786/2027.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.011696s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (133.6%)

PHY-1001 : Congestion index: top1 = 31.51, top5 = 22.88, top10 = 17.81, top15 = 14.03.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060395s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.110138s wall, 1.156250s user + 0.062500s system = 1.218750s CPU (109.8%)

RUN-1003 : finish command "place" in  6.148301s wall, 9.296875s user + 3.453125s system = 12.750000s CPU (207.4%)

RUN-1004 : used memory is 203 MB, reserved memory is 168 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 840 instances
RUN-1001 : 396 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2027 nets
RUN-1001 : 1478 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6558, tnet num: 2025, tinst num: 838, tnode num: 8906, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70904, over cnt = 147(0%), over = 207, worst = 4
PHY-1002 : len = 71504, over cnt = 68(0%), over = 86, worst = 4
PHY-1002 : len = 72384, over cnt = 11(0%), over = 15, worst = 3
PHY-1002 : len = 72592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138919s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (135.0%)

PHY-1001 : Congestion index: top1 = 31.49, top5 = 22.78, top10 = 17.71, top15 = 13.94.
PHY-1001 : End global routing;  0.205324s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (129.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 207, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 504, reserve = 472, peak = 504.
PHY-1001 : End build detailed router design. 3.913749s wall, 3.812500s user + 0.078125s system = 3.890625s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33336, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.627161s wall, 1.625000s user + 0.000000s system = 1.625000s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End phase 1; 1.632835s wall, 1.640625s user + 0.000000s system = 1.640625s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 188024, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 537, reserve = 506, peak = 537.
PHY-1001 : End initial routed; 1.366280s wall, 2.625000s user + 0.140625s system = 2.765625s CPU (202.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1792(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.485   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.442000s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 539, reserve = 508, peak = 539.
PHY-1001 : End phase 2; 1.808387s wall, 3.062500s user + 0.140625s system = 3.203125s CPU (177.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 188024, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015244s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 188016, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033866s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (138.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 188096, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.031060s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1792(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.485   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.441068s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (99.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.249995s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End phase 3; 0.892606s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (101.5%)

PHY-1003 : Routed, final wirelength = 188096
PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End export database. 0.009759s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  8.479873s wall, 9.640625s user + 0.234375s system = 9.875000s CPU (116.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6558, tnet num: 2025, tinst num: 838, tnode num: 8906, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  9.579725s wall, 10.781250s user + 0.234375s system = 11.015625s CPU (115.0%)

RUN-1004 : used memory is 525 MB, reserved memory is 496 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      820   out of  19600    4.18%
#reg                     1074   out of  19600    5.48%
#le                      1522
  #lut only               448   out of   1522   29.43%
  #reg only               702   out of   1522   46.12%
  #lut&reg                372   out of   1522   24.44%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1522   |595     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1120   |294     |132     |919     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |530    |122     |57      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |47     |0       |0       |47      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |32     |18      |0       |32      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |88     |31      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |308    |80      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |109    |94      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |50     |47      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1442  
    #2          2       314   
    #3          3       103   
    #4          4        17   
    #5        5-10       79   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6558, tnet num: 2025, tinst num: 838, tnode num: 8906, tedge num: 11529.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2025 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 838
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2027, pip num: 14800
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1303 valid insts, and 39274 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.581299s wall, 19.140625s user + 0.046875s system = 19.187500s CPU (535.8%)

RUN-1004 : used memory is 547 MB, reserved memory is 515 MB, peak memory is 674 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_114358.log"
