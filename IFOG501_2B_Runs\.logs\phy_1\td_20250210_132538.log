============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Feb 10 13:25:38 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 9 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 38 trigger nets, 38 data nets.
KIT-1004 : Chipwatcher code = 0101111100011010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2214/23 useful/useless nets, 1275/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 1899/20 useful/useless nets, 1682/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 378 better
SYN-1014 : Optimize round 2
SYN-1032 : 1595/45 useful/useless nets, 1378/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1643/295 useful/useless nets, 1459/43 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 383 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 36 instances.
SYN-2501 : Optimize round 1, 74 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2071/5 useful/useless nets, 1887/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8011, tnet num: 2071, tinst num: 1886, tnode num: 10139, tedge num: 12238.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2071 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 514 instances into 223 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 372 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 120 adder to BLE ...
SYN-4008 : Packed 120 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.067410s wall, 0.937500s user + 0.125000s system = 1.062500s CPU (99.5%)

RUN-1004 : used memory is 140 MB, reserved memory is 98 MB, peak memory is 159 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (255 clock/control pins, 0 other pins).
SYN-4027 : Net dq_dup_1 is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dq_dup_1 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1302 instances
RUN-0007 : 483 luts, 626 seqs, 86 mslices, 60 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1493 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 831 nets have 2 pins
RUN-1001 : 516 nets have [3 - 5] pins
RUN-1001 : 52 nets have [6 - 10] pins
RUN-1001 : 58 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     146     
RUN-1001 :   No   |  No   |  Yes  |     118     
RUN-1001 :   No   |  Yes  |  No   |     79      
RUN-1001 :   Yes  |  No   |  No   |     32      
RUN-1001 :   Yes  |  No   |  Yes  |     251     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   6   |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 16
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1300 instances, 483 luts, 626 seqs, 146 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-0007 : Cell area utilization is 3%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6744, tnet num: 1491, tinst num: 1300, tnode num: 8967, tedge num: 11211.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.130462s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (95.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 346174
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1300.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 3%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 282217, overlap = 76.5
PHY-3002 : Step(2): len = 231651, overlap = 76.5
PHY-3002 : Step(3): len = 204757, overlap = 76.5
PHY-3002 : Step(4): len = 182085, overlap = 76.5
PHY-3002 : Step(5): len = 162972, overlap = 76.5
PHY-3002 : Step(6): len = 144415, overlap = 76.5
PHY-3002 : Step(7): len = 129400, overlap = 76.5
PHY-3002 : Step(8): len = 115888, overlap = 76.5
PHY-3002 : Step(9): len = 100268, overlap = 76.5
PHY-3002 : Step(10): len = 91005, overlap = 76.5
PHY-3002 : Step(11): len = 82307.1, overlap = 76.5
PHY-3002 : Step(12): len = 72859.3, overlap = 76.5
PHY-3002 : Step(13): len = 68143.2, overlap = 76.5
PHY-3002 : Step(14): len = 60664, overlap = 76.5
PHY-3002 : Step(15): len = 56274.2, overlap = 76.5
PHY-3002 : Step(16): len = 52419.7, overlap = 76.5
PHY-3002 : Step(17): len = 46966.9, overlap = 76.5
PHY-3002 : Step(18): len = 43675.2, overlap = 76.5
PHY-3002 : Step(19): len = 40811.2, overlap = 76.5
PHY-3002 : Step(20): len = 38285.5, overlap = 76.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.81913e-06
PHY-3002 : Step(21): len = 38342.5, overlap = 72
PHY-3002 : Step(22): len = 39101.2, overlap = 67.5
PHY-3002 : Step(23): len = 37603.1, overlap = 67.5
PHY-3002 : Step(24): len = 37068.7, overlap = 67.5
PHY-3002 : Step(25): len = 36579.1, overlap = 69.75
PHY-3002 : Step(26): len = 36056.9, overlap = 72
PHY-3002 : Step(27): len = 35603.1, overlap = 72.125
PHY-3002 : Step(28): len = 34941.7, overlap = 76.5625
PHY-3002 : Step(29): len = 34378, overlap = 76.6875
PHY-3002 : Step(30): len = 34190.1, overlap = 76.625
PHY-3002 : Step(31): len = 33864.9, overlap = 76.5
PHY-3002 : Step(32): len = 33051, overlap = 76.5
PHY-3002 : Step(33): len = 32607.2, overlap = 76.5
PHY-3002 : Step(34): len = 32367.9, overlap = 76.5625
PHY-3002 : Step(35): len = 32051.9, overlap = 74.25
PHY-3002 : Step(36): len = 31395.7, overlap = 74.25
PHY-3002 : Step(37): len = 30715.1, overlap = 74.25
PHY-3002 : Step(38): len = 30203, overlap = 76.5
PHY-3002 : Step(39): len = 29986.8, overlap = 76.5
PHY-3002 : Step(40): len = 29545.2, overlap = 76.5
PHY-3002 : Step(41): len = 29386.5, overlap = 74.25
PHY-3002 : Step(42): len = 29212.3, overlap = 74.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 3.63825e-06
PHY-3002 : Step(43): len = 29173.5, overlap = 74.25
PHY-3002 : Step(44): len = 29282.9, overlap = 74.25
PHY-3002 : Step(45): len = 29373.7, overlap = 74.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 7.27651e-06
PHY-3002 : Step(46): len = 29529.5, overlap = 74.25
PHY-3002 : Step(47): len = 29713.5, overlap = 69.75
PHY-3002 : Step(48): len = 29917.9, overlap = 69.75
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.4553e-05
PHY-3002 : Step(49): len = 29858.8, overlap = 65.25
PHY-3002 : Step(50): len = 29844.9, overlap = 65.25
PHY-3002 : Step(51): len = 30042, overlap = 65.25
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.9106e-05
PHY-3002 : Step(52): len = 29997, overlap = 65.25
PHY-3002 : Step(53): len = 29982.1, overlap = 65.25
PHY-3002 : Step(54): len = 29984.9, overlap = 65.25
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 5.82121e-05
PHY-3002 : Step(55): len = 30043.5, overlap = 58.5
PHY-3002 : Step(56): len = 30062, overlap = 58.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003842s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038455s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (121.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(57): len = 35274.2, overlap = 14.25
PHY-3002 : Step(58): len = 35318.3, overlap = 14.7812
PHY-3002 : Step(59): len = 35254.5, overlap = 14.625
PHY-3002 : Step(60): len = 35340, overlap = 14.2812
PHY-3002 : Step(61): len = 35247.2, overlap = 12.625
PHY-3002 : Step(62): len = 35485.9, overlap = 13.1562
PHY-3002 : Step(63): len = 35709.3, overlap = 14.3125
PHY-3002 : Step(64): len = 35436.8, overlap = 14.625
PHY-3002 : Step(65): len = 35442.7, overlap = 14.7188
PHY-3002 : Step(66): len = 35389.8, overlap = 15.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00596905
PHY-3002 : Step(67): len = 35156, overlap = 15.4375
PHY-3002 : Step(68): len = 35156.3, overlap = 15.5312
PHY-3002 : Step(69): len = 35101.5, overlap = 15.5938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.036334s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (129.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.38966e-05
PHY-3002 : Step(70): len = 36618.2, overlap = 44.7188
PHY-3002 : Step(71): len = 37777, overlap = 39
PHY-3002 : Step(72): len = 37663, overlap = 38.8125
PHY-3002 : Step(73): len = 37933.8, overlap = 38.8125
PHY-3002 : Step(74): len = 37890.9, overlap = 38.5938
PHY-3002 : Step(75): len = 37971, overlap = 38.6875
PHY-3002 : Step(76): len = 38035.4, overlap = 38.125
PHY-3002 : Step(77): len = 38061.8, overlap = 38.4688
PHY-3002 : Step(78): len = 38188.2, overlap = 35.2812
PHY-3002 : Step(79): len = 38110.6, overlap = 34.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000147793
PHY-3002 : Step(80): len = 38145.7, overlap = 34.5938
PHY-3002 : Step(81): len = 38225.9, overlap = 34.4062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000295586
PHY-3002 : Step(82): len = 38519.8, overlap = 30.5312
PHY-3002 : Step(83): len = 38775.5, overlap = 29.8125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000591172
PHY-3002 : Step(84): len = 39069.4, overlap = 29.2188
PHY-3002 : Step(85): len = 39173.6, overlap = 28.9688
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00118234
PHY-3002 : Step(86): len = 39652.7, overlap = 28.1562
PHY-3002 : Step(87): len = 39895.9, overlap = 28.3125
PHY-3002 : Step(88): len = 40020.5, overlap = 26.5938
PHY-3002 : Step(89): len = 39639.2, overlap = 25.25
PHY-3002 : Step(90): len = 39500.5, overlap = 23.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6744, tnet num: 1491, tinst num: 1300, tnode num: 8967, tedge num: 11211.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 69.59 peak overflow 3.19
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1493.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49144, over cnt = 234(0%), over = 762, worst = 13
PHY-1001 : End global iterations;  0.072374s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (151.1%)

PHY-1001 : Congestion index: top1 = 35.99, top5 = 21.88, top10 = 14.84, top15 = 10.56.
PHY-1001 : End incremental global routing;  0.120718s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (116.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045360s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.3%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1288 has valid locations, 34 needs to be replaced
PHY-3001 : design contains 1333 instances, 483 luts, 659 seqs, 146 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 39996.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6876, tnet num: 1524, tinst num: 1333, tnode num: 9198, tedge num: 11409.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.137216s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(91): len = 40219.8, overlap = 0
PHY-3002 : Step(92): len = 40646.7, overlap = 0
PHY-3002 : Step(93): len = 40969.8, overlap = 0
PHY-3002 : Step(94): len = 41133.3, overlap = 0
PHY-3002 : Step(95): len = 41080.6, overlap = 0
PHY-3002 : Step(96): len = 41045.5, overlap = 0
PHY-3002 : Step(97): len = 41045.5, overlap = 0
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072272s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(98): len = 41001.1, overlap = 23.3125
PHY-3002 : Step(99): len = 41001.1, overlap = 23.3125
PHY-3001 : Final: Len = 41001.1, Over = 23.3125
PHY-3001 : End incremental placement;  0.322324s wall, 0.281250s user + 0.062500s system = 0.343750s CPU (106.6%)

OPT-1001 : Total overflow 69.59 peak overflow 3.19
OPT-1001 : End high-fanout net optimization;  0.518415s wall, 0.500000s user + 0.078125s system = 0.578125s CPU (111.5%)

OPT-1001 : Current memory(MB): used = 195, reserve = 151, peak = 195.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1059/1526.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 50920, over cnt = 234(0%), over = 745, worst = 12
PHY-1002 : len = 54352, over cnt = 169(0%), over = 394, worst = 10
PHY-1002 : len = 57448, over cnt = 44(0%), over = 105, worst = 8
PHY-1002 : len = 58064, over cnt = 20(0%), over = 38, worst = 4
PHY-1002 : len = 58088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107630s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (101.6%)

PHY-1001 : Congestion index: top1 = 32.56, top5 = 22.21, top10 = 16.03, top15 = 11.70.
OPT-1001 : End congestion update;  0.155189s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (110.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.037311s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.8%)

OPT-0007 : Start: WNS 4629 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.192729s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (105.4%)

OPT-1001 : Current memory(MB): used = 194, reserve = 150, peak = 195.
OPT-1001 : End physical optimization;  0.828519s wall, 0.812500s user + 0.093750s system = 0.906250s CPU (109.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 483 LUT to BLE ...
SYN-4008 : Packed 483 LUT and 162 SEQ to BLE.
SYN-4003 : Packing 497 remaining SEQ's ...
SYN-4005 : Packed 242 SEQ with LUT/SLICE
SYN-4006 : 114 single LUT's are left
SYN-4006 : 255 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 738/1065 primitive instances ...
PHY-3001 : End packing;  0.039261s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 609 instances
RUN-1001 : 281 mslices, 281 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1365 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 651 nets have 2 pins
RUN-1001 : 566 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 51 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 607 instances, 562 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 42351.2, Over = 37.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6055, tnet num: 1363, tinst num: 607, tnode num: 7767, tedge num: 10363.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.139797s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.75354e-05
PHY-3002 : Step(100): len = 41772.1, overlap = 38.75
PHY-3002 : Step(101): len = 41841.1, overlap = 39.75
PHY-3002 : Step(102): len = 41602.5, overlap = 38.25
PHY-3002 : Step(103): len = 41617.5, overlap = 40
PHY-3002 : Step(104): len = 41457.5, overlap = 39.75
PHY-3002 : Step(105): len = 41357, overlap = 38.75
PHY-3002 : Step(106): len = 41157.2, overlap = 38
PHY-3002 : Step(107): len = 40887.7, overlap = 37.75
PHY-3002 : Step(108): len = 40608.9, overlap = 35.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000115071
PHY-3002 : Step(109): len = 40687.1, overlap = 35.5
PHY-3002 : Step(110): len = 40808.4, overlap = 34.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000230141
PHY-3002 : Step(111): len = 41447.9, overlap = 33.5
PHY-3002 : Step(112): len = 41992.4, overlap = 31.25
PHY-3002 : Step(113): len = 42134.6, overlap = 31.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.082263s wall, 0.046875s user + 0.078125s system = 0.125000s CPU (152.0%)

PHY-3001 : Trial Legalized: Len = 52687.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.035059s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (133.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00375655
PHY-3002 : Step(114): len = 50223.7, overlap = 6
PHY-3002 : Step(115): len = 49125.8, overlap = 6
PHY-3002 : Step(116): len = 47691.3, overlap = 8.25
PHY-3002 : Step(117): len = 46719.1, overlap = 9.5
PHY-3002 : Step(118): len = 46259.2, overlap = 10.25
PHY-3002 : Step(119): len = 45710.7, overlap = 10.75
PHY-3002 : Step(120): len = 45127.2, overlap = 12
PHY-3002 : Step(121): len = 44823.1, overlap = 13
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00751311
PHY-3002 : Step(122): len = 44813.9, overlap = 12.5
PHY-3002 : Step(123): len = 44639.3, overlap = 13
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0150262
PHY-3002 : Step(124): len = 44634.5, overlap = 13.25
PHY-3002 : Step(125): len = 44578.5, overlap = 13.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005265s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (296.8%)

PHY-3001 : Legalized: Len = 48736.4, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004362s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 2, deltaY = 5, maxDist = 2.
PHY-3001 : Final: Len = 48918.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6055, tnet num: 1363, tinst num: 607, tnode num: 7767, tedge num: 10363.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 33/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 61184, over cnt = 162(0%), over = 247, worst = 4
PHY-1002 : len = 61864, over cnt = 107(0%), over = 146, worst = 4
PHY-1002 : len = 63408, over cnt = 21(0%), over = 26, worst = 2
PHY-1002 : len = 63720, over cnt = 7(0%), over = 8, worst = 2
PHY-1002 : len = 63816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.152794s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.5%)

PHY-1001 : Congestion index: top1 = 26.47, top5 = 21.20, top10 = 16.87, top15 = 12.94.
PHY-1001 : End incremental global routing;  0.202524s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (108.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.042026s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (111.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.274252s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (108.2%)

OPT-1001 : Current memory(MB): used = 195, reserve = 151, peak = 198.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1176/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004853s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 26.47, top5 = 21.20, top10 = 16.87, top15 = 12.94.
OPT-1001 : End congestion update;  0.048885s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (95.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.040116s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (116.8%)

OPT-0007 : Start: WNS 4763 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.089245s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (105.0%)

OPT-1001 : Current memory(MB): used = 197, reserve = 154, peak = 198.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.038388s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (81.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1176/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004857s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 26.47, top5 = 21.20, top10 = 16.87, top15 = 12.94.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.040117s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (116.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4763 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 26.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4763ps with logic level 7 
OPT-1001 : End physical optimization;  0.623830s wall, 0.593750s user + 0.046875s system = 0.640625s CPU (102.7%)

RUN-1003 : finish command "place" in  3.852744s wall, 5.296875s user + 2.125000s system = 7.421875s CPU (192.6%)

RUN-1004 : used memory is 188 MB, reserved memory is 144 MB, peak memory is 198 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 609 instances
RUN-1001 : 281 mslices, 281 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1365 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 651 nets have 2 pins
RUN-1001 : 566 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 51 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6055, tnet num: 1363, tinst num: 607, tnode num: 7767, tedge num: 10363.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 281 mslices, 281 lslices, 8 pads, 34 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60488, over cnt = 171(0%), over = 278, worst = 5
PHY-1002 : len = 61528, over cnt = 98(0%), over = 135, worst = 3
PHY-1002 : len = 62440, over cnt = 38(0%), over = 48, worst = 3
PHY-1002 : len = 63152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.150114s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (145.7%)

PHY-1001 : Congestion index: top1 = 26.68, top5 = 21.11, top10 = 16.65, top15 = 12.75.
PHY-1001 : End global routing;  0.197939s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (134.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 218, reserve = 175, peak = 218.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net dq_dup_1 will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 477, reserve = 437, peak = 477.
PHY-1001 : End build detailed router design. 3.036597s wall, 2.906250s user + 0.125000s system = 3.031250s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 28672, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.601042s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 509, reserve = 470, peak = 509.
PHY-1001 : End phase 1; 0.607403s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 68% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 236752, over cnt = 50(0%), over = 50, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 510, reserve = 471, peak = 510.
PHY-1001 : End initial routed; 1.968257s wall, 2.703125s user + 0.093750s system = 2.796875s CPU (142.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1228(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.758   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.191027s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.2%)

PHY-1001 : Current memory(MB): used = 512, reserve = 473, peak = 512.
PHY-1001 : End phase 2; 2.159392s wall, 2.890625s user + 0.093750s system = 2.984375s CPU (138.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 236752, over cnt = 50(0%), over = 50, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.011493s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (136.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 236200, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.046947s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (133.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 236296, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.021290s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (146.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 236312, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.017908s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (87.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1228(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.758   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.196081s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.168973s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.7%)

PHY-1001 : Current memory(MB): used = 527, reserve = 487, peak = 527.
PHY-1001 : End phase 3; 0.584820s wall, 0.625000s user + 0.000000s system = 0.625000s CPU (106.9%)

PHY-1003 : Routed, final wirelength = 236312
PHY-1001 : Current memory(MB): used = 528, reserve = 488, peak = 528.
PHY-1001 : End export database. 0.010502s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.568173s wall, 7.203125s user + 0.218750s system = 7.421875s CPU (113.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6055, tnet num: 1363, tinst num: 607, tnode num: 7767, tedge num: 10363.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.142780s wall, 7.843750s user + 0.218750s system = 8.062500s CPU (112.9%)

RUN-1004 : used memory is 484 MB, reserved memory is 444 MB, peak memory is 528 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   5
  #inout                    0

Utilization Statistics
#lut                      803   out of  19600    4.10%
#reg                      672   out of  19600    3.43%
#le                      1054
  #lut only               382   out of   1054   36.24%
  #reg only               251   out of   1054   23.81%
  #lut&reg                421   out of   1054   39.94%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     0
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        dq_dup_1             GCLK               pll                CLK120/pll_inst.clkc0    294
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         139
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
   miso        INPUT         A6        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         A8        LVCMOS33           8            NONE       OREG    
    dq        OUTPUT        B16        LVCMOS33           8            NONE       NONE    
   mosi       OUTPUT         A2        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         A4        LVCMOS33           8            NONE       OREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1054   |657     |146     |677     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |98     |75      |11      |65      |0       |0       |
|    usms                            |Time_1ms        |27     |12      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |195    |128     |23      |128     |0       |0       |
|  uart                              |UART_Control    |126    |108     |7       |57      |0       |0       |
|    U0                              |speed_select_Tx |23     |14      |7       |16      |0       |0       |
|    U1                              |uart_tx         |25     |24      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data       |78     |70      |0       |25      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |603    |329     |99      |401     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |603    |329     |99      |401     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |249    |107     |0       |245     |0       |0       |
|        reg_inst                    |register        |246    |104     |0       |242     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |354    |222     |99      |156     |0       |0       |
|        bus_inst                    |bus_top         |107    |65      |42      |33      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |12     |6       |6       |3       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |66     |40      |26      |17      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |140    |86      |29      |83      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       642   
    #2          2       339   
    #3          3       176   
    #4          4        51   
    #5        5-10       62   
    #6        11-50      72   
    #7       51-100      3    
    #8       101-500     1    
  Average     3.14            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6055, tnet num: 1363, tinst num: 607, tnode num: 7767, tedge num: 10363.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 607
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1365, pip num: 15398
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1337 valid insts, and 40245 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011001110101111100011010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.132650s wall, 17.062500s user + 0.046875s system = 17.109375s CPU (546.2%)

RUN-1004 : used memory is 499 MB, reserved memory is 459 MB, peak memory is 645 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250210_132538.log"
