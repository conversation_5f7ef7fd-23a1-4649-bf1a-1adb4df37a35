============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Sep  6 14:39:26 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1618 instances
RUN-0007 : 364 luts, 984 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2188 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1639 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1616 instances, 364 luts, 984 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7733, tnet num: 2186, tinst num: 1616, tnode num: 10973, tedge num: 13078.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2186 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.296882s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 612638
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1616.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 513862, overlap = 18
PHY-3002 : Step(2): len = 479878, overlap = 20.25
PHY-3002 : Step(3): len = 415239, overlap = 15.75
PHY-3002 : Step(4): len = 366191, overlap = 9
PHY-3002 : Step(5): len = 349242, overlap = 18
PHY-3002 : Step(6): len = 338185, overlap = 18
PHY-3002 : Step(7): len = 325386, overlap = 18
PHY-3002 : Step(8): len = 309400, overlap = 20.25
PHY-3002 : Step(9): len = 299339, overlap = 20.25
PHY-3002 : Step(10): len = 292217, overlap = 20.25
PHY-3002 : Step(11): len = 281134, overlap = 20.25
PHY-3002 : Step(12): len = 271045, overlap = 20.25
PHY-3002 : Step(13): len = 266749, overlap = 20.25
PHY-3002 : Step(14): len = 259221, overlap = 20.25
PHY-3002 : Step(15): len = 245035, overlap = 20.25
PHY-3002 : Step(16): len = 238785, overlap = 20.25
PHY-3002 : Step(17): len = 236051, overlap = 20.25
PHY-3002 : Step(18): len = 218417, overlap = 20.25
PHY-3002 : Step(19): len = 211380, overlap = 20.25
PHY-3002 : Step(20): len = 208690, overlap = 20.25
PHY-3002 : Step(21): len = 201620, overlap = 20.25
PHY-3002 : Step(22): len = 189868, overlap = 20.25
PHY-3002 : Step(23): len = 187706, overlap = 20.25
PHY-3002 : Step(24): len = 183278, overlap = 20.25
PHY-3002 : Step(25): len = 176483, overlap = 20.25
PHY-3002 : Step(26): len = 171601, overlap = 20.25
PHY-3002 : Step(27): len = 169465, overlap = 20.25
PHY-3002 : Step(28): len = 163670, overlap = 20.25
PHY-3002 : Step(29): len = 157134, overlap = 20.25
PHY-3002 : Step(30): len = 153326, overlap = 20.25
PHY-3002 : Step(31): len = 151098, overlap = 20.25
PHY-3002 : Step(32): len = 144007, overlap = 20.25
PHY-3002 : Step(33): len = 140636, overlap = 20.25
PHY-3002 : Step(34): len = 137125, overlap = 20.25
PHY-3002 : Step(35): len = 133422, overlap = 20.25
PHY-3002 : Step(36): len = 131335, overlap = 20.25
PHY-3002 : Step(37): len = 125391, overlap = 20.25
PHY-3002 : Step(38): len = 115207, overlap = 18
PHY-3002 : Step(39): len = 112979, overlap = 20.25
PHY-3002 : Step(40): len = 109114, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.92235e-05
PHY-3002 : Step(41): len = 111535, overlap = 13.5
PHY-3002 : Step(42): len = 111449, overlap = 11.25
PHY-3002 : Step(43): len = 109355, overlap = 11.25
PHY-3002 : Step(44): len = 107028, overlap = 15.75
PHY-3002 : Step(45): len = 106056, overlap = 15.75
PHY-3002 : Step(46): len = 102036, overlap = 4.5
PHY-3002 : Step(47): len = 97052.4, overlap = 9
PHY-3002 : Step(48): len = 94606.5, overlap = 11.25
PHY-3002 : Step(49): len = 92565.7, overlap = 13.5
PHY-3002 : Step(50): len = 91790.5, overlap = 11.25
PHY-3002 : Step(51): len = 90259.3, overlap = 9
PHY-3002 : Step(52): len = 88493.2, overlap = 13.5
PHY-3002 : Step(53): len = 87332.5, overlap = 9
PHY-3002 : Step(54): len = 87585.1, overlap = 11.25
PHY-3002 : Step(55): len = 87159.2, overlap = 11.25
PHY-3002 : Step(56): len = 85718.1, overlap = 11.25
PHY-3002 : Step(57): len = 84507.9, overlap = 9
PHY-3002 : Step(58): len = 81987.1, overlap = 13.5
PHY-3002 : Step(59): len = 80561.1, overlap = 15.75
PHY-3002 : Step(60): len = 78671.1, overlap = 13.5
PHY-3002 : Step(61): len = 77261.1, overlap = 6.75
PHY-3002 : Step(62): len = 75061.5, overlap = 9
PHY-3002 : Step(63): len = 73823.8, overlap = 11.6875
PHY-3002 : Step(64): len = 72548.4, overlap = 14.0625
PHY-3002 : Step(65): len = 72115.7, overlap = 16.25
PHY-3002 : Step(66): len = 70377, overlap = 12.375
PHY-3002 : Step(67): len = 68475.7, overlap = 14.9375
PHY-3002 : Step(68): len = 67096.5, overlap = 17.25
PHY-3002 : Step(69): len = 65653.5, overlap = 16.9375
PHY-3002 : Step(70): len = 62710.7, overlap = 12.5
PHY-3002 : Step(71): len = 62169.4, overlap = 10.0625
PHY-3002 : Step(72): len = 61441.2, overlap = 10.0625
PHY-3002 : Step(73): len = 60056.7, overlap = 17
PHY-3002 : Step(74): len = 59215.4, overlap = 15
PHY-3002 : Step(75): len = 58332.2, overlap = 14.875
PHY-3002 : Step(76): len = 57902.7, overlap = 14.875
PHY-3002 : Step(77): len = 56950.3, overlap = 13
PHY-3002 : Step(78): len = 56144.7, overlap = 13.5
PHY-3002 : Step(79): len = 55165.4, overlap = 11.3125
PHY-3002 : Step(80): len = 53882.2, overlap = 18.5
PHY-3002 : Step(81): len = 53339.6, overlap = 16.5
PHY-3002 : Step(82): len = 52883.8, overlap = 16.6875
PHY-3002 : Step(83): len = 52549.1, overlap = 19.4375
PHY-3002 : Step(84): len = 52281.2, overlap = 19.375
PHY-3002 : Step(85): len = 51912.7, overlap = 19.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000178447
PHY-3002 : Step(86): len = 52127.2, overlap = 17.125
PHY-3002 : Step(87): len = 52148.8, overlap = 17.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000356894
PHY-3002 : Step(88): len = 52056.8, overlap = 14.875
PHY-3002 : Step(89): len = 52061.1, overlap = 14.875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006395s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2186 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071026s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (110.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(90): len = 55718.1, overlap = 15.625
PHY-3002 : Step(91): len = 54543.2, overlap = 16.5
PHY-3002 : Step(92): len = 53988.7, overlap = 16.5
PHY-3002 : Step(93): len = 53784.3, overlap = 16
PHY-3002 : Step(94): len = 53275, overlap = 16.5312
PHY-3002 : Step(95): len = 51485.8, overlap = 16.625
PHY-3002 : Step(96): len = 50703.7, overlap = 16.9688
PHY-3002 : Step(97): len = 50112.9, overlap = 17.375
PHY-3002 : Step(98): len = 49989, overlap = 17.3438
PHY-3002 : Step(99): len = 49537.6, overlap = 17.4375
PHY-3002 : Step(100): len = 49026.7, overlap = 16.9375
PHY-3002 : Step(101): len = 48658.2, overlap = 17.125
PHY-3002 : Step(102): len = 48266.4, overlap = 17.5625
PHY-3002 : Step(103): len = 47746.3, overlap = 18.6562
PHY-3002 : Step(104): len = 47465.1, overlap = 19.1562
PHY-3002 : Step(105): len = 46908.5, overlap = 19.1562
PHY-3002 : Step(106): len = 46590.2, overlap = 19.1562
PHY-3002 : Step(107): len = 46212.3, overlap = 19.125
PHY-3002 : Step(108): len = 46155.8, overlap = 19.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0030813
PHY-3002 : Step(109): len = 46113.5, overlap = 19.25
PHY-3002 : Step(110): len = 46141.1, overlap = 19.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0061626
PHY-3002 : Step(111): len = 46033.6, overlap = 18.9688
PHY-3002 : Step(112): len = 46033.6, overlap = 18.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2186 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064824s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.10453e-05
PHY-3002 : Step(113): len = 46383.3, overlap = 66.2188
PHY-3002 : Step(114): len = 46859.1, overlap = 65.6875
PHY-3002 : Step(115): len = 47575.9, overlap = 57.875
PHY-3002 : Step(116): len = 47833.1, overlap = 55.3125
PHY-3002 : Step(117): len = 47807.9, overlap = 51.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000122091
PHY-3002 : Step(118): len = 47905.3, overlap = 50.2812
PHY-3002 : Step(119): len = 48057.7, overlap = 49.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000244181
PHY-3002 : Step(120): len = 48589, overlap = 49.1875
PHY-3002 : Step(121): len = 50081.6, overlap = 42.5938
PHY-3002 : Step(122): len = 50229.3, overlap = 42.25
PHY-3002 : Step(123): len = 50572.2, overlap = 37.375
PHY-3002 : Step(124): len = 51393.3, overlap = 36.6875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7733, tnet num: 2186, tinst num: 1616, tnode num: 10973, tedge num: 13078.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.97 peak overflow 2.97
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2188.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55064, over cnt = 253(0%), over = 1089, worst = 22
PHY-1001 : End global iterations;  0.054700s wall, 0.062500s user + 0.031250s system = 0.093750s CPU (171.4%)

PHY-1001 : Congestion index: top1 = 43.53, top5 = 25.85, top10 = 16.45, top15 = 11.65.
PHY-1001 : End incremental global routing;  0.104917s wall, 0.109375s user + 0.046875s system = 0.156250s CPU (148.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2186 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066155s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.201512s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (124.1%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1684/2188.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55064, over cnt = 253(0%), over = 1089, worst = 22
PHY-1002 : len = 61648, over cnt = 172(0%), over = 478, worst = 15
PHY-1002 : len = 66704, over cnt = 50(0%), over = 62, worst = 4
PHY-1002 : len = 67696, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 67848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095649s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (114.4%)

PHY-1001 : Congestion index: top1 = 37.89, top5 = 26.08, top10 = 18.49, top15 = 13.53.
OPT-1001 : End congestion update;  0.139133s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (112.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2186 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057889s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.199369s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (109.7%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.673500s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (109.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 364 LUT to BLE ...
SYN-4008 : Packed 364 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 105 SEQ with LUT/SLICE
SYN-4006 : 100 single LUT's are left
SYN-4006 : 698 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1062/1390 primitive instances ...
PHY-3001 : End packing;  0.050354s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (93.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 826 instances
RUN-1001 : 388 mslices, 389 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2021 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1482 nets have 2 pins
RUN-1001 : 424 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 824 instances, 777 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51392.6, Over = 66.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2019, tinst num: 824, tnode num: 8835, tedge num: 11421.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.313748s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (104.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.5618e-05
PHY-3002 : Step(125): len = 50953.2, overlap = 65.75
PHY-3002 : Step(126): len = 50622.6, overlap = 65.75
PHY-3002 : Step(127): len = 50206.4, overlap = 66.75
PHY-3002 : Step(128): len = 49955, overlap = 67
PHY-3002 : Step(129): len = 49978.5, overlap = 66
PHY-3002 : Step(130): len = 50004.2, overlap = 64.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.1236e-05
PHY-3002 : Step(131): len = 50090.5, overlap = 62.5
PHY-3002 : Step(132): len = 50995.9, overlap = 59.75
PHY-3002 : Step(133): len = 51635.3, overlap = 57.25
PHY-3002 : Step(134): len = 51476.8, overlap = 56.75
PHY-3002 : Step(135): len = 51533.2, overlap = 56.25
PHY-3002 : Step(136): len = 51633.7, overlap = 55.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000102472
PHY-3002 : Step(137): len = 52392.5, overlap = 52.75
PHY-3002 : Step(138): len = 52892.3, overlap = 52
PHY-3002 : Step(139): len = 53065.6, overlap = 49.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.091157s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (171.4%)

PHY-3001 : Trial Legalized: Len = 68385.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050923s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000468899
PHY-3002 : Step(140): len = 65246.4, overlap = 8.5
PHY-3002 : Step(141): len = 63213.2, overlap = 14
PHY-3002 : Step(142): len = 61369.4, overlap = 17
PHY-3002 : Step(143): len = 60119.6, overlap = 21.75
PHY-3002 : Step(144): len = 59167.1, overlap = 23.5
PHY-3002 : Step(145): len = 58608.4, overlap = 25.5
PHY-3002 : Step(146): len = 58201.9, overlap = 25.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000937799
PHY-3002 : Step(147): len = 58633, overlap = 23.5
PHY-3002 : Step(148): len = 58761.7, overlap = 23
PHY-3002 : Step(149): len = 58782.4, overlap = 22.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0018756
PHY-3002 : Step(150): len = 58934.1, overlap = 23.25
PHY-3002 : Step(151): len = 58934.1, overlap = 23.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005336s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (292.8%)

PHY-3001 : Legalized: Len = 64063.7, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005410s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 3 instances has been re-located, deltaX = 3, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 64095.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2019, tinst num: 824, tnode num: 8835, tedge num: 11421.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 49/2021.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70072, over cnt = 124(0%), over = 168, worst = 5
PHY-1002 : len = 70608, over cnt = 47(0%), over = 56, worst = 5
PHY-1002 : len = 71304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118220s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (171.8%)

PHY-1001 : Congestion index: top1 = 31.19, top5 = 21.97, top10 = 17.13, top15 = 13.62.
PHY-1001 : End incremental global routing;  0.169799s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (147.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059882s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (104.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.261596s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (131.4%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1771/2021.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005862s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.19, top5 = 21.97, top10 = 17.13, top15 = 13.62.
OPT-1001 : End congestion update;  0.053169s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049192s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 786 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 824 instances, 777 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64104.6, Over = 0
PHY-3001 : End spreading;  0.005040s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64104.6, Over = 0
PHY-3001 : End incremental legalization;  0.035437s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.2%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.152450s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.5%)

OPT-1001 : Current memory(MB): used = 225, reserve = 189, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049395s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1764/2021.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71328, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71336, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.027006s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (115.7%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 22.07, top10 = 17.17, top15 = 13.63.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050365s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.882241s wall, 0.890625s user + 0.062500s system = 0.953125s CPU (108.0%)

RUN-1003 : finish command "place" in  5.313016s wall, 8.718750s user + 2.593750s system = 11.312500s CPU (212.9%)

RUN-1004 : used memory is 202 MB, reserved memory is 165 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 826 instances
RUN-1001 : 388 mslices, 389 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2021 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1482 nets have 2 pins
RUN-1001 : 424 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2019, tinst num: 824, tnode num: 8835, tedge num: 11421.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 388 mslices, 389 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69736, over cnt = 127(0%), over = 175, worst = 5
PHY-1002 : len = 70408, over cnt = 41(0%), over = 51, worst = 5
PHY-1002 : len = 70728, over cnt = 25(0%), over = 26, worst = 2
PHY-1002 : len = 71112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131259s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (107.1%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 22.05, top10 = 17.14, top15 = 13.54.
PHY-1001 : End global routing;  0.181416s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (103.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 205, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 468, peak = 500.
PHY-1001 : End build detailed router design. 3.269881s wall, 3.218750s user + 0.046875s system = 3.265625s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33536, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.286832s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (95.9%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.292701s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (95.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179992, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 501, peak = 534.
PHY-1001 : End initial routed; 1.043021s wall, 2.343750s user + 0.140625s system = 2.484375s CPU (238.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1788(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.902   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.376930s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End phase 2; 1.420041s wall, 2.718750s user + 0.140625s system = 2.859375s CPU (201.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179992, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015741s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (99.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179904, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032582s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179896, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023674s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (66.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1788(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.902   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.374515s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.175926s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.6%)

PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End phase 3; 0.744923s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.7%)

PHY-1003 : Routed, final wirelength = 179896
PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End export database. 0.009751s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.921788s wall, 8.109375s user + 0.187500s system = 8.296875s CPU (119.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2019, tinst num: 824, tnode num: 8835, tedge num: 11421.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.836183s wall, 9.031250s user + 0.203125s system = 9.234375s CPU (117.8%)

RUN-1004 : used memory is 502 MB, reserved memory is 472 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      808   out of  19600    4.12%
#reg                     1074   out of  19600    5.48%
#le                      1506
  #lut only               432   out of   1506   28.69%
  #reg only               698   out of   1506   46.35%
  #lut&reg                376   out of   1506   24.97%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       465
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       108
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_13.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1506   |587     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1115   |295     |129     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |20      |7       |21      |0       |0       |
|    demodu                  |Demodulation                                     |520    |115     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |0       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |17      |0       |27      |0       |0       |
|    integ                   |Integration                                      |140    |16      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |89     |32      |21      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |308    |86      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |105    |91      |7       |60      |0       |0       |
|    U0                      |speed_select_Tx                                  |31     |24      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |20     |17      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |54     |50      |0       |28      |0       |0       |
|  wendu                     |DS18B20                                          |205    |160     |45      |67      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1446  
    #2          2       305   
    #3          3       100   
    #4          4        19   
    #5        5-10       77   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2019, tinst num: 824, tnode num: 8835, tedge num: 11421.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2019 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 824
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2021, pip num: 14503
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1302 valid insts, and 38486 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.055104s wall, 17.937500s user + 0.046875s system = 17.984375s CPU (588.7%)

RUN-1004 : used memory is 519 MB, reserved memory is 486 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230906_143926.log"
