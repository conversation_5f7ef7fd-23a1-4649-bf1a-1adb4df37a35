============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Sep  7 15:24:15 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "elaborate -top IFOG501_2B"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-1007 : elaborate module IFOG501_2B in ../../Src_al/IFOG501_2B.v(17)
HDL-5007 WARNING: latch inferred for net 'dlycnt[19]' in ../../Src_al/IFOG501_2B.v(74)
HDL-1007 : elaborate module global_clock in ../../al_ip/global_clock.v(24)
HDL-1007 : elaborate module EG_PHY_PLL(FIN="20.000",FBCLK_DIV=48,CLKC0_DIV=8,CLKC3_DIV=16,CLKC4_DIV=16,CLKC0_ENABLE="ENABLE",CLKC3_ENABLE="ENABLE",CLKC4_ENABLE="ENABLE",FEEDBK_MODE="NOCOMP",STDBY_ENABLE="DISABLE",CLKC4_FPHASE=3,CLKC0_CPHASE=7,CLKC3_CPHASE=15,CLKC4_CPHASE=12,GMC_GAIN=2,ICP_CURRENT=9,KVCO=2,LPF_CAPACITOR=1,LPF_RESISTOR=8,SYNC_ENABLE="DISABLE") in C:/Anlogic/TD5.6.2/arch/eagle_macro.v(930)
HDL-1007 : elaborate module EG_LOGIC_ODDR in C:/Anlogic/TD5.6.2/arch/eagle_macro.v(87)
HDL-1007 : elaborate module DS18B20 in ../../Src_al/DS18B20.v(17)
HDL-1007 : elaborate module UART_Control in ../../Src_al/UART_Control.v(16)
HDL-1007 : elaborate module speed_select_Tx in ../../Src_al/speed_select_Tx.v(17)
HDL-1007 : elaborate module uart_tx in ../../Src_al/uart_tx.v(16)
HDL-1007 : elaborate module Ctrl_Data in ../../Src_al/Ctrl_Data.v(17)
HDL-1007 : elaborate module SignalProcessing(acum_cnt=17,iWID_TRANS=13,iTRANSIT_TIME=182,iAD_VALID_START=102,iFEEDBACK_SCALE=10,iOUTPUT_SCALE=1350,iDELAYED=120,DA_CONSTANT=13300,iTRANSMIT_COFF=600000) in ../../Src_al/SignalProcessing.v(41)
HDL-1007 : elaborate module SignalGenerator(iTRANSIT_TIME=182,iAD_VALID_START=102) in ../../Src_al/SignalGenerator.v(42)
HDL-1007 : elaborate module Demodulation(acum_cnt=17) in ../../Src_al/Demodulation.v(42)
HDL-1007 : port 'doa' remains unconnected for this instance in ../../al_ip/Asys_fifo56X16.v(261)
HDL-1007 : port 'dib' remains unconnected for this instance in ../../al_ip/Asys_fifo56X16.v(261)
HDL-1007 : elaborate module Asys_fifo56X16 in ../../al_ip/Asys_fifo56X16.v(26)
HDL-1007 : elaborate module fifo_cross_domain_addr_process_al_Asys_fifo56X16(ADDR_WIDTH=7) in ../../al_ip/Asys_fifo56X16.v(290)
HDL-1007 : elaborate module ram_infer_Asys_fifo56X16(DATAWIDTH_A=56,ADDRWIDTH_A=7,DATAWIDTH_B=56,REGMODE_B="OUTREG",ADDRWIDTH_B=7) in ../../al_ip/Asys_fifo56X16.v(378)
HDL-1007 : extracting RAM for identifier 'memory' in ../../al_ip/Asys_fifo56X16.v(443)
HDL-5007 WARNING: input port 'dib[55]' is not connected on this instance in ../../al_ip/Asys_fifo56X16.v(266)
HDL-1007 : elaborate module Integration in ../../Src_al/Integration.v(42)
HDL-1007 : elaborate module Modulation(iWID_TRANS=13,DA_CONSTANT=13300) in ../../Src_al/Modulation.v(42)
HDL-1007 : elaborate module Rs422Output(iDELAYED=120) in ../../Src_al/Rs422Output.v(43)
HDL-1007 : elaborate module SquareWaveGenerator(iTRANSMIT_COFF=600000) in ../../Src_al/SquareWaveGenerator.v(16)
HDL-1200 : Current top model is IFOG501_2B
HDL-1100 : Inferred 1 RAMs.
RUN-1002 : start command "export_db IFOG501_2B_elaborate.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "read_adc ../../Constraints/IFOG_11FB.adc"
RUN-1002 : start command "set_pin_assignment  AD_DATA[0]   LOCATION = P19; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[10]   LOCATION = P3; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[11]   LOCATION = P2; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[1]   LOCATION = P18; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[2]   LOCATION = P17; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[3]   LOCATION = P16; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[4]   LOCATION = P14; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[5]   LOCATION = P12; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[6]   LOCATION = P11; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[7]   LOCATION = P10; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[8]   LOCATION = P5; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  AD_DATA[9]   LOCATION = P4; IOSTANDARD = LVCMOS33; PULLTYPE = NONE; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[0]   LOCATION = P60; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[1]   LOCATION = P59; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[2]   LOCATION = P57; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[3]   LOCATION = P61; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[4]   LOCATION = P62; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[5]   LOCATION = P63; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[6]   LOCATION = P55; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[7]   LOCATION = P52; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[8]   LOCATION = P51; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[9]   LOCATION = P50; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[10]   LOCATION = P49; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[11]   LOCATION = P48; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[12]   LOCATION = P47; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  DA_DATA[13]   LOCATION = P45; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; PACKREG = ON; "
RUN-1002 : start command "set_pin_assignment  RXD   LOCATION = P77; IOSTANDARD = LVCMOS33; PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  RxTransmit   LOCATION = P74; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  TXD   LOCATION = P76; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  TxTransmit   LOCATION = P79; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  clk_ADo   LOCATION = P13; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 16; PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  clk_DA   LOCATION = P54; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; SLEWRATE = FAST; "
RUN-1002 : start command "set_pin_assignment  clk_in   LOCATION = P34; IOSTANDARD = LVCMOS33; PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  dq   LOCATION = P87; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = PULLUP; "
RUN-1001 : Starting of IO setups legality check.
RUN-1001 : Starting of IO setups legality check.
RUN-1001 : Starting of IO vref setups legality check.
RUN-1002 : start command "optimize_rtl"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "global_clock"
SYN-1012 : SanityCheck: Model "SignalProcessing(acum_cnt=17,iWID_TRANS=13,iTRANSIT_TIME=182,iAD_VALID_START=102,iFEEDBACK_SCALE=10,iOUTPUT_SCALE=1350,iDELAYED=120,DA_CONSTANT=13300,iTRANSMIT_COFF=600000)"
SYN-1012 : SanityCheck: Model "SignalGenerator(iTRANSIT_TIME=182,iAD_VALID_START=102)"
SYN-1012 : SanityCheck: Model "Demodulation(acum_cnt=17)"
SYN-1012 : SanityCheck: Model "Asys_fifo56X16"
SYN-1012 : SanityCheck: Model "ram_infer_Asys_fifo56X16(DATAWIDTH_A=56,ADDRWIDTH_A=7,DATAWIDTH_B=56,REGMODE_B="OUTREG",ADDRWIDTH_B=7)"
SYN-1012 : SanityCheck: Model "fifo_cross_domain_addr_process_al_Asys_fifo56X16(ADDR_WIDTH=7)"
SYN-1012 : SanityCheck: Model "Integration"
SYN-1012 : SanityCheck: Model "Modulation(iWID_TRANS=13,DA_CONSTANT=13300)"
SYN-1012 : SanityCheck: Model "Rs422Output(iDELAYED=120)"
SYN-1012 : SanityCheck: Model "SquareWaveGenerator(iTRANSMIT_COFF=600000)"
SYN-1012 : SanityCheck: Model "UART_Control"
SYN-1012 : SanityCheck: Model "speed_select_Tx"
SYN-1012 : SanityCheck: Model "uart_tx"
SYN-1012 : SanityCheck: Model "Ctrl_Data"
SYN-1012 : SanityCheck: Model "DS18B20"
SYN-1043 : Mark DS18B20 as IO macro for instance dq_i
SYN-1043 : Mark global_clock as IO macro for instance pll_inst
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3983/1595 useful/useless nets, 615/123 useful/useless insts
SYN-1001 : Bypass 1 mux instances
SYN-1016 : Merged 6724 instances.
SYN-1025 : Merged 32 RAM ports.
SYN-1026 : Infer Logic BRAM(signal_process/demodu/fifo/ram_inst/ramread0_syn_6)
	 port mode: single dual port
	 port a size: 128 x 56	 write mode: NORMAL
	 port b size: 128 x 56	 write mode: READBEFOREWRITE
SYN-1023 : Infer 0 Logic DRAMs, 1 Logic BRAMs.
SYN-1032 : 4238/800 useful/useless nets, 1294/246 useful/useless insts
SYN-1016 : Merged 82 instances.
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_2", d: "dlycnt_b[0]", q: "dlycnt[0]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_3", d: "dlycnt_b[1]", q: "dlycnt[1]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_4", d: "dlycnt_b[2]", q: "dlycnt[2]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_5", d: "dlycnt_b[3]", q: "dlycnt[3]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_6", d: "dlycnt_b[4]", q: "dlycnt[4]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_7", d: "dlycnt_b[5]", q: "dlycnt[5]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_8", d: "dlycnt_b[6]", q: "dlycnt[6]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_9", d: "dlycnt_b[7]", q: "dlycnt[7]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_10", d: "dlycnt_b[8]", q: "dlycnt[8]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "reg0_syn_11", d: "dlycnt_b[9]", q: "dlycnt[9]" // ../../Src_al/IFOG501_2B.v(74)
SYN-5015 Similar messages will be suppressed.
SYN-5013 WARNING: Undriven net: model "IFOG501_2B" / net "dly_rst_syn_3" in ../../Src_al/IFOG501_2B.v(70)
SYN-5014 WARNING: the net's pin: pin "i" in ../../Src_al/IFOG501_2B.v(70)
SYN-5025 WARNING: Using 0 for all undriven pins and nets
SYN-1032 : 3963/150 useful/useless nets, 3384/221 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1032 : 3831/132 useful/useless nets, 3296/88 useful/useless insts
SYN-1017 : Remove 1 const input seq instances
SYN-1002 :     signal_process/rs422/reg11_syn_5
SYN-1018 : Transformed 59 mux instances.
SYN-1019 : Optimized 59 mux instances.
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 136 distributor mux.
SYN-1001 : Optimize 4 less-than instances
SYN-1016 : Merged 186 instances.
SYN-1015 : Optimize round 1, 1609 better
SYN-1014 : Optimize round 2
SYN-1044 : Optimized 2 inv instances.
SYN-1032 : 2844/16 useful/useless nets, 2314/268 useful/useless insts
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 2, 296 better
SYN-1032 : 2840/0 useful/useless nets, 2310/1 useful/useless insts
SYN-3003 : Optimized 2 equivalent DFF(s)
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3004 : Optimized 1 const0 DFF(s)
SYN-1032 : 2815/25 useful/useless nets, 2288/18 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1003 : finish command "optimize_rtl" in  1.259977s wall, 1.203125s user + 0.046875s system = 1.250000s CPU (99.2%)

RUN-1004 : used memory is 147 MB, reserved memory is 104 MB, peak memory is 149 MB
RUN-1002 : start command "report_area -file IFOG501_2B_rtl.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Gate Statistics
#Basic gates             1953
  #and                    374
  #nand                     0
  #or                     304
  #nor                      0
  #xor                     55
  #xnor                     0
  #buf                      0
  #not                     50
  #bufif1                   1
  #MX21                    37
  #FADD                     0
  #DFF                   1112
  #LATCH                   20
#MACRO_ADD                 29
#MACRO_EQ                  50
#MACRO_MULT                 2
#MACRO_MUX                189
#MACRO_OTHERS               2

Report Hierarchy Area:
+------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |gates  |seq    |macros |
+------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |821    |1132   |83     |
|  CLK120                    |global_clock                                     |0      |0      |0      |
|  signal_process            |SignalProcessing                                 |82     |987    |56     |
|    ctrl_signal             |SignalGenerator                                  |2      |22     |12     |
|    demodu                  |Demodulation                                     |33     |446    |16     |
|      fifo                  |Asys_fifo56X16                                   |27     |130    |7      |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |55     |1      |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |28     |0      |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |28     |0      |
|    integ                   |Integration                                      |1      |112    |2      |
|    modu                    |Modulation                                       |0      |131    |6      |
|    rs422                   |Rs422Output                                      |44     |256    |17     |
|    trans                   |SquareWaveGenerator                              |2      |20     |3      |
|  u_uart                    |UART_Control                                     |40     |54     |5      |
|    U0                      |speed_select_Tx                                  |2      |15     |3      |
|    U1                      |uart_tx                                          |13     |18     |2      |
|    U2                      |Ctrl_Data                                        |25     |21     |0      |
|  wendu                     |DS18B20                                          |698    |71     |19     |
+------------------------------------------------------------------------------------------------------+

RUN-1002 : start command "export_db IFOG501_2B_rtl.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 301 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 301 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.5 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "optimize_gate -maparea IFOG501_2B_gate.area"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param gate pack_seq_in_io on"
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
SYN-1032 : 2820/29 useful/useless nets, 2292/30 useful/useless insts
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2595 : bram inst: signal_process/demodu/fifo/ram_inst/ramread0_syn_6 is set to PDPW/SP from DP for resource saving
SYN-2593 : bram inst: signal_process/demodu/fifo/ram_inst/ramread0_syn_8 will be optimized to a new one with A-width 55 due to unused data out
SYN-2512 : LOGIC BRAM "signal_process/demodu/fifo/ram_inst/ramread0_syn_10"
SYN-2571 : Map 2 macro multiplier
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2893/45 useful/useless nets, 2298/2 useful/useless insts
SYN-2571 : Optimize after map_dsp, round 1, 47 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 39 control mux instances
SYN-1001 : Convert 1 adder
SYN-2501 : Optimize round 1
SYN-1032 : 3406/33 useful/useless nets, 2829/60 useful/useless insts
SYN-1016 : Merged 232 instances.
SYN-2501 : Optimize round 1, 771 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 30 macro adder
SYN-3001 : Mapper mapped 15 instances into 1 LUTs, name keeping = 100%.
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-2501 : Inferred 2 ROM instances
SYN-1019 : Optimized 50 mux instances.
SYN-1016 : Merged 40 instances.
SYN-1032 : 3873/17 useful/useless nets, 3296/1 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11924, tnet num: 3873, tinst num: 3295, tnode num: 14970, tedge num: 17069.
TMR-2508 : Levelizing timing graph completed, there are 113 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3873 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 374 (3.41), #lev = 7 (3.50)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 374 (3.41), #lev = 7 (3.50)
SYN-3001 : Logic optimization runtime opt =   0.06 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 1526 instances into 376 LUTs, name keeping = 62%.
RUN-1002 : start command "report_area -file IFOG501_2B_gate.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

LUT Statistics
#Total_luts               965
  #lut4                   311
  #lut5                    67
  #lut6                     0
  #lut5_mx41                0
  #lut4_alu1b             587

Utilization Statistics
#lut                      965   out of  19600    4.92%
#reg                     1074   out of  19600    5.48%
#le                         0
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%

Report Hierarchy Area:
+---------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |lut     |ripple  |seq     |bram    |dsp     |
+---------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |378     |587     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |138     |370     |930     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26      |0       |21      |0       |0       |
|    demodu                  |Demodulation                                     |59      |171     |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |42      |40      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0       |0       |55      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12      |0       |28      |0       |0       |
|    integ                   |Integration                                      |1       |56      |112     |0       |0       |
|    modu                    |Modulation                                       |2       |40      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |30      |103     |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |20      |0       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |117     |14      |53      |0       |0       |
|    U0                      |speed_select_Tx                                  |21      |14      |15      |0       |0       |
|    U1                      |uart_tx                                          |18      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |78      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |122     |84      |71      |0       |0       |
+---------------------------------------------------------------------------------------------------------------------------+

SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 1074 DFF/LATCH to SEQ ...
SYN-4009 : Pack 6 carry chain into lslice
SYN-4007 : Packing 292 adder to BLE ...
SYN-4008 : Packed 292 adder and 82 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -maparea IFOG501_2B_gate.area" in  1.066559s wall, 0.984375s user + 0.093750s system = 1.078125s CPU (101.1%)

RUN-1004 : used memory is 164 MB, reserved memory is 122 MB, peak memory is 194 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
RUN-1002 : start command "export_db IFOG501_2B_gate.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "backup_run_log run.log ../.logs/syn_1/td_20230907_152415.log"
