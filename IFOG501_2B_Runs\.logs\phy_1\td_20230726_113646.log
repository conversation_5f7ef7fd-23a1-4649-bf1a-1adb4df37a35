============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Jul 26 11:36:46 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 20 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1633 instances
RUN-0007 : 380 luts, 999 seqs, 133 mslices, 72 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2159 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1625 nets have 2 pins
RUN-1001 : 418 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     262     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1631 instances, 380 luts, 999 seqs, 205 slices, 25 macros(205 instances: 133 mslices 72 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7673, tnet num: 2157, tinst num: 1631, tnode num: 10867, tedge num: 12887.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.260244s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (102.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 582575
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1631.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 540212, overlap = 18
PHY-3002 : Step(2): len = 443741, overlap = 15.75
PHY-3002 : Step(3): len = 363698, overlap = 9
PHY-3002 : Step(4): len = 343886, overlap = 11.25
PHY-3002 : Step(5): len = 335269, overlap = 13.5
PHY-3002 : Step(6): len = 325042, overlap = 13.5
PHY-3002 : Step(7): len = 318805, overlap = 13.5
PHY-3002 : Step(8): len = 307755, overlap = 15.75
PHY-3002 : Step(9): len = 300007, overlap = 18
PHY-3002 : Step(10): len = 295058, overlap = 18
PHY-3002 : Step(11): len = 283193, overlap = 18
PHY-3002 : Step(12): len = 274257, overlap = 18
PHY-3002 : Step(13): len = 269842, overlap = 18
PHY-3002 : Step(14): len = 262024, overlap = 18
PHY-3002 : Step(15): len = 250294, overlap = 18
PHY-3002 : Step(16): len = 245253, overlap = 18
PHY-3002 : Step(17): len = 240520, overlap = 18
PHY-3002 : Step(18): len = 231210, overlap = 18
PHY-3002 : Step(19): len = 225846, overlap = 18
PHY-3002 : Step(20): len = 222487, overlap = 18
PHY-3002 : Step(21): len = 216606, overlap = 18
PHY-3002 : Step(22): len = 206996, overlap = 18
PHY-3002 : Step(23): len = 203649, overlap = 18
PHY-3002 : Step(24): len = 200248, overlap = 18
PHY-3002 : Step(25): len = 192716, overlap = 18
PHY-3002 : Step(26): len = 187455, overlap = 18
PHY-3002 : Step(27): len = 184886, overlap = 18
PHY-3002 : Step(28): len = 180269, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000165279
PHY-3002 : Step(29): len = 180678, overlap = 15.75
PHY-3002 : Step(30): len = 178400, overlap = 15.75
PHY-3002 : Step(31): len = 176061, overlap = 13.5
PHY-3002 : Step(32): len = 173639, overlap = 13.5
PHY-3002 : Step(33): len = 166136, overlap = 6.75
PHY-3002 : Step(34): len = 160487, overlap = 9
PHY-3002 : Step(35): len = 158940, overlap = 11.25
PHY-3002 : Step(36): len = 155330, overlap = 11.25
PHY-3002 : Step(37): len = 152754, overlap = 9.0625
PHY-3002 : Step(38): len = 148835, overlap = 11.5
PHY-3002 : Step(39): len = 146038, overlap = 11.3125
PHY-3002 : Step(40): len = 141301, overlap = 11.75
PHY-3002 : Step(41): len = 139804, overlap = 7
PHY-3002 : Step(42): len = 135998, overlap = 7.0625
PHY-3002 : Step(43): len = 133571, overlap = 7.1875
PHY-3002 : Step(44): len = 130660, overlap = 7.1875
PHY-3002 : Step(45): len = 130444, overlap = 7.1875
PHY-3002 : Step(46): len = 126932, overlap = 9.375
PHY-3002 : Step(47): len = 123866, overlap = 9.4375
PHY-3002 : Step(48): len = 121253, overlap = 9.375
PHY-3002 : Step(49): len = 120891, overlap = 9.4375
PHY-3002 : Step(50): len = 118120, overlap = 6.75
PHY-3002 : Step(51): len = 116791, overlap = 6.75
PHY-3002 : Step(52): len = 114801, overlap = 6.75
PHY-3002 : Step(53): len = 113835, overlap = 6.75
PHY-3002 : Step(54): len = 111793, overlap = 6.75
PHY-3002 : Step(55): len = 110812, overlap = 9
PHY-3002 : Step(56): len = 107751, overlap = 9
PHY-3002 : Step(57): len = 106382, overlap = 9
PHY-3002 : Step(58): len = 105149, overlap = 9
PHY-3002 : Step(59): len = 104307, overlap = 6.75
PHY-3002 : Step(60): len = 100587, overlap = 6.75
PHY-3002 : Step(61): len = 99409.5, overlap = 6.75
PHY-3002 : Step(62): len = 98046.5, overlap = 6.75
PHY-3002 : Step(63): len = 96638.4, overlap = 6.75
PHY-3002 : Step(64): len = 94899.2, overlap = 9
PHY-3002 : Step(65): len = 93584.4, overlap = 9
PHY-3002 : Step(66): len = 92154.1, overlap = 9
PHY-3002 : Step(67): len = 91472.7, overlap = 9
PHY-3002 : Step(68): len = 88797.3, overlap = 6.75
PHY-3002 : Step(69): len = 87797.2, overlap = 9
PHY-3002 : Step(70): len = 86339.8, overlap = 9
PHY-3002 : Step(71): len = 85553.3, overlap = 9
PHY-3002 : Step(72): len = 83883.8, overlap = 9
PHY-3002 : Step(73): len = 82929.3, overlap = 11.25
PHY-3002 : Step(74): len = 81199.8, overlap = 9
PHY-3002 : Step(75): len = 79164, overlap = 9
PHY-3002 : Step(76): len = 76902.5, overlap = 11.25
PHY-3002 : Step(77): len = 76709.1, overlap = 9
PHY-3002 : Step(78): len = 75131.9, overlap = 9
PHY-3002 : Step(79): len = 73293.9, overlap = 9
PHY-3002 : Step(80): len = 72090.6, overlap = 6.75
PHY-3002 : Step(81): len = 71270, overlap = 9
PHY-3002 : Step(82): len = 70382.6, overlap = 11.25
PHY-3002 : Step(83): len = 69455.9, overlap = 9
PHY-3002 : Step(84): len = 67747.2, overlap = 9
PHY-3002 : Step(85): len = 65928.3, overlap = 9
PHY-3002 : Step(86): len = 65360.8, overlap = 11.25
PHY-3002 : Step(87): len = 64920.8, overlap = 11.25
PHY-3002 : Step(88): len = 64470.2, overlap = 9
PHY-3002 : Step(89): len = 63890.1, overlap = 11.25
PHY-3002 : Step(90): len = 63202, overlap = 11.25
PHY-3002 : Step(91): len = 62443.3, overlap = 13.5
PHY-3002 : Step(92): len = 61843.7, overlap = 9
PHY-3002 : Step(93): len = 61109.4, overlap = 11.25
PHY-3002 : Step(94): len = 59339.7, overlap = 9
PHY-3002 : Step(95): len = 58275.9, overlap = 11.25
PHY-3002 : Step(96): len = 57960.7, overlap = 11.25
PHY-3002 : Step(97): len = 57681.5, overlap = 11.25
PHY-3002 : Step(98): len = 57053.7, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000330558
PHY-3002 : Step(99): len = 57247.4, overlap = 6.75
PHY-3002 : Step(100): len = 57302.7, overlap = 6.75
PHY-3002 : Step(101): len = 57158.6, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000661116
PHY-3002 : Step(102): len = 57056.7, overlap = 6.75
PHY-3002 : Step(103): len = 57000.7, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006544s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061906s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(104): len = 61601.9, overlap = 2.6875
PHY-3002 : Step(105): len = 60381.3, overlap = 2.5625
PHY-3002 : Step(106): len = 59381.2, overlap = 2.0625
PHY-3002 : Step(107): len = 59043.9, overlap = 2.0625
PHY-3002 : Step(108): len = 58458.7, overlap = 1.625
PHY-3002 : Step(109): len = 57859.8, overlap = 0.5
PHY-3002 : Step(110): len = 56943.8, overlap = 0.6875
PHY-3002 : Step(111): len = 56634.3, overlap = 0.625
PHY-3002 : Step(112): len = 55655.7, overlap = 1.125
PHY-3002 : Step(113): len = 54863.3, overlap = 1.4375
PHY-3002 : Step(114): len = 54786.4, overlap = 1.375
PHY-3002 : Step(115): len = 53575.9, overlap = 4.75
PHY-3002 : Step(116): len = 52737.5, overlap = 4.59375
PHY-3002 : Step(117): len = 52686, overlap = 3.875
PHY-3002 : Step(118): len = 51979.1, overlap = 4.4375
PHY-3002 : Step(119): len = 51116.8, overlap = 9.15625
PHY-3002 : Step(120): len = 50782.7, overlap = 9.5
PHY-3002 : Step(121): len = 49843.8, overlap = 9.53125
PHY-3002 : Step(122): len = 49356.4, overlap = 10.0312
PHY-3002 : Step(123): len = 49389.9, overlap = 9.9375
PHY-3002 : Step(124): len = 49141, overlap = 11
PHY-3002 : Step(125): len = 48444.9, overlap = 14.9375
PHY-3002 : Step(126): len = 48360.7, overlap = 15.1875
PHY-3002 : Step(127): len = 47895.2, overlap = 15.625
PHY-3002 : Step(128): len = 47607.8, overlap = 15.5625
PHY-3002 : Step(129): len = 47589.6, overlap = 15.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00575614
PHY-3002 : Step(130): len = 47523.4, overlap = 15.5312
PHY-3002 : Step(131): len = 47336.4, overlap = 15.5312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069302s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100459
PHY-3002 : Step(132): len = 48187.7, overlap = 46.7188
PHY-3002 : Step(133): len = 49380.5, overlap = 41.5625
PHY-3002 : Step(134): len = 49501.8, overlap = 39.0625
PHY-3002 : Step(135): len = 48925.5, overlap = 39.9375
PHY-3002 : Step(136): len = 48703.6, overlap = 39.7812
PHY-3002 : Step(137): len = 48262.3, overlap = 40.125
PHY-3002 : Step(138): len = 47913.1, overlap = 40
PHY-3002 : Step(139): len = 48018.6, overlap = 40.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000200919
PHY-3002 : Step(140): len = 48045.1, overlap = 39.6562
PHY-3002 : Step(141): len = 48520.5, overlap = 36.6875
PHY-3002 : Step(142): len = 48909.9, overlap = 35.125
PHY-3002 : Step(143): len = 49150.1, overlap = 34.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000401837
PHY-3002 : Step(144): len = 49139.7, overlap = 34.4688
PHY-3002 : Step(145): len = 49496.8, overlap = 31.5
PHY-3002 : Step(146): len = 49995.7, overlap = 26.9062
PHY-3002 : Step(147): len = 50560.2, overlap = 26.375
PHY-3002 : Step(148): len = 50671.3, overlap = 25.5625
PHY-3002 : Step(149): len = 50522.8, overlap = 25.5625
PHY-3002 : Step(150): len = 50569.1, overlap = 25.25
PHY-3002 : Step(151): len = 50480.9, overlap = 25.3438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7673, tnet num: 2157, tinst num: 1631, tnode num: 10867, tedge num: 12887.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 74.78 peak overflow 3.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2159.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53696, over cnt = 231(0%), over = 830, worst = 19
PHY-1001 : End global iterations;  0.054774s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (199.7%)

PHY-1001 : Congestion index: top1 = 39.07, top5 = 22.98, top10 = 15.42, top15 = 11.19.
PHY-1001 : End incremental global routing;  0.104509s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (164.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066226s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.4%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1592 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1633 instances, 380 luts, 1001 seqs, 205 slices, 25 macros(205 instances: 133 mslices 72 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 50577.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7681, tnet num: 2159, tinst num: 1633, tnode num: 10881, tedge num: 12899.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2159 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.292056s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(152): len = 50593.8, overlap = 1.25
PHY-3002 : Step(153): len = 50601.8, overlap = 1.375
PHY-3002 : Step(154): len = 50604.7, overlap = 1.3125
PHY-3002 : Step(155): len = 50604.7, overlap = 1.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2159 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058525s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00114135
PHY-3002 : Step(156): len = 50597, overlap = 25.3438
PHY-3002 : Step(157): len = 50603.2, overlap = 25.3438
PHY-3001 : Final: Len = 50603.2, Over = 25.3438
PHY-3001 : End incremental placement;  0.443977s wall, 0.437500s user + 0.093750s system = 0.531250s CPU (119.7%)

OPT-1001 : Total overflow 74.78 peak overflow 3.38
OPT-1001 : End high-fanout net optimization;  0.649840s wall, 0.671875s user + 0.140625s system = 0.812500s CPU (125.0%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1593/2161.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53856, over cnt = 231(0%), over = 825, worst = 18
PHY-1002 : len = 58976, over cnt = 138(0%), over = 255, worst = 9
PHY-1002 : len = 61048, over cnt = 25(0%), over = 30, worst = 3
PHY-1002 : len = 61280, over cnt = 17(0%), over = 21, worst = 3
PHY-1002 : len = 61696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.076816s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (122.0%)

PHY-1001 : Congestion index: top1 = 33.12, top5 = 23.01, top10 = 16.56, top15 = 12.44.
OPT-1001 : End congestion update;  0.120941s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2159 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056764s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.180505s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (103.9%)

OPT-1001 : Current memory(MB): used = 216, reserve = 182, peak = 219.
OPT-1001 : End physical optimization;  1.114430s wall, 1.125000s user + 0.156250s system = 1.281250s CPU (115.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 812 remaining SEQ's ...
SYN-4005 : Packed 113 SEQ with LUT/SLICE
SYN-4006 : 99 single LUT's are left
SYN-4006 : 699 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1079/1391 primitive instances ...
PHY-3001 : End packing;  0.051371s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 822 instances
RUN-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1987 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1459 nets have 2 pins
RUN-1001 : 411 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 820 instances, 773 slices, 25 macros(205 instances: 133 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50821, Over = 56.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6423, tnet num: 1985, tinst num: 820, tnode num: 8716, tedge num: 11222.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.294369s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.61299e-05
PHY-3002 : Step(158): len = 50186.1, overlap = 58.5
PHY-3002 : Step(159): len = 49935.8, overlap = 59.5
PHY-3002 : Step(160): len = 49562.4, overlap = 60.5
PHY-3002 : Step(161): len = 49472.3, overlap = 63.25
PHY-3002 : Step(162): len = 49519.3, overlap = 64
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.22597e-05
PHY-3002 : Step(163): len = 49512.7, overlap = 63.75
PHY-3002 : Step(164): len = 49906.8, overlap = 63.5
PHY-3002 : Step(165): len = 50411.9, overlap = 65
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000104519
PHY-3002 : Step(166): len = 50629, overlap = 60.75
PHY-3002 : Step(167): len = 51353.6, overlap = 58
PHY-3002 : Step(168): len = 52153.7, overlap = 56
PHY-3002 : Step(169): len = 52220.1, overlap = 54.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.067640s wall, 0.062500s user + 0.203125s system = 0.265625s CPU (392.7%)

PHY-3001 : Trial Legalized: Len = 65037.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047419s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000715762
PHY-3002 : Step(170): len = 61783, overlap = 9
PHY-3002 : Step(171): len = 59501.8, overlap = 15.25
PHY-3002 : Step(172): len = 58050.5, overlap = 19.75
PHY-3002 : Step(173): len = 57288.4, overlap = 22.25
PHY-3002 : Step(174): len = 56677.1, overlap = 27.25
PHY-3002 : Step(175): len = 56227.6, overlap = 29
PHY-3002 : Step(176): len = 55943.4, overlap = 29.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00143152
PHY-3002 : Step(177): len = 56249.2, overlap = 28.5
PHY-3002 : Step(178): len = 56412.1, overlap = 26.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00286305
PHY-3002 : Step(179): len = 56456, overlap = 26
PHY-3002 : Step(180): len = 56478.9, overlap = 26.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004741s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (329.6%)

PHY-3001 : Legalized: Len = 61035.8, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005328s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 61159.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6423, tnet num: 1985, tinst num: 820, tnode num: 8716, tedge num: 11222.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 137/1987.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68112, over cnt = 146(0%), over = 225, worst = 7
PHY-1002 : len = 68720, over cnt = 102(0%), over = 131, worst = 3
PHY-1002 : len = 69800, over cnt = 35(0%), over = 44, worst = 3
PHY-1002 : len = 70360, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 70496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128156s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (182.9%)

PHY-1001 : Congestion index: top1 = 30.73, top5 = 21.84, top10 = 17.51, top15 = 13.91.
PHY-1001 : End incremental global routing;  0.179109s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (165.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057554s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.266507s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (140.7%)

OPT-1001 : Current memory(MB): used = 220, reserve = 186, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1768/1987.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005399s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.73, top5 = 21.84, top10 = 17.51, top15 = 13.91.
OPT-1001 : End congestion update;  0.050916s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049036s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (127.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 782 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 820 instances, 773 slices, 25 macros(205 instances: 133 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 61179, Over = 0
PHY-3001 : End spreading;  0.005315s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (294.0%)

PHY-3001 : Final: Len = 61179, Over = 0
PHY-3001 : End incremental legalization;  0.033582s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.1%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.145625s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (107.3%)

OPT-1001 : Current memory(MB): used = 224, reserve = 190, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047776s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1765/1987.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007916s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (197.4%)

PHY-1001 : Congestion index: top1 = 30.82, top5 = 21.83, top10 = 17.52, top15 = 13.92.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049327s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.856284s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (113.1%)

RUN-1003 : finish command "place" in  5.674158s wall, 7.734375s user + 2.859375s system = 10.593750s CPU (186.7%)

RUN-1004 : used memory is 210 MB, reserved memory is 175 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 822 instances
RUN-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1987 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1459 nets have 2 pins
RUN-1001 : 411 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6423, tnet num: 1985, tinst num: 820, tnode num: 8716, tedge num: 11222.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66840, over cnt = 152(0%), over = 232, worst = 7
PHY-1002 : len = 67968, over cnt = 80(0%), over = 97, worst = 3
PHY-1002 : len = 69128, over cnt = 13(0%), over = 14, worst = 2
PHY-1002 : len = 69392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125477s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (99.6%)

PHY-1001 : Congestion index: top1 = 30.17, top5 = 21.51, top10 = 17.19, top15 = 13.70.
PHY-1001 : End global routing;  0.173249s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (108.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 203, peak = 243.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 467, peak = 497.
PHY-1001 : End build detailed router design. 3.153654s wall, 3.156250s user + 0.000000s system = 3.156250s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34728, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.227663s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 529, reserve = 501, peak = 529.
PHY-1001 : End phase 1; 1.233791s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 43% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179984, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End initial routed; 1.018984s wall, 2.015625s user + 0.125000s system = 2.140625s CPU (210.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1770(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.234   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.352766s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.9%)

PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End phase 2; 1.371850s wall, 2.375000s user + 0.125000s system = 2.500000s CPU (182.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179984, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014134s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (110.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179968, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.022498s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (138.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180000, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020668s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (75.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1770(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.234   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.359077s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.172041s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End phase 3; 0.715298s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (98.3%)

PHY-1003 : Routed, final wirelength = 180000
PHY-1001 : Current memory(MB): used = 547, reserve = 517, peak = 547.
PHY-1001 : End export database. 0.009480s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.665705s wall, 7.625000s user + 0.140625s system = 7.765625s CPU (116.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6423, tnet num: 1985, tinst num: 820, tnode num: 8716, tedge num: 11222.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.549650s wall, 8.500000s user + 0.156250s system = 8.656250s CPU (114.7%)

RUN-1004 : used memory is 518 MB, reserved memory is 488 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      793   out of  19600    4.05%
#reg                     1049   out of  19600    5.35%
#le                      1492
  #lut only               443   out of   1492   29.69%
  #reg only               699   out of   1492   46.85%
  #lut&reg                350   out of   1492   23.46%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         463
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         107
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    39
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1492   |588     |205     |1080    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1109   |303     |126     |895     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |17      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |545    |130     |58      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |0       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |15      |0       |26      |0       |0       |
|    integ                   |Integration                                      |138    |19      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |65     |23      |14      |61      |0       |1       |
|    rs422                   |Rs422Output                                      |308    |90      |29      |250     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |104    |93      |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |17     |13      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |52     |52      |0       |27      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1423  
    #2          2       289   
    #3          3       104   
    #4          4        18   
    #5        5-10       80   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6423, tnet num: 1985, tinst num: 820, tnode num: 8716, tedge num: 11222.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1985 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 820
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1987, pip num: 14488
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1256 valid insts, and 38241 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.937137s wall, 17.093750s user + 0.046875s system = 17.140625s CPU (583.6%)

RUN-1004 : used memory is 544 MB, reserved memory is 512 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230726_113646.log"
