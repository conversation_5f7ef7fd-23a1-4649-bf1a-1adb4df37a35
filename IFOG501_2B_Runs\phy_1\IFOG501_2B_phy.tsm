eagle_20
13 601 33 491 1338 15940 0 0
2.397 0.096 IFOG501_2B eagle_20 EG4X20BG256 Detail NA 9 2
clock: clk_in
15 0 0 0

clock: CLK120/pll_inst.clkc[0]
23 15940 1338 2
Setup check
33 3
Endpoint: cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31
33 2.397000 124 3
Timing path: cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk->cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk
cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31
35 2.397000 12.024000 9.627000 7 7
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/bus_reg[4] cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.d[0]
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_b[4] cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_120.d[1]
cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_65 cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_192.a[0]
cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_73 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_574.a[0]
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_n12 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.a[0]
cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_94 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.d[1]
cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_97 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0]

Timing path: cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk->cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk
cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31
81 2.590000 12.024000 9.434000 7 16
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/bus_reg[4] cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.d[0]
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_b[4] cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_112.a[1]
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_13 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_115.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_17 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_118.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_21 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_121.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_25 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_124.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_29 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_127.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_33 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_130.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_37 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_133.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_41 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_136.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_45 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_139.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_49 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_142.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_n15 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.b[1]
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/sel0_syn_32 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.b[0]
cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_94 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.d[1]
cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_97 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0]

Timing path: cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk->cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk
cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31
145 2.602000 12.024000 9.422000 7 16
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/bus_reg[4] cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.d[0]
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_b[4] cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_112.a[1]
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_13 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_115.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_17 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_118.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_21 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_121.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_25 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_124.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_29 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_127.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_33 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_130.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_37 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_133.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_41 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_136.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_45 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_139.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_49 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_142.fci
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_n14 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.d[1]
cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/sel0_syn_32 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.b[0]
cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_94 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.d[1]
cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_97 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0]


Endpoint: miso_syn_4
209 3.274000 78 3
Timing path: CtrlData/reg1_syn_56.clk->miso_syn_4
CtrlData/reg1_syn_56.clk
miso_syn_4
211 3.274000 12.132000 8.858000 6 7
CtrlData/Data_SPI[17] SPIM/cnt_sclk_neg_b3[3]_syn_5.d[1]
SPIM/cnt_sclk_neg_b3[3] SPIM/sub0_syn_75.a[0]
SPIM/sub0_syn_45 SPIM/sub0_syn_78.fci
SPIM/mosi_b[6] SPIM/reg4_syn_76.b[0]
SPIM/reg7_syn_87 SPIM/sclk_reg_syn_19.d[0]
SPIM/reg7_syn_89 SPIM/mux2_syn_133.d[0]
SPIM/mux2_syn_119 miso_syn_4.ce

Timing path: SPIM/reg4_syn_74.clk->miso_syn_4
SPIM/reg4_syn_74.clk
miso_syn_4
257 3.363000 12.132000 8.769000 6 8
SPIM/cnt_sclk_pos[1] SPIM/lt0_syn_37.a[0]
SPIM/lt0_syn_9 SPIM/lt0_syn_40.fci
SPIM/lt0_syn_13 SPIM/lt0_syn_43.fci
SPIM/mosi_n3 SPIM/reg1_syn_34.d[1]
SPIM/mosi_i_syn_3 SPIM/reg4_syn_76.c[0]
SPIM/reg7_syn_87 SPIM/sclk_reg_syn_19.d[0]
SPIM/reg7_syn_89 SPIM/mux2_syn_133.d[0]
SPIM/mux2_syn_119 miso_syn_4.ce

Timing path: SPIM/reg1_syn_38.clk->miso_syn_4
SPIM/reg1_syn_38.clk
miso_syn_4
305 3.373000 12.132000 8.759000 6 9
SPIM/cnt_sclk_pos[0] SPIM/lt0_syn_34.a[1]
SPIM/lt0_syn_5 SPIM/lt0_syn_37.fci
SPIM/lt0_syn_9 SPIM/lt0_syn_40.fci
SPIM/lt0_syn_13 SPIM/lt0_syn_43.fci
SPIM/mosi_n3 SPIM/reg1_syn_34.d[1]
SPIM/mosi_i_syn_3 SPIM/reg4_syn_76.c[0]
SPIM/reg7_syn_87 SPIM/sclk_reg_syn_19.d[0]
SPIM/reg7_syn_89 SPIM/mux2_syn_133.d[0]
SPIM/mux2_syn_119 miso_syn_4.ce


Endpoint: CtrlData/reg3_syn_61
355 3.588000 39 3
Timing path: CtrlData/reg3_syn_64.clk->CtrlData/reg3_syn_61
CtrlData/reg3_syn_64.clk
CtrlData/reg3_syn_61
357 3.588000 12.124000 8.536000 6 6
CtrlData/cnt_time[0] CtrlData/tx_state_b[1]_syn_40.a[1]
CtrlData/tx_state_b[1]_syn_4 CtrlData/tx_state_b[1]_syn_42.a[1]
CtrlData/tx_state_b[1]_syn_6 CtrlData/tx_state_b[1]_syn_38.a[1]
CtrlData/tx_state_b[1]_syn_10 CtrlData/tx_state_b[1]_syn_36.a[0]
CtrlData/sel2_syn_226 CtrlData/tx_state_b[1]_syn_38.a[0]
CtrlData/sel2_syn_228 CtrlData/reg3_syn_61.a[1]

Timing path: CtrlData/tx_state_b[1]_syn_42.clk->CtrlData/reg3_syn_61
CtrlData/tx_state_b[1]_syn_42.clk
CtrlData/reg3_syn_61
401 3.666000 12.124000 8.458000 6 6
CtrlData/cnt_time[2] CtrlData/tx_state_b[1]_syn_40.b[1]
CtrlData/tx_state_b[1]_syn_4 CtrlData/tx_state_b[1]_syn_42.a[1]
CtrlData/tx_state_b[1]_syn_6 CtrlData/tx_state_b[1]_syn_38.a[1]
CtrlData/tx_state_b[1]_syn_10 CtrlData/tx_state_b[1]_syn_36.a[0]
CtrlData/sel2_syn_226 CtrlData/tx_state_b[1]_syn_38.a[0]
CtrlData/sel2_syn_228 CtrlData/reg3_syn_61.a[1]

Timing path: CtrlData/reg3_syn_61.clk->CtrlData/reg3_syn_61
CtrlData/reg3_syn_61.clk
CtrlData/reg3_syn_61
445 3.690000 12.124000 8.434000 6 6
CtrlData/cnt_time[5] CtrlData/tx_state_b[1]_syn_40.c[1]
CtrlData/tx_state_b[1]_syn_4 CtrlData/tx_state_b[1]_syn_42.a[1]
CtrlData/tx_state_b[1]_syn_6 CtrlData/tx_state_b[1]_syn_38.a[1]
CtrlData/tx_state_b[1]_syn_10 CtrlData/tx_state_b[1]_syn_36.a[0]
CtrlData/sel2_syn_226 CtrlData/tx_state_b[1]_syn_38.a[0]
CtrlData/sel2_syn_228 CtrlData/reg3_syn_61.a[1]



Hold check
489 3
Endpoint: auto_chipwatcher_0_logicbram_syn_311
491 0.096000 1 1
Timing path: cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_91.clk->auto_chipwatcher_0_logicbram_syn_311
cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_91.clk
auto_chipwatcher_0_logicbram_syn_311
493 0.096000 2.183000 2.279000 0 1
cw_top_syn_41 auto_chipwatcher_0_logicbram_syn_311.wea


Endpoint: auto_chipwatcher_0_logicbram_syn_181
527 0.096000 1 1
Timing path: cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_85.clk->auto_chipwatcher_0_logicbram_syn_181
cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_85.clk
auto_chipwatcher_0_logicbram_syn_181
529 0.096000 2.183000 2.279000 0 1
cw_top_syn_45 auto_chipwatcher_0_logicbram_syn_181.wea


Endpoint: auto_chipwatcher_0_logicbram_syn_101
563 0.096000 1 1
Timing path: cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_75.clk->auto_chipwatcher_0_logicbram_syn_101
cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_75.clk
auto_chipwatcher_0_logicbram_syn_101
565 0.096000 2.183000 2.279000 0 1
cw_top_syn_50 auto_chipwatcher_0_logicbram_syn_101.wea





Timing group statistics: 
	Clock constraints: 
	  Clock Name                                  Min Period     Max Freq           Skew      Fanout            TNS
	  CLK120/pll_inst.clkc[0] (100.0MHz)             7.603ns     131.527MHz        0.480ns       298        0.000ns
	Minimum input arrival time before clock: no constraint path
	Maximum output required time after clock: no constraint path
	Maximum combinational path delay: no constraint path
Warning: No clock constraint on 1 clock net(s): 
	config_inst_syn_10

