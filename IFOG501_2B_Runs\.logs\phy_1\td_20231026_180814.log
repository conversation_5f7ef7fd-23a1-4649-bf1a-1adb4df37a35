============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 18:08:14 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1518 instances
RUN-0007 : 369 luts, 903 seqs, 122 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2045 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1529 nets have 2 pins
RUN-1001 : 404 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     251     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1516 instances, 369 luts, 903 seqs, 197 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7223, tnet num: 2043, tinst num: 1516, tnode num: 10149, tedge num: 12200.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.276396s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 537435
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1516.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 445319, overlap = 15.75
PHY-3002 : Step(2): len = 415220, overlap = 18
PHY-3002 : Step(3): len = 401932, overlap = 20.25
PHY-3002 : Step(4): len = 381555, overlap = 20.25
PHY-3002 : Step(5): len = 374620, overlap = 20.25
PHY-3002 : Step(6): len = 356623, overlap = 18
PHY-3002 : Step(7): len = 341117, overlap = 20.25
PHY-3002 : Step(8): len = 335326, overlap = 20.25
PHY-3002 : Step(9): len = 323880, overlap = 20.25
PHY-3002 : Step(10): len = 314731, overlap = 20.25
PHY-3002 : Step(11): len = 309367, overlap = 20.25
PHY-3002 : Step(12): len = 301088, overlap = 20.25
PHY-3002 : Step(13): len = 292814, overlap = 20.25
PHY-3002 : Step(14): len = 288546, overlap = 20.25
PHY-3002 : Step(15): len = 281411, overlap = 20.25
PHY-3002 : Step(16): len = 273921, overlap = 18
PHY-3002 : Step(17): len = 267411, overlap = 18
PHY-3002 : Step(18): len = 264394, overlap = 18
PHY-3002 : Step(19): len = 250736, overlap = 20.25
PHY-3002 : Step(20): len = 245482, overlap = 20.25
PHY-3002 : Step(21): len = 242218, overlap = 20.25
PHY-3002 : Step(22): len = 235521, overlap = 20.25
PHY-3002 : Step(23): len = 216905, overlap = 20.25
PHY-3002 : Step(24): len = 214094, overlap = 20.25
PHY-3002 : Step(25): len = 209134, overlap = 20.25
PHY-3002 : Step(26): len = 184340, overlap = 20.25
PHY-3002 : Step(27): len = 176950, overlap = 20.25
PHY-3002 : Step(28): len = 174838, overlap = 20.25
PHY-3002 : Step(29): len = 159912, overlap = 20.25
PHY-3002 : Step(30): len = 156709, overlap = 20.25
PHY-3002 : Step(31): len = 151948, overlap = 20.25
PHY-3002 : Step(32): len = 149011, overlap = 20.25
PHY-3002 : Step(33): len = 146301, overlap = 20.25
PHY-3002 : Step(34): len = 139679, overlap = 20.25
PHY-3002 : Step(35): len = 132864, overlap = 20.25
PHY-3002 : Step(36): len = 130994, overlap = 20.25
PHY-3002 : Step(37): len = 127231, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.4723e-05
PHY-3002 : Step(38): len = 127791, overlap = 13.5
PHY-3002 : Step(39): len = 126594, overlap = 13.5
PHY-3002 : Step(40): len = 125035, overlap = 15.75
PHY-3002 : Step(41): len = 123237, overlap = 15.75
PHY-3002 : Step(42): len = 118325, overlap = 13.5
PHY-3002 : Step(43): len = 111837, overlap = 13.5
PHY-3002 : Step(44): len = 110170, overlap = 13.5
PHY-3002 : Step(45): len = 109048, overlap = 13.5
PHY-3002 : Step(46): len = 106037, overlap = 11.25
PHY-3002 : Step(47): len = 103401, overlap = 13.5
PHY-3002 : Step(48): len = 101548, overlap = 13.5
PHY-3002 : Step(49): len = 99670.6, overlap = 13.5
PHY-3002 : Step(50): len = 94517.7, overlap = 18
PHY-3002 : Step(51): len = 93020.8, overlap = 15.75
PHY-3002 : Step(52): len = 90824, overlap = 15.75
PHY-3002 : Step(53): len = 88921.7, overlap = 13.5
PHY-3002 : Step(54): len = 88190.9, overlap = 13.5
PHY-3002 : Step(55): len = 87183, overlap = 15.75
PHY-3002 : Step(56): len = 85807.8, overlap = 15.75
PHY-3002 : Step(57): len = 84535, overlap = 13.5
PHY-3002 : Step(58): len = 81947.1, overlap = 13.5
PHY-3002 : Step(59): len = 79060.7, overlap = 13.5
PHY-3002 : Step(60): len = 78055.8, overlap = 13.5
PHY-3002 : Step(61): len = 77363.5, overlap = 13.5
PHY-3002 : Step(62): len = 75727.1, overlap = 13.5
PHY-3002 : Step(63): len = 74193.7, overlap = 13.625
PHY-3002 : Step(64): len = 72540.5, overlap = 13.625
PHY-3002 : Step(65): len = 69195.4, overlap = 14.5
PHY-3002 : Step(66): len = 68611.9, overlap = 14.75
PHY-3002 : Step(67): len = 67975.1, overlap = 12.5
PHY-3002 : Step(68): len = 67670.5, overlap = 12.625
PHY-3002 : Step(69): len = 67095.7, overlap = 17.0625
PHY-3002 : Step(70): len = 66316, overlap = 14.875
PHY-3002 : Step(71): len = 64564.7, overlap = 17.4375
PHY-3002 : Step(72): len = 63362.2, overlap = 19.625
PHY-3002 : Step(73): len = 62167.3, overlap = 19.6875
PHY-3002 : Step(74): len = 61911.8, overlap = 17.625
PHY-3002 : Step(75): len = 61280.4, overlap = 17.8125
PHY-3002 : Step(76): len = 60300.2, overlap = 17.9375
PHY-3002 : Step(77): len = 59787.9, overlap = 18.0625
PHY-3002 : Step(78): len = 58108.7, overlap = 16.125
PHY-3002 : Step(79): len = 57027.7, overlap = 18.5
PHY-3002 : Step(80): len = 57198.1, overlap = 16.375
PHY-3002 : Step(81): len = 57110.3, overlap = 16.375
PHY-3002 : Step(82): len = 56575.3, overlap = 16.375
PHY-3002 : Step(83): len = 56469.4, overlap = 16.1875
PHY-3002 : Step(84): len = 56382.5, overlap = 16.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000189446
PHY-3002 : Step(85): len = 56416.7, overlap = 16.1875
PHY-3002 : Step(86): len = 56405.1, overlap = 16.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000378892
PHY-3002 : Step(87): len = 56400.4, overlap = 18.4375
PHY-3002 : Step(88): len = 56431.8, overlap = 16.4375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007229s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (216.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057444s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000471608
PHY-3002 : Step(89): len = 60691.2, overlap = 15.0938
PHY-3002 : Step(90): len = 59863.3, overlap = 13.2188
PHY-3002 : Step(91): len = 59009, overlap = 11.0938
PHY-3002 : Step(92): len = 57635.4, overlap = 11.7188
PHY-3002 : Step(93): len = 56835.8, overlap = 11.6875
PHY-3002 : Step(94): len = 56075.4, overlap = 11.875
PHY-3002 : Step(95): len = 55051.6, overlap = 14.75
PHY-3002 : Step(96): len = 53692.6, overlap = 13.8125
PHY-3002 : Step(97): len = 52585.5, overlap = 16.3125
PHY-3002 : Step(98): len = 51681.1, overlap = 17
PHY-3002 : Step(99): len = 50661.5, overlap = 17.9688
PHY-3002 : Step(100): len = 50509.9, overlap = 17.75
PHY-3002 : Step(101): len = 50295.7, overlap = 16.4062
PHY-3002 : Step(102): len = 50423, overlap = 15.7812
PHY-3002 : Step(103): len = 49837.8, overlap = 15.9062
PHY-3002 : Step(104): len = 49219.7, overlap = 16.625
PHY-3002 : Step(105): len = 49009.6, overlap = 17.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000943215
PHY-3002 : Step(106): len = 48684.1, overlap = 17.6562
PHY-3002 : Step(107): len = 48670.6, overlap = 18.0312
PHY-3002 : Step(108): len = 48675.6, overlap = 18.0938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00188643
PHY-3002 : Step(109): len = 48700.5, overlap = 18.1875
PHY-3002 : Step(110): len = 48700.5, overlap = 18.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054077s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.60388e-05
PHY-3002 : Step(111): len = 49234.8, overlap = 50.8125
PHY-3002 : Step(112): len = 49969.9, overlap = 48.1875
PHY-3002 : Step(113): len = 50352.7, overlap = 47.9688
PHY-3002 : Step(114): len = 49694.2, overlap = 42.8125
PHY-3002 : Step(115): len = 49320.6, overlap = 43.125
PHY-3002 : Step(116): len = 49305.8, overlap = 43.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000132078
PHY-3002 : Step(117): len = 49713.7, overlap = 43.375
PHY-3002 : Step(118): len = 50047.5, overlap = 42.9062
PHY-3002 : Step(119): len = 50637.3, overlap = 39.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000264155
PHY-3002 : Step(120): len = 51139.2, overlap = 34.5938
PHY-3002 : Step(121): len = 51424.1, overlap = 34.375
PHY-3002 : Step(122): len = 52301.9, overlap = 33.5938
PHY-3002 : Step(123): len = 52045.5, overlap = 33.4375
PHY-3002 : Step(124): len = 51851.5, overlap = 33.3125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7223, tnet num: 2043, tinst num: 1516, tnode num: 10149, tedge num: 12200.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.25 peak overflow 2.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2045.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54392, over cnt = 243(0%), over = 1069, worst = 16
PHY-1001 : End global iterations;  0.079818s wall, 0.078125s user + 0.046875s system = 0.125000s CPU (156.6%)

PHY-1001 : Congestion index: top1 = 43.75, top5 = 25.71, top10 = 15.80, top15 = 11.11.
PHY-1001 : End incremental global routing;  0.128327s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (133.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061487s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.216930s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (122.4%)

OPT-1001 : Current memory(MB): used = 211, reserve = 174, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1563/2045.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54392, over cnt = 243(0%), over = 1069, worst = 16
PHY-1002 : len = 61520, over cnt = 169(0%), over = 448, worst = 13
PHY-1002 : len = 63200, over cnt = 110(0%), over = 257, worst = 13
PHY-1002 : len = 65928, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 66008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094873s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (115.3%)

PHY-1001 : Congestion index: top1 = 37.07, top5 = 25.26, top10 = 17.74, top15 = 12.85.
OPT-1001 : End congestion update;  0.137317s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053756s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.194535s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.4%)

OPT-1001 : Current memory(MB): used = 214, reserve = 177, peak = 214.
OPT-1001 : End physical optimization;  0.672544s wall, 0.671875s user + 0.078125s system = 0.750000s CPU (111.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 173 SEQ to BLE.
SYN-4003 : Packing 730 remaining SEQ's ...
SYN-4005 : Packed 107 SEQ with LUT/SLICE
SYN-4006 : 108 single LUT's are left
SYN-4006 : 623 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 992/1272 primitive instances ...
PHY-3001 : End packing;  0.045425s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 770 instances
RUN-1001 : 360 mslices, 361 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1880 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1363 nets have 2 pins
RUN-1001 : 405 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 768 instances, 721 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 51720.6, Over = 60.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6097, tnet num: 1878, tinst num: 768, tnode num: 8207, tedge num: 10710.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1878 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.293725s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.99268e-05
PHY-3002 : Step(125): len = 50656.4, overlap = 58.75
PHY-3002 : Step(126): len = 50186.4, overlap = 59.5
PHY-3002 : Step(127): len = 49926.4, overlap = 61
PHY-3002 : Step(128): len = 49937.1, overlap = 60.25
PHY-3002 : Step(129): len = 49854.7, overlap = 58.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.98536e-05
PHY-3002 : Step(130): len = 49987.2, overlap = 59.25
PHY-3002 : Step(131): len = 50314.4, overlap = 58
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.43102e-05
PHY-3002 : Step(132): len = 51583.2, overlap = 56.25
PHY-3002 : Step(133): len = 52412, overlap = 53
PHY-3002 : Step(134): len = 52827.9, overlap = 51.25
PHY-3002 : Step(135): len = 53180.2, overlap = 51.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.062362s wall, 0.031250s user + 0.140625s system = 0.171875s CPU (275.6%)

PHY-3001 : Trial Legalized: Len = 67622.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1878 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047984s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000468069
PHY-3002 : Step(136): len = 63434.4, overlap = 8.5
PHY-3002 : Step(137): len = 61353.3, overlap = 13.75
PHY-3002 : Step(138): len = 59135.1, overlap = 17.75
PHY-3002 : Step(139): len = 58147.1, overlap = 20.25
PHY-3002 : Step(140): len = 57567.4, overlap = 20.75
PHY-3002 : Step(141): len = 57223.3, overlap = 23.75
PHY-3002 : Step(142): len = 57127.8, overlap = 24.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000936139
PHY-3002 : Step(143): len = 57521.5, overlap = 24.5
PHY-3002 : Step(144): len = 57692.1, overlap = 24.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00187228
PHY-3002 : Step(145): len = 57928.2, overlap = 22.25
PHY-3002 : Step(146): len = 57928.2, overlap = 22.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004992s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (313.0%)

PHY-3001 : Legalized: Len = 62527.8, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005246s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 7 instances has been re-located, deltaX = 1, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 62563.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6097, tnet num: 1878, tinst num: 768, tnode num: 8207, tedge num: 10710.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 46/1880.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69280, over cnt = 147(0%), over = 224, worst = 7
PHY-1002 : len = 70256, over cnt = 77(0%), over = 90, worst = 3
PHY-1002 : len = 71056, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 71088, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 71280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.135887s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.5%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 22.13, top10 = 17.53, top15 = 13.72.
PHY-1001 : End incremental global routing;  0.184073s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1878 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053583s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.265226s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.2%)

OPT-1001 : Current memory(MB): used = 217, reserve = 180, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1658/1880.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005894s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 22.13, top10 = 17.53, top15 = 13.72.
OPT-1001 : End congestion update;  0.051019s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1878 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044320s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 730 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 768 instances, 721 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62579.6, Over = 0
PHY-3001 : End spreading;  0.004939s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62579.6, Over = 0
PHY-3001 : End incremental legalization;  0.033330s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.8%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.141852s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.1%)

OPT-1001 : Current memory(MB): used = 221, reserve = 183, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1878 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048600s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1650/1880.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71280, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015367s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.7%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 22.15, top10 = 17.55, top15 = 13.72.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1878 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044024s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (106.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.834935s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (101.1%)

RUN-1003 : finish command "place" in  5.176700s wall, 8.046875s user + 2.953125s system = 11.000000s CPU (212.5%)

RUN-1004 : used memory is 204 MB, reserved memory is 165 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 770 instances
RUN-1001 : 360 mslices, 361 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1880 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1363 nets have 2 pins
RUN-1001 : 405 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6097, tnet num: 1878, tinst num: 768, tnode num: 8207, tedge num: 10710.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 360 mslices, 361 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1878 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68640, over cnt = 138(0%), over = 214, worst = 7
PHY-1002 : len = 69752, over cnt = 75(0%), over = 88, worst = 3
PHY-1002 : len = 70736, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 70784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.126964s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (98.5%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 22.10, top10 = 17.46, top15 = 13.62.
PHY-1001 : End global routing;  0.173417s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (108.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 199, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 495, reserve = 462, peak = 495.
PHY-1001 : End build detailed router design. 3.198212s wall, 3.156250s user + 0.046875s system = 3.203125s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31528, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.287411s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (98.3%)

PHY-1001 : Current memory(MB): used = 527, reserve = 496, peak = 527.
PHY-1001 : End phase 1; 1.294155s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182512, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 530.
PHY-1001 : End initial routed; 1.356604s wall, 1.968750s user + 0.078125s system = 2.046875s CPU (150.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1668(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.359   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.458   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.358064s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 532, reserve = 498, peak = 532.
PHY-1001 : End phase 2; 1.714764s wall, 2.328125s user + 0.078125s system = 2.406250s CPU (140.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182512, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015138s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182424, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027619s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (226.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182440, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.018391s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1668(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.359   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.458   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.345233s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (99.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.167822s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (93.1%)

PHY-1001 : Current memory(MB): used = 546, reserve = 512, peak = 546.
PHY-1001 : End phase 3; 0.702272s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (104.6%)

PHY-1003 : Routed, final wirelength = 182440
PHY-1001 : Current memory(MB): used = 546, reserve = 513, peak = 546.
PHY-1001 : End export database. 0.011258s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.100559s wall, 7.671875s user + 0.140625s system = 7.812500s CPU (110.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6097, tnet num: 1878, tinst num: 768, tnode num: 8207, tedge num: 10710.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6139, tnet num: 1899, tinst num: 789, tnode num: 8249, tedge num: 10752.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.943321s wall, 2.906250s user + 0.156250s system = 3.062500s CPU (104.0%)

RUN-1003 : finish command "route" in  10.539160s wall, 11.031250s user + 0.328125s system = 11.359375s CPU (107.8%)

RUN-1004 : used memory is 527 MB, reserved memory is 494 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      812   out of  19600    4.14%
#reg                      957   out of  19600    4.88%
#le                      1435
  #lut only               478   out of   1435   33.31%
  #reg only               623   out of   1435   43.41%
  #lut&reg                334   out of   1435   23.28%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         419
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1435   |615     |197     |988     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1024   |303     |105     |803     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |21      |7       |22      |0       |0       |
|    demodu                  |Demodulation                                     |470    |134     |44      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |38      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |10      |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |10      |0       |15      |0       |0       |
|    integ                   |Integration                                      |138    |23      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |58     |16      |6       |56      |0       |1       |
|    rs422                   |Rs422Output                                      |306    |90      |29      |249     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |19      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |122    |108     |7       |61      |0       |0       |
|    U0                      |speed_select_Tx                                  |28     |21      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |69     |68      |0       |28      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1348  
    #2          2       271   
    #3          3       117   
    #4          4        17   
    #5        5-10       75   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6139, tnet num: 1899, tinst num: 789, tnode num: 8249, tedge num: 10752.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1899 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 789
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1901, pip num: 14288
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1321 valid insts, and 37603 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.013264s wall, 17.140625s user + 0.031250s system = 17.171875s CPU (569.9%)

RUN-1004 : used memory is 544 MB, reserved memory is 510 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_180814.log"
