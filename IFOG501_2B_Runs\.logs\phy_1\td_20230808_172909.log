============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Aug  8 17:29:09 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1611 instances
RUN-0007 : 376 luts, 972 seqs, 139 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2167 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1632 nets have 2 pins
RUN-1001 : 421 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 14 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1609 instances, 376 luts, 972 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7684, tnet num: 2165, tinst num: 1609, tnode num: 10871, tedge num: 12963.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.280822s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 605903
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1609.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 484159, overlap = 20.25
PHY-3002 : Step(2): len = 441999, overlap = 20.25
PHY-3002 : Step(3): len = 405807, overlap = 20.25
PHY-3002 : Step(4): len = 381914, overlap = 20.25
PHY-3002 : Step(5): len = 369603, overlap = 20.25
PHY-3002 : Step(6): len = 359919, overlap = 20.25
PHY-3002 : Step(7): len = 347855, overlap = 18
PHY-3002 : Step(8): len = 334474, overlap = 18
PHY-3002 : Step(9): len = 327027, overlap = 18
PHY-3002 : Step(10): len = 316288, overlap = 20.25
PHY-3002 : Step(11): len = 305826, overlap = 20.25
PHY-3002 : Step(12): len = 297477, overlap = 20.25
PHY-3002 : Step(13): len = 291517, overlap = 20.25
PHY-3002 : Step(14): len = 279845, overlap = 20.25
PHY-3002 : Step(15): len = 272571, overlap = 20.25
PHY-3002 : Step(16): len = 266812, overlap = 20.25
PHY-3002 : Step(17): len = 260640, overlap = 20.25
PHY-3002 : Step(18): len = 253322, overlap = 20.25
PHY-3002 : Step(19): len = 249270, overlap = 20.25
PHY-3002 : Step(20): len = 242552, overlap = 20.25
PHY-3002 : Step(21): len = 238393, overlap = 20.25
PHY-3002 : Step(22): len = 231951, overlap = 20.25
PHY-3002 : Step(23): len = 228345, overlap = 20.25
PHY-3002 : Step(24): len = 222292, overlap = 20.25
PHY-3002 : Step(25): len = 217717, overlap = 20.25
PHY-3002 : Step(26): len = 212837, overlap = 20.25
PHY-3002 : Step(27): len = 208488, overlap = 20.25
PHY-3002 : Step(28): len = 203564, overlap = 20.25
PHY-3002 : Step(29): len = 200305, overlap = 20.25
PHY-3002 : Step(30): len = 191407, overlap = 20.25
PHY-3002 : Step(31): len = 187639, overlap = 20.25
PHY-3002 : Step(32): len = 184629, overlap = 20.25
PHY-3002 : Step(33): len = 172450, overlap = 20.25
PHY-3002 : Step(34): len = 161787, overlap = 20.25
PHY-3002 : Step(35): len = 160756, overlap = 20.25
PHY-3002 : Step(36): len = 140404, overlap = 20.25
PHY-3002 : Step(37): len = 112439, overlap = 20.875
PHY-3002 : Step(38): len = 110452, overlap = 20.7812
PHY-3002 : Step(39): len = 107712, overlap = 20.7188
PHY-3002 : Step(40): len = 104792, overlap = 18.375
PHY-3002 : Step(41): len = 102821, overlap = 20.5625
PHY-3002 : Step(42): len = 101431, overlap = 20.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.71191e-05
PHY-3002 : Step(43): len = 102999, overlap = 18.1875
PHY-3002 : Step(44): len = 102327, overlap = 11.4375
PHY-3002 : Step(45): len = 101018, overlap = 16
PHY-3002 : Step(46): len = 100322, overlap = 13.75
PHY-3002 : Step(47): len = 98300.9, overlap = 16.125
PHY-3002 : Step(48): len = 96639.2, overlap = 15.75
PHY-3002 : Step(49): len = 96693.6, overlap = 11.25
PHY-3002 : Step(50): len = 94642.8, overlap = 11.25
PHY-3002 : Step(51): len = 90719.7, overlap = 11.25
PHY-3002 : Step(52): len = 89834.8, overlap = 9
PHY-3002 : Step(53): len = 88251.4, overlap = 11.25
PHY-3002 : Step(54): len = 87179.7, overlap = 11.25
PHY-3002 : Step(55): len = 86070.5, overlap = 11.25
PHY-3002 : Step(56): len = 85260.4, overlap = 9
PHY-3002 : Step(57): len = 84260.7, overlap = 11.25
PHY-3002 : Step(58): len = 83163.7, overlap = 9
PHY-3002 : Step(59): len = 81938.1, overlap = 13.5
PHY-3002 : Step(60): len = 80142.8, overlap = 13.5
PHY-3002 : Step(61): len = 79771.3, overlap = 13.5
PHY-3002 : Step(62): len = 78939.2, overlap = 11.25
PHY-3002 : Step(63): len = 78353.6, overlap = 9
PHY-3002 : Step(64): len = 75907, overlap = 11.25
PHY-3002 : Step(65): len = 75073.8, overlap = 13.5
PHY-3002 : Step(66): len = 74088.1, overlap = 15.75
PHY-3002 : Step(67): len = 71667.8, overlap = 11.25
PHY-3002 : Step(68): len = 69385.5, overlap = 11.25
PHY-3002 : Step(69): len = 68566.3, overlap = 11.25
PHY-3002 : Step(70): len = 66766.8, overlap = 11.25
PHY-3002 : Step(71): len = 66619.8, overlap = 11.25
PHY-3002 : Step(72): len = 66293, overlap = 9
PHY-3002 : Step(73): len = 65503.1, overlap = 9
PHY-3002 : Step(74): len = 62672.4, overlap = 11.25
PHY-3002 : Step(75): len = 61948.6, overlap = 11.25
PHY-3002 : Step(76): len = 61755.8, overlap = 11.25
PHY-3002 : Step(77): len = 61714.2, overlap = 13.5
PHY-3002 : Step(78): len = 61274.4, overlap = 11.25
PHY-3002 : Step(79): len = 60407.3, overlap = 9
PHY-3002 : Step(80): len = 60028.2, overlap = 13.5
PHY-3002 : Step(81): len = 59956.5, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000174238
PHY-3002 : Step(82): len = 60119, overlap = 13.5
PHY-3002 : Step(83): len = 60187, overlap = 11.25
PHY-3002 : Step(84): len = 60222.3, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000348476
PHY-3002 : Step(85): len = 60165.8, overlap = 11.25
PHY-3002 : Step(86): len = 60347.4, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006447s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065953s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(87): len = 62887.7, overlap = 5.96875
PHY-3002 : Step(88): len = 61671.9, overlap = 5.46875
PHY-3002 : Step(89): len = 60780.7, overlap = 4.125
PHY-3002 : Step(90): len = 59318.5, overlap = 3.5
PHY-3002 : Step(91): len = 58095, overlap = 3.8125
PHY-3002 : Step(92): len = 56792.5, overlap = 3.8125
PHY-3002 : Step(93): len = 55513.4, overlap = 4.625
PHY-3002 : Step(94): len = 54588.2, overlap = 4.6875
PHY-3002 : Step(95): len = 52571.6, overlap = 10.9688
PHY-3002 : Step(96): len = 50934.7, overlap = 15.9688
PHY-3002 : Step(97): len = 50427.7, overlap = 15.7812
PHY-3002 : Step(98): len = 49749.7, overlap = 16.0312
PHY-3002 : Step(99): len = 49350.2, overlap = 15.0312
PHY-3002 : Step(100): len = 48877.6, overlap = 14.7812
PHY-3002 : Step(101): len = 48725.4, overlap = 14.3125
PHY-3002 : Step(102): len = 48365.4, overlap = 14.4375
PHY-3002 : Step(103): len = 48352.2, overlap = 14.4375
PHY-3002 : Step(104): len = 48196.5, overlap = 14.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000746459
PHY-3002 : Step(105): len = 47910, overlap = 15.25
PHY-3002 : Step(106): len = 47928.7, overlap = 15.25
PHY-3002 : Step(107): len = 47953.9, overlap = 15.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00149292
PHY-3002 : Step(108): len = 47970.2, overlap = 15.1875
PHY-3002 : Step(109): len = 47970.2, overlap = 15.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061031s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00013329
PHY-3002 : Step(110): len = 48778.9, overlap = 56.375
PHY-3002 : Step(111): len = 49306.9, overlap = 56.1875
PHY-3002 : Step(112): len = 49427.1, overlap = 55.5625
PHY-3002 : Step(113): len = 49577.5, overlap = 54.625
PHY-3002 : Step(114): len = 49771.6, overlap = 50.1875
PHY-3002 : Step(115): len = 49684.5, overlap = 49.125
PHY-3002 : Step(116): len = 49560.6, overlap = 41.6875
PHY-3002 : Step(117): len = 49598.1, overlap = 37.125
PHY-3002 : Step(118): len = 49506.2, overlap = 35.5
PHY-3002 : Step(119): len = 49388.8, overlap = 36.375
PHY-3002 : Step(120): len = 49366, overlap = 35.9062
PHY-3002 : Step(121): len = 49088.6, overlap = 35.9062
PHY-3002 : Step(122): len = 49021.6, overlap = 36.1562
PHY-3002 : Step(123): len = 49030, overlap = 36.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00026658
PHY-3002 : Step(124): len = 49055.2, overlap = 35.5625
PHY-3002 : Step(125): len = 49313.7, overlap = 35.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000533159
PHY-3002 : Step(126): len = 49537.8, overlap = 34.3438
PHY-3002 : Step(127): len = 49735.2, overlap = 33.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7684, tnet num: 2165, tinst num: 1609, tnode num: 10871, tedge num: 12963.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 91.12 peak overflow 4.34
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2167.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53560, over cnt = 246(0%), over = 1016, worst = 22
PHY-1001 : End global iterations;  0.059439s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (157.7%)

PHY-1001 : Congestion index: top1 = 43.08, top5 = 25.03, top10 = 15.87, top15 = 11.18.
PHY-1001 : End incremental global routing;  0.113102s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (124.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2165 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071986s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1570 has valid locations, 4 needs to be replaced
PHY-3001 : design contains 1612 instances, 376 luts, 975 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 50026.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7696, tnet num: 2168, tinst num: 1612, tnode num: 10892, tedge num: 12981.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2168 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.304046s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(128): len = 50049.1, overlap = 4.71875
PHY-3002 : Step(129): len = 50075.7, overlap = 4.71875
PHY-3002 : Step(130): len = 50107, overlap = 4.90625
PHY-3002 : Step(131): len = 50104.1, overlap = 4.96875
PHY-3002 : Step(132): len = 50104.1, overlap = 4.96875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2168 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060012s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000551519
PHY-3002 : Step(133): len = 50104.1, overlap = 34.0625
PHY-3002 : Step(134): len = 50104.1, overlap = 34.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00110304
PHY-3002 : Step(135): len = 50140.6, overlap = 33.8125
PHY-3002 : Step(136): len = 50187.3, overlap = 33.8125
PHY-3001 : Final: Len = 50187.3, Over = 33.8125
PHY-3001 : End incremental placement;  0.492053s wall, 0.500000s user + 0.156250s system = 0.656250s CPU (133.4%)

OPT-1001 : Total overflow 91.38 peak overflow 4.34
OPT-1001 : End high-fanout net optimization;  0.713315s wall, 0.734375s user + 0.156250s system = 0.890625s CPU (124.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1655/2170.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53816, over cnt = 247(0%), over = 1010, worst = 22
PHY-1002 : len = 60672, over cnt = 172(0%), over = 360, worst = 15
PHY-1002 : len = 64440, over cnt = 54(0%), over = 80, worst = 8
PHY-1002 : len = 65464, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 65848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.091498s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (136.6%)

PHY-1001 : Congestion index: top1 = 38.43, top5 = 25.39, top10 = 17.90, top15 = 13.03.
OPT-1001 : End congestion update;  0.135768s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (126.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2168 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064514s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.203045s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.4%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 220.
OPT-1001 : End physical optimization;  1.199146s wall, 1.250000s user + 0.187500s system = 1.437500s CPU (119.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 793 remaining SEQ's ...
SYN-4005 : Packed 99 SEQ with LUT/SLICE
SYN-4006 : 115 single LUT's are left
SYN-4006 : 694 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1391 primitive instances ...
PHY-3001 : End packing;  0.052376s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 824 instances
RUN-1001 : 387 mslices, 388 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2003 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1475 nets have 2 pins
RUN-1001 : 413 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 822 instances, 775 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50208.4, Over = 61.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6467, tnet num: 2001, tinst num: 822, tnode num: 8771, tedge num: 11337.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.319245s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (102.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.5923e-05
PHY-3002 : Step(137): len = 49872.3, overlap = 61
PHY-3002 : Step(138): len = 49667.3, overlap = 62.75
PHY-3002 : Step(139): len = 49443.8, overlap = 64
PHY-3002 : Step(140): len = 49451.5, overlap = 62.5
PHY-3002 : Step(141): len = 49530, overlap = 61.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.18461e-05
PHY-3002 : Step(142): len = 49620.7, overlap = 62
PHY-3002 : Step(143): len = 50172.9, overlap = 60.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000100997
PHY-3002 : Step(144): len = 50791.6, overlap = 57.25
PHY-3002 : Step(145): len = 53002.1, overlap = 52.25
PHY-3002 : Step(146): len = 53458.8, overlap = 51
PHY-3002 : Step(147): len = 53492.9, overlap = 49
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.092570s wall, 0.062500s user + 0.125000s system = 0.187500s CPU (202.5%)

PHY-3001 : Trial Legalized: Len = 67094.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049759s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000625815
PHY-3002 : Step(148): len = 64025, overlap = 8.5
PHY-3002 : Step(149): len = 61676, overlap = 11.25
PHY-3002 : Step(150): len = 60010, overlap = 14.25
PHY-3002 : Step(151): len = 58809.6, overlap = 17.5
PHY-3002 : Step(152): len = 58212, overlap = 20.25
PHY-3002 : Step(153): len = 57766.3, overlap = 22.25
PHY-3002 : Step(154): len = 57435.1, overlap = 24.25
PHY-3002 : Step(155): len = 57196.9, overlap = 26
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00125163
PHY-3002 : Step(156): len = 57569.2, overlap = 24
PHY-3002 : Step(157): len = 57661.1, overlap = 23.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00250326
PHY-3002 : Step(158): len = 57809.6, overlap = 23
PHY-3002 : Step(159): len = 57868.9, overlap = 22.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005268s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63192.3, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005561s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (281.0%)

PHY-3001 : 10 instances has been re-located, deltaX = 3, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 63352.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6467, tnet num: 2001, tinst num: 822, tnode num: 8771, tedge num: 11337.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 106/2003.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69552, over cnt = 137(0%), over = 204, worst = 7
PHY-1002 : len = 70416, over cnt = 78(0%), over = 96, worst = 4
PHY-1002 : len = 71304, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 71504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122905s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (165.3%)

PHY-1001 : Congestion index: top1 = 30.67, top5 = 22.21, top10 = 17.54, top15 = 14.02.
PHY-1001 : End incremental global routing;  0.177727s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (140.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060668s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.269454s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (127.6%)

OPT-1001 : Current memory(MB): used = 220, reserve = 186, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1772/2003.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006777s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (230.6%)

PHY-1001 : Congestion index: top1 = 30.67, top5 = 22.21, top10 = 17.54, top15 = 14.02.
OPT-1001 : End congestion update;  0.053855s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051625s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 784 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 822 instances, 775 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63376.4, Over = 0
PHY-3001 : End spreading;  0.005411s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (288.8%)

PHY-3001 : Final: Len = 63376.4, Over = 0
PHY-3001 : End incremental legalization;  0.038726s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (121.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.157396s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.3%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051252s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1769/2003.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008347s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.67, top5 = 22.21, top10 = 17.53, top15 = 14.03.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052392s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.887743s wall, 0.937500s user + 0.031250s system = 0.968750s CPU (109.1%)

RUN-1003 : finish command "place" in  5.981428s wall, 8.828125s user + 2.968750s system = 11.796875s CPU (197.2%)

RUN-1004 : used memory is 204 MB, reserved memory is 168 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 824 instances
RUN-1001 : 387 mslices, 388 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2003 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1475 nets have 2 pins
RUN-1001 : 413 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6467, tnet num: 2001, tinst num: 822, tnode num: 8771, tedge num: 11337.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 387 mslices, 388 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68856, over cnt = 140(0%), over = 210, worst = 7
PHY-1002 : len = 69864, over cnt = 79(0%), over = 102, worst = 5
PHY-1002 : len = 71048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107889s wall, 0.156250s user + 0.062500s system = 0.218750s CPU (202.8%)

PHY-1001 : Congestion index: top1 = 30.69, top5 = 22.07, top10 = 17.39, top15 = 13.90.
PHY-1001 : End global routing;  0.159732s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (166.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 467, peak = 499.
PHY-1001 : End build detailed router design. 3.267940s wall, 3.187500s user + 0.078125s system = 3.265625s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32928, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.396762s wall, 1.375000s user + 0.031250s system = 1.406250s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 533.
PHY-1001 : End phase 1; 1.402759s wall, 1.375000s user + 0.031250s system = 1.406250s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179544, over cnt = 16(0%), over = 16, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End initial routed; 1.157186s wall, 2.093750s user + 0.171875s system = 2.265625s CPU (195.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1777(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.311   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.375838s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (103.9%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.533112s wall, 2.484375s user + 0.171875s system = 2.656250s CPU (173.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179544, over cnt = 16(0%), over = 16, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018309s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179584, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025324s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (61.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179616, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020975s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1777(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.311   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.377121s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.178754s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.9%)

PHY-1001 : Current memory(MB): used = 548, reserve = 517, peak = 548.
PHY-1001 : End phase 3; 0.752689s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 179616
PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End export database. 0.011028s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (141.7%)

PHY-1001 : End detail routing;  7.155410s wall, 8.000000s user + 0.281250s system = 8.281250s CPU (115.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6467, tnet num: 2001, tinst num: 822, tnode num: 8771, tedge num: 11337.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.049687s wall, 8.937500s user + 0.343750s system = 9.281250s CPU (115.3%)

RUN-1004 : used memory is 503 MB, reserved memory is 473 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      807   out of  19600    4.12%
#reg                     1050   out of  19600    5.36%
#le                      1501
  #lut only               451   out of   1501   30.05%
  #reg only               694   out of   1501   46.24%
  #lut&reg                356   out of   1501   23.72%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         459
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1501   |593     |214     |1081    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1094   |292     |121     |895     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |30     |24      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |518    |117     |53      |429     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |14      |0       |27      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |60     |25      |14      |56      |0       |1       |
|    rs422                   |Rs422Output                                      |319    |86      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |109    |89      |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |30     |21      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |24     |19      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |49      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1439  
    #2          2       284   
    #3          3       109   
    #4          4        20   
    #5        5-10       79   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6467, tnet num: 2001, tinst num: 822, tnode num: 8771, tedge num: 11337.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2001 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 822
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2003, pip num: 14572
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1325 valid insts, and 38465 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.369366s wall, 18.906250s user + 0.093750s system = 19.000000s CPU (563.9%)

RUN-1004 : used memory is 544 MB, reserved memory is 511 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230808_172909.log"
