============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 11:37:53 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(93)
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(103)
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(93)
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(103)
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "elaborate -top IFOG501_2B"
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(93)
HDL-5007 WARNING: 'clk' is not declared in ../../Src_al/IFOG501_2B.v(103)
HDL-1007 : elaborate module IFOG501_2B in ../../Src_al/IFOG501_2B.v(17)
HDL-5007 WARNING: latch inferred for net 'ARM_INT' in ../../Src_al/IFOG501_2B.v(103)
HDL-1007 : elaborate module global_clock in ../../al_ip/global_clock.v(22)
HDL-1007 : elaborate module EG_PHY_PLL(FIN="20.000",FBCLK_DIV=50,CLKC0_DIV=10,CLKC0_ENABLE="ENABLE",FEEDBK_MODE="NOCOMP",STDBY_ENABLE="DISABLE",CLKC0_CPHASE=9,GMC_GAIN=2,ICP_CURRENT=9,KVCO=2,LPF_CAPACITOR=1,LPF_RESISTOR=8,SYNC_ENABLE="DISABLE") in C:/Anlogic/TD5.6.2/arch/eagle_macro.v(930)
HDL-1007 : elaborate module SPI_MASTER(CLK_FREQ=100000000) in ../../Src_al/SPI_MASTER.v(17)
HDL-1007 : elaborate module CtrlData in ../../Src_al/CtrlData.v(17)
HDL-1007 : elaborate module Time_1ms in ../../Src_al/Time_1ms.v(17)
HDL-5007 WARNING: net 'reg_ms' does not have a driver in ../../Src_al/CtrlData.v(40)
HDL-1007 : elaborate module DS18B20 in ../../Src_al/DS18B20.v(16)
HDL-1007 : elaborate module UART_Control(RX_SYS_FREQ=100000000,TX_SYS_FREQ=100000000) in ../../Src_al/UART_Control.v(16)
HDL-1007 : elaborate module speed_select_Tx(SYS_FREQ=100000000) in ../../Src_al/speed_select_Tx.v(16)
HDL-1007 : elaborate module uart_tx in ../../Src_al/uart_tx.v(15)
HDL-1007 : elaborate module Ctrl_Data in ../../Src_al/Ctrl_Data.v(17)
HDL-1200 : Current top model is IFOG501_2B
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "export_db IFOG501_2B_elaborate.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "read_adc ../../Constraints/IFOG_11FB.adc"
RUN-1002 : start command "set_pin_assignment  RXD          LOCATION = L16; IOSTANDARD = LVCMOS33; PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  TXD          LOCATION = K16; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  dq           LOCATION = B16; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  miso         LOCATION = N1; IOSTANDARD = LVCMOS33; PULLTYPE = PULLUP; "
RUN-1002 : start command "set_pin_assignment  cs_dacc      LOCATION = M1; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  sclk         LOCATION = P1; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; "
RUN-1002 : start command "set_pin_assignment  mosi         LOCATION = R1; IOSTANDARD = LVCMOS33; DRIVESTRENGTH = 8; PULLTYPE = NONE; "
RUN-1001 : Starting of IO setups legality check.
RUN-1001 : Starting of IO setups legality check.
RUN-1001 : Starting of IO vref setups legality check.
USR-6010 WARNING: ADC constraints: top model pin clk_in has no constraint.
RUN-1002 : start command "optimize_rtl"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "global_clock"
SYN-1012 : SanityCheck: Model "CtrlData"
SYN-1012 : SanityCheck: Model "Time_1ms"
SYN-1012 : SanityCheck: Model "SPI_MASTER(CLK_FREQ=100000000)"
SYN-1012 : SanityCheck: Model "UART_Control(RX_SYS_FREQ=100000000,TX_SYS_FREQ=100000000)"
SYN-1012 : SanityCheck: Model "speed_select_Tx(SYS_FREQ=100000000)"
SYN-1012 : SanityCheck: Model "uart_tx"
SYN-1012 : SanityCheck: Model "Ctrl_Data"
SYN-1012 : SanityCheck: Model "DS18B20"
SYN-1043 : Mark DS18B20 as IO macro for instance dq_i
SYN-1043 : Mark global_clock as IO macro for instance pll_inst
SYN-1011 : Flatten model IFOG501_2B
SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (21) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[19]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[19]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[19]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (20) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[18]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[18]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[18]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (19) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[17]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[17]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[17]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (18) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[16]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[16]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[16]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (17) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[15]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[15]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[15]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (16) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[14]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[14]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[14]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (15) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[13]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[13]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[13]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (14) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[12]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[12]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[12]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (13) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[11]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[11]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[11]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 CRITICAL-WARNING: The model (IFOG501_2B) has (12) unstable comb insts loops, print one loop:
		<Inst: add0> <Inpin: add0.i0[10]> <Outpin: add0.o[19]>   // ../../Src_al/IFOG501_2B.v(99)
		<Net: count_b1[19]>   
		<Inst: mux0> <Inpin: mux0.i0[19]> <Outpin: mux0.o[19]>   // ../../Src_al/IFOG501_2B.v(96)
		<Net: count_b[19]>   
		<Inst: mux1> <Inpin: mux1.i0[19]> <Outpin: mux1.o[10]>   // ../../Src_al/IFOG501_2B.v(94)
		<Net: count[10]>   // ../../Src_al/IFOG501_2B.v(61)

SYN-5033 Similar messages will be suppressed.
SYN-1032 : 1949/496 useful/useless nets, 689/58 useful/useless insts
SYN-1001 : Bypass 1 mux instances
SYN-1016 : Merged 7129 instances.
SYN-1032 : 2462/709 useful/useless nets, 1410/385 useful/useless insts
SYN-1016 : Merged 144 instances.
SYN-5015 WARNING: Found latch in "IFOG501_2B", name: "ARM_INT_reg", d: "_al_nx", q: "CtrlData/ARM_INT" // ../../Src_al/IFOG501_2B.v(103)
SYN-5011 WARNING: Undriven pin: model "IFOG501_2B" / inst "CtrlData/reg0_syn_10" in ../../Src_al/CtrlData.v(262) / pin "d"
SYN-5013 WARNING: Undriven net: model "IFOG501_2B" / net "CtrlData/reg_ms" in ../../Src_al/CtrlData.v(40)
SYN-5014 WARNING: the net's pin: pin "I" in ../../Src_al/CtrlData.v(118)
SYN-5025 WARNING: Using 0 for all undriven pins and nets
SYN-1032 : 2303/42 useful/useless nets, 2150/140 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1017 : Remove 1 const input seq instances
SYN-1002 :     SPIM/reg0_syn_2
SYN-1018 : Transformed 59 mux instances.
SYN-1019 : Optimized 67 mux instances.
SYN-1021 : Optimized 36 onehot mux instances.
SYN-1020 : Optimized 126 distributor mux.
SYN-1001 : Optimize 2 less-than instances
SYN-1019 : Optimized 6 mux instances.
SYN-1016 : Merged 152 instances.
SYN-1015 : Optimize round 1, 678 better
SYN-1014 : Optimize round 2
SYN-1044 : Optimized 2 inv instances.
SYN-1032 : 1831/9 useful/useless nets, 1681/143 useful/useless insts
SYN-1019 : Optimized 8 mux instances.
SYN-1015 : Optimize round 2, 167 better
SYN-1032 : 1827/1 useful/useless nets, 1677/2 useful/useless insts
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3003 : Optimized 1 equivalent DFF(s)
SYN-3004 : Optimized 1 const0 DFF(s)
SYN-1032 : 1806/21 useful/useless nets, 1656/17 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 2 better
SYN-1014 : Optimize round 2
SYN-1015 : Optimize round 2, 0 better
RUN-1002 : start command "report_area -file IFOG501_2B_rtl.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Gate Statistics
#Basic gates             1208
  #and                    445
  #nand                     0
  #or                     321
  #nor                      0
  #xor                     39
  #xnor                     0
  #buf                      0
  #not                     35
  #bufif1                   1
  #MX21                    41
  #FADD                     0
  #DFF                    325
  #LATCH                    1
#MACRO_ADD                 17
#MACRO_EQ                  42
#MACRO_MUX                226

Report Hierarchy Area:
+----------------------------------------------------+
|Instance   |Module          |gates  |seq    |macros |
+----------------------------------------------------+
|top        |IFOG501_2B      |882    |326    |59     |
|  CLK120   |global_clock    |0      |0      |0      |
|  CtrlData |CtrlData        |30     |65     |14     |
|    usms   |Time_1ms        |4      |18     |2      |
|  SPIM     |SPI_MASTER      |106    |131    |17     |
|  uart     |UART_Control    |45     |54     |5      |
|    U0     |speed_select_Tx |2      |15     |3      |
|    U1     |uart_tx         |10     |18     |2      |
|    U2     |Ctrl_Data       |33     |21     |0      |
|  wendu    |DS18B20         |701    |75     |20     |
+----------------------------------------------------+

RUN-1002 : start command "export_db IFOG501_2B_rtl.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "optimize_gate -maparea IFOG501_2B_gate.area"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param gate pack_seq_in_io on"
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-1062 : Register "CtrlData/ARM_INT" in ../../Src_al/CtrlData.v(22) is described with both an asynchronous reset and an asynchronous set. Implement the asynchronous set logic of current register.
SYN-1016 : Merged 1 instances.
SYN-2001 : Map 8 IOs to PADs
SYN-1032 : 1815/3 useful/useless nets, 1663/5 useful/useless insts
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2571 : Optimize after map_dsp, round 1
SYN-2571 : Optimize after map_dsp, round 1, 0 better
SYN-1001 : Throwback 58 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 116 instances.
SYN-2501 : Optimize round 1, 480 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 17 macro adder
SYN-3001 : Mapper mapped 15 instances into 1 LUTs, name keeping = 100%.
SYN-3001 : Mapper mapped 7 instances into 1 LUTs, name keeping = 100%.
SYN-2501 : Inferred 2 ROM instances
SYN-1019 : Optimized 63 mux instances.
SYN-1016 : Merged 113 instances.
SYN-1032 : 2598/42 useful/useless nets, 2470/8 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7813, tnet num: 2598, tinst num: 2469, tnode num: 8973, tedge num: 10982.
TMR-2509 : Cut connection from add0_syn_20.a to add0_syn_20.o[1].
TMR-2509 : Cut connection from add0_syn_21.a to add0_syn_21.o[0].
TMR-2509 : Cut connection from eq0_syn_40.I[0] to eq0_syn_40.O.
TMR-2509 : Cut connection from eq1_syn_43.I[1] to eq1_syn_43.O.
TMR-2509 : Cut connection from eq1_syn_48.I[1] to eq1_syn_48.O.
TMR-2509 : Cut connection from eq1_syn_58.I[1] to eq1_syn_58.O.
TMR-2509 : Cut connection from mux0_syn_41.i0 to mux0_syn_41.o.
TMR-2509 : Cut connection from eq0_syn_53.I[1] to eq0_syn_53.O.
TMR-2509 : Cut connection from eq1_syn_56.I[1] to eq1_syn_56.O.
TMR-2509 : Cut connection from eq1_syn_57.I[0] to eq1_syn_57.O.
TMR-2509 : Cut connection from eq0_syn_56.I[1] to eq0_syn_56.O.
TMR-2509 : Cut connection from eq0_syn_57.I[0] to eq0_syn_57.O.
TMR-2509 : Cut connection from eq0_syn_58.I[0] to eq0_syn_58.O.
TMR-2509 : Cut connection from add0_syn_35.c to add0_syn_35.o[0].
TMR-2509 : Cut connection from add0_syn_35.c to add0_syn_35.o[1].
TMR-2509 : Cut connection from add0_syn_36.a to add0_syn_36.o[0].
TMR-2509 : Cut connection from add0_syn_37.a to add0_syn_37.o[0].
TMR-2509 : Cut connection from eq1_syn_55.I[1] to eq1_syn_55.O.
TMR-2509 : Cut connection from eq0_syn_55.I[1] to eq0_syn_55.O.
TMR-2509 : Cut connection from add0_syn_38.a to add0_syn_38.o[0].
TMR-2509 : Cut connection from eq1_syn_54.I[1] to eq1_syn_54.O.
TMR-2509 : Cut connection from add0_syn_39.a to add0_syn_39.o[0].
TMR-2509 : Cut connection from mux0_syn_26.i0 to mux0_syn_26.o.
TMR-2509 : Cut connection from eq0_syn_44.I[1] to eq0_syn_44.O.
TMR-2509 : Cut connection from eq1_syn_47.I[1] to eq1_syn_47.O.
TMR-2509 : Cut connection from eq0_syn_47.I[1] to eq0_syn_47.O.
TMR-2509 : Cut connection from eq0_syn_48.I[0] to eq0_syn_48.O.
TMR-2509 : Cut connection from add0_syn_25.c to add0_syn_25.o[0].
TMR-2509 : Cut connection from add0_syn_25.c to add0_syn_25.o[1].
TMR-2509 : Cut connection from add0_syn_26.a to add0_syn_26.o[0].
TMR-2509 : Cut connection from add0_syn_27.a to add0_syn_27.o[0].
TMR-2509 : Cut connection from eq0_syn_46.I[1] to eq0_syn_46.O.
TMR-2509 : Cut connection from add0_syn_28.a to add0_syn_28.o[0].
TMR-2509 : Cut connection from eq1_syn_45.I[1] to eq1_syn_45.O.
TMR-2509 : Cut connection from add0_syn_29.a to add0_syn_29.o[0].
TMR-2509 : Cut connection from add0_syn_30.a to add0_syn_30.o[0].
TMR-2509 : Cut connection from add0_syn_31.a to add0_syn_31.o[0].
TMR-2509 : Cut connection from eq1_syn_52.I[1] to eq1_syn_52.O.
TMR-2509 : Cut connection from eq0_syn_52.I[1] to eq0_syn_52.O.
TMR-2509 : Cut connection from add0_syn_32.a to add0_syn_32.o[0].
TMR-2509 : Cut connection from eq0_syn_51.I[1] to eq0_syn_51.O.
TMR-2509 : Cut connection from add0_syn_33.a to add0_syn_33.o[0].
TMR-2509 : Cut connection from eq1_syn_50.I[1] to eq1_syn_50.O.
TMR-2509 : Cut connection from add0_syn_34.a to add0_syn_34.o[0].
TMR-2509 : Cut connection from mux0_syn_21.i0 to mux0_syn_21.o.
TMR-2509 : Cut connection from eq0_syn_43.I[1] to eq0_syn_43.O.
TMR-2509 : Cut connection from add0_syn_22.a to add0_syn_22.o[0].
TMR-2509 : Cut connection from eq1_syn_42.I[1] to eq1_syn_42.O.
TMR-2509 : Cut connection from eq0_syn_42.I[1] to eq0_syn_42.O.
TMR-2509 : Cut connection from add0_syn_23.a to add0_syn_23.o[0].
TMR-2509 : Cut connection from eq1_syn_41.I[1] to eq1_syn_41.O.
TMR-2509 : Cut connection from eq0_syn_41.I[1] to eq0_syn_41.O.
TMR-2509 : Cut connection from add0_syn_24.a to add0_syn_24.o[0].
TMR-2507 : Eliminate loop in the timing graph, delete 53 tedges.
TMR-2508 : Levelizing timing graph completed, there are 105 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2598 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 428 (3.64), #lev = 7 (4.25)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 428 (3.64), #lev = 7 (4.25)
SYN-3001 : Logic optimization runtime opt =   0.08 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 1760 instances into 430 LUTs, name keeping = 57%.
RUN-1002 : start command "report_area -file IFOG501_2B_gate.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

LUT Statistics
#Total_luts               646
  #lut4                   365
  #lut5                    67
  #lut6                     0
  #lut5_mx41                0
  #lut4_alu1b             214

Utilization Statistics
#lut                      646   out of  19600    3.30%
#reg                      324   out of  19600    1.65%
#le                         0
#dsp                        0   out of     29    0.00%
#bram                       0   out of     64    0.00%
  #bram9k                   0
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%

Report Hierarchy Area:
+-------------------------------------------------------------------------+
|Instance   |Module          |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------+
|top        |IFOG501_2B      |432     |214     |330     |0       |0       |
|  CLK120   |global_clock    |0       |0       |0       |0       |0       |
|  CtrlData |CtrlData        |88      |28      |64      |0       |0       |
|    usms   |Time_1ms        |8       |17      |18      |0       |0       |
|  SPIM     |SPI_MASTER      |95      |58      |129     |0       |0       |
|  uart     |UART_Control    |109     |14      |53      |0       |0       |
|    U0     |speed_select_Tx |7       |14      |15      |0       |0       |
|    U1     |uart_tx         |18      |0       |17      |0       |0       |
|    U2     |Ctrl_Data       |84      |0       |21      |0       |0       |
|  wendu    |DS18B20         |122     |93      |75      |0       |0       |
+-------------------------------------------------------------------------+

SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 324 DFF/LATCH to SEQ ...
SYN-4009 : Pack 6 carry chain into lslice
SYN-4007 : Packing 107 adder to BLE ...
SYN-4008 : Packed 107 adder and 4 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 1128/0 useful/useless nets, 1009/1 useful/useless insts
RUN-1002 : start command "export_db IFOG501_2B_gate.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "backup_run_log run.log ../.logs/syn_1/td_20240621_113753.log"
