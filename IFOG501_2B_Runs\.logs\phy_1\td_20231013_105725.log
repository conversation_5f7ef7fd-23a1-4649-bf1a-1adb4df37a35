============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Oct 13 10:57:26 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 301 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 301 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1625 instances
RUN-0007 : 378 luts, 979 seqs, 144 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2181 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1646 nets have 2 pins
RUN-1001 : 419 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1623 instances, 378 luts, 979 seqs, 219 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7720, tnet num: 2179, tinst num: 1623, tnode num: 10905, tedge num: 13007.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.288063s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (103.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 551246
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1623.
PHY-3001 : End clustering;  0.000029s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 453149, overlap = 18
PHY-3002 : Step(2): len = 333767, overlap = 13.5
PHY-3002 : Step(3): len = 309620, overlap = 15.75
PHY-3002 : Step(4): len = 301034, overlap = 20.25
PHY-3002 : Step(5): len = 294255, overlap = 20.25
PHY-3002 : Step(6): len = 289472, overlap = 20.25
PHY-3002 : Step(7): len = 281724, overlap = 20.25
PHY-3002 : Step(8): len = 276766, overlap = 18
PHY-3002 : Step(9): len = 270893, overlap = 15.75
PHY-3002 : Step(10): len = 266104, overlap = 15.75
PHY-3002 : Step(11): len = 259795, overlap = 15.75
PHY-3002 : Step(12): len = 255567, overlap = 15.75
PHY-3002 : Step(13): len = 249374, overlap = 15.75
PHY-3002 : Step(14): len = 245142, overlap = 18
PHY-3002 : Step(15): len = 239200, overlap = 18
PHY-3002 : Step(16): len = 233879, overlap = 18
PHY-3002 : Step(17): len = 228107, overlap = 18
PHY-3002 : Step(18): len = 225132, overlap = 18
PHY-3002 : Step(19): len = 217439, overlap = 15.75
PHY-3002 : Step(20): len = 211955, overlap = 18
PHY-3002 : Step(21): len = 208061, overlap = 18
PHY-3002 : Step(22): len = 204703, overlap = 18
PHY-3002 : Step(23): len = 195066, overlap = 15.75
PHY-3002 : Step(24): len = 190981, overlap = 18
PHY-3002 : Step(25): len = 187935, overlap = 18
PHY-3002 : Step(26): len = 183753, overlap = 18
PHY-3002 : Step(27): len = 171606, overlap = 18
PHY-3002 : Step(28): len = 168122, overlap = 20.25
PHY-3002 : Step(29): len = 165674, overlap = 20.25
PHY-3002 : Step(30): len = 160861, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000162837
PHY-3002 : Step(31): len = 159797, overlap = 11.25
PHY-3002 : Step(32): len = 158448, overlap = 9
PHY-3002 : Step(33): len = 155941, overlap = 11.25
PHY-3002 : Step(34): len = 151981, overlap = 9
PHY-3002 : Step(35): len = 149893, overlap = 9
PHY-3002 : Step(36): len = 148154, overlap = 9
PHY-3002 : Step(37): len = 145016, overlap = 9
PHY-3002 : Step(38): len = 142907, overlap = 9
PHY-3002 : Step(39): len = 140593, overlap = 6.75
PHY-3002 : Step(40): len = 138000, overlap = 9
PHY-3002 : Step(41): len = 133858, overlap = 6.75
PHY-3002 : Step(42): len = 131958, overlap = 6.75
PHY-3002 : Step(43): len = 129580, overlap = 6.75
PHY-3002 : Step(44): len = 125886, overlap = 9
PHY-3002 : Step(45): len = 122132, overlap = 6.75
PHY-3002 : Step(46): len = 121339, overlap = 6.75
PHY-3002 : Step(47): len = 118523, overlap = 6.75
PHY-3002 : Step(48): len = 114124, overlap = 9
PHY-3002 : Step(49): len = 111898, overlap = 6.75
PHY-3002 : Step(50): len = 110578, overlap = 6.75
PHY-3002 : Step(51): len = 108399, overlap = 6.75
PHY-3002 : Step(52): len = 104593, overlap = 11.25
PHY-3002 : Step(53): len = 101319, overlap = 6.75
PHY-3002 : Step(54): len = 100454, overlap = 9
PHY-3002 : Step(55): len = 98018.5, overlap = 6.75
PHY-3002 : Step(56): len = 96289.2, overlap = 9
PHY-3002 : Step(57): len = 94343, overlap = 6.75
PHY-3002 : Step(58): len = 91744.1, overlap = 9
PHY-3002 : Step(59): len = 89414, overlap = 9
PHY-3002 : Step(60): len = 88340.1, overlap = 4.5
PHY-3002 : Step(61): len = 86882.9, overlap = 9
PHY-3002 : Step(62): len = 79339.2, overlap = 9
PHY-3002 : Step(63): len = 77720.3, overlap = 9
PHY-3002 : Step(64): len = 76900, overlap = 11.25
PHY-3002 : Step(65): len = 75074.9, overlap = 15.75
PHY-3002 : Step(66): len = 74887.4, overlap = 13.5
PHY-3002 : Step(67): len = 74639.2, overlap = 9
PHY-3002 : Step(68): len = 74149.8, overlap = 6.875
PHY-3002 : Step(69): len = 72431.6, overlap = 6.875
PHY-3002 : Step(70): len = 70963.8, overlap = 6.875
PHY-3002 : Step(71): len = 70811.4, overlap = 6.75
PHY-3002 : Step(72): len = 69793.6, overlap = 4.625
PHY-3002 : Step(73): len = 68742.5, overlap = 9.4375
PHY-3002 : Step(74): len = 66683.6, overlap = 11.4375
PHY-3002 : Step(75): len = 65765, overlap = 9.4375
PHY-3002 : Step(76): len = 65138.7, overlap = 9.5
PHY-3002 : Step(77): len = 64548.2, overlap = 9.5
PHY-3002 : Step(78): len = 63206.7, overlap = 7
PHY-3002 : Step(79): len = 61392.1, overlap = 6.875
PHY-3002 : Step(80): len = 61002.7, overlap = 7
PHY-3002 : Step(81): len = 60124.6, overlap = 7.0625
PHY-3002 : Step(82): len = 59119, overlap = 6.875
PHY-3002 : Step(83): len = 58736.6, overlap = 9
PHY-3002 : Step(84): len = 57480.8, overlap = 11.375
PHY-3002 : Step(85): len = 56480.6, overlap = 9.0625
PHY-3002 : Step(86): len = 55353.7, overlap = 6.75
PHY-3002 : Step(87): len = 54322, overlap = 9
PHY-3002 : Step(88): len = 54132.1, overlap = 11.25
PHY-3002 : Step(89): len = 53809.3, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000325674
PHY-3002 : Step(90): len = 53624.3, overlap = 6.75
PHY-3002 : Step(91): len = 53575.5, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000651348
PHY-3002 : Step(92): len = 53608.1, overlap = 6.75
PHY-3002 : Step(93): len = 53590, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005898s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063195s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(94): len = 57254.8, overlap = 6.25
PHY-3002 : Step(95): len = 56179, overlap = 5.125
PHY-3002 : Step(96): len = 55565.1, overlap = 4.1875
PHY-3002 : Step(97): len = 54658.6, overlap = 3.625
PHY-3002 : Step(98): len = 53975.9, overlap = 3.375
PHY-3002 : Step(99): len = 53289.7, overlap = 3.375
PHY-3002 : Step(100): len = 52367.1, overlap = 3.75
PHY-3002 : Step(101): len = 51510.5, overlap = 3.0625
PHY-3002 : Step(102): len = 50610.2, overlap = 2.5625
PHY-3002 : Step(103): len = 49713.2, overlap = 2.84375
PHY-3002 : Step(104): len = 49128.9, overlap = 2.65625
PHY-3002 : Step(105): len = 48678.6, overlap = 3.40625
PHY-3002 : Step(106): len = 47960.4, overlap = 3.5625
PHY-3002 : Step(107): len = 47355.4, overlap = 3.28125
PHY-3002 : Step(108): len = 46946, overlap = 6.40625
PHY-3002 : Step(109): len = 46654.1, overlap = 6.40625
PHY-3002 : Step(110): len = 46019.5, overlap = 7.59375
PHY-3002 : Step(111): len = 45707.5, overlap = 7.625
PHY-3002 : Step(112): len = 45357.5, overlap = 6.875
PHY-3002 : Step(113): len = 45150.5, overlap = 9.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000820957
PHY-3002 : Step(114): len = 45221.9, overlap = 8.5625
PHY-3002 : Step(115): len = 44945.6, overlap = 8.5625
PHY-3002 : Step(116): len = 45022.7, overlap = 8.625
PHY-3002 : Step(117): len = 44902.7, overlap = 8.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00164191
PHY-3002 : Step(118): len = 44859.7, overlap = 8.625
PHY-3002 : Step(119): len = 44828.8, overlap = 10.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057712s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.91348e-05
PHY-3002 : Step(120): len = 45324.8, overlap = 61.875
PHY-3002 : Step(121): len = 46233.8, overlap = 56.4375
PHY-3002 : Step(122): len = 46937.1, overlap = 52.6875
PHY-3002 : Step(123): len = 47178.4, overlap = 52.1562
PHY-3002 : Step(124): len = 46931.8, overlap = 53.4688
PHY-3002 : Step(125): len = 46711.3, overlap = 54.9375
PHY-3002 : Step(126): len = 46576.7, overlap = 54.5625
PHY-3002 : Step(127): len = 46343.4, overlap = 53.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00015827
PHY-3002 : Step(128): len = 46798, overlap = 52.75
PHY-3002 : Step(129): len = 47090.6, overlap = 51.4688
PHY-3002 : Step(130): len = 47484.2, overlap = 44.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000316539
PHY-3002 : Step(131): len = 47970, overlap = 40.8125
PHY-3002 : Step(132): len = 48504.9, overlap = 34.375
PHY-3002 : Step(133): len = 48672.3, overlap = 32.875
PHY-3002 : Step(134): len = 48700.5, overlap = 33.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7720, tnet num: 2179, tinst num: 1623, tnode num: 10905, tedge num: 13007.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.19 peak overflow 4.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2181.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52176, over cnt = 247(0%), over = 1036, worst = 19
PHY-1001 : End global iterations;  0.070625s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (132.7%)

PHY-1001 : Congestion index: top1 = 41.44, top5 = 24.97, top10 = 15.47, top15 = 10.96.
PHY-1001 : End incremental global routing;  0.120710s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (116.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064943s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (96.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.215460s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (108.8%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1640/2181.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52176, over cnt = 247(0%), over = 1036, worst = 19
PHY-1002 : len = 57536, over cnt = 167(0%), over = 504, worst = 14
PHY-1002 : len = 60216, over cnt = 79(0%), over = 254, worst = 14
PHY-1002 : len = 63040, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 63056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087009s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (143.7%)

PHY-1001 : Congestion index: top1 = 36.21, top5 = 24.85, top10 = 17.45, top15 = 12.66.
OPT-1001 : End congestion update;  0.129667s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (132.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056567s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.188705s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (115.9%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : End physical optimization;  0.680975s wall, 0.656250s user + 0.062500s system = 0.718750s CPU (105.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 378 LUT to BLE ...
SYN-4008 : Packed 378 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 98 SEQ with LUT/SLICE
SYN-4006 : 110 single LUT's are left
SYN-4006 : 692 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1396 primitive instances ...
PHY-3001 : End packing;  0.049237s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (126.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 831 instances
RUN-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2006 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1478 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 829 instances, 782 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 48822.2, Over = 64.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6486, tnet num: 2004, tinst num: 829, tnode num: 8781, tedge num: 11366.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.324689s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.1578e-05
PHY-3002 : Step(135): len = 48283.2, overlap = 64.5
PHY-3002 : Step(136): len = 48046, overlap = 64
PHY-3002 : Step(137): len = 47849, overlap = 64
PHY-3002 : Step(138): len = 47930.8, overlap = 62.5
PHY-3002 : Step(139): len = 48160.7, overlap = 62.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.31561e-05
PHY-3002 : Step(140): len = 48251.7, overlap = 62.75
PHY-3002 : Step(141): len = 48408.4, overlap = 62.25
PHY-3002 : Step(142): len = 48850.9, overlap = 60
PHY-3002 : Step(143): len = 49758.5, overlap = 58.25
PHY-3002 : Step(144): len = 50139.5, overlap = 57
PHY-3002 : Step(145): len = 50192.2, overlap = 56.5
PHY-3002 : Step(146): len = 50264.6, overlap = 54
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.63122e-05
PHY-3002 : Step(147): len = 50456.3, overlap = 53.5
PHY-3002 : Step(148): len = 50844.5, overlap = 53.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.082406s wall, 0.062500s user + 0.125000s system = 0.187500s CPU (227.5%)

PHY-3001 : Trial Legalized: Len = 66757.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048929s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000509707
PHY-3002 : Step(149): len = 63738.4, overlap = 9.75
PHY-3002 : Step(150): len = 61228.5, overlap = 15
PHY-3002 : Step(151): len = 59555.1, overlap = 16.75
PHY-3002 : Step(152): len = 58516.7, overlap = 18.25
PHY-3002 : Step(153): len = 57829.2, overlap = 20.25
PHY-3002 : Step(154): len = 57511.2, overlap = 21.5
PHY-3002 : Step(155): len = 57201.5, overlap = 22.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00101941
PHY-3002 : Step(156): len = 57544.9, overlap = 22
PHY-3002 : Step(157): len = 57760.8, overlap = 20.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00203883
PHY-3002 : Step(158): len = 57880.9, overlap = 20.5
PHY-3002 : Step(159): len = 57991.2, overlap = 20.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005053s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62499.2, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005609s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.6%)

PHY-3001 : 9 instances has been re-located, deltaX = 5, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 62545.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6486, tnet num: 2004, tinst num: 829, tnode num: 8781, tedge num: 11366.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 25/2006.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68864, over cnt = 117(0%), over = 183, worst = 7
PHY-1002 : len = 69744, over cnt = 56(0%), over = 70, worst = 4
PHY-1002 : len = 70200, over cnt = 22(0%), over = 30, worst = 3
PHY-1002 : len = 70624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113793s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (123.6%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.34, top10 = 17.44, top15 = 13.82.
PHY-1001 : End incremental global routing;  0.168996s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (110.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062990s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.262294s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (107.2%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1791/2006.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005732s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.34, top10 = 17.44, top15 = 13.82.
OPT-1001 : End congestion update;  0.054010s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050915s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 791 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 829 instances, 782 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62562.2, Over = 0
PHY-3001 : End spreading;  0.004957s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62562.2, Over = 0
PHY-3001 : End incremental legalization;  0.033349s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (421.7%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150680s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (176.3%)

OPT-1001 : Current memory(MB): used = 226, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047600s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1784/2006.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007307s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.30, top10 = 17.43, top15 = 13.82.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049405s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.846929s wall, 0.968750s user + 0.000000s system = 0.968750s CPU (114.4%)

RUN-1003 : finish command "place" in  5.243276s wall, 8.625000s user + 2.828125s system = 11.453125s CPU (218.4%)

RUN-1004 : used memory is 205 MB, reserved memory is 169 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 831 instances
RUN-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2006 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1478 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6486, tnet num: 2004, tinst num: 829, tnode num: 8781, tedge num: 11366.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2004 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68584, over cnt = 121(0%), over = 189, worst = 7
PHY-1002 : len = 69432, over cnt = 63(0%), over = 80, worst = 5
PHY-1002 : len = 70120, over cnt = 15(0%), over = 21, worst = 2
PHY-1002 : len = 70408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106251s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (176.5%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.20, top10 = 17.35, top15 = 13.75.
PHY-1001 : End global routing;  0.156762s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (149.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 205, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 469, peak = 501.
PHY-1001 : End build detailed router design. 4.141662s wall, 3.968750s user + 0.078125s system = 4.046875s CPU (97.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35192, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.027482s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 535.
PHY-1001 : End phase 1; 2.034282s wall, 2.015625s user + 0.031250s system = 2.046875s CPU (100.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182456, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End initial routed; 1.696591s wall, 2.578125s user + 0.218750s system = 2.796875s CPU (164.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1776(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.253   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.411   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.791045s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 538, reserve = 505, peak = 538.
PHY-1001 : End phase 2; 2.487779s wall, 3.359375s user + 0.234375s system = 3.593750s CPU (144.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182456, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.025193s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (124.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182432, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033559s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182464, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027183s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1776(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.253   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.411   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.759644s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.389440s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 553, reserve = 520, peak = 553.
PHY-1001 : End phase 3; 1.476925s wall, 1.468750s user + 0.015625s system = 1.484375s CPU (100.5%)

PHY-1003 : Routed, final wirelength = 182464
PHY-1001 : Current memory(MB): used = 553, reserve = 521, peak = 553.
PHY-1001 : End export database. 0.020705s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (75.5%)

PHY-1001 : End detail routing;  10.594056s wall, 11.265625s user + 0.359375s system = 11.625000s CPU (109.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6486, tnet num: 2004, tinst num: 829, tnode num: 8781, tedge num: 11366.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[19] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[20] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[4] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.addra[4] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2028, tinst num: 853, tnode num: 8829, tedge num: 11414.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  5.822252s wall, 5.796875s user + 0.203125s system = 6.000000s CPU (103.1%)

RUN-1003 : finish command "route" in  16.930634s wall, 17.640625s user + 0.578125s system = 18.218750s CPU (107.6%)

RUN-1004 : used memory is 549 MB, reserved memory is 519 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      869   out of  19600    4.43%
#reg                     1047   out of  19600    5.34%
#le                      1561
  #lut only               514   out of   1561   32.93%
  #reg only               692   out of   1561   44.33%
  #lut&reg                355   out of   1561   22.74%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         459
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1561   |650     |219     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1152   |339     |126     |894     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |17      |0       |0       |
|    demodu                  |Demodulation                                     |589    |177     |58      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |62      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |53     |2       |0       |53      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |18      |0       |28      |0       |0       |
|    integ                   |Integration                                      |137    |18      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |66     |24      |14      |62      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |84      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |19      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |111    |99      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |20     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |54     |53      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1466  
    #2          2       289   
    #3          3       106   
    #4          4        15   
    #5        5-10       81   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2028, tinst num: 853, tnode num: 8829, tedge num: 11414.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 853
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2030, pip num: 14714
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1346 valid insts, and 39274 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.865586s wall, 20.484375s user + 0.093750s system = 20.578125s CPU (532.3%)

RUN-1004 : used memory is 561 MB, reserved memory is 528 MB, peak memory is 690 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231013_105725.log"
