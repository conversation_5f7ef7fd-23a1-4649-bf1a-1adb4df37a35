============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug 17 18:47:37 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1640 instances
RUN-0007 : 373 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2210 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1656 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1638 instances, 373 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7790, tnet num: 2208, tinst num: 1638, tnode num: 11028, tedge num: 13149.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.312264s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540637
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1638.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 490661, overlap = 18
PHY-3002 : Step(2): len = 407132, overlap = 18
PHY-3002 : Step(3): len = 356095, overlap = 18
PHY-3002 : Step(4): len = 335425, overlap = 11.25
PHY-3002 : Step(5): len = 328918, overlap = 18
PHY-3002 : Step(6): len = 323394, overlap = 18
PHY-3002 : Step(7): len = 316286, overlap = 18
PHY-3002 : Step(8): len = 307667, overlap = 18
PHY-3002 : Step(9): len = 302099, overlap = 18
PHY-3002 : Step(10): len = 295570, overlap = 18
PHY-3002 : Step(11): len = 286523, overlap = 20.25
PHY-3002 : Step(12): len = 280718, overlap = 20.25
PHY-3002 : Step(13): len = 275372, overlap = 20.25
PHY-3002 : Step(14): len = 268952, overlap = 20.25
PHY-3002 : Step(15): len = 263189, overlap = 20.25
PHY-3002 : Step(16): len = 258213, overlap = 20.25
PHY-3002 : Step(17): len = 252599, overlap = 20.25
PHY-3002 : Step(18): len = 247566, overlap = 20.25
PHY-3002 : Step(19): len = 243115, overlap = 20.25
PHY-3002 : Step(20): len = 237815, overlap = 20.25
PHY-3002 : Step(21): len = 232349, overlap = 20.25
PHY-3002 : Step(22): len = 227562, overlap = 20.25
PHY-3002 : Step(23): len = 223255, overlap = 20.25
PHY-3002 : Step(24): len = 218095, overlap = 20.25
PHY-3002 : Step(25): len = 213282, overlap = 20.25
PHY-3002 : Step(26): len = 208281, overlap = 20.25
PHY-3002 : Step(27): len = 203402, overlap = 20.25
PHY-3002 : Step(28): len = 199502, overlap = 20.25
PHY-3002 : Step(29): len = 194178, overlap = 20.25
PHY-3002 : Step(30): len = 190330, overlap = 20.25
PHY-3002 : Step(31): len = 184133, overlap = 20.25
PHY-3002 : Step(32): len = 179637, overlap = 20.25
PHY-3002 : Step(33): len = 176420, overlap = 20.25
PHY-3002 : Step(34): len = 171608, overlap = 20.25
PHY-3002 : Step(35): len = 165323, overlap = 20.25
PHY-3002 : Step(36): len = 161828, overlap = 20.25
PHY-3002 : Step(37): len = 158663, overlap = 20.25
PHY-3002 : Step(38): len = 153031, overlap = 20.25
PHY-3002 : Step(39): len = 143922, overlap = 20.25
PHY-3002 : Step(40): len = 140881, overlap = 20.25
PHY-3002 : Step(41): len = 138909, overlap = 20.25
PHY-3002 : Step(42): len = 110438, overlap = 20.25
PHY-3002 : Step(43): len = 96713.2, overlap = 20.25
PHY-3002 : Step(44): len = 96192.5, overlap = 20.25
PHY-3002 : Step(45): len = 94190.7, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.85015e-05
PHY-3002 : Step(46): len = 94909.5, overlap = 9
PHY-3002 : Step(47): len = 94738, overlap = 9
PHY-3002 : Step(48): len = 93946.9, overlap = 11.25
PHY-3002 : Step(49): len = 93332.5, overlap = 15.75
PHY-3002 : Step(50): len = 93084.7, overlap = 13.5
PHY-3002 : Step(51): len = 92494.7, overlap = 4.5
PHY-3002 : Step(52): len = 90228.7, overlap = 4.5
PHY-3002 : Step(53): len = 88221.3, overlap = 4.5
PHY-3002 : Step(54): len = 86217.3, overlap = 6.75
PHY-3002 : Step(55): len = 84754, overlap = 9
PHY-3002 : Step(56): len = 84742.6, overlap = 11.25
PHY-3002 : Step(57): len = 84093.6, overlap = 9
PHY-3002 : Step(58): len = 83014, overlap = 4.5
PHY-3002 : Step(59): len = 82026.5, overlap = 9
PHY-3002 : Step(60): len = 79531.5, overlap = 11.25
PHY-3002 : Step(61): len = 78429.8, overlap = 9
PHY-3002 : Step(62): len = 77887.1, overlap = 6.75
PHY-3002 : Step(63): len = 73456.4, overlap = 9
PHY-3002 : Step(64): len = 71191, overlap = 9
PHY-3002 : Step(65): len = 70075.7, overlap = 6.75
PHY-3002 : Step(66): len = 69879.9, overlap = 4.5
PHY-3002 : Step(67): len = 69099.5, overlap = 4.5
PHY-3002 : Step(68): len = 68229.6, overlap = 9
PHY-3002 : Step(69): len = 66982.3, overlap = 9
PHY-3002 : Step(70): len = 66212.2, overlap = 9
PHY-3002 : Step(71): len = 65826.6, overlap = 6.75
PHY-3002 : Step(72): len = 65001.9, overlap = 4.5
PHY-3002 : Step(73): len = 63887, overlap = 4.5
PHY-3002 : Step(74): len = 63366.1, overlap = 9
PHY-3002 : Step(75): len = 63067.7, overlap = 9
PHY-3002 : Step(76): len = 62439, overlap = 9
PHY-3002 : Step(77): len = 62396.6, overlap = 9
PHY-3002 : Step(78): len = 62504, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000197003
PHY-3002 : Step(79): len = 62395.2, overlap = 6.75
PHY-3002 : Step(80): len = 62378.1, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000394006
PHY-3002 : Step(81): len = 62485.5, overlap = 4.5
PHY-3002 : Step(82): len = 62542.7, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006710s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058422s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(83): len = 66067.2, overlap = 4.28125
PHY-3002 : Step(84): len = 64800.5, overlap = 3.5625
PHY-3002 : Step(85): len = 63894.1, overlap = 5
PHY-3002 : Step(86): len = 62216.2, overlap = 5
PHY-3002 : Step(87): len = 60780.3, overlap = 5.0625
PHY-3002 : Step(88): len = 59333.9, overlap = 4.875
PHY-3002 : Step(89): len = 58075.4, overlap = 4.8125
PHY-3002 : Step(90): len = 57215.2, overlap = 3.9375
PHY-3002 : Step(91): len = 55760.9, overlap = 3.125
PHY-3002 : Step(92): len = 53968.6, overlap = 5.0625
PHY-3002 : Step(93): len = 52967.9, overlap = 8.125
PHY-3002 : Step(94): len = 52525.5, overlap = 8.5
PHY-3002 : Step(95): len = 52037.2, overlap = 8.5625
PHY-3002 : Step(96): len = 51436, overlap = 6.6875
PHY-3002 : Step(97): len = 50902.9, overlap = 6.25
PHY-3002 : Step(98): len = 50743.8, overlap = 6.34375
PHY-3002 : Step(99): len = 50548.3, overlap = 6
PHY-3002 : Step(100): len = 49643.4, overlap = 8.34375
PHY-3002 : Step(101): len = 49115.1, overlap = 8.625
PHY-3002 : Step(102): len = 48451, overlap = 10.4688
PHY-3002 : Step(103): len = 48161, overlap = 11.6562
PHY-3002 : Step(104): len = 47829.6, overlap = 12.7812
PHY-3002 : Step(105): len = 47501.3, overlap = 12.9688
PHY-3002 : Step(106): len = 47222.9, overlap = 11.5938
PHY-3002 : Step(107): len = 47226.2, overlap = 11.9062
PHY-3002 : Step(108): len = 47018, overlap = 12.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000534068
PHY-3002 : Step(109): len = 46953.2, overlap = 12.0312
PHY-3002 : Step(110): len = 46937.7, overlap = 12.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00106814
PHY-3002 : Step(111): len = 46790, overlap = 12.1875
PHY-3002 : Step(112): len = 46943.2, overlap = 12.2812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063591s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000107053
PHY-3002 : Step(113): len = 47225.8, overlap = 52.2188
PHY-3002 : Step(114): len = 48202.5, overlap = 53.2188
PHY-3002 : Step(115): len = 48502.3, overlap = 46.375
PHY-3002 : Step(116): len = 49379.6, overlap = 43.1562
PHY-3002 : Step(117): len = 49435.7, overlap = 42.375
PHY-3002 : Step(118): len = 49253.1, overlap = 43.6875
PHY-3002 : Step(119): len = 48726.7, overlap = 43.5625
PHY-3002 : Step(120): len = 48743.2, overlap = 43
PHY-3002 : Step(121): len = 48625.7, overlap = 42.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000214106
PHY-3002 : Step(122): len = 48742, overlap = 41.7812
PHY-3002 : Step(123): len = 49709.8, overlap = 39.0625
PHY-3002 : Step(124): len = 50088.3, overlap = 38.9375
PHY-3002 : Step(125): len = 50476.2, overlap = 37.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000428212
PHY-3002 : Step(126): len = 50537.5, overlap = 37.3438
PHY-3002 : Step(127): len = 50757.8, overlap = 37.6875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7790, tnet num: 2208, tinst num: 1638, tnode num: 11028, tedge num: 13149.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 95.62 peak overflow 3.19
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2210.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53088, over cnt = 247(0%), over = 1122, worst = 20
PHY-1001 : End global iterations;  0.071989s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.5%)

PHY-1001 : Congestion index: top1 = 46.51, top5 = 25.09, top10 = 15.65, top15 = 11.02.
PHY-1001 : End incremental global routing;  0.120263s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (90.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2208 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068625s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.8%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1599 has valid locations, 4 needs to be replaced
PHY-3001 : design contains 1641 instances, 373 luts, 995 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 50788.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7802, tnet num: 2211, tinst num: 1641, tnode num: 11049, tedge num: 13167.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2211 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.318731s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (102.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(128): len = 50858.7, overlap = 1.375
PHY-3002 : Step(129): len = 50869.1, overlap = 1.375
PHY-3002 : Step(130): len = 50872.7, overlap = 1.4375
PHY-3002 : Step(131): len = 50872.7, overlap = 1.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2211 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057403s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000151683
PHY-3002 : Step(132): len = 50872.7, overlap = 37.6875
PHY-3002 : Step(133): len = 50872.7, overlap = 37.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000303365
PHY-3002 : Step(134): len = 50888.6, overlap = 37.6875
PHY-3002 : Step(135): len = 50888.6, overlap = 37.6875
PHY-3001 : Final: Len = 50888.6, Over = 37.6875
PHY-3001 : End incremental placement;  0.487140s wall, 0.468750s user + 0.093750s system = 0.562500s CPU (115.5%)

OPT-1001 : Total overflow 95.81 peak overflow 3.19
OPT-1001 : End high-fanout net optimization;  0.711903s wall, 0.687500s user + 0.093750s system = 0.781250s CPU (109.7%)

OPT-1001 : Current memory(MB): used = 226, reserve = 185, peak = 227.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1675/2213.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53256, over cnt = 247(0%), over = 1113, worst = 20
PHY-1002 : len = 62136, over cnt = 187(0%), over = 384, worst = 11
PHY-1002 : len = 64640, over cnt = 113(0%), over = 141, worst = 8
PHY-1002 : len = 66256, over cnt = 40(0%), over = 44, worst = 2
PHY-1002 : len = 67080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094742s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (131.9%)

PHY-1001 : Congestion index: top1 = 39.96, top5 = 25.85, top10 = 18.08, top15 = 13.22.
OPT-1001 : End congestion update;  0.139394s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (123.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2211 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061635s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.203830s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.0%)

OPT-1001 : Current memory(MB): used = 224, reserve = 184, peak = 227.
OPT-1001 : End physical optimization;  1.203845s wall, 1.281250s user + 0.109375s system = 1.390625s CPU (115.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 806 remaining SEQ's ...
SYN-4005 : Packed 86 SEQ with LUT/SLICE
SYN-4006 : 118 single LUT's are left
SYN-4006 : 720 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1093/1426 primitive instances ...
PHY-3001 : End packing;  0.053709s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 847 instances
RUN-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2039 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1491 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 845 instances, 798 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51151.4, Over = 63.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2037, tinst num: 845, tnode num: 8898, tedge num: 11511.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.305592s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (102.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.58419e-05
PHY-3002 : Step(136): len = 50645.1, overlap = 61.75
PHY-3002 : Step(137): len = 49899.2, overlap = 64.75
PHY-3002 : Step(138): len = 50018.3, overlap = 64.75
PHY-3002 : Step(139): len = 49919.7, overlap = 66
PHY-3002 : Step(140): len = 49963.5, overlap = 65
PHY-3002 : Step(141): len = 49789.2, overlap = 65.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.16837e-05
PHY-3002 : Step(142): len = 50185.5, overlap = 64.25
PHY-3002 : Step(143): len = 50702.1, overlap = 63
PHY-3002 : Step(144): len = 51114.8, overlap = 62.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000103367
PHY-3002 : Step(145): len = 51776.4, overlap = 61.5
PHY-3002 : Step(146): len = 52852.2, overlap = 61.25
PHY-3002 : Step(147): len = 53257.9, overlap = 59
PHY-3002 : Step(148): len = 53612.5, overlap = 60.25
PHY-3002 : Step(149): len = 53698.6, overlap = 58
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.080867s wall, 0.093750s user + 0.109375s system = 0.203125s CPU (251.2%)

PHY-3001 : Trial Legalized: Len = 67939.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052011s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000474423
PHY-3002 : Step(150): len = 64519, overlap = 9.25
PHY-3002 : Step(151): len = 62223.1, overlap = 13.5
PHY-3002 : Step(152): len = 60605.5, overlap = 15.75
PHY-3002 : Step(153): len = 59391.5, overlap = 17.75
PHY-3002 : Step(154): len = 58635, overlap = 22
PHY-3002 : Step(155): len = 58266.8, overlap = 24.25
PHY-3002 : Step(156): len = 58155.9, overlap = 24.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000948847
PHY-3002 : Step(157): len = 58567.9, overlap = 24.75
PHY-3002 : Step(158): len = 58653.8, overlap = 24.25
PHY-3002 : Step(159): len = 58691.7, overlap = 24
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00189769
PHY-3002 : Step(160): len = 58836.1, overlap = 22.75
PHY-3002 : Step(161): len = 58863.5, overlap = 22.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004943s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (316.1%)

PHY-3001 : Legalized: Len = 63282, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005504s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 4, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 63304, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2037, tinst num: 845, tnode num: 8898, tedge num: 11511.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 70/2039.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69456, over cnt = 131(0%), over = 200, worst = 7
PHY-1002 : len = 70504, over cnt = 53(0%), over = 55, worst = 2
PHY-1002 : len = 71096, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 71200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124975s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (112.5%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 22.32, top10 = 17.46, top15 = 13.88.
PHY-1001 : End incremental global routing;  0.175370s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063340s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.268754s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (104.6%)

OPT-1001 : Current memory(MB): used = 226, reserve = 186, peak = 227.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1812/2039.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005875s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 22.32, top10 = 17.46, top15 = 13.88.
OPT-1001 : End congestion update;  0.050661s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050311s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 807 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 845 instances, 798 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63393.4, Over = 0
PHY-3001 : End spreading;  0.005710s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (273.7%)

PHY-3001 : Final: Len = 63393.4, Over = 0
PHY-3001 : End incremental legalization;  0.036471s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.7%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.151186s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.3%)

OPT-1001 : Current memory(MB): used = 230, reserve = 190, peak = 230.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048601s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1802/2039.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71368, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 71360, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.025722s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (60.7%)

PHY-1001 : Congestion index: top1 = 31.77, top5 = 22.39, top10 = 17.51, top15 = 13.92.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049686s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (94.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.890635s wall, 0.984375s user + 0.015625s system = 1.000000s CPU (112.3%)

RUN-1003 : finish command "place" in  6.091073s wall, 9.515625s user + 3.171875s system = 12.687500s CPU (208.3%)

RUN-1004 : used memory is 209 MB, reserved memory is 168 MB, peak memory is 230 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 847 instances
RUN-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2039 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1491 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2037, tinst num: 845, tnode num: 8898, tedge num: 11511.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69000, over cnt = 137(0%), over = 206, worst = 7
PHY-1002 : len = 70136, over cnt = 54(0%), over = 56, worst = 2
PHY-1002 : len = 70824, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 70872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131785s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (154.1%)

PHY-1001 : Congestion index: top1 = 31.96, top5 = 22.21, top10 = 17.40, top15 = 13.85.
PHY-1001 : End global routing;  0.181005s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (138.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 243, reserve = 203, peak = 252.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 503, reserve = 467, peak = 503.
PHY-1001 : End build detailed router design. 3.260533s wall, 3.187500s user + 0.046875s system = 3.234375s CPU (99.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33752, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.242455s wall, 1.234375s user + 0.015625s system = 1.250000s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 537, reserve = 500, peak = 538.
PHY-1001 : End phase 1; 1.248129s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (101.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176360, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 537, reserve = 500, peak = 538.
PHY-1001 : End initial routed; 1.059541s wall, 1.656250s user + 0.078125s system = 1.734375s CPU (163.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1802(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.875   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.355191s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (96.8%)

PHY-1001 : Current memory(MB): used = 540, reserve = 503, peak = 540.
PHY-1001 : End phase 2; 1.414817s wall, 2.000000s user + 0.078125s system = 2.078125s CPU (146.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176360, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014320s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176360, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030983s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176448, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022410s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (69.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1802(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.875   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.359753s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.175728s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (106.7%)

PHY-1001 : Current memory(MB): used = 553, reserve = 516, peak = 553.
PHY-1001 : End phase 3; 0.724581s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (101.4%)

PHY-1003 : Routed, final wirelength = 176448
PHY-1001 : Current memory(MB): used = 553, reserve = 517, peak = 553.
PHY-1001 : End export database. 0.009713s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.840372s wall, 7.328125s user + 0.156250s system = 7.484375s CPU (109.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2037, tinst num: 845, tnode num: 8898, tedge num: 11511.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.757624s wall, 8.312500s user + 0.171875s system = 8.484375s CPU (109.4%)

RUN-1004 : used memory is 507 MB, reserved memory is 473 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      830   out of  19600    4.23%
#reg                     1077   out of  19600    5.49%
#le                      1550
  #lut only               473   out of   1550   30.52%
  #reg only               720   out of   1550   46.45%
  #lut&reg                357   out of   1550   23.03%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         470
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1550   |604     |226     |1108    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1155   |307     |133     |930     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |30      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |537    |120     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |13      |0       |25      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |92     |25      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |323    |91      |29      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |101    |89      |7       |51      |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |18     |13      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |49     |49      |0       |19      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1455  
    #2          2       299   
    #3          3       111   
    #4          4        22   
    #5        5-10       77   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2037, tinst num: 845, tnode num: 8898, tedge num: 11511.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2037 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 845
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2039, pip num: 14592
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1307 valid insts, and 38716 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.166322s wall, 18.671875s user + 0.078125s system = 18.750000s CPU (592.2%)

RUN-1004 : used memory is 522 MB, reserved memory is 488 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230817_184737.log"
