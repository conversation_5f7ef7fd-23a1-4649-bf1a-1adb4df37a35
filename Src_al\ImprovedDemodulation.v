//////////////////////////////////////////////////////////////////////////////////
// 改进的解调模块 - 集成数字滤波、自适应控制和质量监测
// 功能：在原有解调基础上增加信号处理和质量控制功能
// 特点：更强的抗噪声能力、自适应调整、实时质量监测
//////////////////////////////////////////////////////////////////////////////////

module ImprovedDemodulation
#(
    parameter acum_cnt = 45,        // 采样次数
    parameter iWID_IN = 12,         // 输入位宽
    parameter iWID_OUT = 56,        // 输出位宽
    parameter ENABLE_FILTER = 1,    // 使能滤波器
    parameter ENABLE_AGC = 1,       // 使能自动增益控制
    parameter ENABLE_MONITOR = 1    // 使能质量监测
)
(
    input                           clk,
    input                           rst_n,
    input   [iWID_IN-1:0]           din,            // AD输入数据
    input                           clk_in,         // AD时钟
    input                           AD_valid,       // AD有效信号
    input                           polarity,       // 极性信号
    input                           demodulate,     // 解调使能
    
    output  [iWID_OUT-1:0]          dout,           // 解调输出
    output                          data_ready,     // 数据准备好
    
    // 质量监测输出
    output  [15:0]                  signal_quality, // 信号质量指示
    output                          quality_good,   // 质量良好
    output                          quality_warning,// 质量警告
    output                          quality_bad,    // 质量差
    
    // 调试输出
    output  [15:0]                  current_gain,   // 当前增益
    output  [15:0]                  snr_value      // 信噪比
);

// 内部信号定义
wire [iWID_IN-1:0] filtered_data;      // 滤波后数据
wire [15:0] agc_output;                 // AGC输出
wire filter_ready, agc_ready;          // 各模块准备信号

// 原始解调相关信号（保持原有逻辑）
reg [1:0] demodu_dy;
reg [1:0] AD_valid_dy;
reg [7:0] sample_cnt;
reg [iWID_OUT-1:0] sample_sum;
reg [iWID_OUT-1:0] sample_sum_DY;
reg [iWID_OUT-1:0] median_sum_n;
reg [iWID_OUT-1:0] INS_dout;

// 选择处理后的数据作为解调输入
wire [iWID_IN-1:0] demod_input;

// 根据配置选择数据路径
generate
    if (ENABLE_FILTER && ENABLE_AGC) begin
        assign demod_input = agc_output[iWID_IN-1:0];  // 使用AGC输出
    end
    else if (ENABLE_FILTER) begin
        assign demod_input = filtered_data;            // 只使用滤波
    end
    else begin
        assign demod_input = din;                      // 使用原始数据
    end
endgenerate

// 实例化数字滤波器
generate
    if (ENABLE_FILTER) begin : filter_inst
        DigitalFilter_IIR #(
            .DATA_WIDTH(iWID_IN),
            .OUTPUT_WIDTH(iWID_IN)
        ) digital_filter (
            .clk(clk),
            .rst_n(rst_n),
            .data_valid(AD_valid),
            .data_in(din),
            .data_ready(filter_ready),
            .data_out(filtered_data)
        );
    end
    else begin
        assign filtered_data = din;
        assign filter_ready = AD_valid;
    end
endgenerate

// 实例化自适应增益控制
generate
    if (ENABLE_AGC) begin : agc_inst
        AdaptiveGainControl #(
            .DATA_WIDTH(iWID_IN),
            .OUTPUT_WIDTH(16)
        ) agc_control (
            .clk(clk),
            .rst_n(rst_n),
            .data_valid(filter_ready),
            .data_in(filtered_data),
            .target_power(16'h4000),        // 目标功率
            .agc_speed(8'h10),              // AGC速度
            .data_ready(agc_ready),
            .data_out(agc_output),
            .current_gain(current_gain),
            .signal_power()
        );
    end
    else begin
        assign agc_output = {4'b0, filtered_data};
        assign agc_ready = filter_ready;
        assign current_gain = 16'h4000;  // 固定增益
    end
endgenerate

// 实例化信号质量监测
generate
    if (ENABLE_MONITOR) begin : monitor_inst
        SignalQualityMonitor #(
            .DATA_WIDTH(iWID_IN)
        ) quality_monitor (
            .clk(clk),
            .rst_n(rst_n),
            .data_valid(AD_valid),
            .data_in(din),
            .snr_estimate(snr_value),
            .signal_amplitude(signal_quality),
            .signal_good(quality_good),
            .signal_warning(quality_warning),
            .signal_bad(quality_bad),
            .snr_threshold_good(16'h1000),      // SNR良好阈值
            .snr_threshold_warning(16'h0800),   // SNR警告阈值
            .amplitude_threshold(16'h0400)      // 幅度阈值
        );
    end
    else begin
        assign signal_quality = 16'hFFFF;
        assign quality_good = 1'b1;
        assign quality_warning = 1'b0;
        assign quality_bad = 1'b0;
        assign snr_value = 16'hFFFF;
    end
endgenerate

// 原有解调逻辑（使用处理后的数据）
always @(posedge clk) begin
    demodu_dy <= {demodu_dy[0], demodulate};
    AD_valid_dy <= {AD_valid_dy[0], AD_valid};
end

// 采样计数和累加（改进版）
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        sample_cnt <= 0;
        sample_sum <= 0;
    end
    else if (AD_valid_dy[1:0] == 2'b01) begin  // AD_valid上升沿
        sample_cnt <= 0;
        sample_sum <= 0;
    end
    else if (AD_valid && (sample_cnt < acum_cnt)) begin
        sample_cnt <= sample_cnt + 1;
        sample_sum <= sample_sum + demod_input;  // 使用处理后的数据
    end
end

// 解调输出计算（保持原有算法）
always @(posedge clk) begin
    if (demodu_dy[1:0] == 2'b01) begin
        sample_sum_DY <= sample_sum;
        if (polarity) begin
            INS_dout <= sample_sum_DY - median_sum_n;
        end
        else begin
            median_sum_n <= sample_sum;
            INS_dout <= INS_dout;
        end
    end
end

// 输出赋值
assign dout = INS_dout;
assign data_ready = demodu_dy[1:0] == 2'b01;

endmodule
