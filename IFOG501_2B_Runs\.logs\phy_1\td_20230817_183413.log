============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug 17 18:34:13 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1646 instances
RUN-0007 : 379 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2216 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1655 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1644 instances, 379 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7837, tnet num: 2214, tinst num: 1644, tnode num: 11075, tedge num: 13231.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.271335s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (97.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 539650
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1644.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 492567, overlap = 18
PHY-3002 : Step(2): len = 402930, overlap = 13.5
PHY-3002 : Step(3): len = 334698, overlap = 15.75
PHY-3002 : Step(4): len = 323673, overlap = 13.5
PHY-3002 : Step(5): len = 316922, overlap = 15.75
PHY-3002 : Step(6): len = 309477, overlap = 18
PHY-3002 : Step(7): len = 304502, overlap = 18
PHY-3002 : Step(8): len = 296360, overlap = 20.25
PHY-3002 : Step(9): len = 290010, overlap = 20.25
PHY-3002 : Step(10): len = 285645, overlap = 20.25
PHY-3002 : Step(11): len = 277807, overlap = 20.25
PHY-3002 : Step(12): len = 270637, overlap = 20.25
PHY-3002 : Step(13): len = 266372, overlap = 20.25
PHY-3002 : Step(14): len = 259999, overlap = 20.25
PHY-3002 : Step(15): len = 254648, overlap = 20.25
PHY-3002 : Step(16): len = 250419, overlap = 20.25
PHY-3002 : Step(17): len = 245176, overlap = 20.25
PHY-3002 : Step(18): len = 240364, overlap = 20.25
PHY-3002 : Step(19): len = 235678, overlap = 20.25
PHY-3002 : Step(20): len = 231349, overlap = 20.25
PHY-3002 : Step(21): len = 226478, overlap = 20.25
PHY-3002 : Step(22): len = 219950, overlap = 20.25
PHY-3002 : Step(23): len = 216767, overlap = 20.25
PHY-3002 : Step(24): len = 211885, overlap = 20.25
PHY-3002 : Step(25): len = 196968, overlap = 20.25
PHY-3002 : Step(26): len = 191909, overlap = 20.25
PHY-3002 : Step(27): len = 189970, overlap = 20.25
PHY-3002 : Step(28): len = 152578, overlap = 18
PHY-3002 : Step(29): len = 142847, overlap = 20.25
PHY-3002 : Step(30): len = 142024, overlap = 20.25
PHY-3002 : Step(31): len = 136642, overlap = 20.25
PHY-3002 : Step(32): len = 129049, overlap = 18
PHY-3002 : Step(33): len = 128092, overlap = 20.25
PHY-3002 : Step(34): len = 125948, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000152683
PHY-3002 : Step(35): len = 126788, overlap = 11.25
PHY-3002 : Step(36): len = 125747, overlap = 11.25
PHY-3002 : Step(37): len = 123951, overlap = 11.25
PHY-3002 : Step(38): len = 123156, overlap = 11.25
PHY-3002 : Step(39): len = 121893, overlap = 6.75
PHY-3002 : Step(40): len = 119281, overlap = 9
PHY-3002 : Step(41): len = 117514, overlap = 6.75
PHY-3002 : Step(42): len = 114185, overlap = 4.5
PHY-3002 : Step(43): len = 110343, overlap = 9
PHY-3002 : Step(44): len = 107777, overlap = 11.25
PHY-3002 : Step(45): len = 106717, overlap = 6.75
PHY-3002 : Step(46): len = 105635, overlap = 4.5
PHY-3002 : Step(47): len = 102488, overlap = 11.25
PHY-3002 : Step(48): len = 98760.4, overlap = 6.75
PHY-3002 : Step(49): len = 98103.9, overlap = 4.5
PHY-3002 : Step(50): len = 97076.4, overlap = 4.5
PHY-3002 : Step(51): len = 96261.5, overlap = 4.5
PHY-3002 : Step(52): len = 96161.9, overlap = 4.5
PHY-3002 : Step(53): len = 95441.3, overlap = 4.5
PHY-3002 : Step(54): len = 94567.4, overlap = 6.75
PHY-3002 : Step(55): len = 93907, overlap = 6.75
PHY-3002 : Step(56): len = 93145.6, overlap = 4.5
PHY-3002 : Step(57): len = 91300.6, overlap = 4.5
PHY-3002 : Step(58): len = 90524, overlap = 6.75
PHY-3002 : Step(59): len = 89087.3, overlap = 6.75
PHY-3002 : Step(60): len = 87140.7, overlap = 4.5
PHY-3002 : Step(61): len = 85183.1, overlap = 4.5
PHY-3002 : Step(62): len = 84670.9, overlap = 4.5
PHY-3002 : Step(63): len = 83396.8, overlap = 4.5
PHY-3002 : Step(64): len = 77040.7, overlap = 6.75
PHY-3002 : Step(65): len = 73989.2, overlap = 9
PHY-3002 : Step(66): len = 73454.9, overlap = 4.5
PHY-3002 : Step(67): len = 72627.3, overlap = 4.5
PHY-3002 : Step(68): len = 72306.8, overlap = 6.75
PHY-3002 : Step(69): len = 71970, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000305366
PHY-3002 : Step(70): len = 71932.4, overlap = 6.75
PHY-3002 : Step(71): len = 71973.3, overlap = 4.5
PHY-3002 : Step(72): len = 71957.9, overlap = 4.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000610732
PHY-3002 : Step(73): len = 71850.9, overlap = 4.5
PHY-3002 : Step(74): len = 71803, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006476s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (241.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062805s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(75): len = 74868.5, overlap = 3.375
PHY-3002 : Step(76): len = 73360.8, overlap = 3.5
PHY-3002 : Step(77): len = 71948.8, overlap = 3.8125
PHY-3002 : Step(78): len = 70011.5, overlap = 3.5
PHY-3002 : Step(79): len = 68350.9, overlap = 2.5
PHY-3002 : Step(80): len = 66614.9, overlap = 2.0625
PHY-3002 : Step(81): len = 64790.5, overlap = 2.8125
PHY-3002 : Step(82): len = 63501.9, overlap = 2.8125
PHY-3002 : Step(83): len = 61744, overlap = 4.8125
PHY-3002 : Step(84): len = 60016.6, overlap = 4.875
PHY-3002 : Step(85): len = 58436.6, overlap = 4.0625
PHY-3002 : Step(86): len = 57712.5, overlap = 3.625
PHY-3002 : Step(87): len = 56944.8, overlap = 4.125
PHY-3002 : Step(88): len = 55880.1, overlap = 3.875
PHY-3002 : Step(89): len = 54785.7, overlap = 5.5
PHY-3002 : Step(90): len = 54179.8, overlap = 7
PHY-3002 : Step(91): len = 53600, overlap = 6.9375
PHY-3002 : Step(92): len = 52913.1, overlap = 7.125
PHY-3002 : Step(93): len = 52367.4, overlap = 7.125
PHY-3002 : Step(94): len = 52073.1, overlap = 7.03125
PHY-3002 : Step(95): len = 51537.4, overlap = 6.59375
PHY-3002 : Step(96): len = 51252.2, overlap = 6.34375
PHY-3002 : Step(97): len = 50505.9, overlap = 6.15625
PHY-3002 : Step(98): len = 49985.5, overlap = 6
PHY-3002 : Step(99): len = 49165.4, overlap = 9.5
PHY-3002 : Step(100): len = 48650.2, overlap = 9.5625
PHY-3002 : Step(101): len = 48195.8, overlap = 10.0625
PHY-3002 : Step(102): len = 47658.2, overlap = 9.71875
PHY-3002 : Step(103): len = 47210.7, overlap = 10.2812
PHY-3002 : Step(104): len = 47192.8, overlap = 10.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00037635
PHY-3002 : Step(105): len = 47135.7, overlap = 10.2812
PHY-3002 : Step(106): len = 47004, overlap = 9.90625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000752701
PHY-3002 : Step(107): len = 46965, overlap = 9.96875
PHY-3002 : Step(108): len = 47068.8, overlap = 9.96875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058281s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.94372e-05
PHY-3002 : Step(109): len = 47409, overlap = 57.3438
PHY-3002 : Step(110): len = 47987.6, overlap = 48.3125
PHY-3002 : Step(111): len = 48580.6, overlap = 51.4062
PHY-3002 : Step(112): len = 49719.4, overlap = 48.0938
PHY-3002 : Step(113): len = 49956.4, overlap = 46
PHY-3002 : Step(114): len = 49628, overlap = 41.875
PHY-3002 : Step(115): len = 48956.2, overlap = 41.9062
PHY-3002 : Step(116): len = 48559.2, overlap = 42.7188
PHY-3002 : Step(117): len = 48634.6, overlap = 42.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000178874
PHY-3002 : Step(118): len = 48863.1, overlap = 42.1875
PHY-3002 : Step(119): len = 49329, overlap = 39.375
PHY-3002 : Step(120): len = 49911.2, overlap = 39.125
PHY-3002 : Step(121): len = 50064.1, overlap = 38.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000357749
PHY-3002 : Step(122): len = 50062.4, overlap = 37.6875
PHY-3002 : Step(123): len = 50218.8, overlap = 35.3125
PHY-3002 : Step(124): len = 50636.5, overlap = 34.2812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7837, tnet num: 2214, tinst num: 1644, tnode num: 11075, tedge num: 13231.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 90.53 peak overflow 4.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2216.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53472, over cnt = 240(0%), over = 1017, worst = 18
PHY-1001 : End global iterations;  0.072396s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.3%)

PHY-1001 : Congestion index: top1 = 46.29, top5 = 24.31, top10 = 15.50, top15 = 11.04.
PHY-1001 : End incremental global routing;  0.122678s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (76.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065581s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.218904s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (92.8%)

OPT-1001 : Current memory(MB): used = 216, reserve = 179, peak = 216.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1628/2216.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53472, over cnt = 240(0%), over = 1017, worst = 18
PHY-1002 : len = 61824, over cnt = 168(0%), over = 373, worst = 12
PHY-1002 : len = 63400, over cnt = 66(0%), over = 182, worst = 11
PHY-1002 : len = 65808, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 66336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.092782s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (134.7%)

PHY-1001 : Congestion index: top1 = 40.09, top5 = 24.94, top10 = 17.57, top15 = 13.04.
OPT-1001 : End congestion update;  0.135410s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (115.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067804s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.205621s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (114.0%)

OPT-1001 : Current memory(MB): used = 219, reserve = 182, peak = 219.
OPT-1001 : End physical optimization;  0.694148s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (119.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 379 LUT to BLE ...
SYN-4008 : Packed 379 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 81 SEQ with LUT/SLICE
SYN-4006 : 128 single LUT's are left
SYN-4006 : 722 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1101/1434 primitive instances ...
PHY-3001 : End packing;  0.049430s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 858 instances
RUN-1001 : 405 mslices, 404 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2041 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1487 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 856 instances, 809 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50811.8, Over = 60.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2039, tinst num: 856, tnode num: 8975, tedge num: 11623.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.326673s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (95.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.25603e-05
PHY-3002 : Step(125): len = 50443.6, overlap = 61.5
PHY-3002 : Step(126): len = 50088.2, overlap = 62.75
PHY-3002 : Step(127): len = 49973.4, overlap = 62
PHY-3002 : Step(128): len = 49972, overlap = 62.75
PHY-3002 : Step(129): len = 49903.6, overlap = 63.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.51207e-05
PHY-3002 : Step(130): len = 50092.3, overlap = 63.5
PHY-3002 : Step(131): len = 50558.1, overlap = 61.75
PHY-3002 : Step(132): len = 51314.2, overlap = 59
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.02414e-05
PHY-3002 : Step(133): len = 51828.4, overlap = 59.25
PHY-3002 : Step(134): len = 52845.9, overlap = 56
PHY-3002 : Step(135): len = 53734.8, overlap = 52
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.073486s wall, 0.031250s user + 0.140625s system = 0.171875s CPU (233.9%)

PHY-3001 : Trial Legalized: Len = 69238.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052334s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000420186
PHY-3002 : Step(136): len = 66081.7, overlap = 9.5
PHY-3002 : Step(137): len = 63569.3, overlap = 14
PHY-3002 : Step(138): len = 61723, overlap = 17
PHY-3002 : Step(139): len = 60596.9, overlap = 17.5
PHY-3002 : Step(140): len = 59908.1, overlap = 19
PHY-3002 : Step(141): len = 59458.3, overlap = 21.25
PHY-3002 : Step(142): len = 59148.4, overlap = 21.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000840373
PHY-3002 : Step(143): len = 59597.8, overlap = 20.5
PHY-3002 : Step(144): len = 59711.6, overlap = 20.25
PHY-3002 : Step(145): len = 59734, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00168075
PHY-3002 : Step(146): len = 59928.3, overlap = 19.5
PHY-3002 : Step(147): len = 60082.9, overlap = 19.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005009s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64990.4, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006198s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (252.1%)

PHY-3001 : 13 instances has been re-located, deltaX = 4, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 65088.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2039, tinst num: 856, tnode num: 8975, tedge num: 11623.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 56/2041.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71488, over cnt = 132(0%), over = 187, worst = 4
PHY-1002 : len = 72352, over cnt = 52(0%), over = 59, worst = 4
PHY-1002 : len = 72912, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 73024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118877s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (170.9%)

PHY-1001 : Congestion index: top1 = 32.00, top5 = 22.71, top10 = 17.73, top15 = 14.06.
PHY-1001 : End incremental global routing;  0.169966s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (147.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059032s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.259261s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (132.6%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1814/2041.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006787s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.00, top5 = 22.71, top10 = 17.73, top15 = 14.06.
OPT-1001 : End congestion update;  0.052509s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053645s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 818 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 856 instances, 809 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65126.8, Over = 0
PHY-3001 : End spreading;  0.005049s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (309.5%)

PHY-3001 : Final: Len = 65126.8, Over = 0
PHY-3001 : End incremental legalization;  0.036519s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.155588s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.4%)

OPT-1001 : Current memory(MB): used = 226, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049904s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1806/2041.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008564s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.00, top5 = 22.73, top10 = 17.75, top15 = 14.08.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050133s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.863249s wall, 0.906250s user + 0.031250s system = 0.937500s CPU (108.6%)

RUN-1003 : finish command "place" in  5.229347s wall, 8.671875s user + 2.562500s system = 11.234375s CPU (214.8%)

RUN-1004 : used memory is 205 MB, reserved memory is 168 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 858 instances
RUN-1001 : 405 mslices, 404 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2041 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1487 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2039, tinst num: 856, tnode num: 8975, tedge num: 11623.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 405 mslices, 404 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71008, over cnt = 129(0%), over = 184, worst = 5
PHY-1002 : len = 71824, over cnt = 48(0%), over = 57, worst = 3
PHY-1002 : len = 72416, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.136019s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.4%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 22.56, top10 = 17.63, top15 = 13.98.
PHY-1001 : End global routing;  0.185955s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 207, peak = 250.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 470, peak = 501.
PHY-1001 : End build detailed router design. 3.207787s wall, 3.203125s user + 0.015625s system = 3.218750s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33920, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.306992s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 533, reserve = 504, peak = 533.
PHY-1001 : End phase 1; 1.312612s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184160, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End initial routed; 1.071645s wall, 2.390625s user + 0.156250s system = 2.546875s CPU (237.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1804(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.148   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.365557s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.6%)

PHY-1001 : Current memory(MB): used = 538, reserve = 506, peak = 538.
PHY-1001 : End phase 2; 1.437289s wall, 2.765625s user + 0.156250s system = 2.921875s CPU (203.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184160, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015211s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184088, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028086s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (222.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184176, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.026991s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1804(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.148   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.368825s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (97.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.173250s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (108.2%)

PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End phase 3; 0.732350s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (104.5%)

PHY-1003 : Routed, final wirelength = 184176
PHY-1001 : Current memory(MB): used = 553, reserve = 521, peak = 553.
PHY-1001 : End export database. 0.011387s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.883458s wall, 8.187500s user + 0.203125s system = 8.390625s CPU (121.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2039, tinst num: 856, tnode num: 8975, tedge num: 11623.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.800514s wall, 9.093750s user + 0.218750s system = 9.312500s CPU (119.4%)

RUN-1004 : used memory is 504 MB, reserved memory is 472 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      838   out of  19600    4.28%
#reg                     1074   out of  19600    5.48%
#le                      1560
  #lut only               486   out of   1560   31.15%
  #reg only               722   out of   1560   46.28%
  #lut&reg                352   out of   1560   22.56%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    46
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1560   |612     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1157   |307     |133     |925     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |38     |32      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |538    |117     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |13      |0       |26      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |93     |26      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |318    |89      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |105    |93      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |29      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |17     |13      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |52     |51      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |67      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1451  
    #2          2       315   
    #3          3       100   
    #4          4        20   
    #5        5-10       80   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2039, tinst num: 856, tnode num: 8975, tedge num: 11623.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 856
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2041, pip num: 14831
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1334 valid insts, and 39396 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.136777s wall, 18.203125s user + 0.062500s system = 18.265625s CPU (582.3%)

RUN-1004 : used memory is 548 MB, reserved memory is 514 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230817_183413.log"
