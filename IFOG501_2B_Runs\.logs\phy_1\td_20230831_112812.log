============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug 31 11:28:12 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1620 instances
RUN-0007 : 373 luts, 979 seqs, 144 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2176 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1647 nets have 2 pins
RUN-1001 : 414 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1618 instances, 373 luts, 979 seqs, 219 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7670, tnet num: 2174, tinst num: 1618, tnode num: 10855, tedge num: 12917.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2174 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.275962s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (96.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540910
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1618.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 483756, overlap = 20.25
PHY-3002 : Step(2): len = 405420, overlap = 20.25
PHY-3002 : Step(3): len = 349062, overlap = 13.5
PHY-3002 : Step(4): len = 337669, overlap = 15.75
PHY-3002 : Step(5): len = 329310, overlap = 18
PHY-3002 : Step(6): len = 323723, overlap = 18
PHY-3002 : Step(7): len = 317425, overlap = 18
PHY-3002 : Step(8): len = 310698, overlap = 18
PHY-3002 : Step(9): len = 303942, overlap = 15.75
PHY-3002 : Step(10): len = 298148, overlap = 15.75
PHY-3002 : Step(11): len = 292918, overlap = 18
PHY-3002 : Step(12): len = 285714, overlap = 18
PHY-3002 : Step(13): len = 279751, overlap = 18
PHY-3002 : Step(14): len = 274469, overlap = 18
PHY-3002 : Step(15): len = 268803, overlap = 18
PHY-3002 : Step(16): len = 263090, overlap = 18
PHY-3002 : Step(17): len = 257584, overlap = 18
PHY-3002 : Step(18): len = 251938, overlap = 18
PHY-3002 : Step(19): len = 246966, overlap = 18
PHY-3002 : Step(20): len = 242007, overlap = 18
PHY-3002 : Step(21): len = 236720, overlap = 18
PHY-3002 : Step(22): len = 231819, overlap = 18
PHY-3002 : Step(23): len = 227132, overlap = 18
PHY-3002 : Step(24): len = 221865, overlap = 18
PHY-3002 : Step(25): len = 215801, overlap = 18
PHY-3002 : Step(26): len = 211315, overlap = 18
PHY-3002 : Step(27): len = 207777, overlap = 18
PHY-3002 : Step(28): len = 201517, overlap = 18
PHY-3002 : Step(29): len = 196423, overlap = 18
PHY-3002 : Step(30): len = 193195, overlap = 18
PHY-3002 : Step(31): len = 188478, overlap = 18
PHY-3002 : Step(32): len = 181361, overlap = 18
PHY-3002 : Step(33): len = 177322, overlap = 18
PHY-3002 : Step(34): len = 174756, overlap = 18
PHY-3002 : Step(35): len = 160327, overlap = 18
PHY-3002 : Step(36): len = 149622, overlap = 20.25
PHY-3002 : Step(37): len = 148326, overlap = 20.25
PHY-3002 : Step(38): len = 134537, overlap = 18
PHY-3002 : Step(39): len = 105954, overlap = 18
PHY-3002 : Step(40): len = 102188, overlap = 15.75
PHY-3002 : Step(41): len = 100788, overlap = 18
PHY-3002 : Step(42): len = 99700.5, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000129014
PHY-3002 : Step(43): len = 100942, overlap = 6.75
PHY-3002 : Step(44): len = 100790, overlap = 4.5
PHY-3002 : Step(45): len = 98898.9, overlap = 9
PHY-3002 : Step(46): len = 97982, overlap = 15.75
PHY-3002 : Step(47): len = 95537.6, overlap = 13.5
PHY-3002 : Step(48): len = 94227.1, overlap = 6.75
PHY-3002 : Step(49): len = 92464, overlap = 9
PHY-3002 : Step(50): len = 91874.9, overlap = 9
PHY-3002 : Step(51): len = 89721.6, overlap = 15.75
PHY-3002 : Step(52): len = 87701, overlap = 13.5
PHY-3002 : Step(53): len = 87092.7, overlap = 6.75
PHY-3002 : Step(54): len = 85921, overlap = 9.125
PHY-3002 : Step(55): len = 83136, overlap = 16.6875
PHY-3002 : Step(56): len = 81869.9, overlap = 12.3125
PHY-3002 : Step(57): len = 79815.6, overlap = 12.375
PHY-3002 : Step(58): len = 79053.9, overlap = 14.4375
PHY-3002 : Step(59): len = 77139.7, overlap = 16.375
PHY-3002 : Step(60): len = 76362.3, overlap = 14.0625
PHY-3002 : Step(61): len = 76170.5, overlap = 7.3125
PHY-3002 : Step(62): len = 75706.7, overlap = 9.5625
PHY-3002 : Step(63): len = 73939.4, overlap = 11.8125
PHY-3002 : Step(64): len = 71387.4, overlap = 11.3125
PHY-3002 : Step(65): len = 70738.1, overlap = 9.125
PHY-3002 : Step(66): len = 70198, overlap = 9.125
PHY-3002 : Step(67): len = 68594.5, overlap = 9.3125
PHY-3002 : Step(68): len = 67409.4, overlap = 11.5625
PHY-3002 : Step(69): len = 65366.3, overlap = 7.0625
PHY-3002 : Step(70): len = 62889.4, overlap = 9.4375
PHY-3002 : Step(71): len = 61969.3, overlap = 11.625
PHY-3002 : Step(72): len = 61258, overlap = 11.625
PHY-3002 : Step(73): len = 60673.8, overlap = 15.875
PHY-3002 : Step(74): len = 60419.5, overlap = 11.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000258029
PHY-3002 : Step(75): len = 60435, overlap = 9.125
PHY-3002 : Step(76): len = 60505.7, overlap = 9.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000516057
PHY-3002 : Step(77): len = 60369.5, overlap = 9
PHY-3002 : Step(78): len = 60348.4, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006383s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (244.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2174 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062401s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(79): len = 64554.4, overlap = 3.125
PHY-3002 : Step(80): len = 62809.1, overlap = 2.75
PHY-3002 : Step(81): len = 61671, overlap = 3
PHY-3002 : Step(82): len = 60714.7, overlap = 2.875
PHY-3002 : Step(83): len = 59199.7, overlap = 2.375
PHY-3002 : Step(84): len = 58229.1, overlap = 2.6875
PHY-3002 : Step(85): len = 57249.5, overlap = 2.9375
PHY-3002 : Step(86): len = 56056.9, overlap = 2.6875
PHY-3002 : Step(87): len = 54992.9, overlap = 2.6875
PHY-3002 : Step(88): len = 53841.8, overlap = 2.6875
PHY-3002 : Step(89): len = 52603, overlap = 4.53125
PHY-3002 : Step(90): len = 51699, overlap = 7.375
PHY-3002 : Step(91): len = 51101.2, overlap = 8.96875
PHY-3002 : Step(92): len = 50551.4, overlap = 9.75
PHY-3002 : Step(93): len = 49692.5, overlap = 12.125
PHY-3002 : Step(94): len = 49633, overlap = 12.5312
PHY-3002 : Step(95): len = 49339.8, overlap = 12.9062
PHY-3002 : Step(96): len = 48626.9, overlap = 15.1562
PHY-3002 : Step(97): len = 48524.7, overlap = 15.2188
PHY-3002 : Step(98): len = 48231, overlap = 15.6562
PHY-3002 : Step(99): len = 48079.8, overlap = 16.0312
PHY-3002 : Step(100): len = 48031, overlap = 16.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000884165
PHY-3002 : Step(101): len = 47861.2, overlap = 16
PHY-3002 : Step(102): len = 47926.1, overlap = 15.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00176833
PHY-3002 : Step(103): len = 47729, overlap = 15.2188
PHY-3002 : Step(104): len = 47803.3, overlap = 15.1562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2174 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062321s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.0119e-05
PHY-3002 : Step(105): len = 48462.8, overlap = 62.7812
PHY-3002 : Step(106): len = 48863.2, overlap = 62.1562
PHY-3002 : Step(107): len = 49866, overlap = 50.4375
PHY-3002 : Step(108): len = 50280.1, overlap = 48.6562
PHY-3002 : Step(109): len = 50068.9, overlap = 43.5625
PHY-3002 : Step(110): len = 49651.2, overlap = 44.2188
PHY-3002 : Step(111): len = 49589, overlap = 41.4688
PHY-3002 : Step(112): len = 49324.4, overlap = 42.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000180238
PHY-3002 : Step(113): len = 49764, overlap = 40.625
PHY-3002 : Step(114): len = 49960.3, overlap = 39.4688
PHY-3002 : Step(115): len = 50233.7, overlap = 38.4688
PHY-3002 : Step(116): len = 50377.6, overlap = 37.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000360476
PHY-3002 : Step(117): len = 50383.2, overlap = 35.5625
PHY-3002 : Step(118): len = 50952.7, overlap = 31.5
PHY-3002 : Step(119): len = 51112.2, overlap = 31.375
PHY-3002 : Step(120): len = 50988.4, overlap = 30.7188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7670, tnet num: 2174, tinst num: 1618, tnode num: 10855, tedge num: 12917.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 88.53 peak overflow 3.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2176.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53000, over cnt = 226(0%), over = 901, worst = 20
PHY-1001 : End global iterations;  0.070353s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (133.3%)

PHY-1001 : Congestion index: top1 = 41.12, top5 = 24.03, top10 = 15.27, top15 = 10.76.
PHY-1001 : End incremental global routing;  0.121448s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (128.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2174 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067733s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (92.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.218065s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (114.6%)

OPT-1001 : Current memory(MB): used = 214, reserve = 177, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1599/2176.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53000, over cnt = 226(0%), over = 901, worst = 20
PHY-1002 : len = 57648, over cnt = 166(0%), over = 471, worst = 15
PHY-1002 : len = 60192, over cnt = 88(0%), over = 243, worst = 15
PHY-1002 : len = 63992, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 64024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.082041s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (114.3%)

PHY-1001 : Congestion index: top1 = 36.29, top5 = 24.46, top10 = 16.98, top15 = 12.40.
OPT-1001 : End congestion update;  0.124199s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (113.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2174 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057546s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.184149s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (110.3%)

OPT-1001 : Current memory(MB): used = 217, reserve = 180, peak = 217.
OPT-1001 : End physical optimization;  0.679297s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (105.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 100 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 690 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1063/1389 primitive instances ...
PHY-3001 : End packing;  0.050903s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 832 instances
RUN-1001 : 392 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2001 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1479 nets have 2 pins
RUN-1001 : 407 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 830 instances, 783 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50955.2, Over = 58.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6441, tnet num: 1999, tinst num: 830, tnode num: 8741, tedge num: 11283.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.312105s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.29846e-05
PHY-3002 : Step(121): len = 50578.6, overlap = 59.25
PHY-3002 : Step(122): len = 50217.1, overlap = 60.5
PHY-3002 : Step(123): len = 50114.3, overlap = 59.25
PHY-3002 : Step(124): len = 49991.5, overlap = 59.75
PHY-3002 : Step(125): len = 50098.2, overlap = 61.75
PHY-3002 : Step(126): len = 49925.1, overlap = 62.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.59693e-05
PHY-3002 : Step(127): len = 50294.7, overlap = 59.5
PHY-3002 : Step(128): len = 50604.2, overlap = 58.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.19385e-05
PHY-3002 : Step(129): len = 51151.7, overlap = 57.25
PHY-3002 : Step(130): len = 52621.5, overlap = 51.75
PHY-3002 : Step(131): len = 53111.4, overlap = 50.75
PHY-3002 : Step(132): len = 53478.1, overlap = 48.5
PHY-3002 : Step(133): len = 53493.8, overlap = 45.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.099266s wall, 0.015625s user + 0.140625s system = 0.156250s CPU (157.4%)

PHY-3001 : Trial Legalized: Len = 65362.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048632s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000460939
PHY-3002 : Step(134): len = 63141.9, overlap = 5.75
PHY-3002 : Step(135): len = 61605.3, overlap = 12.25
PHY-3002 : Step(136): len = 60247.9, overlap = 16.75
PHY-3002 : Step(137): len = 59394, overlap = 21.25
PHY-3002 : Step(138): len = 58723.4, overlap = 23
PHY-3002 : Step(139): len = 58455.8, overlap = 22.75
PHY-3002 : Step(140): len = 58262.2, overlap = 25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000921878
PHY-3002 : Step(141): len = 58755.3, overlap = 23.25
PHY-3002 : Step(142): len = 58941.1, overlap = 21.25
PHY-3002 : Step(143): len = 58898, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00184376
PHY-3002 : Step(144): len = 59122.4, overlap = 19.5
PHY-3002 : Step(145): len = 59122.4, overlap = 19.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005047s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63832.5, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005704s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (273.9%)

PHY-3001 : 10 instances has been re-located, deltaX = 4, deltaY = 7, maxDist = 2.
PHY-3001 : Final: Len = 63924.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6441, tnet num: 1999, tinst num: 830, tnode num: 8741, tedge num: 11283.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 121/2001.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70624, over cnt = 143(0%), over = 221, worst = 7
PHY-1002 : len = 71352, over cnt = 78(0%), over = 110, worst = 7
PHY-1002 : len = 72528, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 72696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130297s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (131.9%)

PHY-1001 : Congestion index: top1 = 33.51, top5 = 23.16, top10 = 18.09, top15 = 14.19.
PHY-1001 : End incremental global routing;  0.181263s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (120.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063350s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.274977s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (113.6%)

OPT-1001 : Current memory(MB): used = 220, reserve = 183, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2001.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006298s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (248.1%)

PHY-1001 : Congestion index: top1 = 33.51, top5 = 23.16, top10 = 18.09, top15 = 14.19.
OPT-1001 : End congestion update;  0.050972s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049532s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.102417s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (106.8%)

OPT-1001 : Current memory(MB): used = 222, reserve = 185, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048012s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2001.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006265s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.51, top5 = 23.16, top10 = 18.09, top15 = 14.19.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048673s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (128.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.137931
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.824169s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (106.2%)

RUN-1003 : finish command "place" in  5.080914s wall, 8.140625s user + 2.359375s system = 10.500000s CPU (206.7%)

RUN-1004 : used memory is 198 MB, reserved memory is 161 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 832 instances
RUN-1001 : 392 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2001 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1479 nets have 2 pins
RUN-1001 : 407 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6441, tnet num: 1999, tinst num: 830, tnode num: 8741, tedge num: 11283.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 392 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69360, over cnt = 147(0%), over = 238, worst = 6
PHY-1002 : len = 70224, over cnt = 82(0%), over = 123, worst = 6
PHY-1002 : len = 71832, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 71976, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132522s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (176.9%)

PHY-1001 : Congestion index: top1 = 33.23, top5 = 23.02, top10 = 17.90, top15 = 14.04.
PHY-1001 : End global routing;  0.183907s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (152.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 205, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 467, peak = 500.
PHY-1001 : End build detailed router design. 3.217909s wall, 3.171875s user + 0.046875s system = 3.218750s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33696, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.291689s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.297406s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177528, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End initial routed; 0.997215s wall, 1.906250s user + 0.171875s system = 2.078125s CPU (208.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1771(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.597   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.364423s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.6%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 1.361722s wall, 2.265625s user + 0.171875s system = 2.437500s CPU (179.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177528, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016072s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177576, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031939s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177584, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022541s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (69.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1771(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.597   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.368925s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.166377s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (93.9%)

PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End phase 3; 0.727100s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (98.9%)

PHY-1003 : Routed, final wirelength = 177584
PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End export database. 0.009859s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.796140s wall, 7.656250s user + 0.218750s system = 7.875000s CPU (115.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6441, tnet num: 1999, tinst num: 830, tnode num: 8741, tedge num: 11283.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.700350s wall, 8.625000s user + 0.250000s system = 8.875000s CPU (115.3%)

RUN-1004 : used memory is 502 MB, reserved memory is 470 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      816   out of  19600    4.16%
#reg                     1047   out of  19600    5.34%
#le                      1506
  #lut only               459   out of   1506   30.48%
  #reg only               690   out of   1506   45.82%
  #lut&reg                357   out of   1506   23.71%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         460
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1506   |597     |219     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1104   |291     |126     |897     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |538    |131     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |160    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |23     |11      |0       |23      |0       |0       |
|    integ                   |Integration                                      |139    |16      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |65     |27      |14      |61      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |82      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |22     |17      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |109    |99      |7       |53      |0       |0       |
|    U0                      |speed_select_Tx                                  |33     |26      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |23     |20      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |53     |53      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1443  
    #2          2       280   
    #3          3       105   
    #4          4        22   
    #5        5-10       78   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6441, tnet num: 1999, tinst num: 830, tnode num: 8741, tedge num: 11283.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1999 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 830
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2001, pip num: 14396
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1305 valid insts, and 38108 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.968623s wall, 17.671875s user + 0.046875s system = 17.718750s CPU (596.9%)

RUN-1004 : used memory is 545 MB, reserved memory is 510 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230831_112812.log"
