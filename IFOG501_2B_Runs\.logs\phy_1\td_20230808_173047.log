============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Aug  8 17:30:47 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1631 instances
RUN-0007 : 376 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2201 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1641 nets have 2 pins
RUN-1001 : 446 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1629 instances, 376 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7804, tnet num: 2199, tinst num: 1629, tnode num: 11044, tedge num: 13195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.296717s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (94.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 581126
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1629.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 551651, overlap = 20.25
PHY-3002 : Step(2): len = 454809, overlap = 20.25
PHY-3002 : Step(3): len = 384725, overlap = 15.75
PHY-3002 : Step(4): len = 370980, overlap = 11.25
PHY-3002 : Step(5): len = 358354, overlap = 15.75
PHY-3002 : Step(6): len = 345512, overlap = 15.75
PHY-3002 : Step(7): len = 339129, overlap = 15.75
PHY-3002 : Step(8): len = 327614, overlap = 15.75
PHY-3002 : Step(9): len = 319382, overlap = 20.25
PHY-3002 : Step(10): len = 311491, overlap = 20.25
PHY-3002 : Step(11): len = 305097, overlap = 20.25
PHY-3002 : Step(12): len = 294867, overlap = 20.25
PHY-3002 : Step(13): len = 289626, overlap = 20.25
PHY-3002 : Step(14): len = 282432, overlap = 20.25
PHY-3002 : Step(15): len = 276494, overlap = 20.25
PHY-3002 : Step(16): len = 269830, overlap = 20.25
PHY-3002 : Step(17): len = 265468, overlap = 20.25
PHY-3002 : Step(18): len = 256321, overlap = 20.25
PHY-3002 : Step(19): len = 252092, overlap = 20.25
PHY-3002 : Step(20): len = 247116, overlap = 20.25
PHY-3002 : Step(21): len = 239458, overlap = 20.25
PHY-3002 : Step(22): len = 233280, overlap = 20.25
PHY-3002 : Step(23): len = 230615, overlap = 20.25
PHY-3002 : Step(24): len = 221700, overlap = 20.25
PHY-3002 : Step(25): len = 214006, overlap = 20.25
PHY-3002 : Step(26): len = 210953, overlap = 20.25
PHY-3002 : Step(27): len = 207039, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000146557
PHY-3002 : Step(28): len = 207073, overlap = 15.75
PHY-3002 : Step(29): len = 204662, overlap = 13.5
PHY-3002 : Step(30): len = 202689, overlap = 15.75
PHY-3002 : Step(31): len = 193329, overlap = 11.25
PHY-3002 : Step(32): len = 186658, overlap = 13.5
PHY-3002 : Step(33): len = 183005, overlap = 6.75
PHY-3002 : Step(34): len = 179710, overlap = 13.5
PHY-3002 : Step(35): len = 177829, overlap = 9
PHY-3002 : Step(36): len = 171262, overlap = 9
PHY-3002 : Step(37): len = 167509, overlap = 6.75
PHY-3002 : Step(38): len = 164831, overlap = 9
PHY-3002 : Step(39): len = 161535, overlap = 11.25
PHY-3002 : Step(40): len = 153147, overlap = 9
PHY-3002 : Step(41): len = 151309, overlap = 6.75
PHY-3002 : Step(42): len = 148510, overlap = 6.75
PHY-3002 : Step(43): len = 146798, overlap = 6.75
PHY-3002 : Step(44): len = 143757, overlap = 11.25
PHY-3002 : Step(45): len = 142464, overlap = 7.0625
PHY-3002 : Step(46): len = 137983, overlap = 6.75
PHY-3002 : Step(47): len = 135905, overlap = 9
PHY-3002 : Step(48): len = 133572, overlap = 9
PHY-3002 : Step(49): len = 130585, overlap = 9
PHY-3002 : Step(50): len = 123921, overlap = 11.25
PHY-3002 : Step(51): len = 123058, overlap = 6.75
PHY-3002 : Step(52): len = 120635, overlap = 9
PHY-3002 : Step(53): len = 114724, overlap = 11.25
PHY-3002 : Step(54): len = 113083, overlap = 6.75
PHY-3002 : Step(55): len = 112012, overlap = 6.75
PHY-3002 : Step(56): len = 109857, overlap = 6.75
PHY-3002 : Step(57): len = 108549, overlap = 11.25
PHY-3002 : Step(58): len = 103380, overlap = 11.25
PHY-3002 : Step(59): len = 97522.2, overlap = 13.625
PHY-3002 : Step(60): len = 95568.5, overlap = 13.625
PHY-3002 : Step(61): len = 94461, overlap = 9.125
PHY-3002 : Step(62): len = 93133.9, overlap = 11.4375
PHY-3002 : Step(63): len = 92712.8, overlap = 6.875
PHY-3002 : Step(64): len = 92588.6, overlap = 6.75
PHY-3002 : Step(65): len = 92046, overlap = 6.75
PHY-3002 : Step(66): len = 90366, overlap = 11.25
PHY-3002 : Step(67): len = 87961.8, overlap = 13.5
PHY-3002 : Step(68): len = 86875, overlap = 6.75
PHY-3002 : Step(69): len = 84967.7, overlap = 9
PHY-3002 : Step(70): len = 84068, overlap = 9
PHY-3002 : Step(71): len = 82002, overlap = 11.25
PHY-3002 : Step(72): len = 79386.9, overlap = 9.1875
PHY-3002 : Step(73): len = 77589.2, overlap = 9
PHY-3002 : Step(74): len = 76489.9, overlap = 9
PHY-3002 : Step(75): len = 74614.5, overlap = 11.4375
PHY-3002 : Step(76): len = 74136.4, overlap = 11.25
PHY-3002 : Step(77): len = 73443.1, overlap = 7
PHY-3002 : Step(78): len = 72497.5, overlap = 7
PHY-3002 : Step(79): len = 70643, overlap = 7.1875
PHY-3002 : Step(80): len = 70126.7, overlap = 9.25
PHY-3002 : Step(81): len = 69059.2, overlap = 11.5625
PHY-3002 : Step(82): len = 67190.5, overlap = 11.6875
PHY-3002 : Step(83): len = 65790.4, overlap = 11.25
PHY-3002 : Step(84): len = 65465.8, overlap = 9
PHY-3002 : Step(85): len = 64869.9, overlap = 9
PHY-3002 : Step(86): len = 64250.9, overlap = 6.75
PHY-3002 : Step(87): len = 63171.8, overlap = 4.5
PHY-3002 : Step(88): len = 62920.2, overlap = 6.75
PHY-3002 : Step(89): len = 62598.9, overlap = 9
PHY-3002 : Step(90): len = 62296.6, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000293114
PHY-3002 : Step(91): len = 62225.2, overlap = 6.75
PHY-3002 : Step(92): len = 62164.7, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000586228
PHY-3002 : Step(93): len = 62041.8, overlap = 6.75
PHY-3002 : Step(94): len = 61962.9, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006593s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068646s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00154763
PHY-3002 : Step(95): len = 63440.9, overlap = 4.125
PHY-3002 : Step(96): len = 62427.4, overlap = 3.78125
PHY-3002 : Step(97): len = 61582.6, overlap = 3.78125
PHY-3002 : Step(98): len = 60505.5, overlap = 3.8125
PHY-3002 : Step(99): len = 58762.2, overlap = 2.84375
PHY-3002 : Step(100): len = 57758.6, overlap = 2.90625
PHY-3002 : Step(101): len = 57123.5, overlap = 3.8125
PHY-3002 : Step(102): len = 55920.4, overlap = 4
PHY-3002 : Step(103): len = 55137.7, overlap = 3.84375
PHY-3002 : Step(104): len = 54675.9, overlap = 3.71875
PHY-3002 : Step(105): len = 54469.3, overlap = 3.71875
PHY-3002 : Step(106): len = 54050.7, overlap = 3.34375
PHY-3002 : Step(107): len = 53861.5, overlap = 2.65625
PHY-3002 : Step(108): len = 53578.2, overlap = 2.59375
PHY-3002 : Step(109): len = 53251.3, overlap = 2.59375
PHY-3002 : Step(110): len = 52991.4, overlap = 2.59375
PHY-3002 : Step(111): len = 52437.4, overlap = 3.625
PHY-3002 : Step(112): len = 52230.7, overlap = 3.8125
PHY-3002 : Step(113): len = 51877.5, overlap = 3.8125
PHY-3002 : Step(114): len = 51507.5, overlap = 4.6875
PHY-3002 : Step(115): len = 51466, overlap = 4.6875
PHY-3002 : Step(116): len = 51004.8, overlap = 5.125
PHY-3002 : Step(117): len = 50383.1, overlap = 7
PHY-3002 : Step(118): len = 49788.6, overlap = 9.0625
PHY-3002 : Step(119): len = 48965.6, overlap = 10.3438
PHY-3002 : Step(120): len = 48591.6, overlap = 9.96875
PHY-3002 : Step(121): len = 47879.5, overlap = 9.09375
PHY-3002 : Step(122): len = 47073, overlap = 8.40625
PHY-3002 : Step(123): len = 46685.2, overlap = 8.40625
PHY-3002 : Step(124): len = 46522.3, overlap = 8.90625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00309527
PHY-3002 : Step(125): len = 46379.1, overlap = 8.90625
PHY-3002 : Step(126): len = 46396.5, overlap = 8.90625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00619054
PHY-3002 : Step(127): len = 46327, overlap = 8.96875
PHY-3002 : Step(128): len = 46327, overlap = 8.96875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063700s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.95998e-05
PHY-3002 : Step(129): len = 47274.1, overlap = 57
PHY-3002 : Step(130): len = 47305, overlap = 56.2188
PHY-3002 : Step(131): len = 47455.3, overlap = 54.75
PHY-3002 : Step(132): len = 47782.3, overlap = 54.6875
PHY-3002 : Step(133): len = 47530.2, overlap = 54.0625
PHY-3002 : Step(134): len = 47634.2, overlap = 53.75
PHY-3002 : Step(135): len = 47620.6, overlap = 53.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0001792
PHY-3002 : Step(136): len = 47274.3, overlap = 52.7188
PHY-3002 : Step(137): len = 47334.7, overlap = 53.2188
PHY-3002 : Step(138): len = 47451.5, overlap = 53.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000358399
PHY-3002 : Step(139): len = 48532.6, overlap = 48.0312
PHY-3002 : Step(140): len = 49205.9, overlap = 46.5312
PHY-3002 : Step(141): len = 50970.3, overlap = 35.2188
PHY-3002 : Step(142): len = 50871.2, overlap = 34.7188
PHY-3002 : Step(143): len = 50728.1, overlap = 33.1875
PHY-3002 : Step(144): len = 50756.7, overlap = 31.7188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7804, tnet num: 2199, tinst num: 1629, tnode num: 11044, tedge num: 13195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.62 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2201.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54216, over cnt = 254(0%), over = 988, worst = 25
PHY-1001 : End global iterations;  0.079034s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.8%)

PHY-1001 : Congestion index: top1 = 43.79, top5 = 24.45, top10 = 15.97, top15 = 11.38.
PHY-1001 : End incremental global routing;  0.129754s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (96.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070064s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.231023s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1676/2201.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54216, over cnt = 254(0%), over = 988, worst = 25
PHY-1002 : len = 60560, over cnt = 164(0%), over = 394, worst = 13
PHY-1002 : len = 65080, over cnt = 35(0%), over = 50, worst = 5
PHY-1002 : len = 65632, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 66048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113901s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (123.5%)

PHY-1001 : Congestion index: top1 = 38.73, top5 = 24.78, top10 = 17.69, top15 = 13.14.
OPT-1001 : End congestion update;  0.157832s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (118.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059842s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.220271s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (113.5%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : End physical optimization;  0.735468s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (104.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 115 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1078/1406 primitive instances ...
PHY-3001 : End packing;  0.052926s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 835 instances
RUN-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 833 instances, 786 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50780, Over = 53
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 833, tnode num: 8898, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.311816s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (95.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.55945e-05
PHY-3002 : Step(145): len = 50386, overlap = 52.75
PHY-3002 : Step(146): len = 50440.2, overlap = 54
PHY-3002 : Step(147): len = 50171.4, overlap = 55.75
PHY-3002 : Step(148): len = 50024.6, overlap = 54.75
PHY-3002 : Step(149): len = 49903.9, overlap = 54.25
PHY-3002 : Step(150): len = 49928.7, overlap = 56
PHY-3002 : Step(151): len = 49816.1, overlap = 56.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.1189e-05
PHY-3002 : Step(152): len = 49877.3, overlap = 56.75
PHY-3002 : Step(153): len = 49962.5, overlap = 57
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.28237e-05
PHY-3002 : Step(154): len = 50364.4, overlap = 56.25
PHY-3002 : Step(155): len = 51274.2, overlap = 55.75
PHY-3002 : Step(156): len = 52460.1, overlap = 54.5
PHY-3002 : Step(157): len = 52682.7, overlap = 53.5
PHY-3002 : Step(158): len = 52922.7, overlap = 52.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.105186s wall, 0.140625s user + 0.171875s system = 0.312500s CPU (297.1%)

PHY-3001 : Trial Legalized: Len = 65905
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052509s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000346174
PHY-3002 : Step(159): len = 63298.2, overlap = 5
PHY-3002 : Step(160): len = 61443.5, overlap = 11
PHY-3002 : Step(161): len = 59649.7, overlap = 17.75
PHY-3002 : Step(162): len = 58570.2, overlap = 19.75
PHY-3002 : Step(163): len = 58159.5, overlap = 23.25
PHY-3002 : Step(164): len = 57834.7, overlap = 24.5
PHY-3002 : Step(165): len = 57619.6, overlap = 25.25
PHY-3002 : Step(166): len = 57496.9, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000692347
PHY-3002 : Step(167): len = 58129.1, overlap = 24
PHY-3002 : Step(168): len = 58349.7, overlap = 23.75
PHY-3002 : Step(169): len = 58325.7, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00138469
PHY-3002 : Step(170): len = 58566.7, overlap = 23.5
PHY-3002 : Step(171): len = 58734.5, overlap = 23.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005328s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63294.5, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006237s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 16 instances has been re-located, deltaX = 5, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 63354.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 833, tnode num: 8898, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 67/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69928, over cnt = 139(0%), over = 207, worst = 7
PHY-1002 : len = 70712, over cnt = 87(0%), over = 100, worst = 3
PHY-1002 : len = 71792, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 72048, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.143159s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (98.2%)

PHY-1001 : Congestion index: top1 = 32.00, top5 = 22.72, top10 = 18.01, top15 = 14.22.
PHY-1001 : End incremental global routing;  0.195580s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (95.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064184s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.290773s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (96.7%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1799/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006261s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.00, top5 = 22.72, top10 = 18.01, top15 = 14.22.
OPT-1001 : End congestion update;  0.056893s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053473s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 795 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 833 instances, 786 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63363, Over = 0
PHY-3001 : End spreading;  0.005222s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63363, Over = 0
PHY-3001 : End incremental legalization;  0.035419s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.2%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.159156s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (98.2%)

OPT-1001 : Current memory(MB): used = 227, reserve = 191, peak = 227.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051456s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1795/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008462s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (184.7%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.74, top10 = 18.02, top15 = 14.22.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061305s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.917605s wall, 0.875000s user + 0.031250s system = 0.906250s CPU (98.8%)

RUN-1003 : finish command "place" in  6.056798s wall, 10.218750s user + 3.765625s system = 13.984375s CPU (230.9%)

RUN-1004 : used memory is 204 MB, reserved memory is 167 MB, peak memory is 227 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 835 instances
RUN-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 833, tnode num: 8898, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 393 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69208, over cnt = 147(0%), over = 215, worst = 7
PHY-1002 : len = 70112, over cnt = 91(0%), over = 106, worst = 3
PHY-1002 : len = 71368, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 71552, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148260s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (126.5%)

PHY-1001 : Congestion index: top1 = 32.07, top5 = 22.79, top10 = 17.97, top15 = 14.13.
PHY-1001 : End global routing;  0.197619s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (126.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 207, peak = 250.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 469, peak = 501.
PHY-1001 : End build detailed router design. 3.325446s wall, 3.203125s user + 0.109375s system = 3.312500s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35240, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.391392s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (102.2%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 535.
PHY-1001 : End phase 1; 1.397309s wall, 1.390625s user + 0.031250s system = 1.421875s CPU (101.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183728, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End initial routed; 1.103816s wall, 2.078125s user + 0.125000s system = 2.203125s CPU (199.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.466   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.382343s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.1%)

PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End phase 2; 1.486250s wall, 2.453125s user + 0.125000s system = 2.578125s CPU (173.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183728, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016070s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 183616, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026856s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (116.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 183648, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.028579s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (109.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 183680, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.018794s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (83.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.466   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.386850s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.179176s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.9%)

PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End phase 3; 0.786647s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (99.3%)

PHY-1003 : Routed, final wirelength = 183680
PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End export database. 0.010443s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.192909s wall, 8.031250s user + 0.265625s system = 8.296875s CPU (115.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 833, tnode num: 8898, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.131548s wall, 8.984375s user + 0.296875s system = 9.281250s CPU (114.1%)

RUN-1004 : used memory is 527 MB, reserved memory is 501 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      825   out of  19600    4.21%
#reg                     1074   out of  19600    5.48%
#le                      1527
  #lut only               453   out of   1527   29.67%
  #reg only               702   out of   1527   45.97%
  #lut&reg                372   out of   1527   24.36%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         467
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    39
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1527   |604     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1131   |305     |128     |919     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |42     |36      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |522    |107     |53      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |13      |0       |28      |0       |0       |
|    integ                   |Integration                                      |139    |23      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |89     |23      |21      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |99      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |22     |17      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |94     |83      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |18     |16      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |39     |39      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |219    |174     |45      |78      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1445  
    #2          2       308   
    #3          3       105   
    #4          4        25   
    #5        5-10       76   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 833, tnode num: 8898, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 833
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2033, pip num: 14774
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1301 valid insts, and 39178 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.238063s wall, 18.812500s user + 0.031250s system = 18.843750s CPU (581.9%)

RUN-1004 : used memory is 552 MB, reserved memory is 521 MB, peak memory is 673 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230808_173047.log"
