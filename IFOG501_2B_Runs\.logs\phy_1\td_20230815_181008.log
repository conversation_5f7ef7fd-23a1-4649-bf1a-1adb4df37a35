============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Aug 15 18:10:08 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1623 instances
RUN-0007 : 368 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2193 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1635 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1621 instances, 368 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7773, tnet num: 2191, tinst num: 1621, tnode num: 11013, tedge num: 13149.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.287090s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 595720
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1621.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 474164, overlap = 20.25
PHY-3002 : Step(2): len = 437307, overlap = 20.25
PHY-3002 : Step(3): len = 393555, overlap = 20.25
PHY-3002 : Step(4): len = 364075, overlap = 18
PHY-3002 : Step(5): len = 353739, overlap = 20.25
PHY-3002 : Step(6): len = 340276, overlap = 20.25
PHY-3002 : Step(7): len = 324848, overlap = 18
PHY-3002 : Step(8): len = 315753, overlap = 18
PHY-3002 : Step(9): len = 309711, overlap = 18
PHY-3002 : Step(10): len = 298134, overlap = 20.25
PHY-3002 : Step(11): len = 290136, overlap = 20.25
PHY-3002 : Step(12): len = 284319, overlap = 20.25
PHY-3002 : Step(13): len = 277686, overlap = 20.25
PHY-3002 : Step(14): len = 268072, overlap = 20.25
PHY-3002 : Step(15): len = 263270, overlap = 20.25
PHY-3002 : Step(16): len = 256935, overlap = 20.25
PHY-3002 : Step(17): len = 250197, overlap = 20.25
PHY-3002 : Step(18): len = 244055, overlap = 20.25
PHY-3002 : Step(19): len = 239843, overlap = 20.25
PHY-3002 : Step(20): len = 231338, overlap = 20.25
PHY-3002 : Step(21): len = 226656, overlap = 20.25
PHY-3002 : Step(22): len = 222687, overlap = 20.25
PHY-3002 : Step(23): len = 218357, overlap = 20.25
PHY-3002 : Step(24): len = 208232, overlap = 20.25
PHY-3002 : Step(25): len = 204384, overlap = 20.25
PHY-3002 : Step(26): len = 201410, overlap = 20.25
PHY-3002 : Step(27): len = 191048, overlap = 20.25
PHY-3002 : Step(28): len = 180409, overlap = 20.25
PHY-3002 : Step(29): len = 178861, overlap = 20.25
PHY-3002 : Step(30): len = 171547, overlap = 20.25
PHY-3002 : Step(31): len = 151182, overlap = 20.25
PHY-3002 : Step(32): len = 146815, overlap = 20.25
PHY-3002 : Step(33): len = 144676, overlap = 20.25
PHY-3002 : Step(34): len = 135608, overlap = 20.25
PHY-3002 : Step(35): len = 132789, overlap = 20.25
PHY-3002 : Step(36): len = 131374, overlap = 20.25
PHY-3002 : Step(37): len = 128884, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00011694
PHY-3002 : Step(38): len = 130336, overlap = 13.5
PHY-3002 : Step(39): len = 130056, overlap = 11.25
PHY-3002 : Step(40): len = 129060, overlap = 11.25
PHY-3002 : Step(41): len = 127029, overlap = 13.5
PHY-3002 : Step(42): len = 123313, overlap = 18
PHY-3002 : Step(43): len = 121825, overlap = 18
PHY-3002 : Step(44): len = 122201, overlap = 11.25
PHY-3002 : Step(45): len = 117526, overlap = 13.5
PHY-3002 : Step(46): len = 111193, overlap = 13.5
PHY-3002 : Step(47): len = 106696, overlap = 9
PHY-3002 : Step(48): len = 105547, overlap = 6.75
PHY-3002 : Step(49): len = 103457, overlap = 15.75
PHY-3002 : Step(50): len = 101497, overlap = 15.75
PHY-3002 : Step(51): len = 98720.2, overlap = 11.25
PHY-3002 : Step(52): len = 95158.4, overlap = 13.5
PHY-3002 : Step(53): len = 94065.2, overlap = 11.25
PHY-3002 : Step(54): len = 92602.6, overlap = 11.25
PHY-3002 : Step(55): len = 91655.4, overlap = 9
PHY-3002 : Step(56): len = 89381.5, overlap = 6.75
PHY-3002 : Step(57): len = 86964.1, overlap = 13.5
PHY-3002 : Step(58): len = 86854.9, overlap = 11.25
PHY-3002 : Step(59): len = 85728.7, overlap = 11.25
PHY-3002 : Step(60): len = 83925.4, overlap = 9
PHY-3002 : Step(61): len = 79951.4, overlap = 13.5
PHY-3002 : Step(62): len = 79005.4, overlap = 15.75
PHY-3002 : Step(63): len = 78495.6, overlap = 13.5
PHY-3002 : Step(64): len = 77721, overlap = 11.25
PHY-3002 : Step(65): len = 74402.1, overlap = 7.5
PHY-3002 : Step(66): len = 71326.7, overlap = 7.125
PHY-3002 : Step(67): len = 69972.3, overlap = 7.375
PHY-3002 : Step(68): len = 68308.7, overlap = 14
PHY-3002 : Step(69): len = 68033.1, overlap = 14
PHY-3002 : Step(70): len = 68100.2, overlap = 9.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00023388
PHY-3002 : Step(71): len = 68267.4, overlap = 9.6875
PHY-3002 : Step(72): len = 68299.2, overlap = 9.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00046776
PHY-3002 : Step(73): len = 68346.5, overlap = 9.75
PHY-3002 : Step(74): len = 68293.2, overlap = 9.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005633s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063330s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00189293
PHY-3002 : Step(75): len = 69703.1, overlap = 6.8125
PHY-3002 : Step(76): len = 68090.9, overlap = 8.1875
PHY-3002 : Step(77): len = 66253.3, overlap = 6.875
PHY-3002 : Step(78): len = 64193, overlap = 9.125
PHY-3002 : Step(79): len = 62577.7, overlap = 9.6875
PHY-3002 : Step(80): len = 60837.7, overlap = 10.6562
PHY-3002 : Step(81): len = 58978.4, overlap = 9.90625
PHY-3002 : Step(82): len = 57541.5, overlap = 12.1875
PHY-3002 : Step(83): len = 55318.6, overlap = 12.5625
PHY-3002 : Step(84): len = 53487.3, overlap = 13.9375
PHY-3002 : Step(85): len = 52240.7, overlap = 15.6875
PHY-3002 : Step(86): len = 51518.5, overlap = 15.625
PHY-3002 : Step(87): len = 50775.8, overlap = 15.8438
PHY-3002 : Step(88): len = 49772.8, overlap = 16.2812
PHY-3002 : Step(89): len = 48847, overlap = 16.5
PHY-3002 : Step(90): len = 48616.8, overlap = 16.4062
PHY-3002 : Step(91): len = 48373.2, overlap = 16.4688
PHY-3002 : Step(92): len = 48183.9, overlap = 16.8125
PHY-3002 : Step(93): len = 47881.4, overlap = 17.8125
PHY-3002 : Step(94): len = 47852.7, overlap = 18.875
PHY-3002 : Step(95): len = 47561.8, overlap = 20.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062655s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000129937
PHY-3002 : Step(96): len = 48620.4, overlap = 58.4375
PHY-3002 : Step(97): len = 48660.5, overlap = 55.3438
PHY-3002 : Step(98): len = 49697.5, overlap = 45.7812
PHY-3002 : Step(99): len = 50511.6, overlap = 45.0312
PHY-3002 : Step(100): len = 50409.1, overlap = 40.5
PHY-3002 : Step(101): len = 50210.8, overlap = 34.6875
PHY-3002 : Step(102): len = 50148.8, overlap = 34.5625
PHY-3002 : Step(103): len = 49824.7, overlap = 40.8438
PHY-3002 : Step(104): len = 49522.7, overlap = 41.0312
PHY-3002 : Step(105): len = 49551.3, overlap = 41.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000259873
PHY-3002 : Step(106): len = 49485.6, overlap = 41.0625
PHY-3002 : Step(107): len = 49663.1, overlap = 34.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000519746
PHY-3002 : Step(108): len = 50068.5, overlap = 32.625
PHY-3002 : Step(109): len = 50607.1, overlap = 31.9375
PHY-3002 : Step(110): len = 51372, overlap = 31.9375
PHY-3002 : Step(111): len = 51871, overlap = 31.5
PHY-3002 : Step(112): len = 52157.3, overlap = 30.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7773, tnet num: 2191, tinst num: 1621, tnode num: 11013, tedge num: 13149.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.69 peak overflow 2.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2193.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55072, over cnt = 236(0%), over = 888, worst = 16
PHY-1001 : End global iterations;  0.070292s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.1%)

PHY-1001 : Congestion index: top1 = 39.33, top5 = 24.08, top10 = 15.64, top15 = 11.16.
PHY-1001 : End incremental global routing;  0.126546s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (111.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068202s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (91.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1582 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1623 instances, 368 luts, 987 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 52360.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2193, tinst num: 1623, tnode num: 11027, tedge num: 13161.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.317495s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(113): len = 52381.3, overlap = 3.1875
PHY-3002 : Step(114): len = 52380.9, overlap = 3.1875
PHY-3002 : Step(115): len = 52375, overlap = 3.3125
PHY-3002 : Step(116): len = 52380.4, overlap = 3.375
PHY-3002 : Step(117): len = 52380.4, overlap = 3.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058646s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00303067
PHY-3002 : Step(118): len = 52431, overlap = 30.9062
PHY-3002 : Step(119): len = 52431, overlap = 30.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00527242
PHY-3002 : Step(120): len = 52477.1, overlap = 30.9062
PHY-3002 : Step(121): len = 52463.9, overlap = 30.9062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0105448
PHY-3002 : Step(122): len = 52474.8, overlap = 30.9062
PHY-3002 : Step(123): len = 52474.8, overlap = 30.9062
PHY-3001 : Final: Len = 52474.8, Over = 30.9062
PHY-3001 : End incremental placement;  0.505021s wall, 0.484375s user + 0.171875s system = 0.656250s CPU (129.9%)

OPT-1001 : Total overflow 82.75 peak overflow 2.38
OPT-1001 : End high-fanout net optimization;  0.736103s wall, 0.703125s user + 0.187500s system = 0.890625s CPU (121.0%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1634/2195.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55280, over cnt = 238(0%), over = 883, worst = 16
PHY-1002 : len = 61808, over cnt = 111(0%), over = 195, worst = 8
PHY-1002 : len = 63728, over cnt = 21(0%), over = 32, worst = 3
PHY-1002 : len = 64080, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 64160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.093934s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (116.4%)

PHY-1001 : Congestion index: top1 = 34.66, top5 = 24.11, top10 = 16.81, top15 = 12.43.
OPT-1001 : End congestion update;  0.137961s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (113.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057564s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.198220s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (110.4%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 222.
OPT-1001 : End physical optimization;  1.211909s wall, 1.203125s user + 0.187500s system = 1.390625s CPU (114.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 103 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1398 primitive instances ...
PHY-3001 : End packing;  0.052171s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 842 instances
RUN-1001 : 396 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2028 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1475 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 840 instances, 793 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52416, Over = 63.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2026, tinst num: 840, tnode num: 8942, tedge num: 11562.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.319775s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (97.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.83481e-05
PHY-3002 : Step(124): len = 51798.1, overlap = 56.5
PHY-3002 : Step(125): len = 51246.5, overlap = 57.75
PHY-3002 : Step(126): len = 50845.7, overlap = 58.25
PHY-3002 : Step(127): len = 50776.6, overlap = 59.25
PHY-3002 : Step(128): len = 50904.9, overlap = 57.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.66962e-05
PHY-3002 : Step(129): len = 51105.2, overlap = 57.75
PHY-3002 : Step(130): len = 51639.7, overlap = 57.25
PHY-3002 : Step(131): len = 52249.7, overlap = 55
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000113392
PHY-3002 : Step(132): len = 52970.6, overlap = 54.25
PHY-3002 : Step(133): len = 53521.1, overlap = 52.75
PHY-3002 : Step(134): len = 54669.4, overlap = 48.75
PHY-3002 : Step(135): len = 54915, overlap = 43.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.089173s wall, 0.109375s user + 0.093750s system = 0.203125s CPU (227.8%)

PHY-3001 : Trial Legalized: Len = 67521.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.053770s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000633551
PHY-3002 : Step(136): len = 64758.6, overlap = 5.5
PHY-3002 : Step(137): len = 62183.5, overlap = 11.75
PHY-3002 : Step(138): len = 60191.1, overlap = 15.75
PHY-3002 : Step(139): len = 59164.3, overlap = 19.25
PHY-3002 : Step(140): len = 58714.3, overlap = 20.75
PHY-3002 : Step(141): len = 58371.3, overlap = 22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0012671
PHY-3002 : Step(142): len = 58641.1, overlap = 20.5
PHY-3002 : Step(143): len = 58683.9, overlap = 18.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0025342
PHY-3002 : Step(144): len = 58849, overlap = 18.75
PHY-3002 : Step(145): len = 58874.9, overlap = 19.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004873s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63250.3, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005774s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (270.6%)

PHY-3001 : 14 instances has been re-located, deltaX = 2, deltaY = 12, maxDist = 1.
PHY-3001 : Final: Len = 63350.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2026, tinst num: 840, tnode num: 8942, tedge num: 11562.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 97/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69568, over cnt = 125(0%), over = 193, worst = 7
PHY-1002 : len = 70336, over cnt = 77(0%), over = 95, worst = 4
PHY-1002 : len = 71376, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 71592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.101056s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (123.7%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.36, top10 = 17.17, top15 = 13.52.
PHY-1001 : End incremental global routing;  0.152207s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (112.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062708s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.244578s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (108.6%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006171s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (253.2%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.36, top10 = 17.17, top15 = 13.52.
OPT-1001 : End congestion update;  0.051854s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049920s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 802 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 840 instances, 793 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63375, Over = 0
PHY-3001 : End spreading;  0.005210s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63375, Over = 0
PHY-3001 : End incremental legalization;  0.034479s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (136.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149093s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 227, reserve = 191, peak = 227.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048071s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1775/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71624, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71584, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71616, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.035119s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.0%)

PHY-1001 : Congestion index: top1 = 30.95, top5 = 22.37, top10 = 17.17, top15 = 13.52.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051327s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.517241
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.868951s wall, 0.906250s user + 0.031250s system = 0.937500s CPU (107.9%)

RUN-1003 : finish command "place" in  5.509247s wall, 8.046875s user + 2.828125s system = 10.875000s CPU (197.4%)

RUN-1004 : used memory is 205 MB, reserved memory is 170 MB, peak memory is 227 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 842 instances
RUN-1001 : 396 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2028 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1475 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2026, tinst num: 840, tnode num: 8942, tedge num: 11562.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68832, over cnt = 126(0%), over = 197, worst = 8
PHY-1002 : len = 69872, over cnt = 76(0%), over = 93, worst = 3
PHY-1002 : len = 70816, over cnt = 15(0%), over = 17, worst = 2
PHY-1002 : len = 71136, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111555s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (182.1%)

PHY-1001 : Congestion index: top1 = 30.86, top5 = 22.23, top10 = 17.06, top15 = 13.41.
PHY-1001 : End global routing;  0.161020s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (165.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 207, peak = 250.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 502, reserve = 469, peak = 502.
PHY-1001 : End build detailed router design. 5.801305s wall, 5.671875s user + 0.171875s system = 5.843750s CPU (100.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33168, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.159061s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End phase 1; 2.165192s wall, 2.140625s user + 0.015625s system = 2.156250s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184432, over cnt = 22(0%), over = 22, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End initial routed; 1.723464s wall, 2.921875s user + 0.187500s system = 3.109375s CPU (180.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.350   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.697151s wall, 0.687500s user + 0.015625s system = 0.703125s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 537, reserve = 506, peak = 537.
PHY-1001 : End phase 2; 2.420783s wall, 3.609375s user + 0.203125s system = 3.812500s CPU (157.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184432, over cnt = 22(0%), over = 22, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.039502s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184520, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.049642s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184584, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.060225s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 184600, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.088554s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.350   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.710987s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (101.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.304421s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (97.5%)

PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End phase 3; 1.458075s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (99.7%)

PHY-1003 : Routed, final wirelength = 184600
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.016381s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (95.4%)

PHY-1001 : End detail routing;  12.198587s wall, 13.203125s user + 0.406250s system = 13.609375s CPU (111.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2026, tinst num: 840, tnode num: 8942, tedge num: 11562.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  13.406204s wall, 14.484375s user + 0.437500s system = 14.921875s CPU (111.3%)

RUN-1004 : used memory is 504 MB, reserved memory is 476 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      816   out of  19600    4.16%
#reg                     1076   out of  19600    5.49%
#le                      1518
  #lut only               442   out of   1518   29.12%
  #reg only               702   out of   1518   46.25%
  #lut&reg                374   out of   1518   24.64%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    45
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1518   |595     |221     |1107    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1117   |295     |128     |926     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |525    |120     |53      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |47     |2       |0       |47      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |92     |33      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |307    |82      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |105    |90      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |22     |18      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |47     |44      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1439  
    #2          2       311   
    #3          3       105   
    #4          4        18   
    #5        5-10       81   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6576, tnet num: 2026, tinst num: 840, tnode num: 8942, tedge num: 11562.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 840
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2028, pip num: 14790
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1368 valid insts, and 39089 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.898032s wall, 20.843750s user + 0.171875s system = 21.015625s CPU (539.1%)

RUN-1004 : used memory is 520 MB, reserved memory is 491 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230815_181008.log"
