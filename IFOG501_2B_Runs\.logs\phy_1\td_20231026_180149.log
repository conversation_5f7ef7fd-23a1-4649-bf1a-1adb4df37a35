============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 18:01:50 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1490 instances
RUN-0007 : 376 luts, 866 seqs, 133 mslices, 66 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1500 nets have 2 pins
RUN-1001 : 394 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     215     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     289     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 19
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1488 instances, 376 luts, 866 seqs, 199 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7120, tnet num: 2006, tinst num: 1488, tnode num: 9996, tedge num: 12027.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.264432s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 542681
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1488.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 493738, overlap = 20.25
PHY-3002 : Step(2): len = 462518, overlap = 20.25
PHY-3002 : Step(3): len = 441481, overlap = 15.75
PHY-3002 : Step(4): len = 422975, overlap = 11.25
PHY-3002 : Step(5): len = 409635, overlap = 13.5
PHY-3002 : Step(6): len = 400016, overlap = 9
PHY-3002 : Step(7): len = 384389, overlap = 11.25
PHY-3002 : Step(8): len = 375175, overlap = 11.25
PHY-3002 : Step(9): len = 364126, overlap = 13.5
PHY-3002 : Step(10): len = 351836, overlap = 13.5
PHY-3002 : Step(11): len = 342813, overlap = 18
PHY-3002 : Step(12): len = 336263, overlap = 15.75
PHY-3002 : Step(13): len = 322379, overlap = 13.5
PHY-3002 : Step(14): len = 312964, overlap = 13.5
PHY-3002 : Step(15): len = 307479, overlap = 13.5
PHY-3002 : Step(16): len = 299924, overlap = 15.75
PHY-3002 : Step(17): len = 292323, overlap = 15.75
PHY-3002 : Step(18): len = 286764, overlap = 15.75
PHY-3002 : Step(19): len = 281707, overlap = 20.25
PHY-3002 : Step(20): len = 273766, overlap = 20.25
PHY-3002 : Step(21): len = 266140, overlap = 20.25
PHY-3002 : Step(22): len = 262420, overlap = 20.25
PHY-3002 : Step(23): len = 255712, overlap = 20.25
PHY-3002 : Step(24): len = 245461, overlap = 20.25
PHY-3002 : Step(25): len = 240955, overlap = 20.25
PHY-3002 : Step(26): len = 236972, overlap = 20.25
PHY-3002 : Step(27): len = 228525, overlap = 20.25
PHY-3002 : Step(28): len = 220101, overlap = 20.25
PHY-3002 : Step(29): len = 217302, overlap = 20.25
PHY-3002 : Step(30): len = 211082, overlap = 20.25
PHY-3002 : Step(31): len = 173951, overlap = 20.25
PHY-3002 : Step(32): len = 167731, overlap = 20.25
PHY-3002 : Step(33): len = 165189, overlap = 20.25
PHY-3002 : Step(34): len = 133455, overlap = 20.25
PHY-3002 : Step(35): len = 130672, overlap = 20.25
PHY-3002 : Step(36): len = 126543, overlap = 20.25
PHY-3002 : Step(37): len = 122565, overlap = 20.25
PHY-3002 : Step(38): len = 120654, overlap = 20.25
PHY-3002 : Step(39): len = 117225, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.79991e-05
PHY-3002 : Step(40): len = 117757, overlap = 20.25
PHY-3002 : Step(41): len = 117933, overlap = 13.5
PHY-3002 : Step(42): len = 116392, overlap = 13.5
PHY-3002 : Step(43): len = 114990, overlap = 13.5
PHY-3002 : Step(44): len = 110828, overlap = 13.5
PHY-3002 : Step(45): len = 106998, overlap = 13.5
PHY-3002 : Step(46): len = 105081, overlap = 11.25
PHY-3002 : Step(47): len = 103603, overlap = 11.25
PHY-3002 : Step(48): len = 98172.1, overlap = 13.5
PHY-3002 : Step(49): len = 95837.7, overlap = 11.25
PHY-3002 : Step(50): len = 93711.2, overlap = 13.5
PHY-3002 : Step(51): len = 93121, overlap = 13.5
PHY-3002 : Step(52): len = 91882.7, overlap = 11.8125
PHY-3002 : Step(53): len = 90128.2, overlap = 11.75
PHY-3002 : Step(54): len = 88630.4, overlap = 13.875
PHY-3002 : Step(55): len = 87979.1, overlap = 14.0625
PHY-3002 : Step(56): len = 85644.2, overlap = 12.1875
PHY-3002 : Step(57): len = 84279.8, overlap = 12.4375
PHY-3002 : Step(58): len = 81999.2, overlap = 12.875
PHY-3002 : Step(59): len = 80057, overlap = 10.875
PHY-3002 : Step(60): len = 78334.9, overlap = 13.1875
PHY-3002 : Step(61): len = 77672.1, overlap = 13.4375
PHY-3002 : Step(62): len = 74593.8, overlap = 16.3125
PHY-3002 : Step(63): len = 72910.5, overlap = 18.75
PHY-3002 : Step(64): len = 72422.2, overlap = 14.625
PHY-3002 : Step(65): len = 70611.6, overlap = 12.5312
PHY-3002 : Step(66): len = 69663.7, overlap = 12.625
PHY-3002 : Step(67): len = 68038.7, overlap = 17.2812
PHY-3002 : Step(68): len = 66816.2, overlap = 17.25
PHY-3002 : Step(69): len = 66256.8, overlap = 19.4688
PHY-3002 : Step(70): len = 65267.2, overlap = 15
PHY-3002 : Step(71): len = 64299.4, overlap = 17.1562
PHY-3002 : Step(72): len = 63641.9, overlap = 16.875
PHY-3002 : Step(73): len = 62764.6, overlap = 16.8125
PHY-3002 : Step(74): len = 62052.8, overlap = 14.4375
PHY-3002 : Step(75): len = 61217.6, overlap = 19
PHY-3002 : Step(76): len = 60785.4, overlap = 18.75
PHY-3002 : Step(77): len = 60410.7, overlap = 14.1875
PHY-3002 : Step(78): len = 60053.4, overlap = 16.375
PHY-3002 : Step(79): len = 59402.6, overlap = 16.375
PHY-3002 : Step(80): len = 58712.2, overlap = 16.3438
PHY-3002 : Step(81): len = 58407.1, overlap = 16.3438
PHY-3002 : Step(82): len = 57409.1, overlap = 18.0938
PHY-3002 : Step(83): len = 56926.7, overlap = 13
PHY-3002 : Step(84): len = 55602, overlap = 10
PHY-3002 : Step(85): len = 55378.2, overlap = 9.6875
PHY-3002 : Step(86): len = 54569.1, overlap = 11.6875
PHY-3002 : Step(87): len = 54134.7, overlap = 11.625
PHY-3002 : Step(88): len = 53482.6, overlap = 13.875
PHY-3002 : Step(89): len = 53160.9, overlap = 11.625
PHY-3002 : Step(90): len = 51846, overlap = 9.5625
PHY-3002 : Step(91): len = 51298.1, overlap = 11.625
PHY-3002 : Step(92): len = 50839, overlap = 11.375
PHY-3002 : Step(93): len = 50565.5, overlap = 11.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000155998
PHY-3002 : Step(94): len = 50857.2, overlap = 11.625
PHY-3002 : Step(95): len = 50947, overlap = 11.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000311996
PHY-3002 : Step(96): len = 51177.4, overlap = 11.625
PHY-3002 : Step(97): len = 51333.6, overlap = 11.6875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008266s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (189.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058795s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00141106
PHY-3002 : Step(98): len = 54587.8, overlap = 12.8438
PHY-3002 : Step(99): len = 54667.5, overlap = 12.75
PHY-3002 : Step(100): len = 53974.1, overlap = 13
PHY-3002 : Step(101): len = 53995.3, overlap = 13.2812
PHY-3002 : Step(102): len = 54114, overlap = 13.5
PHY-3002 : Step(103): len = 53138.4, overlap = 13.375
PHY-3002 : Step(104): len = 52545.9, overlap = 12.25
PHY-3002 : Step(105): len = 51298.9, overlap = 9.90625
PHY-3002 : Step(106): len = 51246.6, overlap = 9.46875
PHY-3002 : Step(107): len = 50160.5, overlap = 11.0312
PHY-3002 : Step(108): len = 49376.5, overlap = 11.1562
PHY-3002 : Step(109): len = 48806.1, overlap = 11.9062
PHY-3002 : Step(110): len = 47871.9, overlap = 11.9062
PHY-3002 : Step(111): len = 47736.7, overlap = 12.2812
PHY-3002 : Step(112): len = 47161.3, overlap = 12.5312
PHY-3002 : Step(113): len = 47063.7, overlap = 12.5625
PHY-3002 : Step(114): len = 46939.5, overlap = 14.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00282212
PHY-3002 : Step(115): len = 46643.3, overlap = 14.3125
PHY-3002 : Step(116): len = 46377.2, overlap = 13.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00564425
PHY-3002 : Step(117): len = 46474.2, overlap = 13.0938
PHY-3002 : Step(118): len = 46474.2, overlap = 13.0938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054781s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102521
PHY-3002 : Step(119): len = 46846, overlap = 49.5938
PHY-3002 : Step(120): len = 47747.9, overlap = 47.5
PHY-3002 : Step(121): len = 48229.9, overlap = 50.0312
PHY-3002 : Step(122): len = 48036.8, overlap = 49.0938
PHY-3002 : Step(123): len = 48152.5, overlap = 48.6875
PHY-3002 : Step(124): len = 48236.8, overlap = 49.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000205042
PHY-3002 : Step(125): len = 48838.7, overlap = 44
PHY-3002 : Step(126): len = 49409.1, overlap = 43.375
PHY-3002 : Step(127): len = 49995.3, overlap = 43.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000410083
PHY-3002 : Step(128): len = 50612.1, overlap = 41.6875
PHY-3002 : Step(129): len = 51126, overlap = 34.8438
PHY-3002 : Step(130): len = 51941.1, overlap = 34.0938
PHY-3002 : Step(131): len = 51911.3, overlap = 32.75
PHY-3002 : Step(132): len = 51364.4, overlap = 32.625
PHY-3002 : Step(133): len = 51232.2, overlap = 30.5
PHY-3002 : Step(134): len = 50634.1, overlap = 31.2188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7120, tnet num: 2006, tinst num: 1488, tnode num: 9996, tedge num: 12027.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 87.00 peak overflow 2.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53112, over cnt = 235(0%), over = 1005, worst = 18
PHY-1001 : End global iterations;  0.074763s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (146.3%)

PHY-1001 : Congestion index: top1 = 43.49, top5 = 25.94, top10 = 15.83, top15 = 11.14.
PHY-1001 : End incremental global routing;  0.122447s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (140.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060694s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.212351s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (125.1%)

OPT-1001 : Current memory(MB): used = 210, reserve = 172, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1517/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53112, over cnt = 235(0%), over = 1005, worst = 18
PHY-1002 : len = 60160, over cnt = 149(0%), over = 295, worst = 14
PHY-1002 : len = 63024, over cnt = 31(0%), over = 50, worst = 8
PHY-1002 : len = 63784, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 63880, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096714s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (129.2%)

PHY-1001 : Congestion index: top1 = 37.63, top5 = 25.76, top10 = 17.82, top15 = 12.85.
OPT-1001 : End congestion update;  0.139100s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (112.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053116s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.194884s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.2%)

OPT-1001 : Current memory(MB): used = 213, reserve = 175, peak = 213.
OPT-1001 : End physical optimization;  0.682956s wall, 0.703125s user + 0.046875s system = 0.750000s CPU (109.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 176 SEQ to BLE.
SYN-4003 : Packing 690 remaining SEQ's ...
SYN-4005 : Packed 107 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 583 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 959/1241 primitive instances ...
PHY-3001 : End packing;  0.045139s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 751 instances
RUN-1001 : 351 mslices, 351 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1840 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1339 nets have 2 pins
RUN-1001 : 387 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 749 instances, 702 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 50919, Over = 56.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5982, tnet num: 1838, tinst num: 749, tnode num: 8064, tedge num: 10500.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1838 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.302561s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (103.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.85836e-05
PHY-3002 : Step(135): len = 50070.6, overlap = 57.25
PHY-3002 : Step(136): len = 49483.1, overlap = 57.75
PHY-3002 : Step(137): len = 48916.6, overlap = 56.25
PHY-3002 : Step(138): len = 48677.2, overlap = 57.5
PHY-3002 : Step(139): len = 48630, overlap = 56.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.71673e-05
PHY-3002 : Step(140): len = 49191.4, overlap = 55.25
PHY-3002 : Step(141): len = 49416.9, overlap = 53.25
PHY-3002 : Step(142): len = 49697.4, overlap = 53.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000114335
PHY-3002 : Step(143): len = 50976.1, overlap = 51.5
PHY-3002 : Step(144): len = 51722.9, overlap = 49.25
PHY-3002 : Step(145): len = 51998.9, overlap = 48
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.068830s wall, 0.015625s user + 0.109375s system = 0.125000s CPU (181.6%)

PHY-3001 : Trial Legalized: Len = 65220.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1838 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.045031s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00054351
PHY-3002 : Step(146): len = 60676.5, overlap = 7.5
PHY-3002 : Step(147): len = 58662.4, overlap = 11.75
PHY-3002 : Step(148): len = 57148.6, overlap = 15.75
PHY-3002 : Step(149): len = 56227.5, overlap = 16.25
PHY-3002 : Step(150): len = 55467.4, overlap = 17
PHY-3002 : Step(151): len = 55106.5, overlap = 17.5
PHY-3002 : Step(152): len = 54884.4, overlap = 17.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00108702
PHY-3002 : Step(153): len = 55091.4, overlap = 17.25
PHY-3002 : Step(154): len = 55283.8, overlap = 16.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00217404
PHY-3002 : Step(155): len = 55347.6, overlap = 16.5
PHY-3002 : Step(156): len = 55381.3, overlap = 16.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005796s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (269.6%)

PHY-3001 : Legalized: Len = 59302.2, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005221s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 1, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 59456.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5982, tnet num: 1838, tinst num: 749, tnode num: 8064, tedge num: 10500.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 104/1840.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66120, over cnt = 148(0%), over = 245, worst = 6
PHY-1002 : len = 66568, over cnt = 110(0%), over = 175, worst = 6
PHY-1002 : len = 68144, over cnt = 46(0%), over = 60, worst = 6
PHY-1002 : len = 68888, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 69080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.135558s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (92.2%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.60, top10 = 18.04, top15 = 13.80.
PHY-1001 : End incremental global routing;  0.185863s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (100.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1838 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056617s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.272138s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (97.6%)

OPT-1001 : Current memory(MB): used = 218, reserve = 180, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1615/1840.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005178s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.60, top10 = 18.04, top15 = 13.80.
OPT-1001 : End congestion update;  0.051510s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1838 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045485s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 711 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 749 instances, 702 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 59471, Over = 0
PHY-3001 : End spreading;  0.004807s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 59471, Over = 0
PHY-3001 : End incremental legalization;  0.032663s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.7%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.141893s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.1%)

OPT-1001 : Current memory(MB): used = 223, reserve = 185, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1838 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043608s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (107.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1611/1840.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006416s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.60, top10 = 18.04, top15 = 13.81.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1838 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047836s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.825427s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (100.3%)

RUN-1003 : finish command "place" in  5.080140s wall, 7.578125s user + 3.015625s system = 10.593750s CPU (208.5%)

RUN-1004 : used memory is 202 MB, reserved memory is 164 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 751 instances
RUN-1001 : 351 mslices, 351 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1840 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1339 nets have 2 pins
RUN-1001 : 387 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5982, tnet num: 1838, tinst num: 749, tnode num: 8064, tedge num: 10500.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 351 mslices, 351 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1838 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65448, over cnt = 151(0%), over = 250, worst = 6
PHY-1002 : len = 65920, over cnt = 108(0%), over = 176, worst = 6
PHY-1002 : len = 67704, over cnt = 37(0%), over = 47, worst = 3
PHY-1002 : len = 68344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132962s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (117.5%)

PHY-1001 : Congestion index: top1 = 32.09, top5 = 23.32, top10 = 17.85, top15 = 13.67.
PHY-1001 : End global routing;  0.179785s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (113.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 199, peak = 238.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 464, peak = 497.
PHY-1001 : End build detailed router design. 3.272139s wall, 3.203125s user + 0.078125s system = 3.281250s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32776, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.302213s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 529, reserve = 499, peak = 529.
PHY-1001 : End phase 1; 1.308000s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (98.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 173440, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 500, peak = 531.
PHY-1001 : End initial routed; 1.259085s wall, 2.031250s user + 0.046875s system = 2.078125s CPU (165.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1625(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.302   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.014   |  -0.027   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.355846s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (96.6%)

PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 533.
PHY-1001 : End phase 2; 1.615019s wall, 2.375000s user + 0.046875s system = 2.421875s CPU (150.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 173440, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014566s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (107.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 173408, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.037509s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 173440, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020878s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (149.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1625(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.302   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.014   |  -0.027   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.330940s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.164142s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.2%)

PHY-1001 : Current memory(MB): used = 547, reserve = 514, peak = 547.
PHY-1001 : End phase 3; 0.693588s wall, 0.671875s user + 0.015625s system = 0.687500s CPU (99.1%)

PHY-1003 : Routed, final wirelength = 173440
PHY-1001 : Current memory(MB): used = 547, reserve = 515, peak = 547.
PHY-1001 : End export database. 0.009607s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (162.6%)

PHY-1001 : End detail routing;  7.071081s wall, 7.703125s user + 0.140625s system = 7.843750s CPU (110.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5982, tnet num: 1838, tinst num: 749, tnode num: 8064, tedge num: 10500.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6002, tnet num: 1848, tinst num: 759, tnode num: 8084, tedge num: 10520.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.901960s wall, 2.921875s user + 0.125000s system = 3.046875s CPU (105.0%)

RUN-1003 : finish command "route" in  10.516337s wall, 11.171875s user + 0.265625s system = 11.437500s CPU (108.8%)

RUN-1004 : used memory is 541 MB, reserved memory is 512 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      794   out of  19600    4.05%
#reg                      941   out of  19600    4.80%
#le                      1377
  #lut only               436   out of   1377   31.66%
  #reg only               583   out of   1377   42.34%
  #lut&reg                358   out of   1377   26.00%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                       408
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                       100
#3        wendu/clk_us                    GCLK               lslice             signal_process/trans/clk_out_n_syn_48.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1377   |595     |199     |972     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |986    |288     |115     |788     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |19      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |450    |116     |44      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |31      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |9       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |7       |0       |16      |0       |0       |
|    integ                   |Integration                                      |135    |22      |14      |109     |0       |0       |
|    modu                    |Modulation                                       |39     |7       |0       |39      |0       |1       |
|    rs422                   |Rs422Output                                      |305    |98      |45      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |124    |117     |7       |63      |0       |0       |
|    U0                      |speed_select_Tx                                  |29     |22      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |24     |24      |0       |19      |0       |0       |
|    U2                      |Ctrl_Data                                        |71     |71      |0       |27      |0       |0       |
|  wendu                     |DS18B20                                          |202    |157     |45      |67      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1313  
    #2          2       253   
    #3          3       117   
    #4          4        17   
    #5        5-10       79   
    #6        11-50      27   
    #7       101-500     1    
  Average     1.97            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6002, tnet num: 1848, tinst num: 759, tnode num: 8084, tedge num: 10520.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1848 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 759
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1850, pip num: 13881
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1211 valid insts, and 36678 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.911714s wall, 15.546875s user + 0.015625s system = 15.562500s CPU (534.5%)

RUN-1004 : used memory is 549 MB, reserved memory is 515 MB, peak memory is 678 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_180149.log"
