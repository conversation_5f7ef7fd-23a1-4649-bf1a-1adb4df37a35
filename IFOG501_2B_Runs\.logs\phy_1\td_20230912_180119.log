============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Sep 12 18:01:20 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1621 instances
RUN-0007 : 366 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2191 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1630 nets have 2 pins
RUN-1001 : 444 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1619 instances, 366 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7772, tnet num: 2189, tinst num: 1619, tnode num: 11012, tedge num: 13151.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.332166s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 624436
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1619.
PHY-3001 : End clustering;  0.000025s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 500921, overlap = 20.25
PHY-3002 : Step(2): len = 459988, overlap = 20.25
PHY-3002 : Step(3): len = 424200, overlap = 20.25
PHY-3002 : Step(4): len = 399476, overlap = 20.25
PHY-3002 : Step(5): len = 389170, overlap = 20.25
PHY-3002 : Step(6): len = 377423, overlap = 20.25
PHY-3002 : Step(7): len = 317027, overlap = 20.25
PHY-3002 : Step(8): len = 285179, overlap = 18
PHY-3002 : Step(9): len = 272221, overlap = 20.25
PHY-3002 : Step(10): len = 267999, overlap = 20.25
PHY-3002 : Step(11): len = 256302, overlap = 20.25
PHY-3002 : Step(12): len = 248415, overlap = 20.25
PHY-3002 : Step(13): len = 244440, overlap = 20.25
PHY-3002 : Step(14): len = 235745, overlap = 20.25
PHY-3002 : Step(15): len = 226244, overlap = 20.25
PHY-3002 : Step(16): len = 221656, overlap = 20.25
PHY-3002 : Step(17): len = 218159, overlap = 20.25
PHY-3002 : Step(18): len = 209490, overlap = 20.25
PHY-3002 : Step(19): len = 204309, overlap = 20.25
PHY-3002 : Step(20): len = 201649, overlap = 20.25
PHY-3002 : Step(21): len = 196818, overlap = 20.25
PHY-3002 : Step(22): len = 179214, overlap = 20.25
PHY-3002 : Step(23): len = 176299, overlap = 20.25
PHY-3002 : Step(24): len = 173357, overlap = 20.25
PHY-3002 : Step(25): len = 166625, overlap = 15.75
PHY-3002 : Step(26): len = 161654, overlap = 20.25
PHY-3002 : Step(27): len = 158184, overlap = 20.25
PHY-3002 : Step(28): len = 155074, overlap = 20.25
PHY-3002 : Step(29): len = 152058, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000130232
PHY-3002 : Step(30): len = 153331, overlap = 13.5
PHY-3002 : Step(31): len = 152611, overlap = 11.25
PHY-3002 : Step(32): len = 150494, overlap = 18
PHY-3002 : Step(33): len = 147065, overlap = 18
PHY-3002 : Step(34): len = 140710, overlap = 11.25
PHY-3002 : Step(35): len = 135123, overlap = 11.25
PHY-3002 : Step(36): len = 130809, overlap = 15.75
PHY-3002 : Step(37): len = 130324, overlap = 13.5
PHY-3002 : Step(38): len = 127833, overlap = 11.25
PHY-3002 : Step(39): len = 125340, overlap = 11.25
PHY-3002 : Step(40): len = 122668, overlap = 13.5
PHY-3002 : Step(41): len = 119804, overlap = 15.75
PHY-3002 : Step(42): len = 116288, overlap = 9
PHY-3002 : Step(43): len = 112192, overlap = 9
PHY-3002 : Step(44): len = 111864, overlap = 9
PHY-3002 : Step(45): len = 107436, overlap = 15.75
PHY-3002 : Step(46): len = 102044, overlap = 13.5
PHY-3002 : Step(47): len = 99530.7, overlap = 11.25
PHY-3002 : Step(48): len = 99156.2, overlap = 13.5
PHY-3002 : Step(49): len = 97057.4, overlap = 13.5
PHY-3002 : Step(50): len = 96704, overlap = 9
PHY-3002 : Step(51): len = 94561.4, overlap = 9
PHY-3002 : Step(52): len = 92952.4, overlap = 13.5
PHY-3002 : Step(53): len = 90311.3, overlap = 11.25
PHY-3002 : Step(54): len = 88873.9, overlap = 14.5625
PHY-3002 : Step(55): len = 86660.9, overlap = 14.625
PHY-3002 : Step(56): len = 84869.7, overlap = 12.3125
PHY-3002 : Step(57): len = 82797.2, overlap = 12.375
PHY-3002 : Step(58): len = 81960.2, overlap = 12.3125
PHY-3002 : Step(59): len = 80085.9, overlap = 10.3125
PHY-3002 : Step(60): len = 79207.4, overlap = 10.625
PHY-3002 : Step(61): len = 77656, overlap = 10.6875
PHY-3002 : Step(62): len = 76405.5, overlap = 10.625
PHY-3002 : Step(63): len = 72576.7, overlap = 15.1875
PHY-3002 : Step(64): len = 71871.6, overlap = 12.625
PHY-3002 : Step(65): len = 70726.8, overlap = 12.625
PHY-3002 : Step(66): len = 68832.9, overlap = 11.625
PHY-3002 : Step(67): len = 67292.7, overlap = 11.5625
PHY-3002 : Step(68): len = 66361.4, overlap = 12.125
PHY-3002 : Step(69): len = 63823.2, overlap = 12.9375
PHY-3002 : Step(70): len = 63527.7, overlap = 13.0625
PHY-3002 : Step(71): len = 62708.8, overlap = 13.375
PHY-3002 : Step(72): len = 61757.6, overlap = 17.6875
PHY-3002 : Step(73): len = 60728.5, overlap = 18.75
PHY-3002 : Step(74): len = 59726.5, overlap = 16.6875
PHY-3002 : Step(75): len = 58246.1, overlap = 10.125
PHY-3002 : Step(76): len = 57831, overlap = 12.375
PHY-3002 : Step(77): len = 56387.6, overlap = 14.8125
PHY-3002 : Step(78): len = 54351.6, overlap = 15.5
PHY-3002 : Step(79): len = 53994.4, overlap = 15.875
PHY-3002 : Step(80): len = 53703.1, overlap = 16.0625
PHY-3002 : Step(81): len = 53542.9, overlap = 13.7812
PHY-3002 : Step(82): len = 53426.1, overlap = 13.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000260463
PHY-3002 : Step(83): len = 53428.4, overlap = 13.9375
PHY-3002 : Step(84): len = 53509.8, overlap = 13.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000520927
PHY-3002 : Step(85): len = 53474.8, overlap = 11.625
PHY-3002 : Step(86): len = 53563.4, overlap = 11.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007956s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.106644s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (102.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00722081
PHY-3002 : Step(87): len = 56765.8, overlap = 14.1875
PHY-3002 : Step(88): len = 55892.1, overlap = 14.8125
PHY-3002 : Step(89): len = 55291.1, overlap = 14.6875
PHY-3002 : Step(90): len = 54752.1, overlap = 15.4375
PHY-3002 : Step(91): len = 54409.1, overlap = 15.875
PHY-3002 : Step(92): len = 52623.8, overlap = 16.375
PHY-3002 : Step(93): len = 51890.7, overlap = 16.6875
PHY-3002 : Step(94): len = 51281.3, overlap = 16.25
PHY-3002 : Step(95): len = 51065.5, overlap = 16.4062
PHY-3002 : Step(96): len = 51097.5, overlap = 16.0312
PHY-3002 : Step(97): len = 50151.2, overlap = 16.3438
PHY-3002 : Step(98): len = 49639.4, overlap = 16.3438
PHY-3002 : Step(99): len = 49458.7, overlap = 16.3438
PHY-3002 : Step(100): len = 49069.5, overlap = 16.3438
PHY-3002 : Step(101): len = 48715.6, overlap = 16.9688
PHY-3002 : Step(102): len = 48411.1, overlap = 17.4062
PHY-3002 : Step(103): len = 47742.1, overlap = 18.5312
PHY-3002 : Step(104): len = 47227, overlap = 18.9062
PHY-3002 : Step(105): len = 46512.6, overlap = 21.5625
PHY-3002 : Step(106): len = 46338.5, overlap = 22.0938
PHY-3002 : Step(107): len = 46263.3, overlap = 22.1562
PHY-3002 : Step(108): len = 46267.9, overlap = 22.0938
PHY-3002 : Step(109): len = 46153.4, overlap = 22.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.077688s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.3279e-05
PHY-3002 : Step(110): len = 46600.6, overlap = 68.7188
PHY-3002 : Step(111): len = 47327.3, overlap = 69.4688
PHY-3002 : Step(112): len = 47992, overlap = 58.8125
PHY-3002 : Step(113): len = 48903.4, overlap = 57.9688
PHY-3002 : Step(114): len = 48781.2, overlap = 55.6875
PHY-3002 : Step(115): len = 48803.7, overlap = 55.3438
PHY-3002 : Step(116): len = 48672.4, overlap = 55
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000146558
PHY-3002 : Step(117): len = 48678.8, overlap = 55.0938
PHY-3002 : Step(118): len = 48678.8, overlap = 55.0938
PHY-3002 : Step(119): len = 48710.7, overlap = 50.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000293116
PHY-3002 : Step(120): len = 49639.2, overlap = 49.1562
PHY-3002 : Step(121): len = 49934.8, overlap = 45.0938
PHY-3002 : Step(122): len = 50184.3, overlap = 42.0625
PHY-3002 : Step(123): len = 50660, overlap = 38.9688
PHY-3002 : Step(124): len = 50789.1, overlap = 38.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7772, tnet num: 2189, tinst num: 1619, tnode num: 11012, tedge num: 13151.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 93.84 peak overflow 3.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2191.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55336, over cnt = 266(0%), over = 1159, worst = 24
PHY-1001 : End global iterations;  0.083355s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (131.2%)

PHY-1001 : Congestion index: top1 = 46.06, top5 = 25.91, top10 = 16.22, top15 = 11.44.
PHY-1001 : End incremental global routing;  0.146660s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (117.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2189 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.085221s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (110.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1580 has valid locations, 4 needs to be replaced
PHY-3001 : design contains 1622 instances, 369 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 51167.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2192, tinst num: 1622, tnode num: 11021, tedge num: 13163.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.360739s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(125): len = 51143.4, overlap = 4.71875
PHY-3002 : Step(126): len = 51119.4, overlap = 4.71875
PHY-3002 : Step(127): len = 51117.2, overlap = 4.71875
PHY-3002 : Step(128): len = 51117.2, overlap = 4.71875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.078442s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(129): len = 51117.2, overlap = 38.625
PHY-3002 : Step(130): len = 51117.2, overlap = 38.625
PHY-3001 : Final: Len = 51117.2, Over = 38.625
PHY-3001 : End incremental placement;  0.633233s wall, 0.578125s user + 0.171875s system = 0.750000s CPU (118.4%)

OPT-1001 : Total overflow 93.94 peak overflow 3.50
OPT-1001 : End high-fanout net optimization;  0.916120s wall, 0.843750s user + 0.203125s system = 1.046875s CPU (114.3%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1714/2194.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55536, over cnt = 267(0%), over = 1161, worst = 24
PHY-1002 : len = 63904, over cnt = 199(0%), over = 431, worst = 15
PHY-1002 : len = 67480, over cnt = 63(0%), over = 134, worst = 8
PHY-1002 : len = 69048, over cnt = 12(0%), over = 27, worst = 8
PHY-1002 : len = 69624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.143658s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (174.0%)

PHY-1001 : Congestion index: top1 = 41.59, top5 = 26.78, top10 = 18.70, top15 = 13.66.
OPT-1001 : End congestion update;  0.198177s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (149.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.085750s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (109.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.288525s wall, 0.343750s user + 0.046875s system = 0.390625s CPU (135.4%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : End physical optimization;  1.508470s wall, 1.562500s user + 0.265625s system = 1.828125s CPU (121.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 106 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1071/1399 primitive instances ...
PHY-3001 : End packing;  0.061285s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 838 instances
RUN-1001 : 394 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1472 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 836 instances, 789 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50558.6, Over = 61.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 836, tnode num: 8929, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.356553s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.36044e-05
PHY-3002 : Step(131): len = 50151.5, overlap = 62
PHY-3002 : Step(132): len = 49848.5, overlap = 60.75
PHY-3002 : Step(133): len = 49595.4, overlap = 61.25
PHY-3002 : Step(134): len = 49353.2, overlap = 61.25
PHY-3002 : Step(135): len = 49346.2, overlap = 59.75
PHY-3002 : Step(136): len = 49357, overlap = 59.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.72088e-05
PHY-3002 : Step(137): len = 49691.1, overlap = 58.5
PHY-3002 : Step(138): len = 50069.7, overlap = 57.25
PHY-3002 : Step(139): len = 51207.5, overlap = 53.25
PHY-3002 : Step(140): len = 51998.3, overlap = 52.5
PHY-3002 : Step(141): len = 51963.2, overlap = 51.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.44176e-05
PHY-3002 : Step(142): len = 52309.8, overlap = 50
PHY-3002 : Step(143): len = 52309.8, overlap = 50
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.119511s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (183.0%)

PHY-3001 : Trial Legalized: Len = 68456.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056677s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000567142
PHY-3002 : Step(144): len = 65702, overlap = 6
PHY-3002 : Step(145): len = 63497.1, overlap = 13
PHY-3002 : Step(146): len = 61770.6, overlap = 16.75
PHY-3002 : Step(147): len = 60770.8, overlap = 19.75
PHY-3002 : Step(148): len = 60229.4, overlap = 22
PHY-3002 : Step(149): len = 59942.2, overlap = 22.75
PHY-3002 : Step(150): len = 59702.6, overlap = 22.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00113428
PHY-3002 : Step(151): len = 60227.2, overlap = 21.25
PHY-3002 : Step(152): len = 60460.8, overlap = 21.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00226857
PHY-3002 : Step(153): len = 60632.5, overlap = 21
PHY-3002 : Step(154): len = 60770.6, overlap = 20.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009549s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 65419.1, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006894s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 3, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 65615.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 836, tnode num: 8929, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 41/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72304, over cnt = 152(0%), over = 208, worst = 5
PHY-1002 : len = 73000, over cnt = 69(0%), over = 76, worst = 3
PHY-1002 : len = 73872, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 73968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.196878s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (142.9%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.65, top10 = 17.69, top15 = 14.05.
PHY-1001 : End incremental global routing;  0.258704s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (132.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077460s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (80.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.370119s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (122.4%)

OPT-1001 : Current memory(MB): used = 221, reserve = 187, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1782/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009048s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (172.7%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.65, top10 = 17.69, top15 = 14.05.
OPT-1001 : End congestion update;  0.071926s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066805s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 798 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 836 instances, 789 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65609.6, Over = 0
PHY-3001 : End spreading;  0.006933s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (225.4%)

PHY-3001 : Final: Len = 65609.6, Over = 0
PHY-3001 : End incremental legalization;  0.049205s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.210138s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (96.7%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072062s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1770/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73936, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73936, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.044284s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.9%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 22.66, top10 = 17.71, top15 = 14.06.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064579s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.655172
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.186057s wall, 1.234375s user + 0.031250s system = 1.265625s CPU (106.7%)

RUN-1003 : finish command "place" in  8.090708s wall, 12.906250s user + 4.484375s system = 17.390625s CPU (214.9%)

RUN-1004 : used memory is 210 MB, reserved memory is 174 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 838 instances
RUN-1001 : 394 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1472 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 836, tnode num: 8929, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71776, over cnt = 146(0%), over = 206, worst = 5
PHY-1002 : len = 72504, over cnt = 67(0%), over = 76, worst = 3
PHY-1002 : len = 73360, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 73424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.188714s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (132.5%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.62, top10 = 17.60, top15 = 13.95.
PHY-1001 : End global routing;  0.251611s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (124.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 204, peak = 251.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 506, reserve = 473, peak = 506.
PHY-1001 : End build detailed router design. 3.627308s wall, 3.546875s user + 0.078125s system = 3.625000s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33848, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.658490s wall, 1.656250s user + 0.000000s system = 1.656250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 537, reserve = 506, peak = 537.
PHY-1001 : End phase 1; 1.667433s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 187936, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 538, reserve = 507, peak = 538.
PHY-1001 : End initial routed; 1.456403s wall, 2.312500s user + 0.265625s system = 2.578125s CPU (177.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.322   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.485   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.505800s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (95.8%)

PHY-1001 : Current memory(MB): used = 539, reserve = 508, peak = 539.
PHY-1001 : End phase 2; 1.962398s wall, 2.796875s user + 0.265625s system = 3.062500s CPU (156.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 187936, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016987s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (92.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 187912, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031543s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (198.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 187976, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027373s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (114.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.322   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.485   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.428090s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (102.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.197272s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.0%)

PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End phase 3; 0.846696s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (105.2%)

PHY-1003 : Routed, final wirelength = 187976
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.012016s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  8.317707s wall, 9.093750s user + 0.359375s system = 9.453125s CPU (113.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 836, tnode num: 8929, tedge num: 11549.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addra[4] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[32] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[8] slack -88ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6600, tnet num: 2040, tinst num: 852, tnode num: 8961, tedge num: 11581.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.955969s wall, 4.000000s user + 0.296875s system = 4.296875s CPU (108.6%)

RUN-1003 : finish command "route" in  12.931304s wall, 13.781250s user + 0.687500s system = 14.468750s CPU (111.9%)

RUN-1004 : used memory is 549 MB, reserved memory is 520 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      847   out of  19600    4.32%
#reg                     1074   out of  19600    5.48%
#le                      1549
  #lut only               475   out of   1549   30.66%
  #reg only               702   out of   1549   45.32%
  #lut&reg                372   out of   1549   24.02%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    46
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1549   |626     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1151   |327     |128     |921     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |22     |16      |6       |16      |0       |0       |
|    demodu                  |Demodulation                                     |555    |154     |53      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |64      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |0       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |31     |18      |0       |31      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |89     |23      |21      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |320    |96      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |21      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |94     |81      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |22     |18      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |36     |35      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |221    |176     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1452  
    #2          2       314   
    #3          3       103   
    #4          4        15   
    #5        5-10       82   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6600, tnet num: 2040, tinst num: 852, tnode num: 8961, tedge num: 11581.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 852
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2042, pip num: 14963
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1342 valid insts, and 39719 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.984440s wall, 22.000000s user + 0.062500s system = 22.062500s CPU (553.7%)

RUN-1004 : used memory is 555 MB, reserved memory is 522 MB, peak memory is 693 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230912_180119.log"
