============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 15:26:15 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1626 instances
RUN-0007 : 379 luts, 979 seqs, 144 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2183 nets
RUN-1001 : 1646 nets have 2 pins
RUN-1001 : 423 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1624 instances, 379 luts, 979 seqs, 219 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7727, tnet num: 2181, tinst num: 1624, tnode num: 10915, tedge num: 13017.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.269891s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (104.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 601754
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1624.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 482087, overlap = 15.75
PHY-3002 : Step(2): len = 356254, overlap = 18
PHY-3002 : Step(3): len = 326875, overlap = 20.25
PHY-3002 : Step(4): len = 312764, overlap = 20.25
PHY-3002 : Step(5): len = 303268, overlap = 15.75
PHY-3002 : Step(6): len = 296216, overlap = 15.75
PHY-3002 : Step(7): len = 287700, overlap = 15.75
PHY-3002 : Step(8): len = 282393, overlap = 15.75
PHY-3002 : Step(9): len = 272992, overlap = 18
PHY-3002 : Step(10): len = 266996, overlap = 18
PHY-3002 : Step(11): len = 261652, overlap = 18
PHY-3002 : Step(12): len = 255956, overlap = 18
PHY-3002 : Step(13): len = 249767, overlap = 18
PHY-3002 : Step(14): len = 246595, overlap = 18
PHY-3002 : Step(15): len = 237496, overlap = 18
PHY-3002 : Step(16): len = 231237, overlap = 18
PHY-3002 : Step(17): len = 227700, overlap = 18
PHY-3002 : Step(18): len = 223615, overlap = 20.25
PHY-3002 : Step(19): len = 208685, overlap = 20.25
PHY-3002 : Step(20): len = 204953, overlap = 20.25
PHY-3002 : Step(21): len = 201857, overlap = 20.25
PHY-3002 : Step(22): len = 195034, overlap = 20.25
PHY-3002 : Step(23): len = 184784, overlap = 20.25
PHY-3002 : Step(24): len = 182661, overlap = 20.25
PHY-3002 : Step(25): len = 178512, overlap = 20.25
PHY-3002 : Step(26): len = 153512, overlap = 20.25
PHY-3002 : Step(27): len = 144725, overlap = 20.25
PHY-3002 : Step(28): len = 143782, overlap = 20.25
PHY-3002 : Step(29): len = 138631, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000140547
PHY-3002 : Step(30): len = 140144, overlap = 11.25
PHY-3002 : Step(31): len = 138643, overlap = 13.5
PHY-3002 : Step(32): len = 135511, overlap = 15.75
PHY-3002 : Step(33): len = 132669, overlap = 13.5
PHY-3002 : Step(34): len = 127092, overlap = 6.75
PHY-3002 : Step(35): len = 126080, overlap = 6.75
PHY-3002 : Step(36): len = 121774, overlap = 11.25
PHY-3002 : Step(37): len = 121782, overlap = 9
PHY-3002 : Step(38): len = 118979, overlap = 6.75
PHY-3002 : Step(39): len = 116071, overlap = 6.75
PHY-3002 : Step(40): len = 112117, overlap = 9
PHY-3002 : Step(41): len = 111067, overlap = 11.25
PHY-3002 : Step(42): len = 108248, overlap = 6.75
PHY-3002 : Step(43): len = 106519, overlap = 6.75
PHY-3002 : Step(44): len = 103465, overlap = 6.75
PHY-3002 : Step(45): len = 102417, overlap = 6.75
PHY-3002 : Step(46): len = 99242.3, overlap = 6.75
PHY-3002 : Step(47): len = 97141.9, overlap = 6.75
PHY-3002 : Step(48): len = 95000.8, overlap = 6.75
PHY-3002 : Step(49): len = 93455.8, overlap = 11.25
PHY-3002 : Step(50): len = 89044.9, overlap = 9
PHY-3002 : Step(51): len = 88213, overlap = 6.75
PHY-3002 : Step(52): len = 86147.8, overlap = 6.75
PHY-3002 : Step(53): len = 83978.4, overlap = 6.75
PHY-3002 : Step(54): len = 82403.2, overlap = 6.75
PHY-3002 : Step(55): len = 79871.2, overlap = 11.25
PHY-3002 : Step(56): len = 78714.1, overlap = 6.75
PHY-3002 : Step(57): len = 77570.5, overlap = 9
PHY-3002 : Step(58): len = 76433.7, overlap = 6.75
PHY-3002 : Step(59): len = 74475.9, overlap = 11.25
PHY-3002 : Step(60): len = 73614.2, overlap = 6.75
PHY-3002 : Step(61): len = 71705.6, overlap = 9
PHY-3002 : Step(62): len = 70241.5, overlap = 9
PHY-3002 : Step(63): len = 68923.2, overlap = 9
PHY-3002 : Step(64): len = 68257.2, overlap = 9.125
PHY-3002 : Step(65): len = 63925.6, overlap = 6.75
PHY-3002 : Step(66): len = 61723.4, overlap = 11.5625
PHY-3002 : Step(67): len = 61115.3, overlap = 9.1875
PHY-3002 : Step(68): len = 60739.7, overlap = 11.4375
PHY-3002 : Step(69): len = 60331.2, overlap = 7.375
PHY-3002 : Step(70): len = 60078.2, overlap = 7.4375
PHY-3002 : Step(71): len = 59487.3, overlap = 7.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000281094
PHY-3002 : Step(72): len = 59092.9, overlap = 7.5625
PHY-3002 : Step(73): len = 59179.1, overlap = 7.5625
PHY-3002 : Step(74): len = 59191.6, overlap = 5.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000562188
PHY-3002 : Step(75): len = 59025.7, overlap = 3.0625
PHY-3002 : Step(76): len = 58948.7, overlap = 3.0625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005776s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.076531s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(77): len = 59983.5, overlap = 11.5
PHY-3002 : Step(78): len = 58874.8, overlap = 11.9062
PHY-3002 : Step(79): len = 57777.5, overlap = 12
PHY-3002 : Step(80): len = 56219, overlap = 12.0938
PHY-3002 : Step(81): len = 54931.6, overlap = 13.25
PHY-3002 : Step(82): len = 54059, overlap = 13.75
PHY-3002 : Step(83): len = 52476.9, overlap = 14.2188
PHY-3002 : Step(84): len = 51050.2, overlap = 16.5312
PHY-3002 : Step(85): len = 49656.2, overlap = 18.625
PHY-3002 : Step(86): len = 48726.7, overlap = 19.4688
PHY-3002 : Step(87): len = 48045.3, overlap = 20.8438
PHY-3002 : Step(88): len = 47484.4, overlap = 21.2188
PHY-3002 : Step(89): len = 46587.2, overlap = 22.6562
PHY-3002 : Step(90): len = 45566.9, overlap = 22.0938
PHY-3002 : Step(91): len = 45018.3, overlap = 23.9062
PHY-3002 : Step(92): len = 44627.5, overlap = 24.6562
PHY-3002 : Step(93): len = 43972.1, overlap = 24.75
PHY-3002 : Step(94): len = 43599.6, overlap = 24.625
PHY-3002 : Step(95): len = 42960.4, overlap = 26.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000238526
PHY-3002 : Step(96): len = 42878.3, overlap = 26.3125
PHY-3002 : Step(97): len = 43001.3, overlap = 26.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000477052
PHY-3002 : Step(98): len = 42937.2, overlap = 25.25
PHY-3002 : Step(99): len = 43037.8, overlap = 24.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062461s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.9936e-05
PHY-3002 : Step(100): len = 43239.9, overlap = 69.5
PHY-3002 : Step(101): len = 44825.9, overlap = 64.5938
PHY-3002 : Step(102): len = 46137, overlap = 53.1562
PHY-3002 : Step(103): len = 45724.4, overlap = 52.625
PHY-3002 : Step(104): len = 45214.6, overlap = 53.3125
PHY-3002 : Step(105): len = 45111.8, overlap = 50.8125
PHY-3002 : Step(106): len = 44828.6, overlap = 50.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000119872
PHY-3002 : Step(107): len = 44764.8, overlap = 50.75
PHY-3002 : Step(108): len = 45453.5, overlap = 48.4375
PHY-3002 : Step(109): len = 45773.5, overlap = 47.8438
PHY-3002 : Step(110): len = 45733.7, overlap = 46.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000239744
PHY-3002 : Step(111): len = 45651.5, overlap = 45.875
PHY-3002 : Step(112): len = 46009.9, overlap = 38.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000479488
PHY-3002 : Step(113): len = 46562.4, overlap = 35.4375
PHY-3002 : Step(114): len = 47210.4, overlap = 33.25
PHY-3002 : Step(115): len = 48693.4, overlap = 31.4062
PHY-3002 : Step(116): len = 49185.2, overlap = 24.9062
PHY-3002 : Step(117): len = 48441, overlap = 25.4062
PHY-3002 : Step(118): len = 48119.5, overlap = 26.0625
PHY-3002 : Step(119): len = 47850.1, overlap = 25.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7727, tnet num: 2181, tinst num: 1624, tnode num: 10915, tedge num: 13017.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.34 peak overflow 3.47
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51032, over cnt = 242(0%), over = 1050, worst = 17
PHY-1001 : End global iterations;  0.071859s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (130.5%)

PHY-1001 : Congestion index: top1 = 39.01, top5 = 24.58, top10 = 15.56, top15 = 11.02.
PHY-1001 : End incremental global routing;  0.120873s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (103.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067722s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.217259s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (107.9%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1663/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51032, over cnt = 242(0%), over = 1050, worst = 17
PHY-1002 : len = 57232, over cnt = 170(0%), over = 421, worst = 13
PHY-1002 : len = 61280, over cnt = 33(0%), over = 35, worst = 3
PHY-1002 : len = 61712, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 61968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094977s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (115.2%)

PHY-1001 : Congestion index: top1 = 35.58, top5 = 24.41, top10 = 17.20, top15 = 12.66.
OPT-1001 : End congestion update;  0.139345s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (112.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058452s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.200201s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 216, reserve = 179, peak = 216.
OPT-1001 : End physical optimization;  0.686072s wall, 0.718750s user + 0.062500s system = 0.781250s CPU (113.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 379 LUT to BLE ...
SYN-4008 : Packed 379 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 98 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 692 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1071/1397 primitive instances ...
PHY-3001 : End packing;  0.048221s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (129.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-1001 : 1473 nets have 2 pins
RUN-1001 : 419 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 48159.6, Over = 54.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6504, tnet num: 2006, tinst num: 835, tnode num: 8813, tedge num: 11393.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.310325s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.78551e-05
PHY-3002 : Step(120): len = 47639.2, overlap = 53.75
PHY-3002 : Step(121): len = 47586, overlap = 56
PHY-3002 : Step(122): len = 47425.7, overlap = 56
PHY-3002 : Step(123): len = 47261, overlap = 58.25
PHY-3002 : Step(124): len = 47161.1, overlap = 58.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.57102e-05
PHY-3002 : Step(125): len = 47527.8, overlap = 55.75
PHY-3002 : Step(126): len = 48008.6, overlap = 54
PHY-3002 : Step(127): len = 48456.1, overlap = 53.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00011142
PHY-3002 : Step(128): len = 49360.2, overlap = 50.5
PHY-3002 : Step(129): len = 50325.7, overlap = 46.75
PHY-3002 : Step(130): len = 50453.2, overlap = 45.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090326s wall, 0.062500s user + 0.125000s system = 0.187500s CPU (207.6%)

PHY-3001 : Trial Legalized: Len = 64243.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048520s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000553441
PHY-3002 : Step(131): len = 61722.1, overlap = 4.75
PHY-3002 : Step(132): len = 60041.4, overlap = 12.25
PHY-3002 : Step(133): len = 58439.4, overlap = 17.5
PHY-3002 : Step(134): len = 57417, overlap = 19.25
PHY-3002 : Step(135): len = 56636.2, overlap = 19.25
PHY-3002 : Step(136): len = 56211.8, overlap = 19.75
PHY-3002 : Step(137): len = 55861.5, overlap = 20.5
PHY-3002 : Step(138): len = 55493.4, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00110688
PHY-3002 : Step(139): len = 56031.2, overlap = 18.75
PHY-3002 : Step(140): len = 56175.2, overlap = 19.5
PHY-3002 : Step(141): len = 56190.3, overlap = 19.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00221376
PHY-3002 : Step(142): len = 56385.2, overlap = 19
PHY-3002 : Step(143): len = 56475, overlap = 18.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004716s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 60873.2, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005364s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 2, deltaY = 2, maxDist = 1.
PHY-3001 : Final: Len = 60857.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6504, tnet num: 2006, tinst num: 835, tnode num: 8813, tedge num: 11393.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 166/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67968, over cnt = 152(0%), over = 201, worst = 3
PHY-1002 : len = 68656, over cnt = 64(0%), over = 78, worst = 3
PHY-1002 : len = 69416, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 69520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.103923s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (105.2%)

PHY-1001 : Congestion index: top1 = 32.03, top5 = 23.06, top10 = 18.03, top15 = 14.12.
PHY-1001 : End incremental global routing;  0.154638s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (101.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059612s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.243719s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (102.6%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1781/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005648s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (276.7%)

PHY-1001 : Congestion index: top1 = 32.03, top5 = 23.06, top10 = 18.03, top15 = 14.12.
OPT-1001 : End congestion update;  0.050032s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047681s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 797 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 60856.6, Over = 0
PHY-3001 : End spreading;  0.005621s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 60856.6, Over = 0
PHY-3001 : End incremental legalization;  0.039096s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.9%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150061s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.7%)

OPT-1001 : Current memory(MB): used = 224, reserve = 188, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048269s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1777/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007289s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (214.4%)

PHY-1001 : Congestion index: top1 = 32.03, top5 = 23.07, top10 = 18.04, top15 = 14.12.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056080s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.551724
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.838995s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (98.7%)

RUN-1003 : finish command "place" in  5.016065s wall, 7.187500s user + 2.656250s system = 9.843750s CPU (196.2%)

RUN-1004 : used memory is 202 MB, reserved memory is 166 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-1001 : 1473 nets have 2 pins
RUN-1001 : 419 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6504, tnet num: 2006, tinst num: 835, tnode num: 8813, tedge num: 11393.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66448, over cnt = 147(0%), over = 208, worst = 3
PHY-1002 : len = 67320, over cnt = 74(0%), over = 84, worst = 3
PHY-1002 : len = 68424, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 68440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109884s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (113.8%)

PHY-1001 : Congestion index: top1 = 32.00, top5 = 22.80, top10 = 17.79, top15 = 13.92.
PHY-1001 : End global routing;  0.160041s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (117.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 205, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 497, reserve = 466, peak = 497.
PHY-1001 : End build detailed router design. 3.181056s wall, 3.140625s user + 0.046875s system = 3.187500s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34040, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.244729s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 529, reserve = 499, peak = 529.
PHY-1001 : End phase 1; 1.250449s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 176088, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 532.
PHY-1001 : End initial routed; 0.946925s wall, 1.875000s user + 0.125000s system = 2.000000s CPU (211.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1779(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.353   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366134s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.4%)

PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 533.
PHY-1001 : End phase 2; 1.313143s wall, 2.250000s user + 0.125000s system = 2.375000s CPU (180.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176088, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014225s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176088, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024139s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (129.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176104, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019605s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (159.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1779(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.353   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.374854s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.176889s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (106.0%)

PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End phase 3; 0.731724s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (102.5%)

PHY-1003 : Routed, final wirelength = 176104
PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End export database. 0.009544s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.671720s wall, 7.562500s user + 0.187500s system = 7.750000s CPU (116.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6504, tnet num: 2006, tinst num: 835, tnode num: 8813, tedge num: 11393.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.552329s wall, 8.468750s user + 0.187500s system = 8.656250s CPU (114.6%)

RUN-1004 : used memory is 498 MB, reserved memory is 465 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      822   out of  19600    4.19%
#reg                     1048   out of  19600    5.35%
#le                      1514
  #lut only               466   out of   1514   30.78%
  #reg only               692   out of   1514   45.71%
  #lut&reg                356   out of   1514   23.51%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         465
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1514   |603     |219     |1079    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1109   |293     |129     |899     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |19      |9       |19      |0       |0       |
|    demodu                  |Demodulation                                     |543    |132     |58      |435     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |52     |4       |0       |52      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |14      |0       |27      |0       |0       |
|    integ                   |Integration                                      |136    |17      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |68     |28      |14      |64      |0       |1       |
|    rs422                   |Rs422Output                                      |309    |77      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |20      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |113    |101     |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |55      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1437  
    #2          2       293   
    #3          3       109   
    #4          4        17   
    #5        5-10       81   
    #6        11-50      27   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6504, tnet num: 2006, tinst num: 835, tnode num: 8813, tedge num: 11393.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 835
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2008, pip num: 14601
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1295 valid insts, and 38694 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.181975s wall, 17.312500s user + 0.171875s system = 17.484375s CPU (549.5%)

RUN-1004 : used memory is 518 MB, reserved memory is 486 MB, peak memory is 663 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_152615.log"
