============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Aug  4 15:01:04 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1627 instances
RUN-0007 : 380 luts, 979 seqs, 144 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2183 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1648 nets have 2 pins
RUN-1001 : 419 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1625 instances, 380 luts, 979 seqs, 219 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7713, tnet num: 2181, tinst num: 1625, tnode num: 10898, tedge num: 12989.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.270133s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (98.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 542199
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1625.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 448045, overlap = 18
PHY-3002 : Step(2): len = 345638, overlap = 15.75
PHY-3002 : Step(3): len = 325613, overlap = 15.75
PHY-3002 : Step(4): len = 313838, overlap = 20.25
PHY-3002 : Step(5): len = 307392, overlap = 20.25
PHY-3002 : Step(6): len = 303174, overlap = 20.25
PHY-3002 : Step(7): len = 296476, overlap = 20.25
PHY-3002 : Step(8): len = 290844, overlap = 20.25
PHY-3002 : Step(9): len = 283355, overlap = 15.75
PHY-3002 : Step(10): len = 279484, overlap = 15.75
PHY-3002 : Step(11): len = 273230, overlap = 15.75
PHY-3002 : Step(12): len = 268553, overlap = 15.75
PHY-3002 : Step(13): len = 261496, overlap = 15.75
PHY-3002 : Step(14): len = 257646, overlap = 18
PHY-3002 : Step(15): len = 251507, overlap = 18
PHY-3002 : Step(16): len = 245899, overlap = 18
PHY-3002 : Step(17): len = 240495, overlap = 18
PHY-3002 : Step(18): len = 237501, overlap = 18
PHY-3002 : Step(19): len = 226375, overlap = 15.75
PHY-3002 : Step(20): len = 221354, overlap = 18
PHY-3002 : Step(21): len = 218253, overlap = 18
PHY-3002 : Step(22): len = 210263, overlap = 18
PHY-3002 : Step(23): len = 198613, overlap = 15.75
PHY-3002 : Step(24): len = 196492, overlap = 18
PHY-3002 : Step(25): len = 190409, overlap = 15.75
PHY-3002 : Step(26): len = 138519, overlap = 15.75
PHY-3002 : Step(27): len = 134897, overlap = 18
PHY-3002 : Step(28): len = 133380, overlap = 15.75
PHY-3002 : Step(29): len = 126661, overlap = 15.75
PHY-3002 : Step(30): len = 122687, overlap = 15.75
PHY-3002 : Step(31): len = 118197, overlap = 15.75
PHY-3002 : Step(32): len = 116035, overlap = 20.25
PHY-3002 : Step(33): len = 112990, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000151635
PHY-3002 : Step(34): len = 114055, overlap = 4.5
PHY-3002 : Step(35): len = 113804, overlap = 4.5
PHY-3002 : Step(36): len = 112428, overlap = 6.75
PHY-3002 : Step(37): len = 111114, overlap = 13.5
PHY-3002 : Step(38): len = 108738, overlap = 11.25
PHY-3002 : Step(39): len = 105904, overlap = 4.5
PHY-3002 : Step(40): len = 105460, overlap = 4.5
PHY-3002 : Step(41): len = 103375, overlap = 4.5
PHY-3002 : Step(42): len = 101358, overlap = 13.5
PHY-3002 : Step(43): len = 99673.2, overlap = 6.75
PHY-3002 : Step(44): len = 99005.3, overlap = 6.75
PHY-3002 : Step(45): len = 97153.3, overlap = 6.75
PHY-3002 : Step(46): len = 94808, overlap = 11.25
PHY-3002 : Step(47): len = 93355, overlap = 9
PHY-3002 : Step(48): len = 92804.5, overlap = 6.75
PHY-3002 : Step(49): len = 90856.9, overlap = 9
PHY-3002 : Step(50): len = 87779.7, overlap = 11.25
PHY-3002 : Step(51): len = 85855.3, overlap = 11.25
PHY-3002 : Step(52): len = 85389.9, overlap = 9.0625
PHY-3002 : Step(53): len = 83938.1, overlap = 6.75
PHY-3002 : Step(54): len = 78916.4, overlap = 11.25
PHY-3002 : Step(55): len = 77889.3, overlap = 9
PHY-3002 : Step(56): len = 76966.6, overlap = 9
PHY-3002 : Step(57): len = 76333.6, overlap = 9.0625
PHY-3002 : Step(58): len = 75786.2, overlap = 9.0625
PHY-3002 : Step(59): len = 75348.5, overlap = 11.5625
PHY-3002 : Step(60): len = 74987.3, overlap = 9.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000303269
PHY-3002 : Step(61): len = 75047, overlap = 9.375
PHY-3002 : Step(62): len = 75059, overlap = 9.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000606539
PHY-3002 : Step(63): len = 74938.4, overlap = 9.4375
PHY-3002 : Step(64): len = 74900.3, overlap = 9.4375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006784s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067318s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(65): len = 76930.6, overlap = 5.75
PHY-3002 : Step(66): len = 74511.8, overlap = 5.6875
PHY-3002 : Step(67): len = 72228.7, overlap = 5.75
PHY-3002 : Step(68): len = 70468.9, overlap = 5.6875
PHY-3002 : Step(69): len = 67943.6, overlap = 3.875
PHY-3002 : Step(70): len = 66151.9, overlap = 3.9375
PHY-3002 : Step(71): len = 64820.7, overlap = 4.3125
PHY-3002 : Step(72): len = 63513.8, overlap = 4.125
PHY-3002 : Step(73): len = 60657.1, overlap = 4.3125
PHY-3002 : Step(74): len = 57874.6, overlap = 3.9375
PHY-3002 : Step(75): len = 56602.4, overlap = 5.5625
PHY-3002 : Step(76): len = 55139.3, overlap = 6.4375
PHY-3002 : Step(77): len = 54422.9, overlap = 6.5
PHY-3002 : Step(78): len = 53885, overlap = 6.625
PHY-3002 : Step(79): len = 53450.2, overlap = 7
PHY-3002 : Step(80): len = 52423.5, overlap = 8.9375
PHY-3002 : Step(81): len = 52003.8, overlap = 8.75
PHY-3002 : Step(82): len = 51446.4, overlap = 8.0625
PHY-3002 : Step(83): len = 50712.2, overlap = 8.4375
PHY-3002 : Step(84): len = 49788.3, overlap = 8.0625
PHY-3002 : Step(85): len = 49001.9, overlap = 9.9375
PHY-3002 : Step(86): len = 48325.1, overlap = 10.75
PHY-3002 : Step(87): len = 47686.9, overlap = 13.4062
PHY-3002 : Step(88): len = 47138.9, overlap = 13.375
PHY-3002 : Step(89): len = 46675, overlap = 13.3125
PHY-3002 : Step(90): len = 46275.7, overlap = 13.8125
PHY-3002 : Step(91): len = 46029.8, overlap = 13.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000376049
PHY-3002 : Step(92): len = 45919.4, overlap = 14.4062
PHY-3002 : Step(93): len = 45854, overlap = 14.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000752098
PHY-3002 : Step(94): len = 45719.8, overlap = 13.875
PHY-3002 : Step(95): len = 45684.1, overlap = 13.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069686s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.70854e-05
PHY-3002 : Step(96): len = 46256.6, overlap = 62.6875
PHY-3002 : Step(97): len = 46768.3, overlap = 50.5
PHY-3002 : Step(98): len = 47540.7, overlap = 51
PHY-3002 : Step(99): len = 47988.9, overlap = 49.0312
PHY-3002 : Step(100): len = 47638, overlap = 49.3438
PHY-3002 : Step(101): len = 47063.9, overlap = 48.5312
PHY-3002 : Step(102): len = 46784.8, overlap = 48.7812
PHY-3002 : Step(103): len = 46576.8, overlap = 47.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000174171
PHY-3002 : Step(104): len = 46926.5, overlap = 46.3125
PHY-3002 : Step(105): len = 47120.4, overlap = 45.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000348341
PHY-3002 : Step(106): len = 47503.4, overlap = 44.75
PHY-3002 : Step(107): len = 48218.7, overlap = 43.4062
PHY-3002 : Step(108): len = 49079.5, overlap = 42.2812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7713, tnet num: 2181, tinst num: 1625, tnode num: 10898, tedge num: 12989.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 90.78 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52664, over cnt = 256(0%), over = 1114, worst = 20
PHY-1001 : End global iterations;  0.078771s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.2%)

PHY-1001 : Congestion index: top1 = 45.15, top5 = 25.81, top10 = 15.97, top15 = 11.23.
PHY-1001 : End incremental global routing;  0.127377s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066935s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.224334s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.5%)

OPT-1001 : Current memory(MB): used = 214, reserve = 177, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1667/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52664, over cnt = 256(0%), over = 1114, worst = 20
PHY-1002 : len = 60784, over cnt = 188(0%), over = 418, worst = 20
PHY-1002 : len = 64640, over cnt = 40(0%), over = 69, worst = 11
PHY-1002 : len = 65296, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 65808, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106406s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (102.8%)

PHY-1001 : Congestion index: top1 = 39.09, top5 = 26.08, top10 = 18.18, top15 = 13.19.
OPT-1001 : End congestion update;  0.150672s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056118s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.209328s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (104.5%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.699417s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 88 SEQ with LUT/SLICE
SYN-4006 : 124 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1082/1408 primitive instances ...
PHY-3001 : End packing;  0.050655s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 48822.6, Over = 67
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 835, tnode num: 8776, tedge num: 11349.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.302299s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.64936e-05
PHY-3002 : Step(109): len = 48293.8, overlap = 68
PHY-3002 : Step(110): len = 48113, overlap = 68
PHY-3002 : Step(111): len = 47858.7, overlap = 69.25
PHY-3002 : Step(112): len = 47743.5, overlap = 69.5
PHY-3002 : Step(113): len = 47924.7, overlap = 71
PHY-3002 : Step(114): len = 47992.4, overlap = 71.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.29872e-05
PHY-3002 : Step(115): len = 48257.4, overlap = 69.5
PHY-3002 : Step(116): len = 48855.1, overlap = 70.25
PHY-3002 : Step(117): len = 49868.6, overlap = 66.5
PHY-3002 : Step(118): len = 50129.6, overlap = 65
PHY-3002 : Step(119): len = 50247.2, overlap = 64.25
PHY-3002 : Step(120): len = 50407.9, overlap = 61
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000105974
PHY-3002 : Step(121): len = 50896.3, overlap = 60.75
PHY-3002 : Step(122): len = 51155.6, overlap = 60.75
PHY-3002 : Step(123): len = 51371.3, overlap = 61
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.068836s wall, 0.062500s user + 0.125000s system = 0.187500s CPU (272.4%)

PHY-3001 : Trial Legalized: Len = 65041.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050021s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000455097
PHY-3002 : Step(124): len = 62617.1, overlap = 3.25
PHY-3002 : Step(125): len = 60435.2, overlap = 9.75
PHY-3002 : Step(126): len = 58078.7, overlap = 18.75
PHY-3002 : Step(127): len = 57057.2, overlap = 21.25
PHY-3002 : Step(128): len = 56607.7, overlap = 23.75
PHY-3002 : Step(129): len = 56234.6, overlap = 24
PHY-3002 : Step(130): len = 55931.2, overlap = 26.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000910193
PHY-3002 : Step(131): len = 56381.8, overlap = 24.25
PHY-3002 : Step(132): len = 56586.1, overlap = 23.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00182039
PHY-3002 : Step(133): len = 56835.1, overlap = 23
PHY-3002 : Step(134): len = 56972.4, overlap = 23.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004694s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 60980.8, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005518s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 1, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 61078.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 835, tnode num: 8776, tedge num: 11349.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 77/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67528, over cnt = 151(0%), over = 226, worst = 7
PHY-1002 : len = 68696, over cnt = 50(0%), over = 55, worst = 2
PHY-1002 : len = 69216, over cnt = 14(0%), over = 17, worst = 2
PHY-1002 : len = 69504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128212s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (121.9%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.59, top10 = 17.50, top15 = 13.82.
PHY-1001 : End incremental global routing;  0.178326s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (113.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059224s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (105.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.265422s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (106.0%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1800/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005836s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (267.7%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.59, top10 = 17.50, top15 = 13.82.
OPT-1001 : End congestion update;  0.051603s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (121.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046764s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 797 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 61023.6, Over = 0
PHY-3001 : End spreading;  0.005244s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 61023.6, Over = 0
PHY-3001 : End incremental legalization;  0.036142s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (259.4%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.146235s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (149.6%)

OPT-1001 : Current memory(MB): used = 224, reserve = 187, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047794s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1796/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008043s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.31, top5 = 22.58, top10 = 17.49, top15 = 13.80.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047761s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (130.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.849381s wall, 0.875000s user + 0.078125s system = 0.953125s CPU (112.2%)

RUN-1003 : finish command "place" in  4.996614s wall, 7.515625s user + 2.687500s system = 10.203125s CPU (204.2%)

RUN-1004 : used memory is 200 MB, reserved memory is 163 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 410 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 835, tnode num: 8776, tedge num: 11349.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66608, over cnt = 145(0%), over = 224, worst = 7
PHY-1002 : len = 67784, over cnt = 56(0%), over = 64, worst = 3
PHY-1002 : len = 68344, over cnt = 25(0%), over = 25, worst = 1
PHY-1002 : len = 68720, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132587s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (153.2%)

PHY-1001 : Congestion index: top1 = 31.10, top5 = 22.25, top10 = 17.31, top15 = 13.67.
PHY-1001 : End global routing;  0.181583s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (137.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 204, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 468, peak = 500.
PHY-1001 : End build detailed router design. 3.232232s wall, 3.218750s user + 0.015625s system = 3.234375s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33272, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.282142s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.287777s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 174696, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 534.
PHY-1001 : End initial routed; 0.985806s wall, 1.859375s user + 0.093750s system = 1.953125s CPU (198.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1778(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.189   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350150s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.6%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 1.336040s wall, 2.218750s user + 0.093750s system = 2.312500s CPU (173.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 174696, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014425s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 174696, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.020732s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (150.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 174720, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.018739s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (83.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1778(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.189   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363103s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.178670s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.9%)

PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End phase 3; 0.717352s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.2%)

PHY-1003 : Routed, final wirelength = 174720
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.009451s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (165.3%)

PHY-1001 : End detail routing;  6.764923s wall, 7.609375s user + 0.109375s system = 7.718750s CPU (114.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 835, tnode num: 8776, tedge num: 11349.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.672878s wall, 8.562500s user + 0.140625s system = 8.703125s CPU (113.4%)

RUN-1004 : used memory is 504 MB, reserved memory is 476 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      823   out of  19600    4.20%
#reg                     1047   out of  19600    5.34%
#le                      1525
  #lut only               478   out of   1525   31.34%
  #reg only               702   out of   1525   46.03%
  #lut&reg                345   out of   1525   22.62%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         460
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1525   |604     |219     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1115   |293     |126     |898     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |22      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |537    |123     |58      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |14      |0       |28      |0       |0       |
|    integ                   |Integration                                      |140    |16      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |64     |23      |14      |60      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |85      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |114    |101     |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |22     |17      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |57     |56      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1445  
    #2          2       292   
    #3          3       104   
    #4          4        14   
    #5        5-10       80   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6480, tnet num: 2006, tinst num: 835, tnode num: 8776, tedge num: 11349.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 835
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2008, pip num: 14395
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1269 valid insts, and 38365 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.047692s wall, 17.656250s user + 0.062500s system = 17.718750s CPU (581.4%)

RUN-1004 : used memory is 523 MB, reserved memory is 492 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230804_150104.log"
