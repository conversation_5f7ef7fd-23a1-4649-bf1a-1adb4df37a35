============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 14:51:23 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 4.4000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 4.4000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 38 trigger nets, 38 data nets.
KIT-1004 : Chipwatcher code = 0101111100011010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2441/23 useful/useless nets, 1477/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 2126/20 useful/useless nets, 1884/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 378 better
SYN-1014 : Optimize round 2
SYN-1032 : 1822/45 useful/useless nets, 1580/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1870/295 useful/useless nets, 1661/43 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 383 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 36 instances.
SYN-2501 : Optimize round 1, 74 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2298/5 useful/useless nets, 2089/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8860, tnet num: 2298, tinst num: 2088, tnode num: 11114, tedge num: 13565.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2298 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 222 (3.48), #lev = 7 (1.82)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 222 (3.47), #lev = 7 (1.93)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 514 instances into 222 LUTs, name keeping = 71%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 372 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 120 adder to BLE ...
SYN-4008 : Packed 120 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.140186s wall, 1.078125s user + 0.062500s system = 1.140625s CPU (100.0%)

RUN-1004 : used memory is 144 MB, reserved memory is 101 MB, peak memory is 168 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (255 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1503 instances
RUN-0007 : 592 luts, 678 seqs, 116 mslices, 70 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1719 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 978 nets have 2 pins
RUN-1001 : 549 nets have [3 - 5] pins
RUN-1001 : 94 nets have [6 - 10] pins
RUN-1001 : 62 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     160     
RUN-1001 :   No   |  No   |  Yes  |     118     
RUN-1001 :   No   |  Yes  |  No   |     92      
RUN-1001 :   Yes  |  No   |  No   |     57      
RUN-1001 :   Yes  |  No   |  Yes  |     251     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 18
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1501 instances, 592 luts, 678 seqs, 186 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7589, tnet num: 1717, tinst num: 1501, tnode num: 9938, tedge num: 12532.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1717 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.137138s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 400159
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1501.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 320085, overlap = 76.5
PHY-3002 : Step(2): len = 254370, overlap = 76.5
PHY-3002 : Step(3): len = 218432, overlap = 76.5
PHY-3002 : Step(4): len = 192375, overlap = 76.5
PHY-3002 : Step(5): len = 169420, overlap = 76.5
PHY-3002 : Step(6): len = 149446, overlap = 76.5
PHY-3002 : Step(7): len = 134392, overlap = 76.5
PHY-3002 : Step(8): len = 118135, overlap = 76.5
PHY-3002 : Step(9): len = 104791, overlap = 76.5
PHY-3002 : Step(10): len = 92870.4, overlap = 76.5
PHY-3002 : Step(11): len = 84782.2, overlap = 76.5
PHY-3002 : Step(12): len = 76721.5, overlap = 72
PHY-3002 : Step(13): len = 69913, overlap = 72
PHY-3002 : Step(14): len = 65087, overlap = 67.5
PHY-3002 : Step(15): len = 60105.5, overlap = 69.8438
PHY-3002 : Step(16): len = 54884.5, overlap = 71.0625
PHY-3002 : Step(17): len = 51425.4, overlap = 71.9688
PHY-3002 : Step(18): len = 48647.4, overlap = 80.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.36068e-06
PHY-3002 : Step(19): len = 49943, overlap = 81.1562
PHY-3002 : Step(20): len = 54661.9, overlap = 68.3438
PHY-3002 : Step(21): len = 51563.5, overlap = 72.4062
PHY-3002 : Step(22): len = 50776.3, overlap = 74.6562
PHY-3002 : Step(23): len = 49530.2, overlap = 74.125
PHY-3002 : Step(24): len = 48862.2, overlap = 69.625
PHY-3002 : Step(25): len = 47005.5, overlap = 71.75
PHY-3002 : Step(26): len = 46621.1, overlap = 69.3438
PHY-3002 : Step(27): len = 46006.6, overlap = 71.6562
PHY-3002 : Step(28): len = 46043, overlap = 71.7812
PHY-3002 : Step(29): len = 45702.4, overlap = 71.8438
PHY-3002 : Step(30): len = 45157.6, overlap = 71.9688
PHY-3002 : Step(31): len = 44474.7, overlap = 76.4688
PHY-3002 : Step(32): len = 43865.9, overlap = 76.4375
PHY-3002 : Step(33): len = 43406.2, overlap = 76.5
PHY-3002 : Step(34): len = 43207.3, overlap = 76.6875
PHY-3002 : Step(35): len = 42453.2, overlap = 78.2188
PHY-3002 : Step(36): len = 41946.2, overlap = 75.7812
PHY-3002 : Step(37): len = 41689.8, overlap = 71.0625
PHY-3002 : Step(38): len = 41261.1, overlap = 73.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.87214e-05
PHY-3002 : Step(39): len = 41343, overlap = 73.2188
PHY-3002 : Step(40): len = 41434.7, overlap = 73.25
PHY-3002 : Step(41): len = 41503, overlap = 68.5938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 3.74427e-05
PHY-3002 : Step(42): len = 41559, overlap = 68.6562
PHY-3002 : Step(43): len = 41580.2, overlap = 68.125
PHY-3002 : Step(44): len = 41598.8, overlap = 68.125
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 7.48854e-05
PHY-3002 : Step(45): len = 41680.4, overlap = 68.125
PHY-3002 : Step(46): len = 41690.6, overlap = 68
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.000149771
PHY-3002 : Step(47): len = 41692.1, overlap = 68.125
PHY-3002 : Step(48): len = 41727.8, overlap = 61.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003776s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1717 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.042473s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (110.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000180781
PHY-3002 : Step(49): len = 45012.4, overlap = 18.875
PHY-3002 : Step(50): len = 45028.3, overlap = 18.7812
PHY-3002 : Step(51): len = 44795.2, overlap = 17.6562
PHY-3002 : Step(52): len = 44756.5, overlap = 16.1875
PHY-3002 : Step(53): len = 44559.7, overlap = 15.5625
PHY-3002 : Step(54): len = 44409.5, overlap = 14.9688
PHY-3002 : Step(55): len = 44050.7, overlap = 15.9688
PHY-3002 : Step(56): len = 43826, overlap = 16.375
PHY-3002 : Step(57): len = 43455, overlap = 17.7812
PHY-3002 : Step(58): len = 43006.9, overlap = 19.875
PHY-3002 : Step(59): len = 42478.6, overlap = 22.5625
PHY-3002 : Step(60): len = 41962.6, overlap = 22.6875
PHY-3002 : Step(61): len = 41452.2, overlap = 22.1562
PHY-3002 : Step(62): len = 41050.1, overlap = 21.3438
PHY-3002 : Step(63): len = 40645.7, overlap = 21.0938
PHY-3002 : Step(64): len = 40124.1, overlap = 21.2812
PHY-3002 : Step(65): len = 39724.8, overlap = 20
PHY-3002 : Step(66): len = 39210.9, overlap = 21.3438
PHY-3002 : Step(67): len = 39047.4, overlap = 19.7812
PHY-3002 : Step(68): len = 38755, overlap = 23.0312
PHY-3002 : Step(69): len = 38537, overlap = 24.4375
PHY-3002 : Step(70): len = 38188.9, overlap = 23.3125
PHY-3002 : Step(71): len = 37804.9, overlap = 23.0625
PHY-3002 : Step(72): len = 37470.9, overlap = 23.875
PHY-3002 : Step(73): len = 37167.7, overlap = 24.5938
PHY-3002 : Step(74): len = 36790.6, overlap = 22.5
PHY-3002 : Step(75): len = 36283.2, overlap = 22.5625
PHY-3002 : Step(76): len = 35912.5, overlap = 22.75
PHY-3002 : Step(77): len = 35944.2, overlap = 22.375
PHY-3002 : Step(78): len = 35878.9, overlap = 21.3438
PHY-3002 : Step(79): len = 35628.1, overlap = 20.8438
PHY-3002 : Step(80): len = 35395.6, overlap = 22.7188
PHY-3002 : Step(81): len = 35283.5, overlap = 23.4062
PHY-3002 : Step(82): len = 35180, overlap = 23.5312
PHY-3002 : Step(83): len = 35078.4, overlap = 23.5312
PHY-3002 : Step(84): len = 34921.4, overlap = 23.3438
PHY-3002 : Step(85): len = 35009.2, overlap = 21.8438
PHY-3002 : Step(86): len = 35042.4, overlap = 20.0625
PHY-3002 : Step(87): len = 34894.8, overlap = 19
PHY-3002 : Step(88): len = 34881, overlap = 21.5938
PHY-3002 : Step(89): len = 34648.5, overlap = 23
PHY-3002 : Step(90): len = 34554.4, overlap = 23.5312
PHY-3002 : Step(91): len = 34496.4, overlap = 23.5312
PHY-3002 : Step(92): len = 34631.4, overlap = 22.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000361562
PHY-3002 : Step(93): len = 34356.8, overlap = 23.0312
PHY-3002 : Step(94): len = 34338.1, overlap = 23.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000723124
PHY-3002 : Step(95): len = 34384, overlap = 22.9688
PHY-3002 : Step(96): len = 34384, overlap = 22.9688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00144625
PHY-3002 : Step(97): len = 34566.2, overlap = 23.2812
PHY-3002 : Step(98): len = 34566.2, overlap = 23.2812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00289249
PHY-3002 : Step(99): len = 34605.7, overlap = 22.9375
PHY-3002 : Step(100): len = 34611, overlap = 22.9375
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00578499
PHY-3002 : Step(101): len = 34535.8, overlap = 22.9375
PHY-3002 : Step(102): len = 34535.8, overlap = 22.9375
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.01157
PHY-3002 : Step(103): len = 34428.3, overlap = 22.9375
PHY-3002 : Step(104): len = 34428.3, overlap = 22.9375
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.02314
PHY-3002 : Step(105): len = 34362.7, overlap = 23
PHY-3002 : Step(106): len = 34362.7, overlap = 23
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1717 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.042668s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (109.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.21658e-05
PHY-3002 : Step(107): len = 36749.7, overlap = 53.4062
PHY-3002 : Step(108): len = 36749.7, overlap = 53.4062
PHY-3002 : Step(109): len = 36492.2, overlap = 53.2188
PHY-3002 : Step(110): len = 36492.2, overlap = 53.2188
PHY-3002 : Step(111): len = 36403.3, overlap = 51.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000144332
PHY-3002 : Step(112): len = 37232.4, overlap = 49.75
PHY-3002 : Step(113): len = 37232.4, overlap = 49.75
PHY-3002 : Step(114): len = 37099.3, overlap = 49.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000284405
PHY-3002 : Step(115): len = 37964.2, overlap = 42.875
PHY-3002 : Step(116): len = 38424.8, overlap = 41.6562
PHY-3002 : Step(117): len = 38660, overlap = 40
PHY-3002 : Step(118): len = 38758.2, overlap = 35.3125
PHY-3002 : Step(119): len = 39021.6, overlap = 33.8438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000568811
PHY-3002 : Step(120): len = 38754.7, overlap = 33.9375
PHY-3002 : Step(121): len = 38750.1, overlap = 33.9688
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00113762
PHY-3002 : Step(122): len = 38912, overlap = 30.5625
PHY-3002 : Step(123): len = 39055.5, overlap = 30.1562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7589, tnet num: 1717, tinst num: 1501, tnode num: 9938, tedge num: 12532.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 72.56 peak overflow 2.91
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1719.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 50856, over cnt = 238(0%), over = 782, worst = 15
PHY-1001 : End global iterations;  0.093545s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (83.5%)

PHY-1001 : Congestion index: top1 = 34.38, top5 = 21.48, top10 = 14.82, top15 = 10.89.
PHY-1001 : End incremental global routing;  0.146385s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (96.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1717 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053638s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1489 has valid locations, 34 needs to be replaced
PHY-3001 : design contains 1534 instances, 592 luts, 711 seqs, 186 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 39480.6
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7721, tnet num: 1750, tinst num: 1534, tnode num: 10169, tedge num: 12730.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1750 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.164160s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (104.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(124): len = 39586.9, overlap = 0.625
PHY-3002 : Step(125): len = 39586.9, overlap = 0.625
PHY-3002 : Step(126): len = 39656.1, overlap = 0.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1750 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.044944s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0746176
PHY-3002 : Step(127): len = 39662, overlap = 30.2812
PHY-3002 : Step(128): len = 39662, overlap = 30.2812
PHY-3001 : Final: Len = 39662, Over = 30.2812
PHY-3001 : End incremental placement;  0.310225s wall, 0.250000s user + 0.093750s system = 0.343750s CPU (110.8%)

OPT-1001 : Total overflow 73.31 peak overflow 2.91
OPT-1001 : End high-fanout net optimization;  0.546890s wall, 0.468750s user + 0.125000s system = 0.593750s CPU (108.6%)

OPT-1001 : Current memory(MB): used = 205, reserve = 159, peak = 205.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1177/1752.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52016, over cnt = 239(0%), over = 773, worst = 15
PHY-1002 : len = 56024, over cnt = 178(0%), over = 404, worst = 13
PHY-1002 : len = 59352, over cnt = 85(0%), over = 158, worst = 7
PHY-1002 : len = 60984, over cnt = 16(0%), over = 28, worst = 4
PHY-1002 : len = 61352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106206s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (147.1%)

PHY-1001 : Congestion index: top1 = 33.15, top5 = 22.08, top10 = 16.23, top15 = 12.22.
OPT-1001 : End congestion update;  0.150087s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (124.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1750 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043003s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (109.0%)

OPT-0007 : Start: WNS 5598 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.193315s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (121.2%)

OPT-1001 : Current memory(MB): used = 202, reserve = 157, peak = 205.
OPT-1001 : End physical optimization;  0.868540s wall, 0.828125s user + 0.140625s system = 0.968750s CPU (111.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 592 LUT to BLE ...
SYN-4008 : Packed 592 LUT and 199 SEQ to BLE.
SYN-4003 : Packing 512 remaining SEQ's ...
SYN-4005 : Packed 242 SEQ with LUT/SLICE
SYN-4006 : 176 single LUT's are left
SYN-4006 : 270 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 862/1229 primitive instances ...
PHY-3001 : End packing;  0.050539s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 717 instances
RUN-1001 : 335 mslices, 335 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1556 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 761 nets have 2 pins
RUN-1001 : 603 nets have [3 - 5] pins
RUN-1001 : 99 nets have [6 - 10] pins
RUN-1001 : 56 nets have [11 - 20] pins
RUN-1001 : 26 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 715 instances, 670 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-3001 : Cell area utilization is 8%
PHY-3001 : After packing: Len = 40542.4, Over = 45
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6766, tnet num: 1554, tinst num: 715, tnode num: 8574, tedge num: 11512.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1554 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.165117s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (94.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.38986e-05
PHY-3002 : Step(129): len = 40102.7, overlap = 44
PHY-3002 : Step(130): len = 40130, overlap = 50
PHY-3002 : Step(131): len = 39550.1, overlap = 48.75
PHY-3002 : Step(132): len = 39424.3, overlap = 48.5
PHY-3002 : Step(133): len = 39473, overlap = 48.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000127797
PHY-3002 : Step(134): len = 39587.5, overlap = 49.25
PHY-3002 : Step(135): len = 39989.7, overlap = 46.5
PHY-3002 : Step(136): len = 40664.2, overlap = 45
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000255595
PHY-3002 : Step(137): len = 41392.2, overlap = 43.5
PHY-3002 : Step(138): len = 41925.2, overlap = 42.5
PHY-3002 : Step(139): len = 41865.8, overlap = 40.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.112117s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (195.1%)

PHY-3001 : Trial Legalized: Len = 56407.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1554 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.040545s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00342208
PHY-3002 : Step(140): len = 52845.2, overlap = 7.25
PHY-3002 : Step(141): len = 50247.7, overlap = 11
PHY-3002 : Step(142): len = 48688.2, overlap = 14.75
PHY-3002 : Step(143): len = 47257.4, overlap = 17
PHY-3002 : Step(144): len = 46492.3, overlap = 21
PHY-3002 : Step(145): len = 46134.1, overlap = 20.75
PHY-3002 : Step(146): len = 45533, overlap = 21
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00684416
PHY-3002 : Step(147): len = 45543.9, overlap = 21.5
PHY-3002 : Step(148): len = 45298.4, overlap = 20.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0136883
PHY-3002 : Step(149): len = 45350.4, overlap = 20.75
PHY-3002 : Step(150): len = 45280, overlap = 21.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005317s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 51226.2, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004861s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 4, deltaY = 11, maxDist = 2.
PHY-3001 : Final: Len = 51892.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6766, tnet num: 1554, tinst num: 715, tnode num: 8574, tedge num: 11512.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 79/1556.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66592, over cnt = 215(0%), over = 346, worst = 6
PHY-1002 : len = 67776, over cnt = 123(0%), over = 173, worst = 4
PHY-1002 : len = 69048, over cnt = 46(0%), over = 59, worst = 3
PHY-1002 : len = 69776, over cnt = 2(0%), over = 4, worst = 3
PHY-1002 : len = 69840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.181154s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (112.1%)

PHY-1001 : Congestion index: top1 = 28.53, top5 = 21.96, top10 = 17.61, top15 = 13.94.
PHY-1001 : End incremental global routing;  0.239417s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (104.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1554 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054287s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.328722s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (104.6%)

OPT-1001 : Current memory(MB): used = 204, reserve = 159, peak = 205.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1351/1556.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006932s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 28.53, top5 = 21.96, top10 = 17.61, top15 = 13.94.
OPT-1001 : End congestion update;  0.055289s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1554 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047617s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (131.3%)

OPT-0007 : Start: WNS 5021 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.103186s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (106.0%)

OPT-1001 : Current memory(MB): used = 206, reserve = 161, peak = 206.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1554 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046439s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (67.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1351/1556.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006961s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 28.53, top5 = 21.96, top10 = 17.61, top15 = 13.94.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1554 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053278s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 5021 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 28.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 5021ps with logic level 7 
OPT-1001 : End physical optimization;  0.753436s wall, 0.750000s user + 0.015625s system = 0.765625s CPU (101.6%)

RUN-1003 : finish command "place" in  4.833929s wall, 7.656250s user + 2.328125s system = 9.984375s CPU (206.5%)

RUN-1004 : used memory is 188 MB, reserved memory is 141 MB, peak memory is 207 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 717 instances
RUN-1001 : 335 mslices, 335 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1556 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 761 nets have 2 pins
RUN-1001 : 603 nets have [3 - 5] pins
RUN-1001 : 99 nets have [6 - 10] pins
RUN-1001 : 56 nets have [11 - 20] pins
RUN-1001 : 26 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6766, tnet num: 1554, tinst num: 715, tnode num: 8574, tedge num: 11512.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 335 mslices, 335 lslices, 8 pads, 34 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1554 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65832, over cnt = 223(0%), over = 361, worst = 6
PHY-1002 : len = 67200, over cnt = 127(0%), over = 173, worst = 4
PHY-1002 : len = 68248, over cnt = 61(0%), over = 78, worst = 3
PHY-1002 : len = 69296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.170720s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.7%)

PHY-1001 : Congestion index: top1 = 28.69, top5 = 21.95, top10 = 17.56, top15 = 13.87.
PHY-1001 : End global routing;  0.220588s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 224, reserve = 179, peak = 224.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 484, reserve = 442, peak = 484.
PHY-1001 : End build detailed router design. 3.211402s wall, 3.171875s user + 0.046875s system = 3.218750s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 36320, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.594890s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 516, reserve = 475, peak = 516.
PHY-1001 : End phase 1; 0.600819s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (98.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 260352, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 518, reserve = 477, peak = 519.
PHY-1001 : End initial routed; 2.684924s wall, 3.078125s user + 0.156250s system = 3.234375s CPU (120.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1384(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   3.571   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.213739s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (102.3%)

PHY-1001 : Current memory(MB): used = 521, reserve = 480, peak = 521.
PHY-1001 : End phase 2; 2.898745s wall, 3.296875s user + 0.156250s system = 3.453125s CPU (119.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 260352, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.012753s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (122.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 259552, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.129786s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 259328, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.058005s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 259336, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.022541s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (69.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1384(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   3.571   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.212680s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (102.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 15 feed throughs used by 13 nets
PHY-1001 : End commit to database; 0.193012s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (97.1%)

PHY-1001 : Current memory(MB): used = 535, reserve = 494, peak = 536.
PHY-1001 : End phase 3; 0.743133s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (98.8%)

PHY-1003 : Routed, final wirelength = 259336
PHY-1001 : Current memory(MB): used = 536, reserve = 495, peak = 536.
PHY-1001 : End export database. 0.010447s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.646055s wall, 7.968750s user + 0.218750s system = 8.187500s CPU (107.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6766, tnet num: 1554, tinst num: 715, tnode num: 8574, tedge num: 11512.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.300700s wall, 8.609375s user + 0.234375s system = 8.843750s CPU (106.5%)

RUN-1004 : used memory is 490 MB, reserved memory is 448 MB, peak memory is 536 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      984   out of  19600    5.02%
#reg                      714   out of  19600    3.64%
#le                      1254
  #lut only               540   out of   1254   43.06%
  #reg only               270   out of   1254   21.53%
  #lut&reg                444   out of   1254   35.41%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    323
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         139
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1254   |798     |186     |720     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |119    |97      |11      |65      |0       |0       |
|    usms                            |Time_1ms        |30     |16      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |205    |121     |23      |130     |0       |0       |
|  uart                              |UART_Control    |119    |103     |4       |54      |0       |0       |
|    U0                              |speed_select_Tx |22     |11      |4       |16      |0       |0       |
|    U1                              |uart_tx         |16     |11      |0       |15      |0       |0       |
|    U2                              |Ctrl_Data       |81     |81      |0       |23      |0       |0       |
|  wendu                             |DS18B20         |176    |135     |41      |43      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |597    |319     |99      |401     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |597    |319     |99      |401     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |246    |107     |0       |246     |0       |0       |
|        reg_inst                    |register        |244    |106     |0       |244     |0       |0       |
|        tap_inst                    |tap             |2      |1       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |351    |212     |99      |155     |0       |0       |
|        bus_inst                    |bus_top         |118    |75      |42      |40      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |16     |10      |6       |4       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |25     |15      |10      |7       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |1      |0       |0       |1       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |75     |49      |26      |27      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |151    |97      |29      |86      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       752   
    #2          2       364   
    #3          3       178   
    #4          4        61   
    #5        5-10      106   
    #6        11-50      72   
    #7       51-100      3    
    #8       101-500     1    
  Average     3.07            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6766, tnet num: 1554, tinst num: 715, tnode num: 8574, tedge num: 11512.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1554 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 715
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1556, pip num: 17075
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 15
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1434 valid insts, and 45487 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011001110101111100011010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.489987s wall, 18.609375s user + 0.062500s system = 18.671875s CPU (535.0%)

RUN-1004 : used memory is 531 MB, reserved memory is 490 MB, peak memory is 652 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_145123.log"
