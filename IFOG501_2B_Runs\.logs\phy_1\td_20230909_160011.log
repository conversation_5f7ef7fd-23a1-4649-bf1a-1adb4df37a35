============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Sat Sep  9 16:00:11 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 20 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1609 instances
RUN-0007 : 368 luts, 992 seqs, 128 mslices, 72 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2135 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1603 nets have 2 pins
RUN-1001 : 416 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1607 instances, 368 luts, 992 seqs, 200 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7613, tnet num: 2133, tinst num: 1607, tnode num: 10809, tedge num: 12815.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.291815s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (101.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 567612
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1607.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 476280, overlap = 20.25
PHY-3002 : Step(2): len = 443438, overlap = 20.25
PHY-3002 : Step(3): len = 425759, overlap = 20.25
PHY-3002 : Step(4): len = 409828, overlap = 18
PHY-3002 : Step(5): len = 400961, overlap = 18
PHY-3002 : Step(6): len = 390051, overlap = 18
PHY-3002 : Step(7): len = 378758, overlap = 20.25
PHY-3002 : Step(8): len = 367731, overlap = 20.25
PHY-3002 : Step(9): len = 359145, overlap = 20.25
PHY-3002 : Step(10): len = 347278, overlap = 18
PHY-3002 : Step(11): len = 337356, overlap = 18
PHY-3002 : Step(12): len = 328793, overlap = 18
PHY-3002 : Step(13): len = 318443, overlap = 20.25
PHY-3002 : Step(14): len = 306691, overlap = 20.25
PHY-3002 : Step(15): len = 299788, overlap = 20.25
PHY-3002 : Step(16): len = 289904, overlap = 20.25
PHY-3002 : Step(17): len = 280880, overlap = 20.25
PHY-3002 : Step(18): len = 272418, overlap = 20.25
PHY-3002 : Step(19): len = 265403, overlap = 20.25
PHY-3002 : Step(20): len = 255769, overlap = 20.25
PHY-3002 : Step(21): len = 249360, overlap = 20.25
PHY-3002 : Step(22): len = 243168, overlap = 20.25
PHY-3002 : Step(23): len = 236841, overlap = 20.25
PHY-3002 : Step(24): len = 228706, overlap = 20.25
PHY-3002 : Step(25): len = 223574, overlap = 20.25
PHY-3002 : Step(26): len = 218929, overlap = 20.25
PHY-3002 : Step(27): len = 212333, overlap = 20.25
PHY-3002 : Step(28): len = 206666, overlap = 20.25
PHY-3002 : Step(29): len = 203299, overlap = 20.25
PHY-3002 : Step(30): len = 198500, overlap = 20.25
PHY-3002 : Step(31): len = 188917, overlap = 20.25
PHY-3002 : Step(32): len = 184887, overlap = 20.25
PHY-3002 : Step(33): len = 182606, overlap = 20.25
PHY-3002 : Step(34): len = 157256, overlap = 20.25
PHY-3002 : Step(35): len = 148728, overlap = 20.25
PHY-3002 : Step(36): len = 147928, overlap = 20.25
PHY-3002 : Step(37): len = 134596, overlap = 20.25
PHY-3002 : Step(38): len = 123995, overlap = 20.25
PHY-3002 : Step(39): len = 122132, overlap = 20.25
PHY-3002 : Step(40): len = 120862, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000116288
PHY-3002 : Step(41): len = 121806, overlap = 18
PHY-3002 : Step(42): len = 121589, overlap = 15.75
PHY-3002 : Step(43): len = 121036, overlap = 13.5
PHY-3002 : Step(44): len = 119726, overlap = 13.5
PHY-3002 : Step(45): len = 117339, overlap = 15.75
PHY-3002 : Step(46): len = 116372, overlap = 13.5
PHY-3002 : Step(47): len = 115578, overlap = 11.25
PHY-3002 : Step(48): len = 113167, overlap = 11.25
PHY-3002 : Step(49): len = 110921, overlap = 11.25
PHY-3002 : Step(50): len = 108604, overlap = 11.25
PHY-3002 : Step(51): len = 105485, overlap = 9
PHY-3002 : Step(52): len = 104881, overlap = 9
PHY-3002 : Step(53): len = 102873, overlap = 11.25
PHY-3002 : Step(54): len = 98105.1, overlap = 11.25
PHY-3002 : Step(55): len = 93966.6, overlap = 9
PHY-3002 : Step(56): len = 93110.6, overlap = 9
PHY-3002 : Step(57): len = 91859.3, overlap = 9
PHY-3002 : Step(58): len = 90675, overlap = 11.25
PHY-3002 : Step(59): len = 89469.9, overlap = 13.5
PHY-3002 : Step(60): len = 89288, overlap = 13.5
PHY-3002 : Step(61): len = 87520.9, overlap = 13.5
PHY-3002 : Step(62): len = 86363.8, overlap = 13.5
PHY-3002 : Step(63): len = 85975.9, overlap = 13.5
PHY-3002 : Step(64): len = 85217.3, overlap = 11.25
PHY-3002 : Step(65): len = 84611.3, overlap = 11.25
PHY-3002 : Step(66): len = 84142.8, overlap = 9
PHY-3002 : Step(67): len = 83985.1, overlap = 9
PHY-3002 : Step(68): len = 83035.6, overlap = 11.25
PHY-3002 : Step(69): len = 81986.1, overlap = 11.25
PHY-3002 : Step(70): len = 81170.8, overlap = 6.75
PHY-3002 : Step(71): len = 80110, overlap = 9
PHY-3002 : Step(72): len = 79285.3, overlap = 11.25
PHY-3002 : Step(73): len = 76403.8, overlap = 11.25
PHY-3002 : Step(74): len = 75653.2, overlap = 11.25
PHY-3002 : Step(75): len = 74651.2, overlap = 11.25
PHY-3002 : Step(76): len = 74121.5, overlap = 11.25
PHY-3002 : Step(77): len = 72417.6, overlap = 11.25
PHY-3002 : Step(78): len = 71171.7, overlap = 11.25
PHY-3002 : Step(79): len = 71359.1, overlap = 11.25
PHY-3002 : Step(80): len = 70687, overlap = 4.5
PHY-3002 : Step(81): len = 67781.6, overlap = 9
PHY-3002 : Step(82): len = 66506.5, overlap = 11.25
PHY-3002 : Step(83): len = 64985.1, overlap = 11.25
PHY-3002 : Step(84): len = 64519, overlap = 9
PHY-3002 : Step(85): len = 64339.3, overlap = 9
PHY-3002 : Step(86): len = 63878.5, overlap = 9
PHY-3002 : Step(87): len = 63085.6, overlap = 11.25
PHY-3002 : Step(88): len = 61490, overlap = 15.75
PHY-3002 : Step(89): len = 60683.6, overlap = 13.5
PHY-3002 : Step(90): len = 59834.7, overlap = 13.5
PHY-3002 : Step(91): len = 59497.5, overlap = 9
PHY-3002 : Step(92): len = 58860.8, overlap = 9
PHY-3002 : Step(93): len = 57712.8, overlap = 11.25
PHY-3002 : Step(94): len = 56153.3, overlap = 9
PHY-3002 : Step(95): len = 54772, overlap = 6.75
PHY-3002 : Step(96): len = 54420.5, overlap = 9
PHY-3002 : Step(97): len = 53843.2, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000232575
PHY-3002 : Step(98): len = 53752.5, overlap = 11.25
PHY-3002 : Step(99): len = 53816.9, overlap = 11.25
PHY-3002 : Step(100): len = 53840.7, overlap = 11.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00046515
PHY-3002 : Step(101): len = 53945.6, overlap = 9
PHY-3002 : Step(102): len = 53988.1, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007608s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066812s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(103): len = 58213.8, overlap = 4.5
PHY-3002 : Step(104): len = 57661.8, overlap = 3.1875
PHY-3002 : Step(105): len = 56771.4, overlap = 3.25
PHY-3002 : Step(106): len = 55917.3, overlap = 3.125
PHY-3002 : Step(107): len = 55106.9, overlap = 3.125
PHY-3002 : Step(108): len = 53893.8, overlap = 6.5625
PHY-3002 : Step(109): len = 52994.6, overlap = 8.3125
PHY-3002 : Step(110): len = 52426.8, overlap = 9.625
PHY-3002 : Step(111): len = 51897.9, overlap = 9.625
PHY-3002 : Step(112): len = 51596.9, overlap = 9.1875
PHY-3002 : Step(113): len = 51604.3, overlap = 8.875
PHY-3002 : Step(114): len = 51183.2, overlap = 7.9375
PHY-3002 : Step(115): len = 50659.5, overlap = 8.40625
PHY-3002 : Step(116): len = 50695.5, overlap = 8.21875
PHY-3002 : Step(117): len = 50249, overlap = 8.78125
PHY-3002 : Step(118): len = 49675.4, overlap = 8.625
PHY-3002 : Step(119): len = 49631.9, overlap = 8.75
PHY-3002 : Step(120): len = 49313.5, overlap = 8.75
PHY-3002 : Step(121): len = 49073.9, overlap = 8.6875
PHY-3002 : Step(122): len = 49099.5, overlap = 8.4375
PHY-3002 : Step(123): len = 48659.3, overlap = 7.75
PHY-3002 : Step(124): len = 47736.9, overlap = 11.125
PHY-3002 : Step(125): len = 47462.8, overlap = 12.25
PHY-3002 : Step(126): len = 47061.9, overlap = 15.5312
PHY-3002 : Step(127): len = 46690.7, overlap = 15.7188
PHY-3002 : Step(128): len = 46688.5, overlap = 15.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0009571
PHY-3002 : Step(129): len = 46644.5, overlap = 15.5
PHY-3002 : Step(130): len = 46644.5, overlap = 15.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0019142
PHY-3002 : Step(131): len = 46542.1, overlap = 15.5
PHY-3002 : Step(132): len = 46472.9, overlap = 16
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.074294s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.96647e-05
PHY-3002 : Step(133): len = 47301.2, overlap = 55.625
PHY-3002 : Step(134): len = 47657.7, overlap = 56.875
PHY-3002 : Step(135): len = 48820.8, overlap = 50.4688
PHY-3002 : Step(136): len = 49679, overlap = 45.9062
PHY-3002 : Step(137): len = 49903.3, overlap = 44.6562
PHY-3002 : Step(138): len = 49550.9, overlap = 42.8125
PHY-3002 : Step(139): len = 49333.8, overlap = 42.25
PHY-3002 : Step(140): len = 49316.4, overlap = 42.3125
PHY-3002 : Step(141): len = 49142.1, overlap = 42.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000199329
PHY-3002 : Step(142): len = 49013.2, overlap = 41.9688
PHY-3002 : Step(143): len = 48992.5, overlap = 39.9062
PHY-3002 : Step(144): len = 49107.9, overlap = 39.8438
PHY-3002 : Step(145): len = 48995.1, overlap = 40.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000398659
PHY-3002 : Step(146): len = 49537.4, overlap = 38.1875
PHY-3002 : Step(147): len = 50415.8, overlap = 31.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7613, tnet num: 2133, tinst num: 1607, tnode num: 10809, tedge num: 12815.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.12 peak overflow 3.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2135.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53080, over cnt = 209(0%), over = 925, worst = 21
PHY-1001 : End global iterations;  0.063592s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (147.4%)

PHY-1001 : Congestion index: top1 = 42.46, top5 = 24.17, top10 = 15.38, top15 = 10.86.
PHY-1001 : End incremental global routing;  0.114693s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (122.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069361s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.214924s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (109.1%)

OPT-1001 : Current memory(MB): used = 211, reserve = 177, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1619/2135.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53080, over cnt = 209(0%), over = 925, worst = 21
PHY-1002 : len = 59648, over cnt = 146(0%), over = 332, worst = 16
PHY-1002 : len = 63632, over cnt = 34(0%), over = 43, worst = 4
PHY-1002 : len = 64120, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 64328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.080350s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (136.1%)

PHY-1001 : Congestion index: top1 = 37.03, top5 = 24.38, top10 = 17.30, top15 = 12.57.
OPT-1001 : End congestion update;  0.126607s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (123.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060438s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.189535s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (115.4%)

OPT-1001 : Current memory(MB): used = 214, reserve = 180, peak = 214.
OPT-1001 : End physical optimization;  0.699923s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (125.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 810 remaining SEQ's ...
SYN-4005 : Packed 129 SEQ with LUT/SLICE
SYN-4006 : 79 single LUT's are left
SYN-4006 : 681 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1049/1356 primitive instances ...
PHY-3001 : End packing;  0.052367s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 804 instances
RUN-1001 : 378 mslices, 377 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1967 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1447 nets have 2 pins
RUN-1001 : 403 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 802 instances, 755 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50765.8, Over = 55.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6375, tnet num: 1965, tinst num: 802, tnode num: 8667, tedge num: 11162.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.326168s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.87163e-05
PHY-3002 : Step(148): len = 50257.5, overlap = 55.75
PHY-3002 : Step(149): len = 49700.9, overlap = 56.25
PHY-3002 : Step(150): len = 49652.9, overlap = 58.5
PHY-3002 : Step(151): len = 49793.5, overlap = 58.25
PHY-3002 : Step(152): len = 49538.1, overlap = 58.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.74326e-05
PHY-3002 : Step(153): len = 49892.7, overlap = 57.5
PHY-3002 : Step(154): len = 50052.9, overlap = 58
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.94021e-05
PHY-3002 : Step(155): len = 50351.4, overlap = 55.75
PHY-3002 : Step(156): len = 51509, overlap = 52.25
PHY-3002 : Step(157): len = 52446.4, overlap = 50.5
PHY-3002 : Step(158): len = 52830, overlap = 47.75
PHY-3002 : Step(159): len = 52873.6, overlap = 47
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.080283s wall, 0.078125s user + 0.140625s system = 0.218750s CPU (272.5%)

PHY-3001 : Trial Legalized: Len = 66157.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055364s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000610703
PHY-3002 : Step(160): len = 63512.1, overlap = 5.75
PHY-3002 : Step(161): len = 61830.2, overlap = 8.25
PHY-3002 : Step(162): len = 60172.3, overlap = 13.75
PHY-3002 : Step(163): len = 58654, overlap = 17.25
PHY-3002 : Step(164): len = 57917.2, overlap = 21.25
PHY-3002 : Step(165): len = 57549.5, overlap = 22.5
PHY-3002 : Step(166): len = 57228.3, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00122141
PHY-3002 : Step(167): len = 57537.9, overlap = 22.25
PHY-3002 : Step(168): len = 57761.8, overlap = 20.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00244281
PHY-3002 : Step(169): len = 57855.1, overlap = 20.5
PHY-3002 : Step(170): len = 57954.6, overlap = 20.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005221s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (299.3%)

PHY-3001 : Legalized: Len = 61918.6, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005823s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 12 instances has been re-located, deltaX = 3, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 61992.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6375, tnet num: 1965, tinst num: 802, tnode num: 8667, tedge num: 11162.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 49/1967.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68376, over cnt = 140(0%), over = 212, worst = 8
PHY-1002 : len = 69344, over cnt = 76(0%), over = 92, worst = 4
PHY-1002 : len = 70064, over cnt = 35(0%), over = 41, worst = 2
PHY-1002 : len = 70640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130003s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (156.2%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 22.84, top10 = 17.71, top15 = 13.73.
PHY-1001 : End incremental global routing;  0.186227s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (134.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069112s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.287122s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (125.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 184, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1756/1967.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006331s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 22.84, top10 = 17.71, top15 = 13.73.
OPT-1001 : End congestion update;  0.061364s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (76.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052948s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 764 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 802 instances, 755 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62088.8, Over = 0
PHY-3001 : End spreading;  0.005829s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (268.1%)

PHY-3001 : Final: Len = 62088.8, Over = 0
PHY-3001 : End incremental legalization;  0.042613s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (110.0%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.171540s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (91.1%)

OPT-1001 : Current memory(MB): used = 222, reserve = 188, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053231s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1749/1967.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70704, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010290s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (151.8%)

PHY-1001 : Congestion index: top1 = 31.77, top5 = 22.86, top10 = 17.69, top15 = 13.74.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051659s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.934932s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (105.3%)

RUN-1003 : finish command "place" in  6.027325s wall, 9.671875s user + 3.750000s system = 13.421875s CPU (222.7%)

RUN-1004 : used memory is 202 MB, reserved memory is 167 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 804 instances
RUN-1001 : 378 mslices, 377 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1967 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1447 nets have 2 pins
RUN-1001 : 403 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6375, tnet num: 1965, tinst num: 802, tnode num: 8667, tedge num: 11162.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 378 mslices, 377 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68064, over cnt = 137(0%), over = 208, worst = 8
PHY-1002 : len = 68936, over cnt = 83(0%), over = 100, worst = 4
PHY-1002 : len = 69920, over cnt = 24(0%), over = 25, worst = 2
PHY-1002 : len = 70312, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117660s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (119.5%)

PHY-1001 : Congestion index: top1 = 31.68, top5 = 22.73, top10 = 17.57, top15 = 13.65.
PHY-1001 : End global routing;  0.170855s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (109.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 205, peak = 238.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 467, peak = 497.
PHY-1001 : End build detailed router design. 3.351043s wall, 3.312500s user + 0.046875s system = 3.359375s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33072, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.428338s wall, 1.421875s user + 0.015625s system = 1.437500s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 531.
PHY-1001 : End phase 1; 1.434755s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (101.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 178928, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 501, peak = 531.
PHY-1001 : End initial routed; 1.162917s wall, 2.281250s user + 0.140625s system = 2.421875s CPU (208.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1754(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.652   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.086   |  -0.251   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.388925s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 532, reserve = 502, peak = 532.
PHY-1001 : End phase 2; 1.551928s wall, 2.671875s user + 0.140625s system = 2.812500s CPU (181.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178928, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015391s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178856, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028641s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (54.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178896, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.025744s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (60.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 178952, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021050s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1754(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.652   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.086   |  -0.251   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.381900s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (102.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.174221s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 546, reserve = 516, peak = 546.
PHY-1001 : End phase 3; 0.775864s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.7%)

PHY-1003 : Routed, final wirelength = 178952
PHY-1001 : Current memory(MB): used = 546, reserve = 516, peak = 546.
PHY-1001 : End export database. 0.010993s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (142.1%)

PHY-1001 : End detail routing;  7.304222s wall, 8.375000s user + 0.203125s system = 8.578125s CPU (117.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6375, tnet num: 1965, tinst num: 802, tnode num: 8667, tedge num: 11162.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6413, tnet num: 1984, tinst num: 821, tnode num: 8705, tedge num: 11200.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.224448s wall, 3.359375s user + 0.203125s system = 3.562500s CPU (110.5%)

RUN-1003 : finish command "route" in  11.069943s wall, 12.296875s user + 0.406250s system = 12.703125s CPU (114.8%)

RUN-1004 : used memory is 542 MB, reserved memory is 515 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      810   out of  19600    4.13%
#reg                     1047   out of  19600    5.34%
#le                      1491
  #lut only               444   out of   1491   29.78%
  #reg only               681   out of   1491   45.67%
  #lut&reg                366   out of   1491   24.55%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         454
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1491   |610     |200     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1118   |336     |121     |900     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |17      |0       |0       |
|    demodu                  |Demodulation                                     |560    |158     |53      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |14      |0       |26      |0       |0       |
|    integ                   |Integration                                      |139    |27      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |65     |24      |14      |61      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |96      |29      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |90     |78      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |27      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |18     |15      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |36     |36      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1430  
    #2          2       282   
    #3          3       106   
    #4          4        15   
    #5        5-10       81   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6413, tnet num: 1984, tinst num: 821, tnode num: 8705, tedge num: 11200.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1984 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 821
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1986, pip num: 14536
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1265 valid insts, and 38435 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.348229s wall, 18.671875s user + 0.031250s system = 18.703125s CPU (558.6%)

RUN-1004 : used memory is 550 MB, reserved memory is 519 MB, peak memory is 686 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230909_160011.log"
