============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 18:47:35 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1646 instances
RUN-0007 : 373 luts, 1013 seqs, 138 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2181 nets
RUN-1001 : 1631 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     205     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1644 instances, 373 luts, 1013 seqs, 211 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7775, tnet num: 2179, tinst num: 1644, tnode num: 11020, tedge num: 13128.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.284941s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 559468
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1644.
PHY-3001 : End clustering;  0.000025s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 530793, overlap = 20.25
PHY-3002 : Step(2): len = 434663, overlap = 18
PHY-3002 : Step(3): len = 359003, overlap = 11.25
PHY-3002 : Step(4): len = 326960, overlap = 15.75
PHY-3002 : Step(5): len = 314455, overlap = 20.25
PHY-3002 : Step(6): len = 306452, overlap = 20.25
PHY-3002 : Step(7): len = 300623, overlap = 20.25
PHY-3002 : Step(8): len = 292334, overlap = 20.25
PHY-3002 : Step(9): len = 277348, overlap = 20.25
PHY-3002 : Step(10): len = 272622, overlap = 20.25
PHY-3002 : Step(11): len = 266923, overlap = 20.25
PHY-3002 : Step(12): len = 258590, overlap = 20.25
PHY-3002 : Step(13): len = 254219, overlap = 20.25
PHY-3002 : Step(14): len = 248437, overlap = 20.25
PHY-3002 : Step(15): len = 243534, overlap = 20.25
PHY-3002 : Step(16): len = 238355, overlap = 20.25
PHY-3002 : Step(17): len = 232898, overlap = 20.25
PHY-3002 : Step(18): len = 228867, overlap = 20.25
PHY-3002 : Step(19): len = 223706, overlap = 20.25
PHY-3002 : Step(20): len = 219203, overlap = 20.25
PHY-3002 : Step(21): len = 215143, overlap = 20.25
PHY-3002 : Step(22): len = 210626, overlap = 20.25
PHY-3002 : Step(23): len = 203706, overlap = 20.25
PHY-3002 : Step(24): len = 200100, overlap = 20.25
PHY-3002 : Step(25): len = 196910, overlap = 20.25
PHY-3002 : Step(26): len = 188061, overlap = 20.25
PHY-3002 : Step(27): len = 182636, overlap = 20.25
PHY-3002 : Step(28): len = 180751, overlap = 20.25
PHY-3002 : Step(29): len = 169995, overlap = 20.25
PHY-3002 : Step(30): len = 161016, overlap = 20.25
PHY-3002 : Step(31): len = 159236, overlap = 20.25
PHY-3002 : Step(32): len = 154486, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000126833
PHY-3002 : Step(33): len = 156171, overlap = 15.75
PHY-3002 : Step(34): len = 154558, overlap = 11.25
PHY-3002 : Step(35): len = 151629, overlap = 18
PHY-3002 : Step(36): len = 145237, overlap = 6.75
PHY-3002 : Step(37): len = 137746, overlap = 6.75
PHY-3002 : Step(38): len = 136617, overlap = 9
PHY-3002 : Step(39): len = 133263, overlap = 9
PHY-3002 : Step(40): len = 132486, overlap = 6.75
PHY-3002 : Step(41): len = 129469, overlap = 6.75
PHY-3002 : Step(42): len = 127096, overlap = 11.25
PHY-3002 : Step(43): len = 122737, overlap = 15.75
PHY-3002 : Step(44): len = 119476, overlap = 9
PHY-3002 : Step(45): len = 117480, overlap = 9
PHY-3002 : Step(46): len = 116532, overlap = 11.25
PHY-3002 : Step(47): len = 112480, overlap = 9
PHY-3002 : Step(48): len = 111075, overlap = 9
PHY-3002 : Step(49): len = 108780, overlap = 9
PHY-3002 : Step(50): len = 107165, overlap = 9
PHY-3002 : Step(51): len = 104716, overlap = 9
PHY-3002 : Step(52): len = 103255, overlap = 11.25
PHY-3002 : Step(53): len = 95361.2, overlap = 9
PHY-3002 : Step(54): len = 93303.1, overlap = 9
PHY-3002 : Step(55): len = 91645.9, overlap = 6.75
PHY-3002 : Step(56): len = 90899, overlap = 11.25
PHY-3002 : Step(57): len = 89108.8, overlap = 9
PHY-3002 : Step(58): len = 84143, overlap = 4.5
PHY-3002 : Step(59): len = 80915.7, overlap = 6.75
PHY-3002 : Step(60): len = 79033.9, overlap = 4.5
PHY-3002 : Step(61): len = 77948.9, overlap = 9
PHY-3002 : Step(62): len = 76815.6, overlap = 11.25
PHY-3002 : Step(63): len = 76346.9, overlap = 9
PHY-3002 : Step(64): len = 76565.2, overlap = 6.75
PHY-3002 : Step(65): len = 76268.9, overlap = 6.75
PHY-3002 : Step(66): len = 75130.9, overlap = 9
PHY-3002 : Step(67): len = 74344, overlap = 11.25
PHY-3002 : Step(68): len = 73833.7, overlap = 11.25
PHY-3002 : Step(69): len = 72789.4, overlap = 9
PHY-3002 : Step(70): len = 71212, overlap = 9
PHY-3002 : Step(71): len = 70318.3, overlap = 9
PHY-3002 : Step(72): len = 69546.9, overlap = 11.25
PHY-3002 : Step(73): len = 69383.8, overlap = 11.25
PHY-3002 : Step(74): len = 69335.2, overlap = 9
PHY-3002 : Step(75): len = 68607.8, overlap = 11.25
PHY-3002 : Step(76): len = 68036.2, overlap = 11.25
PHY-3002 : Step(77): len = 66892.3, overlap = 11.25
PHY-3002 : Step(78): len = 66801.1, overlap = 11.25
PHY-3002 : Step(79): len = 66351.5, overlap = 9
PHY-3002 : Step(80): len = 64823.7, overlap = 11.25
PHY-3002 : Step(81): len = 62987.1, overlap = 11.25
PHY-3002 : Step(82): len = 61977.9, overlap = 11.25
PHY-3002 : Step(83): len = 61553.3, overlap = 11.25
PHY-3002 : Step(84): len = 60950.4, overlap = 11.25
PHY-3002 : Step(85): len = 60217.6, overlap = 9
PHY-3002 : Step(86): len = 59784.9, overlap = 9
PHY-3002 : Step(87): len = 59675, overlap = 9
PHY-3002 : Step(88): len = 59290.4, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000253666
PHY-3002 : Step(89): len = 59295.2, overlap = 9
PHY-3002 : Step(90): len = 59353.7, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000507332
PHY-3002 : Step(91): len = 59332.2, overlap = 9
PHY-3002 : Step(92): len = 59322.1, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007883s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (198.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063682s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 63138.8, overlap = 6.84375
PHY-3002 : Step(94): len = 62259.8, overlap = 6.59375
PHY-3002 : Step(95): len = 61960.5, overlap = 5.78125
PHY-3002 : Step(96): len = 61221.7, overlap = 5.625
PHY-3002 : Step(97): len = 60623, overlap = 6.0625
PHY-3002 : Step(98): len = 59344.3, overlap = 5.9375
PHY-3002 : Step(99): len = 58527.4, overlap = 5
PHY-3002 : Step(100): len = 57095.6, overlap = 6.125
PHY-3002 : Step(101): len = 56316.3, overlap = 6.1875
PHY-3002 : Step(102): len = 53955, overlap = 5.3125
PHY-3002 : Step(103): len = 53367.9, overlap = 5.4375
PHY-3002 : Step(104): len = 52833.1, overlap = 5.3125
PHY-3002 : Step(105): len = 52491, overlap = 5.375
PHY-3002 : Step(106): len = 51910.1, overlap = 5.375
PHY-3002 : Step(107): len = 51452.8, overlap = 5.625
PHY-3002 : Step(108): len = 51065.6, overlap = 5.5625
PHY-3002 : Step(109): len = 50391.1, overlap = 5.4375
PHY-3002 : Step(110): len = 49616.7, overlap = 4.375
PHY-3002 : Step(111): len = 49529.6, overlap = 4.375
PHY-3002 : Step(112): len = 49216.4, overlap = 4.3125
PHY-3002 : Step(113): len = 49149.7, overlap = 4
PHY-3002 : Step(114): len = 48893, overlap = 4.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000890203
PHY-3002 : Step(115): len = 48722, overlap = 4.0625
PHY-3002 : Step(116): len = 48742.4, overlap = 4.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00178041
PHY-3002 : Step(117): len = 48752.4, overlap = 4.5625
PHY-3002 : Step(118): len = 48752.4, overlap = 4.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066894s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.26739e-05
PHY-3002 : Step(119): len = 48962.4, overlap = 54.9062
PHY-3002 : Step(120): len = 49080.7, overlap = 54.7188
PHY-3002 : Step(121): len = 49077.4, overlap = 53.8438
PHY-3002 : Step(122): len = 49273.5, overlap = 51.2188
PHY-3002 : Step(123): len = 49642.4, overlap = 49.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000165348
PHY-3002 : Step(124): len = 49554.7, overlap = 44.9688
PHY-3002 : Step(125): len = 49674.7, overlap = 44.4375
PHY-3002 : Step(126): len = 49752, overlap = 44.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000330696
PHY-3002 : Step(127): len = 50521.7, overlap = 43.7812
PHY-3002 : Step(128): len = 50718.7, overlap = 43.625
PHY-3002 : Step(129): len = 51221.1, overlap = 39.625
PHY-3002 : Step(130): len = 51642.3, overlap = 39.2812
PHY-3002 : Step(131): len = 52206.8, overlap = 38.5625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7775, tnet num: 2179, tinst num: 1644, tnode num: 11020, tedge num: 13128.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 101.78 peak overflow 3.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2181.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55208, over cnt = 264(0%), over = 1128, worst = 19
PHY-1001 : End global iterations;  0.077322s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (181.9%)

PHY-1001 : Congestion index: top1 = 44.03, top5 = 26.61, top10 = 16.76, top15 = 11.81.
PHY-1001 : End incremental global routing;  0.127904s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (158.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070347s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.229190s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (136.3%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1686/2181.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55208, over cnt = 264(0%), over = 1128, worst = 19
PHY-1002 : len = 63968, over cnt = 159(0%), over = 384, worst = 14
PHY-1002 : len = 67888, over cnt = 42(0%), over = 60, worst = 5
PHY-1002 : len = 68632, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 69000, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.097263s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (112.5%)

PHY-1001 : Congestion index: top1 = 39.61, top5 = 26.58, top10 = 18.98, top15 = 13.84.
OPT-1001 : End congestion update;  0.140175s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070395s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.213313s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (95.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : End physical optimization;  0.718334s wall, 0.750000s user + 0.046875s system = 0.796875s CPU (110.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 186 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 93 SEQ with LUT/SLICE
SYN-4006 : 117 single LUT's are left
SYN-4006 : 734 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1107/1425 primitive instances ...
PHY-3001 : End packing;  0.055171s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2009 nets
RUN-1001 : 1463 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 839 instances, 792 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52314.2, Over = 65.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2007, tinst num: 839, tnode num: 8868, tedge num: 11480.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.326905s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (95.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.88433e-05
PHY-3002 : Step(132): len = 51848.2, overlap = 66.25
PHY-3002 : Step(133): len = 51644, overlap = 66.5
PHY-3002 : Step(134): len = 50842.2, overlap = 68.75
PHY-3002 : Step(135): len = 50631.2, overlap = 69.25
PHY-3002 : Step(136): len = 50622.9, overlap = 67.5
PHY-3002 : Step(137): len = 50652, overlap = 68.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.76866e-05
PHY-3002 : Step(138): len = 50757.8, overlap = 66
PHY-3002 : Step(139): len = 51114.8, overlap = 63
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000111961
PHY-3002 : Step(140): len = 51738.6, overlap = 59.5
PHY-3002 : Step(141): len = 53281, overlap = 54
PHY-3002 : Step(142): len = 53838.2, overlap = 54.75
PHY-3002 : Step(143): len = 54405.7, overlap = 52.5
PHY-3002 : Step(144): len = 54764.7, overlap = 51.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.089171s wall, 0.078125s user + 0.125000s system = 0.203125s CPU (227.8%)

PHY-3001 : Trial Legalized: Len = 69096.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059209s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000676082
PHY-3002 : Step(145): len = 66279.8, overlap = 7
PHY-3002 : Step(146): len = 64186.9, overlap = 12.5
PHY-3002 : Step(147): len = 62220.7, overlap = 15.25
PHY-3002 : Step(148): len = 60834.4, overlap = 20.75
PHY-3002 : Step(149): len = 59720.3, overlap = 24.5
PHY-3002 : Step(150): len = 59103, overlap = 27.75
PHY-3002 : Step(151): len = 58679.3, overlap = 30
PHY-3002 : Step(152): len = 58290.4, overlap = 30.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00135216
PHY-3002 : Step(153): len = 58615.2, overlap = 29.5
PHY-3002 : Step(154): len = 58819.7, overlap = 28.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00270433
PHY-3002 : Step(155): len = 58913.2, overlap = 28
PHY-3002 : Step(156): len = 59027.1, overlap = 28.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006472s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (241.4%)

PHY-3001 : Legalized: Len = 63370.5, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005882s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 0, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 63476.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2007, tinst num: 839, tnode num: 8868, tedge num: 11480.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 44/2009.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69512, over cnt = 154(0%), over = 235, worst = 7
PHY-1002 : len = 70496, over cnt = 88(0%), over = 113, worst = 4
PHY-1002 : len = 71880, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 71992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129796s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (132.4%)

PHY-1001 : Congestion index: top1 = 31.36, top5 = 22.85, top10 = 17.89, top15 = 14.17.
PHY-1001 : End incremental global routing;  0.179841s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (121.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060591s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.273313s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (114.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1795/2009.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006447s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.36, top5 = 22.85, top10 = 17.89, top15 = 14.17.
OPT-1001 : End congestion update;  0.052930s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054856s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 801 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 839 instances, 792 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63533.2, Over = 0
PHY-3001 : End spreading;  0.005080s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63533.2, Over = 0
PHY-3001 : End incremental legalization;  0.037492s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.4%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.160109s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (165.9%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047595s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1787/2009.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008540s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.87, top10 = 17.90, top15 = 14.18.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056636s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.898592s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (114.8%)

RUN-1003 : finish command "place" in  5.412363s wall, 8.015625s user + 3.171875s system = 11.187500s CPU (206.7%)

RUN-1004 : used memory is 202 MB, reserved memory is 166 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2009 nets
RUN-1001 : 1463 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2007, tinst num: 839, tnode num: 8868, tedge num: 11480.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69120, over cnt = 155(0%), over = 233, worst = 7
PHY-1002 : len = 70120, over cnt = 93(0%), over = 116, worst = 4
PHY-1002 : len = 71544, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132282s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (94.5%)

PHY-1001 : Congestion index: top1 = 31.49, top5 = 22.85, top10 = 17.88, top15 = 14.11.
PHY-1001 : End global routing;  0.180934s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 499, reserve = 467, peak = 499.
PHY-1001 : End build detailed router design. 5.884349s wall, 5.781250s user + 0.109375s system = 5.890625s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34288, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.647422s wall, 2.609375s user + 0.031250s system = 2.640625s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End phase 1; 2.653856s wall, 2.625000s user + 0.031250s system = 2.656250s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183416, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 503, peak = 532.
PHY-1001 : End initial routed; 1.470877s wall, 2.953125s user + 0.265625s system = 3.218750s CPU (218.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1787(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.323   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.370634s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (97.0%)

PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End phase 2; 1.841612s wall, 3.312500s user + 0.265625s system = 3.578125s CPU (194.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183416, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014558s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 183496, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024497s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (127.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 183536, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.020437s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (76.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 183536, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.018014s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (86.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1787(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.323   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.375726s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.174799s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (98.3%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.753471s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (101.6%)

PHY-1003 : Routed, final wirelength = 183536
PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End export database. 0.010266s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  11.326984s wall, 12.671875s user + 0.406250s system = 13.078125s CPU (115.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2007, tinst num: 839, tnode num: 8868, tedge num: 11480.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  12.243519s wall, 13.578125s user + 0.406250s system = 13.984375s CPU (114.2%)

RUN-1004 : used memory is 501 MB, reserved memory is 474 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      802   out of  19600    4.09%
#reg                     1069   out of  19600    5.45%
#le                      1536
  #lut only               467   out of   1536   30.40%
  #reg only               734   out of   1536   47.79%
  #lut&reg                335   out of   1536   21.81%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       471
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       109
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_10.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1536   |591     |211     |1100    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1147   |292     |126     |914     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |41     |34      |7       |23      |0       |0       |
|    demodu                  |Demodulation                                     |540    |117     |58      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |164    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |52     |2       |0       |52      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |11      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |15      |0       |30      |0       |0       |
|    integ                   |Integration                                      |136    |15      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |101    |28      |15      |87      |0       |1       |
|    rs422                   |Rs422Output                                      |310    |82      |29      |249     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |16      |3       |15      |0       |0       |
|  u_uart                    |UART_Control                                     |108    |96      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |17     |14      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |54      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |79      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1427  
    #2          2       304   
    #3          3       107   
    #4          4        17   
    #5        5-10       79   
    #6        11-50      31   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2007, tinst num: 839, tnode num: 8868, tedge num: 11480.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 839
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2009, pip num: 14806
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1286 valid insts, and 39010 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.074643s wall, 17.328125s user + 0.093750s system = 17.421875s CPU (566.6%)

RUN-1004 : used memory is 520 MB, reserved memory is 489 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_184735.log"
