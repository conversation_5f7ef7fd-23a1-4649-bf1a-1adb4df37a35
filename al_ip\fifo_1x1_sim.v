// Verilog netlist created by Tang Dynasty v5.6.71036
// Mon Jul  3 22:28:18 2023

`timescale 1ns / 1ps
module fifo_1x1  // fifo_1x1.v(14)
  (
  clkr,
  clkw,
  di,
  re,
  rst,
  we,
  do,
  empty_flag,
  full_flag
  );

  input clkr;  // fifo_1x1.v(25)
  input clkw;  // fifo_1x1.v(24)
  input [0:0] di;  // fifo_1x1.v(23)
  input re;  // fifo_1x1.v(25)
  input rst;  // fifo_1x1.v(22)
  input we;  // fifo_1x1.v(24)
  output [1:0] do;  // fifo_1x1.v(27)
  output empty_flag;  // fifo_1x1.v(28)
  output full_flag;  // fifo_1x1.v(29)

  wire empty_flag_syn_2;  // fifo_1x1.v(28)
  wire full_flag_syn_2;  // fifo_1x1.v(29)

  EG_PHY_CONFIG #(
    .DONE_PERSISTN("ENABLE"),
    .INIT_PERSISTN("ENABLE"),
    .JTAG_PERSISTN("DISABLE"),
    .PROGRAMN_PERSISTN("DISABLE"))
    config_inst ();
  not empty_flag_syn_1 (empty_flag_syn_2, empty_flag);  // fifo_1x1.v(28)
  EG_PHY_FIFO #(
    .AE(32'b00000000000000000000000001101000),
    .AEP1(32'b00000000000000000000000001111000),
    .AF(32'b00000000000000000001111111010000),
    .AFM1(32'b00000000000000000001111111001000),
    .ASYNC_RESET_RELEASE("SYNC"),
    .DATA_WIDTH_A("9"),
    .DATA_WIDTH_B("18"),
    .E(32'b00000000000000000000000000001000),
    .EP1(32'b00000000000000000000000000011000),
    .F(32'b00000000000000000010000000000000),
    .FM1(32'b00000000000000000001111111111000),
    .GSR("DISABLE"),
    .MODE("FIFO8K"),
    .REGMODE_A("NOREG"),
    .REGMODE_B("NOREG"),
    .RESETMODE("ASYNC"))
    fifo_inst_syn_2 (
    .clkr(clkr),
    .clkw(clkw),
    .csr({2'b11,empty_flag_syn_2}),
    .csw({2'b11,full_flag_syn_2}),
    .dia({open_n47,open_n48,open_n49,open_n50,open_n51,open_n52,open_n53,open_n54,di}),
    .orea(1'b0),
    .oreb(1'b0),
    .re(re),
    .rprst(rst),
    .rst(rst),
    .we(we),
    .doa({open_n66,open_n67,open_n68,open_n69,open_n70,open_n71,open_n72,open_n73,do[1]}),
    .dob({open_n74,open_n75,open_n76,open_n77,open_n78,open_n79,open_n80,open_n81,do[0]}),
    .empty_flag(empty_flag),
    .full_flag(full_flag));  // fifo_1x1.v(41)
  not full_flag_syn_1 (full_flag_syn_2, full_flag);  // fifo_1x1.v(29)

endmodule 

