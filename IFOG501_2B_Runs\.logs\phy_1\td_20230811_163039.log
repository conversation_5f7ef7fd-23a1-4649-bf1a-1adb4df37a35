============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Aug 11 16:30:39 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1623 instances
RUN-0007 : 368 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2193 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1634 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1621 instances, 368 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2191, tinst num: 1621, tnode num: 11021, tedge num: 13165.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.290156s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 560930
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1621.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 530613, overlap = 20.25
PHY-3002 : Step(2): len = 412880, overlap = 13.5
PHY-3002 : Step(3): len = 351221, overlap = 9
PHY-3002 : Step(4): len = 326598, overlap = 15.75
PHY-3002 : Step(5): len = 313986, overlap = 15.75
PHY-3002 : Step(6): len = 307993, overlap = 20.25
PHY-3002 : Step(7): len = 300300, overlap = 20.25
PHY-3002 : Step(8): len = 293849, overlap = 20.25
PHY-3002 : Step(9): len = 288495, overlap = 20.25
PHY-3002 : Step(10): len = 281665, overlap = 20.25
PHY-3002 : Step(11): len = 275508, overlap = 20.25
PHY-3002 : Step(12): len = 270902, overlap = 20.25
PHY-3002 : Step(13): len = 264386, overlap = 20.25
PHY-3002 : Step(14): len = 257321, overlap = 20.25
PHY-3002 : Step(15): len = 253338, overlap = 20.25
PHY-3002 : Step(16): len = 247636, overlap = 20.25
PHY-3002 : Step(17): len = 240770, overlap = 20.25
PHY-3002 : Step(18): len = 237037, overlap = 20.25
PHY-3002 : Step(19): len = 230736, overlap = 20.25
PHY-3002 : Step(20): len = 223522, overlap = 20.25
PHY-3002 : Step(21): len = 219438, overlap = 20.25
PHY-3002 : Step(22): len = 215606, overlap = 20.25
PHY-3002 : Step(23): len = 205272, overlap = 20.25
PHY-3002 : Step(24): len = 200673, overlap = 20.25
PHY-3002 : Step(25): len = 198359, overlap = 20.25
PHY-3002 : Step(26): len = 172886, overlap = 20.25
PHY-3002 : Step(27): len = 161350, overlap = 20.25
PHY-3002 : Step(28): len = 160079, overlap = 20.25
PHY-3002 : Step(29): len = 144228, overlap = 20.25
PHY-3002 : Step(30): len = 118213, overlap = 20.25
PHY-3002 : Step(31): len = 113670, overlap = 20.25
PHY-3002 : Step(32): len = 110876, overlap = 20.25
PHY-3002 : Step(33): len = 108011, overlap = 20.25
PHY-3002 : Step(34): len = 105656, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.69432e-05
PHY-3002 : Step(35): len = 107381, overlap = 18
PHY-3002 : Step(36): len = 106552, overlap = 13.5
PHY-3002 : Step(37): len = 105160, overlap = 13.5
PHY-3002 : Step(38): len = 102956, overlap = 15.75
PHY-3002 : Step(39): len = 101398, overlap = 15.75
PHY-3002 : Step(40): len = 100624, overlap = 13.5
PHY-3002 : Step(41): len = 98285.1, overlap = 13.5
PHY-3002 : Step(42): len = 95930.8, overlap = 15.75
PHY-3002 : Step(43): len = 93576.1, overlap = 11.25
PHY-3002 : Step(44): len = 92586.2, overlap = 11.25
PHY-3002 : Step(45): len = 91341.5, overlap = 11.25
PHY-3002 : Step(46): len = 89463.2, overlap = 11.25
PHY-3002 : Step(47): len = 87787, overlap = 11.25
PHY-3002 : Step(48): len = 85801.8, overlap = 11.25
PHY-3002 : Step(49): len = 85511.2, overlap = 11.25
PHY-3002 : Step(50): len = 82875.5, overlap = 13.5
PHY-3002 : Step(51): len = 71402.9, overlap = 10.9375
PHY-3002 : Step(52): len = 69802.2, overlap = 10.9375
PHY-3002 : Step(53): len = 67560.8, overlap = 13.125
PHY-3002 : Step(54): len = 66773, overlap = 13.125
PHY-3002 : Step(55): len = 67030.5, overlap = 10.8125
PHY-3002 : Step(56): len = 66838.3, overlap = 8.625
PHY-3002 : Step(57): len = 65561, overlap = 6.375
PHY-3002 : Step(58): len = 64162.8, overlap = 15.375
PHY-3002 : Step(59): len = 63171.4, overlap = 13
PHY-3002 : Step(60): len = 61475.3, overlap = 13.0625
PHY-3002 : Step(61): len = 60748.9, overlap = 12.75
PHY-3002 : Step(62): len = 60256.3, overlap = 12.6875
PHY-3002 : Step(63): len = 59768.2, overlap = 12.5
PHY-3002 : Step(64): len = 58616.7, overlap = 12.5
PHY-3002 : Step(65): len = 58081, overlap = 12.25
PHY-3002 : Step(66): len = 58108.9, overlap = 14.5
PHY-3002 : Step(67): len = 58076.3, overlap = 14.375
PHY-3002 : Step(68): len = 57865.1, overlap = 12.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000173886
PHY-3002 : Step(69): len = 57926.8, overlap = 14.4375
PHY-3002 : Step(70): len = 57943.3, overlap = 14.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000347773
PHY-3002 : Step(71): len = 57862.9, overlap = 12.125
PHY-3002 : Step(72): len = 57906.1, overlap = 12.125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009943s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (314.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.105114s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (104.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000769131
PHY-3002 : Step(73): len = 61729.4, overlap = 7.625
PHY-3002 : Step(74): len = 60556.3, overlap = 7.25
PHY-3002 : Step(75): len = 59725.8, overlap = 7.125
PHY-3002 : Step(76): len = 58942, overlap = 5.5
PHY-3002 : Step(77): len = 58270.6, overlap = 5.875
PHY-3002 : Step(78): len = 56345.4, overlap = 8.0625
PHY-3002 : Step(79): len = 55025.4, overlap = 8.25
PHY-3002 : Step(80): len = 53589.6, overlap = 8
PHY-3002 : Step(81): len = 52964.4, overlap = 7.875
PHY-3002 : Step(82): len = 52042.2, overlap = 7.78125
PHY-3002 : Step(83): len = 51474.2, overlap = 7.8125
PHY-3002 : Step(84): len = 50759.7, overlap = 8.375
PHY-3002 : Step(85): len = 50474.2, overlap = 7.875
PHY-3002 : Step(86): len = 50260.8, overlap = 7.5625
PHY-3002 : Step(87): len = 49488.9, overlap = 7.75
PHY-3002 : Step(88): len = 48914.9, overlap = 7.84375
PHY-3002 : Step(89): len = 48571.4, overlap = 9.21875
PHY-3002 : Step(90): len = 48263.4, overlap = 8.84375
PHY-3002 : Step(91): len = 47881.8, overlap = 8.84375
PHY-3002 : Step(92): len = 47746.9, overlap = 12.5938
PHY-3002 : Step(93): len = 47475.5, overlap = 11.9688
PHY-3002 : Step(94): len = 47112.7, overlap = 10.0312
PHY-3002 : Step(95): len = 46993.6, overlap = 11.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00153826
PHY-3002 : Step(96): len = 46837.6, overlap = 11.2812
PHY-3002 : Step(97): len = 46712.8, overlap = 11.3438
PHY-3002 : Step(98): len = 46681.7, overlap = 17.4062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00307652
PHY-3002 : Step(99): len = 46587.6, overlap = 17.5625
PHY-3002 : Step(100): len = 46587.6, overlap = 17.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.100951s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (92.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.1741e-05
PHY-3002 : Step(101): len = 47216.6, overlap = 54.5312
PHY-3002 : Step(102): len = 47307.4, overlap = 54.1875
PHY-3002 : Step(103): len = 48269.1, overlap = 51.5
PHY-3002 : Step(104): len = 49163.6, overlap = 49.8438
PHY-3002 : Step(105): len = 49223.8, overlap = 49.875
PHY-3002 : Step(106): len = 48899.1, overlap = 50.3438
PHY-3002 : Step(107): len = 48281.9, overlap = 45.5625
PHY-3002 : Step(108): len = 48234.3, overlap = 44.7188
PHY-3002 : Step(109): len = 48166, overlap = 43.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000163482
PHY-3002 : Step(110): len = 48048.6, overlap = 43.2812
PHY-3002 : Step(111): len = 48407.6, overlap = 44.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000326964
PHY-3002 : Step(112): len = 48451.7, overlap = 43.4062
PHY-3002 : Step(113): len = 49438.9, overlap = 41.75
PHY-3002 : Step(114): len = 50263.9, overlap = 40
PHY-3002 : Step(115): len = 50570.4, overlap = 27.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2191, tinst num: 1621, tnode num: 11021, tedge num: 13165.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.72 peak overflow 3.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2193.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53736, over cnt = 235(0%), over = 992, worst = 15
PHY-1001 : End global iterations;  0.105510s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (162.9%)

PHY-1001 : Congestion index: top1 = 41.49, top5 = 24.45, top10 = 15.59, top15 = 11.09.
PHY-1001 : End incremental global routing;  0.184210s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (135.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.100828s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (108.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1582 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1623 instances, 368 luts, 987 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 50743.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7789, tnet num: 2193, tinst num: 1623, tnode num: 11035, tedge num: 13177.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.427510s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(116): len = 50757.5, overlap = 5.21875
PHY-3002 : Step(117): len = 50762, overlap = 5.28125
PHY-3002 : Step(118): len = 50755.2, overlap = 5.34375
PHY-3002 : Step(119): len = 50755.2, overlap = 5.34375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.079246s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000305855
PHY-3002 : Step(120): len = 50778.7, overlap = 27.625
PHY-3002 : Step(121): len = 50778.7, overlap = 27.625
PHY-3001 : Final: Len = 50778.7, Over = 27.625
PHY-3001 : End incremental placement;  0.629201s wall, 0.687500s user + 0.093750s system = 0.781250s CPU (124.2%)

OPT-1001 : Total overflow 84.84 peak overflow 3.59
OPT-1001 : End high-fanout net optimization;  0.967834s wall, 1.062500s user + 0.125000s system = 1.187500s CPU (122.7%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1690/2195.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53904, over cnt = 235(0%), over = 988, worst = 15
PHY-1002 : len = 61016, over cnt = 144(0%), over = 253, worst = 15
PHY-1002 : len = 63808, over cnt = 25(0%), over = 40, worst = 6
PHY-1002 : len = 64384, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 64528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138025s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (124.5%)

PHY-1001 : Congestion index: top1 = 36.19, top5 = 24.26, top10 = 17.21, top15 = 12.74.
OPT-1001 : End congestion update;  0.194032s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (120.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.081022s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.279029s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (112.0%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 220.
OPT-1001 : End physical optimization;  1.650410s wall, 1.781250s user + 0.125000s system = 1.906250s CPU (115.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 103 SEQ with LUT/SLICE
SYN-4006 : 103 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1398 primitive instances ...
PHY-3001 : End packing;  0.085560s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2028 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1475 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 835 instances, 788 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50783, Over = 56
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6567, tnet num: 2026, tinst num: 835, tnode num: 8916, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.421581s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.38595e-05
PHY-3002 : Step(122): len = 50130, overlap = 58
PHY-3002 : Step(123): len = 49780.5, overlap = 59.25
PHY-3002 : Step(124): len = 49501.6, overlap = 59.5
PHY-3002 : Step(125): len = 49509, overlap = 59
PHY-3002 : Step(126): len = 49477.8, overlap = 59.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.7719e-05
PHY-3002 : Step(127): len = 49800.2, overlap = 58.5
PHY-3002 : Step(128): len = 50294.4, overlap = 55.5
PHY-3002 : Step(129): len = 50888.6, overlap = 55.25
PHY-3002 : Step(130): len = 51412.1, overlap = 54.5
PHY-3002 : Step(131): len = 51442.6, overlap = 53.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.5438e-05
PHY-3002 : Step(132): len = 51981, overlap = 52.5
PHY-3002 : Step(133): len = 52210.3, overlap = 52.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.129785s wall, 0.078125s user + 0.156250s system = 0.234375s CPU (180.6%)

PHY-3001 : Trial Legalized: Len = 67547.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072305s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000419468
PHY-3002 : Step(134): len = 64710.8, overlap = 4.75
PHY-3002 : Step(135): len = 62329, overlap = 11.5
PHY-3002 : Step(136): len = 60397.7, overlap = 17.25
PHY-3002 : Step(137): len = 59267.7, overlap = 16.5
PHY-3002 : Step(138): len = 58577.4, overlap = 17
PHY-3002 : Step(139): len = 58145.1, overlap = 19.5
PHY-3002 : Step(140): len = 57917.3, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000838936
PHY-3002 : Step(141): len = 58435.8, overlap = 20.25
PHY-3002 : Step(142): len = 58666.8, overlap = 18.75
PHY-3002 : Step(143): len = 58577.7, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00167787
PHY-3002 : Step(144): len = 58837.7, overlap = 19.5
PHY-3002 : Step(145): len = 59064.7, overlap = 18.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009042s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63376.9, Over = 0
PHY-3001 : Spreading special nets. 10 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.009970s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (156.7%)

PHY-3001 : 15 instances has been re-located, deltaX = 0, deltaY = 14, maxDist = 1.
PHY-3001 : Final: Len = 63542.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6567, tnet num: 2026, tinst num: 835, tnode num: 8916, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 68/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69920, over cnt = 136(0%), over = 213, worst = 8
PHY-1002 : len = 70784, over cnt = 81(0%), over = 95, worst = 3
PHY-1002 : len = 71608, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 71824, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.157502s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (129.0%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.53, top10 = 17.67, top15 = 13.97.
PHY-1001 : End incremental global routing;  0.223180s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (119.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.075314s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.336659s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (111.4%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1785/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007849s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (199.1%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.53, top10 = 17.67, top15 = 13.97.
OPT-1001 : End congestion update;  0.064335s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062414s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 797 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 835 instances, 788 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63568, Over = 0
PHY-3001 : End spreading;  0.006594s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63568, Over = 0
PHY-3001 : End incremental legalization;  0.043588s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (71.7%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.186783s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060173s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1782/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71944, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71944, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.019907s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (78.5%)

PHY-1001 : Congestion index: top1 = 31.25, top5 = 22.49, top10 = 17.68, top15 = 13.97.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061673s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.118692s wall, 1.156250s user + 0.078125s system = 1.234375s CPU (110.3%)

RUN-1003 : finish command "place" in  8.354667s wall, 12.468750s user + 4.375000s system = 16.843750s CPU (201.6%)

RUN-1004 : used memory is 208 MB, reserved memory is 172 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2028 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1475 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6567, tnet num: 2026, tinst num: 835, tnode num: 8916, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69344, over cnt = 138(0%), over = 219, worst = 8
PHY-1002 : len = 70392, over cnt = 80(0%), over = 95, worst = 3
PHY-1002 : len = 71320, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 71424, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71520, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146163s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (171.0%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.36, top10 = 17.56, top15 = 13.87.
PHY-1001 : End global routing;  0.206432s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (159.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 203, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 503, reserve = 470, peak = 503.
PHY-1001 : End build detailed router design. 8.412327s wall, 8.250000s user + 0.156250s system = 8.406250s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33960, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.764997s wall, 2.703125s user + 0.000000s system = 2.703125s CPU (97.8%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 1; 2.771647s wall, 2.703125s user + 0.000000s system = 2.703125s CPU (97.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177888, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 537.
PHY-1001 : End initial routed; 2.141071s wall, 3.656250s user + 0.281250s system = 3.937500s CPU (183.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.465   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.899304s wall, 0.875000s user + 0.031250s system = 0.906250s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 538, reserve = 507, peak = 538.
PHY-1001 : End phase 2; 3.040684s wall, 4.531250s user + 0.312500s system = 4.843750s CPU (159.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177888, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.033389s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177904, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.054801s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177976, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.046370s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.465   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.979299s wall, 0.984375s user + 0.000000s system = 0.984375s CPU (100.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.401033s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (97.4%)

PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End phase 3; 1.740629s wall, 1.718750s user + 0.015625s system = 1.734375s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 177976
PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End export database. 0.018446s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (169.4%)

PHY-1001 : End detail routing;  16.401404s wall, 17.625000s user + 0.484375s system = 18.109375s CPU (110.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6567, tnet num: 2026, tinst num: 835, tnode num: 8916, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  17.924704s wall, 19.234375s user + 0.515625s system = 19.750000s CPU (110.2%)

RUN-1004 : used memory is 527 MB, reserved memory is 499 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      816   out of  19600    4.16%
#reg                     1076   out of  19600    5.49%
#le                      1518
  #lut only               442   out of   1518   29.12%
  #reg only               702   out of   1518   46.25%
  #lut&reg                374   out of   1518   24.64%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       472
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       110
#3        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_11.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1518   |595     |221     |1107    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1122   |299     |128     |927     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |29     |23      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |520    |115     |53      |429     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |88     |26      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |93      |29      |259     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |95     |81      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |37     |35      |0       |19      |0       |0       |
|  wendu                     |DS18B20                                          |218    |173     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1439  
    #2          2       311   
    #3          3       102   
    #4          4        21   
    #5        5-10       80   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6567, tnet num: 2026, tinst num: 835, tnode num: 8916, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 835
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2028, pip num: 14552
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1301 valid insts, and 38628 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.980049s wall, 19.859375s user + 0.187500s system = 20.046875s CPU (503.7%)

RUN-1004 : used memory is 550 MB, reserved memory is 519 MB, peak memory is 674 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230811_163039.log"
