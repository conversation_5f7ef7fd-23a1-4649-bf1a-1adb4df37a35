============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug 17 18:41:52 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1647 instances
RUN-0007 : 380 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2217 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1653 nets have 2 pins
RUN-1001 : 446 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1645 instances, 380 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7843, tnet num: 2215, tinst num: 1645, tnode num: 11081, tedge num: 13241.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.285038s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (98.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540862
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1645.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 494092, overlap = 18
PHY-3002 : Step(2): len = 402515, overlap = 15.75
PHY-3002 : Step(3): len = 333681, overlap = 15.75
PHY-3002 : Step(4): len = 321878, overlap = 15.75
PHY-3002 : Step(5): len = 314189, overlap = 18
PHY-3002 : Step(6): len = 306839, overlap = 20.25
PHY-3002 : Step(7): len = 301874, overlap = 20.25
PHY-3002 : Step(8): len = 292511, overlap = 20.25
PHY-3002 : Step(9): len = 286214, overlap = 20.25
PHY-3002 : Step(10): len = 281731, overlap = 20.25
PHY-3002 : Step(11): len = 274908, overlap = 20.25
PHY-3002 : Step(12): len = 268541, overlap = 20.25
PHY-3002 : Step(13): len = 264051, overlap = 20.25
PHY-3002 : Step(14): len = 258820, overlap = 20.25
PHY-3002 : Step(15): len = 252559, overlap = 20.25
PHY-3002 : Step(16): len = 248015, overlap = 20.25
PHY-3002 : Step(17): len = 243514, overlap = 20.25
PHY-3002 : Step(18): len = 236966, overlap = 20.25
PHY-3002 : Step(19): len = 232873, overlap = 20.25
PHY-3002 : Step(20): len = 227573, overlap = 20.25
PHY-3002 : Step(21): len = 222918, overlap = 20.25
PHY-3002 : Step(22): len = 218752, overlap = 20.25
PHY-3002 : Step(23): len = 213798, overlap = 20.25
PHY-3002 : Step(24): len = 209479, overlap = 20.25
PHY-3002 : Step(25): len = 205659, overlap = 20.25
PHY-3002 : Step(26): len = 199440, overlap = 20.25
PHY-3002 : Step(27): len = 195088, overlap = 20.25
PHY-3002 : Step(28): len = 190909, overlap = 20.25
PHY-3002 : Step(29): len = 186090, overlap = 20.25
PHY-3002 : Step(30): len = 181900, overlap = 20.25
PHY-3002 : Step(31): len = 177651, overlap = 20.25
PHY-3002 : Step(32): len = 172564, overlap = 20.25
PHY-3002 : Step(33): len = 167834, overlap = 20.25
PHY-3002 : Step(34): len = 164112, overlap = 20.25
PHY-3002 : Step(35): len = 160428, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000132697
PHY-3002 : Step(36): len = 161186, overlap = 15.75
PHY-3002 : Step(37): len = 159711, overlap = 13.5
PHY-3002 : Step(38): len = 157430, overlap = 18
PHY-3002 : Step(39): len = 153540, overlap = 13.5
PHY-3002 : Step(40): len = 151452, overlap = 9
PHY-3002 : Step(41): len = 148073, overlap = 9
PHY-3002 : Step(42): len = 145415, overlap = 9
PHY-3002 : Step(43): len = 141611, overlap = 6.75
PHY-3002 : Step(44): len = 135258, overlap = 4.5
PHY-3002 : Step(45): len = 131564, overlap = 4.5
PHY-3002 : Step(46): len = 130257, overlap = 4.5
PHY-3002 : Step(47): len = 125550, overlap = 9
PHY-3002 : Step(48): len = 122237, overlap = 9
PHY-3002 : Step(49): len = 120020, overlap = 6.75
PHY-3002 : Step(50): len = 119360, overlap = 6.75
PHY-3002 : Step(51): len = 116594, overlap = 11.25
PHY-3002 : Step(52): len = 114424, overlap = 4.5
PHY-3002 : Step(53): len = 111274, overlap = 2.25
PHY-3002 : Step(54): len = 110269, overlap = 4.5
PHY-3002 : Step(55): len = 108057, overlap = 4.5
PHY-3002 : Step(56): len = 106478, overlap = 9
PHY-3002 : Step(57): len = 105847, overlap = 6.75
PHY-3002 : Step(58): len = 104021, overlap = 2.25
PHY-3002 : Step(59): len = 102013, overlap = 2.25
PHY-3002 : Step(60): len = 99716.7, overlap = 2.25
PHY-3002 : Step(61): len = 98384.7, overlap = 4.5
PHY-3002 : Step(62): len = 96406.6, overlap = 9
PHY-3002 : Step(63): len = 96101.1, overlap = 4.5
PHY-3002 : Step(64): len = 96016.4, overlap = 4.5
PHY-3002 : Step(65): len = 95125, overlap = 4.5
PHY-3002 : Step(66): len = 93483.1, overlap = 4.5
PHY-3002 : Step(67): len = 92162.3, overlap = 11.25
PHY-3002 : Step(68): len = 90565.2, overlap = 4.5
PHY-3002 : Step(69): len = 89601.4, overlap = 4.5
PHY-3002 : Step(70): len = 88045.9, overlap = 4.5
PHY-3002 : Step(71): len = 87159.3, overlap = 4.5
PHY-3002 : Step(72): len = 85351.3, overlap = 4.5
PHY-3002 : Step(73): len = 79420.2, overlap = 9
PHY-3002 : Step(74): len = 76861.5, overlap = 9
PHY-3002 : Step(75): len = 75823.6, overlap = 6.75
PHY-3002 : Step(76): len = 74969.2, overlap = 9
PHY-3002 : Step(77): len = 74903.4, overlap = 6.75
PHY-3002 : Step(78): len = 74887.9, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000265394
PHY-3002 : Step(79): len = 74885.6, overlap = 6.75
PHY-3002 : Step(80): len = 74901, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000530789
PHY-3002 : Step(81): len = 74884, overlap = 6.75
PHY-3002 : Step(82): len = 75038.5, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006313s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065381s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(83): len = 78322.1, overlap = 4.125
PHY-3002 : Step(84): len = 76749.3, overlap = 3.6875
PHY-3002 : Step(85): len = 75049.4, overlap = 4.0625
PHY-3002 : Step(86): len = 73012.5, overlap = 3.25
PHY-3002 : Step(87): len = 71664.3, overlap = 3
PHY-3002 : Step(88): len = 70040.6, overlap = 3.375
PHY-3002 : Step(89): len = 67796.4, overlap = 4.1875
PHY-3002 : Step(90): len = 65782.7, overlap = 6.375
PHY-3002 : Step(91): len = 64311.3, overlap = 6.5
PHY-3002 : Step(92): len = 62775.9, overlap = 6.75
PHY-3002 : Step(93): len = 61025.6, overlap = 6.375
PHY-3002 : Step(94): len = 59134, overlap = 5.1875
PHY-3002 : Step(95): len = 58311.1, overlap = 4.6875
PHY-3002 : Step(96): len = 57640.9, overlap = 4.4375
PHY-3002 : Step(97): len = 57048.8, overlap = 4.25
PHY-3002 : Step(98): len = 55488.5, overlap = 7.125
PHY-3002 : Step(99): len = 55117.7, overlap = 7.6875
PHY-3002 : Step(100): len = 54642.7, overlap = 7.4375
PHY-3002 : Step(101): len = 54023.7, overlap = 7.4375
PHY-3002 : Step(102): len = 53282.2, overlap = 8
PHY-3002 : Step(103): len = 53025.5, overlap = 7.375
PHY-3002 : Step(104): len = 52724.4, overlap = 6.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00148932
PHY-3002 : Step(105): len = 52691, overlap = 7.125
PHY-3002 : Step(106): len = 52535, overlap = 7.1875
PHY-3002 : Step(107): len = 52454.4, overlap = 6.6875
PHY-3002 : Step(108): len = 52238.7, overlap = 6.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064695s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000166475
PHY-3002 : Step(109): len = 53321.3, overlap = 35.8125
PHY-3002 : Step(110): len = 53799.3, overlap = 36.6562
PHY-3002 : Step(111): len = 53135.8, overlap = 38.5312
PHY-3002 : Step(112): len = 53458.9, overlap = 38.0938
PHY-3002 : Step(113): len = 52843.8, overlap = 33.625
PHY-3002 : Step(114): len = 52576.8, overlap = 33.2812
PHY-3002 : Step(115): len = 52378.8, overlap = 32.75
PHY-3002 : Step(116): len = 52395.4, overlap = 32.7188
PHY-3002 : Step(117): len = 52380.2, overlap = 34.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000332951
PHY-3002 : Step(118): len = 52423.7, overlap = 34.1562
PHY-3002 : Step(119): len = 52533.5, overlap = 34.2188
PHY-3002 : Step(120): len = 52965.2, overlap = 33.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000665902
PHY-3002 : Step(121): len = 53333.2, overlap = 33.4688
PHY-3002 : Step(122): len = 53674.4, overlap = 33.25
PHY-3002 : Step(123): len = 54062.1, overlap = 33.5938
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.0013318
PHY-3002 : Step(124): len = 54022.1, overlap = 33.7812
PHY-3002 : Step(125): len = 54267.1, overlap = 32.4375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00266361
PHY-3002 : Step(126): len = 54490.6, overlap = 31.5938
PHY-3002 : Step(127): len = 54652.2, overlap = 31.2188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00532721
PHY-3002 : Step(128): len = 54694.2, overlap = 30.4375
PHY-3002 : Step(129): len = 54814.8, overlap = 30.3125
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.0106544
PHY-3002 : Step(130): len = 54845.7, overlap = 31
PHY-3002 : Step(131): len = 54882.6, overlap = 30.3125
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.0213089
PHY-3002 : Step(132): len = 54972.4, overlap = 30.3438
PHY-3002 : Step(133): len = 55007, overlap = 29.75
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0426177
PHY-3002 : Step(134): len = 55092.2, overlap = 29.6875
PHY-3002 : Step(135): len = 55092.2, overlap = 29.6875
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.0852354
PHY-3002 : Step(136): len = 55118, overlap = 29.4062
PHY-3002 : Step(137): len = 55118, overlap = 29.4062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7843, tnet num: 2215, tinst num: 1645, tnode num: 11081, tedge num: 13241.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 94.34 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57584, over cnt = 247(0%), over = 1034, worst = 17
PHY-1001 : End global iterations;  0.062756s wall, 0.062500s user + 0.046875s system = 0.109375s CPU (174.3%)

PHY-1001 : Congestion index: top1 = 43.73, top5 = 25.28, top10 = 16.47, top15 = 11.76.
PHY-1001 : End incremental global routing;  0.114374s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (150.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070255s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.215485s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (123.3%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1668/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57584, over cnt = 247(0%), over = 1034, worst = 17
PHY-1002 : len = 62592, over cnt = 175(0%), over = 523, worst = 16
PHY-1002 : len = 68920, over cnt = 21(0%), over = 32, worst = 6
PHY-1002 : len = 69152, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 69232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.092559s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (118.2%)

PHY-1001 : Congestion index: top1 = 37.87, top5 = 25.18, top10 = 18.05, top15 = 13.44.
OPT-1001 : End congestion update;  0.136554s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (114.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060288s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.199377s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (109.7%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.708059s wall, 0.703125s user + 0.062500s system = 0.765625s CPU (108.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 91 SEQ with LUT/SLICE
SYN-4006 : 120 single LUT's are left
SYN-4006 : 712 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1092/1425 primitive instances ...
PHY-3001 : End packing;  0.051364s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 852 instances
RUN-1001 : 401 mslices, 402 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1478 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 850 instances, 803 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55384, Over = 56
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 850, tnode num: 8961, tedge num: 11612.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.315815s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.36948e-05
PHY-3002 : Step(138): len = 54498.8, overlap = 54
PHY-3002 : Step(139): len = 53687.5, overlap = 53.5
PHY-3002 : Step(140): len = 53369.2, overlap = 54.75
PHY-3002 : Step(141): len = 53514.9, overlap = 55.75
PHY-3002 : Step(142): len = 53293.7, overlap = 56.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.73897e-05
PHY-3002 : Step(143): len = 53719.1, overlap = 56.25
PHY-3002 : Step(144): len = 54499.4, overlap = 53.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000134779
PHY-3002 : Step(145): len = 54764.2, overlap = 53
PHY-3002 : Step(146): len = 55875.5, overlap = 47.25
PHY-3002 : Step(147): len = 56159, overlap = 46.5
PHY-3002 : Step(148): len = 56466.8, overlap = 47.5
PHY-3002 : Step(149): len = 56699, overlap = 47.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.088536s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (247.1%)

PHY-3001 : Trial Legalized: Len = 70642.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055688s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000673143
PHY-3002 : Step(150): len = 68286.9, overlap = 5.75
PHY-3002 : Step(151): len = 66150.7, overlap = 9.25
PHY-3002 : Step(152): len = 64173.3, overlap = 17.5
PHY-3002 : Step(153): len = 63065.4, overlap = 17.75
PHY-3002 : Step(154): len = 62211.1, overlap = 20.25
PHY-3002 : Step(155): len = 61808.9, overlap = 24.5
PHY-3002 : Step(156): len = 61456.1, overlap = 25.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00134629
PHY-3002 : Step(157): len = 61776.7, overlap = 24.25
PHY-3002 : Step(158): len = 61892, overlap = 23.5
PHY-3002 : Step(159): len = 61901.8, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00269257
PHY-3002 : Step(160): len = 61973.2, overlap = 23.25
PHY-3002 : Step(161): len = 61973.2, overlap = 23.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005668s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 66682.3, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005836s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 0, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 66790.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 850, tnode num: 8961, tedge num: 11612.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 106/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73520, over cnt = 156(0%), over = 232, worst = 6
PHY-1002 : len = 74080, over cnt = 105(0%), over = 134, worst = 4
PHY-1002 : len = 75336, over cnt = 26(0%), over = 29, worst = 3
PHY-1002 : len = 75752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111748s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (111.9%)

PHY-1001 : Congestion index: top1 = 31.59, top5 = 22.78, top10 = 18.00, top15 = 14.52.
PHY-1001 : End incremental global routing;  0.165560s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (113.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063745s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260205s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (108.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1810/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 75752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010656s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.6%)

PHY-1001 : Congestion index: top1 = 31.59, top5 = 22.78, top10 = 18.00, top15 = 14.52.
OPT-1001 : End congestion update;  0.059920s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053273s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 812 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 850 instances, 803 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 66821, Over = 0
PHY-3001 : End spreading;  0.005536s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (282.2%)

PHY-3001 : Final: Len = 66821, Over = 0
PHY-3001 : End incremental legalization;  0.034818s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (134.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.160150s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.6%)

OPT-1001 : Current memory(MB): used = 225, reserve = 189, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049068s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1807/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 75824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008140s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (191.9%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.82, top10 = 18.02, top15 = 14.54.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059726s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (104.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.885368s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (102.4%)

RUN-1003 : finish command "place" in  5.704373s wall, 9.359375s user + 2.968750s system = 12.328125s CPU (216.1%)

RUN-1004 : used memory is 205 MB, reserved memory is 168 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 852 instances
RUN-1001 : 401 mslices, 402 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1478 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 850, tnode num: 8961, tedge num: 11612.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 401 mslices, 402 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72528, over cnt = 160(0%), over = 233, worst = 6
PHY-1002 : len = 73648, over cnt = 69(0%), over = 80, worst = 3
PHY-1002 : len = 74312, over cnt = 25(0%), over = 32, worst = 3
PHY-1002 : len = 74712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133434s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (152.2%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.59, top10 = 17.83, top15 = 14.33.
PHY-1001 : End global routing;  0.188634s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (140.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 206, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 502, reserve = 471, peak = 502.
PHY-1001 : End build detailed router design. 3.246204s wall, 3.171875s user + 0.078125s system = 3.250000s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.334967s wall, 1.312500s user + 0.031250s system = 1.343750s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 537.
PHY-1001 : End phase 1; 1.341053s wall, 1.328125s user + 0.031250s system = 1.359375s CPU (101.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 188592, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End initial routed; 1.148222s wall, 2.390625s user + 0.125000s system = 2.515625s CPU (219.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1805(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.324   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.379266s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (103.0%)

PHY-1001 : Current memory(MB): used = 538, reserve = 506, peak = 538.
PHY-1001 : End phase 2; 1.527583s wall, 2.781250s user + 0.125000s system = 2.906250s CPU (190.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 188592, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014834s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 188464, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026005s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (180.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 188512, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.025146s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (124.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 188528, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021804s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (71.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1805(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.324   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.381970s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (102.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.183771s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.0%)

PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End phase 3; 0.779691s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (102.2%)

PHY-1003 : Routed, final wirelength = 188528
PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End export database. 0.011886s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.090786s wall, 8.234375s user + 0.250000s system = 8.484375s CPU (119.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 850, tnode num: 8961, tedge num: 11612.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.027333s wall, 9.218750s user + 0.281250s system = 9.500000s CPU (118.3%)

RUN-1004 : used memory is 526 MB, reserved memory is 495 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      838   out of  19600    4.28%
#reg                     1074   out of  19600    5.48%
#le                      1550
  #lut only               476   out of   1550   30.71%
  #reg only               712   out of   1550   45.94%
  #lut&reg                362   out of   1550   23.35%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1550   |612     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1138   |297     |133     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |540    |132     |58      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |160    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |13      |0       |25      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |93     |23      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |83      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |114    |103     |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |23     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |56     |56      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1442  
    #2          2       324   
    #3          3       104   
    #4          4        14   
    #5        5-10       83   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6613, tnet num: 2040, tinst num: 850, tnode num: 8961, tedge num: 11612.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 850
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2042, pip num: 15014
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1341 valid insts, and 39723 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.178559s wall, 18.671875s user + 0.062500s system = 18.734375s CPU (589.4%)

RUN-1004 : used memory is 550 MB, reserved memory is 517 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230817_184152.log"
