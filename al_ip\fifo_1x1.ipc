<?xml version='1.0' encoding='UTF-8'?>
<FIFOConfig>
    <GeneralConfig>
        <Type>EG_LOGIC_FIFO</Type>
        <Device>EG4S20NG88</Device>
        <create_VHDL>true</create_VHDL>
        <inst>fifo_1x1</inst>
    </GeneralConfig>
    <DataPortOption>
        <write_width>1</write_width>
        <write_depth>1024</write_depth>
        <read_width>2</read_width>
        <read_depth>512</read_depth>
    </DataPortOption>
    <FlagOption>
        <aempty_flag>false</aempty_flag>
        <afull_flag>false</afull_flag>
        <resetmode>ASYNC</resetmode>
        <reset_release>SYNC</reset_release>
    </FlagOption>
    <OutputOption>
        <regmode_r>None</regmode_r>
    </OutputOption>
    <InputOption>
        <regmode_w>None</regmode_w>
    </InputOption>
    <EndianOption>
        <endian>Big</endian>
    </EndianOption>
    <SyncClockOption>
        <sync_clk>false</sync_clk>
        <SSROVERCE>false</SSROVERCE>
    </SyncClockOption>
    <ECC>
        <ecc_is_enable>false</ecc_is_enable>
        <ecc_is_de_enable>false</ecc_is_de_enable>
    </ECC>
    <PH_OutputOption>
        <ph_regmode_r>OUTREG</ph_regmode_r>
    </PH_OutputOption>
    <GeneratedFiles>
        <Verilog Enable="true">fifo_1x1.v</Verilog>
        <SimVerilog Enable="false">fifo_1x1_sim.v</SimVerilog>
        <VHDL Enable="false">fifo_1x1.vhd</VHDL>
    </GeneratedFiles>
</FIFOConfig>
