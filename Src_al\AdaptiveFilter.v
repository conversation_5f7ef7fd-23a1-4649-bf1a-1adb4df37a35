//////////////////////////////////////////////////////////////////////////////////
// 自适应滤波器模块
// 功能：根据信号特性自动调整滤波参数，实现最优滤波效果
// 原理：使用LMS（最小均方）算法，通过误差反馈自动优化滤波器系数
//////////////////////////////////////////////////////////////////////////////////

module AdaptiveFilter
#(
    parameter DATA_WIDTH = 12,      // 输入数据位宽
    parameter COEFF_WIDTH = 16,     // 系数位宽
    parameter FILTER_ORDER = 8,     // 滤波器阶数
    parameter OUTPUT_WIDTH = 16,    // 输出位宽
    parameter MU_WIDTH = 8          // 学习率位宽
)
(
    input                           clk,
    input                           rst_n,
    input                           data_valid,
    input   [DATA_WIDTH-1:0]        data_in,        // 输入信号
    input   [DATA_WIDTH-1:0]        reference_in,   // 参考信号（期望输出）
    input   [MU_WIDTH-1:0]          learning_rate,  // 学习率（适应速度）
    output reg                      data_ready,
    output reg [OUTPUT_WIDTH-1:0]   data_out,       // 滤波输出
    output reg [15:0]               error_out       // 误差输出（用于监测）
);

// 自适应滤波器系数（会自动调整）
reg signed [COEFF_WIDTH-1:0] adaptive_coeff [0:FILTER_ORDER-1];

// 输入数据移位寄存器
reg signed [DATA_WIDTH-1:0] input_buffer [0:FILTER_ORDER-1];

// 中间计算变量
reg signed [DATA_WIDTH+COEFF_WIDTH-1:0] mult_results [0:FILTER_ORDER-1];
reg signed [DATA_WIDTH+COEFF_WIDTH+3:0] filter_output;
reg signed [DATA_WIDTH:0] error_signal;
reg signed [COEFF_WIDTH-1:0] coeff_update [0:FILTER_ORDER-1];

integer i;

// 初始化滤波器系数（从简单的平均滤波开始）
initial begin
    for (i = 0; i < FILTER_ORDER; i = i + 1) begin
        adaptive_coeff[i] = 16'h2000 / FILTER_ORDER;  // 初始值：1/N
    end
end

// 输入数据缓存更新
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        for (i = 0; i < FILTER_ORDER; i = i + 1) begin
            input_buffer[i] <= 0;
        end
    end
    else if (data_valid) begin
        // 新数据进入，历史数据后移
        input_buffer[0] <= data_in;
        for (i = 1; i < FILTER_ORDER; i = i + 1) begin
            input_buffer[i] <= input_buffer[i-1];
        end
    end
end

// 滤波器输出计算（第一个时钟周期）
always @(posedge clk) begin
    if (data_valid) begin
        // 计算每个抽头的乘法结果
        for (i = 0; i < FILTER_ORDER; i = i + 1) begin
            mult_results[i] <= input_buffer[i] * adaptive_coeff[i];
        end
    end
end

// 累加和误差计算（第二个时钟周期）
always @(posedge clk) begin
    // 累加所有乘法结果
    filter_output <= mult_results[0] + mult_results[1] + mult_results[2] + mult_results[3] +
                     mult_results[4] + mult_results[5] + mult_results[6] + mult_results[7];
    
    // 计算误差：期望输出 - 实际输出
    error_signal <= reference_in - filter_output[DATA_WIDTH+COEFF_WIDTH+3:COEFF_WIDTH];
end

// 系数更新（LMS算法核心）
always @(posedge clk) begin
    if (data_valid) begin
        for (i = 0; i < FILTER_ORDER; i = i + 1) begin
            // LMS更新公式：w[n+1] = w[n] + μ * error * x[n]
            // μ是学习率，error是误差，x[n]是输入
            coeff_update[i] <= (learning_rate * error_signal * input_buffer[i]) >>> 8;
            adaptive_coeff[i] <= adaptive_coeff[i] + coeff_update[i];
        end
    end
end

// 输出数据和状态
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        data_out <= 0;
        error_out <= 0;
        data_ready <= 0;
    end
    else begin
        // 输出滤波结果
        data_out <= filter_output[DATA_WIDTH+COEFF_WIDTH+3:DATA_WIDTH+COEFF_WIDTH+3-OUTPUT_WIDTH+1];
        // 输出误差（用于监测收敛情况）
        error_out <= error_signal[15:0];
        // 延迟3个时钟周期后数据准备好
        data_ready <= data_valid;
    end
end

// 系数饱和保护（防止系数过大或过小）
always @(posedge clk) begin
    for (i = 0; i < FILTER_ORDER; i = i + 1) begin
        if (adaptive_coeff[i] > 16'h7FFF) begin
            adaptive_coeff[i] <= 16'h7FFF;  // 正向饱和
        end
        else if (adaptive_coeff[i] < 16'h8000) begin
            adaptive_coeff[i] <= 16'h8000;  // 负向饱和
        end
    end
end

endmodule
