============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jan 11 15:07:01 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1538 instances
RUN-0007 : 370 luts, 907 seqs, 137 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2093 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1551 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1536 instances, 370 luts, 907 seqs, 212 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7380, tnet num: 2091, tinst num: 1536, tnode num: 10365, tedge num: 12521.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2091 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.281334s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 552143
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1536.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 466820, overlap = 15.75
PHY-3002 : Step(2): len = 430832, overlap = 20.25
PHY-3002 : Step(3): len = 413079, overlap = 20.25
PHY-3002 : Step(4): len = 396162, overlap = 20.25
PHY-3002 : Step(5): len = 386035, overlap = 18
PHY-3002 : Step(6): len = 371952, overlap = 20.25
PHY-3002 : Step(7): len = 363619, overlap = 20.25
PHY-3002 : Step(8): len = 356025, overlap = 20.25
PHY-3002 : Step(9): len = 344094, overlap = 18
PHY-3002 : Step(10): len = 335215, overlap = 20.25
PHY-3002 : Step(11): len = 329099, overlap = 18
PHY-3002 : Step(12): len = 319378, overlap = 20.25
PHY-3002 : Step(13): len = 311832, overlap = 18
PHY-3002 : Step(14): len = 305396, overlap = 20.25
PHY-3002 : Step(15): len = 298603, overlap = 13.5
PHY-3002 : Step(16): len = 289383, overlap = 18
PHY-3002 : Step(17): len = 283576, overlap = 13.5
PHY-3002 : Step(18): len = 278588, overlap = 18
PHY-3002 : Step(19): len = 272540, overlap = 13.5
PHY-3002 : Step(20): len = 265477, overlap = 15.75
PHY-3002 : Step(21): len = 261249, overlap = 15.75
PHY-3002 : Step(22): len = 256066, overlap = 15.75
PHY-3002 : Step(23): len = 247509, overlap = 15.75
PHY-3002 : Step(24): len = 241789, overlap = 13.5
PHY-3002 : Step(25): len = 238826, overlap = 13.5
PHY-3002 : Step(26): len = 229544, overlap = 13.5
PHY-3002 : Step(27): len = 219526, overlap = 13.5
PHY-3002 : Step(28): len = 216227, overlap = 13.5
PHY-3002 : Step(29): len = 211821, overlap = 13.5
PHY-3002 : Step(30): len = 187749, overlap = 13.5
PHY-3002 : Step(31): len = 179974, overlap = 11.25
PHY-3002 : Step(32): len = 178872, overlap = 13.5
PHY-3002 : Step(33): len = 156239, overlap = 18
PHY-3002 : Step(34): len = 136382, overlap = 15.75
PHY-3002 : Step(35): len = 133881, overlap = 11.25
PHY-3002 : Step(36): len = 132105, overlap = 15.75
PHY-3002 : Step(37): len = 127441, overlap = 13.5
PHY-3002 : Step(38): len = 125523, overlap = 13.5
PHY-3002 : Step(39): len = 121074, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000140235
PHY-3002 : Step(40): len = 121277, overlap = 13.5
PHY-3002 : Step(41): len = 120693, overlap = 13.5
PHY-3002 : Step(42): len = 120145, overlap = 13.5
PHY-3002 : Step(43): len = 119187, overlap = 13.5
PHY-3002 : Step(44): len = 118541, overlap = 13.5
PHY-3002 : Step(45): len = 114470, overlap = 13.5
PHY-3002 : Step(46): len = 111040, overlap = 13.5
PHY-3002 : Step(47): len = 110538, overlap = 11.25
PHY-3002 : Step(48): len = 109045, overlap = 9
PHY-3002 : Step(49): len = 105437, overlap = 13.5
PHY-3002 : Step(50): len = 103832, overlap = 13.5
PHY-3002 : Step(51): len = 101942, overlap = 13.5
PHY-3002 : Step(52): len = 100102, overlap = 13.5
PHY-3002 : Step(53): len = 97990.3, overlap = 15.75
PHY-3002 : Step(54): len = 97042.6, overlap = 13.5
PHY-3002 : Step(55): len = 93610.1, overlap = 9
PHY-3002 : Step(56): len = 91638.9, overlap = 11.25
PHY-3002 : Step(57): len = 90318.7, overlap = 13.5
PHY-3002 : Step(58): len = 88610.5, overlap = 13.5
PHY-3002 : Step(59): len = 85444.8, overlap = 15.75
PHY-3002 : Step(60): len = 83862.7, overlap = 13.5
PHY-3002 : Step(61): len = 82123.7, overlap = 11.25
PHY-3002 : Step(62): len = 81291.8, overlap = 11.25
PHY-3002 : Step(63): len = 80462.3, overlap = 9
PHY-3002 : Step(64): len = 79129.6, overlap = 11.25
PHY-3002 : Step(65): len = 75276.4, overlap = 15.75
PHY-3002 : Step(66): len = 72573.5, overlap = 11.5
PHY-3002 : Step(67): len = 71738.3, overlap = 11.4375
PHY-3002 : Step(68): len = 69908.3, overlap = 13.75
PHY-3002 : Step(69): len = 69499, overlap = 11.5625
PHY-3002 : Step(70): len = 68797.4, overlap = 11.9375
PHY-3002 : Step(71): len = 68174.5, overlap = 12.5625
PHY-3002 : Step(72): len = 67070.2, overlap = 10.6875
PHY-3002 : Step(73): len = 66047.1, overlap = 16
PHY-3002 : Step(74): len = 65074.4, overlap = 14.4375
PHY-3002 : Step(75): len = 63680.3, overlap = 14.625
PHY-3002 : Step(76): len = 62359.9, overlap = 14.9375
PHY-3002 : Step(77): len = 61824.2, overlap = 16.9375
PHY-3002 : Step(78): len = 60494.2, overlap = 14.9375
PHY-3002 : Step(79): len = 59447.7, overlap = 14.25
PHY-3002 : Step(80): len = 57643.8, overlap = 11.6875
PHY-3002 : Step(81): len = 57398.6, overlap = 13.75
PHY-3002 : Step(82): len = 56499.2, overlap = 16.125
PHY-3002 : Step(83): len = 55977.5, overlap = 18.0625
PHY-3002 : Step(84): len = 55870.7, overlap = 13.625
PHY-3002 : Step(85): len = 55417.9, overlap = 14.0625
PHY-3002 : Step(86): len = 54865.2, overlap = 15.875
PHY-3002 : Step(87): len = 54767.3, overlap = 16
PHY-3002 : Step(88): len = 54089.7, overlap = 16.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00028047
PHY-3002 : Step(89): len = 54120.5, overlap = 16.375
PHY-3002 : Step(90): len = 54132.8, overlap = 16.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00056094
PHY-3002 : Step(91): len = 54211.2, overlap = 16.375
PHY-3002 : Step(92): len = 54255.9, overlap = 16.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006934s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2091 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056356s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000210935
PHY-3002 : Step(93): len = 58033.6, overlap = 16.125
PHY-3002 : Step(94): len = 56986.8, overlap = 15.125
PHY-3002 : Step(95): len = 56250.3, overlap = 15
PHY-3002 : Step(96): len = 55346.1, overlap = 15.6875
PHY-3002 : Step(97): len = 54682.8, overlap = 15.7188
PHY-3002 : Step(98): len = 53776.2, overlap = 16.0938
PHY-3002 : Step(99): len = 52800.6, overlap = 18.9375
PHY-3002 : Step(100): len = 51459.1, overlap = 19.6562
PHY-3002 : Step(101): len = 50608.1, overlap = 19.4688
PHY-3002 : Step(102): len = 50164.9, overlap = 19.875
PHY-3002 : Step(103): len = 49468.4, overlap = 22.5312
PHY-3002 : Step(104): len = 49291.9, overlap = 21.4375
PHY-3002 : Step(105): len = 49047, overlap = 21.625
PHY-3002 : Step(106): len = 48827.6, overlap = 21.6875
PHY-3002 : Step(107): len = 48660.3, overlap = 22.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00042187
PHY-3002 : Step(108): len = 48542.2, overlap = 22.75
PHY-3002 : Step(109): len = 48493.1, overlap = 22.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00084374
PHY-3002 : Step(110): len = 48333.7, overlap = 22.0938
PHY-3002 : Step(111): len = 48442.4, overlap = 21.7812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2091 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066910s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (93.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.57921e-05
PHY-3002 : Step(112): len = 48614.2, overlap = 55.5
PHY-3002 : Step(113): len = 49839.7, overlap = 50.9688
PHY-3002 : Step(114): len = 50366.3, overlap = 49.0938
PHY-3002 : Step(115): len = 49812.2, overlap = 46.5938
PHY-3002 : Step(116): len = 49757.3, overlap = 46.875
PHY-3002 : Step(117): len = 49606.2, overlap = 46.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000111584
PHY-3002 : Step(118): len = 49757.5, overlap = 45.8438
PHY-3002 : Step(119): len = 50154.7, overlap = 44.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000223168
PHY-3002 : Step(120): len = 50263.9, overlap = 43.7812
PHY-3002 : Step(121): len = 51308.8, overlap = 40.2812
PHY-3002 : Step(122): len = 51785.4, overlap = 39.125
PHY-3002 : Step(123): len = 52030, overlap = 41.625
PHY-3002 : Step(124): len = 52292.4, overlap = 37.4062
PHY-3002 : Step(125): len = 52684.2, overlap = 34.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7380, tnet num: 2091, tinst num: 1536, tnode num: 10365, tedge num: 12521.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 83.56 peak overflow 2.16
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2093.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56192, over cnt = 264(0%), over = 1094, worst = 22
PHY-1001 : End global iterations;  0.086661s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (162.3%)

PHY-1001 : Congestion index: top1 = 44.61, top5 = 27.41, top10 = 16.78, top15 = 11.78.
PHY-1001 : End incremental global routing;  0.138775s wall, 0.140625s user + 0.062500s system = 0.203125s CPU (146.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2091 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073741s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.244330s wall, 0.250000s user + 0.062500s system = 0.312500s CPU (127.9%)

OPT-1001 : Current memory(MB): used = 211, reserve = 175, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1643/2093.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56192, over cnt = 264(0%), over = 1094, worst = 22
PHY-1002 : len = 65088, over cnt = 152(0%), over = 306, worst = 14
PHY-1002 : len = 67776, over cnt = 37(0%), over = 52, worst = 8
PHY-1002 : len = 68456, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 68896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112168s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (153.2%)

PHY-1001 : Congestion index: top1 = 39.05, top5 = 26.87, top10 = 18.93, top15 = 13.74.
OPT-1001 : End congestion update;  0.156726s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (139.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2091 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057264s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.217218s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (129.5%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : End physical optimization;  0.738531s wall, 0.781250s user + 0.078125s system = 0.859375s CPU (116.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 370 LUT to BLE ...
SYN-4008 : Packed 370 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 102 SEQ with LUT/SLICE
SYN-4006 : 116 single LUT's are left
SYN-4006 : 634 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1004/1299 primitive instances ...
PHY-3001 : End packing;  0.047603s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 787 instances
RUN-1001 : 369 mslices, 369 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1395 nets have 2 pins
RUN-1001 : 423 nets have [3 - 5] pins
RUN-1001 : 69 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 785 instances, 738 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52414.8, Over = 59
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6219, tnet num: 1928, tinst num: 785, tnode num: 8377, tedge num: 10960.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.316240s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (103.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.66094e-05
PHY-3002 : Step(126): len = 51470.2, overlap = 58.25
PHY-3002 : Step(127): len = 50881.4, overlap = 62.75
PHY-3002 : Step(128): len = 50586.8, overlap = 63.25
PHY-3002 : Step(129): len = 50587.1, overlap = 59
PHY-3002 : Step(130): len = 50601.1, overlap = 58
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.32189e-05
PHY-3002 : Step(131): len = 51115.8, overlap = 55.25
PHY-3002 : Step(132): len = 51639.7, overlap = 52.75
PHY-3002 : Step(133): len = 52028.4, overlap = 52.5
PHY-3002 : Step(134): len = 52028.4, overlap = 52.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000106438
PHY-3002 : Step(135): len = 52778.9, overlap = 51
PHY-3002 : Step(136): len = 53869.3, overlap = 49.25
PHY-3002 : Step(137): len = 54354.3, overlap = 47.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.071834s wall, 0.062500s user + 0.125000s system = 0.187500s CPU (261.0%)

PHY-3001 : Trial Legalized: Len = 68477.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051795s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0010393
PHY-3002 : Step(138): len = 64865.3, overlap = 6.75
PHY-3002 : Step(139): len = 62738.8, overlap = 13
PHY-3002 : Step(140): len = 60845.6, overlap = 14.5
PHY-3002 : Step(141): len = 59833.9, overlap = 14.25
PHY-3002 : Step(142): len = 58983.9, overlap = 14.5
PHY-3002 : Step(143): len = 58573.8, overlap = 17
PHY-3002 : Step(144): len = 58387, overlap = 17.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00207861
PHY-3002 : Step(145): len = 58557.9, overlap = 17
PHY-3002 : Step(146): len = 58681.3, overlap = 17.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00415722
PHY-3002 : Step(147): len = 58764.6, overlap = 16.5
PHY-3002 : Step(148): len = 58773.3, overlap = 17
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005543s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (281.9%)

PHY-3001 : Legalized: Len = 62973.6, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006442s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 16 instances has been re-located, deltaX = 6, deltaY = 12, maxDist = 2.
PHY-3001 : Final: Len = 63145.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6219, tnet num: 1928, tinst num: 785, tnode num: 8377, tedge num: 10960.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 54/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69872, over cnt = 156(0%), over = 222, worst = 7
PHY-1002 : len = 70560, over cnt = 83(0%), over = 114, worst = 7
PHY-1002 : len = 71984, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72000, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146240s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (160.3%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.39, top10 = 17.78, top15 = 13.95.
PHY-1001 : End incremental global routing;  0.201229s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (139.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057684s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (108.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.288789s wall, 0.312500s user + 0.062500s system = 0.375000s CPU (129.9%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1700/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006912s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.39, top10 = 17.78, top15 = 13.95.
OPT-1001 : End congestion update;  0.054559s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055576s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 747 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 785 instances, 738 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63160.8, Over = 0
PHY-3001 : End spreading;  0.004816s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63160.8, Over = 0
PHY-3001 : End incremental legalization;  0.034509s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.6%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.157550s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.2%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047508s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1696/1930.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72064, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.019344s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (161.6%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.39, top10 = 17.78, top15 = 13.95.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049224s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.275862
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.904156s wall, 0.921875s user + 0.062500s system = 0.984375s CPU (108.9%)

RUN-1003 : finish command "place" in  5.370794s wall, 8.218750s user + 2.796875s system = 11.015625s CPU (205.1%)

RUN-1004 : used memory is 196 MB, reserved memory is 159 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 787 instances
RUN-1001 : 369 mslices, 369 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1930 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1395 nets have 2 pins
RUN-1001 : 423 nets have [3 - 5] pins
RUN-1001 : 69 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6219, tnet num: 1928, tinst num: 785, tnode num: 8377, tedge num: 10960.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 369 mslices, 369 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1928 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69224, over cnt = 149(0%), over = 212, worst = 7
PHY-1002 : len = 70008, over cnt = 74(0%), over = 100, worst = 7
PHY-1002 : len = 71232, over cnt = 14(0%), over = 17, worst = 2
PHY-1002 : len = 71504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.137747s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (90.7%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 22.24, top10 = 17.59, top15 = 13.84.
PHY-1001 : End global routing;  0.189248s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (90.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 201, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 465, peak = 497.
PHY-1001 : End build detailed router design. 3.273933s wall, 3.156250s user + 0.109375s system = 3.265625s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32496, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.438519s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 529, reserve = 498, peak = 529.
PHY-1001 : End phase 1; 1.444646s wall, 1.437500s user + 0.000000s system = 1.437500s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 185736, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 499, peak = 532.
PHY-1001 : End initial routed; 1.509681s wall, 2.625000s user + 0.234375s system = 2.859375s CPU (189.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1704(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.427   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.462   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350838s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.4%)

PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End phase 2; 1.860612s wall, 2.984375s user + 0.234375s system = 3.218750s CPU (173.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 185736, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014894s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 185688, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024461s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (191.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 185776, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023257s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (201.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1704(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.427   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.462   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.365631s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.169507s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End phase 3; 0.723032s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (103.7%)

PHY-1003 : Routed, final wirelength = 185776
PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End export database. 0.010790s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (144.8%)

PHY-1001 : End detail routing;  7.490099s wall, 8.484375s user + 0.375000s system = 8.859375s CPU (118.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6219, tnet num: 1928, tinst num: 785, tnode num: 8377, tedge num: 10960.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6259, tnet num: 1948, tinst num: 805, tnode num: 8417, tedge num: 11000.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.037178s wall, 3.187500s user + 0.125000s system = 3.312500s CPU (109.1%)

RUN-1003 : finish command "route" in  11.071846s wall, 12.187500s user + 0.515625s system = 12.703125s CPU (114.7%)

RUN-1004 : used memory is 525 MB, reserved memory is 493 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      834   out of  19600    4.26%
#reg                      990   out of  19600    5.05%
#le                      1468
  #lut only               478   out of   1468   32.56%
  #reg only               634   out of   1468   43.19%
  #lut&reg                356   out of   1468   24.25%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         433
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         99
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1468   |622     |212     |1021    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1065   |313     |122     |836     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |45     |36      |9       |24      |0       |0       |
|    demodu                  |Demodulation                                     |470    |123     |44      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |27      |6       |49      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |7       |0       |17      |0       |0       |
|    integ                   |Integration                                      |134    |22      |14      |108     |0       |0       |
|    modu                    |Modulation                                       |92     |36      |21      |87      |0       |1       |
|    rs422                   |Rs422Output                                      |303    |80      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |21     |16      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |122    |111     |7       |61      |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |19      |0       |0       |
|    U1                      |uart_tx                                          |25     |21      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |63     |63      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |204    |159     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1379  
    #2          2       285   
    #3          3       122   
    #4          4        16   
    #5        5-10       75   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6259, tnet num: 1948, tinst num: 805, tnode num: 8417, tedge num: 11000.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1948 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 805
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1950, pip num: 14424
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1286 valid insts, and 38503 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.081978s wall, 17.109375s user + 0.078125s system = 17.187500s CPU (557.7%)

RUN-1004 : used memory is 548 MB, reserved memory is 515 MB, peak memory is 674 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240111_150701.log"
