============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Nov 13 14:27:33 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/111.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 28 trigger nets, 28 data nets.
KIT-1004 : Chipwatcher code = 1011101111010000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=94) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=94) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=94)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=94)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3317/18 useful/useless nets, 2037/10 useful/useless insts
SYN-1016 : Merged 24 instances.
SYN-1032 : 3054/20 useful/useless nets, 2390/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 317 better
SYN-1014 : Optimize round 2
SYN-1032 : 2815/30 useful/useless nets, 2151/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2851/222 useful/useless nets, 2211/33 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 288 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 26 instances.
SYN-2501 : Optimize round 1, 54 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 3232/5 useful/useless nets, 2592/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11860, tnet num: 3232, tinst num: 2591, tnode num: 15868, tedge num: 19058.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3232 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 190 (3.55), #lev = 7 (1.92)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 190 (3.55), #lev = 7 (1.92)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 447 instances into 190 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 327 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.475991s wall, 1.328125s user + 0.156250s system = 1.484375s CPU (100.6%)

RUN-1004 : used memory is 170 MB, reserved memory is 131 MB, peak memory is 202 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (207 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2161 instances
RUN-0007 : 564 luts, 1233 seqs, 179 mslices, 110 lslices, 34 pads, 29 brams, 5 dsps
RUN-1001 : There are total 2803 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1923 nets have 2 pins
RUN-1001 : 692 nets have [3 - 5] pins
RUN-1001 : 117 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 36 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     270     
RUN-1001 :   No   |  No   |  Yes  |     231     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     332     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  17   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 27
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2159 instances, 564 luts, 1233 seqs, 289 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10913, tnet num: 2801, tinst num: 2159, tnode num: 15161, tedge num: 18486.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2801 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.384557s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 741551
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2159.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 643396, overlap = 72
PHY-3002 : Step(2): len = 612419, overlap = 76.5
PHY-3002 : Step(3): len = 582170, overlap = 76.5
PHY-3002 : Step(4): len = 560886, overlap = 74.25
PHY-3002 : Step(5): len = 543888, overlap = 76.5
PHY-3002 : Step(6): len = 528431, overlap = 76.5
PHY-3002 : Step(7): len = 514242, overlap = 74.25
PHY-3002 : Step(8): len = 504444, overlap = 69.75
PHY-3002 : Step(9): len = 449679, overlap = 74.25
PHY-3002 : Step(10): len = 416487, overlap = 72
PHY-3002 : Step(11): len = 403906, overlap = 67.5
PHY-3002 : Step(12): len = 396252, overlap = 72
PHY-3002 : Step(13): len = 382595, overlap = 67.5
PHY-3002 : Step(14): len = 361442, overlap = 74.25
PHY-3002 : Step(15): len = 355465, overlap = 74.25
PHY-3002 : Step(16): len = 349520, overlap = 72
PHY-3002 : Step(17): len = 340223, overlap = 72
PHY-3002 : Step(18): len = 334332, overlap = 72
PHY-3002 : Step(19): len = 327307, overlap = 72
PHY-3002 : Step(20): len = 318179, overlap = 72
PHY-3002 : Step(21): len = 311156, overlap = 72
PHY-3002 : Step(22): len = 305184, overlap = 72
PHY-3002 : Step(23): len = 293132, overlap = 69.75
PHY-3002 : Step(24): len = 283850, overlap = 69.75
PHY-3002 : Step(25): len = 279367, overlap = 69.75
PHY-3002 : Step(26): len = 270142, overlap = 69.75
PHY-3002 : Step(27): len = 249055, overlap = 74.25
PHY-3002 : Step(28): len = 243524, overlap = 72
PHY-3002 : Step(29): len = 239910, overlap = 72
PHY-3002 : Step(30): len = 202759, overlap = 74.9688
PHY-3002 : Step(31): len = 191698, overlap = 74.625
PHY-3002 : Step(32): len = 190420, overlap = 74.6875
PHY-3002 : Step(33): len = 182469, overlap = 75.2188
PHY-3002 : Step(34): len = 177062, overlap = 73.0312
PHY-3002 : Step(35): len = 174014, overlap = 70.6875
PHY-3002 : Step(36): len = 167860, overlap = 72.2812
PHY-3002 : Step(37): len = 165105, overlap = 71.5625
PHY-3002 : Step(38): len = 160882, overlap = 70.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 2.96774e-05
PHY-3002 : Step(39): len = 163259, overlap = 70.75
PHY-3002 : Step(40): len = 160753, overlap = 66.3438
PHY-3002 : Step(41): len = 158969, overlap = 64.1875
PHY-3002 : Step(42): len = 155453, overlap = 64.25
PHY-3002 : Step(43): len = 153470, overlap = 64.25
PHY-3002 : Step(44): len = 151402, overlap = 62.0938
PHY-3002 : Step(45): len = 149129, overlap = 61.6562
PHY-3002 : Step(46): len = 147571, overlap = 61.4375
PHY-3002 : Step(47): len = 146010, overlap = 63.6875
PHY-3002 : Step(48): len = 142169, overlap = 63.8125
PHY-3002 : Step(49): len = 132673, overlap = 63.9375
PHY-3002 : Step(50): len = 128256, overlap = 61.8125
PHY-3002 : Step(51): len = 126532, overlap = 64
PHY-3002 : Step(52): len = 125535, overlap = 63.9375
PHY-3002 : Step(53): len = 122191, overlap = 64.1875
PHY-3002 : Step(54): len = 120049, overlap = 64.375
PHY-3002 : Step(55): len = 117165, overlap = 62.25
PHY-3002 : Step(56): len = 112748, overlap = 64.625
PHY-3002 : Step(57): len = 111467, overlap = 64.5
PHY-3002 : Step(58): len = 108890, overlap = 64.8125
PHY-3002 : Step(59): len = 103586, overlap = 67.1875
PHY-3002 : Step(60): len = 100811, overlap = 63
PHY-3002 : Step(61): len = 100265, overlap = 63.375
PHY-3002 : Step(62): len = 98459.8, overlap = 65.875
PHY-3002 : Step(63): len = 96886.5, overlap = 66.0625
PHY-3002 : Step(64): len = 95813.5, overlap = 61.375
PHY-3002 : Step(65): len = 92575.9, overlap = 62.75
PHY-3002 : Step(66): len = 91045.6, overlap = 60.5625
PHY-3002 : Step(67): len = 90103.3, overlap = 65.0625
PHY-3002 : Step(68): len = 88809.1, overlap = 67.375
PHY-3002 : Step(69): len = 87604.5, overlap = 65.8125
PHY-3002 : Step(70): len = 86167.9, overlap = 68.0625
PHY-3002 : Step(71): len = 83951.5, overlap = 66.25
PHY-3002 : Step(72): len = 82952.5, overlap = 66
PHY-3002 : Step(73): len = 82147.6, overlap = 61.0625
PHY-3002 : Step(74): len = 81010.1, overlap = 60.25
PHY-3002 : Step(75): len = 79648.9, overlap = 59.8125
PHY-3002 : Step(76): len = 78590.2, overlap = 62.1875
PHY-3002 : Step(77): len = 77421, overlap = 62.1875
PHY-3002 : Step(78): len = 76739.2, overlap = 59.5625
PHY-3002 : Step(79): len = 74943.6, overlap = 59.625
PHY-3002 : Step(80): len = 74100, overlap = 56.7188
PHY-3002 : Step(81): len = 73265.5, overlap = 59.5
PHY-3002 : Step(82): len = 72452.8, overlap = 57.9688
PHY-3002 : Step(83): len = 71935.2, overlap = 58.4062
PHY-3002 : Step(84): len = 70864.8, overlap = 65.625
PHY-3002 : Step(85): len = 69987.6, overlap = 69.9375
PHY-3002 : Step(86): len = 69248.1, overlap = 67.7188
PHY-3002 : Step(87): len = 68738.3, overlap = 67.9062
PHY-3002 : Step(88): len = 67927.7, overlap = 68.5625
PHY-3002 : Step(89): len = 67266.1, overlap = 69.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 5.93548e-05
PHY-3002 : Step(90): len = 67625.8, overlap = 69.4375
PHY-3002 : Step(91): len = 67848.9, overlap = 69.2188
PHY-3002 : Step(92): len = 67973.8, overlap = 67.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00011871
PHY-3002 : Step(93): len = 68199, overlap = 67.0312
PHY-3002 : Step(94): len = 68328.8, overlap = 67.0312
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008559s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (547.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2801 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.099191s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.83748e-05
PHY-3002 : Step(95): len = 79275.5, overlap = 33.625
PHY-3002 : Step(96): len = 79353.5, overlap = 33.9375
PHY-3002 : Step(97): len = 78682.5, overlap = 33.875
PHY-3002 : Step(98): len = 78324.3, overlap = 32.8125
PHY-3002 : Step(99): len = 78176.3, overlap = 32.7188
PHY-3002 : Step(100): len = 77098.5, overlap = 33.6562
PHY-3002 : Step(101): len = 76896.1, overlap = 35.3125
PHY-3002 : Step(102): len = 76000.3, overlap = 32.0938
PHY-3002 : Step(103): len = 75313.2, overlap = 31
PHY-3002 : Step(104): len = 74709.8, overlap = 32.7812
PHY-3002 : Step(105): len = 73784.4, overlap = 33.125
PHY-3002 : Step(106): len = 73526.8, overlap = 34.0938
PHY-3002 : Step(107): len = 72681, overlap = 34.3125
PHY-3002 : Step(108): len = 71674.1, overlap = 35.0625
PHY-3002 : Step(109): len = 71042.9, overlap = 37.5312
PHY-3002 : Step(110): len = 70736.3, overlap = 38.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00015675
PHY-3002 : Step(111): len = 70395.6, overlap = 38.6875
PHY-3002 : Step(112): len = 70630.7, overlap = 39
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000313499
PHY-3002 : Step(113): len = 70469.8, overlap = 39.125
PHY-3002 : Step(114): len = 70742.7, overlap = 37.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2801 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.087553s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (89.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.36104e-05
PHY-3002 : Step(115): len = 70431.1, overlap = 81.9062
PHY-3002 : Step(116): len = 70514.9, overlap = 80.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.71726e-05
PHY-3002 : Step(117): len = 70812.9, overlap = 81.5938
PHY-3002 : Step(118): len = 73311.9, overlap = 68.4688
PHY-3002 : Step(119): len = 73312.5, overlap = 66.1562
PHY-3002 : Step(120): len = 73178.1, overlap = 59.6562
PHY-3002 : Step(121): len = 74180.9, overlap = 57.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000134345
PHY-3002 : Step(122): len = 73631.3, overlap = 56.9688
PHY-3002 : Step(123): len = 74045.9, overlap = 48.1562
PHY-3002 : Step(124): len = 74480.5, overlap = 46.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00026869
PHY-3002 : Step(125): len = 74619.8, overlap = 47.8125
PHY-3002 : Step(126): len = 75458.6, overlap = 44.2812
PHY-3002 : Step(127): len = 76837.5, overlap = 40.125
PHY-3002 : Step(128): len = 77095.3, overlap = 39.6562
PHY-3002 : Step(129): len = 76459.5, overlap = 42.1562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10913, tnet num: 2801, tinst num: 2159, tnode num: 15161, tedge num: 18486.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 122.22 peak overflow 3.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2803.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 87064, over cnt = 333(0%), over = 1336, worst = 18
PHY-1001 : End global iterations;  0.153696s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (152.5%)

PHY-1001 : Congestion index: top1 = 46.66, top5 = 29.80, top10 = 21.52, top15 = 16.38.
PHY-1001 : End incremental global routing;  0.224805s wall, 0.234375s user + 0.062500s system = 0.296875s CPU (132.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2801 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.118577s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (105.4%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 2119 has valid locations, 25 needs to be replaced
PHY-3001 : design contains 2183 instances, 564 luts, 1257 seqs, 289 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 76715.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11009, tnet num: 2825, tinst num: 2183, tnode num: 15329, tedge num: 18630.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2825 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.498679s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(130): len = 76717.9, overlap = 3.5625
PHY-3002 : Step(131): len = 76816, overlap = 3.5625
PHY-3002 : Step(132): len = 76816, overlap = 3.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2825 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.125917s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00207555
PHY-3002 : Step(133): len = 76896.5, overlap = 42.0938
PHY-3002 : Step(134): len = 76896.5, overlap = 42.0938
PHY-3001 : Final: Len = 76896.5, Over = 42.0938
PHY-3001 : End incremental placement;  0.734114s wall, 0.750000s user + 0.078125s system = 0.828125s CPU (112.8%)

OPT-1001 : Total overflow 122.47 peak overflow 3.81
OPT-1001 : End high-fanout net optimization;  1.133967s wall, 1.156250s user + 0.156250s system = 1.312500s CPU (115.7%)

OPT-1001 : Current memory(MB): used = 245, reserve = 204, peak = 245.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2105/2827.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 87936, over cnt = 334(0%), over = 1317, worst = 18
PHY-1002 : len = 98584, over cnt = 230(0%), over = 481, worst = 15
PHY-1002 : len = 101344, over cnt = 129(0%), over = 270, worst = 15
PHY-1002 : len = 104168, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 104400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.173434s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (117.1%)

PHY-1001 : Congestion index: top1 = 41.92, top5 = 29.12, top10 = 22.92, top15 = 18.36.
OPT-1001 : End congestion update;  0.231261s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (108.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2825 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.111120s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (112.5%)

OPT-0007 : Start: WNS -2897 TNS -52374 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2897 TNS -52374 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.347155s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (108.0%)

OPT-1001 : Current memory(MB): used = 245, reserve = 204, peak = 245.
OPT-1001 : End physical optimization;  1.905506s wall, 1.937500s user + 0.171875s system = 2.109375s CPU (110.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 564 LUT to BLE ...
SYN-4008 : Packed 564 LUT and 232 SEQ to BLE.
SYN-4003 : Packing 1025 remaining SEQ's ...
SYN-4005 : Packed 245 SEQ with LUT/SLICE
SYN-4006 : 122 single LUT's are left
SYN-4006 : 780 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1344/1742 primitive instances ...
PHY-3001 : End packing;  0.099411s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1081 instances
RUN-1001 : 503 mslices, 503 lslices, 34 pads, 29 brams, 5 dsps
RUN-1001 : There are total 2604 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1702 nets have 2 pins
RUN-1001 : 714 nets have [3 - 5] pins
RUN-1001 : 119 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
PHY-3001 : design contains 1079 instances, 1006 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-3001 : Cell area utilization is 13%
PHY-3001 : After packing: Len = 77532.6, Over = 70
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 13%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9386, tnet num: 2602, tinst num: 1079, tnode num: 12510, tedge num: 16412.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2602 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.455052s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.92592e-05
PHY-3002 : Step(135): len = 76531.9, overlap = 69
PHY-3002 : Step(136): len = 75570.2, overlap = 72.25
PHY-3002 : Step(137): len = 74867.1, overlap = 75.75
PHY-3002 : Step(138): len = 74832.3, overlap = 77.75
PHY-3002 : Step(139): len = 74249.7, overlap = 77.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.85184e-05
PHY-3002 : Step(140): len = 74600.8, overlap = 78
PHY-3002 : Step(141): len = 75425.2, overlap = 75.25
PHY-3002 : Step(142): len = 75932.3, overlap = 72.5
PHY-3002 : Step(143): len = 75968.4, overlap = 71.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000117037
PHY-3002 : Step(144): len = 76293.9, overlap = 70.75
PHY-3002 : Step(145): len = 77587.4, overlap = 70
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.153879s wall, 0.078125s user + 0.265625s system = 0.343750s CPU (223.4%)

PHY-3001 : Trial Legalized: Len = 97662.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 13%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2602 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.111514s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000484213
PHY-3002 : Step(146): len = 93195.7, overlap = 5.5
PHY-3002 : Step(147): len = 89315.9, overlap = 15
PHY-3002 : Step(148): len = 86561.7, overlap = 18.5
PHY-3002 : Step(149): len = 85132.4, overlap = 21.75
PHY-3002 : Step(150): len = 84483.7, overlap = 25.75
PHY-3002 : Step(151): len = 84009, overlap = 25.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000968426
PHY-3002 : Step(152): len = 84457.7, overlap = 25.75
PHY-3002 : Step(153): len = 84545.7, overlap = 24.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00193685
PHY-3002 : Step(154): len = 84770.3, overlap = 23.25
PHY-3002 : Step(155): len = 84825.8, overlap = 22.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006077s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 91197.4, Over = 0
PHY-3001 : Spreading special nets. 36 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.011031s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (141.6%)

PHY-3001 : 51 instances has been re-located, deltaX = 26, deltaY = 31, maxDist = 3.
PHY-3001 : Final: Len = 92377.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9386, tnet num: 2602, tinst num: 1079, tnode num: 12510, tedge num: 16412.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 63/2604.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 108528, over cnt = 261(0%), over = 443, worst = 5
PHY-1002 : len = 110488, over cnt = 148(0%), over = 210, worst = 5
PHY-1002 : len = 112088, over cnt = 52(0%), over = 71, worst = 4
PHY-1002 : len = 113208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.247454s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (132.6%)

PHY-1001 : Congestion index: top1 = 33.86, top5 = 26.51, top10 = 22.11, top15 = 18.68.
PHY-1001 : End incremental global routing;  0.318727s wall, 0.343750s user + 0.046875s system = 0.390625s CPU (122.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2602 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.126794s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.520759s wall, 0.546875s user + 0.046875s system = 0.593750s CPU (114.0%)

OPT-1001 : Current memory(MB): used = 243, reserve = 204, peak = 245.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2266/2604.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 113208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010283s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (151.9%)

PHY-1001 : Congestion index: top1 = 33.86, top5 = 26.51, top10 = 22.11, top15 = 18.68.
OPT-1001 : End congestion update;  0.088397s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2602 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.075284s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (83.0%)

OPT-0007 : Start: WNS -2897 TNS -52974 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2897 TNS -52974 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.167415s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.7%)

OPT-1001 : Current memory(MB): used = 245, reserve = 206, peak = 245.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2602 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.083845s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (93.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2266/2604.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 113208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009050s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (172.7%)

PHY-1001 : Congestion index: top1 = 33.86, top5 = 26.51, top10 = 22.11, top15 = 18.68.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2602 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.076862s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -2897 TNS -52974 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -2897ps with logic level 2 
RUN-1001 :       #2 path slack -2897ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2604 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2604 nets
OPT-1001 : End physical optimization;  1.349961s wall, 1.500000s user + 0.046875s system = 1.546875s CPU (114.6%)

RUN-1003 : finish command "place" in  7.994649s wall, 12.203125s user + 4.828125s system = 17.031250s CPU (213.0%)

RUN-1004 : used memory is 233 MB, reserved memory is 193 MB, peak memory is 246 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1081 instances
RUN-1001 : 503 mslices, 503 lslices, 34 pads, 29 brams, 5 dsps
RUN-1001 : There are total 2604 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1702 nets have 2 pins
RUN-1001 : 714 nets have [3 - 5] pins
RUN-1001 : 119 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9386, tnet num: 2602, tinst num: 1079, tnode num: 12510, tedge num: 16412.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 503 mslices, 503 lslices, 34 pads, 29 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2602 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 107584, over cnt = 260(0%), over = 449, worst = 5
PHY-1002 : len = 109544, over cnt = 157(0%), over = 226, worst = 4
PHY-1002 : len = 111800, over cnt = 33(0%), over = 38, worst = 2
PHY-1002 : len = 112272, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 112304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.270732s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (121.2%)

PHY-1001 : Congestion index: top1 = 33.53, top5 = 26.28, top10 = 21.98, top15 = 18.52.
PHY-1001 : End global routing;  0.341021s wall, 0.328125s user + 0.062500s system = 0.390625s CPU (114.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 263, reserve = 223, peak = 274.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 527, reserve = 490, peak = 527.
PHY-1001 : End build detailed router design. 4.180770s wall, 4.109375s user + 0.078125s system = 4.187500s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 42792, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.558661s wall, 2.562500s user + 0.000000s system = 2.562500s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 560, reserve = 525, peak = 560.
PHY-1001 : End phase 1; 2.564614s wall, 2.578125s user + 0.000000s system = 2.578125s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 334040, over cnt = 64(0%), over = 64, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 560, reserve = 525, peak = 561.
PHY-1001 : End initial routed; 4.836873s wall, 6.093750s user + 0.250000s system = 6.343750s CPU (131.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2314(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.138   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.253   |  -23.345  |  37   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.542866s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 562, reserve = 526, peak = 562.
PHY-1001 : End phase 2; 5.379848s wall, 6.640625s user + 0.250000s system = 6.890625s CPU (128.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 334040, over cnt = 64(0%), over = 64, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.023494s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (133.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 333872, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.087904s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (124.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 334016, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.034894s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (134.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 334032, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.026529s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (117.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2314(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.138   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.253   |  -23.345  |  37   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.571785s wall, 0.562500s user + 0.015625s system = 0.578125s CPU (101.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 11 feed throughs used by 10 nets
PHY-1001 : End commit to database; 0.326060s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 578, reserve = 542, peak = 578.
PHY-1001 : End phase 3; 1.213418s wall, 1.187500s user + 0.046875s system = 1.234375s CPU (101.7%)

PHY-1003 : Routed, final wirelength = 334032
PHY-1001 : Current memory(MB): used = 579, reserve = 543, peak = 579.
PHY-1001 : End export database. 0.013711s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (114.0%)

PHY-1001 : End detail routing;  13.572978s wall, 14.750000s user + 0.375000s system = 15.125000s CPU (111.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9386, tnet num: 2602, tinst num: 1079, tnode num: 12510, tedge num: 16412.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  15.010728s wall, 16.171875s user + 0.437500s system = 16.609375s CPU (110.7%)

RUN-1004 : used memory is 553 MB, reserved memory is 521 MB, peak memory is 579 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1149   out of  19600    5.86%
#reg                     1312   out of  19600    6.69%
#le                      1929
  #lut only               617   out of   1929   31.99%
  #reg only               780   out of   1929   40.44%
  #lut&reg                532   out of   1929   27.58%
#dsp                        5   out of     29   17.24%
#bram                      29   out of     64   45.31%
  #bram9k                  29
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                  Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                   432
#2        config_inst_syn_9               GCLK               config             config_inst.jtck                        112
#3        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di                         103
#4        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                   101
#5        wendu/clk_us                    GCLK               lslice             signal_process/demodu/reg1_syn_89.q0    42
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                           11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                   1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1929   |860     |289     |1343    |29      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |997    |261     |105     |808     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |26     |20      |6       |22      |0       |0       |
|    demodu                          |Demodulation                                     |442    |82      |44      |357     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |56     |27      |6       |46      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |7       |0       |17      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |6       |0       |15      |0       |0       |
|    integ                           |Integration                                      |135    |30      |14      |109     |0       |0       |
|    modu                            |Modulation                                       |54     |15      |7       |52      |0       |1       |
|    rs422                           |Rs422Output                                      |310    |89      |29      |249     |0       |4       |
|    trans                           |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                            |UART_Control                                     |127    |120     |7       |61      |0       |0       |
|    U0                              |speed_select_Tx                                  |32     |25      |7       |19      |0       |0       |
|    U1                              |uart_tx                                          |21     |21      |0       |14      |0       |0       |
|    U2                              |Ctrl_Data                                        |74     |74      |0       |28      |0       |0       |
|  wendu                             |DS18B20                                          |201    |156     |45      |71      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |508    |281     |91      |334     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |508    |281     |91      |334     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |203    |99      |0       |202     |0       |0       |
|        reg_inst                    |register                                         |200    |96      |0       |199     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |305    |182     |91      |132     |0       |0       |
|        bus_inst                    |bus_top                                          |91     |50      |30      |33      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |1      |0       |0       |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det                                          |47     |22      |16      |15      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det                                          |39     |24      |14      |13      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |125    |78      |29      |72      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1667  
    #2          2       448   
    #3          3       226   
    #4          4        40   
    #5        5-10      126   
    #6        11-50      49   
    #7       51-100      2    
    #8       101-500     2    
  Average     2.32            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9386, tnet num: 2602, tinst num: 1079, tnode num: 12510, tedge num: 16412.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2602 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 0be513b19a9cc175ba86a812b9762225081cfa4c3fd045b02a849695b6355a0c -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1079
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2604, pip num: 22877
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1692 valid insts, and 59029 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010001101011101111010000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.583185s wall, 27.046875s user + 0.125000s system = 27.171875s CPU (592.9%)

RUN-1004 : used memory is 579 MB, reserved memory is 543 MB, peak memory is 699 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231113_142733.log"
