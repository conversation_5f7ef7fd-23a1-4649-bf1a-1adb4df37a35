============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Oct 10 17:50:38 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1638 instances
RUN-0007 : 374 luts, 1009 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2178 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1616 nets have 2 pins
RUN-1001 : 449 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1636 instances, 374 luts, 1009 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7770, tnet num: 2176, tinst num: 1636, tnode num: 11032, tedge num: 13136.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2176 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.282185s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (105.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 633306
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1636.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 528066, overlap = 20.25
PHY-3002 : Step(2): len = 488007, overlap = 20.25
PHY-3002 : Step(3): len = 411101, overlap = 13.5
PHY-3002 : Step(4): len = 359194, overlap = 18
PHY-3002 : Step(5): len = 327823, overlap = 20.25
PHY-3002 : Step(6): len = 304679, overlap = 20.25
PHY-3002 : Step(7): len = 298639, overlap = 20.25
PHY-3002 : Step(8): len = 290656, overlap = 20.25
PHY-3002 : Step(9): len = 276449, overlap = 20.25
PHY-3002 : Step(10): len = 268827, overlap = 20.25
PHY-3002 : Step(11): len = 262185, overlap = 20.25
PHY-3002 : Step(12): len = 254206, overlap = 20.25
PHY-3002 : Step(13): len = 245719, overlap = 20.25
PHY-3002 : Step(14): len = 240730, overlap = 20.25
PHY-3002 : Step(15): len = 233370, overlap = 20.25
PHY-3002 : Step(16): len = 227529, overlap = 20.25
PHY-3002 : Step(17): len = 222367, overlap = 20.25
PHY-3002 : Step(18): len = 217056, overlap = 20.25
PHY-3002 : Step(19): len = 211654, overlap = 20.25
PHY-3002 : Step(20): len = 206917, overlap = 20.25
PHY-3002 : Step(21): len = 202265, overlap = 20.25
PHY-3002 : Step(22): len = 198234, overlap = 20.25
PHY-3002 : Step(23): len = 191883, overlap = 20.25
PHY-3002 : Step(24): len = 187773, overlap = 20.25
PHY-3002 : Step(25): len = 183759, overlap = 20.25
PHY-3002 : Step(26): len = 177952, overlap = 20.25
PHY-3002 : Step(27): len = 173738, overlap = 20.25
PHY-3002 : Step(28): len = 170364, overlap = 20.25
PHY-3002 : Step(29): len = 165300, overlap = 20.25
PHY-3002 : Step(30): len = 161502, overlap = 20.25
PHY-3002 : Step(31): len = 157400, overlap = 20.25
PHY-3002 : Step(32): len = 153958, overlap = 20.25
PHY-3002 : Step(33): len = 145261, overlap = 20.25
PHY-3002 : Step(34): len = 141593, overlap = 18
PHY-3002 : Step(35): len = 139499, overlap = 18
PHY-3002 : Step(36): len = 134724, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000101307
PHY-3002 : Step(37): len = 137013, overlap = 13.5
PHY-3002 : Step(38): len = 135512, overlap = 18
PHY-3002 : Step(39): len = 131717, overlap = 18
PHY-3002 : Step(40): len = 125521, overlap = 13.5
PHY-3002 : Step(41): len = 122537, overlap = 13.5
PHY-3002 : Step(42): len = 120303, overlap = 11.25
PHY-3002 : Step(43): len = 117576, overlap = 15.75
PHY-3002 : Step(44): len = 115150, overlap = 13.5
PHY-3002 : Step(45): len = 112288, overlap = 13.5
PHY-3002 : Step(46): len = 110437, overlap = 13.5
PHY-3002 : Step(47): len = 107380, overlap = 15.75
PHY-3002 : Step(48): len = 104583, overlap = 15.75
PHY-3002 : Step(49): len = 101612, overlap = 13.5
PHY-3002 : Step(50): len = 101024, overlap = 13.5
PHY-3002 : Step(51): len = 96377.1, overlap = 15.75
PHY-3002 : Step(52): len = 94270.3, overlap = 9
PHY-3002 : Step(53): len = 92155.6, overlap = 9
PHY-3002 : Step(54): len = 91578.2, overlap = 11.25
PHY-3002 : Step(55): len = 90132.4, overlap = 13.5
PHY-3002 : Step(56): len = 89342.4, overlap = 11.25
PHY-3002 : Step(57): len = 87850.6, overlap = 9
PHY-3002 : Step(58): len = 87119.7, overlap = 11.25
PHY-3002 : Step(59): len = 84338.7, overlap = 11.25
PHY-3002 : Step(60): len = 83589.8, overlap = 6.75
PHY-3002 : Step(61): len = 83031.4, overlap = 9
PHY-3002 : Step(62): len = 81490.7, overlap = 11.375
PHY-3002 : Step(63): len = 80094.7, overlap = 11.4375
PHY-3002 : Step(64): len = 79142, overlap = 11.4375
PHY-3002 : Step(65): len = 78721.7, overlap = 9.4375
PHY-3002 : Step(66): len = 76949, overlap = 9.5
PHY-3002 : Step(67): len = 74740.4, overlap = 12.0625
PHY-3002 : Step(68): len = 72612.9, overlap = 9.875
PHY-3002 : Step(69): len = 71930.7, overlap = 12.125
PHY-3002 : Step(70): len = 71049.1, overlap = 12.1875
PHY-3002 : Step(71): len = 69779.5, overlap = 12.375
PHY-3002 : Step(72): len = 69355, overlap = 12.4375
PHY-3002 : Step(73): len = 68494.4, overlap = 12.4375
PHY-3002 : Step(74): len = 67450.2, overlap = 12.4375
PHY-3002 : Step(75): len = 67531.2, overlap = 10.1875
PHY-3002 : Step(76): len = 67441.3, overlap = 10.1875
PHY-3002 : Step(77): len = 66922.5, overlap = 10.1875
PHY-3002 : Step(78): len = 66325.7, overlap = 12.8125
PHY-3002 : Step(79): len = 64724.8, overlap = 12.5625
PHY-3002 : Step(80): len = 64029.1, overlap = 12.5625
PHY-3002 : Step(81): len = 63081.3, overlap = 14.75
PHY-3002 : Step(82): len = 62887.6, overlap = 14.75
PHY-3002 : Step(83): len = 61881.8, overlap = 12.75
PHY-3002 : Step(84): len = 59503.1, overlap = 10.3125
PHY-3002 : Step(85): len = 57764, overlap = 12.375
PHY-3002 : Step(86): len = 57544.1, overlap = 14.625
PHY-3002 : Step(87): len = 57265.5, overlap = 14.5
PHY-3002 : Step(88): len = 56834.9, overlap = 12.3125
PHY-3002 : Step(89): len = 56707.3, overlap = 12.375
PHY-3002 : Step(90): len = 56828.4, overlap = 12.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000202614
PHY-3002 : Step(91): len = 56892.3, overlap = 14.6875
PHY-3002 : Step(92): len = 56958.5, overlap = 16.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000405228
PHY-3002 : Step(93): len = 56886, overlap = 12.4375
PHY-3002 : Step(94): len = 56881.8, overlap = 12.4375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006368s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2176 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061733s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00101951
PHY-3002 : Step(95): len = 60973.3, overlap = 12.0312
PHY-3002 : Step(96): len = 60021.2, overlap = 10.9375
PHY-3002 : Step(97): len = 59351.2, overlap = 11
PHY-3002 : Step(98): len = 58567.6, overlap = 10.6562
PHY-3002 : Step(99): len = 57828.1, overlap = 11.125
PHY-3002 : Step(100): len = 56590.9, overlap = 11.4375
PHY-3002 : Step(101): len = 55519.9, overlap = 10.5625
PHY-3002 : Step(102): len = 54662.4, overlap = 10.5625
PHY-3002 : Step(103): len = 53872.4, overlap = 10.4688
PHY-3002 : Step(104): len = 53351.2, overlap = 10.9062
PHY-3002 : Step(105): len = 52723.8, overlap = 10.375
PHY-3002 : Step(106): len = 52008.1, overlap = 10.375
PHY-3002 : Step(107): len = 51281.1, overlap = 10.6875
PHY-3002 : Step(108): len = 50834.2, overlap = 9.9375
PHY-3002 : Step(109): len = 50130.9, overlap = 10.625
PHY-3002 : Step(110): len = 49822.2, overlap = 10.875
PHY-3002 : Step(111): len = 49653.2, overlap = 11.4375
PHY-3002 : Step(112): len = 49675.1, overlap = 11.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00203903
PHY-3002 : Step(113): len = 49563.8, overlap = 11.4375
PHY-3002 : Step(114): len = 49433, overlap = 11.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00407805
PHY-3002 : Step(115): len = 49336.6, overlap = 11.6875
PHY-3002 : Step(116): len = 49315.3, overlap = 11.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2176 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060892s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.44852e-05
PHY-3002 : Step(117): len = 49794.6, overlap = 62.3438
PHY-3002 : Step(118): len = 50398.5, overlap = 57.1562
PHY-3002 : Step(119): len = 50411.2, overlap = 56.625
PHY-3002 : Step(120): len = 50258.7, overlap = 53.375
PHY-3002 : Step(121): len = 50206.1, overlap = 49.8438
PHY-3002 : Step(122): len = 50234.5, overlap = 49.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00016897
PHY-3002 : Step(123): len = 50821.2, overlap = 48.4375
PHY-3002 : Step(124): len = 51301.2, overlap = 46.5
PHY-3002 : Step(125): len = 51816.5, overlap = 46.5312
PHY-3002 : Step(126): len = 51783.1, overlap = 45.8125
PHY-3002 : Step(127): len = 51805.5, overlap = 46.0312
PHY-3002 : Step(128): len = 51670.9, overlap = 41.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000337941
PHY-3002 : Step(129): len = 52023.9, overlap = 40.7188
PHY-3002 : Step(130): len = 52194.3, overlap = 40.5625
PHY-3002 : Step(131): len = 52424.4, overlap = 40.0312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7770, tnet num: 2176, tinst num: 1636, tnode num: 11032, tedge num: 13136.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 96.31 peak overflow 3.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2178.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55184, over cnt = 231(0%), over = 984, worst = 19
PHY-1001 : End global iterations;  0.069894s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (134.1%)

PHY-1001 : Congestion index: top1 = 43.73, top5 = 25.01, top10 = 15.90, top15 = 11.20.
PHY-1001 : End incremental global routing;  0.120061s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (104.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2176 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066786s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.216474s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.1%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1648/2178.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55184, over cnt = 231(0%), over = 984, worst = 19
PHY-1002 : len = 61544, over cnt = 162(0%), over = 342, worst = 19
PHY-1002 : len = 65376, over cnt = 30(0%), over = 42, worst = 6
PHY-1002 : len = 65776, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 66032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087690s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (124.7%)

PHY-1001 : Congestion index: top1 = 39.05, top5 = 25.23, top10 = 17.86, top15 = 12.89.
OPT-1001 : End congestion update;  0.130860s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (119.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2176 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059711s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.193433s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (113.1%)

OPT-1001 : Current memory(MB): used = 216, reserve = 181, peak = 216.
OPT-1001 : End physical optimization;  0.676284s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (120.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 100 SEQ with LUT/SLICE
SYN-4006 : 113 single LUT's are left
SYN-4006 : 727 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1101/1414 primitive instances ...
PHY-3001 : End packing;  0.050717s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 829 instances
RUN-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2010 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1456 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 827 instances, 780 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52381.2, Over = 68.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6521, tnet num: 2008, tinst num: 827, tnode num: 8860, tedge num: 11470.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.302465s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (98.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.50945e-05
PHY-3002 : Step(132): len = 51791, overlap = 72.25
PHY-3002 : Step(133): len = 51184.6, overlap = 73.75
PHY-3002 : Step(134): len = 51191.3, overlap = 76.5
PHY-3002 : Step(135): len = 51376.8, overlap = 74.5
PHY-3002 : Step(136): len = 51136.6, overlap = 72.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.01891e-05
PHY-3002 : Step(137): len = 51477.3, overlap = 70.75
PHY-3002 : Step(138): len = 51641.1, overlap = 70.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.10867e-05
PHY-3002 : Step(139): len = 52294.7, overlap = 67.25
PHY-3002 : Step(140): len = 53489.1, overlap = 60.75
PHY-3002 : Step(141): len = 54250.5, overlap = 60.25
PHY-3002 : Step(142): len = 54066.8, overlap = 60.5
PHY-3002 : Step(143): len = 54086.7, overlap = 60.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.065011s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (264.4%)

PHY-3001 : Trial Legalized: Len = 70392.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051912s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000626913
PHY-3002 : Step(144): len = 67100.7, overlap = 8
PHY-3002 : Step(145): len = 64378.5, overlap = 14.5
PHY-3002 : Step(146): len = 62830.7, overlap = 17
PHY-3002 : Step(147): len = 61583.8, overlap = 17.5
PHY-3002 : Step(148): len = 60756.6, overlap = 21.25
PHY-3002 : Step(149): len = 60461.4, overlap = 21.25
PHY-3002 : Step(150): len = 60182.6, overlap = 22.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00125383
PHY-3002 : Step(151): len = 60661.5, overlap = 21.5
PHY-3002 : Step(152): len = 60770.7, overlap = 21.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00250765
PHY-3002 : Step(153): len = 60882.1, overlap = 20.75
PHY-3002 : Step(154): len = 60977.2, overlap = 20.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004922s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64824.3, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005363s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (291.3%)

PHY-3001 : 7 instances has been re-located, deltaX = 2, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 64920.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6521, tnet num: 2008, tinst num: 827, tnode num: 8860, tedge num: 11470.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 59/2010.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71160, over cnt = 149(0%), over = 219, worst = 8
PHY-1002 : len = 72016, over cnt = 84(0%), over = 105, worst = 3
PHY-1002 : len = 73168, over cnt = 13(0%), over = 14, worst = 2
PHY-1002 : len = 73312, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 73488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127323s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (122.7%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.35, top10 = 18.21, top15 = 14.42.
PHY-1001 : End incremental global routing;  0.177377s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (114.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058590s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.265882s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (111.7%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1784/2010.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006543s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.35, top10 = 18.21, top15 = 14.42.
OPT-1001 : End congestion update;  0.052195s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051758s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.105775s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (103.4%)

OPT-1001 : Current memory(MB): used = 221, reserve = 186, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048316s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1784/2010.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006428s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.35, top10 = 18.21, top15 = 14.42.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051924s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.807429s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (104.5%)

RUN-1003 : finish command "place" in  5.224958s wall, 8.734375s user + 2.468750s system = 11.203125s CPU (214.4%)

RUN-1004 : used memory is 198 MB, reserved memory is 163 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 829 instances
RUN-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2010 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1456 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6521, tnet num: 2008, tinst num: 827, tnode num: 8860, tedge num: 11470.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2008 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70832, over cnt = 145(0%), over = 215, worst = 8
PHY-1002 : len = 71728, over cnt = 85(0%), over = 104, worst = 3
PHY-1002 : len = 72648, over cnt = 21(0%), over = 23, worst = 3
PHY-1002 : len = 72936, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 73080, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124698s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (125.3%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 23.25, top10 = 18.05, top15 = 14.30.
PHY-1001 : End global routing;  0.176789s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (114.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 245.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.141846s wall, 3.140625s user + 0.000000s system = 3.140625s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33224, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.307799s wall, 1.250000s user + 0.046875s system = 1.296875s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 532, reserve = 502, peak = 532.
PHY-1001 : End phase 1; 1.313519s wall, 1.265625s user + 0.046875s system = 1.312500s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 185616, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 534.
PHY-1001 : End initial routed; 1.112750s wall, 1.968750s user + 0.156250s system = 2.125000s CPU (191.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1791(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.699   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.532   |   9   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350683s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.0%)

PHY-1001 : Current memory(MB): used = 537, reserve = 506, peak = 537.
PHY-1001 : End phase 2; 1.463519s wall, 2.312500s user + 0.156250s system = 2.468750s CPU (168.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 185616, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014800s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 185616, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024249s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (128.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 185704, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021261s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (73.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1791(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.699   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.532   |   9   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.357484s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 0 feed throughs used by 0 nets
PHY-1001 : End commit to database; 0.171940s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End phase 3; 0.699039s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (100.6%)

PHY-1003 : Routed, final wirelength = 185704
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.009589s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (163.0%)

PHY-1001 : End detail routing;  6.805344s wall, 7.625000s user + 0.203125s system = 7.828125s CPU (115.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6521, tnet num: 2008, tinst num: 827, tnode num: 8860, tedge num: 11470.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addra[9] slack -36ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[20] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[28] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[30] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[4] slack -29ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6567, tnet num: 2031, tinst num: 850, tnode num: 8906, tedge num: 11516.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.061369s wall, 3.015625s user + 0.156250s system = 3.171875s CPU (103.6%)

RUN-1003 : finish command "route" in  10.383901s wall, 11.171875s user + 0.375000s system = 11.546875s CPU (111.2%)

RUN-1004 : used memory is 531 MB, reserved memory is 501 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      838   out of  19600    4.28%
#reg                     1074   out of  19600    5.48%
#le                      1565
  #lut only               491   out of   1565   31.37%
  #reg only               727   out of   1565   46.45%
  #lut&reg                347   out of   1565   22.17%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         469
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1565   |632     |206     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1180   |343     |122     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |21      |6       |16      |0       |0       |
|    demodu                  |Demodulation                                     |571    |164     |53      |435     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |64      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |0       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |24     |10      |0       |24      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |31     |18      |0       |31      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |100    |21      |15      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |97      |29      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |105    |86      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |24     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |44     |40      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1443  
    #2          2       313   
    #3          3       112   
    #4          4        12   
    #5        5-10       80   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6567, tnet num: 2031, tinst num: 850, tnode num: 8906, tedge num: 11516.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 850
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2033, pip num: 14905
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1328 valid insts, and 39429 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.038887s wall, 17.828125s user + 0.015625s system = 17.843750s CPU (587.2%)

RUN-1004 : used memory is 548 MB, reserved memory is 516 MB, peak memory is 675 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231010_175038.log"
