============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 17:50:16 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1631 instances
RUN-0007 : 376 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2201 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1641 nets have 2 pins
RUN-1001 : 446 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1629 instances, 376 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7804, tnet num: 2199, tinst num: 1629, tnode num: 11044, tedge num: 13195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.284048s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 580706
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1629.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 551230, overlap = 20.25
PHY-3002 : Step(2): len = 454342, overlap = 20.25
PHY-3002 : Step(3): len = 384287, overlap = 15.75
PHY-3002 : Step(4): len = 370538, overlap = 11.25
PHY-3002 : Step(5): len = 357869, overlap = 15.75
PHY-3002 : Step(6): len = 345047, overlap = 15.75
PHY-3002 : Step(7): len = 338643, overlap = 15.75
PHY-3002 : Step(8): len = 327160, overlap = 15.75
PHY-3002 : Step(9): len = 318898, overlap = 20.25
PHY-3002 : Step(10): len = 311087, overlap = 20.25
PHY-3002 : Step(11): len = 304551, overlap = 20.25
PHY-3002 : Step(12): len = 295066, overlap = 20.25
PHY-3002 : Step(13): len = 289752, overlap = 20.25
PHY-3002 : Step(14): len = 282611, overlap = 20.25
PHY-3002 : Step(15): len = 276248, overlap = 20.25
PHY-3002 : Step(16): len = 268831, overlap = 20.25
PHY-3002 : Step(17): len = 264810, overlap = 20.25
PHY-3002 : Step(18): len = 257081, overlap = 20.25
PHY-3002 : Step(19): len = 251477, overlap = 20.25
PHY-3002 : Step(20): len = 246173, overlap = 20.25
PHY-3002 : Step(21): len = 241040, overlap = 20.25
PHY-3002 : Step(22): len = 234612, overlap = 20.25
PHY-3002 : Step(23): len = 231029, overlap = 20.25
PHY-3002 : Step(24): len = 224876, overlap = 20.25
PHY-3002 : Step(25): len = 221252, overlap = 20.25
PHY-3002 : Step(26): len = 215336, overlap = 20.25
PHY-3002 : Step(27): len = 211688, overlap = 20.25
PHY-3002 : Step(28): len = 207009, overlap = 20.25
PHY-3002 : Step(29): len = 203478, overlap = 20.25
PHY-3002 : Step(30): len = 197976, overlap = 20.25
PHY-3002 : Step(31): len = 194356, overlap = 20.25
PHY-3002 : Step(32): len = 190005, overlap = 20.25
PHY-3002 : Step(33): len = 183956, overlap = 20.25
PHY-3002 : Step(34): len = 178077, overlap = 20.25
PHY-3002 : Step(35): len = 176251, overlap = 20.25
PHY-3002 : Step(36): len = 166318, overlap = 20.25
PHY-3002 : Step(37): len = 142482, overlap = 20.25
PHY-3002 : Step(38): len = 138354, overlap = 20.25
PHY-3002 : Step(39): len = 136313, overlap = 20.25
PHY-3002 : Step(40): len = 128949, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.98314e-05
PHY-3002 : Step(41): len = 128736, overlap = 11.25
PHY-3002 : Step(42): len = 126450, overlap = 13.5
PHY-3002 : Step(43): len = 124970, overlap = 13.5
PHY-3002 : Step(44): len = 122787, overlap = 13.5
PHY-3002 : Step(45): len = 118768, overlap = 11.25
PHY-3002 : Step(46): len = 116883, overlap = 11.25
PHY-3002 : Step(47): len = 114195, overlap = 4.5
PHY-3002 : Step(48): len = 112863, overlap = 9
PHY-3002 : Step(49): len = 109013, overlap = 13.5
PHY-3002 : Step(50): len = 108083, overlap = 11.25
PHY-3002 : Step(51): len = 105430, overlap = 11.25
PHY-3002 : Step(52): len = 102397, overlap = 11.25
PHY-3002 : Step(53): len = 100053, overlap = 11.25
PHY-3002 : Step(54): len = 99624.7, overlap = 9
PHY-3002 : Step(55): len = 96382.4, overlap = 11.25
PHY-3002 : Step(56): len = 95550.2, overlap = 9
PHY-3002 : Step(57): len = 94955.4, overlap = 6.75
PHY-3002 : Step(58): len = 93429.3, overlap = 9
PHY-3002 : Step(59): len = 91976.9, overlap = 6.75
PHY-3002 : Step(60): len = 88553, overlap = 13.5
PHY-3002 : Step(61): len = 86039.8, overlap = 11.25
PHY-3002 : Step(62): len = 84277.2, overlap = 11.25
PHY-3002 : Step(63): len = 83904.9, overlap = 13.5
PHY-3002 : Step(64): len = 82759.9, overlap = 13.5
PHY-3002 : Step(65): len = 81554.6, overlap = 11.375
PHY-3002 : Step(66): len = 80718.8, overlap = 6.9375
PHY-3002 : Step(67): len = 79441.3, overlap = 13.5
PHY-3002 : Step(68): len = 73142.3, overlap = 11.4375
PHY-3002 : Step(69): len = 72030.3, overlap = 13.625
PHY-3002 : Step(70): len = 71028.5, overlap = 11.375
PHY-3002 : Step(71): len = 70324.5, overlap = 9.125
PHY-3002 : Step(72): len = 69730.3, overlap = 9.125
PHY-3002 : Step(73): len = 69100.5, overlap = 9
PHY-3002 : Step(74): len = 68239.7, overlap = 9
PHY-3002 : Step(75): len = 67564.3, overlap = 9
PHY-3002 : Step(76): len = 66862.6, overlap = 9
PHY-3002 : Step(77): len = 66119.2, overlap = 11.25
PHY-3002 : Step(78): len = 65578.8, overlap = 11.25
PHY-3002 : Step(79): len = 65062.8, overlap = 11.25
PHY-3002 : Step(80): len = 64740.7, overlap = 11.25
PHY-3002 : Step(81): len = 64043.6, overlap = 11.25
PHY-3002 : Step(82): len = 63477.1, overlap = 11.25
PHY-3002 : Step(83): len = 63295.3, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000199663
PHY-3002 : Step(84): len = 63555.5, overlap = 11.25
PHY-3002 : Step(85): len = 63676.3, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000399326
PHY-3002 : Step(86): len = 63569.3, overlap = 9
PHY-3002 : Step(87): len = 63575.4, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006594s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060908s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00205594
PHY-3002 : Step(88): len = 66429, overlap = 4.9375
PHY-3002 : Step(89): len = 65155.7, overlap = 4.875
PHY-3002 : Step(90): len = 64167, overlap = 4.8125
PHY-3002 : Step(91): len = 63038.7, overlap = 4.84375
PHY-3002 : Step(92): len = 61562, overlap = 5.1875
PHY-3002 : Step(93): len = 60225.8, overlap = 4.75
PHY-3002 : Step(94): len = 59041.2, overlap = 3.875
PHY-3002 : Step(95): len = 57752.1, overlap = 4.90625
PHY-3002 : Step(96): len = 56006.2, overlap = 5.46875
PHY-3002 : Step(97): len = 55111.7, overlap = 6.09375
PHY-3002 : Step(98): len = 54655.4, overlap = 5.46875
PHY-3002 : Step(99): len = 54033.4, overlap = 4.03125
PHY-3002 : Step(100): len = 53504.9, overlap = 4.90625
PHY-3002 : Step(101): len = 53166.6, overlap = 6.46875
PHY-3002 : Step(102): len = 52416.4, overlap = 6.21875
PHY-3002 : Step(103): len = 51512.3, overlap = 5.875
PHY-3002 : Step(104): len = 51049.9, overlap = 9
PHY-3002 : Step(105): len = 50680.5, overlap = 9.75
PHY-3002 : Step(106): len = 49853.6, overlap = 10.8438
PHY-3002 : Step(107): len = 49055.1, overlap = 10.9688
PHY-3002 : Step(108): len = 48337.7, overlap = 9.96875
PHY-3002 : Step(109): len = 47576.1, overlap = 8.15625
PHY-3002 : Step(110): len = 47277.4, overlap = 8.4375
PHY-3002 : Step(111): len = 46802.9, overlap = 10.4062
PHY-3002 : Step(112): len = 46160.7, overlap = 12.0625
PHY-3002 : Step(113): len = 45300.6, overlap = 13
PHY-3002 : Step(114): len = 45219.5, overlap = 13.2812
PHY-3002 : Step(115): len = 45087.1, overlap = 13.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00411188
PHY-3002 : Step(116): len = 44950.5, overlap = 13.5312
PHY-3002 : Step(117): len = 44863.1, overlap = 13.9062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064078s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.91912e-05
PHY-3002 : Step(118): len = 45670.9, overlap = 60.0938
PHY-3002 : Step(119): len = 46088.9, overlap = 59.4688
PHY-3002 : Step(120): len = 45923.8, overlap = 58.25
PHY-3002 : Step(121): len = 45973.2, overlap = 58.7188
PHY-3002 : Step(122): len = 45956.6, overlap = 54.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000158382
PHY-3002 : Step(123): len = 46307.1, overlap = 53.3438
PHY-3002 : Step(124): len = 46445.9, overlap = 53.0938
PHY-3002 : Step(125): len = 47298.3, overlap = 47
PHY-3002 : Step(126): len = 48165, overlap = 45.5625
PHY-3002 : Step(127): len = 48940.1, overlap = 42.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000316765
PHY-3002 : Step(128): len = 48826.9, overlap = 40.8125
PHY-3002 : Step(129): len = 48854.1, overlap = 40.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7804, tnet num: 2199, tinst num: 1629, tnode num: 11044, tedge num: 13195.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 94.59 peak overflow 3.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2201.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51352, over cnt = 235(0%), over = 1025, worst = 25
PHY-1001 : End global iterations;  0.074652s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.7%)

PHY-1001 : Congestion index: top1 = 43.06, top5 = 24.92, top10 = 15.72, top15 = 11.03.
PHY-1001 : End incremental global routing;  0.122955s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (101.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067428s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.7%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1590 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1631 instances, 378 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 48928
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7810, tnet num: 2201, tinst num: 1631, tnode num: 11050, tedge num: 13203.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.315477s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (94.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(130): len = 48902.1, overlap = 2.9375
PHY-3002 : Step(131): len = 48900.6, overlap = 2.9375
PHY-3002 : Step(132): len = 48900.6, overlap = 2.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059887s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(133): len = 48900.6, overlap = 40.375
PHY-3002 : Step(134): len = 48900.6, overlap = 40.375
PHY-3001 : Final: Len = 48900.6, Over = 40.375
PHY-3001 : End incremental placement;  0.458047s wall, 0.421875s user + 0.109375s system = 0.531250s CPU (116.0%)

OPT-1001 : Total overflow 94.78 peak overflow 3.78
OPT-1001 : End high-fanout net optimization;  0.685339s wall, 0.640625s user + 0.109375s system = 0.750000s CPU (109.4%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1682/2203.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51424, over cnt = 234(0%), over = 1024, worst = 25
PHY-1002 : len = 58656, over cnt = 168(0%), over = 467, worst = 18
PHY-1002 : len = 64088, over cnt = 43(0%), over = 70, worst = 6
PHY-1002 : len = 64904, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 65112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.080621s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (135.7%)

PHY-1001 : Congestion index: top1 = 39.22, top5 = 25.82, top10 = 18.28, top15 = 13.31.
OPT-1001 : End congestion update;  0.122803s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (127.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056709s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.182271s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (120.0%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 219.
OPT-1001 : End physical optimization;  1.147785s wall, 1.109375s user + 0.125000s system = 1.234375s CPU (107.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 378 LUT to BLE ...
SYN-4008 : Packed 378 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 99 SEQ with LUT/SLICE
SYN-4006 : 115 single LUT's are left
SYN-4006 : 704 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1082/1410 primitive instances ...
PHY-3001 : End packing;  0.051170s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 842 instances
RUN-1001 : 396 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2035 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1478 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 840 instances, 793 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 48713.4, Over = 70.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6578, tnet num: 2033, tinst num: 840, tnode num: 8916, tedge num: 11558.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.309104s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.8326e-05
PHY-3002 : Step(135): len = 48346, overlap = 71.5
PHY-3002 : Step(136): len = 47875.5, overlap = 71.75
PHY-3002 : Step(137): len = 47865.3, overlap = 70.75
PHY-3002 : Step(138): len = 47469.7, overlap = 69.75
PHY-3002 : Step(139): len = 47305.5, overlap = 69
PHY-3002 : Step(140): len = 47308.1, overlap = 68.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.66521e-05
PHY-3002 : Step(141): len = 47574.9, overlap = 67.5
PHY-3002 : Step(142): len = 48340.8, overlap = 65.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.09609e-05
PHY-3002 : Step(143): len = 48718.5, overlap = 66
PHY-3002 : Step(144): len = 50005.2, overlap = 59.75
PHY-3002 : Step(145): len = 51492.5, overlap = 55.75
PHY-3002 : Step(146): len = 52042.8, overlap = 55.5
PHY-3002 : Step(147): len = 52040.5, overlap = 56
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.093500s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (167.1%)

PHY-3001 : Trial Legalized: Len = 64990.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051589s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000465162
PHY-3002 : Step(148): len = 62203.1, overlap = 6.25
PHY-3002 : Step(149): len = 59864.7, overlap = 12.25
PHY-3002 : Step(150): len = 58528.2, overlap = 14.75
PHY-3002 : Step(151): len = 57801.2, overlap = 17
PHY-3002 : Step(152): len = 57187, overlap = 18.75
PHY-3002 : Step(153): len = 56817.4, overlap = 20.25
PHY-3002 : Step(154): len = 56588.8, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000930324
PHY-3002 : Step(155): len = 57092, overlap = 19.25
PHY-3002 : Step(156): len = 57271.5, overlap = 19.25
PHY-3002 : Step(157): len = 57341.5, overlap = 18.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00186065
PHY-3002 : Step(158): len = 57465.9, overlap = 18
PHY-3002 : Step(159): len = 57481.2, overlap = 17.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004728s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62443.4, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005636s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 1, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 62561.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6578, tnet num: 2033, tinst num: 840, tnode num: 8916, tedge num: 11558.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 29/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68688, over cnt = 141(0%), over = 194, worst = 4
PHY-1002 : len = 69376, over cnt = 73(0%), over = 89, worst = 4
PHY-1002 : len = 70240, over cnt = 5(0%), over = 7, worst = 2
PHY-1002 : len = 70336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.115356s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (121.9%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.28, top10 = 17.47, top15 = 13.86.
PHY-1001 : End incremental global routing;  0.172568s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (117.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059012s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.261539s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (107.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005686s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (274.8%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.28, top10 = 17.47, top15 = 13.86.
OPT-1001 : End congestion update;  0.050745s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051430s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 802 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 840 instances, 793 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62602.4, Over = 0
PHY-3001 : End spreading;  0.005267s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62602.4, Over = 0
PHY-3001 : End incremental legalization;  0.034689s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.1%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149763s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (104.3%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050610s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1771/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70400, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.018246s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.6%)

PHY-1001 : Congestion index: top1 = 31.68, top5 = 22.30, top10 = 17.47, top15 = 13.87.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051221s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.275862
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.875937s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (105.2%)

RUN-1003 : finish command "place" in  5.801157s wall, 8.984375s user + 2.687500s system = 11.671875s CPU (201.2%)

RUN-1004 : used memory is 206 MB, reserved memory is 171 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 842 instances
RUN-1001 : 396 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2035 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1478 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6578, tnet num: 2033, tinst num: 840, tnode num: 8916, tedge num: 11558.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68256, over cnt = 141(0%), over = 195, worst = 4
PHY-1002 : len = 68920, over cnt = 71(0%), over = 83, worst = 4
PHY-1002 : len = 69808, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 69856, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121386s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (128.7%)

PHY-1001 : Congestion index: top1 = 31.49, top5 = 22.07, top10 = 17.29, top15 = 13.72.
PHY-1001 : End global routing;  0.170974s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (118.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 205, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 469, peak = 499.
PHY-1001 : End build detailed router design. 3.267797s wall, 3.203125s user + 0.078125s system = 3.281250s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34960, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.303480s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 532, reserve = 503, peak = 533.
PHY-1001 : End phase 1; 1.310194s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180904, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End initial routed; 1.030033s wall, 2.250000s user + 0.093750s system = 2.343750s CPU (227.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1802(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.360   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.368518s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.8%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.398633s wall, 2.625000s user + 0.093750s system = 2.718750s CPU (194.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180904, over cnt = 35(0%), over = 35, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014343s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180872, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.034440s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (136.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180928, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.030153s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1802(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.360   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.373895s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.178725s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.2%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End phase 3; 0.753809s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 180928
PHY-1001 : Current memory(MB): used = 549, reserve = 519, peak = 549.
PHY-1001 : End export database. 0.009674s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (161.5%)

PHY-1001 : End detail routing;  6.927047s wall, 8.015625s user + 0.187500s system = 8.203125s CPU (118.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6578, tnet num: 2033, tinst num: 840, tnode num: 8916, tedge num: 11558.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.831412s wall, 8.953125s user + 0.187500s system = 9.140625s CPU (116.7%)

RUN-1004 : used memory is 525 MB, reserved memory is 497 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      825   out of  19600    4.21%
#reg                     1074   out of  19600    5.48%
#le                      1529
  #lut only               455   out of   1529   29.76%
  #reg only               704   out of   1529   46.04%
  #lut&reg                370   out of   1529   24.20%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    38
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1529   |604     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1130   |301     |128     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |37     |31      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |526    |107     |53      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |13      |0       |26      |0       |0       |
|    integ                   |Integration                                      |140    |24      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |88     |33      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |320    |92      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |101    |91      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |16     |14      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |49     |49      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1442  
    #2          2       315   
    #3          3       103   
    #4          4        23   
    #5        5-10       79   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6578, tnet num: 2033, tinst num: 840, tnode num: 8916, tedge num: 11558.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 840
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2035, pip num: 14676
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1319 valid insts, and 38905 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.134976s wall, 18.093750s user + 0.093750s system = 18.187500s CPU (580.1%)

RUN-1004 : used memory is 548 MB, reserved memory is 517 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_175016.log"
