============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Sep  8 10:52:31 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1632 instances
RUN-0007 : 368 luts, 1009 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2172 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1615 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1630 instances, 368 luts, 1009 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7752, tnet num: 2170, tinst num: 1630, tnode num: 11014, tedge num: 13112.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.286476s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 635049
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1630.
PHY-3001 : End clustering;  0.000047s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 530985, overlap = 20.25
PHY-3002 : Step(2): len = 491286, overlap = 20.25
PHY-3002 : Step(3): len = 404020, overlap = 18
PHY-3002 : Step(4): len = 338779, overlap = 13.5
PHY-3002 : Step(5): len = 306561, overlap = 15.75
PHY-3002 : Step(6): len = 292288, overlap = 18
PHY-3002 : Step(7): len = 283529, overlap = 20.25
PHY-3002 : Step(8): len = 274739, overlap = 20.25
PHY-3002 : Step(9): len = 269130, overlap = 20.25
PHY-3002 : Step(10): len = 260225, overlap = 20.25
PHY-3002 : Step(11): len = 253326, overlap = 20.25
PHY-3002 : Step(12): len = 247425, overlap = 20.25
PHY-3002 : Step(13): len = 241783, overlap = 20.25
PHY-3002 : Step(14): len = 233807, overlap = 20.25
PHY-3002 : Step(15): len = 229389, overlap = 20.25
PHY-3002 : Step(16): len = 223902, overlap = 20.25
PHY-3002 : Step(17): len = 218566, overlap = 20.25
PHY-3002 : Step(18): len = 213829, overlap = 20.25
PHY-3002 : Step(19): len = 209728, overlap = 20.25
PHY-3002 : Step(20): len = 202406, overlap = 20.25
PHY-3002 : Step(21): len = 197075, overlap = 20.25
PHY-3002 : Step(22): len = 194338, overlap = 20.25
PHY-3002 : Step(23): len = 189756, overlap = 20.25
PHY-3002 : Step(24): len = 174339, overlap = 20.25
PHY-3002 : Step(25): len = 169706, overlap = 20.25
PHY-3002 : Step(26): len = 168016, overlap = 20.25
PHY-3002 : Step(27): len = 144557, overlap = 18
PHY-3002 : Step(28): len = 137421, overlap = 18
PHY-3002 : Step(29): len = 136414, overlap = 18
PHY-3002 : Step(30): len = 127390, overlap = 18
PHY-3002 : Step(31): len = 121832, overlap = 18
PHY-3002 : Step(32): len = 120347, overlap = 18
PHY-3002 : Step(33): len = 117605, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.68282e-05
PHY-3002 : Step(34): len = 120063, overlap = 18
PHY-3002 : Step(35): len = 118851, overlap = 15.75
PHY-3002 : Step(36): len = 116993, overlap = 15.75
PHY-3002 : Step(37): len = 115037, overlap = 15.75
PHY-3002 : Step(38): len = 109491, overlap = 15.75
PHY-3002 : Step(39): len = 106515, overlap = 13.5
PHY-3002 : Step(40): len = 103392, overlap = 13.75
PHY-3002 : Step(41): len = 103036, overlap = 11.5
PHY-3002 : Step(42): len = 100538, overlap = 14
PHY-3002 : Step(43): len = 98148.7, overlap = 14
PHY-3002 : Step(44): len = 95928.9, overlap = 11.25
PHY-3002 : Step(45): len = 94018.1, overlap = 11.25
PHY-3002 : Step(46): len = 93371.6, overlap = 13.5
PHY-3002 : Step(47): len = 92301.3, overlap = 13.5
PHY-3002 : Step(48): len = 86744.5, overlap = 11.25
PHY-3002 : Step(49): len = 83551.7, overlap = 9
PHY-3002 : Step(50): len = 82198.5, overlap = 9.0625
PHY-3002 : Step(51): len = 81638.2, overlap = 9
PHY-3002 : Step(52): len = 80712.4, overlap = 11.5
PHY-3002 : Step(53): len = 80162, overlap = 11.5625
PHY-3002 : Step(54): len = 79538, overlap = 11.5625
PHY-3002 : Step(55): len = 77937.8, overlap = 9.3125
PHY-3002 : Step(56): len = 76015.3, overlap = 9.625
PHY-3002 : Step(57): len = 73788.5, overlap = 12.1875
PHY-3002 : Step(58): len = 72270.9, overlap = 12.375
PHY-3002 : Step(59): len = 72106.1, overlap = 12.375
PHY-3002 : Step(60): len = 71566.8, overlap = 16.875
PHY-3002 : Step(61): len = 70545.9, overlap = 14.625
PHY-3002 : Step(62): len = 69774.9, overlap = 10.125
PHY-3002 : Step(63): len = 68801.4, overlap = 12.375
PHY-3002 : Step(64): len = 66831.9, overlap = 9.9375
PHY-3002 : Step(65): len = 65767.2, overlap = 12.25
PHY-3002 : Step(66): len = 65513.8, overlap = 10.25
PHY-3002 : Step(67): len = 64773.3, overlap = 12.5
PHY-3002 : Step(68): len = 64063.1, overlap = 12.5
PHY-3002 : Step(69): len = 63838.5, overlap = 14.75
PHY-3002 : Step(70): len = 63767.8, overlap = 14.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000153656
PHY-3002 : Step(71): len = 63774.8, overlap = 14.6875
PHY-3002 : Step(72): len = 63845.8, overlap = 12.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000307313
PHY-3002 : Step(73): len = 64034, overlap = 12.125
PHY-3002 : Step(74): len = 64066.3, overlap = 12.0625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005368s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (291.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061754s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000994021
PHY-3002 : Step(75): len = 67376.1, overlap = 4
PHY-3002 : Step(76): len = 65678.8, overlap = 3.25
PHY-3002 : Step(77): len = 64088.6, overlap = 3.1875
PHY-3002 : Step(78): len = 63206.3, overlap = 3.1875
PHY-3002 : Step(79): len = 62340.7, overlap = 3.5625
PHY-3002 : Step(80): len = 60632.2, overlap = 4.0625
PHY-3002 : Step(81): len = 59741, overlap = 4.125
PHY-3002 : Step(82): len = 58960.9, overlap = 4.125
PHY-3002 : Step(83): len = 57963.6, overlap = 4.25
PHY-3002 : Step(84): len = 56348.6, overlap = 4
PHY-3002 : Step(85): len = 54735.8, overlap = 5.8125
PHY-3002 : Step(86): len = 52755.8, overlap = 11.3125
PHY-3002 : Step(87): len = 52040.8, overlap = 11.25
PHY-3002 : Step(88): len = 51471.9, overlap = 10.1875
PHY-3002 : Step(89): len = 51048.1, overlap = 10.375
PHY-3002 : Step(90): len = 50634.8, overlap = 11
PHY-3002 : Step(91): len = 50154.3, overlap = 11.25
PHY-3002 : Step(92): len = 49607.6, overlap = 11.25
PHY-3002 : Step(93): len = 49232.5, overlap = 11.6875
PHY-3002 : Step(94): len = 48649, overlap = 13.1875
PHY-3002 : Step(95): len = 48095.2, overlap = 15.6562
PHY-3002 : Step(96): len = 47276.8, overlap = 14.9688
PHY-3002 : Step(97): len = 46679.2, overlap = 15.5625
PHY-3002 : Step(98): len = 46375.7, overlap = 15.4688
PHY-3002 : Step(99): len = 46232.9, overlap = 15.6562
PHY-3002 : Step(100): len = 46259, overlap = 16.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00198804
PHY-3002 : Step(101): len = 46048.4, overlap = 16.25
PHY-3002 : Step(102): len = 45894.9, overlap = 16.4062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00397608
PHY-3002 : Step(103): len = 45910.6, overlap = 16.2812
PHY-3002 : Step(104): len = 45910.6, overlap = 16.2812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.074935s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (83.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.5229e-05
PHY-3002 : Step(105): len = 46351.6, overlap = 69.125
PHY-3002 : Step(106): len = 46341.5, overlap = 69.1562
PHY-3002 : Step(107): len = 46844.6, overlap = 57.2812
PHY-3002 : Step(108): len = 47231.3, overlap = 56.625
PHY-3002 : Step(109): len = 47522.3, overlap = 51.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000130458
PHY-3002 : Step(110): len = 47425, overlap = 51.2188
PHY-3002 : Step(111): len = 47848.1, overlap = 50.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000260916
PHY-3002 : Step(112): len = 47726.1, overlap = 48
PHY-3002 : Step(113): len = 49327.6, overlap = 45.0312
PHY-3002 : Step(114): len = 50036, overlap = 38.8125
PHY-3002 : Step(115): len = 50036.3, overlap = 37.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7752, tnet num: 2170, tinst num: 1630, tnode num: 11014, tedge num: 13112.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 93.09 peak overflow 3.69
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2172.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52736, over cnt = 233(0%), over = 1016, worst = 23
PHY-1001 : End global iterations;  0.069011s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.2%)

PHY-1001 : Congestion index: top1 = 43.81, top5 = 24.76, top10 = 15.38, top15 = 10.84.
PHY-1001 : End incremental global routing;  0.119437s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (91.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067965s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.217227s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 176, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1643/2172.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52736, over cnt = 233(0%), over = 1016, worst = 23
PHY-1002 : len = 60264, over cnt = 181(0%), over = 394, worst = 14
PHY-1002 : len = 63704, over cnt = 50(0%), over = 99, worst = 9
PHY-1002 : len = 65424, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 65848, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.093635s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 38.90, top5 = 25.65, top10 = 17.81, top15 = 12.93.
OPT-1001 : End congestion update;  0.135733s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059604s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.197983s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.6%)

OPT-1001 : Current memory(MB): used = 217, reserve = 179, peak = 217.
OPT-1001 : End physical optimization;  0.696369s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (116.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 108 SEQ with LUT/SLICE
SYN-4006 : 96 single LUT's are left
SYN-4006 : 719 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1087/1400 primitive instances ...
PHY-3001 : End packing;  0.049604s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (126.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 827 instances
RUN-1001 : 389 mslices, 389 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2004 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1449 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 825 instances, 778 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50057, Over = 65
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2002, tinst num: 825, tnode num: 8828, tedge num: 11430.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.309240s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.3489e-05
PHY-3002 : Step(116): len = 49594.3, overlap = 66.75
PHY-3002 : Step(117): len = 49432, overlap = 67.5
PHY-3002 : Step(118): len = 48900.3, overlap = 70
PHY-3002 : Step(119): len = 48831.1, overlap = 69
PHY-3002 : Step(120): len = 48671.8, overlap = 70.5
PHY-3002 : Step(121): len = 48852, overlap = 69.25
PHY-3002 : Step(122): len = 48705.7, overlap = 68.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.69781e-05
PHY-3002 : Step(123): len = 49006.8, overlap = 65.75
PHY-3002 : Step(124): len = 49162.8, overlap = 65.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.76859e-05
PHY-3002 : Step(125): len = 49535.1, overlap = 63.25
PHY-3002 : Step(126): len = 50219.7, overlap = 60
PHY-3002 : Step(127): len = 51591.7, overlap = 50.75
PHY-3002 : Step(128): len = 52191.1, overlap = 48.25
PHY-3002 : Step(129): len = 52469.7, overlap = 46.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.073675s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (233.3%)

PHY-3001 : Trial Legalized: Len = 66748.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055013s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00047157
PHY-3002 : Step(130): len = 64563, overlap = 5
PHY-3002 : Step(131): len = 62425.6, overlap = 9.75
PHY-3002 : Step(132): len = 60879.6, overlap = 15.25
PHY-3002 : Step(133): len = 59914.1, overlap = 19.25
PHY-3002 : Step(134): len = 59323.5, overlap = 20
PHY-3002 : Step(135): len = 58708.3, overlap = 23.25
PHY-3002 : Step(136): len = 58398.7, overlap = 24.25
PHY-3002 : Step(137): len = 58094.2, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000943141
PHY-3002 : Step(138): len = 58441.1, overlap = 24.5
PHY-3002 : Step(139): len = 58637.7, overlap = 23.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00188628
PHY-3002 : Step(140): len = 58818.6, overlap = 23.25
PHY-3002 : Step(141): len = 58979.6, overlap = 23.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005468s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (285.7%)

PHY-3001 : Legalized: Len = 63515.5, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005547s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 3, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 63645.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2002, tinst num: 825, tnode num: 8828, tedge num: 11430.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 59/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69616, over cnt = 153(0%), over = 218, worst = 4
PHY-1002 : len = 70416, over cnt = 75(0%), over = 92, worst = 3
PHY-1002 : len = 71360, over cnt = 15(0%), over = 15, worst = 1
PHY-1002 : len = 71576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.113543s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (151.4%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.30, top10 = 17.92, top15 = 13.97.
PHY-1001 : End incremental global routing;  0.165286s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (132.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063567s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (98.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.258315s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (121.0%)

OPT-1001 : Current memory(MB): used = 220, reserve = 183, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1779/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006605s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 23.30, top10 = 17.92, top15 = 13.97.
OPT-1001 : End congestion update;  0.055020s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048950s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 787 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 825 instances, 778 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63558, Over = 0
PHY-3001 : End spreading;  0.005151s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (303.3%)

PHY-3001 : Final: Len = 63558, Over = 0
PHY-3001 : End incremental legalization;  0.035161s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.152288s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (102.6%)

OPT-1001 : Current memory(MB): used = 224, reserve = 187, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047545s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1772/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71472, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.016575s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.20, top5 = 23.31, top10 = 17.92, top15 = 13.97.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049865s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.867530s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (104.5%)

RUN-1003 : finish command "place" in  5.021000s wall, 7.765625s user + 2.640625s system = 10.406250s CPU (207.3%)

RUN-1004 : used memory is 201 MB, reserved memory is 163 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 827 instances
RUN-1001 : 389 mslices, 389 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2004 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1449 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2002, tinst num: 825, tnode num: 8828, tedge num: 11430.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 389 mslices, 389 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69032, over cnt = 157(0%), over = 226, worst = 6
PHY-1002 : len = 69832, over cnt = 74(0%), over = 90, worst = 3
PHY-1002 : len = 70672, over cnt = 18(0%), over = 21, worst = 3
PHY-1002 : len = 71016, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118672s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (158.0%)

PHY-1001 : Congestion index: top1 = 32.07, top5 = 23.23, top10 = 17.86, top15 = 13.91.
PHY-1001 : End global routing;  0.167107s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (140.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 202, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 496, reserve = 463, peak = 496.
PHY-1001 : End build detailed router design. 3.185008s wall, 3.125000s user + 0.046875s system = 3.171875s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34640, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.277509s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 528, reserve = 496, peak = 528.
PHY-1001 : End phase 1; 1.283315s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (101.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183912, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 497, peak = 530.
PHY-1001 : End initial routed; 1.033547s wall, 1.968750s user + 0.078125s system = 2.046875s CPU (198.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1785(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.739   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.278   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.369162s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (97.3%)

PHY-1001 : Current memory(MB): used = 533, reserve = 499, peak = 533.
PHY-1001 : End phase 2; 1.402794s wall, 2.328125s user + 0.078125s system = 2.406250s CPU (171.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183912, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014781s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 183976, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029217s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (160.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184032, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.026548s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (58.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 184048, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.027605s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1785(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.739   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.278   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366757s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.172213s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 548, reserve = 515, peak = 548.
PHY-1001 : End phase 3; 0.761626s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.5%)

PHY-1003 : Routed, final wirelength = 184048
PHY-1001 : Current memory(MB): used = 548, reserve = 515, peak = 548.
PHY-1001 : End export database. 0.009506s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (164.4%)

PHY-1001 : End detail routing;  6.825038s wall, 7.687500s user + 0.156250s system = 7.843750s CPU (114.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6494, tnet num: 2002, tinst num: 825, tnode num: 8828, tedge num: 11430.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[27] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.addra[9] slack -28ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.dia[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2022, tinst num: 845, tnode num: 8868, tedge num: 11470.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.074854s wall, 3.125000s user + 0.093750s system = 3.218750s CPU (104.7%)

RUN-1003 : finish command "route" in  10.411567s wall, 11.375000s user + 0.265625s system = 11.640625s CPU (111.8%)

RUN-1004 : used memory is 505 MB, reserved memory is 478 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      825   out of  19600    4.21%
#reg                     1074   out of  19600    5.48%
#le                      1544
  #lut only               470   out of   1544   30.44%
  #reg only               719   out of   1544   46.57%
  #lut&reg                355   out of   1544   22.99%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         469
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    39
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1544   |619     |206     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1172   |336     |122     |925     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |38     |32      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |562    |148     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |62      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |0       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |12      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |16      |0       |30      |0       |0       |
|    integ                   |Integration                                      |137    |24      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |102    |28      |15      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |88      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |21     |16      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |101    |89      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |42     |42      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |206    |161     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1433  
    #2          2       320   
    #3          3       106   
    #4          4        12   
    #5        5-10       80   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6534, tnet num: 2022, tinst num: 845, tnode num: 8868, tedge num: 11470.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2022 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 845
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2024, pip num: 14915
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1305 valid insts, and 39294 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.062409s wall, 18.046875s user + 0.046875s system = 18.093750s CPU (590.8%)

RUN-1004 : used memory is 522 MB, reserved memory is 490 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230908_105231.log"
