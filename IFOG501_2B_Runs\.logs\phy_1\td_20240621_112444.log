============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 11:24:44 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 25 trigger nets, 25 data nets.
KIT-1004 : Chipwatcher code = 1001011011010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2118/10 useful/useless nets, 1336/5 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 1891/18 useful/useless nets, 1666/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 282 better
SYN-1014 : Optimize round 2
SYN-1032 : 1696/15 useful/useless nets, 1471/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1720/156 useful/useless nets, 1517/30 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 207 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 2088/5 useful/useless nets, 1885/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7850, tnet num: 2088, tinst num: 1884, tnode num: 9818, tedge num: 12042.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2088 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 175 (3.57), #lev = 7 (1.77)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 174 (3.59), #lev = 6 (1.89)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 395 instances into 174 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 280 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (180 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1390 instances
RUN-0007 : 594 luts, 595 seqs, 102 mslices, 63 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1600 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 929 nets have 2 pins
RUN-1001 : 481 nets have [3 - 5] pins
RUN-1001 : 100 nets have [6 - 10] pins
RUN-1001 : 53 nets have [11 - 20] pins
RUN-1001 : 26 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     145     
RUN-1001 :   No   |  No   |  Yes  |     101     
RUN-1001 :   No   |  Yes  |  No   |     84      
RUN-1001 :   Yes  |  No   |  No   |     89      
RUN-1001 :   Yes  |  No   |  Yes  |     176     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 17
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1388 instances, 594 luts, 595 seqs, 165 slices, 25 macros(165 instances: 102 mslices 63 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6798, tnet num: 1598, tinst num: 1388, tnode num: 8769, tedge num: 11158.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1598 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.123092s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (101.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 347873
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1388.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 267470, overlap = 51.75
PHY-3002 : Step(2): len = 212097, overlap = 51.75
PHY-3002 : Step(3): len = 177071, overlap = 51.75
PHY-3002 : Step(4): len = 154388, overlap = 51.75
PHY-3002 : Step(5): len = 132356, overlap = 51.75
PHY-3002 : Step(6): len = 119702, overlap = 51.75
PHY-3002 : Step(7): len = 108470, overlap = 51.75
PHY-3002 : Step(8): len = 93268.4, overlap = 51.75
PHY-3002 : Step(9): len = 86611.6, overlap = 52
PHY-3002 : Step(10): len = 78514.6, overlap = 52.125
PHY-3002 : Step(11): len = 72149.8, overlap = 52.4375
PHY-3002 : Step(12): len = 67475.9, overlap = 52.4375
PHY-3002 : Step(13): len = 63675.7, overlap = 52.875
PHY-3002 : Step(14): len = 60270.7, overlap = 53.5625
PHY-3002 : Step(15): len = 56538.1, overlap = 54.125
PHY-3002 : Step(16): len = 53704.8, overlap = 54.5312
PHY-3002 : Step(17): len = 50347.7, overlap = 54.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.07075e-05
PHY-3002 : Step(18): len = 50418.8, overlap = 54.5
PHY-3002 : Step(19): len = 50983.6, overlap = 54.3125
PHY-3002 : Step(20): len = 50610.8, overlap = 45.9062
PHY-3002 : Step(21): len = 50396.1, overlap = 52.3438
PHY-3002 : Step(22): len = 51094.6, overlap = 51.5
PHY-3002 : Step(23): len = 51018.6, overlap = 51.5
PHY-3002 : Step(24): len = 48882.2, overlap = 47
PHY-3002 : Step(25): len = 48661.8, overlap = 42.5
PHY-3002 : Step(26): len = 47395.3, overlap = 35.625
PHY-3002 : Step(27): len = 45795.1, overlap = 40.25
PHY-3002 : Step(28): len = 45272.2, overlap = 38
PHY-3002 : Step(29): len = 44462.3, overlap = 33.5
PHY-3002 : Step(30): len = 44062.7, overlap = 33.5
PHY-3002 : Step(31): len = 43847.7, overlap = 33.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.14149e-05
PHY-3002 : Step(32): len = 43857.1, overlap = 33.5
PHY-3002 : Step(33): len = 43849, overlap = 33.5
PHY-3002 : Step(34): len = 43821.9, overlap = 31.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 4.28298e-05
PHY-3002 : Step(35): len = 43807.1, overlap = 31.25
PHY-3002 : Step(36): len = 43713.2, overlap = 35.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007181s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1598 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.041163s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (75.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(37): len = 44882, overlap = 14.7188
PHY-3002 : Step(38): len = 44973.4, overlap = 14.6875
PHY-3002 : Step(39): len = 44764.2, overlap = 14.8438
PHY-3002 : Step(40): len = 44622.4, overlap = 14.5938
PHY-3002 : Step(41): len = 44524, overlap = 11.5625
PHY-3002 : Step(42): len = 43804.3, overlap = 10.375
PHY-3002 : Step(43): len = 43243.5, overlap = 9.34375
PHY-3002 : Step(44): len = 42540.9, overlap = 6.96875
PHY-3002 : Step(45): len = 41833.6, overlap = 6.8125
PHY-3002 : Step(46): len = 40937.5, overlap = 6.1875
PHY-3002 : Step(47): len = 40389.7, overlap = 6.21875
PHY-3002 : Step(48): len = 39869.2, overlap = 5.78125
PHY-3002 : Step(49): len = 39351.2, overlap = 5.46875
PHY-3002 : Step(50): len = 38787.4, overlap = 7.0625
PHY-3002 : Step(51): len = 38075.1, overlap = 13.8438
PHY-3002 : Step(52): len = 37718.8, overlap = 18.0938
PHY-3002 : Step(53): len = 37021.2, overlap = 18.4688
PHY-3002 : Step(54): len = 36691.4, overlap = 16.8125
PHY-3002 : Step(55): len = 36107.7, overlap = 17.75
PHY-3002 : Step(56): len = 35661.6, overlap = 15.8438
PHY-3002 : Step(57): len = 35207.5, overlap = 11.125
PHY-3002 : Step(58): len = 34810.4, overlap = 19.4375
PHY-3002 : Step(59): len = 34453.9, overlap = 28.1875
PHY-3002 : Step(60): len = 34023.6, overlap = 32.5938
PHY-3002 : Step(61): len = 33848.3, overlap = 37.0625
PHY-3002 : Step(62): len = 33755.5, overlap = 37.75
PHY-3002 : Step(63): len = 33402.7, overlap = 28.0312
PHY-3002 : Step(64): len = 33286.9, overlap = 22.0312
PHY-3002 : Step(65): len = 33088.4, overlap = 21.3438
PHY-3002 : Step(66): len = 33077.9, overlap = 16.5938
PHY-3002 : Step(67): len = 33130.6, overlap = 9.9375
PHY-3002 : Step(68): len = 33059.5, overlap = 14.7188
PHY-3002 : Step(69): len = 32900.4, overlap = 16.2188
PHY-3002 : Step(70): len = 32848, overlap = 16.875
PHY-3002 : Step(71): len = 33013.6, overlap = 16.1875
PHY-3002 : Step(72): len = 33046.1, overlap = 12.2812
PHY-3002 : Step(73): len = 32889, overlap = 8.625
PHY-3002 : Step(74): len = 32762.3, overlap = 8.875
PHY-3002 : Step(75): len = 32779.9, overlap = 11.8438
PHY-3002 : Step(76): len = 32467.4, overlap = 15.25
PHY-3002 : Step(77): len = 32526.7, overlap = 16.5
PHY-3002 : Step(78): len = 32269.2, overlap = 15.7812
PHY-3002 : Step(79): len = 32113.6, overlap = 15.625
PHY-3002 : Step(80): len = 31938.7, overlap = 15.125
PHY-3002 : Step(81): len = 31847.8, overlap = 14.8125
PHY-3002 : Step(82): len = 31659.4, overlap = 14.625
PHY-3002 : Step(83): len = 31683.6, overlap = 14.8125
PHY-3002 : Step(84): len = 31633.5, overlap = 16.6875
PHY-3002 : Step(85): len = 31444.3, overlap = 14.9375
PHY-3002 : Step(86): len = 31362.8, overlap = 14.0312
PHY-3002 : Step(87): len = 31158.9, overlap = 14.1875
PHY-3002 : Step(88): len = 31095.5, overlap = 15.0938
PHY-3002 : Step(89): len = 31019.3, overlap = 14.25
PHY-3002 : Step(90): len = 31035.1, overlap = 13.2188
PHY-3002 : Step(91): len = 30948.3, overlap = 13.0938
PHY-3002 : Step(92): len = 30878.2, overlap = 14.0625
PHY-3002 : Step(93): len = 30832.9, overlap = 15.625
PHY-3002 : Step(94): len = 30845.2, overlap = 17.9375
PHY-3002 : Step(95): len = 30606.5, overlap = 21.9688
PHY-3002 : Step(96): len = 30526, overlap = 22.6562
PHY-3002 : Step(97): len = 30569, overlap = 22.4375
PHY-3002 : Step(98): len = 30694, overlap = 22.9062
PHY-3002 : Step(99): len = 30731.8, overlap = 23.9062
PHY-3002 : Step(100): len = 30731.3, overlap = 24.7812
PHY-3002 : Step(101): len = 30777.2, overlap = 25.9375
PHY-3002 : Step(102): len = 30886.7, overlap = 27.6875
PHY-3002 : Step(103): len = 30801.5, overlap = 29.25
PHY-3002 : Step(104): len = 30846, overlap = 29.9688
PHY-3002 : Step(105): len = 30840.6, overlap = 30.3438
PHY-3002 : Step(106): len = 30685.7, overlap = 30.5625
PHY-3002 : Step(107): len = 30694.3, overlap = 30.3438
PHY-3002 : Step(108): len = 30665.8, overlap = 29.5938
PHY-3002 : Step(109): len = 30657.4, overlap = 28.6875
PHY-3002 : Step(110): len = 30635.8, overlap = 27.4062
PHY-3002 : Step(111): len = 30717.5, overlap = 27.25
PHY-3002 : Step(112): len = 30823.1, overlap = 26.5
PHY-3002 : Step(113): len = 30927.9, overlap = 25.5
PHY-3002 : Step(114): len = 31027.8, overlap = 23.7812
PHY-3002 : Step(115): len = 30847, overlap = 23.5312
PHY-3002 : Step(116): len = 30812.9, overlap = 23.4062
PHY-3002 : Step(117): len = 30739.5, overlap = 23.0938
PHY-3002 : Step(118): len = 30674.6, overlap = 22.9688
PHY-3002 : Step(119): len = 30674.6, overlap = 22.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1598 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038872s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (120.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.69802e-05
PHY-3002 : Step(120): len = 31711.3, overlap = 67.3125
PHY-3002 : Step(121): len = 32377.1, overlap = 62.5
PHY-3002 : Step(122): len = 33371, overlap = 57.625
PHY-3002 : Step(123): len = 34715.8, overlap = 53.2188
PHY-3002 : Step(124): len = 36192.1, overlap = 50.875
PHY-3002 : Step(125): len = 35466.9, overlap = 52
PHY-3002 : Step(126): len = 35075.1, overlap = 50.875
PHY-3002 : Step(127): len = 34658.8, overlap = 49.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00013396
PHY-3002 : Step(128): len = 34430.7, overlap = 49.5312
PHY-3002 : Step(129): len = 34542.2, overlap = 44.6875
PHY-3002 : Step(130): len = 34918.7, overlap = 42.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000267921
PHY-3002 : Step(131): len = 35377, overlap = 38.125
PHY-3002 : Step(132): len = 35764, overlap = 37.6875
PHY-3002 : Step(133): len = 36457.6, overlap = 28.4062
PHY-3002 : Step(134): len = 36013.7, overlap = 30.4062
PHY-3002 : Step(135): len = 36019.4, overlap = 30.2812
PHY-3002 : Step(136): len = 35759.8, overlap = 29.3438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6798, tnet num: 1598, tinst num: 1388, tnode num: 8769, tedge num: 11158.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 78.50 peak overflow 4.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1600.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 41312, over cnt = 201(0%), over = 767, worst = 20
PHY-1001 : End global iterations;  0.063919s wall, 0.062500s user + 0.062500s system = 0.125000s CPU (195.6%)

PHY-1001 : Congestion index: top1 = 35.82, top5 = 21.02, top10 = 13.45, top15 = 9.49.
PHY-1001 : End incremental global routing;  0.110175s wall, 0.109375s user + 0.062500s system = 0.171875s CPU (156.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1598 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.042657s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (109.9%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1375 has valid locations, 27 needs to be replaced
PHY-3001 : design contains 1413 instances, 597 luts, 617 seqs, 165 slices, 25 macros(165 instances: 102 mslices 63 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 36452.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6895, tnet num: 1623, tinst num: 1413, tnode num: 8932, tedge num: 11302.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1623 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.147230s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (95.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(137): len = 36301.4, overlap = 1.25
PHY-3002 : Step(138): len = 36286.9, overlap = 1.1875
PHY-3002 : Step(139): len = 36314.2, overlap = 1.1875
PHY-3002 : Step(140): len = 36341.5, overlap = 1.25
PHY-3002 : Step(141): len = 36341.5, overlap = 1.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1623 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039625s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00067222
PHY-3002 : Step(142): len = 36303.5, overlap = 29.4375
PHY-3002 : Step(143): len = 36337.7, overlap = 29.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00131494
PHY-3002 : Step(144): len = 36399.4, overlap = 29.4375
PHY-3002 : Step(145): len = 36412.2, overlap = 29.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00262988
PHY-3002 : Step(146): len = 36397, overlap = 29.2812
PHY-3002 : Step(147): len = 36403.6, overlap = 29.2812
PHY-3001 : Final: Len = 36403.6, Over = 29.2812
PHY-3001 : End incremental placement;  0.330134s wall, 0.390625s user + 0.171875s system = 0.562500s CPU (170.4%)

OPT-1001 : Total overflow 79.09 peak overflow 4.66
OPT-1001 : End high-fanout net optimization;  0.513274s wall, 0.578125s user + 0.234375s system = 0.812500s CPU (158.3%)

OPT-1001 : Current memory(MB): used = 199, reserve = 153, peak = 199.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1081/1625.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 42344, over cnt = 200(0%), over = 766, worst = 20
PHY-1002 : len = 48528, over cnt = 127(0%), over = 233, worst = 6
PHY-1002 : len = 50264, over cnt = 34(0%), over = 40, worst = 3
PHY-1002 : len = 50736, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 50920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110603s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.9%)

PHY-1001 : Congestion index: top1 = 32.72, top5 = 21.47, top10 = 14.88, top15 = 10.79.
OPT-1001 : End congestion update;  0.153342s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (101.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1623 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.039886s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (78.3%)

OPT-0007 : Start: WNS 4959 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.193469s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (96.9%)

OPT-1001 : Current memory(MB): used = 197, reserve = 151, peak = 199.
OPT-1001 : End physical optimization;  0.824105s wall, 0.875000s user + 0.250000s system = 1.125000s CPU (136.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 597 LUT to BLE ...
SYN-4008 : Packed 597 LUT and 227 SEQ to BLE.
SYN-4003 : Packing 390 remaining SEQ's ...
SYN-4005 : Packed 207 SEQ with LUT/SLICE
SYN-4006 : 185 single LUT's are left
SYN-4006 : 183 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 780/1113 primitive instances ...
PHY-3001 : End packing;  0.040527s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (115.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 635 instances
RUN-1001 : 300 mslices, 299 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1401 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 695 nets have 2 pins
RUN-1001 : 508 nets have [3 - 5] pins
RUN-1001 : 112 nets have [6 - 10] pins
RUN-1001 : 48 nets have [11 - 20] pins
RUN-1001 : 27 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 633 instances, 599 slices, 25 macros(165 instances: 102 mslices 63 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 37180.6, Over = 45
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6019, tnet num: 1399, tinst num: 633, tnode num: 7535, tedge num: 10254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.149233s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.06326e-05
PHY-3002 : Step(148): len = 36861.8, overlap = 46
PHY-3002 : Step(149): len = 37228.4, overlap = 44.25
PHY-3002 : Step(150): len = 37241.6, overlap = 44
PHY-3002 : Step(151): len = 37538.8, overlap = 39.75
PHY-3002 : Step(152): len = 37613.1, overlap = 39.5
PHY-3002 : Step(153): len = 37752, overlap = 40.5
PHY-3002 : Step(154): len = 37449.5, overlap = 41
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000121265
PHY-3002 : Step(155): len = 37799.5, overlap = 39.5
PHY-3002 : Step(156): len = 37949.3, overlap = 39.25
PHY-3002 : Step(157): len = 38085.6, overlap = 40
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00024253
PHY-3002 : Step(158): len = 38850.5, overlap = 34.5
PHY-3002 : Step(159): len = 39051.8, overlap = 33.5
PHY-3002 : Step(160): len = 39201.6, overlap = 35
PHY-3002 : Step(161): len = 39275.2, overlap = 35
PHY-3002 : Step(162): len = 39129.9, overlap = 33.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.094626s wall, 0.078125s user + 0.078125s system = 0.156250s CPU (165.1%)

PHY-3001 : Trial Legalized: Len = 51397.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037623s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00642047
PHY-3002 : Step(163): len = 49308.7, overlap = 4.25
PHY-3002 : Step(164): len = 47525, overlap = 7
PHY-3002 : Step(165): len = 45916.3, overlap = 8.5
PHY-3002 : Step(166): len = 44695.2, overlap = 11
PHY-3002 : Step(167): len = 44324.2, overlap = 12
PHY-3002 : Step(168): len = 43721.8, overlap = 14.25
PHY-3002 : Step(169): len = 43225.2, overlap = 13.5
PHY-3002 : Step(170): len = 42853.5, overlap = 14.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0128409
PHY-3002 : Step(171): len = 42801.1, overlap = 15
PHY-3002 : Step(172): len = 42762, overlap = 15.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0256819
PHY-3002 : Step(173): len = 42716.3, overlap = 15.25
PHY-3002 : Step(174): len = 42634.1, overlap = 14.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004864s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 47381.6, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004511s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 47561.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6019, tnet num: 1399, tinst num: 633, tnode num: 7535, tedge num: 10254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 30/1401.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55800, over cnt = 177(0%), over = 288, worst = 5
PHY-1002 : len = 57112, over cnt = 72(0%), over = 93, worst = 3
PHY-1002 : len = 57936, over cnt = 23(0%), over = 25, worst = 2
PHY-1002 : len = 58112, over cnt = 13(0%), over = 15, worst = 2
PHY-1002 : len = 58296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.144730s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (151.1%)

PHY-1001 : Congestion index: top1 = 28.79, top5 = 21.86, top10 = 16.43, top15 = 12.45.
PHY-1001 : End incremental global routing;  0.194781s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (144.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044447s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (70.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.265082s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (129.7%)

OPT-1001 : Current memory(MB): used = 198, reserve = 153, peak = 201.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1209/1401.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004993s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 28.79, top5 = 21.86, top10 = 16.43, top15 = 12.45.
OPT-1001 : End congestion update;  0.047403s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.034445s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.7%)

OPT-0007 : Start: WNS 4859 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.082056s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (95.2%)

OPT-1001 : Current memory(MB): used = 200, reserve = 155, peak = 201.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.033479s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (140.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1209/1401.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004773s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (327.3%)

PHY-1001 : Congestion index: top1 = 28.79, top5 = 21.86, top10 = 16.43, top15 = 12.45.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.033118s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4859 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 28.344828
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4859ps with logic level 6 
RUN-1001 :       #2 path slack 4865ps with logic level 6 
OPT-1001 : End physical optimization;  0.606773s wall, 0.656250s user + 0.015625s system = 0.671875s CPU (110.7%)

RUN-1003 : finish command "place" in  4.542788s wall, 7.218750s user + 2.843750s system = 10.062500s CPU (221.5%)

RUN-1004 : used memory is 192 MB, reserved memory is 146 MB, peak memory is 201 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 635 instances
RUN-1001 : 300 mslices, 299 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1401 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 695 nets have 2 pins
RUN-1001 : 508 nets have [3 - 5] pins
RUN-1001 : 112 nets have [6 - 10] pins
RUN-1001 : 48 nets have [11 - 20] pins
RUN-1001 : 27 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6019, tnet num: 1399, tinst num: 633, tnode num: 7535, tedge num: 10254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 300 mslices, 299 lslices, 8 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55608, over cnt = 174(0%), over = 290, worst = 5
PHY-1002 : len = 56944, over cnt = 70(0%), over = 93, worst = 3
PHY-1002 : len = 57928, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 58040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133877s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (163.4%)

PHY-1001 : Congestion index: top1 = 28.99, top5 = 21.73, top10 = 16.35, top15 = 12.38.
PHY-1001 : End global routing;  0.180868s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (155.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 220, reserve = 176, peak = 220.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 478, reserve = 436, peak = 478.
PHY-1001 : End build detailed router design. 3.108975s wall, 3.093750s user + 0.015625s system = 3.109375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30200, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.596442s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 510, reserve = 470, peak = 510.
PHY-1001 : End phase 1; 0.602556s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (98.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 202024, over cnt = 58(0%), over = 59, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 510, reserve = 470, peak = 511.
PHY-1001 : End initial routed; 1.417633s wall, 2.031250s user + 0.031250s system = 2.062500s CPU (145.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1245(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.758   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.194925s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (96.2%)

PHY-1001 : Current memory(MB): used = 511, reserve = 470, peak = 511.
PHY-1001 : End phase 2; 1.612651s wall, 2.218750s user + 0.031250s system = 2.250000s CPU (139.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 202024, over cnt = 58(0%), over = 59, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.011673s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (133.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 201224, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.101071s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (108.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 201248, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.030498s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (51.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1245(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.758   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.190536s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 16 feed throughs used by 12 nets
PHY-1001 : End commit to database; 0.166582s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (93.8%)

PHY-1001 : Current memory(MB): used = 527, reserve = 485, peak = 527.
PHY-1001 : End phase 3; 0.610947s wall, 0.609375s user + 0.031250s system = 0.640625s CPU (104.9%)

PHY-1003 : Routed, final wirelength = 201248
PHY-1001 : Current memory(MB): used = 527, reserve = 486, peak = 527.
PHY-1001 : End export database. 0.009444s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.113358s wall, 6.703125s user + 0.078125s system = 6.781250s CPU (110.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6019, tnet num: 1399, tinst num: 633, tnode num: 7535, tedge num: 10254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  6.675622s wall, 7.312500s user + 0.125000s system = 7.437500s CPU (111.4%)

RUN-1004 : used memory is 484 MB, reserved memory is 441 MB, peak memory is 527 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      951   out of  19600    4.85%
#reg                      621   out of  19600    3.17%
#le                      1134
  #lut only               513   out of   1134   45.24%
  #reg only               183   out of   1134   16.14%
  #lut&reg                438   out of   1134   38.62%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  23
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    299
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         104
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1134   |786     |165     |627     |23      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |109    |91      |8       |61      |0       |0       |
|    usms                            |Time_1ms        |27     |12      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |195    |124     |23      |130     |0       |0       |
|  uart                              |UART_Control    |134    |124     |4       |54      |0       |0       |
|    U0                              |speed_select_Tx |23     |14      |4       |16      |0       |0       |
|    U1                              |uart_tx         |24     |23      |0       |17      |0       |0       |
|    U2                              |Ctrl_Data       |87     |87      |0       |21      |0       |0       |
|  wendu                             |DS18B20         |218    |175     |43      |75      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |473    |268     |87      |298     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |473    |268     |87      |298     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |182    |89      |0       |172     |0       |0       |
|        reg_inst                    |register        |181    |88      |0       |171     |0       |0       |
|        tap_inst                    |tap             |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |291    |179     |87      |126     |0       |0       |
|        bus_inst                    |bus_top         |78     |52      |26      |26      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |77     |51      |26      |25      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |131    |85      |29      |74      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       686   
    #2          2       298   
    #3          3       163   
    #4          4        47   
    #5        5-10      116   
    #6        11-50      70   
    #7       51-100      1    
    #8       101-500     1    
  Average     3.03            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6019, tnet num: 1399, tinst num: 633, tnode num: 7535, tedge num: 10254.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1399 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 588953dbe18b1871915f908614aff20f758057a2d1993c4931f4cee582d18853 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 633
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1401, pip num: 14571
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 16
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1250 valid insts, and 40093 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010011011001011011010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.107710s wall, 15.968750s user + 0.109375s system = 16.078125s CPU (517.4%)

RUN-1004 : used memory is 498 MB, reserved memory is 458 MB, peak memory is 645 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_112444.log"
