`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/26/2022 
// Design Name: 	SPI
// Module Name:    	SCHA634 
// Project Name: 	8K21
// Target Devices: 
// Tool versions: 	TD5.6.1-64bit
// Description: 	SPI数据处理
// Revision 1.01 - File Created
// Additional Comments: 
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////
module SPI_MASTER
#(
    parameter CPOL   		= 1,//clock polarity
    parameter CPHA   		= 1, //clock phase
    parameter CLK_FREQ   	= 88_000_000,// input clk frequency
    parameter SCLK_FREQ   	= 8_000_000 // sclk frequency = SCLK_FREQ / 2
	//parameter DATA_WIDTH  	= 16 //Clock cycle
)
(
    input   	            clk     ,
    input   	            rst_n   ,
    input   	            miso    ,
    input   	  [31:0]    data_i  ,
    input   	            start   ,  
	input		  [3:0]		tx_state,	//数据处理状态机，为了好在SPI_MASTER模块处理多种位宽传输
    output  reg             mosi    ,
    output  reg             sclk    ,
    output  reg             valid   ,
    output  reg   [31:0]    data_o  ,
    output  reg             finish //finish
);

// wire	[35:0]	CONTROL0;
// wire	[55:0]	TRIG0;
localparam       IDLE   =   5'b00001  ;
localparam       WAIT1  =   5'b00010  ;
localparam       DATA   =   5'b00100  ;
localparam       WAIT2  =   5'b01000  ;
localparam       FINISH =   5'b10000  ;
 
localparam       CNT_MAX =   CLK_FREQ / SCLK_FREQ - 1;

reg						miso_reg;
reg						read_done;//获取完一帧SPI总线数据
reg						read_donedy;
reg		[5:0]			DATA_WIDTH;
reg		[15:0]			cnt_wait;
reg     [15:0]  			cnt          ;/*synthesis keep*/   //sclk的时钟周期的计数器
reg     [4:0]   		state        ;/*synthesis keep*/   
reg     [4:0]   		nx_state     ;/*synthesis keep*/
reg     [1:0]    		sclk_dly     ;/*synthesis keep*/   //sclk的打一拍信号
reg     [5:0]   		cnt_sclk_pos ;/*synthesis keep*/   //sclk的上升沿计数器信号
reg     [5:0]   		cnt_sclk_neg ;   //sclk的下降沿计数器信号
reg     [1:0]   		start_dly    ;   //start的打一拍信号
reg     [5:0]   		cnt_data_dly ;/*synthesis keep*/
reg [31:0]	rx_data; 
reg [31:0]	rx_datady; 

wire            sclk_posedge ;/*synthesis keep*/   //sclk的上升沿
wire            sclk_negedge ;/*synthesis keep*/   //sclk的下降沿

assign sclk_posedge = ((CPHA == 1'b1) && (sclk_dly == 2'b01)) ? 1'b1 : 1'b0;
assign sclk_negedge = ((CPHA == 1'b0) && (sclk_dly == 2'b10)) ? 1'b1 : 1'b0;

always @(posedge clk or negedge rst_n) begin
	if(!rst_n)begin
		DATA_WIDTH  <= 6'd0;
	end
	else begin
		case(tx_state)
			4'd6:DATA_WIDTH <= 6'd24;
			4'd7:DATA_WIDTH <= 6'd32;
			default:DATA_WIDTH <= 6'd16;
		endcase
	end
end

always @(posedge clk or negedge rst_n) begin
	if(!rst_n)begin
		sclk_dly  <= 2'b00;
		start_dly <= 2'b00;
	end
	else begin
		sclk_dly  <= {sclk_dly[0],sclk};
		start_dly <= {start_dly[0],start};
	end
end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        cnt_sclk_pos <= 6'd0;
    end
    else if(state != DATA)begin
        cnt_sclk_pos <= 6'd0;
    end
    else if((sclk_posedge) && (cnt_sclk_pos == DATA_WIDTH)) begin
        cnt_sclk_pos <= DATA_WIDTH - 1;
    end
    else if(sclk_posedge) begin
        cnt_sclk_pos <= cnt_sclk_pos + 1'b1;
    end
    else begin
        cnt_sclk_pos <= cnt_sclk_pos;
    end
end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        cnt_sclk_neg <= 6'd0;
    end
    else if(state != DATA) begin
        cnt_sclk_neg <= 6'd0;
    end
    else if((sclk_negedge) && (cnt_sclk_neg == DATA_WIDTH)) begin
        cnt_sclk_neg <= DATA_WIDTH - 1;
    end
    else if(sclk_negedge) begin
        cnt_sclk_neg <= cnt_sclk_neg + 1'b1;
    end
    else begin
        cnt_sclk_neg <= cnt_sclk_neg;
    end
end
always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        state <= IDLE;
    end
    else begin
        state <= nx_state;
    end
end

always @(*) begin
    nx_state <= IDLE;
    case(state)
        IDLE:   nx_state <= (start_dly == 2'b01) ? WAIT1 :IDLE;
        WAIT1:  nx_state <= (cnt_wait == 16'd100) ?  DATA : WAIT1;
        DATA:   nx_state <= (cnt_sclk_pos == DATA_WIDTH) ?  WAIT2 : DATA;
        WAIT2:  nx_state <= (cnt_wait == 16'd100) ?  FINISH : WAIT2;
        FINISH: nx_state <= IDLE;
        default:nx_state <= IDLE;
    endcase
end


always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        cnt <= 8'd0;
    end
    else if((state == DATA) && (cnt == CNT_MAX)) begin
        cnt <= 8'd0;
    end
    else if((state == WAIT2) && (cnt == CNT_MAX)) begin
        cnt <= 8'd0;
    end
    else if(state == WAIT2) begin
        cnt <= cnt + 1'b1;
    end
    else if(state == DATA) begin
        cnt <= cnt + 1'b1;
    end
    else begin
        cnt <= 'd0;
    end
end  

always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        sclk <= (CPOL) ? 1'b1 : 1'b0;
    end
    else if(start_dly[1]) begin//start_dly
        if((state == DATA) && (cnt == CNT_MAX) && (cnt_sclk_pos < DATA_WIDTH) ) begin
			sclk <= ~sclk;
		end
        else if((state == WAIT2) && (cnt == CNT_MAX)) begin
			sclk <= (CPOL) ? 1'b1 : 1'b0;
		end
		else begin
			sclk <= sclk;
		end
    end
    else begin
        sclk <= (CPOL) ? 1'b1 : 1'b0;
    end
end 

always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
        cnt_wait <= 16'd0;
	end
    else if((state == WAIT1)||(state == WAIT2))begin
        if(cnt_wait <= 16'hFFFF) begin
			cnt_wait <= cnt_wait + 16'd1;
		end
		else begin
			cnt_wait <= cnt_wait;
		end
	end
	else begin
		cnt_wait <= 16'd0;
	end
end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n) begin
		valid	 <= 1'b0;
	end
    else if(state == WAIT2)begin
        if(cnt_wait == 16'd90) begin
			valid	 <= 1'b1;
		end
		else begin
			valid	 <= 1'b0;
		end
	end
	else begin
		valid <= valid;
	end
end

always @(posedge clk or negedge rst_n) begin
    if(!rst_n) 
        cnt_data_dly <= 6'd0;
    else 
        cnt_data_dly <= cnt_sclk_pos;
end

//rising edge miso;
always @(posedge clk or negedge rst_n) begin
    if (~rst_n) begin
		read_done 	<= 1'b0;
        rx_data 	<= 32'b0;
    end
    else if((state == DATA)&&(cnt_sclk_pos < DATA_WIDTH)) begin
		read_done 	<= 1'b0;
        rx_data[DATA_WIDTH - 1 - cnt_sclk_pos] <= miso;
    end
	else begin
		read_done 	<= 1'b1;
		rx_data <= rx_data;
	end
end

always @(posedge clk or negedge rst_n) begin
	if(!rst_n)begin
		rx_datady  <= 32'd0;
	end
	else begin
		case(tx_state)
			4'd0:rx_datady <= 32'd0;
			4'd1:rx_datady <= 32'd0;
			4'd2:rx_datady <= {23'd0,rx_data[7:0]};
			4'd3:rx_datady <= 32'd0;
			4'd4:rx_datady <= {23'd0,rx_data[7:0]};
			4'd5:rx_datady <= {23'd0,rx_data[7:0]};
			4'd6:rx_datady <= {16'd0,rx_data[15:0]};
			4'd7:rx_datady <= {8'd0,rx_data[23:0]};
			default:rx_datady <= 32'd0;
		endcase
	end
end
//mosi
always @(posedge clk or negedge rst_n) begin
    if (~rst_n) begin
        mosi <= 1'b1;
    end
    else begin
        mosi <= (state == DATA) ? ((cnt_sclk_pos < DATA_WIDTH) ? data_i[DATA_WIDTH - 1 - cnt_sclk_pos] : mosi) : mosi;
    end
end

always @(posedge clk) begin
	read_donedy <= read_done;
end

always @(posedge clk or negedge rst_n) begin
    if (~rst_n) 
		data_o <= 32'd0;
	else
		data_o <= (~read_donedy & read_done)? rx_datady : data_o;
end

//finish表示SPI读完一次完整的数据
always @(posedge clk) begin
	finish <= (state == FINISH) ? 1'b1 : 1'b0;
end

 // chipscope_icon ICON
 // (
	// .CONTROL0(CONTROL0)
 // );
 
 // chipscope_ila ILA  
 // (
	// .CLK(clk), 
	// .CONTROL(CONTROL0), 
	// .TRIG0(TRIG0)
// );
 
// assign TRIG0[15:0] 	= data_i;
// assign TRIG0[31:16] = rx_data;
// assign TRIG0[37:32] = cnt_sclk_pos;
// assign TRIG0[46:38] = state;
// assign TRIG0[47] 	= finish;
// assign TRIG0[48] 	= read_done;
// assign TRIG0[49] 	= start;
// assign TRIG0[50] 	= mosi;
// assign TRIG0[51] 	= miso;
// assign TRIG0[52] 	= miso_reg;
// assign TRIG0[53] 	= sclk_posedge;
	
endmodule
