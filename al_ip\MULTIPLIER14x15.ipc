<?xml version='1.0' encoding='UTF-8'?>
<DSPConfig>
    <GeneralConfig>
        <Type>EG_LOGIC_MULT</Type>
        <Device>EG4S20NG88</Device>
        <create_VHDL>true</create_VHDL>
        <inst>MULTIPLIER14x15</inst>
    </GeneralConfig>
    <SR_Mode_Opt>
        <sr_mode/>
    </SR_Mode_Opt>
    <Implement_Opt>
        <implement>1</implement>
    </Implement_Opt>
    <Input_Format_Opt>
        <input_format>UNSIGNED</input_format>
    </Input_Format_Opt>
    <Input_Option>
        <input_a>14</input_a>
        <input_b>15</input_b>
    </Input_Option>
    <Register_Option>
        <reg_a>false</reg_a>
        <reg_b>false</reg_b>
        <reg_p>false</reg_p>
    </Register_Option>
    <GeneratedFiles>
        <Verilog Enable="true">MULTIPLIER14x15.v</Verilog>
        <VHDL Enable="false">MULTIPLIER14x15.vhd</VHDL>
    </GeneratedFiles>
</DSPConfig>
