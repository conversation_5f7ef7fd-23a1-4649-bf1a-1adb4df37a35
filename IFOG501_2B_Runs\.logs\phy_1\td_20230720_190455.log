============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jul 20 19:04:55 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1632 instances
RUN-0007 : 368 luts, 1009 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2172 nets
RUN-1001 : 1616 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1630 instances, 368 luts, 1009 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7753, tnet num: 2170, tinst num: 1630, tnode num: 11015, tedge num: 13113.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.276286s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 635223
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1630.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 531193, overlap = 20.25
PHY-3002 : Step(2): len = 491525, overlap = 20.25
PHY-3002 : Step(3): len = 404184, overlap = 18
PHY-3002 : Step(4): len = 338964, overlap = 13.5
PHY-3002 : Step(5): len = 306668, overlap = 15.75
PHY-3002 : Step(6): len = 292514, overlap = 18
PHY-3002 : Step(7): len = 283670, overlap = 20.25
PHY-3002 : Step(8): len = 275548, overlap = 20.25
PHY-3002 : Step(9): len = 270083, overlap = 20.25
PHY-3002 : Step(10): len = 258551, overlap = 20.25
PHY-3002 : Step(11): len = 251519, overlap = 20.25
PHY-3002 : Step(12): len = 246917, overlap = 20.25
PHY-3002 : Step(13): len = 240432, overlap = 20.25
PHY-3002 : Step(14): len = 228938, overlap = 20.25
PHY-3002 : Step(15): len = 225380, overlap = 20.25
PHY-3002 : Step(16): len = 220348, overlap = 20.25
PHY-3002 : Step(17): len = 212354, overlap = 20.25
PHY-3002 : Step(18): len = 206623, overlap = 20.25
PHY-3002 : Step(19): len = 204060, overlap = 20.25
PHY-3002 : Step(20): len = 192424, overlap = 20.25
PHY-3002 : Step(21): len = 183938, overlap = 20.25
PHY-3002 : Step(22): len = 182116, overlap = 20.25
PHY-3002 : Step(23): len = 175035, overlap = 20.25
PHY-3002 : Step(24): len = 160481, overlap = 20.25
PHY-3002 : Step(25): len = 157530, overlap = 20.25
PHY-3002 : Step(26): len = 155156, overlap = 20.25
PHY-3002 : Step(27): len = 150536, overlap = 20.25
PHY-3002 : Step(28): len = 144752, overlap = 18
PHY-3002 : Step(29): len = 139574, overlap = 18
PHY-3002 : Step(30): len = 138239, overlap = 18
PHY-3002 : Step(31): len = 132759, overlap = 18
PHY-3002 : Step(32): len = 123143, overlap = 18
PHY-3002 : Step(33): len = 119111, overlap = 18
PHY-3002 : Step(34): len = 117364, overlap = 18
PHY-3002 : Step(35): len = 114174, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.46134e-05
PHY-3002 : Step(36): len = 116137, overlap = 15.75
PHY-3002 : Step(37): len = 115962, overlap = 13.5
PHY-3002 : Step(38): len = 114028, overlap = 15.75
PHY-3002 : Step(39): len = 111832, overlap = 18
PHY-3002 : Step(40): len = 111098, overlap = 18
PHY-3002 : Step(41): len = 108878, overlap = 11.25
PHY-3002 : Step(42): len = 106459, overlap = 9
PHY-3002 : Step(43): len = 103969, overlap = 11.25
PHY-3002 : Step(44): len = 101524, overlap = 16
PHY-3002 : Step(45): len = 98002.7, overlap = 11.75
PHY-3002 : Step(46): len = 95713.7, overlap = 13.75
PHY-3002 : Step(47): len = 95029.6, overlap = 13.5
PHY-3002 : Step(48): len = 93416.6, overlap = 11.25
PHY-3002 : Step(49): len = 92766.7, overlap = 9
PHY-3002 : Step(50): len = 88627.4, overlap = 9
PHY-3002 : Step(51): len = 86683.2, overlap = 11.25
PHY-3002 : Step(52): len = 85362.9, overlap = 11.25
PHY-3002 : Step(53): len = 84224.9, overlap = 11.25
PHY-3002 : Step(54): len = 82339.3, overlap = 9
PHY-3002 : Step(55): len = 80596.2, overlap = 9.375
PHY-3002 : Step(56): len = 78736.2, overlap = 7.1875
PHY-3002 : Step(57): len = 76844, overlap = 11.9375
PHY-3002 : Step(58): len = 76542.9, overlap = 11.9375
PHY-3002 : Step(59): len = 72178.3, overlap = 14.625
PHY-3002 : Step(60): len = 69068.6, overlap = 14.8125
PHY-3002 : Step(61): len = 68738.7, overlap = 14.8125
PHY-3002 : Step(62): len = 67323.9, overlap = 8.0625
PHY-3002 : Step(63): len = 66463, overlap = 10.3125
PHY-3002 : Step(64): len = 66382.2, overlap = 10.3125
PHY-3002 : Step(65): len = 66222.9, overlap = 12.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000169227
PHY-3002 : Step(66): len = 66064.7, overlap = 12.5625
PHY-3002 : Step(67): len = 66062.1, overlap = 10.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000338454
PHY-3002 : Step(68): len = 66260.5, overlap = 10.3125
PHY-3002 : Step(69): len = 66373.2, overlap = 12.5625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005387s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060302s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00151072
PHY-3002 : Step(70): len = 69777.5, overlap = 4
PHY-3002 : Step(71): len = 68016.1, overlap = 3.3125
PHY-3002 : Step(72): len = 66048.9, overlap = 3.25
PHY-3002 : Step(73): len = 64980.9, overlap = 3.125
PHY-3002 : Step(74): len = 64022.7, overlap = 3
PHY-3002 : Step(75): len = 62431.6, overlap = 3.1875
PHY-3002 : Step(76): len = 61074.3, overlap = 3.875
PHY-3002 : Step(77): len = 60180.1, overlap = 3.9375
PHY-3002 : Step(78): len = 59135.1, overlap = 4.0625
PHY-3002 : Step(79): len = 57578.7, overlap = 4.4375
PHY-3002 : Step(80): len = 55826.3, overlap = 4.625
PHY-3002 : Step(81): len = 54048.2, overlap = 9.5625
PHY-3002 : Step(82): len = 53084.7, overlap = 9.6875
PHY-3002 : Step(83): len = 52640.3, overlap = 9.875
PHY-3002 : Step(84): len = 51908.6, overlap = 11.1875
PHY-3002 : Step(85): len = 51786.4, overlap = 12
PHY-3002 : Step(86): len = 51378.1, overlap = 11.3125
PHY-3002 : Step(87): len = 50854.4, overlap = 11.1875
PHY-3002 : Step(88): len = 50802.9, overlap = 11.4375
PHY-3002 : Step(89): len = 50355.6, overlap = 11.5
PHY-3002 : Step(90): len = 49838.4, overlap = 11.5
PHY-3002 : Step(91): len = 49746.4, overlap = 11.625
PHY-3002 : Step(92): len = 49212.1, overlap = 11.9375
PHY-3002 : Step(93): len = 48267.7, overlap = 15.2188
PHY-3002 : Step(94): len = 47304.6, overlap = 14.9688
PHY-3002 : Step(95): len = 46754.3, overlap = 15.0938
PHY-3002 : Step(96): len = 46434.8, overlap = 15.375
PHY-3002 : Step(97): len = 46303.8, overlap = 15.4375
PHY-3002 : Step(98): len = 46356.1, overlap = 15.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00302143
PHY-3002 : Step(99): len = 46209.6, overlap = 15.4062
PHY-3002 : Step(100): len = 46108.2, overlap = 15.9062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00604286
PHY-3002 : Step(101): len = 46052.8, overlap = 15.9062
PHY-3002 : Step(102): len = 45962.3, overlap = 15.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.074757s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.04256e-05
PHY-3002 : Step(103): len = 46195.4, overlap = 70.0625
PHY-3002 : Step(104): len = 46615.5, overlap = 68.2188
PHY-3002 : Step(105): len = 46791.2, overlap = 67
PHY-3002 : Step(106): len = 47045.5, overlap = 58.6562
PHY-3002 : Step(107): len = 47555.2, overlap = 56.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000140851
PHY-3002 : Step(108): len = 47382.6, overlap = 56.0625
PHY-3002 : Step(109): len = 47779.3, overlap = 53.5312
PHY-3002 : Step(110): len = 47867.3, overlap = 52.1875
PHY-3002 : Step(111): len = 47961.4, overlap = 50.8125
PHY-3002 : Step(112): len = 48116.4, overlap = 51.3438
PHY-3002 : Step(113): len = 48333.3, overlap = 51
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000281703
PHY-3002 : Step(114): len = 48456, overlap = 50
PHY-3002 : Step(115): len = 48633.7, overlap = 47.9375
PHY-3002 : Step(116): len = 49946.2, overlap = 39.4062
PHY-3002 : Step(117): len = 50569.8, overlap = 36.25
PHY-3002 : Step(118): len = 50340.1, overlap = 34.4375
PHY-3002 : Step(119): len = 50085.5, overlap = 33.2812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7753, tnet num: 2170, tinst num: 1630, tnode num: 11015, tedge num: 13113.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.78 peak overflow 3.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2172.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53608, over cnt = 252(0%), over = 959, worst = 21
PHY-1001 : End global iterations;  0.066982s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (116.6%)

PHY-1001 : Congestion index: top1 = 41.14, top5 = 23.96, top10 = 15.47, top15 = 10.96.
PHY-1001 : End incremental global routing;  0.118140s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (119.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064362s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.213078s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (110.0%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1636/2172.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53608, over cnt = 252(0%), over = 959, worst = 21
PHY-1002 : len = 60392, over cnt = 172(0%), over = 330, worst = 14
PHY-1002 : len = 63848, over cnt = 43(0%), over = 51, worst = 4
PHY-1002 : len = 64432, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 64664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.092268s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (118.5%)

PHY-1001 : Congestion index: top1 = 36.38, top5 = 24.61, top10 = 17.38, top15 = 12.77.
OPT-1001 : End congestion update;  0.135197s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (115.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062488s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.200706s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (116.8%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.694188s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (108.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 107 SEQ with LUT/SLICE
SYN-4006 : 97 single LUT's are left
SYN-4006 : 720 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1088/1401 primitive instances ...
PHY-3001 : End packing;  0.049533s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 830 instances
RUN-1001 : 391 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2004 nets
RUN-1001 : 1456 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 828 instances, 781 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50459, Over = 59.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6513, tnet num: 2002, tinst num: 828, tnode num: 8857, tedge num: 11463.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.320742s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (102.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.42218e-05
PHY-3002 : Step(120): len = 49921.9, overlap = 61.5
PHY-3002 : Step(121): len = 49635.4, overlap = 62.5
PHY-3002 : Step(122): len = 49211.5, overlap = 64.5
PHY-3002 : Step(123): len = 49118.1, overlap = 63
PHY-3002 : Step(124): len = 49155.4, overlap = 62
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.84436e-05
PHY-3002 : Step(125): len = 49289.3, overlap = 61.5
PHY-3002 : Step(126): len = 49606.9, overlap = 59.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.55447e-05
PHY-3002 : Step(127): len = 50264.2, overlap = 56.75
PHY-3002 : Step(128): len = 51226.7, overlap = 55.25
PHY-3002 : Step(129): len = 51586.3, overlap = 53
PHY-3002 : Step(130): len = 52086.2, overlap = 48.25
PHY-3002 : Step(131): len = 52617.2, overlap = 46.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.097550s wall, 0.078125s user + 0.218750s system = 0.296875s CPU (304.3%)

PHY-3001 : Trial Legalized: Len = 66715.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049622s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000620995
PHY-3002 : Step(132): len = 63821.8, overlap = 8.25
PHY-3002 : Step(133): len = 62071.7, overlap = 11.5
PHY-3002 : Step(134): len = 60695.3, overlap = 16.5
PHY-3002 : Step(135): len = 59783.1, overlap = 19.5
PHY-3002 : Step(136): len = 59069.6, overlap = 21
PHY-3002 : Step(137): len = 58592.8, overlap = 22.75
PHY-3002 : Step(138): len = 58217, overlap = 23.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00124199
PHY-3002 : Step(139): len = 58607.5, overlap = 22.75
PHY-3002 : Step(140): len = 58684.2, overlap = 22
PHY-3002 : Step(141): len = 58684.2, overlap = 22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00248398
PHY-3002 : Step(142): len = 58860.1, overlap = 22.5
PHY-3002 : Step(143): len = 58917.8, overlap = 22.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005098s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63314.7, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005607s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.7%)

PHY-3001 : 6 instances has been re-located, deltaX = 2, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 63344.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6513, tnet num: 2002, tinst num: 828, tnode num: 8857, tedge num: 11463.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 55/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69368, over cnt = 157(0%), over = 226, worst = 7
PHY-1002 : len = 69944, over cnt = 94(0%), over = 125, worst = 3
PHY-1002 : len = 71336, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109402s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (114.3%)

PHY-1001 : Congestion index: top1 = 31.01, top5 = 22.99, top10 = 17.97, top15 = 14.09.
PHY-1001 : End incremental global routing;  0.158904s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (108.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060691s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.249394s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (106.5%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1763/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005670s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.01, top5 = 22.99, top10 = 17.97, top15 = 14.09.
OPT-1001 : End congestion update;  0.050279s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048687s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 790 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 828 instances, 781 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63241, Over = 0
PHY-3001 : End spreading;  0.005927s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (263.6%)

PHY-3001 : Final: Len = 63241, Over = 0
PHY-3001 : End incremental legalization;  0.035898s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.1%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.149413s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.6%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048602s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1756/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71296, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71296, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024157s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (64.7%)

PHY-1001 : Congestion index: top1 = 31.03, top5 = 22.97, top10 = 17.97, top15 = 14.08.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057228s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.872334s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (102.1%)

RUN-1003 : finish command "place" in  5.004226s wall, 7.296875s user + 2.796875s system = 10.093750s CPU (201.7%)

RUN-1004 : used memory is 200 MB, reserved memory is 163 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 830 instances
RUN-1001 : 391 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2004 nets
RUN-1001 : 1456 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6513, tnet num: 2002, tinst num: 828, tnode num: 8857, tedge num: 11463.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 391 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68736, over cnt = 164(0%), over = 238, worst = 7
PHY-1002 : len = 69496, over cnt = 76(0%), over = 91, worst = 3
PHY-1002 : len = 70544, over cnt = 11(0%), over = 13, worst = 3
PHY-1002 : len = 70736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118754s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (157.9%)

PHY-1001 : Congestion index: top1 = 30.65, top5 = 22.81, top10 = 17.81, top15 = 13.95.
PHY-1001 : End global routing;  0.167509s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (139.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 200, peak = 245.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 465, peak = 496.
PHY-1001 : End build detailed router design. 3.275426s wall, 3.250000s user + 0.015625s system = 3.265625s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34344, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.271071s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 528, reserve = 501, peak = 528.
PHY-1001 : End phase 1; 1.277392s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 181616, over cnt = 19(0%), over = 19, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 501, peak = 531.
PHY-1001 : End initial routed; 1.006916s wall, 1.609375s user + 0.093750s system = 1.703125s CPU (169.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1786(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.380   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.375095s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End phase 2; 1.382102s wall, 1.984375s user + 0.093750s system = 2.078125s CPU (150.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181616, over cnt = 19(0%), over = 19, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014195s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (110.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181640, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023451s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (199.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181704, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021934s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1786(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.380   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.371047s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.170479s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End phase 3; 0.724069s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.3%)

PHY-1003 : Routed, final wirelength = 181704
PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End export database. 0.010348s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (151.0%)

PHY-1001 : End detail routing;  6.851338s wall, 7.437500s user + 0.109375s system = 7.546875s CPU (110.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6513, tnet num: 2002, tinst num: 828, tnode num: 8857, tedge num: 11463.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.746950s wall, 8.390625s user + 0.125000s system = 8.515625s CPU (109.9%)

RUN-1004 : used memory is 523 MB, reserved memory is 495 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      787   out of  19600    4.02%
#reg                     1074   out of  19600    5.48%
#le                      1507
  #lut only               433   out of   1507   28.73%
  #reg only               720   out of   1507   47.78%
  #lut&reg                354   out of   1507   23.49%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         471
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1507   |581     |206     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1133   |299     |122     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |33      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |523    |109     |53      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |0       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |12      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |16      |0       |30      |0       |0       |
|    integ                   |Integration                                      |135    |19      |14      |109     |0       |0       |
|    modu                    |Modulation                                       |100    |24      |15      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |95      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |19      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |98     |83      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |19     |16      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |42     |39      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1420  
    #2          2       310   
    #3          3       105   
    #4          4        16   
    #5        5-10       80   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6513, tnet num: 2002, tinst num: 828, tnode num: 8857, tedge num: 11463.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 828
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2004, pip num: 14725
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1308 valid insts, and 38702 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.149164s wall, 18.234375s user + 0.078125s system = 18.312500s CPU (581.5%)

RUN-1004 : used memory is 547 MB, reserved memory is 516 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230720_190455.log"
