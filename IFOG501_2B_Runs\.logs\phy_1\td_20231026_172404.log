============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 17:24:04 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1531 instances
RUN-0007 : 384 luts, 893 seqs, 130 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2071 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1551 nets have 2 pins
RUN-1001 : 404 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     241     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1529 instances, 384 luts, 893 seqs, 205 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7311, tnet num: 2069, tinst num: 1529, tnode num: 10240, tedge num: 12361.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2069 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.268419s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (99.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 529718
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1529.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 440044, overlap = 15.75
PHY-3002 : Step(2): len = 407682, overlap = 20.25
PHY-3002 : Step(3): len = 385485, overlap = 20.25
PHY-3002 : Step(4): len = 370864, overlap = 20.25
PHY-3002 : Step(5): len = 363599, overlap = 20.25
PHY-3002 : Step(6): len = 349842, overlap = 18
PHY-3002 : Step(7): len = 341120, overlap = 20.25
PHY-3002 : Step(8): len = 335195, overlap = 18
PHY-3002 : Step(9): len = 325129, overlap = 20.25
PHY-3002 : Step(10): len = 317769, overlap = 18
PHY-3002 : Step(11): len = 312352, overlap = 20.25
PHY-3002 : Step(12): len = 304459, overlap = 18
PHY-3002 : Step(13): len = 292899, overlap = 20.25
PHY-3002 : Step(14): len = 288104, overlap = 18
PHY-3002 : Step(15): len = 282721, overlap = 18
PHY-3002 : Step(16): len = 273633, overlap = 18
PHY-3002 : Step(17): len = 267532, overlap = 18
PHY-3002 : Step(18): len = 263680, overlap = 18
PHY-3002 : Step(19): len = 256926, overlap = 18
PHY-3002 : Step(20): len = 250795, overlap = 18
PHY-3002 : Step(21): len = 247378, overlap = 15.75
PHY-3002 : Step(22): len = 240374, overlap = 15.75
PHY-3002 : Step(23): len = 231993, overlap = 15.75
PHY-3002 : Step(24): len = 229049, overlap = 15.75
PHY-3002 : Step(25): len = 223159, overlap = 15.75
PHY-3002 : Step(26): len = 196952, overlap = 13.5
PHY-3002 : Step(27): len = 190446, overlap = 13.5
PHY-3002 : Step(28): len = 188237, overlap = 13.5
PHY-3002 : Step(29): len = 149567, overlap = 18
PHY-3002 : Step(30): len = 141427, overlap = 18
PHY-3002 : Step(31): len = 139147, overlap = 13.5
PHY-3002 : Step(32): len = 133070, overlap = 13.5
PHY-3002 : Step(33): len = 128980, overlap = 13.5
PHY-3002 : Step(34): len = 127358, overlap = 13.5
PHY-3002 : Step(35): len = 123530, overlap = 13.5
PHY-3002 : Step(36): len = 120369, overlap = 13.5
PHY-3002 : Step(37): len = 116993, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000116373
PHY-3002 : Step(38): len = 117158, overlap = 13.5
PHY-3002 : Step(39): len = 116263, overlap = 13.5
PHY-3002 : Step(40): len = 115029, overlap = 11.25
PHY-3002 : Step(41): len = 114303, overlap = 13.5
PHY-3002 : Step(42): len = 113177, overlap = 13.5
PHY-3002 : Step(43): len = 108875, overlap = 13.5
PHY-3002 : Step(44): len = 106414, overlap = 11.25
PHY-3002 : Step(45): len = 104715, overlap = 13.5
PHY-3002 : Step(46): len = 103382, overlap = 11.25
PHY-3002 : Step(47): len = 100345, overlap = 13.5
PHY-3002 : Step(48): len = 99128.9, overlap = 13.5
PHY-3002 : Step(49): len = 97348, overlap = 13.5
PHY-3002 : Step(50): len = 94871, overlap = 13.5
PHY-3002 : Step(51): len = 93389.2, overlap = 13.5
PHY-3002 : Step(52): len = 92276.8, overlap = 13.5
PHY-3002 : Step(53): len = 86532.3, overlap = 11.25
PHY-3002 : Step(54): len = 84073.3, overlap = 11.25
PHY-3002 : Step(55): len = 82268.3, overlap = 13.5
PHY-3002 : Step(56): len = 81121.4, overlap = 13.5
PHY-3002 : Step(57): len = 80069.4, overlap = 13.5
PHY-3002 : Step(58): len = 79171.6, overlap = 13.5
PHY-3002 : Step(59): len = 78442.3, overlap = 11.25
PHY-3002 : Step(60): len = 77039.6, overlap = 11.25
PHY-3002 : Step(61): len = 74958, overlap = 13.5
PHY-3002 : Step(62): len = 74120.7, overlap = 13.5
PHY-3002 : Step(63): len = 72860.4, overlap = 11.25
PHY-3002 : Step(64): len = 71200.2, overlap = 12
PHY-3002 : Step(65): len = 70890.5, overlap = 12.3125
PHY-3002 : Step(66): len = 69145.9, overlap = 10.5625
PHY-3002 : Step(67): len = 68243.9, overlap = 15.875
PHY-3002 : Step(68): len = 67850.4, overlap = 13.5625
PHY-3002 : Step(69): len = 67483.3, overlap = 13.6875
PHY-3002 : Step(70): len = 66368.6, overlap = 16.125
PHY-3002 : Step(71): len = 65868.4, overlap = 15.3125
PHY-3002 : Step(72): len = 65821.4, overlap = 15.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000232746
PHY-3002 : Step(73): len = 66101.5, overlap = 15.3125
PHY-3002 : Step(74): len = 66101.1, overlap = 15.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000465492
PHY-3002 : Step(75): len = 66177, overlap = 15.25
PHY-3002 : Step(76): len = 66301.5, overlap = 14.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004967s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2069 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056478s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00907162
PHY-3002 : Step(77): len = 68557.9, overlap = 17.2188
PHY-3002 : Step(78): len = 67716, overlap = 15.5625
PHY-3002 : Step(79): len = 66191.4, overlap = 14.5625
PHY-3002 : Step(80): len = 64181.4, overlap = 15.6562
PHY-3002 : Step(81): len = 63145.5, overlap = 15.0312
PHY-3002 : Step(82): len = 60174.6, overlap = 16.1875
PHY-3002 : Step(83): len = 59261, overlap = 15.1875
PHY-3002 : Step(84): len = 57503, overlap = 15.6562
PHY-3002 : Step(85): len = 56910.6, overlap = 14.9062
PHY-3002 : Step(86): len = 56494.9, overlap = 14.2812
PHY-3002 : Step(87): len = 56289.8, overlap = 13.25
PHY-3002 : Step(88): len = 55687.4, overlap = 13.0938
PHY-3002 : Step(89): len = 55283, overlap = 13.5312
PHY-3002 : Step(90): len = 54580.6, overlap = 13.5312
PHY-3002 : Step(91): len = 54168.9, overlap = 13.8438
PHY-3002 : Step(92): len = 53545, overlap = 15.0312
PHY-3002 : Step(93): len = 53099.9, overlap = 14.75
PHY-3002 : Step(94): len = 52720.3, overlap = 14.0312
PHY-3002 : Step(95): len = 52258.7, overlap = 13.4688
PHY-3002 : Step(96): len = 52251.8, overlap = 13.4688
PHY-3002 : Step(97): len = 52035.8, overlap = 13.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0181432
PHY-3002 : Step(98): len = 51747.5, overlap = 13.1562
PHY-3002 : Step(99): len = 51684.1, overlap = 13.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0362865
PHY-3002 : Step(100): len = 51553.7, overlap = 13.3438
PHY-3002 : Step(101): len = 51598.8, overlap = 13.3438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2069 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052900s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.34377e-05
PHY-3002 : Step(102): len = 52155.3, overlap = 44.5
PHY-3002 : Step(103): len = 52350.7, overlap = 43.8125
PHY-3002 : Step(104): len = 52833.9, overlap = 46.3125
PHY-3002 : Step(105): len = 52603.2, overlap = 44.7812
PHY-3002 : Step(106): len = 52169, overlap = 39.8125
PHY-3002 : Step(107): len = 52023.3, overlap = 35.0938
PHY-3002 : Step(108): len = 51763.2, overlap = 35.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000186875
PHY-3002 : Step(109): len = 52033.5, overlap = 33.9375
PHY-3002 : Step(110): len = 52185.8, overlap = 33.4062
PHY-3002 : Step(111): len = 52824.1, overlap = 33.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000373751
PHY-3002 : Step(112): len = 52755.1, overlap = 29.7812
PHY-3002 : Step(113): len = 53562.2, overlap = 24
PHY-3002 : Step(114): len = 54316.9, overlap = 26.4375
PHY-3002 : Step(115): len = 54557.6, overlap = 25.875
PHY-3002 : Step(116): len = 54729.7, overlap = 24.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7311, tnet num: 2069, tinst num: 1529, tnode num: 10240, tedge num: 12361.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 73.88 peak overflow 1.91
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2071.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58200, over cnt = 244(0%), over = 1019, worst = 15
PHY-1001 : End global iterations;  0.078540s wall, 0.078125s user + 0.046875s system = 0.125000s CPU (159.2%)

PHY-1001 : Congestion index: top1 = 43.34, top5 = 26.71, top10 = 16.62, top15 = 11.75.
PHY-1001 : End incremental global routing;  0.128270s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (134.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2069 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063038s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.220551s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (120.4%)

OPT-1001 : Current memory(MB): used = 212, reserve = 174, peak = 212.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1601/2071.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58200, over cnt = 244(0%), over = 1019, worst = 15
PHY-1002 : len = 64888, over cnt = 140(0%), over = 351, worst = 12
PHY-1002 : len = 68760, over cnt = 30(0%), over = 43, worst = 6
PHY-1002 : len = 69136, over cnt = 15(0%), over = 16, worst = 2
PHY-1002 : len = 69464, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104135s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (135.0%)

PHY-1001 : Congestion index: top1 = 37.46, top5 = 25.80, top10 = 18.30, top15 = 13.40.
OPT-1001 : End congestion update;  0.145897s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (128.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2069 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053842s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.203135s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.4%)

OPT-1001 : Current memory(MB): used = 215, reserve = 177, peak = 215.
OPT-1001 : End physical optimization;  0.707913s wall, 0.718750s user + 0.062500s system = 0.781250s CPU (110.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 384 LUT to BLE ...
SYN-4008 : Packed 384 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 722 remaining SEQ's ...
SYN-4005 : Packed 127 SEQ with LUT/SLICE
SYN-4006 : 104 single LUT's are left
SYN-4006 : 595 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 979/1267 primitive instances ...
PHY-3001 : End packing;  0.045615s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 773 instances
RUN-1001 : 362 mslices, 362 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1908 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1392 nets have 2 pins
RUN-1001 : 399 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 771 instances, 724 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54917.8, Over = 48.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6177, tnet num: 1906, tinst num: 771, tnode num: 8302, tedge num: 10843.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1906 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.296303s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.82271e-05
PHY-3002 : Step(117): len = 54367.3, overlap = 45.5
PHY-3002 : Step(118): len = 53696.9, overlap = 48
PHY-3002 : Step(119): len = 53140, overlap = 48.75
PHY-3002 : Step(120): len = 52923.7, overlap = 51.5
PHY-3002 : Step(121): len = 52931.9, overlap = 51.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.64543e-05
PHY-3002 : Step(122): len = 53166.7, overlap = 50.75
PHY-3002 : Step(123): len = 53403.5, overlap = 50.25
PHY-3002 : Step(124): len = 54083.8, overlap = 46.25
PHY-3002 : Step(125): len = 54305.6, overlap = 46.5
PHY-3002 : Step(126): len = 54296.8, overlap = 45.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000152909
PHY-3002 : Step(127): len = 54516.8, overlap = 45
PHY-3002 : Step(128): len = 55342.8, overlap = 41.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078681s wall, 0.062500s user + 0.078125s system = 0.140625s CPU (178.7%)

PHY-3001 : Trial Legalized: Len = 68042.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1906 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.045602s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00143276
PHY-3002 : Step(129): len = 65450.8, overlap = 3.75
PHY-3002 : Step(130): len = 63572.3, overlap = 8.5
PHY-3002 : Step(131): len = 62252.4, overlap = 11.25
PHY-3002 : Step(132): len = 61129.8, overlap = 13.25
PHY-3002 : Step(133): len = 60252.9, overlap = 15
PHY-3002 : Step(134): len = 59838.2, overlap = 18
PHY-3002 : Step(135): len = 59624.3, overlap = 19.25
PHY-3002 : Step(136): len = 59485.9, overlap = 20.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00286551
PHY-3002 : Step(137): len = 59560.3, overlap = 21
PHY-3002 : Step(138): len = 59599.8, overlap = 21.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00573103
PHY-3002 : Step(139): len = 59633.1, overlap = 20.5
PHY-3002 : Step(140): len = 59641.4, overlap = 21
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005142s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63732, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004941s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 3 instances has been re-located, deltaX = 0, deltaY = 3, maxDist = 1.
PHY-3001 : Final: Len = 63828, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6177, tnet num: 1906, tinst num: 771, tnode num: 8302, tedge num: 10843.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 66/1908.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70360, over cnt = 159(0%), over = 252, worst = 7
PHY-1002 : len = 71280, over cnt = 91(0%), over = 124, worst = 4
PHY-1002 : len = 72624, over cnt = 6(0%), over = 7, worst = 2
PHY-1002 : len = 72712, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.152508s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (102.5%)

PHY-1001 : Congestion index: top1 = 31.59, top5 = 23.11, top10 = 18.15, top15 = 13.99.
PHY-1001 : End incremental global routing;  0.203904s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (99.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1906 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056637s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.290296s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (96.9%)

OPT-1001 : Current memory(MB): used = 217, reserve = 180, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1686/1908.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005476s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.59, top5 = 23.11, top10 = 18.15, top15 = 13.99.
OPT-1001 : End congestion update;  0.050334s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1906 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046578s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 733 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 771 instances, 724 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63843.8, Over = 0
PHY-3001 : End spreading;  0.005561s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (281.0%)

PHY-3001 : Final: Len = 63843.8, Over = 0
PHY-3001 : End incremental legalization;  0.035458s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.146659s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.5%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1906 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048862s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1682/1908.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72728, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72728, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024619s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.5%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 23.11, top10 = 18.15, top15 = 13.98.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1906 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047108s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.137931
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.883576s wall, 0.906250s user + 0.031250s system = 0.937500s CPU (106.1%)

RUN-1003 : finish command "place" in  5.016231s wall, 7.359375s user + 2.578125s system = 9.937500s CPU (198.1%)

RUN-1004 : used memory is 204 MB, reserved memory is 166 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 773 instances
RUN-1001 : 362 mslices, 362 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1908 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1392 nets have 2 pins
RUN-1001 : 399 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6177, tnet num: 1906, tinst num: 771, tnode num: 8302, tedge num: 10843.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 362 mslices, 362 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1906 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70008, over cnt = 160(0%), over = 255, worst = 7
PHY-1002 : len = 71104, over cnt = 82(0%), over = 110, worst = 3
PHY-1002 : len = 72392, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 72576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.132630s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (117.8%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.96, top10 = 18.08, top15 = 13.95.
PHY-1001 : End global routing;  0.181206s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 200, peak = 238.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 464, peak = 497.
PHY-1001 : End build detailed router design. 3.162479s wall, 3.156250s user + 0.015625s system = 3.171875s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33104, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.297866s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 529, reserve = 498, peak = 529.
PHY-1001 : End phase 1; 1.304005s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186456, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 498, peak = 531.
PHY-1001 : End initial routed; 1.349753s wall, 2.046875s user + 0.140625s system = 2.187500s CPU (162.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.280   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.417   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350517s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.5%)

PHY-1001 : Current memory(MB): used = 533, reserve = 500, peak = 533.
PHY-1001 : End phase 2; 1.700356s wall, 2.406250s user + 0.140625s system = 2.546875s CPU (149.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186456, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014546s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (107.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186464, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029208s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (160.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186544, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019913s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (78.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.280   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.417   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.361469s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.170544s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 547, reserve = 513, peak = 547.
PHY-1001 : End phase 3; 0.721469s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (104.0%)

PHY-1003 : Routed, final wirelength = 186544
PHY-1001 : Current memory(MB): used = 547, reserve = 514, peak = 547.
PHY-1001 : End export database. 0.009755s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (160.2%)

PHY-1001 : End detail routing;  7.080751s wall, 7.781250s user + 0.171875s system = 7.953125s CPU (112.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6177, tnet num: 1906, tinst num: 771, tnode num: 8302, tedge num: 10843.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[2] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[3] slack -52ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6207, tnet num: 1921, tinst num: 786, tnode num: 8332, tedge num: 10873.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.943628s wall, 2.953125s user + 0.156250s system = 3.109375s CPU (105.6%)

RUN-1003 : finish command "route" in  10.541845s wall, 11.265625s user + 0.328125s system = 11.593750s CPU (110.0%)

RUN-1004 : used memory is 534 MB, reserved memory is 504 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      825   out of  19600    4.21%
#reg                      961   out of  19600    4.90%
#le                      1420
  #lut only               459   out of   1420   32.32%
  #reg only               595   out of   1420   41.90%
  #lut&reg                366   out of   1420   25.77%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         421
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1420   |620     |205     |992     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1010   |303     |113     |809     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |19      |7       |21      |0       |0       |
|    demodu                  |Demodulation                                     |453    |120     |44      |342     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |59     |36      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |9       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|    integ                   |Integration                                      |143    |43      |14      |116     |0       |0       |
|    modu                    |Modulation                                       |62     |17      |14      |58      |0       |1       |
|    rs422                   |Rs422Output                                      |307    |90      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |122    |114     |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |27      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |26     |26      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |61     |61      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1371  
    #2          2       265   
    #3          3       123   
    #4          4        11   
    #5        5-10       83   
    #6        11-50      26   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6207, tnet num: 1921, tinst num: 786, tnode num: 8332, tedge num: 10873.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1921 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 786
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1923, pip num: 14437
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1299 valid insts, and 38018 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.038718s wall, 17.593750s user + 0.062500s system = 17.656250s CPU (581.0%)

RUN-1004 : used memory is 551 MB, reserved memory is 517 MB, peak memory is 680 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_172404.log"
