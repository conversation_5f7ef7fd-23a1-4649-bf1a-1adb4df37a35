============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Feb 10 15:44:40 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 38 trigger nets, 38 data nets.
KIT-1004 : Chipwatcher code = 0101111100011010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2620/23 useful/useless nets, 1653/12 useful/useless insts
SYN-1016 : Merged 29 instances.
SYN-1032 : 2304/20 useful/useless nets, 2059/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 378 better
SYN-1014 : Optimize round 2
SYN-1032 : 2000/45 useful/useless nets, 1755/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2048/295 useful/useless nets, 1836/43 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 383 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 36 instances.
SYN-2501 : Optimize round 1, 74 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2476/5 useful/useless nets, 2264/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 9797, tnet num: 2476, tinst num: 2263, tnode num: 12378, tedge num: 15100.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2476 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 514 instances into 223 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 372 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 120 adder to BLE ...
SYN-4008 : Packed 120 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.155517s wall, 1.093750s user + 0.062500s system = 1.156250s CPU (100.1%)

RUN-1004 : used memory is 143 MB, reserved memory is 100 MB, peak memory is 167 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (255 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1679 instances
RUN-0007 : 735 luts, 711 seqs, 116 mslices, 70 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1898 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 1112 nets have 2 pins
RUN-1001 : 591 nets have [3 - 5] pins
RUN-1001 : 89 nets have [6 - 10] pins
RUN-1001 : 65 nets have [11 - 20] pins
RUN-1001 : 25 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     31      
RUN-1001 :   No   |  No   |  Yes  |     332     
RUN-1001 :   No   |  Yes  |  No   |      8      
RUN-1001 :   Yes  |  No   |  No   |      0      
RUN-1001 :   Yes  |  No   |  Yes  |     340     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 12
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1677 instances, 735 luts, 711 seqs, 186 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8530, tnet num: 1896, tinst num: 1677, tnode num: 11206, tedge num: 14073.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.144591s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (97.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 427726
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1677.
PHY-3001 : End clustering;  0.000029s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 341613, overlap = 76.5
PHY-3002 : Step(2): len = 284074, overlap = 76.5
PHY-3002 : Step(3): len = 246311, overlap = 76.5
PHY-3002 : Step(4): len = 218119, overlap = 76.5
PHY-3002 : Step(5): len = 192839, overlap = 76.5
PHY-3002 : Step(6): len = 174292, overlap = 76.5
PHY-3002 : Step(7): len = 155078, overlap = 76.5
PHY-3002 : Step(8): len = 132573, overlap = 76.5
PHY-3002 : Step(9): len = 118745, overlap = 76.8125
PHY-3002 : Step(10): len = 107215, overlap = 77.9375
PHY-3002 : Step(11): len = 90001.8, overlap = 77.5
PHY-3002 : Step(12): len = 83484.4, overlap = 78.3438
PHY-3002 : Step(13): len = 75970.1, overlap = 77.1875
PHY-3002 : Step(14): len = 72151.3, overlap = 76.5
PHY-3002 : Step(15): len = 68013.3, overlap = 77.4375
PHY-3002 : Step(16): len = 61511.8, overlap = 77.6875
PHY-3002 : Step(17): len = 57599.3, overlap = 79.4062
PHY-3002 : Step(18): len = 53284.1, overlap = 78.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.63772e-06
PHY-3002 : Step(19): len = 51614, overlap = 72.4688
PHY-3002 : Step(20): len = 51768.3, overlap = 70.1875
PHY-3002 : Step(21): len = 50350.9, overlap = 70.875
PHY-3002 : Step(22): len = 50703.3, overlap = 70.2188
PHY-3002 : Step(23): len = 49421.5, overlap = 68.3438
PHY-3002 : Step(24): len = 48309.8, overlap = 73.2188
PHY-3002 : Step(25): len = 47988.4, overlap = 70.9688
PHY-3002 : Step(26): len = 47009.9, overlap = 71.0625
PHY-3002 : Step(27): len = 46335.9, overlap = 75.5312
PHY-3002 : Step(28): len = 44988.7, overlap = 71.0625
PHY-3002 : Step(29): len = 43682.7, overlap = 71.0625
PHY-3002 : Step(30): len = 42718.8, overlap = 70.9375
PHY-3002 : Step(31): len = 42293.8, overlap = 70.3125
PHY-3002 : Step(32): len = 41598.4, overlap = 74.5312
PHY-3002 : Step(33): len = 41409.1, overlap = 69.4688
PHY-3002 : Step(34): len = 40789.7, overlap = 69.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.27543e-06
PHY-3002 : Step(35): len = 40627.5, overlap = 69.5938
PHY-3002 : Step(36): len = 40574.1, overlap = 69.7812
PHY-3002 : Step(37): len = 40427.2, overlap = 69.2812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 1.85509e-05
PHY-3002 : Step(38): len = 40447.5, overlap = 69.5
PHY-3002 : Step(39): len = 40455, overlap = 64.9062
PHY-3002 : Step(40): len = 40455, overlap = 66.7188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 3.71017e-05
PHY-3002 : Step(41): len = 40445.8, overlap = 64.375
PHY-3002 : Step(42): len = 40424.4, overlap = 64.5938
PHY-3002 : Step(43): len = 40513.2, overlap = 59.75
PHY-3002 : Step(44): len = 40520.6, overlap = 59.75
PHY-3002 : Step(45): len = 40507, overlap = 59.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005320s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (293.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049791s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00126082
PHY-3002 : Step(46): len = 44147.5, overlap = 3.875
PHY-3002 : Step(47): len = 44417, overlap = 3.5
PHY-3002 : Step(48): len = 44124.6, overlap = 2.6875
PHY-3002 : Step(49): len = 44417.6, overlap = 2.625
PHY-3002 : Step(50): len = 44245.2, overlap = 2.8125
PHY-3002 : Step(51): len = 44379.2, overlap = 3.3125
PHY-3002 : Step(52): len = 44856.9, overlap = 4
PHY-3002 : Step(53): len = 44259.7, overlap = 5.96875
PHY-3002 : Step(54): len = 44186.5, overlap = 8.5
PHY-3002 : Step(55): len = 44160.5, overlap = 10.4062
PHY-3002 : Step(56): len = 43863.4, overlap = 12.4062
PHY-3002 : Step(57): len = 43628.2, overlap = 15
PHY-3002 : Step(58): len = 43501.4, overlap = 16.4688
PHY-3002 : Step(59): len = 43066.9, overlap = 16.9062
PHY-3002 : Step(60): len = 42980.9, overlap = 17.6875
PHY-3002 : Step(61): len = 42843.9, overlap = 18.5938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047472s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.50349e-05
PHY-3002 : Step(62): len = 44644.3, overlap = 58.5938
PHY-3002 : Step(63): len = 45913.3, overlap = 52.2812
PHY-3002 : Step(64): len = 46089.2, overlap = 50.2188
PHY-3002 : Step(65): len = 46334.5, overlap = 46.2188
PHY-3002 : Step(66): len = 46478.5, overlap = 44.5938
PHY-3002 : Step(67): len = 46664, overlap = 39.7188
PHY-3002 : Step(68): len = 47019.3, overlap = 41.9062
PHY-3002 : Step(69): len = 46859.4, overlap = 43.4062
PHY-3002 : Step(70): len = 46687.6, overlap = 45.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00017007
PHY-3002 : Step(71): len = 46492.2, overlap = 44.9688
PHY-3002 : Step(72): len = 46511.4, overlap = 44.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00034014
PHY-3002 : Step(73): len = 46731, overlap = 44.8125
PHY-3002 : Step(74): len = 46826.1, overlap = 44.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000680279
PHY-3002 : Step(75): len = 47285.4, overlap = 38.3125
PHY-3002 : Step(76): len = 47525.1, overlap = 36.4375
PHY-3002 : Step(77): len = 47992.9, overlap = 35.4062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8530, tnet num: 1896, tinst num: 1677, tnode num: 11206, tedge num: 14073.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.53 peak overflow 2.34
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 59016, over cnt = 255(0%), over = 823, worst = 17
PHY-1001 : End global iterations;  0.100620s wall, 0.125000s user + 0.078125s system = 0.203125s CPU (201.9%)

PHY-1001 : Congestion index: top1 = 35.24, top5 = 23.20, top10 = 16.65, top15 = 12.37.
PHY-1001 : End incremental global routing;  0.152134s wall, 0.171875s user + 0.093750s system = 0.265625s CPU (174.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056683s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.3%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1665 has valid locations, 34 needs to be replaced
PHY-3001 : design contains 1710 instances, 735 luts, 744 seqs, 186 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 48479.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8662, tnet num: 1929, tinst num: 1710, tnode num: 11437, tedge num: 14271.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.166741s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(78): len = 48379.4, overlap = 0.9375
PHY-3002 : Step(79): len = 48379.4, overlap = 0.9375
PHY-3002 : Step(80): len = 48389.4, overlap = 0.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049936s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000707066
PHY-3002 : Step(81): len = 48468.7, overlap = 35.5312
PHY-3002 : Step(82): len = 48468.7, overlap = 35.5312
PHY-3001 : Final: Len = 48468.7, Over = 35.5312
PHY-3001 : End incremental placement;  0.298181s wall, 0.328125s user + 0.046875s system = 0.375000s CPU (125.8%)

OPT-1001 : Total overflow 87.47 peak overflow 2.34
OPT-1001 : End high-fanout net optimization;  0.546068s wall, 0.593750s user + 0.140625s system = 0.734375s CPU (134.5%)

OPT-1001 : Current memory(MB): used = 206, reserve = 162, peak = 206.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1339/1931.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 60048, over cnt = 256(0%), over = 821, worst = 17
PHY-1002 : len = 64560, over cnt = 180(0%), over = 387, worst = 8
PHY-1002 : len = 68472, over cnt = 60(0%), over = 78, worst = 4
PHY-1002 : len = 69408, over cnt = 11(0%), over = 13, worst = 3
PHY-1002 : len = 69536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118629s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (118.5%)

PHY-1001 : Congestion index: top1 = 33.02, top5 = 23.85, top10 = 17.84, top15 = 13.62.
OPT-1001 : End congestion update;  0.163066s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (115.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050240s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.3%)

OPT-0007 : Start: WNS 3709 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.213542s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (109.8%)

OPT-1001 : Current memory(MB): used = 203, reserve = 160, peak = 206.
OPT-1001 : End physical optimization;  0.897614s wall, 0.968750s user + 0.140625s system = 1.109375s CPU (123.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 735 LUT to BLE ...
SYN-4008 : Packed 735 LUT and 316 SEQ to BLE.
SYN-4003 : Packing 428 remaining SEQ's ...
SYN-4005 : Packed 213 SEQ with LUT/SLICE
SYN-4006 : 234 single LUT's are left
SYN-4006 : 215 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 950/1317 primitive instances ...
PHY-3001 : End packing;  0.052632s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 761 instances
RUN-1001 : 357 mslices, 357 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1620 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 785 nets have 2 pins
RUN-1001 : 636 nets have [3 - 5] pins
RUN-1001 : 95 nets have [6 - 10] pins
RUN-1001 : 62 nets have [11 - 20] pins
RUN-1001 : 30 nets have [21 - 99] pins
RUN-1001 : 6 nets have 100+ pins
PHY-3001 : design contains 759 instances, 714 slices, 30 macros(186 instances: 116 mslices 70 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 48889.2, Over = 45.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7399, tnet num: 1618, tinst num: 759, tnode num: 9434, tedge num: 12684.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1618 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.170635s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000110536
PHY-3002 : Step(83): len = 47710.4, overlap = 50
PHY-3002 : Step(84): len = 47002.3, overlap = 49.5
PHY-3002 : Step(85): len = 46515.6, overlap = 51.75
PHY-3002 : Step(86): len = 46266.5, overlap = 52.75
PHY-3002 : Step(87): len = 46091.5, overlap = 53
PHY-3002 : Step(88): len = 45827.6, overlap = 54.5
PHY-3002 : Step(89): len = 45439.9, overlap = 57
PHY-3002 : Step(90): len = 45280.5, overlap = 56
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000221073
PHY-3002 : Step(91): len = 45548.3, overlap = 55.25
PHY-3002 : Step(92): len = 45767.2, overlap = 50.25
PHY-3002 : Step(93): len = 46047, overlap = 50.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000442146
PHY-3002 : Step(94): len = 46734.6, overlap = 44.75
PHY-3002 : Step(95): len = 46975.9, overlap = 44.25
PHY-3002 : Step(96): len = 47279, overlap = 44.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.124038s wall, 0.109375s user + 0.171875s system = 0.281250s CPU (226.7%)

PHY-3001 : Trial Legalized: Len = 62617.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1618 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.041953s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (111.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0129176
PHY-3002 : Step(97): len = 60527.8, overlap = 4.75
PHY-3002 : Step(98): len = 59384.7, overlap = 7.5
PHY-3002 : Step(99): len = 58501.4, overlap = 8.5
PHY-3002 : Step(100): len = 56612.9, overlap = 8.5
PHY-3002 : Step(101): len = 55322.4, overlap = 10.5
PHY-3002 : Step(102): len = 54664.4, overlap = 11.5
PHY-3002 : Step(103): len = 53578.1, overlap = 14.5
PHY-3002 : Step(104): len = 52912.6, overlap = 14
PHY-3002 : Step(105): len = 52145.3, overlap = 13.5
PHY-3002 : Step(106): len = 51877.7, overlap = 14.25
PHY-3002 : Step(107): len = 51549.4, overlap = 15
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0258352
PHY-3002 : Step(108): len = 51558.2, overlap = 15
PHY-3002 : Step(109): len = 51512.3, overlap = 16.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0516705
PHY-3002 : Step(110): len = 51466.4, overlap = 16.75
PHY-3002 : Step(111): len = 51416.8, overlap = 16.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004924s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 56452.4, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005097s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 0, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 56548.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7399, tnet num: 1618, tinst num: 759, tnode num: 9434, tedge num: 12684.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 29/1620.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71920, over cnt = 242(0%), over = 413, worst = 8
PHY-1002 : len = 73752, over cnt = 135(0%), over = 190, worst = 4
PHY-1002 : len = 75336, over cnt = 26(0%), over = 43, worst = 3
PHY-1002 : len = 75552, over cnt = 12(0%), over = 14, worst = 2
PHY-1002 : len = 75760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.203158s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (146.1%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 23.20, top10 = 18.72, top15 = 15.04.
PHY-1001 : End incremental global routing;  0.260945s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (137.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1618 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054521s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.349569s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (125.2%)

OPT-1001 : Current memory(MB): used = 207, reserve = 163, peak = 207.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1411/1620.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 75760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008493s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (184.0%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 23.20, top10 = 18.72, top15 = 15.04.
OPT-1001 : End congestion update;  0.059007s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1618 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050905s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

OPT-0007 : Start: WNS 3609 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.110143s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (99.3%)

OPT-1001 : Current memory(MB): used = 210, reserve = 166, peak = 210.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1618 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049872s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (125.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1411/1620.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 75760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005747s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 23.20, top10 = 18.72, top15 = 15.04.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1618 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053239s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3609 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3609ps with logic level 6 
RUN-1001 :       #2 path slack 3665ps with logic level 6 
OPT-1001 : End physical optimization;  0.774044s wall, 0.812500s user + 0.046875s system = 0.859375s CPU (111.0%)

RUN-1003 : finish command "place" in  4.162917s wall, 6.093750s user + 1.890625s system = 7.984375s CPU (191.8%)

RUN-1004 : used memory is 199 MB, reserved memory is 155 MB, peak memory is 211 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 761 instances
RUN-1001 : 357 mslices, 357 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1620 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 785 nets have 2 pins
RUN-1001 : 636 nets have [3 - 5] pins
RUN-1001 : 95 nets have [6 - 10] pins
RUN-1001 : 62 nets have [11 - 20] pins
RUN-1001 : 30 nets have [21 - 99] pins
RUN-1001 : 6 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7399, tnet num: 1618, tinst num: 759, tnode num: 9434, tedge num: 12684.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 357 mslices, 357 lslices, 8 pads, 34 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1618 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71264, over cnt = 239(0%), over = 411, worst = 8
PHY-1002 : len = 73144, over cnt = 137(0%), over = 194, worst = 4
PHY-1002 : len = 74928, over cnt = 17(0%), over = 31, worst = 3
PHY-1002 : len = 75048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.190883s wall, 0.250000s user + 0.046875s system = 0.296875s CPU (155.5%)

PHY-1001 : Congestion index: top1 = 31.19, top5 = 23.06, top10 = 18.60, top15 = 14.95.
PHY-1001 : End global routing;  0.242377s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (141.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 226, reserve = 183, peak = 226.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 487, reserve = 447, peak = 487.
PHY-1001 : End build detailed router design. 3.110156s wall, 3.031250s user + 0.078125s system = 3.109375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31960, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.597208s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (102.0%)

PHY-1001 : Current memory(MB): used = 519, reserve = 481, peak = 519.
PHY-1001 : End phase 1; 0.602912s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (101.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 306952, over cnt = 66(0%), over = 66, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 521, reserve = 482, peak = 522.
PHY-1001 : End initial routed; 2.041441s wall, 2.937500s user + 0.046875s system = 2.984375s CPU (146.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1448(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.815   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.231226s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (94.6%)

PHY-1001 : Current memory(MB): used = 523, reserve = 483, peak = 523.
PHY-1001 : End phase 2; 2.272758s wall, 3.156250s user + 0.046875s system = 3.203125s CPU (140.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 306952, over cnt = 66(0%), over = 66, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015407s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (202.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 306560, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.062741s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 306520, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027392s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (114.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1448(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.703   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.235863s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 14 feed throughs used by 14 nets
PHY-1001 : End commit to database; 0.212206s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (103.1%)

PHY-1001 : Current memory(MB): used = 539, reserve = 499, peak = 539.
PHY-1001 : End phase 3; 0.665002s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (101.0%)

PHY-1003 : Routed, final wirelength = 306520
PHY-1001 : Current memory(MB): used = 539, reserve = 500, peak = 539.
PHY-1001 : End export database. 0.010872s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (143.7%)

PHY-1001 : End detail routing;  6.835880s wall, 7.625000s user + 0.140625s system = 7.765625s CPU (113.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7399, tnet num: 1618, tinst num: 759, tnode num: 9434, tedge num: 12684.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.535571s wall, 8.359375s user + 0.203125s system = 8.562500s CPU (113.6%)

RUN-1004 : used memory is 495 MB, reserved memory is 455 MB, peak memory is 539 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                     1133   out of  19600    5.78%
#reg                      748   out of  19600    3.82%
#le                      1348
  #lut only               600   out of   1348   44.51%
  #reg only               215   out of   1348   15.95%
  #lut&reg                533   out of   1348   39.54%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    347
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         143
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
   miso        INPUT         A4        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         A8        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         A6        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8            NONE       TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1348   |947     |186     |754     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |122    |110     |11      |63      |0       |0       |
|    usms                            |Time_1ms        |35     |30      |5       |19      |0       |0       |
|  SPIM                              |SPI_MASTER      |222    |175     |23      |130     |0       |0       |
|  uart                              |UART_Control    |156    |138     |4       |60      |0       |0       |
|    U0                              |speed_select_Tx |29     |25      |4       |18      |0       |0       |
|    U1                              |uart_tx         |24     |18      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data       |103    |95      |0       |26      |0       |0       |
|  wendu                             |DS18B20         |214    |171     |43      |74      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |602    |335     |99      |400     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |602    |335     |99      |400     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |252    |106     |0       |243     |0       |0       |
|        reg_inst                    |register        |249    |103     |0       |240     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |350    |229     |99      |157     |0       |0       |
|        bus_inst                    |bus_top         |118    |76      |42      |44      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |14     |8       |6       |5       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |28     |18      |10      |12      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |74     |48      |26      |25      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |132    |82      |29      |78      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       776   
    #2          2       362   
    #3          3       218   
    #4          4        56   
    #5        5-10      100   
    #6        11-50      84   
    #7       51-100      3    
    #8       101-500     2    
  Average     3.29            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7399, tnet num: 1618, tinst num: 759, tnode num: 9434, tedge num: 12684.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1618 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 759
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1620, pip num: 19172
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 14
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1806 valid insts, and 50734 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011001110101111100011010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.732174s wall, 24.312500s user + 0.140625s system = 24.453125s CPU (655.2%)

RUN-1004 : used memory is 513 MB, reserved memory is 478 MB, peak memory is 657 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250210_154440.log"
