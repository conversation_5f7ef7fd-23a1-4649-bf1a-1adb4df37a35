============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 15:10:16 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/DA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0111110101101110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3073/12 useful/useless nets, 1867/4 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1016 : Merged 13 instances.
SYN-1032 : 2829/16 useful/useless nets, 2189/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1017 : Remove 1 const input seq instances
SYN-1002 :     cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[0]$bus_nodes/reg0_syn_10
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 276 better
SYN-1014 : Optimize round 2
SYN-1032 : 2597/30 useful/useless nets, 1957/32 useful/useless insts
SYN-1001 : Optimize 1 less-than instances
SYN-1015 : Optimize round 2, 79 better
SYN-1032 : 2596/1 useful/useless nets, 1956/0 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 1 better
SYN-1014 : Optimize round 2
SYN-1015 : Optimize round 2, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2619/157 useful/useless nets, 2002/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 12 instances.
SYN-2501 : Optimize round 1, 68 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 13 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 2971/5 useful/useless nets, 2354/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10827, tnet num: 2971, tinst num: 2353, tnode num: 14497, tedge num: 17500.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2971 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 158 (3.68), #lev = 8 (1.86)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 164 (3.61), #lev = 7 (2.15)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 368 instances into 164 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 270 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 96 adder to BLE ...
SYN-4008 : Packed 96 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.856027s wall, 1.796875s user + 0.062500s system = 1.859375s CPU (100.2%)

RUN-1004 : used memory is 166 MB, reserved memory is 126 MB, peak memory is 195 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (185 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1984 instances
RUN-0007 : 531 luts, 1098 seqs, 181 mslices, 101 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2603 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1800 nets have 2 pins
RUN-1001 : 633 nets have [3 - 5] pins
RUN-1001 : 101 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 37 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     192     
RUN-1001 :   No   |  No   |  Yes  |     211     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     275     
RUN-1001 :   Yes  |  No   |  Yes  |     310     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  15   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 25
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1982 instances, 531 luts, 1098 seqs, 282 slices, 34 macros(282 instances: 181 mslices 101 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10035, tnet num: 2601, tinst num: 1982, tnode num: 13903, tedge num: 17074.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2601 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.402385s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (101.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 683736
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1982.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 594076, overlap = 67.5
PHY-3002 : Step(2): len = 553522, overlap = 65.25
PHY-3002 : Step(3): len = 531614, overlap = 72
PHY-3002 : Step(4): len = 510455, overlap = 65.25
PHY-3002 : Step(5): len = 492858, overlap = 72
PHY-3002 : Step(6): len = 464152, overlap = 67.5
PHY-3002 : Step(7): len = 454986, overlap = 69.75
PHY-3002 : Step(8): len = 445485, overlap = 65.25
PHY-3002 : Step(9): len = 435643, overlap = 65.25
PHY-3002 : Step(10): len = 425525, overlap = 63
PHY-3002 : Step(11): len = 412295, overlap = 65.25
PHY-3002 : Step(12): len = 403232, overlap = 65.25
PHY-3002 : Step(13): len = 393764, overlap = 63
PHY-3002 : Step(14): len = 383597, overlap = 63
PHY-3002 : Step(15): len = 372866, overlap = 63
PHY-3002 : Step(16): len = 364374, overlap = 63
PHY-3002 : Step(17): len = 353944, overlap = 65.25
PHY-3002 : Step(18): len = 345091, overlap = 65.25
PHY-3002 : Step(19): len = 336876, overlap = 65.25
PHY-3002 : Step(20): len = 327196, overlap = 65.25
PHY-3002 : Step(21): len = 319032, overlap = 65.25
PHY-3002 : Step(22): len = 313659, overlap = 65.25
PHY-3002 : Step(23): len = 300854, overlap = 60.75
PHY-3002 : Step(24): len = 293148, overlap = 63
PHY-3002 : Step(25): len = 288732, overlap = 63
PHY-3002 : Step(26): len = 279615, overlap = 67.5
PHY-3002 : Step(27): len = 266154, overlap = 67.5
PHY-3002 : Step(28): len = 262286, overlap = 67.5
PHY-3002 : Step(29): len = 256143, overlap = 67.5
PHY-3002 : Step(30): len = 239639, overlap = 69.75
PHY-3002 : Step(31): len = 230586, overlap = 72
PHY-3002 : Step(32): len = 228200, overlap = 72
PHY-3002 : Step(33): len = 204945, overlap = 72
PHY-3002 : Step(34): len = 192523, overlap = 72
PHY-3002 : Step(35): len = 190362, overlap = 72
PHY-3002 : Step(36): len = 187343, overlap = 72
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.99864e-05
PHY-3002 : Step(37): len = 186804, overlap = 72
PHY-3002 : Step(38): len = 184897, overlap = 72
PHY-3002 : Step(39): len = 181485, overlap = 65.25
PHY-3002 : Step(40): len = 174165, overlap = 69.75
PHY-3002 : Step(41): len = 171716, overlap = 69.75
PHY-3002 : Step(42): len = 169359, overlap = 65.25
PHY-3002 : Step(43): len = 166486, overlap = 65.25
PHY-3002 : Step(44): len = 162535, overlap = 67.5
PHY-3002 : Step(45): len = 152637, overlap = 65.25
PHY-3002 : Step(46): len = 148657, overlap = 63
PHY-3002 : Step(47): len = 146575, overlap = 63
PHY-3002 : Step(48): len = 139092, overlap = 69.75
PHY-3002 : Step(49): len = 137794, overlap = 69.75
PHY-3002 : Step(50): len = 134632, overlap = 69.75
PHY-3002 : Step(51): len = 131973, overlap = 60.75
PHY-3002 : Step(52): len = 127837, overlap = 60.75
PHY-3002 : Step(53): len = 125190, overlap = 65.25
PHY-3002 : Step(54): len = 122534, overlap = 60.75
PHY-3002 : Step(55): len = 118375, overlap = 65.25
PHY-3002 : Step(56): len = 116478, overlap = 65.25
PHY-3002 : Step(57): len = 115013, overlap = 58.5
PHY-3002 : Step(58): len = 109929, overlap = 60.75
PHY-3002 : Step(59): len = 109056, overlap = 56.25
PHY-3002 : Step(60): len = 107873, overlap = 56.25
PHY-3002 : Step(61): len = 106757, overlap = 56.25
PHY-3002 : Step(62): len = 106154, overlap = 56.25
PHY-3002 : Step(63): len = 104994, overlap = 57.25
PHY-3002 : Step(64): len = 101585, overlap = 57.5938
PHY-3002 : Step(65): len = 100101, overlap = 57.4375
PHY-3002 : Step(66): len = 98323.4, overlap = 57.125
PHY-3002 : Step(67): len = 96121, overlap = 56.875
PHY-3002 : Step(68): len = 93967.3, overlap = 56.75
PHY-3002 : Step(69): len = 89779.6, overlap = 56.4375
PHY-3002 : Step(70): len = 88399.4, overlap = 56.25
PHY-3002 : Step(71): len = 86582.2, overlap = 56.25
PHY-3002 : Step(72): len = 85340.5, overlap = 56.25
PHY-3002 : Step(73): len = 83254.4, overlap = 56.25
PHY-3002 : Step(74): len = 79539.7, overlap = 54
PHY-3002 : Step(75): len = 78344.6, overlap = 56.25
PHY-3002 : Step(76): len = 77669.3, overlap = 54
PHY-3002 : Step(77): len = 76741.5, overlap = 56.3125
PHY-3002 : Step(78): len = 75891.2, overlap = 56.4375
PHY-3002 : Step(79): len = 74481, overlap = 58.8125
PHY-3002 : Step(80): len = 73359.9, overlap = 56.6875
PHY-3002 : Step(81): len = 71465.2, overlap = 52.6875
PHY-3002 : Step(82): len = 70472.6, overlap = 52.5625
PHY-3002 : Step(83): len = 70037.4, overlap = 54.875
PHY-3002 : Step(84): len = 69619.8, overlap = 52.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 3.99728e-05
PHY-3002 : Step(85): len = 69469.8, overlap = 52.75
PHY-3002 : Step(86): len = 69618.2, overlap = 50.3125
PHY-3002 : Step(87): len = 69712, overlap = 50.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 7.99456e-05
PHY-3002 : Step(88): len = 69778.1, overlap = 50.25
PHY-3002 : Step(89): len = 69925.3, overlap = 50.25
PHY-3002 : Step(90): len = 70354.9, overlap = 50.25
PHY-3002 : Step(91): len = 70126.8, overlap = 50.25
PHY-3002 : Step(92): len = 70244.1, overlap = 50.3125
PHY-3002 : Step(93): len = 70498.7, overlap = 52.5625
PHY-3002 : Step(94): len = 70496.3, overlap = 52.5625
PHY-3002 : Step(95): len = 70469.6, overlap = 52.5625
PHY-3002 : Step(96): len = 70344.4, overlap = 52.4375
PHY-3002 : Step(97): len = 70268.1, overlap = 52.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000159891
PHY-3002 : Step(98): len = 70573.3, overlap = 52.375
PHY-3002 : Step(99): len = 70766.6, overlap = 52.3125
PHY-3002 : Step(100): len = 71126.6, overlap = 52.4375
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.000319782
PHY-3002 : Step(101): len = 71666.6, overlap = 52.375
PHY-3002 : Step(102): len = 71940.1, overlap = 52.3125
PHY-3002 : Step(103): len = 71941.9, overlap = 52.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011623s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (134.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2601 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.098640s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (95.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(104): len = 81181.8, overlap = 20.125
PHY-3002 : Step(105): len = 81402.9, overlap = 20.25
PHY-3002 : Step(106): len = 80558.4, overlap = 17.25
PHY-3002 : Step(107): len = 80234.2, overlap = 18.125
PHY-3002 : Step(108): len = 80413.2, overlap = 18.3125
PHY-3002 : Step(109): len = 78956.4, overlap = 17.5
PHY-3002 : Step(110): len = 77908.5, overlap = 21.9062
PHY-3002 : Step(111): len = 77059.1, overlap = 24.6562
PHY-3002 : Step(112): len = 74743.1, overlap = 27.5312
PHY-3002 : Step(113): len = 74114.9, overlap = 28.7188
PHY-3002 : Step(114): len = 73183.7, overlap = 28
PHY-3002 : Step(115): len = 72401.6, overlap = 28.5312
PHY-3002 : Step(116): len = 72259.4, overlap = 28.5938
PHY-3002 : Step(117): len = 71221.2, overlap = 29.9375
PHY-3002 : Step(118): len = 70852.7, overlap = 29.9688
PHY-3002 : Step(119): len = 70724.5, overlap = 29.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00174314
PHY-3002 : Step(120): len = 70134.3, overlap = 29.375
PHY-3002 : Step(121): len = 70180.4, overlap = 28.875
PHY-3002 : Step(122): len = 69507.3, overlap = 29.125
PHY-3002 : Step(123): len = 69161, overlap = 25.4688
PHY-3002 : Step(124): len = 67673.9, overlap = 27.1875
PHY-3002 : Step(125): len = 66794.5, overlap = 28.7188
PHY-3002 : Step(126): len = 65311.7, overlap = 29.7188
PHY-3002 : Step(127): len = 64619.2, overlap = 30
PHY-3002 : Step(128): len = 63585, overlap = 31.8125
PHY-3002 : Step(129): len = 63767.8, overlap = 30.4375
PHY-3002 : Step(130): len = 63224.6, overlap = 32.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2601 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.097806s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (95.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.93228e-05
PHY-3002 : Step(131): len = 63035.6, overlap = 91.3125
PHY-3002 : Step(132): len = 64211.6, overlap = 87
PHY-3002 : Step(133): len = 66144.7, overlap = 73.5312
PHY-3002 : Step(134): len = 66447.6, overlap = 73.25
PHY-3002 : Step(135): len = 65976.6, overlap = 72.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.86455e-05
PHY-3002 : Step(136): len = 66015, overlap = 71.0938
PHY-3002 : Step(137): len = 66760.8, overlap = 60.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000176148
PHY-3002 : Step(138): len = 66873, overlap = 59.5938
PHY-3002 : Step(139): len = 68913.6, overlap = 47.875
PHY-3002 : Step(140): len = 69455.2, overlap = 45.4062
PHY-3002 : Step(141): len = 69883.4, overlap = 43.5
PHY-3002 : Step(142): len = 70222.7, overlap = 43.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10035, tnet num: 2601, tinst num: 1982, tnode num: 13903, tedge num: 17074.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 118.81 peak overflow 3.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2603.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 79560, over cnt = 332(0%), over = 1329, worst = 21
PHY-1001 : End global iterations;  0.143293s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (109.0%)

PHY-1001 : Congestion index: top1 = 46.77, top5 = 29.89, top10 = 20.90, top15 = 15.48.
PHY-1001 : End incremental global routing;  0.216822s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (108.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2601 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.104454s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (104.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.373916s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (104.5%)

OPT-1001 : Current memory(MB): used = 230, reserve = 187, peak = 230.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1942/2603.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 79560, over cnt = 332(0%), over = 1329, worst = 21
PHY-1002 : len = 88408, over cnt = 248(0%), over = 647, worst = 15
PHY-1002 : len = 91680, over cnt = 112(0%), over = 331, worst = 14
PHY-1002 : len = 94312, over cnt = 14(0%), over = 28, worst = 5
PHY-1002 : len = 94304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.219453s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (142.4%)

PHY-1001 : Congestion index: top1 = 41.79, top5 = 29.06, top10 = 22.01, top15 = 17.30.
OPT-1001 : End congestion update;  0.291147s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (134.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2601 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.092286s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (101.6%)

OPT-0007 : Start: WNS -2597 TNS -52624 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2597 TNS -52624 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.388686s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (124.6%)

OPT-1001 : Current memory(MB): used = 234, reserve = 192, peak = 234.
OPT-1001 : End physical optimization;  1.183583s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (109.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 531 LUT to BLE ...
SYN-4008 : Packed 531 LUT and 228 SEQ to BLE.
SYN-4003 : Packing 870 remaining SEQ's ...
SYN-4005 : Packed 217 SEQ with LUT/SLICE
SYN-4006 : 123 single LUT's are left
SYN-4006 : 653 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1184/1573 primitive instances ...
PHY-3001 : End packing;  0.080545s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 975 instances
RUN-1001 : 451 mslices, 451 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2384 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1582 nets have 2 pins
RUN-1001 : 632 nets have [3 - 5] pins
RUN-1001 : 102 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
PHY-3001 : design contains 973 instances, 902 slices, 34 macros(282 instances: 181 mslices 101 lslices)
PHY-3001 : Cell area utilization is 11%
PHY-3001 : After packing: Len = 70761.6, Over = 64
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8512, tnet num: 2382, tinst num: 973, tnode num: 11288, tedge num: 14984.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.415122s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.97723e-05
PHY-3002 : Step(143): len = 70107.2, overlap = 65
PHY-3002 : Step(144): len = 69279.3, overlap = 63
PHY-3002 : Step(145): len = 68976.7, overlap = 65.25
PHY-3002 : Step(146): len = 69156, overlap = 65.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.95447e-05
PHY-3002 : Step(147): len = 69289.9, overlap = 66.5
PHY-3002 : Step(148): len = 69751.9, overlap = 64.75
PHY-3002 : Step(149): len = 70114.9, overlap = 64.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000119089
PHY-3002 : Step(150): len = 70666.8, overlap = 61.25
PHY-3002 : Step(151): len = 71121.1, overlap = 62
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.156675s wall, 0.140625s user + 0.203125s system = 0.343750s CPU (219.4%)

PHY-3001 : Trial Legalized: Len = 88262
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.086684s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (108.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000791999
PHY-3002 : Step(152): len = 83667.6, overlap = 6.5
PHY-3002 : Step(153): len = 81556.5, overlap = 16.25
PHY-3002 : Step(154): len = 79740.8, overlap = 22.5
PHY-3002 : Step(155): len = 78422.2, overlap = 28.5
PHY-3002 : Step(156): len = 77903.5, overlap = 30.75
PHY-3002 : Step(157): len = 77423.9, overlap = 33.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.001584
PHY-3002 : Step(158): len = 77611.9, overlap = 32.5
PHY-3002 : Step(159): len = 77704.2, overlap = 31
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.003168
PHY-3002 : Step(160): len = 77740.5, overlap = 32.25
PHY-3002 : Step(161): len = 77740.5, overlap = 32.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007577s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (206.2%)

PHY-3001 : Legalized: Len = 83167.9, Over = 0
PHY-3001 : Spreading special nets. 24 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.010964s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (142.5%)

PHY-3001 : 36 instances has been re-located, deltaX = 22, deltaY = 25, maxDist = 3.
PHY-3001 : Final: Len = 83989.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8512, tnet num: 2382, tinst num: 973, tnode num: 11288, tedge num: 14984.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 77/2384.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 98440, over cnt = 242(0%), over = 432, worst = 8
PHY-1002 : len = 99984, over cnt = 181(0%), over = 271, worst = 5
PHY-1002 : len = 102848, over cnt = 27(0%), over = 33, worst = 3
PHY-1002 : len = 103152, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 103200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.267859s wall, 0.343750s user + 0.078125s system = 0.421875s CPU (157.5%)

PHY-1001 : Congestion index: top1 = 35.45, top5 = 27.25, top10 = 22.09, top15 = 18.30.
PHY-1001 : End incremental global routing;  0.349409s wall, 0.437500s user + 0.078125s system = 0.515625s CPU (147.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.098303s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (95.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.495715s wall, 0.578125s user + 0.078125s system = 0.656250s CPU (132.4%)

OPT-1001 : Current memory(MB): used = 238, reserve = 196, peak = 238.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2092/2384.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 103200, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010473s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (149.2%)

PHY-1001 : Congestion index: top1 = 35.45, top5 = 27.25, top10 = 22.09, top15 = 18.30.
OPT-1001 : End congestion update;  0.073747s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.075798s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.1%)

OPT-0007 : Start: WNS -2597 TNS -52274 NUM_FEPS 28
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 934 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 973 instances, 902 slices, 34 macros(282 instances: 181 mslices 101 lslices)
PHY-3001 : Cell area utilization is 11%
PHY-3001 : Initial: Len = 84030.6, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007851s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 1, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 84030.6, Over = 0
PHY-3001 : End incremental legalization;  0.066799s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.6%)

OPT-0007 : Iter 1: improved WNS -2597 TNS -52274 NUM_FEPS 28 with 2 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS -2597 TNS -52274 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.239529s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (130.5%)

OPT-1001 : Current memory(MB): used = 240, reserve = 198, peak = 240.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073079s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2088/2384.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 103232, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 103216, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 103232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.036406s wall, 0.062500s user + 0.031250s system = 0.093750s CPU (257.5%)

PHY-1001 : Congestion index: top1 = 35.45, top5 = 27.23, top10 = 22.08, top15 = 18.29.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.101730s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -2597 TNS -52274 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 35.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -2597ps with logic level 2 
RUN-1001 :       #2 path slack -2597ps with logic level 2 
RUN-1001 :       #3 path slack -2597ps with logic level 2 
RUN-1001 :       #4 path slack -2597ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2384 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2384 nets
OPT-1001 : End physical optimization;  1.482703s wall, 1.656250s user + 0.125000s system = 1.781250s CPU (120.1%)

RUN-1003 : finish command "place" in  9.188225s wall, 16.140625s user + 4.656250s system = 20.796875s CPU (226.3%)

RUN-1004 : used memory is 228 MB, reserved memory is 184 MB, peak memory is 242 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 975 instances
RUN-1001 : 451 mslices, 451 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2384 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1582 nets have 2 pins
RUN-1001 : 632 nets have [3 - 5] pins
RUN-1001 : 102 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8512, tnet num: 2382, tinst num: 973, tnode num: 11288, tedge num: 14984.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 451 mslices, 451 lslices, 34 pads, 28 brams, 4 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2382 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 97336, over cnt = 245(0%), over = 437, worst = 8
PHY-1002 : len = 99656, over cnt = 131(0%), over = 163, worst = 4
PHY-1002 : len = 101032, over cnt = 47(0%), over = 59, worst = 4
PHY-1002 : len = 101688, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 101736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.270571s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (121.3%)

PHY-1001 : Congestion index: top1 = 35.52, top5 = 26.92, top10 = 21.79, top15 = 18.06.
PHY-1001 : End global routing;  0.352742s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (115.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 255, reserve = 212, peak = 269.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 515, reserve = 477, peak = 515.
PHY-1001 : End build detailed router design. 4.461404s wall, 4.375000s user + 0.062500s system = 4.437500s CPU (99.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33544, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.431755s wall, 2.421875s user + 0.000000s system = 2.421875s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 547, reserve = 510, peak = 547.
PHY-1001 : End phase 1; 2.437844s wall, 2.437500s user + 0.000000s system = 2.437500s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 28% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 87% nets.
PHY-1022 : len = 298248, over cnt = 55(0%), over = 55, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 549, reserve = 511, peak = 550.
PHY-1001 : End initial routed; 3.797938s wall, 4.765625s user + 0.156250s system = 4.921875s CPU (129.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2099(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.417   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.268   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.551213s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (96.4%)

PHY-1001 : Current memory(MB): used = 550, reserve = 512, peak = 550.
PHY-1001 : End phase 2; 4.349256s wall, 5.296875s user + 0.156250s system = 5.453125s CPU (125.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 298248, over cnt = 55(0%), over = 55, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.021239s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (147.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 297736, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.079201s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 297848, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.042171s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (111.2%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 297864, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.027494s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2099(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.417   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.268   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.565428s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.322657s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.7%)

PHY-1001 : Current memory(MB): used = 567, reserve = 530, peak = 567.
PHY-1001 : End phase 3; 1.258378s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (100.6%)

PHY-1003 : Routed, final wirelength = 297864
PHY-1001 : Current memory(MB): used = 568, reserve = 530, peak = 568.
PHY-1001 : End export database. 0.012615s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (123.9%)

PHY-1001 : End detail routing;  12.774971s wall, 13.640625s user + 0.218750s system = 13.859375s CPU (108.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8512, tnet num: 2382, tinst num: 973, tnode num: 11288, tedge num: 14984.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8540, tnet num: 2396, tinst num: 987, tnode num: 11316, tedge num: 15012.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  4.673440s wall, 4.859375s user + 0.140625s system = 5.000000s CPU (107.0%)

RUN-1003 : finish command "route" in  18.326240s wall, 19.421875s user + 0.375000s system = 19.796875s CPU (108.0%)

RUN-1004 : used memory is 547 MB, reserved memory is 509 MB, peak memory is 568 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1127   out of  19600    5.75%
#reg                     1172   out of  19600    5.98%
#le                      1780
  #lut only               608   out of   1780   34.16%
  #reg only               653   out of   1780   36.69%
  #lut&reg                519   out of   1780   29.16%
#dsp                        4   out of     29   13.79%
#bram                      28   out of     64   43.75%
  #bram9k                  28
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       379
#2        config_inst_syn_9               GCLK               config             config_inst.jtck            100
#3        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       99
#4        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di             70
#5        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_11.q0    41
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       NONE    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       NONE    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       NONE    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       NONE    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       NONE    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       NONE    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       NONE    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       NONE    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       NONE    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       NONE    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       NONE    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       NONE    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       NONE    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       NONE    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1780   |845     |282     |1189    |28      |4       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |968    |296     |114     |738     |4       |4       |
|    ctrl_signal                     |SignalGenerator                                  |34     |28      |6       |20      |0       |0       |
|    demodu                          |Demodulation                                     |459    |115     |44      |347     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |53     |29      |6       |45      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |6       |0       |17      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |10      |0       |15      |0       |0       |
|    integ                           |Integration                                      |137    |22      |14      |110     |0       |0       |
|    rs422                           |Rs422Output                                      |308    |106     |45      |242     |0       |4       |
|    trans                           |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                            |UART_Control                                     |129    |121     |7       |62      |0       |0       |
|    U0                              |speed_select_Tx                                  |28     |21      |7       |16      |0       |0       |
|    U1                              |uart_tx                                          |21     |20      |0       |18      |0       |0       |
|    U2                              |Ctrl_Data                                        |80     |80      |0       |28      |0       |0       |
|  wendu                             |DS18B20                                          |192    |147     |45      |79      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |424    |247     |83      |270     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |424    |247     |83      |270     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |178    |98      |0       |177     |0       |0       |
|        reg_inst                    |register                                         |176    |96      |0       |175     |0       |0       |
|        tap_inst                    |tap                                              |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger                                          |246    |149     |83      |93      |0       |0       |
|        bus_inst                    |bus_top                                          |56     |30      |22      |13      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |16     |8       |8       |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |40     |22      |14      |12      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |109    |80      |29      |58      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1561  
    #2          2       439   
    #3          3       177   
    #4          4        16   
    #5        5-10      107   
    #6        11-50      51   
    #7       51-100      2    
    #8       101-500     1    
  Average     2.30            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8540, tnet num: 2396, tinst num: 987, tnode num: 11316, tedge num: 15012.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2396 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 8c0bef7d7f4a23ca399149266bad52a96c0817f4101cbf2ed9a6d4b976597300 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 987
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2398, pip num: 20974
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1553 valid insts, and 54962 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000111110101101110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.635393s wall, 30.437500s user + 0.187500s system = 30.625000s CPU (543.4%)

RUN-1004 : used memory is 545 MB, reserved memory is 508 MB, peak memory is 692 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231129_151016.log"
