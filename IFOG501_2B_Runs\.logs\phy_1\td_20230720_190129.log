============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jul 20 19:01:29 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 20 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1609 instances
RUN-0007 : 368 luts, 992 seqs, 128 mslices, 72 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2135 nets
RUN-1001 : 1604 nets have 2 pins
RUN-1001 : 416 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1607 instances, 368 luts, 992 seqs, 200 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7614, tnet num: 2133, tinst num: 1607, tnode num: 10810, tedge num: 12816.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.275280s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (102.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 567662
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1607.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 476331, overlap = 20.25
PHY-3002 : Step(2): len = 443489, overlap = 20.25
PHY-3002 : Step(3): len = 425826, overlap = 20.25
PHY-3002 : Step(4): len = 409897, overlap = 18
PHY-3002 : Step(5): len = 400641, overlap = 18
PHY-3002 : Step(6): len = 389102, overlap = 18
PHY-3002 : Step(7): len = 379549, overlap = 20.25
PHY-3002 : Step(8): len = 367244, overlap = 20.25
PHY-3002 : Step(9): len = 357655, overlap = 20.25
PHY-3002 : Step(10): len = 347263, overlap = 18
PHY-3002 : Step(11): len = 337670, overlap = 18
PHY-3002 : Step(12): len = 328041, overlap = 18
PHY-3002 : Step(13): len = 318972, overlap = 20.25
PHY-3002 : Step(14): len = 308313, overlap = 20.25
PHY-3002 : Step(15): len = 300769, overlap = 20.25
PHY-3002 : Step(16): len = 290659, overlap = 20.25
PHY-3002 : Step(17): len = 282488, overlap = 20.25
PHY-3002 : Step(18): len = 273291, overlap = 20.25
PHY-3002 : Step(19): len = 266185, overlap = 20.25
PHY-3002 : Step(20): len = 257265, overlap = 20.25
PHY-3002 : Step(21): len = 250919, overlap = 20.25
PHY-3002 : Step(22): len = 243423, overlap = 20.25
PHY-3002 : Step(23): len = 236140, overlap = 20.25
PHY-3002 : Step(24): len = 230401, overlap = 20.25
PHY-3002 : Step(25): len = 225478, overlap = 20.25
PHY-3002 : Step(26): len = 219504, overlap = 20.25
PHY-3002 : Step(27): len = 213829, overlap = 20.25
PHY-3002 : Step(28): len = 208812, overlap = 20.25
PHY-3002 : Step(29): len = 204464, overlap = 20.25
PHY-3002 : Step(30): len = 200298, overlap = 20.25
PHY-3002 : Step(31): len = 195904, overlap = 20.25
PHY-3002 : Step(32): len = 189879, overlap = 20.25
PHY-3002 : Step(33): len = 185961, overlap = 20.25
PHY-3002 : Step(34): len = 182012, overlap = 20.25
PHY-3002 : Step(35): len = 178145, overlap = 20.25
PHY-3002 : Step(36): len = 174329, overlap = 20.25
PHY-3002 : Step(37): len = 169044, overlap = 20.25
PHY-3002 : Step(38): len = 164094, overlap = 20.25
PHY-3002 : Step(39): len = 162106, overlap = 20.25
PHY-3002 : Step(40): len = 156927, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0001341
PHY-3002 : Step(41): len = 158004, overlap = 15.75
PHY-3002 : Step(42): len = 156284, overlap = 15.75
PHY-3002 : Step(43): len = 154119, overlap = 15.75
PHY-3002 : Step(44): len = 151939, overlap = 15.75
PHY-3002 : Step(45): len = 143451, overlap = 9
PHY-3002 : Step(46): len = 139612, overlap = 11.25
PHY-3002 : Step(47): len = 135667, overlap = 11.25
PHY-3002 : Step(48): len = 135357, overlap = 11.25
PHY-3002 : Step(49): len = 130829, overlap = 11.25
PHY-3002 : Step(50): len = 127154, overlap = 11.25
PHY-3002 : Step(51): len = 124076, overlap = 11.25
PHY-3002 : Step(52): len = 121919, overlap = 9
PHY-3002 : Step(53): len = 117767, overlap = 11.25
PHY-3002 : Step(54): len = 115976, overlap = 11.25
PHY-3002 : Step(55): len = 113528, overlap = 11.25
PHY-3002 : Step(56): len = 111537, overlap = 11.25
PHY-3002 : Step(57): len = 108861, overlap = 11.25
PHY-3002 : Step(58): len = 107908, overlap = 9
PHY-3002 : Step(59): len = 105937, overlap = 11.25
PHY-3002 : Step(60): len = 102680, overlap = 15.75
PHY-3002 : Step(61): len = 100226, overlap = 13.5
PHY-3002 : Step(62): len = 99887.1, overlap = 11.25
PHY-3002 : Step(63): len = 97875.1, overlap = 11.25
PHY-3002 : Step(64): len = 96196.2, overlap = 11.25
PHY-3002 : Step(65): len = 94231.8, overlap = 11.25
PHY-3002 : Step(66): len = 93527.9, overlap = 11.25
PHY-3002 : Step(67): len = 91395.3, overlap = 11.25
PHY-3002 : Step(68): len = 88780.9, overlap = 9
PHY-3002 : Step(69): len = 87575.8, overlap = 9
PHY-3002 : Step(70): len = 87268.3, overlap = 9
PHY-3002 : Step(71): len = 84951.4, overlap = 9
PHY-3002 : Step(72): len = 84181.5, overlap = 11.25
PHY-3002 : Step(73): len = 82683.5, overlap = 11.25
PHY-3002 : Step(74): len = 81014, overlap = 11.25
PHY-3002 : Step(75): len = 79667.9, overlap = 9
PHY-3002 : Step(76): len = 79123.4, overlap = 11.25
PHY-3002 : Step(77): len = 76767.8, overlap = 9
PHY-3002 : Step(78): len = 76189.4, overlap = 9
PHY-3002 : Step(79): len = 74550.5, overlap = 11.25
PHY-3002 : Step(80): len = 74041.7, overlap = 9
PHY-3002 : Step(81): len = 72572.5, overlap = 11.25
PHY-3002 : Step(82): len = 71565.4, overlap = 11.25
PHY-3002 : Step(83): len = 69566.8, overlap = 11.25
PHY-3002 : Step(84): len = 69177.3, overlap = 11.25
PHY-3002 : Step(85): len = 67746.6, overlap = 11.25
PHY-3002 : Step(86): len = 64861.2, overlap = 11.25
PHY-3002 : Step(87): len = 63539, overlap = 11.25
PHY-3002 : Step(88): len = 62406.7, overlap = 11.25
PHY-3002 : Step(89): len = 61515.9, overlap = 11.25
PHY-3002 : Step(90): len = 61208, overlap = 11.25
PHY-3002 : Step(91): len = 61193.3, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000268199
PHY-3002 : Step(92): len = 61212, overlap = 11.25
PHY-3002 : Step(93): len = 61305.9, overlap = 13.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000536398
PHY-3002 : Step(94): len = 61269.4, overlap = 9
PHY-3002 : Step(95): len = 61338.2, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007296s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (214.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061526s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(96): len = 64991.8, overlap = 1.875
PHY-3002 : Step(97): len = 63824.1, overlap = 2.4375
PHY-3002 : Step(98): len = 62468.4, overlap = 2.75
PHY-3002 : Step(99): len = 60453.7, overlap = 2.375
PHY-3002 : Step(100): len = 59720.3, overlap = 2.25
PHY-3002 : Step(101): len = 58469.7, overlap = 2.4375
PHY-3002 : Step(102): len = 57427.7, overlap = 1.75
PHY-3002 : Step(103): len = 56475.3, overlap = 1.625
PHY-3002 : Step(104): len = 55048, overlap = 1.9375
PHY-3002 : Step(105): len = 53972.7, overlap = 4.625
PHY-3002 : Step(106): len = 53683.3, overlap = 6.75
PHY-3002 : Step(107): len = 53146, overlap = 6.75
PHY-3002 : Step(108): len = 52851.7, overlap = 7.375
PHY-3002 : Step(109): len = 52179.8, overlap = 7.6875
PHY-3002 : Step(110): len = 51887.7, overlap = 8.3125
PHY-3002 : Step(111): len = 51851.5, overlap = 8.65625
PHY-3002 : Step(112): len = 51022.7, overlap = 8.0625
PHY-3002 : Step(113): len = 50370.1, overlap = 8.125
PHY-3002 : Step(114): len = 49421.7, overlap = 9.625
PHY-3002 : Step(115): len = 49221.2, overlap = 10.6875
PHY-3002 : Step(116): len = 48850.7, overlap = 10.75
PHY-3002 : Step(117): len = 48609.1, overlap = 9.875
PHY-3002 : Step(118): len = 48192.7, overlap = 10.0625
PHY-3002 : Step(119): len = 47888.6, overlap = 10.25
PHY-3002 : Step(120): len = 47586.1, overlap = 10.4375
PHY-3002 : Step(121): len = 47620.8, overlap = 10.4375
PHY-3002 : Step(122): len = 47435.9, overlap = 10.5
PHY-3002 : Step(123): len = 47422.3, overlap = 11.875
PHY-3002 : Step(124): len = 47158.7, overlap = 13.1875
PHY-3002 : Step(125): len = 46764.7, overlap = 15.125
PHY-3002 : Step(126): len = 46743.2, overlap = 15.3438
PHY-3002 : Step(127): len = 46486.1, overlap = 16
PHY-3002 : Step(128): len = 46486.1, overlap = 16
PHY-3002 : Step(129): len = 46430.7, overlap = 16
PHY-3002 : Step(130): len = 46454.2, overlap = 16
PHY-3002 : Step(131): len = 46454.2, overlap = 16
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058088s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.56804e-05
PHY-3002 : Step(132): len = 47064.8, overlap = 55.7812
PHY-3002 : Step(133): len = 47791.7, overlap = 56.0625
PHY-3002 : Step(134): len = 47381, overlap = 56.6875
PHY-3002 : Step(135): len = 47363.8, overlap = 56.4062
PHY-3002 : Step(136): len = 47492.3, overlap = 56.625
PHY-3002 : Step(137): len = 47219.7, overlap = 56.9375
PHY-3002 : Step(138): len = 47579.5, overlap = 49.2188
PHY-3002 : Step(139): len = 47933.7, overlap = 48.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000171361
PHY-3002 : Step(140): len = 47535.6, overlap = 48.2188
PHY-3002 : Step(141): len = 48009.1, overlap = 48.4688
PHY-3002 : Step(142): len = 48632, overlap = 47.9688
PHY-3002 : Step(143): len = 48761.9, overlap = 43.1562
PHY-3002 : Step(144): len = 49094.5, overlap = 41.8125
PHY-3002 : Step(145): len = 49365, overlap = 41.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000342722
PHY-3002 : Step(146): len = 49275.6, overlap = 41.5625
PHY-3002 : Step(147): len = 49600.1, overlap = 37.2188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000685443
PHY-3002 : Step(148): len = 50095.8, overlap = 35.6562
PHY-3002 : Step(149): len = 50727.8, overlap = 28.6875
PHY-3002 : Step(150): len = 51137.2, overlap = 27.2812
PHY-3002 : Step(151): len = 50883.8, overlap = 27.4375
PHY-3002 : Step(152): len = 50684.5, overlap = 26.8125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7614, tnet num: 2133, tinst num: 1607, tnode num: 10810, tedge num: 12816.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 78.03 peak overflow 2.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2135.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52968, over cnt = 232(0%), over = 941, worst = 15
PHY-1001 : End global iterations;  0.054872s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.9%)

PHY-1001 : Congestion index: top1 = 40.26, top5 = 24.07, top10 = 15.44, top15 = 10.94.
PHY-1001 : End incremental global routing;  0.104328s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (119.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069197s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.3%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1568 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1609 instances, 368 luts, 994 seqs, 200 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 50884.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7622, tnet num: 2135, tinst num: 1609, tnode num: 10824, tedge num: 12828.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2135 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.303815s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (102.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(153): len = 50886.3, overlap = 2
PHY-3002 : Step(154): len = 50882.2, overlap = 2
PHY-3002 : Step(155): len = 50873.6, overlap = 2
PHY-3002 : Step(156): len = 50873.6, overlap = 2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2135 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056918s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000301327
PHY-3002 : Step(157): len = 50872.6, overlap = 26.8125
PHY-3002 : Step(158): len = 50872.6, overlap = 26.8125
PHY-3001 : Final: Len = 50872.6, Over = 26.8125
PHY-3001 : End incremental placement;  0.445902s wall, 0.468750s user + 0.031250s system = 0.500000s CPU (112.1%)

OPT-1001 : Total overflow 78.03 peak overflow 2.78
OPT-1001 : End high-fanout net optimization;  0.658707s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (109.1%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 219.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1602/2137.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53056, over cnt = 232(0%), over = 943, worst = 15
PHY-1002 : len = 57832, over cnt = 168(0%), over = 476, worst = 15
PHY-1002 : len = 63024, over cnt = 20(0%), over = 20, worst = 1
PHY-1002 : len = 63304, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 63400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.082810s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (113.2%)

PHY-1001 : Congestion index: top1 = 34.81, top5 = 24.06, top10 = 17.07, top15 = 12.54.
OPT-1001 : End congestion update;  0.127155s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (110.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2135 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058603s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.188445s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (107.8%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 219.
OPT-1001 : End physical optimization;  1.132188s wall, 1.140625s user + 0.062500s system = 1.203125s CPU (106.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 812 remaining SEQ's ...
SYN-4005 : Packed 119 SEQ with LUT/SLICE
SYN-4006 : 88 single LUT's are left
SYN-4006 : 693 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1061/1368 primitive instances ...
PHY-3001 : End packing;  0.049748s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (125.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 809 instances
RUN-1001 : 380 mslices, 380 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1970 nets
RUN-1001 : 1446 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 807 instances, 760 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50815.4, Over = 56
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6392, tnet num: 1968, tinst num: 807, tnode num: 8697, tedge num: 11189.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.316008s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.95918e-05
PHY-3002 : Step(159): len = 50414.2, overlap = 58
PHY-3002 : Step(160): len = 50335.6, overlap = 59.75
PHY-3002 : Step(161): len = 49985.6, overlap = 59.25
PHY-3002 : Step(162): len = 49980.1, overlap = 60
PHY-3002 : Step(163): len = 50117.9, overlap = 58.75
PHY-3002 : Step(164): len = 49920.8, overlap = 59.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.91836e-05
PHY-3002 : Step(165): len = 50155.6, overlap = 59
PHY-3002 : Step(166): len = 50341, overlap = 58.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.65556e-05
PHY-3002 : Step(167): len = 50769.7, overlap = 56.25
PHY-3002 : Step(168): len = 51467.5, overlap = 55.25
PHY-3002 : Step(169): len = 52572.5, overlap = 52.75
PHY-3002 : Step(170): len = 53106.9, overlap = 51.25
PHY-3002 : Step(171): len = 53321.3, overlap = 49.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.086360s wall, 0.109375s user + 0.109375s system = 0.218750s CPU (253.3%)

PHY-3001 : Trial Legalized: Len = 66418.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048799s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00082898
PHY-3002 : Step(172): len = 64201.5, overlap = 5.25
PHY-3002 : Step(173): len = 62298.2, overlap = 11.75
PHY-3002 : Step(174): len = 60397.1, overlap = 15.5
PHY-3002 : Step(175): len = 58876.1, overlap = 17.75
PHY-3002 : Step(176): len = 58271.1, overlap = 21
PHY-3002 : Step(177): len = 57824.5, overlap = 21.5
PHY-3002 : Step(178): len = 57577.8, overlap = 22.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00165796
PHY-3002 : Step(179): len = 57753.5, overlap = 22.5
PHY-3002 : Step(180): len = 57787.8, overlap = 22.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00331592
PHY-3002 : Step(181): len = 57915.8, overlap = 22
PHY-3002 : Step(182): len = 57915.8, overlap = 22
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005091s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (306.9%)

PHY-3001 : Legalized: Len = 62166.6, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005676s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 3, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 62352.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6392, tnet num: 1968, tinst num: 807, tnode num: 8697, tedge num: 11189.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 67/1970.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67680, over cnt = 135(0%), over = 204, worst = 8
PHY-1002 : len = 68672, over cnt = 69(0%), over = 83, worst = 3
PHY-1002 : len = 69432, over cnt = 17(0%), over = 19, worst = 3
PHY-1002 : len = 69752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110793s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (112.8%)

PHY-1001 : Congestion index: top1 = 30.84, top5 = 22.15, top10 = 17.30, top15 = 13.57.
PHY-1001 : End incremental global routing;  0.167326s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (112.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062661s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260670s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (107.9%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1737/1970.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005822s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.84, top5 = 22.15, top10 = 17.30, top15 = 13.57.
OPT-1001 : End congestion update;  0.051680s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048418s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 769 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 807 instances, 760 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62351.4, Over = 0
PHY-3001 : End spreading;  0.005673s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (275.4%)

PHY-3001 : Final: Len = 62351.4, Over = 0
PHY-3001 : End incremental legalization;  0.035938s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150783s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.6%)

OPT-1001 : Current memory(MB): used = 223, reserve = 189, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048289s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1729/1970.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69776, over cnt = 2(0%), over = 3, worst = 2
PHY-1002 : len = 69784, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 69800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024806s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.0%)

PHY-1001 : Congestion index: top1 = 30.91, top5 = 22.16, top10 = 17.28, top15 = 13.55.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048011s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.413793
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.890029s wall, 0.906250s user + 0.000000s system = 0.906250s CPU (101.8%)

RUN-1003 : finish command "place" in  6.000415s wall, 9.328125s user + 2.750000s system = 12.078125s CPU (201.3%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 809 instances
RUN-1001 : 380 mslices, 380 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1970 nets
RUN-1001 : 1446 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6392, tnet num: 1968, tinst num: 807, tnode num: 8697, tedge num: 11189.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 380 mslices, 380 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67104, over cnt = 134(0%), over = 203, worst = 8
PHY-1002 : len = 68104, over cnt = 66(0%), over = 81, worst = 3
PHY-1002 : len = 69032, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 69144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109903s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (113.7%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.02, top10 = 17.17, top15 = 13.46.
PHY-1001 : End global routing;  0.158765s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (108.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 202, peak = 240.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 499, reserve = 468, peak = 499.
PHY-1001 : End build detailed router design. 3.304494s wall, 3.281250s user + 0.015625s system = 3.296875s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32872, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.300644s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (98.5%)

PHY-1001 : Current memory(MB): used = 532, reserve = 502, peak = 533.
PHY-1001 : End phase 1; 1.306433s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (98.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 44% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 178600, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End initial routed; 1.062334s wall, 1.859375s user + 0.000000s system = 1.859375s CPU (175.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1758(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.133   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.368261s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.8%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 1.430710s wall, 2.234375s user + 0.000000s system = 2.234375s CPU (156.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178600, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015456s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178568, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025719s wall, 0.062500s user + 0.031250s system = 0.093750s CPU (364.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178640, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.024290s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (193.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 178672, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.018335s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1758(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.133   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.367746s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.176893s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.2%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.758506s wall, 0.796875s user + 0.031250s system = 0.828125s CPU (109.2%)

PHY-1003 : Routed, final wirelength = 178672
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.010076s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (155.1%)

PHY-1001 : End detail routing;  6.994650s wall, 7.765625s user + 0.078125s system = 7.843750s CPU (112.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6392, tnet num: 1968, tinst num: 807, tnode num: 8697, tedge num: 11189.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.893111s wall, 8.656250s user + 0.093750s system = 8.750000s CPU (110.9%)

RUN-1004 : used memory is 526 MB, reserved memory is 497 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      772   out of  19600    3.94%
#reg                     1049   out of  19600    5.35%
#le                      1465
  #lut only               416   out of   1465   28.40%
  #reg only               693   out of   1465   47.30%
  #lut&reg                356   out of   1465   24.30%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         457
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    44
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1465   |572     |200     |1080    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1093   |300     |121     |902     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |22      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |522    |117     |53      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |56     |2       |0       |56      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |10      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |24     |16      |0       |24      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |65     |25      |14      |61      |0       |1       |
|    rs422                   |Rs422Output                                      |309    |94      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |89     |76      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |18     |14      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |36     |34      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1410  
    #2          2       287   
    #3          3       103   
    #4          4        18   
    #5        5-10       81   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6392, tnet num: 1968, tinst num: 807, tnode num: 8697, tedge num: 11189.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1968 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 807
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1970, pip num: 14464
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1277 valid insts, and 38040 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.128182s wall, 17.843750s user + 0.046875s system = 17.890625s CPU (571.9%)

RUN-1004 : used memory is 549 MB, reserved memory is 518 MB, peak memory is 673 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230720_190129.log"
