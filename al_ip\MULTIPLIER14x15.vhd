--------------------------------------------------------------
 --     Copyright (c) 2012-2023 Anlogic Inc.
 --  All Right Reserved.
--------------------------------------------------------------
 -- Log	:	This file is generated by Anlogic IP Generator.
 -- File	:	D:/GitProject/GitProject/Anlogic/IFOG501_2B/al_ip/MULTIPLIER14x15.vhd
 -- Date	:	2023 04 17
 -- TD version	:	5.6.69102
--------------------------------------------------------------

LIBRARY ieee;
USE work.ALL;
	USE ieee.std_logic_1164.all;
LIBRARY eagle_macro;
	USE eagle_macro.EAGLE_COMPONENTS.all;

ENTITY MULTIPLIER14x15 IS
PORT (
	p		: OUT STD_LOGIC_VECTOR(28 DOWNTO 0);

	a		: IN STD_LOGIC_VECTOR(13 DOWNTO 0);
	b		: IN STD_LOGIC_VECTOR(14 DOWNTO 0)	);
END MULTIPLIER14x15;

ARCHITECTURE struct OF MULTIPLIER14x15 IS

	BEGIN
	inst : EG_LOGIC_MULT
		GENERIC MAP (
			INPUT_WIDTH_A	=> 14,
			INPUT_WIDTH_B	=> 15,
			OUTPUT_WIDTH		=> 29,
			INPUTFORMAT		=> "UNSIGNED",
			INPUTREGA		=> "DISABLE",
			INPUTREGB		=> "DISABLE",
			OUTPUTREG		=> "DISABLE",
			IMPLEMENT		=> "DSP"
		)
		PORT MAP (
			a		=> a,
			b		=> b,
			p		=> p,
			cea		=> '0',
			ceb		=> '0',
			cepd		=> '0',
			clk		=> '0',
			rstan		=> '0',
			rstbn		=> '0',
			rstpdn	=> '0'
			);

END struct;
