============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 14:32:29 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 25 trigger nets, 25 data nets.
KIT-1004 : Chipwatcher code = 1001011011010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2083/10 useful/useless nets, 1284/5 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 1856/18 useful/useless nets, 1614/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 282 better
SYN-1014 : Optimize round 2
SYN-1032 : 1661/15 useful/useless nets, 1419/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1685/156 useful/useless nets, 1465/30 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 207 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 2053/5 useful/useless nets, 1833/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7588, tnet num: 2053, tinst num: 1832, tnode num: 9511, tedge num: 11636.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2053 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 175 (3.57), #lev = 7 (1.77)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 174 (3.59), #lev = 6 (1.89)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 395 instances into 174 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 280 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (180 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1338 instances
RUN-0007 : 543 luts, 585 seqs, 108 mslices, 66 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1565 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 913 nets have 2 pins
RUN-1001 : 471 nets have [3 - 5] pins
RUN-1001 : 97 nets have [6 - 10] pins
RUN-1001 : 50 nets have [11 - 20] pins
RUN-1001 : 23 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     158     
RUN-1001 :   No   |  No   |  Yes  |     101     
RUN-1001 :   No   |  Yes  |  No   |     93      
RUN-1001 :   Yes  |  No   |  No   |     57      
RUN-1001 :   Yes  |  No   |  Yes  |     176     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 18
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1336 instances, 543 luts, 585 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6537, tnet num: 1563, tinst num: 1336, tnode num: 8463, tedge num: 10752.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1563 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.126878s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 346187
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1336.
PHY-3001 : End clustering;  0.000027s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 277682, overlap = 51.75
PHY-3002 : Step(2): len = 238163, overlap = 51.75
PHY-3002 : Step(3): len = 212653, overlap = 51.75
PHY-3002 : Step(4): len = 187340, overlap = 51.75
PHY-3002 : Step(5): len = 163152, overlap = 51.75
PHY-3002 : Step(6): len = 140850, overlap = 51.75
PHY-3002 : Step(7): len = 124350, overlap = 51.75
PHY-3002 : Step(8): len = 111648, overlap = 51.75
PHY-3002 : Step(9): len = 97174.4, overlap = 52.25
PHY-3002 : Step(10): len = 88464.9, overlap = 52.5625
PHY-3002 : Step(11): len = 81008.2, overlap = 52.875
PHY-3002 : Step(12): len = 73675.2, overlap = 52.625
PHY-3002 : Step(13): len = 69346.2, overlap = 52.625
PHY-3002 : Step(14): len = 61945.5, overlap = 52.0625
PHY-3002 : Step(15): len = 57408, overlap = 51.9375
PHY-3002 : Step(16): len = 54373.4, overlap = 52
PHY-3002 : Step(17): len = 50535.3, overlap = 47.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.41362e-05
PHY-3002 : Step(18): len = 51400.1, overlap = 49.5
PHY-3002 : Step(19): len = 53015.3, overlap = 49.5
PHY-3002 : Step(20): len = 51012.6, overlap = 45
PHY-3002 : Step(21): len = 50058.9, overlap = 40.5
PHY-3002 : Step(22): len = 49081.5, overlap = 31.5
PHY-3002 : Step(23): len = 48455.7, overlap = 31.5
PHY-3002 : Step(24): len = 47220.9, overlap = 36
PHY-3002 : Step(25): len = 46171.7, overlap = 40.5
PHY-3002 : Step(26): len = 45108.8, overlap = 45
PHY-3002 : Step(27): len = 44249.1, overlap = 47.25
PHY-3002 : Step(28): len = 43638.9, overlap = 43.0625
PHY-3002 : Step(29): len = 43102.8, overlap = 38.6875
PHY-3002 : Step(30): len = 42746.3, overlap = 38.75
PHY-3002 : Step(31): len = 41840.4, overlap = 34.0625
PHY-3002 : Step(32): len = 41004.3, overlap = 38.5
PHY-3002 : Step(33): len = 40030.7, overlap = 42.75
PHY-3002 : Step(34): len = 39112, overlap = 40.5
PHY-3002 : Step(35): len = 37980.4, overlap = 40.5
PHY-3002 : Step(36): len = 36975.3, overlap = 38.5
PHY-3002 : Step(37): len = 36240.9, overlap = 38.75
PHY-3002 : Step(38): len = 35508.9, overlap = 41.125
PHY-3002 : Step(39): len = 34770.3, overlap = 39.0625
PHY-3002 : Step(40): len = 34213.2, overlap = 39.0625
PHY-3002 : Step(41): len = 34151.4, overlap = 41.3125
PHY-3002 : Step(42): len = 34249.2, overlap = 41.3125
PHY-3002 : Step(43): len = 34254.1, overlap = 41.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.82723e-05
PHY-3002 : Step(44): len = 34111.3, overlap = 41.3125
PHY-3002 : Step(45): len = 34048, overlap = 41.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.65446e-05
PHY-3002 : Step(46): len = 34099.4, overlap = 41.3125
PHY-3002 : Step(47): len = 34139.9, overlap = 41.3125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006185s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1563 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038956s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (80.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(48): len = 37133.9, overlap = 8.25
PHY-3002 : Step(49): len = 37202.3, overlap = 8.21875
PHY-3002 : Step(50): len = 37404.6, overlap = 7.96875
PHY-3002 : Step(51): len = 37530.4, overlap = 8.03125
PHY-3002 : Step(52): len = 37650.3, overlap = 8.28125
PHY-3002 : Step(53): len = 37741.6, overlap = 8.5
PHY-3002 : Step(54): len = 37430.9, overlap = 8.71875
PHY-3002 : Step(55): len = 37313, overlap = 8.875
PHY-3002 : Step(56): len = 36996.8, overlap = 9.125
PHY-3002 : Step(57): len = 36578.5, overlap = 9.21875
PHY-3002 : Step(58): len = 36283.7, overlap = 9.75
PHY-3002 : Step(59): len = 35769.7, overlap = 9.8125
PHY-3002 : Step(60): len = 35588.8, overlap = 11.0938
PHY-3002 : Step(61): len = 35392.5, overlap = 12.7188
PHY-3002 : Step(62): len = 34960.4, overlap = 13.8438
PHY-3002 : Step(63): len = 34666.9, overlap = 16.0938
PHY-3002 : Step(64): len = 34247, overlap = 16.6875
PHY-3002 : Step(65): len = 34066.5, overlap = 18.9375
PHY-3002 : Step(66): len = 33694.9, overlap = 21.4062
PHY-3002 : Step(67): len = 33518.3, overlap = 23.875
PHY-3002 : Step(68): len = 33222.8, overlap = 26.9062
PHY-3002 : Step(69): len = 33158.1, overlap = 30.5
PHY-3002 : Step(70): len = 33080.5, overlap = 29.5938
PHY-3002 : Step(71): len = 32762.9, overlap = 29.4688
PHY-3002 : Step(72): len = 32683.2, overlap = 28.0625
PHY-3002 : Step(73): len = 32619.1, overlap = 27.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000318361
PHY-3002 : Step(74): len = 32638.7, overlap = 26.1562
PHY-3002 : Step(75): len = 32709.8, overlap = 25.75
PHY-3002 : Step(76): len = 33411.6, overlap = 19.7812
PHY-3002 : Step(77): len = 33852.4, overlap = 17.3438
PHY-3002 : Step(78): len = 34673.7, overlap = 16.0312
PHY-3002 : Step(79): len = 34633.8, overlap = 15.0625
PHY-3002 : Step(80): len = 34613.3, overlap = 14.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000636722
PHY-3002 : Step(81): len = 34430.5, overlap = 14.7812
PHY-3002 : Step(82): len = 34430.5, overlap = 14.7812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1563 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039406s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (119.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.75872e-05
PHY-3002 : Step(83): len = 34681.9, overlap = 57.1875
PHY-3002 : Step(84): len = 34857.1, overlap = 56.9062
PHY-3002 : Step(85): len = 34645.5, overlap = 51.9375
PHY-3002 : Step(86): len = 34550.5, overlap = 49
PHY-3002 : Step(87): len = 34586.7, overlap = 48.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.51743e-05
PHY-3002 : Step(88): len = 34498.3, overlap = 48.5625
PHY-3002 : Step(89): len = 34591.1, overlap = 48.2812
PHY-3002 : Step(90): len = 34783.3, overlap = 48.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000190349
PHY-3002 : Step(91): len = 35026.2, overlap = 45.5312
PHY-3002 : Step(92): len = 35268.8, overlap = 44.8438
PHY-3002 : Step(93): len = 36469, overlap = 39.1562
PHY-3002 : Step(94): len = 36301, overlap = 38.5
PHY-3002 : Step(95): len = 36223.2, overlap = 38.1875
PHY-3002 : Step(96): len = 35780.8, overlap = 38.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000380697
PHY-3002 : Step(97): len = 35640.7, overlap = 38.4375
PHY-3002 : Step(98): len = 35640.7, overlap = 38.4375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000761394
PHY-3002 : Step(99): len = 36228, overlap = 35.7188
PHY-3002 : Step(100): len = 36228, overlap = 35.7188
PHY-3002 : Step(101): len = 36084.2, overlap = 35.4375
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00152279
PHY-3002 : Step(102): len = 36541.8, overlap = 34.0938
PHY-3002 : Step(103): len = 36671, overlap = 33.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6537, tnet num: 1563, tinst num: 1336, tnode num: 8463, tedge num: 10752.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 79.09 peak overflow 3.44
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1565.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 40608, over cnt = 195(0%), over = 781, worst = 20
PHY-1001 : End global iterations;  0.064291s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (121.5%)

PHY-1001 : Congestion index: top1 = 36.49, top5 = 20.61, top10 = 12.71, top15 = 8.98.
PHY-1001 : End incremental global routing;  0.114460s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (109.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1563 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046398s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1324 has valid locations, 23 needs to be replaced
PHY-3001 : design contains 1358 instances, 543 luts, 607 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 37099.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6625, tnet num: 1585, tinst num: 1358, tnode num: 8617, tedge num: 10884.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1585 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.147315s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(104): len = 37331.1, overlap = 1.46875
PHY-3002 : Step(105): len = 37585.6, overlap = 1.46875
PHY-3002 : Step(106): len = 37779.2, overlap = 1.46875
PHY-3002 : Step(107): len = 37886.2, overlap = 1.46875
PHY-3002 : Step(108): len = 37846.7, overlap = 1.46875
PHY-3002 : Step(109): len = 37846.7, overlap = 1.46875
PHY-3002 : Step(110): len = 37843.8, overlap = 1.46875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1585 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039585s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000490858
PHY-3002 : Step(111): len = 37819.2, overlap = 33.6875
PHY-3002 : Step(112): len = 37819.2, overlap = 33.6875
PHY-3001 : Final: Len = 37819.2, Over = 33.6875
PHY-3001 : End incremental placement;  0.304290s wall, 0.312500s user + 0.078125s system = 0.390625s CPU (128.4%)

OPT-1001 : Total overflow 79.59 peak overflow 3.44
OPT-1001 : End high-fanout net optimization;  0.496984s wall, 0.515625s user + 0.078125s system = 0.593750s CPU (119.5%)

OPT-1001 : Current memory(MB): used = 198, reserve = 151, peak = 198.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1073/1587.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 41952, over cnt = 197(0%), over = 769, worst = 20
PHY-1002 : len = 47488, over cnt = 125(0%), over = 294, worst = 10
PHY-1002 : len = 50336, over cnt = 45(0%), over = 59, worst = 4
PHY-1002 : len = 50776, over cnt = 17(0%), over = 20, worst = 3
PHY-1002 : len = 51184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095440s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (114.6%)

PHY-1001 : Congestion index: top1 = 32.61, top5 = 21.34, top10 = 14.72, top15 = 10.62.
OPT-1001 : End congestion update;  0.139964s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (111.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1585 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.041001s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (114.3%)

OPT-0007 : Start: WNS 3401 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.181221s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.1%)

OPT-1001 : Current memory(MB): used = 196, reserve = 150, peak = 198.
OPT-1001 : End physical optimization;  0.804560s wall, 0.953125s user + 0.078125s system = 1.031250s CPU (128.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 543 LUT to BLE ...
SYN-4008 : Packed 543 LUT and 196 SEQ to BLE.
SYN-4003 : Packing 411 remaining SEQ's ...
SYN-4005 : Packed 201 SEQ with LUT/SLICE
SYN-4006 : 165 single LUT's are left
SYN-4006 : 210 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 753/1097 primitive instances ...
PHY-3001 : End packing;  0.043825s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (107.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 629 instances
RUN-1001 : 296 mslices, 297 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1393 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 704 nets have 2 pins
RUN-1001 : 504 nets have [3 - 5] pins
RUN-1001 : 108 nets have [6 - 10] pins
RUN-1001 : 39 nets have [11 - 20] pins
RUN-1001 : 27 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 627 instances, 593 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 37967.4, Over = 42.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5800, tnet num: 1391, tinst num: 627, tnode num: 7269, tedge num: 9878.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1391 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.153450s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.07254e-05
PHY-3002 : Step(113): len = 37443.5, overlap = 42.75
PHY-3002 : Step(114): len = 37175.6, overlap = 45.5
PHY-3002 : Step(115): len = 36890.2, overlap = 46.5
PHY-3002 : Step(116): len = 36918.3, overlap = 45
PHY-3002 : Step(117): len = 36959.9, overlap = 46
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000141451
PHY-3002 : Step(118): len = 37035.5, overlap = 45.5
PHY-3002 : Step(119): len = 37352, overlap = 43
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000282902
PHY-3002 : Step(120): len = 37581.3, overlap = 41.25
PHY-3002 : Step(121): len = 38013.6, overlap = 40.5
PHY-3002 : Step(122): len = 38335.1, overlap = 36.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090550s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (155.3%)

PHY-3001 : Trial Legalized: Len = 52566.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1391 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037208s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (84.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0130082
PHY-3002 : Step(123): len = 50734, overlap = 0.75
PHY-3002 : Step(124): len = 47611.5, overlap = 4.25
PHY-3002 : Step(125): len = 46744.4, overlap = 6
PHY-3002 : Step(126): len = 46388.1, overlap = 5.5
PHY-3002 : Step(127): len = 44824.6, overlap = 8.75
PHY-3002 : Step(128): len = 44291.3, overlap = 10.25
PHY-3002 : Step(129): len = 44182, overlap = 10
PHY-3002 : Step(130): len = 43717.8, overlap = 11
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0260165
PHY-3002 : Step(131): len = 43731.4, overlap = 10.5
PHY-3002 : Step(132): len = 43285.9, overlap = 11.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.052033
PHY-3002 : Step(133): len = 43263.8, overlap = 12
PHY-3002 : Step(134): len = 43073.1, overlap = 12
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005375s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 47908.5, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004402s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (354.9%)

PHY-3001 : 4 instances has been re-located, deltaX = 0, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 48032.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5800, tnet num: 1391, tinst num: 627, tnode num: 7269, tedge num: 9878.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/1393.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55648, over cnt = 172(0%), over = 272, worst = 6
PHY-1002 : len = 56624, over cnt = 99(0%), over = 129, worst = 4
PHY-1002 : len = 57904, over cnt = 21(0%), over = 29, worst = 2
PHY-1002 : len = 58344, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 58400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134668s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (139.2%)

PHY-1001 : Congestion index: top1 = 28.41, top5 = 20.86, top10 = 15.93, top15 = 12.10.
PHY-1001 : End incremental global routing;  0.187967s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (124.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1391 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044544s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.259093s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (120.6%)

OPT-1001 : Current memory(MB): used = 198, reserve = 152, peak = 199.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1200/1393.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005611s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.5%)

PHY-1001 : Congestion index: top1 = 28.41, top5 = 20.86, top10 = 15.93, top15 = 12.10.
OPT-1001 : End congestion update;  0.052305s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1391 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.036106s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.5%)

OPT-0007 : Start: WNS 3480 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.088657s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (88.1%)

OPT-1001 : Current memory(MB): used = 200, reserve = 154, peak = 200.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1391 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.036050s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (130.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1200/1393.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006073s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (257.3%)

PHY-1001 : Congestion index: top1 = 28.41, top5 = 20.86, top10 = 15.93, top15 = 12.10.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1391 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.037604s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3480 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 27.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3480ps with logic level 7 
OPT-1001 : End physical optimization;  0.626619s wall, 0.640625s user + 0.031250s system = 0.671875s CPU (107.2%)

RUN-1003 : finish command "place" in  4.334184s wall, 6.312500s user + 2.500000s system = 8.812500s CPU (203.3%)

RUN-1004 : used memory is 185 MB, reserved memory is 139 MB, peak memory is 201 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 629 instances
RUN-1001 : 296 mslices, 297 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1393 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 704 nets have 2 pins
RUN-1001 : 504 nets have [3 - 5] pins
RUN-1001 : 108 nets have [6 - 10] pins
RUN-1001 : 39 nets have [11 - 20] pins
RUN-1001 : 27 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5800, tnet num: 1391, tinst num: 627, tnode num: 7269, tedge num: 9878.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 296 mslices, 297 lslices, 8 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1391 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55096, over cnt = 171(0%), over = 278, worst = 6
PHY-1002 : len = 56200, over cnt = 98(0%), over = 131, worst = 4
PHY-1002 : len = 57648, over cnt = 12(0%), over = 21, worst = 4
PHY-1002 : len = 58008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.145851s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (139.3%)

PHY-1001 : Congestion index: top1 = 27.89, top5 = 20.80, top10 = 15.81, top15 = 12.04.
PHY-1001 : End global routing;  0.201691s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (124.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 219, reserve = 174, peak = 219.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 478, reserve = 436, peak = 478.
PHY-1001 : End build detailed router design. 3.772662s wall, 3.687500s user + 0.062500s system = 3.750000s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 24520, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.747912s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 510, reserve = 469, peak = 510.
PHY-1001 : End phase 1; 0.755015s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (101.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 192216, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 512, reserve = 470, peak = 512.
PHY-1001 : End initial routed; 2.186381s wall, 2.937500s user + 0.015625s system = 2.953125s CPU (135.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1229(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.476   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.236580s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 513, reserve = 470, peak = 513.
PHY-1001 : End phase 2; 2.423078s wall, 3.171875s user + 0.015625s system = 3.187500s CPU (131.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 192216, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013850s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (112.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 192048, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.039687s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 192160, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.031376s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (99.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 192160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021628s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (144.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1229(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.476   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.230459s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (101.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 10 feed throughs used by 10 nets
PHY-1001 : End commit to database; 0.203720s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 527, reserve = 485, peak = 527.
PHY-1001 : End phase 3; 0.671546s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (100.0%)

PHY-1003 : Routed, final wirelength = 192160
PHY-1001 : Current memory(MB): used = 528, reserve = 486, peak = 528.
PHY-1001 : End export database. 0.010608s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (147.3%)

PHY-1001 : End detail routing;  7.837529s wall, 8.515625s user + 0.078125s system = 8.593750s CPU (109.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5800, tnet num: 1391, tinst num: 627, tnode num: 7269, tedge num: 9878.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.470209s wall, 9.187500s user + 0.078125s system = 9.265625s CPU (109.4%)

RUN-1004 : used memory is 484 MB, reserved memory is 442 MB, peak memory is 528 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      913   out of  19600    4.66%
#reg                      610   out of  19600    3.11%
#le                      1123
  #lut only               513   out of   1123   45.68%
  #reg only               210   out of   1123   18.70%
  #lut&reg                400   out of   1123   35.62%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  23
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    288
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         101
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1123   |739     |174     |616     |23      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |113    |90      |11      |63      |0       |0       |
|    usms                            |Time_1ms        |29     |14      |5       |19      |0       |0       |
|  SPIM                              |SPI_MASTER      |192    |124     |23      |132     |0       |0       |
|  uart                              |UART_Control    |108    |98      |4       |58      |0       |0       |
|    U0                              |speed_select_Tx |22     |12      |4       |16      |0       |0       |
|    U1                              |uart_tx         |26     |26      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data       |60     |60      |0       |26      |0       |0       |
|  wendu                             |DS18B20         |173    |132     |41      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |502    |274     |87      |295     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |502    |274     |87      |295     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |183    |68      |0       |177     |0       |0       |
|        reg_inst                    |register        |180    |65      |0       |174     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |319    |206     |87      |118     |0       |0       |
|        bus_inst                    |bus_top         |77     |51      |26      |26      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |74     |48      |26      |23      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |128    |87      |29      |69      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout    Nets  
    #1         1       695   
    #2         2       306   
    #3         3       149   
    #4         4        49   
    #5        5-10     112   
    #6       11-50      61   
    #7       51-100     2    
  Average     2.91           

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5800, tnet num: 1391, tinst num: 627, tnode num: 7269, tedge num: 9878.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1391 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 588953dbe18b1871915f908614aff20f758057a2d1993c4931f4cee582d18853 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 627
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1393, pip num: 14036
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1258 valid insts, and 38605 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010011011001011011010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.111509s wall, 26.390625s user + 0.171875s system = 26.562500s CPU (519.7%)

RUN-1004 : used memory is 499 MB, reserved memory is 460 MB, peak memory is 646 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_143229.log"
