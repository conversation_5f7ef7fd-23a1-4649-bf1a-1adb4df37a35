============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 14:28:44 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1647 instances
RUN-0007 : 380 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2217 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1657 nets have 2 pins
RUN-1001 : 444 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1645 instances, 380 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7833, tnet num: 2215, tinst num: 1645, tnode num: 11071, tedge num: 13221.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.290572s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 544484
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1645.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 490838, overlap = 20.25
PHY-3002 : Step(2): len = 409703, overlap = 13.5
PHY-3002 : Step(3): len = 339990, overlap = 13.5
PHY-3002 : Step(4): len = 333403, overlap = 15.75
PHY-3002 : Step(5): len = 324792, overlap = 13.5
PHY-3002 : Step(6): len = 318086, overlap = 15.75
PHY-3002 : Step(7): len = 313088, overlap = 18
PHY-3002 : Step(8): len = 304012, overlap = 18
PHY-3002 : Step(9): len = 296989, overlap = 20.25
PHY-3002 : Step(10): len = 292418, overlap = 20.25
PHY-3002 : Step(11): len = 285188, overlap = 20.25
PHY-3002 : Step(12): len = 278907, overlap = 20.25
PHY-3002 : Step(13): len = 273752, overlap = 20.25
PHY-3002 : Step(14): len = 266718, overlap = 20.25
PHY-3002 : Step(15): len = 261594, overlap = 20.25
PHY-3002 : Step(16): len = 257170, overlap = 20.25
PHY-3002 : Step(17): len = 251386, overlap = 20.25
PHY-3002 : Step(18): len = 245093, overlap = 20.25
PHY-3002 : Step(19): len = 240815, overlap = 20.25
PHY-3002 : Step(20): len = 235295, overlap = 20.25
PHY-3002 : Step(21): len = 230632, overlap = 20.25
PHY-3002 : Step(22): len = 225230, overlap = 20.25
PHY-3002 : Step(23): len = 220521, overlap = 20.25
PHY-3002 : Step(24): len = 216249, overlap = 20.25
PHY-3002 : Step(25): len = 211195, overlap = 20.25
PHY-3002 : Step(26): len = 206803, overlap = 20.25
PHY-3002 : Step(27): len = 203012, overlap = 20.25
PHY-3002 : Step(28): len = 194807, overlap = 20.25
PHY-3002 : Step(29): len = 190602, overlap = 20.25
PHY-3002 : Step(30): len = 188122, overlap = 20.25
PHY-3002 : Step(31): len = 171898, overlap = 20.25
PHY-3002 : Step(32): len = 159727, overlap = 20.25
PHY-3002 : Step(33): len = 158194, overlap = 20.25
PHY-3002 : Step(34): len = 148288, overlap = 20.25
PHY-3002 : Step(35): len = 116266, overlap = 18
PHY-3002 : Step(36): len = 112505, overlap = 20.25
PHY-3002 : Step(37): len = 110245, overlap = 20.25
PHY-3002 : Step(38): len = 108874, overlap = 20.25
PHY-3002 : Step(39): len = 104581, overlap = 20.25
PHY-3002 : Step(40): len = 102041, overlap = 20.25
PHY-3002 : Step(41): len = 100381, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000127242
PHY-3002 : Step(42): len = 101525, overlap = 6.75
PHY-3002 : Step(43): len = 100830, overlap = 9
PHY-3002 : Step(44): len = 99637.5, overlap = 11.25
PHY-3002 : Step(45): len = 99297, overlap = 13.5
PHY-3002 : Step(46): len = 99054.9, overlap = 11.25
PHY-3002 : Step(47): len = 97667, overlap = 6.75
PHY-3002 : Step(48): len = 95474.6, overlap = 6.75
PHY-3002 : Step(49): len = 95104.9, overlap = 6.75
PHY-3002 : Step(50): len = 93735.2, overlap = 9
PHY-3002 : Step(51): len = 92413.2, overlap = 11.25
PHY-3002 : Step(52): len = 90835.9, overlap = 11.25
PHY-3002 : Step(53): len = 89375.2, overlap = 9
PHY-3002 : Step(54): len = 88641, overlap = 11.25
PHY-3002 : Step(55): len = 86846.2, overlap = 4.5
PHY-3002 : Step(56): len = 85226.1, overlap = 6.75
PHY-3002 : Step(57): len = 84187.2, overlap = 4.5
PHY-3002 : Step(58): len = 82636.4, overlap = 4.5
PHY-3002 : Step(59): len = 82295.9, overlap = 6.75
PHY-3002 : Step(60): len = 79509.7, overlap = 13.5
PHY-3002 : Step(61): len = 78473.4, overlap = 11.25
PHY-3002 : Step(62): len = 78433.7, overlap = 6.75
PHY-3002 : Step(63): len = 77180.6, overlap = 4.5
PHY-3002 : Step(64): len = 75728.5, overlap = 4.5
PHY-3002 : Step(65): len = 74985.8, overlap = 6.75
PHY-3002 : Step(66): len = 73740, overlap = 9
PHY-3002 : Step(67): len = 71657.7, overlap = 9
PHY-3002 : Step(68): len = 71367.2, overlap = 9
PHY-3002 : Step(69): len = 70794.5, overlap = 6.75
PHY-3002 : Step(70): len = 69725.4, overlap = 9
PHY-3002 : Step(71): len = 68720.7, overlap = 6.75
PHY-3002 : Step(72): len = 67997, overlap = 6.75
PHY-3002 : Step(73): len = 67425.6, overlap = 9
PHY-3002 : Step(74): len = 66682.3, overlap = 6.75
PHY-3002 : Step(75): len = 64620.8, overlap = 9
PHY-3002 : Step(76): len = 63585.4, overlap = 11.25
PHY-3002 : Step(77): len = 62777.9, overlap = 9
PHY-3002 : Step(78): len = 62433.5, overlap = 9
PHY-3002 : Step(79): len = 62008.4, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000254484
PHY-3002 : Step(80): len = 61939.9, overlap = 6.75
PHY-3002 : Step(81): len = 62009.4, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000508969
PHY-3002 : Step(82): len = 61953.6, overlap = 6.75
PHY-3002 : Step(83): len = 62006.1, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007194s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (217.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062763s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(84): len = 65734.6, overlap = 5.6875
PHY-3002 : Step(85): len = 64730.8, overlap = 5.375
PHY-3002 : Step(86): len = 63813.3, overlap = 5.1875
PHY-3002 : Step(87): len = 62363, overlap = 3.9375
PHY-3002 : Step(88): len = 61457.9, overlap = 4.375
PHY-3002 : Step(89): len = 60405, overlap = 4.71875
PHY-3002 : Step(90): len = 59385.4, overlap = 4.59375
PHY-3002 : Step(91): len = 58273.4, overlap = 3.6875
PHY-3002 : Step(92): len = 57098.9, overlap = 3.0625
PHY-3002 : Step(93): len = 55619.1, overlap = 3.9375
PHY-3002 : Step(94): len = 54060.5, overlap = 2.8125
PHY-3002 : Step(95): len = 53129.6, overlap = 5.4375
PHY-3002 : Step(96): len = 52778.2, overlap = 6.5625
PHY-3002 : Step(97): len = 52327.1, overlap = 7.1875
PHY-3002 : Step(98): len = 50682.6, overlap = 7.1875
PHY-3002 : Step(99): len = 50260.3, overlap = 7.9375
PHY-3002 : Step(100): len = 49836.6, overlap = 7.9375
PHY-3002 : Step(101): len = 49599.5, overlap = 8.0625
PHY-3002 : Step(102): len = 49295.6, overlap = 8.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000817838
PHY-3002 : Step(103): len = 49245.2, overlap = 8.5
PHY-3002 : Step(104): len = 49185.2, overlap = 8.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00163568
PHY-3002 : Step(105): len = 49291.4, overlap = 8.125
PHY-3002 : Step(106): len = 49495.4, overlap = 6.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065223s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.39487e-05
PHY-3002 : Step(107): len = 50200, overlap = 46
PHY-3002 : Step(108): len = 50348.5, overlap = 49.0938
PHY-3002 : Step(109): len = 51151.5, overlap = 45.75
PHY-3002 : Step(110): len = 52041.3, overlap = 44.9375
PHY-3002 : Step(111): len = 51833.9, overlap = 43.2188
PHY-3002 : Step(112): len = 51485.9, overlap = 44.4062
PHY-3002 : Step(113): len = 51308.8, overlap = 44.4375
PHY-3002 : Step(114): len = 50962.4, overlap = 44.1562
PHY-3002 : Step(115): len = 50734.4, overlap = 43.8125
PHY-3002 : Step(116): len = 50748.2, overlap = 43.1875
PHY-3002 : Step(117): len = 50438.7, overlap = 43.25
PHY-3002 : Step(118): len = 50414.4, overlap = 43.9062
PHY-3002 : Step(119): len = 50351.1, overlap = 42.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000187897
PHY-3002 : Step(120): len = 50275.1, overlap = 43.1562
PHY-3002 : Step(121): len = 50424.8, overlap = 42.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000375795
PHY-3002 : Step(122): len = 51117.6, overlap = 41.5625
PHY-3002 : Step(123): len = 51634.4, overlap = 39.9688
PHY-3002 : Step(124): len = 52521.8, overlap = 37.7188
PHY-3002 : Step(125): len = 52447.9, overlap = 37.375
PHY-3002 : Step(126): len = 52408.2, overlap = 37.8125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7833, tnet num: 2215, tinst num: 1645, tnode num: 11071, tedge num: 13221.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 94.44 peak overflow 2.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55504, over cnt = 248(0%), over = 1082, worst = 23
PHY-1001 : End global iterations;  0.056235s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.1%)

PHY-1001 : Congestion index: top1 = 45.30, top5 = 25.64, top10 = 16.47, top15 = 11.65.
PHY-1001 : End incremental global routing;  0.106307s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (117.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069440s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (90.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.206363s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (106.0%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1703/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55504, over cnt = 248(0%), over = 1082, worst = 23
PHY-1002 : len = 62352, over cnt = 179(0%), over = 481, worst = 23
PHY-1002 : len = 67120, over cnt = 44(0%), over = 111, worst = 14
PHY-1002 : len = 68336, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 68544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110280s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (113.3%)

PHY-1001 : Congestion index: top1 = 38.28, top5 = 25.86, top10 = 18.64, top15 = 13.69.
OPT-1001 : End congestion update;  0.154510s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (111.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059935s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.217107s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.0%)

OPT-1001 : Current memory(MB): used = 216, reserve = 181, peak = 216.
OPT-1001 : End physical optimization;  0.700663s wall, 0.687500s user + 0.031250s system = 0.718750s CPU (102.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 99 SEQ with LUT/SLICE
SYN-4006 : 118 single LUT's are left
SYN-4006 : 704 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1084/1417 primitive instances ...
PHY-3001 : End packing;  0.056522s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1485 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52807, Over = 65
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 841, tnode num: 8927, tedge num: 11572.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.310751s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.975e-05
PHY-3002 : Step(127): len = 52411.8, overlap = 65.5
PHY-3002 : Step(128): len = 52120.5, overlap = 66.5
PHY-3002 : Step(129): len = 51773.4, overlap = 69
PHY-3002 : Step(130): len = 51700.2, overlap = 67.25
PHY-3002 : Step(131): len = 51735.3, overlap = 66.75
PHY-3002 : Step(132): len = 51742.1, overlap = 67.25
PHY-3002 : Step(133): len = 51603.1, overlap = 67
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.95001e-05
PHY-3002 : Step(134): len = 52140.7, overlap = 65
PHY-3002 : Step(135): len = 52740.5, overlap = 62.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000119
PHY-3002 : Step(136): len = 53124.1, overlap = 61
PHY-3002 : Step(137): len = 54624.6, overlap = 60.25
PHY-3002 : Step(138): len = 55545.9, overlap = 57.5
PHY-3002 : Step(139): len = 55228.3, overlap = 57.5
PHY-3002 : Step(140): len = 55109.6, overlap = 57
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.114653s wall, 0.140625s user + 0.218750s system = 0.359375s CPU (313.4%)

PHY-3001 : Trial Legalized: Len = 69483.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055196s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000651614
PHY-3002 : Step(141): len = 66406.8, overlap = 6.5
PHY-3002 : Step(142): len = 64427.1, overlap = 9.25
PHY-3002 : Step(143): len = 62626.9, overlap = 12.5
PHY-3002 : Step(144): len = 61262.8, overlap = 18
PHY-3002 : Step(145): len = 60640.7, overlap = 20
PHY-3002 : Step(146): len = 60138.2, overlap = 21.25
PHY-3002 : Step(147): len = 59869.9, overlap = 22.5
PHY-3002 : Step(148): len = 59621.7, overlap = 24.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00130323
PHY-3002 : Step(149): len = 59984.3, overlap = 24.25
PHY-3002 : Step(150): len = 60138.6, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00260646
PHY-3002 : Step(151): len = 60283.4, overlap = 23.5
PHY-3002 : Step(152): len = 60402.1, overlap = 23.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005042s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (309.9%)

PHY-3001 : Legalized: Len = 65527.4, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006354s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 14 instances has been re-located, deltaX = 5, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 65631.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 841, tnode num: 8927, tedge num: 11572.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 55/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72160, over cnt = 158(0%), over = 244, worst = 7
PHY-1002 : len = 72984, over cnt = 104(0%), over = 134, worst = 5
PHY-1002 : len = 74160, over cnt = 28(0%), over = 31, worst = 3
PHY-1002 : len = 74552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.116010s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (134.7%)

PHY-1001 : Congestion index: top1 = 32.48, top5 = 23.61, top10 = 18.42, top15 = 14.63.
PHY-1001 : End incremental global routing;  0.167665s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (121.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068749s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.267356s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (111.0%)

OPT-1001 : Current memory(MB): used = 220, reserve = 186, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1805/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006325s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.48, top5 = 23.61, top10 = 18.42, top15 = 14.63.
OPT-1001 : End congestion update;  0.053103s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053559s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 803 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65740, Over = 0
PHY-3001 : End spreading;  0.005180s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 65740, Over = 0
PHY-3001 : End incremental legalization;  0.036356s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.157208s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (159.0%)

OPT-1001 : Current memory(MB): used = 225, reserve = 191, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051332s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1794/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74688, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74672, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74672, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74672, over cnt = 1(0%), over = 1, worst = 1
PHY-1001 : End global iterations;  0.039707s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.1%)

PHY-1001 : Congestion index: top1 = 32.59, top5 = 23.64, top10 = 18.44, top15 = 14.65.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050954s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.918083s wall, 1.031250s user + 0.015625s system = 1.046875s CPU (114.0%)

RUN-1003 : finish command "place" in  5.662062s wall, 9.375000s user + 2.906250s system = 12.281250s CPU (216.9%)

RUN-1004 : used memory is 201 MB, reserved memory is 165 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1485 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 841, tnode num: 8927, tedge num: 11572.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71640, over cnt = 153(0%), over = 246, worst = 7
PHY-1002 : len = 72312, over cnt = 103(0%), over = 157, worst = 7
PHY-1002 : len = 74184, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 74312, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125444s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (124.6%)

PHY-1001 : Congestion index: top1 = 32.93, top5 = 23.61, top10 = 18.37, top15 = 14.55.
PHY-1001 : End global routing;  0.177451s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (123.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 206, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.291107s wall, 3.296875s user + 0.000000s system = 3.296875s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32600, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.319073s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (101.9%)

PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 534.
PHY-1001 : End phase 1; 1.324682s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (102.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182344, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End initial routed; 1.085492s wall, 1.718750s user + 0.140625s system = 1.859375s CPU (171.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1805(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.375   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.373206s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 535, reserve = 505, peak = 535.
PHY-1001 : End phase 2; 1.458785s wall, 2.093750s user + 0.140625s system = 2.234375s CPU (153.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182344, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015819s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182256, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023507s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (199.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182280, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019528s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (80.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1805(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.375   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.365027s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.178833s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.8%)

PHY-1001 : Current memory(MB): used = 549, reserve = 519, peak = 549.
PHY-1001 : End phase 3; 0.730184s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (102.7%)

PHY-1003 : Routed, final wirelength = 182280
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.009609s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (162.6%)

PHY-1001 : End detail routing;  7.004434s wall, 7.671875s user + 0.156250s system = 7.828125s CPU (111.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 841, tnode num: 8927, tedge num: 11572.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.948082s wall, 8.640625s user + 0.171875s system = 8.812500s CPU (110.9%)

RUN-1004 : used memory is 525 MB, reserved memory is 498 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      838   out of  19600    4.28%
#reg                     1074   out of  19600    5.48%
#le                      1542
  #lut only               468   out of   1542   30.35%
  #reg only               704   out of   1542   45.65%
  #lut&reg                370   out of   1542   23.99%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1542   |612     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1133   |299     |133     |918     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |539    |131     |58      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |4       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |89     |21      |21      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |87      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |125    |115     |7       |50      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |25     |25      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |65     |62      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |201    |156     |45      |81      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1449  
    #2          2       316   
    #3          3       110   
    #4          4        13   
    #5        5-10       80   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 841, tnode num: 8927, tedge num: 11572.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 841
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2042, pip num: 14853
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1302 valid insts, and 39336 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.463568s wall, 18.890625s user + 0.140625s system = 19.031250s CPU (549.5%)

RUN-1004 : used memory is 548 MB, reserved memory is 518 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_142844.log"
