============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 14:12:39 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1647 instances
RUN-0007 : 380 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2217 nets
RUN-1001 : 1654 nets have 2 pins
RUN-1001 : 446 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1645 instances, 380 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7844, tnet num: 2215, tinst num: 1645, tnode num: 11082, tedge num: 13242.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.281307s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540240
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1645.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 493465, overlap = 18
PHY-3002 : Step(2): len = 401978, overlap = 15.75
PHY-3002 : Step(3): len = 333148, overlap = 15.75
PHY-3002 : Step(4): len = 321463, overlap = 15.75
PHY-3002 : Step(5): len = 314084, overlap = 18
PHY-3002 : Step(6): len = 307014, overlap = 20.25
PHY-3002 : Step(7): len = 301858, overlap = 20.25
PHY-3002 : Step(8): len = 294149, overlap = 20.25
PHY-3002 : Step(9): len = 288517, overlap = 20.25
PHY-3002 : Step(10): len = 283493, overlap = 20.25
PHY-3002 : Step(11): len = 276101, overlap = 20.25
PHY-3002 : Step(12): len = 270054, overlap = 20.25
PHY-3002 : Step(13): len = 265739, overlap = 20.25
PHY-3002 : Step(14): len = 259417, overlap = 20.25
PHY-3002 : Step(15): len = 253413, overlap = 20.25
PHY-3002 : Step(16): len = 249584, overlap = 20.25
PHY-3002 : Step(17): len = 244491, overlap = 20.25
PHY-3002 : Step(18): len = 238104, overlap = 20.25
PHY-3002 : Step(19): len = 233344, overlap = 20.25
PHY-3002 : Step(20): len = 229068, overlap = 20.25
PHY-3002 : Step(21): len = 224090, overlap = 20.25
PHY-3002 : Step(22): len = 218722, overlap = 20.25
PHY-3002 : Step(23): len = 214823, overlap = 20.25
PHY-3002 : Step(24): len = 209547, overlap = 20.25
PHY-3002 : Step(25): len = 205597, overlap = 20.25
PHY-3002 : Step(26): len = 200936, overlap = 20.25
PHY-3002 : Step(27): len = 196063, overlap = 20.25
PHY-3002 : Step(28): len = 191862, overlap = 20.25
PHY-3002 : Step(29): len = 187123, overlap = 20.25
PHY-3002 : Step(30): len = 182799, overlap = 20.25
PHY-3002 : Step(31): len = 179939, overlap = 20.25
PHY-3002 : Step(32): len = 172159, overlap = 20.25
PHY-3002 : Step(33): len = 161003, overlap = 20.25
PHY-3002 : Step(34): len = 158439, overlap = 20.25
PHY-3002 : Step(35): len = 155774, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000125499
PHY-3002 : Step(36): len = 156571, overlap = 15.75
PHY-3002 : Step(37): len = 155140, overlap = 13.5
PHY-3002 : Step(38): len = 152596, overlap = 18
PHY-3002 : Step(39): len = 148996, overlap = 13.5
PHY-3002 : Step(40): len = 147293, overlap = 6.75
PHY-3002 : Step(41): len = 144095, overlap = 6.75
PHY-3002 : Step(42): len = 140939, overlap = 4.5
PHY-3002 : Step(43): len = 137769, overlap = 11.25
PHY-3002 : Step(44): len = 132084, overlap = 4.5
PHY-3002 : Step(45): len = 127731, overlap = 6.75
PHY-3002 : Step(46): len = 126284, overlap = 4.5
PHY-3002 : Step(47): len = 122999, overlap = 9
PHY-3002 : Step(48): len = 116059, overlap = 15.75
PHY-3002 : Step(49): len = 113715, overlap = 11.25
PHY-3002 : Step(50): len = 113192, overlap = 9
PHY-3002 : Step(51): len = 109793, overlap = 6.75
PHY-3002 : Step(52): len = 109087, overlap = 6.75
PHY-3002 : Step(53): len = 107655, overlap = 6.75
PHY-3002 : Step(54): len = 103333, overlap = 13.5
PHY-3002 : Step(55): len = 100144, overlap = 4.5
PHY-3002 : Step(56): len = 99314, overlap = 4.5
PHY-3002 : Step(57): len = 97322.3, overlap = 6.75
PHY-3002 : Step(58): len = 94894.6, overlap = 9
PHY-3002 : Step(59): len = 94617.5, overlap = 6.75
PHY-3002 : Step(60): len = 93308.5, overlap = 4.5
PHY-3002 : Step(61): len = 91725.7, overlap = 6.75
PHY-3002 : Step(62): len = 90390.5, overlap = 6.75
PHY-3002 : Step(63): len = 85793.6, overlap = 9
PHY-3002 : Step(64): len = 82983, overlap = 6.75
PHY-3002 : Step(65): len = 81511.6, overlap = 4.5
PHY-3002 : Step(66): len = 80602.3, overlap = 4.5
PHY-3002 : Step(67): len = 78946, overlap = 6.75
PHY-3002 : Step(68): len = 78572.8, overlap = 6.75
PHY-3002 : Step(69): len = 77797.1, overlap = 6.75
PHY-3002 : Step(70): len = 76268, overlap = 9
PHY-3002 : Step(71): len = 75298.8, overlap = 9
PHY-3002 : Step(72): len = 73845.8, overlap = 11.25
PHY-3002 : Step(73): len = 73608, overlap = 9
PHY-3002 : Step(74): len = 73457.7, overlap = 6.75
PHY-3002 : Step(75): len = 72606.5, overlap = 6.75
PHY-3002 : Step(76): len = 70930.5, overlap = 11.25
PHY-3002 : Step(77): len = 69688.7, overlap = 11.25
PHY-3002 : Step(78): len = 69550, overlap = 6.75
PHY-3002 : Step(79): len = 69143.6, overlap = 6.75
PHY-3002 : Step(80): len = 68264, overlap = 6.75
PHY-3002 : Step(81): len = 66463.2, overlap = 11.25
PHY-3002 : Step(82): len = 66279.8, overlap = 9
PHY-3002 : Step(83): len = 65655.3, overlap = 9
PHY-3002 : Step(84): len = 64656.6, overlap = 9
PHY-3002 : Step(85): len = 63301.8, overlap = 11.25
PHY-3002 : Step(86): len = 62970, overlap = 11.25
PHY-3002 : Step(87): len = 62857.5, overlap = 6.75
PHY-3002 : Step(88): len = 62274.7, overlap = 6.75
PHY-3002 : Step(89): len = 60747.5, overlap = 11.25
PHY-3002 : Step(90): len = 60045.1, overlap = 11.25
PHY-3002 : Step(91): len = 59837.6, overlap = 9
PHY-3002 : Step(92): len = 59394.9, overlap = 9
PHY-3002 : Step(93): len = 59008.5, overlap = 9
PHY-3002 : Step(94): len = 58327.1, overlap = 11.25
PHY-3002 : Step(95): len = 57916.7, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000250998
PHY-3002 : Step(96): len = 58298.4, overlap = 9
PHY-3002 : Step(97): len = 58374.6, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000501996
PHY-3002 : Step(98): len = 58333, overlap = 9
PHY-3002 : Step(99): len = 58323.2, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005427s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060051s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(100): len = 62245.5, overlap = 6.4375
PHY-3002 : Step(101): len = 60850, overlap = 5
PHY-3002 : Step(102): len = 60070.2, overlap = 5.3125
PHY-3002 : Step(103): len = 59279.1, overlap = 4.6875
PHY-3002 : Step(104): len = 58855.1, overlap = 4.4375
PHY-3002 : Step(105): len = 57771.6, overlap = 4.4375
PHY-3002 : Step(106): len = 56903.3, overlap = 4.5
PHY-3002 : Step(107): len = 55915.9, overlap = 5.3125
PHY-3002 : Step(108): len = 54866.3, overlap = 5.8125
PHY-3002 : Step(109): len = 54304.5, overlap = 4.9375
PHY-3002 : Step(110): len = 54183.9, overlap = 4.8125
PHY-3002 : Step(111): len = 53277.2, overlap = 6.15625
PHY-3002 : Step(112): len = 52757.6, overlap = 6.0625
PHY-3002 : Step(113): len = 51952.6, overlap = 5.1875
PHY-3002 : Step(114): len = 51192.1, overlap = 5.375
PHY-3002 : Step(115): len = 50603.5, overlap = 5.78125
PHY-3002 : Step(116): len = 50239.6, overlap = 6.28125
PHY-3002 : Step(117): len = 50078.3, overlap = 5.8125
PHY-3002 : Step(118): len = 49557.2, overlap = 7.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00156747
PHY-3002 : Step(119): len = 49290, overlap = 8
PHY-3002 : Step(120): len = 48930, overlap = 7.9375
PHY-3002 : Step(121): len = 48899.9, overlap = 7.90625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064304s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (97.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102922
PHY-3002 : Step(122): len = 49499.2, overlap = 48.2812
PHY-3002 : Step(123): len = 49823.5, overlap = 45.5312
PHY-3002 : Step(124): len = 50332.3, overlap = 43.5
PHY-3002 : Step(125): len = 50694.5, overlap = 41.375
PHY-3002 : Step(126): len = 50908.6, overlap = 36.9062
PHY-3002 : Step(127): len = 50878.7, overlap = 36.9062
PHY-3002 : Step(128): len = 50704.7, overlap = 35.4375
PHY-3002 : Step(129): len = 50571.8, overlap = 35.875
PHY-3002 : Step(130): len = 50259.4, overlap = 39.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000205844
PHY-3002 : Step(131): len = 50445.7, overlap = 35.5312
PHY-3002 : Step(132): len = 50811.5, overlap = 32.7188
PHY-3002 : Step(133): len = 51071.3, overlap = 33.3438
PHY-3002 : Step(134): len = 51064.8, overlap = 33.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000411688
PHY-3002 : Step(135): len = 51376, overlap = 37.5938
PHY-3002 : Step(136): len = 51702.6, overlap = 36.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7844, tnet num: 2215, tinst num: 1645, tnode num: 11082, tedge num: 13242.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 99.06 peak overflow 5.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54736, over cnt = 238(0%), over = 1165, worst = 19
PHY-1001 : End global iterations;  0.052566s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (178.3%)

PHY-1001 : Congestion index: top1 = 46.34, top5 = 25.78, top10 = 16.23, top15 = 11.45.
PHY-1001 : End incremental global routing;  0.100832s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (124.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067953s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.202324s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.8%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1671/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54736, over cnt = 238(0%), over = 1165, worst = 19
PHY-1002 : len = 61912, over cnt = 173(0%), over = 552, worst = 16
PHY-1002 : len = 67472, over cnt = 28(0%), over = 86, worst = 14
PHY-1002 : len = 68256, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 68448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117477s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (133.0%)

PHY-1001 : Congestion index: top1 = 38.69, top5 = 25.82, top10 = 18.32, top15 = 13.54.
OPT-1001 : End congestion update;  0.164591s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (123.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064822s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (120.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.232260s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (121.1%)

OPT-1001 : Current memory(MB): used = 216, reserve = 180, peak = 216.
OPT-1001 : End physical optimization;  0.719182s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (110.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 95 SEQ with LUT/SLICE
SYN-4006 : 116 single LUT's are left
SYN-4006 : 708 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1088/1421 primitive instances ...
PHY-3001 : End packing;  0.053895s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 847 instances
RUN-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-1001 : 1484 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 845 instances, 798 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51827, Over = 61.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6611, tnet num: 2040, tinst num: 845, tnode num: 8964, tedge num: 11607.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.332618s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.37768e-05
PHY-3002 : Step(137): len = 51293.1, overlap = 61.5
PHY-3002 : Step(138): len = 50796.7, overlap = 60.75
PHY-3002 : Step(139): len = 50863.4, overlap = 61
PHY-3002 : Step(140): len = 50617.9, overlap = 61.75
PHY-3002 : Step(141): len = 50416.1, overlap = 61.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.75535e-05
PHY-3002 : Step(142): len = 50897.9, overlap = 60
PHY-3002 : Step(143): len = 51455, overlap = 57
PHY-3002 : Step(144): len = 51935.4, overlap = 55.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.5107e-05
PHY-3002 : Step(145): len = 52842, overlap = 53.75
PHY-3002 : Step(146): len = 53903.8, overlap = 51.5
PHY-3002 : Step(147): len = 54492, overlap = 53
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.102415s wall, 0.062500s user + 0.078125s system = 0.140625s CPU (137.3%)

PHY-3001 : Trial Legalized: Len = 69588.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050763s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00076807
PHY-3002 : Step(148): len = 66295.7, overlap = 6.75
PHY-3002 : Step(149): len = 63409.7, overlap = 13.5
PHY-3002 : Step(150): len = 61070.3, overlap = 16.75
PHY-3002 : Step(151): len = 60429.3, overlap = 21.75
PHY-3002 : Step(152): len = 59966.4, overlap = 23.25
PHY-3002 : Step(153): len = 59346.7, overlap = 23
PHY-3002 : Step(154): len = 59154.4, overlap = 24
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00147186
PHY-3002 : Step(155): len = 59577.7, overlap = 24.25
PHY-3002 : Step(156): len = 59726.2, overlap = 24.25
PHY-3002 : Step(157): len = 59687.8, overlap = 24
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00294373
PHY-3002 : Step(158): len = 59799.7, overlap = 23.75
PHY-3002 : Step(159): len = 59856.4, overlap = 23.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004821s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64995.7, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005478s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 0, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 65139.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6611, tnet num: 2040, tinst num: 845, tnode num: 8964, tedge num: 11607.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 63/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71360, over cnt = 152(0%), over = 251, worst = 7
PHY-1002 : len = 72456, over cnt = 97(0%), over = 124, worst = 4
PHY-1002 : len = 73808, over cnt = 11(0%), over = 12, worst = 2
PHY-1002 : len = 73984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118463s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (105.5%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.08, top10 = 17.87, top15 = 14.25.
PHY-1001 : End incremental global routing;  0.171617s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (109.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064291s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (121.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.266980s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (105.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1815/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005858s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (266.7%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 23.08, top10 = 17.87, top15 = 14.25.
OPT-1001 : End congestion update;  0.053260s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050877s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 807 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 845 instances, 798 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65173, Over = 0
PHY-3001 : End spreading;  0.005521s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 65173, Over = 0
PHY-3001 : End incremental legalization;  0.035403s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (132.4%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.153564s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (101.7%)

OPT-1001 : Current memory(MB): used = 223, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049661s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (125.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1807/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.011225s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (139.2%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 23.07, top10 = 17.88, top15 = 14.25.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048512s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.868057s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (100.8%)

RUN-1003 : finish command "place" in  5.381547s wall, 9.046875s user + 2.578125s system = 11.625000s CPU (216.0%)

RUN-1004 : used memory is 200 MB, reserved memory is 165 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 847 instances
RUN-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-1001 : 1484 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6611, tnet num: 2040, tinst num: 845, tnode num: 8964, tedge num: 11607.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70928, over cnt = 148(0%), over = 246, worst = 7
PHY-1002 : len = 72008, over cnt = 91(0%), over = 117, worst = 3
PHY-1002 : len = 73320, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 73448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123592s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (126.4%)

PHY-1001 : Congestion index: top1 = 31.81, top5 = 22.78, top10 = 17.72, top15 = 14.12.
PHY-1001 : End global routing;  0.172558s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (117.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 202, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 497, reserve = 466, peak = 497.
PHY-1001 : End build detailed router design. 3.265040s wall, 3.234375s user + 0.031250s system = 3.265625s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33520, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.279476s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 530, reserve = 500, peak = 530.
PHY-1001 : End phase 1; 1.285505s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183160, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End initial routed; 1.097496s wall, 2.171875s user + 0.109375s system = 2.281250s CPU (207.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1806(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.007   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.364182s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.0%)

PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End phase 2; 1.461765s wall, 2.546875s user + 0.109375s system = 2.656250s CPU (181.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183160, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018077s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (86.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 183096, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027245s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (114.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 183112, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019263s wall, 0.031250s user + 0.031250s system = 0.062500s CPU (324.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1806(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.007   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363079s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.180971s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.0%)

PHY-1001 : Current memory(MB): used = 548, reserve = 517, peak = 548.
PHY-1001 : End phase 3; 0.728248s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (105.1%)

PHY-1003 : Routed, final wirelength = 183112
PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End export database. 0.009887s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (158.0%)

PHY-1001 : End detail routing;  6.930379s wall, 7.968750s user + 0.203125s system = 8.171875s CPU (117.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6611, tnet num: 2040, tinst num: 845, tnode num: 8964, tedge num: 11607.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.835609s wall, 8.890625s user + 0.203125s system = 9.093750s CPU (116.1%)

RUN-1004 : used memory is 524 MB, reserved memory is 496 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      836   out of  19600    4.27%
#reg                     1074   out of  19600    5.48%
#le                      1544
  #lut only               470   out of   1544   30.44%
  #reg only               708   out of   1544   45.85%
  #lut&reg                366   out of   1544   23.70%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    44
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1544   |610     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1141   |305     |133     |926     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |539    |128     |58      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |92     |24      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |90      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |33     |28      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |105    |93      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |29      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |48     |46      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1448  
    #2          2       320   
    #3          3       101   
    #4          4        19   
    #5        5-10       82   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6611, tnet num: 2040, tinst num: 845, tnode num: 8964, tedge num: 11607.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 845
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2042, pip num: 14762
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1312 valid insts, and 39172 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.129261s wall, 18.062500s user + 0.062500s system = 18.125000s CPU (579.2%)

RUN-1004 : used memory is 550 MB, reserved memory is 518 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_141239.log"
