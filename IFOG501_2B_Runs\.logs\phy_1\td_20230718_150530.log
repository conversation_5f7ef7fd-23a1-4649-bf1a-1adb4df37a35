============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 15:05:30 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1667 instances
RUN-0007 : 372 luts, 1016 seqs, 148 mslices, 82 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2258 nets
RUN-1001 : 1699 nets have 2 pins
RUN-1001 : 444 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1665 instances, 372 luts, 1016 seqs, 230 slices, 27 macros(230 instances: 148 mslices 82 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7967, tnet num: 2256, tinst num: 1665, tnode num: 11230, tedge num: 13581.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2256 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.289230s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (102.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 624976
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1665.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 489654, overlap = 20.25
PHY-3002 : Step(2): len = 412610, overlap = 11.25
PHY-3002 : Step(3): len = 399547, overlap = 15.75
PHY-3002 : Step(4): len = 379432, overlap = 20.25
PHY-3002 : Step(5): len = 365761, overlap = 20.25
PHY-3002 : Step(6): len = 355781, overlap = 15.75
PHY-3002 : Step(7): len = 345132, overlap = 18
PHY-3002 : Step(8): len = 327516, overlap = 15.75
PHY-3002 : Step(9): len = 320327, overlap = 18
PHY-3002 : Step(10): len = 311400, overlap = 18
PHY-3002 : Step(11): len = 299876, overlap = 20.25
PHY-3002 : Step(12): len = 289276, overlap = 20.25
PHY-3002 : Step(13): len = 283601, overlap = 20.25
PHY-3002 : Step(14): len = 273514, overlap = 20.25
PHY-3002 : Step(15): len = 265544, overlap = 20.25
PHY-3002 : Step(16): len = 259143, overlap = 20.25
PHY-3002 : Step(17): len = 253424, overlap = 20.25
PHY-3002 : Step(18): len = 246263, overlap = 20.25
PHY-3002 : Step(19): len = 241124, overlap = 20.25
PHY-3002 : Step(20): len = 232952, overlap = 20.25
PHY-3002 : Step(21): len = 228830, overlap = 20.25
PHY-3002 : Step(22): len = 223096, overlap = 20.25
PHY-3002 : Step(23): len = 217295, overlap = 20.25
PHY-3002 : Step(24): len = 210906, overlap = 20.25
PHY-3002 : Step(25): len = 207315, overlap = 20.25
PHY-3002 : Step(26): len = 200203, overlap = 20.25
PHY-3002 : Step(27): len = 195999, overlap = 20.25
PHY-3002 : Step(28): len = 191084, overlap = 20.25
PHY-3002 : Step(29): len = 186563, overlap = 20.25
PHY-3002 : Step(30): len = 179434, overlap = 20.25
PHY-3002 : Step(31): len = 176583, overlap = 20.25
PHY-3002 : Step(32): len = 171009, overlap = 20.25
PHY-3002 : Step(33): len = 159819, overlap = 20.25
PHY-3002 : Step(34): len = 154476, overlap = 20.25
PHY-3002 : Step(35): len = 152685, overlap = 20.25
PHY-3002 : Step(36): len = 140616, overlap = 18
PHY-3002 : Step(37): len = 124097, overlap = 20.25
PHY-3002 : Step(38): len = 120635, overlap = 20.25
PHY-3002 : Step(39): len = 119324, overlap = 20.25
PHY-3002 : Step(40): len = 114092, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00010746
PHY-3002 : Step(41): len = 113162, overlap = 9
PHY-3002 : Step(42): len = 112609, overlap = 11.25
PHY-3002 : Step(43): len = 111496, overlap = 11.25
PHY-3002 : Step(44): len = 109413, overlap = 11.25
PHY-3002 : Step(45): len = 104633, overlap = 11.25
PHY-3002 : Step(46): len = 102882, overlap = 9
PHY-3002 : Step(47): len = 101505, overlap = 11.25
PHY-3002 : Step(48): len = 100774, overlap = 9
PHY-3002 : Step(49): len = 97630.8, overlap = 9
PHY-3002 : Step(50): len = 95623, overlap = 13.5
PHY-3002 : Step(51): len = 93187, overlap = 13.5
PHY-3002 : Step(52): len = 91934.5, overlap = 13.5
PHY-3002 : Step(53): len = 89959.8, overlap = 13.6875
PHY-3002 : Step(54): len = 85817.4, overlap = 12.6875
PHY-3002 : Step(55): len = 83200.3, overlap = 12.9375
PHY-3002 : Step(56): len = 82774.5, overlap = 17.5
PHY-3002 : Step(57): len = 81484.1, overlap = 15.4375
PHY-3002 : Step(58): len = 80419.3, overlap = 13.1875
PHY-3002 : Step(59): len = 79647.9, overlap = 13.375
PHY-3002 : Step(60): len = 77989.2, overlap = 13.375
PHY-3002 : Step(61): len = 76617.6, overlap = 13.625
PHY-3002 : Step(62): len = 75940.5, overlap = 16.125
PHY-3002 : Step(63): len = 74055.7, overlap = 16.3125
PHY-3002 : Step(64): len = 71533, overlap = 16.8125
PHY-3002 : Step(65): len = 70237.5, overlap = 16.875
PHY-3002 : Step(66): len = 68997.7, overlap = 17.1875
PHY-3002 : Step(67): len = 68009.8, overlap = 14.9375
PHY-3002 : Step(68): len = 67361.9, overlap = 17.25
PHY-3002 : Step(69): len = 67299, overlap = 17.1875
PHY-3002 : Step(70): len = 66705, overlap = 17.125
PHY-3002 : Step(71): len = 66429.1, overlap = 14.8125
PHY-3002 : Step(72): len = 66080.8, overlap = 16.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00021492
PHY-3002 : Step(73): len = 65937.5, overlap = 14.375
PHY-3002 : Step(74): len = 66004.1, overlap = 12.125
PHY-3002 : Step(75): len = 66016.5, overlap = 14.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000429839
PHY-3002 : Step(76): len = 65909.6, overlap = 12.125
PHY-3002 : Step(77): len = 65849.9, overlap = 12.125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005779s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (270.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2256 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061247s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00107483
PHY-3002 : Step(78): len = 68577.8, overlap = 8.0625
PHY-3002 : Step(79): len = 67433.5, overlap = 8.5
PHY-3002 : Step(80): len = 66892.2, overlap = 7.34375
PHY-3002 : Step(81): len = 65654.5, overlap = 7.15625
PHY-3002 : Step(82): len = 64691.7, overlap = 6.6875
PHY-3002 : Step(83): len = 63239.3, overlap = 6.28125
PHY-3002 : Step(84): len = 61672.2, overlap = 5.1875
PHY-3002 : Step(85): len = 60715.8, overlap = 5.4375
PHY-3002 : Step(86): len = 59400.1, overlap = 5.375
PHY-3002 : Step(87): len = 58057.3, overlap = 5.71875
PHY-3002 : Step(88): len = 56283.8, overlap = 6.21875
PHY-3002 : Step(89): len = 55231.6, overlap = 7.5625
PHY-3002 : Step(90): len = 54609.1, overlap = 7.71875
PHY-3002 : Step(91): len = 54202.4, overlap = 7.96875
PHY-3002 : Step(92): len = 53419.2, overlap = 7.03125
PHY-3002 : Step(93): len = 52744.1, overlap = 6.96875
PHY-3002 : Step(94): len = 52284.7, overlap = 5.84375
PHY-3002 : Step(95): len = 51892.4, overlap = 5.34375
PHY-3002 : Step(96): len = 51380.6, overlap = 5.21875
PHY-3002 : Step(97): len = 50753.8, overlap = 5.03125
PHY-3002 : Step(98): len = 50404.1, overlap = 4.53125
PHY-3002 : Step(99): len = 50247.9, overlap = 4.90625
PHY-3002 : Step(100): len = 50057.4, overlap = 4.78125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00214965
PHY-3002 : Step(101): len = 50034.9, overlap = 4.53125
PHY-3002 : Step(102): len = 49775.6, overlap = 4.53125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0042993
PHY-3002 : Step(103): len = 49634.8, overlap = 4.53125
PHY-3002 : Step(104): len = 49645.8, overlap = 4.53125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2256 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065439s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (95.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000111549
PHY-3002 : Step(105): len = 49811.6, overlap = 48.7812
PHY-3002 : Step(106): len = 50187.7, overlap = 41.7812
PHY-3002 : Step(107): len = 50408.4, overlap = 43.5312
PHY-3002 : Step(108): len = 50527.2, overlap = 41.5625
PHY-3002 : Step(109): len = 50489.1, overlap = 41.9375
PHY-3002 : Step(110): len = 50234.5, overlap = 41.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000223099
PHY-3002 : Step(111): len = 50299.8, overlap = 42.3438
PHY-3002 : Step(112): len = 50436.1, overlap = 42.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000446198
PHY-3002 : Step(113): len = 50458.7, overlap = 41.7812
PHY-3002 : Step(114): len = 51357.8, overlap = 41.6875
PHY-3002 : Step(115): len = 51767.2, overlap = 40.6875
PHY-3002 : Step(116): len = 52399.3, overlap = 41.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7967, tnet num: 2256, tinst num: 1665, tnode num: 11230, tedge num: 13581.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 101.12 peak overflow 3.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2258.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55384, over cnt = 252(0%), over = 1092, worst = 20
PHY-1001 : End global iterations;  0.081495s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (134.2%)

PHY-1001 : Congestion index: top1 = 46.10, top5 = 25.85, top10 = 16.18, top15 = 11.41.
PHY-1001 : End incremental global routing;  0.132670s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (129.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2256 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068214s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (91.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.231263s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (114.9%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1727/2258.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55384, over cnt = 252(0%), over = 1092, worst = 20
PHY-1002 : len = 63104, over cnt = 195(0%), over = 411, worst = 12
PHY-1002 : len = 67152, over cnt = 51(0%), over = 84, worst = 12
PHY-1002 : len = 68256, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 68576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106910s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (131.5%)

PHY-1001 : Congestion index: top1 = 40.24, top5 = 26.21, top10 = 18.38, top15 = 13.53.
OPT-1001 : End congestion update;  0.149859s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (125.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2256 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061148s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.213817s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (116.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : End physical optimization;  0.729525s wall, 0.734375s user + 0.062500s system = 0.796875s CPU (109.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 372 LUT to BLE ...
SYN-4008 : Packed 372 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 100 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 727 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1099/1436 primitive instances ...
PHY-3001 : End packing;  0.049456s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (63.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 862 instances
RUN-1001 : 406 mslices, 407 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2083 nets
RUN-1001 : 1536 nets have 2 pins
RUN-1001 : 426 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 860 instances, 813 slices, 27 macros(230 instances: 148 mslices 82 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52656.6, Over = 69.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6722, tnet num: 2081, tinst num: 860, tnode num: 9080, tedge num: 11931.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.322342s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (96.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.94503e-05
PHY-3002 : Step(117): len = 52245.6, overlap = 70.75
PHY-3002 : Step(118): len = 51634.1, overlap = 71.25
PHY-3002 : Step(119): len = 51528.1, overlap = 71
PHY-3002 : Step(120): len = 51691.5, overlap = 70.25
PHY-3002 : Step(121): len = 51715, overlap = 70.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.89007e-05
PHY-3002 : Step(122): len = 52052.4, overlap = 69.75
PHY-3002 : Step(123): len = 52839.5, overlap = 67.75
PHY-3002 : Step(124): len = 53266.5, overlap = 68
PHY-3002 : Step(125): len = 53284.5, overlap = 69.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000117801
PHY-3002 : Step(126): len = 53824.6, overlap = 67
PHY-3002 : Step(127): len = 54988.3, overlap = 63.75
PHY-3002 : Step(128): len = 55654.2, overlap = 59
PHY-3002 : Step(129): len = 55882.2, overlap = 57.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.080263s wall, 0.125000s user + 0.109375s system = 0.234375s CPU (292.0%)

PHY-3001 : Trial Legalized: Len = 71820.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054898s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00075329
PHY-3002 : Step(130): len = 68657.6, overlap = 8.25
PHY-3002 : Step(131): len = 66069.6, overlap = 16.5
PHY-3002 : Step(132): len = 63830.8, overlap = 20.25
PHY-3002 : Step(133): len = 62260.7, overlap = 22.5
PHY-3002 : Step(134): len = 61194.9, overlap = 24
PHY-3002 : Step(135): len = 60610, overlap = 27.75
PHY-3002 : Step(136): len = 60187.1, overlap = 27
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00150658
PHY-3002 : Step(137): len = 60468, overlap = 27.25
PHY-3002 : Step(138): len = 60575, overlap = 26.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00301316
PHY-3002 : Step(139): len = 60671.1, overlap = 26.75
PHY-3002 : Step(140): len = 60706.2, overlap = 26.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005051s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 65949.9, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005657s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (276.2%)

PHY-3001 : 2 instances has been re-located, deltaX = 0, deltaY = 2, maxDist = 1.
PHY-3001 : Final: Len = 66009.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6722, tnet num: 2081, tinst num: 860, tnode num: 9080, tedge num: 11931.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 66/2083.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72664, over cnt = 140(0%), over = 206, worst = 6
PHY-1002 : len = 73440, over cnt = 75(0%), over = 93, worst = 3
PHY-1002 : len = 74488, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 74520, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 74696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148561s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (136.7%)

PHY-1001 : Congestion index: top1 = 31.83, top5 = 23.08, top10 = 18.11, top15 = 14.40.
PHY-1001 : End incremental global routing;  0.202795s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (123.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063118s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.296460s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (116.0%)

OPT-1001 : Current memory(MB): used = 222, reserve = 187, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1849/2083.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006116s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.83, top5 = 23.08, top10 = 18.11, top15 = 14.40.
OPT-1001 : End congestion update;  0.051360s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056940s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.8%)

OPT-0007 : Start: WNS 1019 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 822 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 860 instances, 813 slices, 27 macros(230 instances: 148 mslices 82 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 66018.6, Over = 0
PHY-3001 : End spreading;  0.005538s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 66018.6, Over = 0
PHY-3001 : End incremental legalization;  0.037666s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (207.4%)

OPT-0007 : Iter 1: improved WNS 1019 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1019 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.162462s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (115.4%)

OPT-1001 : Current memory(MB): used = 227, reserve = 192, peak = 227.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050505s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1845/2083.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009130s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.83, top5 = 23.07, top10 = 18.10, top15 = 14.40.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052605s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1019 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.344828
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1019ps with logic level 5 
RUN-1001 :       #2 path slack 1034ps with logic level 5 
RUN-1001 :       #3 path slack 1099ps with logic level 5 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.918676s wall, 0.953125s user + 0.046875s system = 1.000000s CPU (108.9%)

RUN-1003 : finish command "place" in  5.357490s wall, 8.203125s user + 2.484375s system = 10.687500s CPU (199.5%)

RUN-1004 : used memory is 205 MB, reserved memory is 169 MB, peak memory is 227 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 862 instances
RUN-1001 : 406 mslices, 407 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2083 nets
RUN-1001 : 1536 nets have 2 pins
RUN-1001 : 426 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6722, tnet num: 2081, tinst num: 860, tnode num: 9080, tedge num: 11931.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 406 mslices, 407 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72080, over cnt = 139(0%), over = 209, worst = 6
PHY-1002 : len = 72960, over cnt = 76(0%), over = 97, worst = 3
PHY-1002 : len = 74168, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 74184, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 74312, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.147153s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 31.96, top5 = 22.93, top10 = 18.05, top15 = 14.32.
PHY-1001 : End global routing;  0.198341s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (110.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 208, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 502, reserve = 471, peak = 502.
PHY-1001 : End build detailed router design. 3.257351s wall, 3.203125s user + 0.046875s system = 3.250000s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33808, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.241521s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End phase 1; 1.247240s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 185944, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End initial routed; 1.108777s wall, 1.890625s user + 0.078125s system = 1.968750s CPU (177.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1844(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.137   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.376175s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 539, reserve = 508, peak = 539.
PHY-1001 : End phase 2; 1.485039s wall, 2.265625s user + 0.078125s system = 2.343750s CPU (157.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 185944, over cnt = 37(0%), over = 37, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015109s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 185712, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.035446s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 185768, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.029983s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (104.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1844(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.137   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.374284s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.182854s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.5%)

PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End phase 3; 0.765357s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (100.0%)

PHY-1003 : Routed, final wirelength = 185768
PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End export database. 0.009987s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (156.5%)

PHY-1001 : End detail routing;  6.948452s wall, 7.687500s user + 0.125000s system = 7.812500s CPU (112.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6722, tnet num: 2081, tinst num: 860, tnode num: 9080, tedge num: 11931.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.889817s wall, 8.640625s user + 0.125000s system = 8.765625s CPU (111.1%)

RUN-1004 : used memory is 505 MB, reserved memory is 473 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      838   out of  19600    4.28%
#reg                     1075   out of  19600    5.48%
#le                      1565
  #lut only               490   out of   1565   31.31%
  #reg only               727   out of   1565   46.45%
  #lut&reg                348   out of   1565   22.24%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                     Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                      477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                      108
#3        wendu/clk_us                    GCLK               lslice             signal_process/rs422/p_sum_b_n_syn_5.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                              11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                      1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                            1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1565   |608     |230     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1171   |308     |140     |928     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |33     |24      |9       |22      |0       |0       |
|    demodu                  |Demodulation                                     |538    |126     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |0       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |15      |0       |26      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |103    |25      |15      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |334    |96      |39      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |20      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |110    |99      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |26      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |24     |21      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |52     |52      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1500  
    #2          2       303   
    #3          3       109   
    #4          4        14   
    #5        5-10       82   
    #6        11-50      31   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6722, tnet num: 2081, tinst num: 860, tnode num: 9080, tedge num: 11931.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2081 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 860
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2083, pip num: 15064
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1337 valid insts, and 39983 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.506107s wall, 18.984375s user + 0.078125s system = 19.062500s CPU (543.7%)

RUN-1004 : used memory is 523 MB, reserved memory is 496 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_150530.log"
