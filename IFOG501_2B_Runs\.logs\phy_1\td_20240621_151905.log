============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 15:19:05 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 9 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 38 trigger nets, 38 data nets.
KIT-1004 : Chipwatcher code = 0101111100011010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2214/23 useful/useless nets, 1275/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 1899/20 useful/useless nets, 1682/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 378 better
SYN-1014 : Optimize round 2
SYN-1032 : 1595/45 useful/useless nets, 1378/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1643/295 useful/useless nets, 1459/43 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 383 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 36 instances.
SYN-2501 : Optimize round 1, 74 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2071/5 useful/useless nets, 1887/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8012, tnet num: 2071, tinst num: 1886, tnode num: 10142, tedge num: 12240.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2071 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 514 instances into 223 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 372 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 120 adder to BLE ...
SYN-4008 : Packed 120 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.116119s wall, 1.000000s user + 0.109375s system = 1.109375s CPU (99.4%)

RUN-1004 : used memory is 142 MB, reserved memory is 96 MB, peak memory is 161 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (255 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1302 instances
RUN-0007 : 483 luts, 626 seqs, 86 mslices, 60 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1493 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 830 nets have 2 pins
RUN-1001 : 517 nets have [3 - 5] pins
RUN-1001 : 52 nets have [6 - 10] pins
RUN-1001 : 58 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     146     
RUN-1001 :   No   |  No   |  Yes  |     118     
RUN-1001 :   No   |  Yes  |  No   |     79      
RUN-1001 :   Yes  |  No   |  No   |     32      
RUN-1001 :   Yes  |  No   |  Yes  |     251     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   6   |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 16
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1300 instances, 483 luts, 626 seqs, 146 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-0007 : Cell area utilization is 3%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6745, tnet num: 1491, tinst num: 1300, tnode num: 8970, tedge num: 11213.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.128199s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 349345
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1300.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 3%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 285157, overlap = 76.5
PHY-3002 : Step(2): len = 234846, overlap = 76.5
PHY-3002 : Step(3): len = 207954, overlap = 76.5
PHY-3002 : Step(4): len = 185427, overlap = 76.5
PHY-3002 : Step(5): len = 166202, overlap = 76.5
PHY-3002 : Step(6): len = 148855, overlap = 76.5
PHY-3002 : Step(7): len = 133921, overlap = 76.5
PHY-3002 : Step(8): len = 117884, overlap = 76.5
PHY-3002 : Step(9): len = 105217, overlap = 76.5
PHY-3002 : Step(10): len = 95704.6, overlap = 76.5
PHY-3002 : Step(11): len = 82659.7, overlap = 76.5
PHY-3002 : Step(12): len = 77574.9, overlap = 76.5
PHY-3002 : Step(13): len = 70921.9, overlap = 76.5
PHY-3002 : Step(14): len = 63230.7, overlap = 76.5
PHY-3002 : Step(15): len = 58792.9, overlap = 76.5
PHY-3002 : Step(16): len = 56425.1, overlap = 76.5
PHY-3002 : Step(17): len = 53726.9, overlap = 76.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.23763e-06
PHY-3002 : Step(18): len = 52818.2, overlap = 74.25
PHY-3002 : Step(19): len = 53949.8, overlap = 67.5
PHY-3002 : Step(20): len = 51860.7, overlap = 63
PHY-3002 : Step(21): len = 50851.1, overlap = 76.5
PHY-3002 : Step(22): len = 49938.4, overlap = 76.5
PHY-3002 : Step(23): len = 48948.2, overlap = 76.5
PHY-3002 : Step(24): len = 48004.6, overlap = 76.5
PHY-3002 : Step(25): len = 46760.1, overlap = 72
PHY-3002 : Step(26): len = 45159.7, overlap = 76.5
PHY-3002 : Step(27): len = 42389.7, overlap = 76.5
PHY-3002 : Step(28): len = 40311.1, overlap = 74.25
PHY-3002 : Step(29): len = 39564.2, overlap = 76.5
PHY-3002 : Step(30): len = 39120.2, overlap = 76.5625
PHY-3002 : Step(31): len = 38731.8, overlap = 72
PHY-3002 : Step(32): len = 38018.5, overlap = 72
PHY-3002 : Step(33): len = 37582.5, overlap = 67.625
PHY-3002 : Step(34): len = 36953.3, overlap = 72.0625
PHY-3002 : Step(35): len = 36235.5, overlap = 67.5
PHY-3002 : Step(36): len = 35304.7, overlap = 67.625
PHY-3002 : Step(37): len = 34542.5, overlap = 69.75
PHY-3002 : Step(38): len = 33780.3, overlap = 67.5
PHY-3002 : Step(39): len = 33270.9, overlap = 65.25
PHY-3002 : Step(40): len = 32802.7, overlap = 65.25
PHY-3002 : Step(41): len = 32465.5, overlap = 65.25
PHY-3002 : Step(42): len = 31944.9, overlap = 65.25
PHY-3002 : Step(43): len = 31511.6, overlap = 69.75
PHY-3002 : Step(44): len = 31374.8, overlap = 69.75
PHY-3002 : Step(45): len = 31086.5, overlap = 69.75
PHY-3002 : Step(46): len = 30775.9, overlap = 69.75
PHY-3002 : Step(47): len = 30683.2, overlap = 67.5
PHY-3002 : Step(48): len = 30328.2, overlap = 69.75
PHY-3002 : Step(49): len = 30177.4, overlap = 69.75
PHY-3002 : Step(50): len = 30062, overlap = 67.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 6.47526e-06
PHY-3002 : Step(51): len = 30055.3, overlap = 63.25
PHY-3002 : Step(52): len = 30094.2, overlap = 63.25
PHY-3002 : Step(53): len = 30125.3, overlap = 63.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 1.29505e-05
PHY-3002 : Step(54): len = 30260.9, overlap = 63.25
PHY-3002 : Step(55): len = 30299.3, overlap = 63.25
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 2.5901e-05
PHY-3002 : Step(56): len = 30315.3, overlap = 63.25
PHY-3002 : Step(57): len = 30325.7, overlap = 63.25
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 5.18021e-05
PHY-3002 : Step(58): len = 30349.1, overlap = 63.25
PHY-3002 : Step(59): len = 30357.1, overlap = 63.3125
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 8.38158e-05
PHY-3002 : Step(60): len = 30390.7, overlap = 63.3125
PHY-3002 : Step(61): len = 30435.6, overlap = 63.375
PHY-3002 : Step(62): len = 30937.1, overlap = 63.375
PHY-3002 : Step(63): len = 30942.4, overlap = 63.4375
PHY-3002 : Step(64): len = 30860.1, overlap = 63.5
PHY-3002 : Step(65): len = 30816.2, overlap = 63.5
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.000167632
PHY-3002 : Step(66): len = 30828.5, overlap = 63.5
PHY-3002 : Step(67): len = 30828.5, overlap = 63.5
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.000335263
PHY-3002 : Step(68): len = 30827.1, overlap = 63.5
PHY-3002 : Step(69): len = 30827.1, overlap = 63.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007671s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (203.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.036951s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00042535
PHY-3002 : Step(70): len = 36320.6, overlap = 11.5938
PHY-3002 : Step(71): len = 36370.8, overlap = 11.5938
PHY-3002 : Step(72): len = 36302.6, overlap = 12.0938
PHY-3002 : Step(73): len = 36272.8, overlap = 11.9688
PHY-3002 : Step(74): len = 36374.2, overlap = 15.2188
PHY-3002 : Step(75): len = 36323.4, overlap = 16.0938
PHY-3002 : Step(76): len = 36314.8, overlap = 14.8125
PHY-3002 : Step(77): len = 36414.4, overlap = 15.5938
PHY-3002 : Step(78): len = 36225, overlap = 16.6562
PHY-3002 : Step(79): len = 36167.1, overlap = 16.9062
PHY-3002 : Step(80): len = 36114.4, overlap = 17.4688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037176s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (126.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.36323e-05
PHY-3002 : Step(81): len = 36450.6, overlap = 52.8438
PHY-3002 : Step(82): len = 36799.1, overlap = 52.125
PHY-3002 : Step(83): len = 38139.5, overlap = 46.5938
PHY-3002 : Step(84): len = 38993.6, overlap = 44.25
PHY-3002 : Step(85): len = 39139.1, overlap = 41.6562
PHY-3002 : Step(86): len = 39071, overlap = 36.4062
PHY-3002 : Step(87): len = 38780.1, overlap = 35.625
PHY-3002 : Step(88): len = 38705.5, overlap = 32.7812
PHY-3002 : Step(89): len = 38670.6, overlap = 32.125
PHY-3002 : Step(90): len = 38645.6, overlap = 32.5
PHY-3002 : Step(91): len = 38584.8, overlap = 31.25
PHY-3002 : Step(92): len = 38502.1, overlap = 29.875
PHY-3002 : Step(93): len = 38703.6, overlap = 27.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000127265
PHY-3002 : Step(94): len = 38481.7, overlap = 28.5
PHY-3002 : Step(95): len = 38456.5, overlap = 28.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000254529
PHY-3002 : Step(96): len = 38366.1, overlap = 28.7188
PHY-3002 : Step(97): len = 38366.1, overlap = 28.7188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6745, tnet num: 1491, tinst num: 1300, tnode num: 8970, tedge num: 11213.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 71.47 peak overflow 3.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1493.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 47024, over cnt = 209(0%), over = 771, worst = 17
PHY-1001 : End global iterations;  0.083194s wall, 0.109375s user + 0.046875s system = 0.156250s CPU (187.8%)

PHY-1001 : Congestion index: top1 = 36.85, top5 = 21.47, top10 = 13.78, top15 = 9.78.
PHY-1001 : End incremental global routing;  0.134311s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (162.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045678s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1288 has valid locations, 34 needs to be replaced
PHY-3001 : design contains 1333 instances, 483 luts, 659 seqs, 146 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 38796.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6877, tnet num: 1524, tinst num: 1333, tnode num: 9201, tedge num: 11411.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.139981s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(98): len = 39427.6, overlap = 3.75
PHY-3002 : Step(99): len = 39807.3, overlap = 3.75
PHY-3002 : Step(100): len = 40069.5, overlap = 3.75
PHY-3002 : Step(101): len = 40081.4, overlap = 3.75
PHY-3002 : Step(102): len = 40072.7, overlap = 3.75
PHY-3002 : Step(103): len = 40056.7, overlap = 3.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038428s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (122.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(104): len = 40002.8, overlap = 29.0312
PHY-3002 : Step(105): len = 40002.8, overlap = 29.0312
PHY-3001 : Final: Len = 40002.8, Over = 29.0312
PHY-3001 : End incremental placement;  0.275853s wall, 0.296875s user + 0.109375s system = 0.406250s CPU (147.3%)

OPT-1001 : Total overflow 72.59 peak overflow 3.00
OPT-1001 : End high-fanout net optimization;  0.486806s wall, 0.531250s user + 0.156250s system = 0.687500s CPU (141.2%)

OPT-1001 : Current memory(MB): used = 197, reserve = 151, peak = 197.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 981/1526.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49032, over cnt = 208(0%), over = 747, worst = 16
PHY-1002 : len = 55576, over cnt = 131(0%), over = 231, worst = 6
PHY-1002 : len = 56616, over cnt = 83(0%), over = 139, worst = 6
PHY-1002 : len = 58048, over cnt = 16(0%), over = 32, worst = 5
PHY-1002 : len = 58472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125160s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (112.4%)

PHY-1001 : Congestion index: top1 = 35.67, top5 = 22.35, top10 = 15.31, top15 = 11.20.
OPT-1001 : End congestion update;  0.169179s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (110.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.041437s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (113.1%)

OPT-0007 : Start: WNS 3301 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.210860s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (111.2%)

OPT-1001 : Current memory(MB): used = 196, reserve = 150, peak = 197.
OPT-1001 : End physical optimization;  0.814626s wall, 0.843750s user + 0.187500s system = 1.031250s CPU (126.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 483 LUT to BLE ...
SYN-4008 : Packed 483 LUT and 162 SEQ to BLE.
SYN-4003 : Packing 497 remaining SEQ's ...
SYN-4005 : Packed 249 SEQ with LUT/SLICE
SYN-4006 : 103 single LUT's are left
SYN-4006 : 248 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 731/1058 primitive instances ...
PHY-3001 : End packing;  0.043261s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (108.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 605 instances
RUN-1001 : 279 mslices, 279 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1366 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 656 nets have 2 pins
RUN-1001 : 562 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 51 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 603 instances, 558 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 41036.4, Over = 40.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6053, tnet num: 1364, tinst num: 603, tnode num: 7771, tedge num: 10358.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1364 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.149467s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (94.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.91089e-05
PHY-3002 : Step(106): len = 40857.2, overlap = 39.25
PHY-3002 : Step(107): len = 40907.2, overlap = 39.25
PHY-3002 : Step(108): len = 40807.3, overlap = 40.5
PHY-3002 : Step(109): len = 40896.7, overlap = 41.5
PHY-3002 : Step(110): len = 40857.7, overlap = 43
PHY-3002 : Step(111): len = 40887.5, overlap = 44
PHY-3002 : Step(112): len = 41027.4, overlap = 43.25
PHY-3002 : Step(113): len = 40926.7, overlap = 43.25
PHY-3002 : Step(114): len = 40801.7, overlap = 41.25
PHY-3002 : Step(115): len = 40602.1, overlap = 39.25
PHY-3002 : Step(116): len = 40498.4, overlap = 42
PHY-3002 : Step(117): len = 40161.3, overlap = 43.25
PHY-3002 : Step(118): len = 39971.5, overlap = 45.25
PHY-3002 : Step(119): len = 39914, overlap = 41.5
PHY-3002 : Step(120): len = 39531.6, overlap = 42.5
PHY-3002 : Step(121): len = 39415.7, overlap = 41.25
PHY-3002 : Step(122): len = 39331.7, overlap = 39.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.82178e-05
PHY-3002 : Step(123): len = 39142.2, overlap = 38.75
PHY-3002 : Step(124): len = 39195.1, overlap = 38.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000156436
PHY-3002 : Step(125): len = 39476.3, overlap = 38.25
PHY-3002 : Step(126): len = 39848.3, overlap = 39.25
PHY-3002 : Step(127): len = 40418.4, overlap = 36.25
PHY-3002 : Step(128): len = 40623.1, overlap = 36.75
PHY-3002 : Step(129): len = 40836.5, overlap = 36
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.072629s wall, 0.046875s user + 0.156250s system = 0.203125s CPU (279.7%)

PHY-3001 : Trial Legalized: Len = 53024
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1364 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.034967s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00366846
PHY-3002 : Step(130): len = 49996.5, overlap = 5
PHY-3002 : Step(131): len = 49047.9, overlap = 6
PHY-3002 : Step(132): len = 46983.1, overlap = 8.75
PHY-3002 : Step(133): len = 45931.4, overlap = 11.75
PHY-3002 : Step(134): len = 45225.5, overlap = 14
PHY-3002 : Step(135): len = 44657.7, overlap = 16
PHY-3002 : Step(136): len = 44119.5, overlap = 16.25
PHY-3002 : Step(137): len = 43892.7, overlap = 16.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00733691
PHY-3002 : Step(138): len = 43836.9, overlap = 17
PHY-3002 : Step(139): len = 43653.3, overlap = 16.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0146738
PHY-3002 : Step(140): len = 43652.6, overlap = 15.75
PHY-3002 : Step(141): len = 43583.9, overlap = 15.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004884s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 48717.8, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004191s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 2, deltaY = 2, maxDist = 1.
PHY-3001 : Final: Len = 48745.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6053, tnet num: 1364, tinst num: 603, tnode num: 7771, tedge num: 10358.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 6/1366.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 62320, over cnt = 185(0%), over = 273, worst = 4
PHY-1002 : len = 63336, over cnt = 113(0%), over = 149, worst = 4
PHY-1002 : len = 64056, over cnt = 61(0%), over = 77, worst = 3
PHY-1002 : len = 65160, over cnt = 7(0%), over = 10, worst = 3
PHY-1002 : len = 65320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.151548s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (123.7%)

PHY-1001 : Congestion index: top1 = 28.38, top5 = 21.90, top10 = 17.22, top15 = 13.16.
PHY-1001 : End incremental global routing;  0.200010s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (117.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1364 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043538s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (107.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.269055s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (110.3%)

OPT-1001 : Current memory(MB): used = 198, reserve = 153, peak = 199.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1190/1366.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005234s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 28.38, top5 = 21.90, top10 = 17.22, top15 = 13.16.
OPT-1001 : End congestion update;  0.048912s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1364 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.033785s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (138.7%)

OPT-0007 : Start: WNS 3391 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.082921s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (113.1%)

OPT-1001 : Current memory(MB): used = 200, reserve = 155, peak = 200.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1364 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032206s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1190/1366.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004759s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (328.3%)

PHY-1001 : Congestion index: top1 = 28.38, top5 = 21.90, top10 = 17.22, top15 = 13.16.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1364 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.039476s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3391 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 27.931034
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3391ps with logic level 7 
RUN-1001 :       #2 path slack 3445ps with logic level 6 
OPT-1001 : End physical optimization;  0.617295s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (103.8%)

RUN-1003 : finish command "place" in  4.010754s wall, 5.390625s user + 2.500000s system = 7.890625s CPU (196.7%)

RUN-1004 : used memory is 185 MB, reserved memory is 138 MB, peak memory is 201 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 605 instances
RUN-1001 : 279 mslices, 279 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1366 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 656 nets have 2 pins
RUN-1001 : 562 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 51 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6053, tnet num: 1364, tinst num: 603, tnode num: 7771, tedge num: 10358.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 279 mslices, 279 lslices, 8 pads, 34 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1364 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 61408, over cnt = 205(0%), over = 324, worst = 4
PHY-1002 : len = 62624, over cnt = 125(0%), over = 174, worst = 4
PHY-1002 : len = 63840, over cnt = 50(0%), over = 64, worst = 3
PHY-1002 : len = 64872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.165622s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (122.6%)

PHY-1001 : Congestion index: top1 = 28.41, top5 = 21.96, top10 = 17.24, top15 = 13.14.
PHY-1001 : End global routing;  0.213535s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (117.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 220, reserve = 174, peak = 220.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 479, reserve = 437, peak = 479.
PHY-1001 : End build detailed router design. 3.147300s wall, 3.093750s user + 0.062500s system = 3.156250s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 29768, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.607480s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 511, reserve = 470, peak = 511.
PHY-1001 : End phase 1; 0.613171s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 68% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 242600, over cnt = 64(0%), over = 64, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 513, reserve = 472, peak = 514.
PHY-1001 : End initial routed; 2.834454s wall, 3.421875s user + 0.093750s system = 3.515625s CPU (124.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1229(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.832   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.199802s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (101.7%)

PHY-1001 : Current memory(MB): used = 515, reserve = 474, peak = 515.
PHY-1001 : End phase 2; 3.034341s wall, 3.625000s user + 0.093750s system = 3.718750s CPU (122.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 242600, over cnt = 64(0%), over = 64, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.011456s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (136.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 241792, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.210684s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (96.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 241840, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.084227s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (111.3%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 241808, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.017987s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (86.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1229(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.832   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.205015s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 11 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.184789s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 529, reserve = 487, peak = 529.
PHY-1001 : End phase 3; 0.825898s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.3%)

PHY-1003 : Routed, final wirelength = 241808
PHY-1001 : Current memory(MB): used = 529, reserve = 488, peak = 529.
PHY-1001 : End export database. 0.010367s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (150.7%)

PHY-1001 : End detail routing;  7.811200s wall, 8.328125s user + 0.156250s system = 8.484375s CPU (108.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6053, tnet num: 1364, tinst num: 603, tnode num: 7771, tedge num: 10358.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.405851s wall, 8.921875s user + 0.171875s system = 9.093750s CPU (108.2%)

RUN-1004 : used memory is 485 MB, reserved memory is 444 MB, peak memory is 529 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   5
  #inout                    0

Utilization Statistics
#lut                      801   out of  19600    4.09%
#reg                      672   out of  19600    3.43%
#le                      1049
  #lut only               377   out of   1049   35.94%
  #reg only               248   out of   1049   23.64%
  #lut&reg                424   out of   1049   40.42%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     5
  #treg                     0
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    297
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         140
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
    dq        OUTPUT        B16        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1049   |655     |146     |678     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |94     |71      |11      |62      |0       |0       |
|    usms                            |Time_1ms        |26     |11      |5       |17      |0       |0       |
|  SPIM                              |SPI_MASTER      |203    |131     |23      |130     |0       |0       |
|  uart                              |UART_Control    |128    |110     |7       |61      |0       |0       |
|    U0                              |speed_select_Tx |24     |15      |7       |17      |0       |0       |
|    U1                              |uart_tx         |26     |22      |0       |19      |0       |0       |
|    U2                              |Ctrl_Data       |78     |73      |0       |25      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |591    |326     |99      |397     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |591    |326     |99      |397     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |250    |105     |0       |247     |0       |0       |
|        reg_inst                    |register        |247    |102     |0       |244     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |341    |221     |99      |150     |0       |0       |
|        bus_inst                    |bus_top         |116    |71      |42      |37      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |14     |8       |6       |3       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |29     |19      |10      |10      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |2      |0       |0       |2       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |2      |2       |0       |2       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |69     |42      |26      |20      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |133    |88      |29      |80      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       647   
    #2          2       338   
    #3          3       175   
    #4          4        49   
    #5        5-10       63   
    #6        11-50      71   
    #7       51-100      3    
    #8       101-500     1    
  Average     3.14            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6053, tnet num: 1364, tinst num: 603, tnode num: 7771, tedge num: 10358.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1364 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 603
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1366, pip num: 15607
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1326 valid insts, and 40681 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011001110101111100011010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.267914s wall, 17.140625s user + 0.093750s system = 17.234375s CPU (527.4%)

RUN-1004 : used memory is 502 MB, reserved memory is 460 MB, peak memory is 649 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_151905.log"
