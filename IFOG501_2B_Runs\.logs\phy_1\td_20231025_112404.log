============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Oct 25 11:24:04 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1523 instances
RUN-0007 : 367 luts, 900 seqs, 132 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2077 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1531 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 68 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1521 instances, 367 luts, 900 seqs, 207 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7358, tnet num: 2075, tinst num: 1521, tnode num: 10344, tedge num: 12507.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.271673s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (97.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 573315
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1521.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 503130, overlap = 18
PHY-3002 : Step(2): len = 469531, overlap = 11.25
PHY-3002 : Step(3): len = 439619, overlap = 13.5
PHY-3002 : Step(4): len = 420910, overlap = 11.25
PHY-3002 : Step(5): len = 407897, overlap = 11.25
PHY-3002 : Step(6): len = 398383, overlap = 15.75
PHY-3002 : Step(7): len = 383874, overlap = 13.5
PHY-3002 : Step(8): len = 371707, overlap = 15.75
PHY-3002 : Step(9): len = 362103, overlap = 15.75
PHY-3002 : Step(10): len = 353775, overlap = 15.75
PHY-3002 : Step(11): len = 341726, overlap = 15.75
PHY-3002 : Step(12): len = 333699, overlap = 18
PHY-3002 : Step(13): len = 326624, overlap = 15.75
PHY-3002 : Step(14): len = 316302, overlap = 15.75
PHY-3002 : Step(15): len = 308602, overlap = 15.75
PHY-3002 : Step(16): len = 302623, overlap = 20.25
PHY-3002 : Step(17): len = 291456, overlap = 13.5
PHY-3002 : Step(18): len = 283840, overlap = 18
PHY-3002 : Step(19): len = 277871, overlap = 15.75
PHY-3002 : Step(20): len = 270923, overlap = 18
PHY-3002 : Step(21): len = 256257, overlap = 15.75
PHY-3002 : Step(22): len = 250111, overlap = 18
PHY-3002 : Step(23): len = 245034, overlap = 15.75
PHY-3002 : Step(24): len = 224715, overlap = 15.75
PHY-3002 : Step(25): len = 214546, overlap = 13.5
PHY-3002 : Step(26): len = 212395, overlap = 15.75
PHY-3002 : Step(27): len = 187991, overlap = 18
PHY-3002 : Step(28): len = 171514, overlap = 15.75
PHY-3002 : Step(29): len = 168396, overlap = 15.75
PHY-3002 : Step(30): len = 162832, overlap = 11.25
PHY-3002 : Step(31): len = 143470, overlap = 18
PHY-3002 : Step(32): len = 140475, overlap = 18
PHY-3002 : Step(33): len = 138119, overlap = 15.75
PHY-3002 : Step(34): len = 134980, overlap = 15.75
PHY-3002 : Step(35): len = 132153, overlap = 13.5
PHY-3002 : Step(36): len = 128670, overlap = 15.75
PHY-3002 : Step(37): len = 125781, overlap = 13.5
PHY-3002 : Step(38): len = 122824, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100525
PHY-3002 : Step(39): len = 122565, overlap = 15.75
PHY-3002 : Step(40): len = 121902, overlap = 13.5
PHY-3002 : Step(41): len = 120778, overlap = 11.25
PHY-3002 : Step(42): len = 119619, overlap = 15.75
PHY-3002 : Step(43): len = 118616, overlap = 15.75
PHY-3002 : Step(44): len = 116170, overlap = 15.75
PHY-3002 : Step(45): len = 110167, overlap = 13.5
PHY-3002 : Step(46): len = 108972, overlap = 13.5
PHY-3002 : Step(47): len = 107470, overlap = 13.5
PHY-3002 : Step(48): len = 103153, overlap = 13.5
PHY-3002 : Step(49): len = 100945, overlap = 13.5
PHY-3002 : Step(50): len = 99525.9, overlap = 13.5
PHY-3002 : Step(51): len = 97361.7, overlap = 15.75
PHY-3002 : Step(52): len = 95817, overlap = 13.5
PHY-3002 : Step(53): len = 94525.9, overlap = 13.5
PHY-3002 : Step(54): len = 89957.6, overlap = 11.25
PHY-3002 : Step(55): len = 89252.2, overlap = 11.25
PHY-3002 : Step(56): len = 87547, overlap = 11.25
PHY-3002 : Step(57): len = 85893.8, overlap = 13.5
PHY-3002 : Step(58): len = 84246.8, overlap = 9
PHY-3002 : Step(59): len = 82579, overlap = 11.25
PHY-3002 : Step(60): len = 79507.4, overlap = 11.25
PHY-3002 : Step(61): len = 78599, overlap = 11.25
PHY-3002 : Step(62): len = 77622.9, overlap = 11.25
PHY-3002 : Step(63): len = 75138.2, overlap = 11.25
PHY-3002 : Step(64): len = 74156.9, overlap = 11.25
PHY-3002 : Step(65): len = 72184.7, overlap = 11.25
PHY-3002 : Step(66): len = 71131, overlap = 11.5
PHY-3002 : Step(67): len = 69713.4, overlap = 11.6875
PHY-3002 : Step(68): len = 69031.6, overlap = 12.0625
PHY-3002 : Step(69): len = 66350.5, overlap = 15.0625
PHY-3002 : Step(70): len = 65110.8, overlap = 12.875
PHY-3002 : Step(71): len = 65136.8, overlap = 11
PHY-3002 : Step(72): len = 64902.4, overlap = 13.25
PHY-3002 : Step(73): len = 64093.1, overlap = 10.875
PHY-3002 : Step(74): len = 63840.8, overlap = 10.8125
PHY-3002 : Step(75): len = 63960.7, overlap = 10.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000201049
PHY-3002 : Step(76): len = 63745.5, overlap = 10.75
PHY-3002 : Step(77): len = 63747.5, overlap = 10.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000402099
PHY-3002 : Step(78): len = 63952.8, overlap = 10.6875
PHY-3002 : Step(79): len = 63904.1, overlap = 10.6875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006495s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056770s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00100352
PHY-3002 : Step(80): len = 66502.3, overlap = 14.5
PHY-3002 : Step(81): len = 66556, overlap = 14.7812
PHY-3002 : Step(82): len = 65069.9, overlap = 16.6562
PHY-3002 : Step(83): len = 64607.7, overlap = 17.3125
PHY-3002 : Step(84): len = 62433.7, overlap = 13.7188
PHY-3002 : Step(85): len = 61473, overlap = 13.8125
PHY-3002 : Step(86): len = 60717.2, overlap = 13.8125
PHY-3002 : Step(87): len = 59218.2, overlap = 12.375
PHY-3002 : Step(88): len = 57727.7, overlap = 11.625
PHY-3002 : Step(89): len = 57030.6, overlap = 11.3438
PHY-3002 : Step(90): len = 56369.8, overlap = 12.2188
PHY-3002 : Step(91): len = 55470.3, overlap = 11.2812
PHY-3002 : Step(92): len = 54630.3, overlap = 11.8125
PHY-3002 : Step(93): len = 54143.6, overlap = 10.7812
PHY-3002 : Step(94): len = 53681.6, overlap = 10.8438
PHY-3002 : Step(95): len = 52672.6, overlap = 13.6562
PHY-3002 : Step(96): len = 52293.8, overlap = 13.6562
PHY-3002 : Step(97): len = 51864.1, overlap = 15.7188
PHY-3002 : Step(98): len = 51088.8, overlap = 15.7812
PHY-3002 : Step(99): len = 50433.2, overlap = 13.9062
PHY-3002 : Step(100): len = 49726.5, overlap = 14.7188
PHY-3002 : Step(101): len = 48928.6, overlap = 16.25
PHY-3002 : Step(102): len = 48036.2, overlap = 16.0625
PHY-3002 : Step(103): len = 47580.6, overlap = 16.5625
PHY-3002 : Step(104): len = 47413.7, overlap = 17.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00200704
PHY-3002 : Step(105): len = 47223.3, overlap = 17
PHY-3002 : Step(106): len = 47216.6, overlap = 17.0625
PHY-3002 : Step(107): len = 47117.3, overlap = 17.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00401408
PHY-3002 : Step(108): len = 46827.4, overlap = 17.25
PHY-3002 : Step(109): len = 46827.4, overlap = 17.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056789s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.41756e-05
PHY-3002 : Step(110): len = 47665.5, overlap = 54.9688
PHY-3002 : Step(111): len = 48502.4, overlap = 53.0625
PHY-3002 : Step(112): len = 48505.4, overlap = 50.3125
PHY-3002 : Step(113): len = 47898.2, overlap = 51.1562
PHY-3002 : Step(114): len = 47516.5, overlap = 50.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000108351
PHY-3002 : Step(115): len = 47898.4, overlap = 50.5312
PHY-3002 : Step(116): len = 48389, overlap = 49.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000216702
PHY-3002 : Step(117): len = 48368, overlap = 48.875
PHY-3002 : Step(118): len = 49896.9, overlap = 46.3125
PHY-3002 : Step(119): len = 51704.6, overlap = 46.375
PHY-3002 : Step(120): len = 51674.2, overlap = 42.7188
PHY-3002 : Step(121): len = 51398.9, overlap = 39.8125
PHY-3002 : Step(122): len = 50548.6, overlap = 37.6875
PHY-3002 : Step(123): len = 50418.2, overlap = 37.2188
PHY-3002 : Step(124): len = 50285.9, overlap = 37.9062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000421142
PHY-3002 : Step(125): len = 51294.7, overlap = 36.375
PHY-3002 : Step(126): len = 51255.3, overlap = 35.9688
PHY-3002 : Step(127): len = 51467.6, overlap = 35.3438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000713059
PHY-3002 : Step(128): len = 52164.9, overlap = 34.7812
PHY-3002 : Step(129): len = 52299.9, overlap = 34.7188
PHY-3002 : Step(130): len = 53144.7, overlap = 33.4062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7358, tnet num: 2075, tinst num: 1521, tnode num: 10344, tedge num: 12507.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.75 peak overflow 2.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2077.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55520, over cnt = 249(0%), over = 991, worst = 23
PHY-1001 : End global iterations;  0.078543s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (139.3%)

PHY-1001 : Congestion index: top1 = 43.25, top5 = 25.96, top10 = 16.11, top15 = 11.34.
PHY-1001 : End incremental global routing;  0.126699s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (123.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061492s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (101.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.217766s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (114.8%)

OPT-1001 : Current memory(MB): used = 212, reserve = 175, peak = 212.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1560/2077.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55520, over cnt = 249(0%), over = 991, worst = 23
PHY-1002 : len = 61344, over cnt = 144(0%), over = 403, worst = 14
PHY-1002 : len = 66032, over cnt = 28(0%), over = 47, worst = 10
PHY-1002 : len = 66736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.093348s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.4%)

PHY-1001 : Congestion index: top1 = 36.68, top5 = 25.07, top10 = 18.03, top15 = 13.09.
OPT-1001 : End congestion update;  0.136488s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2075 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057389s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.197256s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.1%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : End physical optimization;  0.689111s wall, 0.671875s user + 0.046875s system = 0.718750s CPU (104.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 164 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 124 SEQ with LUT/SLICE
SYN-4006 : 98 single LUT's are left
SYN-4006 : 612 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 979/1269 primitive instances ...
PHY-3001 : End packing;  0.045856s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 775 instances
RUN-1001 : 363 mslices, 363 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1921 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1378 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 773 instances, 726 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 53111.8, Over = 57.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6242, tnet num: 1919, tinst num: 773, tnode num: 8412, tedge num: 11018.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.290923s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.11547e-05
PHY-3002 : Step(131): len = 52231.2, overlap = 56.75
PHY-3002 : Step(132): len = 51269.6, overlap = 59
PHY-3002 : Step(133): len = 50780.6, overlap = 58
PHY-3002 : Step(134): len = 50765.5, overlap = 56.25
PHY-3002 : Step(135): len = 50448.4, overlap = 55
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.23095e-05
PHY-3002 : Step(136): len = 50859.2, overlap = 55.25
PHY-3002 : Step(137): len = 51795.1, overlap = 54.25
PHY-3002 : Step(138): len = 52288.2, overlap = 54.5
PHY-3002 : Step(139): len = 52240.3, overlap = 54.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000124619
PHY-3002 : Step(140): len = 52702.8, overlap = 53.75
PHY-3002 : Step(141): len = 53553.7, overlap = 52.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078744s wall, 0.078125s user + 0.125000s system = 0.203125s CPU (258.0%)

PHY-3001 : Trial Legalized: Len = 68236.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046807s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0010956
PHY-3002 : Step(142): len = 65232, overlap = 6
PHY-3002 : Step(143): len = 63934.9, overlap = 10.25
PHY-3002 : Step(144): len = 61992.7, overlap = 14.5
PHY-3002 : Step(145): len = 60884.1, overlap = 18.5
PHY-3002 : Step(146): len = 60311.8, overlap = 20.25
PHY-3002 : Step(147): len = 59769.5, overlap = 22
PHY-3002 : Step(148): len = 59527.8, overlap = 23.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00207421
PHY-3002 : Step(149): len = 59741.3, overlap = 22.5
PHY-3002 : Step(150): len = 59776.2, overlap = 22.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00414842
PHY-3002 : Step(151): len = 59939.9, overlap = 23
PHY-3002 : Step(152): len = 59999.9, overlap = 22.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005032s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64378.1, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005170s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (302.2%)

PHY-3001 : 10 instances has been re-located, deltaX = 0, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 64386.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6242, tnet num: 1919, tinst num: 773, tnode num: 8412, tedge num: 11018.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 73/1921.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70512, over cnt = 156(0%), over = 208, worst = 3
PHY-1002 : len = 71176, over cnt = 60(0%), over = 72, worst = 3
PHY-1002 : len = 71904, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 71968, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.147894s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (95.1%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 23.03, top10 = 18.10, top15 = 14.08.
PHY-1001 : End incremental global routing;  0.198271s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (94.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055700s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.282902s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (99.4%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1695/1921.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006608s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 23.03, top10 = 18.10, top15 = 14.08.
OPT-1001 : End congestion update;  0.052357s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046982s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 735 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 773 instances, 726 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64393.8, Over = 0
PHY-3001 : End spreading;  0.004791s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64393.8, Over = 0
PHY-3001 : End incremental legalization;  0.032570s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (143.9%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.145036s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (97.0%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046169s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1687/1921.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72048, over cnt = 2(0%), over = 3, worst = 2
PHY-1002 : len = 72032, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.026850s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (116.4%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 23.04, top10 = 18.10, top15 = 14.09.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046606s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.870956s wall, 0.843750s user + 0.031250s system = 0.875000s CPU (100.5%)

RUN-1003 : finish command "place" in  5.184140s wall, 8.015625s user + 2.843750s system = 10.859375s CPU (209.5%)

RUN-1004 : used memory is 198 MB, reserved memory is 161 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 775 instances
RUN-1001 : 363 mslices, 363 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1921 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1378 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6242, tnet num: 1919, tinst num: 773, tnode num: 8412, tedge num: 11018.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 363 mslices, 363 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69592, over cnt = 156(0%), over = 204, worst = 3
PHY-1002 : len = 70400, over cnt = 63(0%), over = 72, worst = 2
PHY-1002 : len = 71216, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.140298s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (111.4%)

PHY-1001 : Congestion index: top1 = 31.10, top5 = 22.83, top10 = 17.97, top15 = 13.93.
PHY-1001 : End global routing;  0.188663s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (107.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 202, peak = 238.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 464, peak = 497.
PHY-1001 : End build detailed router design. 3.222190s wall, 3.171875s user + 0.046875s system = 3.218750s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33144, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.339945s wall, 1.343750s user + 0.000000s system = 1.343750s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 529.
PHY-1001 : End phase 1; 1.345588s wall, 1.359375s user + 0.000000s system = 1.359375s CPU (101.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 175256, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 498, peak = 531.
PHY-1001 : End initial routed; 1.286545s wall, 2.203125s user + 0.218750s system = 2.421875s CPU (188.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1699(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.054   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.086   |  -0.175   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.352175s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (97.6%)

PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 533.
PHY-1001 : End phase 2; 1.638810s wall, 2.546875s user + 0.218750s system = 2.765625s CPU (168.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 175256, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014204s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 175104, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025368s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (184.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 175168, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023231s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (67.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1699(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.054   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.086   |  -0.175   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.340719s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (96.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.174908s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.2%)

PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End phase 3; 0.703278s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (104.4%)

PHY-1003 : Routed, final wirelength = 175168
PHY-1001 : Current memory(MB): used = 549, reserve = 516, peak = 549.
PHY-1001 : End export database. 0.010005s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.102424s wall, 7.984375s user + 0.265625s system = 8.250000s CPU (116.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6242, tnet num: 1919, tinst num: 773, tnode num: 8412, tedge num: 11018.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6264, tnet num: 1930, tinst num: 784, tnode num: 8434, tedge num: 11040.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.998784s wall, 2.984375s user + 0.078125s system = 3.062500s CPU (102.1%)

RUN-1003 : finish command "route" in  10.632424s wall, 11.500000s user + 0.359375s system = 11.859375s CPU (111.5%)

RUN-1004 : used memory is 505 MB, reserved memory is 477 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      808   out of  19600    4.12%
#reg                      989   out of  19600    5.05%
#le                      1420
  #lut only               431   out of   1420   30.35%
  #reg only               612   out of   1420   43.10%
  #lut&reg                377   out of   1420   26.55%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         433
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1420   |601     |207     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1019   |292     |115     |834     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |17      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |435    |102     |39      |346     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |36      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |11      |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |7       |0       |13      |0       |0       |
|    integ                   |Integration                                      |136    |31      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |92     |30      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |303    |88      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |108    |101     |7       |62      |0       |0       |
|    U0                      |speed_select_Tx                                  |32     |25      |7       |19      |0       |0       |
|    U1                      |uart_tx                                          |17     |17      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |59     |59      |0       |27      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1353  
    #2          2       293   
    #3          3       120   
    #4          4        17   
    #5        5-10       77   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6264, tnet num: 1930, tinst num: 784, tnode num: 8434, tedge num: 11040.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1930 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 784
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1932, pip num: 14222
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1274 valid insts, and 37723 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.009474s wall, 16.671875s user + 0.062500s system = 16.734375s CPU (556.1%)

RUN-1004 : used memory is 522 MB, reserved memory is 490 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231025_112404.log"
