============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 17:31:35 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1532 instances
RUN-0007 : 384 luts, 895 seqs, 129 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2070 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1550 nets have 2 pins
RUN-1001 : 404 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     243     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1530 instances, 384 luts, 895 seqs, 204 slices, 23 macros(204 instances: 129 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7311, tnet num: 2068, tinst num: 1530, tnode num: 10241, tedge num: 12359.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.279259s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 533906
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1530.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 449453, overlap = 20.25
PHY-3002 : Step(2): len = 415279, overlap = 20.25
PHY-3002 : Step(3): len = 393551, overlap = 15.75
PHY-3002 : Step(4): len = 374435, overlap = 20.25
PHY-3002 : Step(5): len = 368195, overlap = 20.25
PHY-3002 : Step(6): len = 356255, overlap = 20.25
PHY-3002 : Step(7): len = 347389, overlap = 20.25
PHY-3002 : Step(8): len = 342103, overlap = 20.25
PHY-3002 : Step(9): len = 331103, overlap = 18
PHY-3002 : Step(10): len = 321178, overlap = 20.25
PHY-3002 : Step(11): len = 316742, overlap = 18
PHY-3002 : Step(12): len = 307272, overlap = 20.25
PHY-3002 : Step(13): len = 297914, overlap = 15.75
PHY-3002 : Step(14): len = 292829, overlap = 18
PHY-3002 : Step(15): len = 286377, overlap = 18
PHY-3002 : Step(16): len = 280383, overlap = 18
PHY-3002 : Step(17): len = 275392, overlap = 18
PHY-3002 : Step(18): len = 268966, overlap = 18
PHY-3002 : Step(19): len = 263963, overlap = 18
PHY-3002 : Step(20): len = 259083, overlap = 18
PHY-3002 : Step(21): len = 253036, overlap = 18
PHY-3002 : Step(22): len = 248979, overlap = 15.75
PHY-3002 : Step(23): len = 243521, overlap = 15.75
PHY-3002 : Step(24): len = 235718, overlap = 15.75
PHY-3002 : Step(25): len = 231327, overlap = 15.75
PHY-3002 : Step(26): len = 227690, overlap = 15.75
PHY-3002 : Step(27): len = 219293, overlap = 13.5
PHY-3002 : Step(28): len = 213798, overlap = 15.75
PHY-3002 : Step(29): len = 210817, overlap = 13.5
PHY-3002 : Step(30): len = 202489, overlap = 13.5
PHY-3002 : Step(31): len = 192427, overlap = 13.5
PHY-3002 : Step(32): len = 189913, overlap = 13.5
PHY-3002 : Step(33): len = 183875, overlap = 13.5
PHY-3002 : Step(34): len = 132480, overlap = 13.5
PHY-3002 : Step(35): len = 130022, overlap = 11.25
PHY-3002 : Step(36): len = 128382, overlap = 13.5
PHY-3002 : Step(37): len = 120959, overlap = 15.75
PHY-3002 : Step(38): len = 116494, overlap = 13.5
PHY-3002 : Step(39): len = 114801, overlap = 13.5
PHY-3002 : Step(40): len = 112726, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.77655e-05
PHY-3002 : Step(41): len = 112494, overlap = 13.5
PHY-3002 : Step(42): len = 112444, overlap = 15.75
PHY-3002 : Step(43): len = 111850, overlap = 13.5
PHY-3002 : Step(44): len = 110778, overlap = 15.75
PHY-3002 : Step(45): len = 109928, overlap = 13.5
PHY-3002 : Step(46): len = 103356, overlap = 11.25
PHY-3002 : Step(47): len = 98687.5, overlap = 13.5
PHY-3002 : Step(48): len = 96925.8, overlap = 11.25
PHY-3002 : Step(49): len = 95773.2, overlap = 11.25
PHY-3002 : Step(50): len = 93631.3, overlap = 18
PHY-3002 : Step(51): len = 90514.5, overlap = 15.75
PHY-3002 : Step(52): len = 90225.4, overlap = 15.75
PHY-3002 : Step(53): len = 88507.9, overlap = 15.75
PHY-3002 : Step(54): len = 86914.6, overlap = 11.25
PHY-3002 : Step(55): len = 86161.6, overlap = 11.25
PHY-3002 : Step(56): len = 84203.2, overlap = 11.25
PHY-3002 : Step(57): len = 80221.1, overlap = 15.75
PHY-3002 : Step(58): len = 78088.3, overlap = 13.5625
PHY-3002 : Step(59): len = 77271.2, overlap = 11.375
PHY-3002 : Step(60): len = 76088.2, overlap = 11.75
PHY-3002 : Step(61): len = 75577.2, overlap = 11.9375
PHY-3002 : Step(62): len = 74165.4, overlap = 15.125
PHY-3002 : Step(63): len = 71740.2, overlap = 15.5625
PHY-3002 : Step(64): len = 71307.1, overlap = 15.625
PHY-3002 : Step(65): len = 70298.4, overlap = 13.5625
PHY-3002 : Step(66): len = 69380.4, overlap = 11.5625
PHY-3002 : Step(67): len = 68514.4, overlap = 13.625
PHY-3002 : Step(68): len = 67163.5, overlap = 13.625
PHY-3002 : Step(69): len = 66015.2, overlap = 13.6875
PHY-3002 : Step(70): len = 63563, overlap = 16
PHY-3002 : Step(71): len = 63022.2, overlap = 16.0625
PHY-3002 : Step(72): len = 62164.3, overlap = 16
PHY-3002 : Step(73): len = 61533.1, overlap = 13.5625
PHY-3002 : Step(74): len = 61691.8, overlap = 13.625
PHY-3002 : Step(75): len = 61450.1, overlap = 13.5625
PHY-3002 : Step(76): len = 61335.7, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000195531
PHY-3002 : Step(77): len = 61377.8, overlap = 13.625
PHY-3002 : Step(78): len = 61366.4, overlap = 13.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000391062
PHY-3002 : Step(79): len = 61359.1, overlap = 13.625
PHY-3002 : Step(80): len = 61377.8, overlap = 13.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006738s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (463.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062099s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000779974
PHY-3002 : Step(81): len = 64202.8, overlap = 16.5312
PHY-3002 : Step(82): len = 64203.8, overlap = 14.7188
PHY-3002 : Step(83): len = 63020.8, overlap = 14.5938
PHY-3002 : Step(84): len = 62180.9, overlap = 14.5312
PHY-3002 : Step(85): len = 60320, overlap = 15.1562
PHY-3002 : Step(86): len = 59252.6, overlap = 13.375
PHY-3002 : Step(87): len = 58338.1, overlap = 13.875
PHY-3002 : Step(88): len = 57761.4, overlap = 13.8125
PHY-3002 : Step(89): len = 57458.4, overlap = 14.2188
PHY-3002 : Step(90): len = 57177.6, overlap = 16.2188
PHY-3002 : Step(91): len = 56300.3, overlap = 17.375
PHY-3002 : Step(92): len = 55547, overlap = 16.3438
PHY-3002 : Step(93): len = 55137.2, overlap = 15.8125
PHY-3002 : Step(94): len = 54921, overlap = 19.9062
PHY-3002 : Step(95): len = 53957.2, overlap = 15.4688
PHY-3002 : Step(96): len = 52833.6, overlap = 13.4062
PHY-3002 : Step(97): len = 52490.5, overlap = 12.9688
PHY-3002 : Step(98): len = 51948.2, overlap = 13.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00155995
PHY-3002 : Step(99): len = 51587.6, overlap = 12.9688
PHY-3002 : Step(100): len = 51603, overlap = 12.9062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0031199
PHY-3002 : Step(101): len = 51629, overlap = 12.5312
PHY-3002 : Step(102): len = 51691.4, overlap = 12.5312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063604s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.31617e-05
PHY-3002 : Step(103): len = 52007.8, overlap = 45.6562
PHY-3002 : Step(104): len = 52596.5, overlap = 44.875
PHY-3002 : Step(105): len = 53366.2, overlap = 48.0625
PHY-3002 : Step(106): len = 53246.9, overlap = 48.9688
PHY-3002 : Step(107): len = 52486.5, overlap = 45.5938
PHY-3002 : Step(108): len = 52301.3, overlap = 45.1875
PHY-3002 : Step(109): len = 52050.9, overlap = 44.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000186323
PHY-3002 : Step(110): len = 52441.4, overlap = 41.6875
PHY-3002 : Step(111): len = 53064.2, overlap = 38.625
PHY-3002 : Step(112): len = 53483.4, overlap = 38.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000372647
PHY-3002 : Step(113): len = 53688.5, overlap = 36.8125
PHY-3002 : Step(114): len = 54111.3, overlap = 34.25
PHY-3002 : Step(115): len = 55247.6, overlap = 33.2188
PHY-3002 : Step(116): len = 55601.6, overlap = 28.75
PHY-3002 : Step(117): len = 54988.3, overlap = 28.1875
PHY-3002 : Step(118): len = 54450.1, overlap = 28.1875
PHY-3002 : Step(119): len = 54011.8, overlap = 28.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7311, tnet num: 2068, tinst num: 1530, tnode num: 10241, tedge num: 12359.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.72 peak overflow 2.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2070.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56232, over cnt = 239(0%), over = 994, worst = 18
PHY-1001 : End global iterations;  0.079258s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (157.7%)

PHY-1001 : Congestion index: top1 = 44.50, top5 = 25.34, top10 = 15.91, top15 = 11.21.
PHY-1001 : End incremental global routing;  0.128335s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (133.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064754s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.222277s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (119.5%)

OPT-1001 : Current memory(MB): used = 213, reserve = 175, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1551/2070.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56232, over cnt = 239(0%), over = 994, worst = 18
PHY-1002 : len = 62880, over cnt = 177(0%), over = 390, worst = 13
PHY-1002 : len = 66608, over cnt = 40(0%), over = 46, worst = 3
PHY-1002 : len = 67544, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 67600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.102628s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (121.8%)

PHY-1001 : Congestion index: top1 = 38.92, top5 = 25.36, top10 = 17.83, top15 = 12.97.
OPT-1001 : End congestion update;  0.143569s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (108.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2068 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054345s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.201058s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (108.8%)

OPT-1001 : Current memory(MB): used = 215, reserve = 177, peak = 215.
OPT-1001 : End physical optimization;  0.688987s wall, 0.671875s user + 0.078125s system = 0.750000s CPU (108.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 384 LUT to BLE ...
SYN-4008 : Packed 384 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 724 remaining SEQ's ...
SYN-4005 : Packed 127 SEQ with LUT/SLICE
SYN-4006 : 104 single LUT's are left
SYN-4006 : 597 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 981/1268 primitive instances ...
PHY-3001 : End packing;  0.047141s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 773 instances
RUN-1001 : 362 mslices, 362 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1907 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1392 nets have 2 pins
RUN-1001 : 399 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 771 instances, 724 slices, 23 macros(204 instances: 129 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54552.8, Over = 54.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6180, tnet num: 1905, tinst num: 771, tnode num: 8309, tedge num: 10847.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.300817s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.67773e-05
PHY-3002 : Step(120): len = 53774.7, overlap = 54
PHY-3002 : Step(121): len = 52976.1, overlap = 52.25
PHY-3002 : Step(122): len = 52940, overlap = 53.5
PHY-3002 : Step(123): len = 52853.4, overlap = 52.5
PHY-3002 : Step(124): len = 52588.1, overlap = 52.75
PHY-3002 : Step(125): len = 52577.5, overlap = 50.25
PHY-3002 : Step(126): len = 52233.9, overlap = 50.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.35546e-05
PHY-3002 : Step(127): len = 52489.6, overlap = 51
PHY-3002 : Step(128): len = 52769.2, overlap = 50.25
PHY-3002 : Step(129): len = 53161, overlap = 46.5
PHY-3002 : Step(130): len = 53562.8, overlap = 48
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000107109
PHY-3002 : Step(131): len = 54071, overlap = 45
PHY-3002 : Step(132): len = 54406.9, overlap = 44.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078680s wall, 0.062500s user + 0.093750s system = 0.156250s CPU (198.6%)

PHY-3001 : Trial Legalized: Len = 67659.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050877s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000483452
PHY-3002 : Step(133): len = 64300, overlap = 7.75
PHY-3002 : Step(134): len = 62434.1, overlap = 10.25
PHY-3002 : Step(135): len = 60785.2, overlap = 14.5
PHY-3002 : Step(136): len = 59735.4, overlap = 17.5
PHY-3002 : Step(137): len = 59345, overlap = 17.75
PHY-3002 : Step(138): len = 59263.8, overlap = 19
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000966904
PHY-3002 : Step(139): len = 59581, overlap = 20
PHY-3002 : Step(140): len = 59710.3, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00193381
PHY-3002 : Step(141): len = 59781.4, overlap = 19.75
PHY-3002 : Step(142): len = 59868, overlap = 19.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005116s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64079.5, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005320s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 64263.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6180, tnet num: 1905, tinst num: 771, tnode num: 8309, tedge num: 10847.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 20/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70312, over cnt = 146(0%), over = 233, worst = 7
PHY-1002 : len = 71216, over cnt = 94(0%), over = 121, worst = 3
PHY-1002 : len = 72552, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72568, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 72664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148686s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.6%)

PHY-1001 : Congestion index: top1 = 31.66, top5 = 23.20, top10 = 18.10, top15 = 14.06.
PHY-1001 : End incremental global routing;  0.197862s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (94.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057802s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.286268s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1697/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006765s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.66, top5 = 23.20, top10 = 18.10, top15 = 14.06.
OPT-1001 : End congestion update;  0.054276s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046594s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 733 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 771 instances, 724 slices, 23 macros(204 instances: 129 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64310.8, Over = 0
PHY-3001 : End spreading;  0.005236s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (298.4%)

PHY-3001 : Final: Len = 64310.8, Over = 0
PHY-3001 : End incremental legalization;  0.035739s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.4%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150808s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.6%)

OPT-1001 : Current memory(MB): used = 223, reserve = 185, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048333s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1689/1907.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72680, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72680, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024358s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (64.1%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 23.22, top10 = 18.09, top15 = 14.06.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048969s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.889168s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (100.2%)

RUN-1003 : finish command "place" in  5.012277s wall, 7.875000s user + 2.609375s system = 10.484375s CPU (209.2%)

RUN-1004 : used memory is 206 MB, reserved memory is 168 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 773 instances
RUN-1001 : 362 mslices, 362 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1907 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1392 nets have 2 pins
RUN-1001 : 399 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6180, tnet num: 1905, tinst num: 771, tnode num: 8309, tedge num: 10847.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 362 mslices, 362 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1905 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69928, over cnt = 147(0%), over = 235, worst = 7
PHY-1002 : len = 70840, over cnt = 99(0%), over = 126, worst = 3
PHY-1002 : len = 72328, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 72456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.137219s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (113.9%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 23.07, top10 = 18.03, top15 = 14.02.
PHY-1001 : End global routing;  0.185429s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (109.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 203, peak = 240.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 467, peak = 500.
PHY-1001 : End build detailed router design. 3.463442s wall, 3.390625s user + 0.078125s system = 3.468750s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33640, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.593757s wall, 1.578125s user + 0.000000s system = 1.578125s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.600959s wall, 1.593750s user + 0.000000s system = 1.593750s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 62% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186448, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End initial routed; 1.504795s wall, 2.359375s user + 0.156250s system = 2.515625s CPU (167.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.234   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.420   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.352204s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (97.6%)

PHY-1001 : Current memory(MB): used = 537, reserve = 504, peak = 537.
PHY-1001 : End phase 2; 1.857102s wall, 2.703125s user + 0.156250s system = 2.859375s CPU (154.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186448, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014140s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (110.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186408, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028391s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (110.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186416, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.030079s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1689(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.234   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.420   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.352989s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.167671s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (93.2%)

PHY-1001 : Current memory(MB): used = 552, reserve = 519, peak = 552.
PHY-1001 : End phase 3; 0.718182s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (100.1%)

PHY-1003 : Routed, final wirelength = 186416
PHY-1001 : Current memory(MB): used = 552, reserve = 519, peak = 552.
PHY-1001 : End export database. 0.009636s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (162.1%)

PHY-1001 : End detail routing;  7.830402s wall, 8.578125s user + 0.250000s system = 8.828125s CPU (112.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6180, tnet num: 1905, tinst num: 771, tnode num: 8309, tedge num: 10847.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[23] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[28] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[1] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[30] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6228, tnet num: 1929, tinst num: 795, tnode num: 8357, tedge num: 10895.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.992039s wall, 3.031250s user + 0.093750s system = 3.125000s CPU (104.4%)

RUN-1003 : finish command "route" in  11.339202s wall, 12.140625s user + 0.343750s system = 12.484375s CPU (110.1%)

RUN-1004 : used memory is 528 MB, reserved memory is 499 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      841   out of  19600    4.29%
#reg                      961   out of  19600    4.90%
#le                      1438
  #lut only               477   out of   1438   33.17%
  #reg only               597   out of   1438   41.52%
  #lut&reg                364   out of   1438   25.31%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         422
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    44
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1438   |637     |204     |992     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1025   |316     |112     |807     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |25     |18      |7       |21      |0       |0       |
|    demodu                  |Demodulation                                     |474    |147     |44      |346     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |43      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |16      |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |14      |0       |17      |0       |0       |
|    integ                   |Integration                                      |139    |34      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |67     |21      |13      |63      |0       |1       |
|    rs422                   |Rs422Output                                      |301    |82      |29      |247     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |127    |120     |7       |65      |0       |0       |
|    U0                      |speed_select_Tx                                  |33     |26      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |24     |24      |0       |19      |0       |0       |
|    U2                      |Ctrl_Data                                        |70     |70      |0       |29      |0       |0       |
|  wendu                     |DS18B20                                          |205    |160     |45      |65      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1380  
    #2          2       263   
    #3          3       124   
    #4          4        12   
    #5        5-10       82   
    #6        11-50      26   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6228, tnet num: 1929, tinst num: 795, tnode num: 8357, tedge num: 10895.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 795
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1931, pip num: 14570
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1346 valid insts, and 38416 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.097882s wall, 18.078125s user + 0.046875s system = 18.125000s CPU (585.1%)

RUN-1004 : used memory is 545 MB, reserved memory is 512 MB, peak memory is 676 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_173135.log"
