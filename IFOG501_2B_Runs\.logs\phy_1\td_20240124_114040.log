============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Jan 24 11:40:40 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1524 instances
RUN-0007 : 367 luts, 901 seqs, 132 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2078 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1534 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1522 instances, 367 luts, 901 seqs, 207 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7360, tnet num: 2076, tinst num: 1522, tnode num: 10346, tedge num: 12510.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.287637s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (103.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 547811
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1522.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 466194, overlap = 15.75
PHY-3002 : Step(2): len = 436084, overlap = 15.75
PHY-3002 : Step(3): len = 413823, overlap = 20.25
PHY-3002 : Step(4): len = 384687, overlap = 11.25
PHY-3002 : Step(5): len = 372279, overlap = 13.5
PHY-3002 : Step(6): len = 367246, overlap = 13.5
PHY-3002 : Step(7): len = 343567, overlap = 18
PHY-3002 : Step(8): len = 329846, overlap = 20.25
PHY-3002 : Step(9): len = 322917, overlap = 20.25
PHY-3002 : Step(10): len = 315039, overlap = 20.25
PHY-3002 : Step(11): len = 304484, overlap = 18
PHY-3002 : Step(12): len = 298953, overlap = 18
PHY-3002 : Step(13): len = 293514, overlap = 18
PHY-3002 : Step(14): len = 283085, overlap = 15.75
PHY-3002 : Step(15): len = 276329, overlap = 18
PHY-3002 : Step(16): len = 272182, overlap = 18
PHY-3002 : Step(17): len = 265958, overlap = 18
PHY-3002 : Step(18): len = 258316, overlap = 20.25
PHY-3002 : Step(19): len = 253755, overlap = 20.25
PHY-3002 : Step(20): len = 249704, overlap = 20.25
PHY-3002 : Step(21): len = 239132, overlap = 20.25
PHY-3002 : Step(22): len = 232611, overlap = 20.25
PHY-3002 : Step(23): len = 229983, overlap = 20.25
PHY-3002 : Step(24): len = 222306, overlap = 18
PHY-3002 : Step(25): len = 209790, overlap = 20.25
PHY-3002 : Step(26): len = 205672, overlap = 20.25
PHY-3002 : Step(27): len = 202579, overlap = 20.25
PHY-3002 : Step(28): len = 166700, overlap = 20.25
PHY-3002 : Step(29): len = 157532, overlap = 20.25
PHY-3002 : Step(30): len = 156513, overlap = 20.25
PHY-3002 : Step(31): len = 152725, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000116189
PHY-3002 : Step(32): len = 153548, overlap = 15.75
PHY-3002 : Step(33): len = 152074, overlap = 15.75
PHY-3002 : Step(34): len = 150230, overlap = 15.75
PHY-3002 : Step(35): len = 146632, overlap = 13.5
PHY-3002 : Step(36): len = 141930, overlap = 13.5
PHY-3002 : Step(37): len = 137739, overlap = 13.5
PHY-3002 : Step(38): len = 136620, overlap = 13.5
PHY-3002 : Step(39): len = 132886, overlap = 15.75
PHY-3002 : Step(40): len = 129952, overlap = 15.75
PHY-3002 : Step(41): len = 127463, overlap = 15.75
PHY-3002 : Step(42): len = 125707, overlap = 15.75
PHY-3002 : Step(43): len = 123622, overlap = 11.25
PHY-3002 : Step(44): len = 121364, overlap = 15.75
PHY-3002 : Step(45): len = 118086, overlap = 15.75
PHY-3002 : Step(46): len = 116863, overlap = 15.75
PHY-3002 : Step(47): len = 114430, overlap = 15.75
PHY-3002 : Step(48): len = 111529, overlap = 15.75
PHY-3002 : Step(49): len = 108613, overlap = 15.75
PHY-3002 : Step(50): len = 107821, overlap = 15.75
PHY-3002 : Step(51): len = 104303, overlap = 13.5
PHY-3002 : Step(52): len = 102326, overlap = 15.75
PHY-3002 : Step(53): len = 100446, overlap = 15.75
PHY-3002 : Step(54): len = 99211.9, overlap = 13.5
PHY-3002 : Step(55): len = 92864.4, overlap = 13.5
PHY-3002 : Step(56): len = 90945.1, overlap = 13.5
PHY-3002 : Step(57): len = 89631.5, overlap = 13.5
PHY-3002 : Step(58): len = 88052.4, overlap = 15.75
PHY-3002 : Step(59): len = 87612.1, overlap = 15.75
PHY-3002 : Step(60): len = 86536.9, overlap = 18
PHY-3002 : Step(61): len = 84994.7, overlap = 13.5
PHY-3002 : Step(62): len = 83785.2, overlap = 13.5
PHY-3002 : Step(63): len = 82668.9, overlap = 13.5
PHY-3002 : Step(64): len = 81641.2, overlap = 15.75
PHY-3002 : Step(65): len = 80999.2, overlap = 15.75
PHY-3002 : Step(66): len = 80171, overlap = 15.75
PHY-3002 : Step(67): len = 79050.1, overlap = 13.5
PHY-3002 : Step(68): len = 77502.4, overlap = 11.25
PHY-3002 : Step(69): len = 75547.7, overlap = 11.25
PHY-3002 : Step(70): len = 74894.6, overlap = 11.6875
PHY-3002 : Step(71): len = 73139.2, overlap = 14.125
PHY-3002 : Step(72): len = 72404.7, overlap = 14.5
PHY-3002 : Step(73): len = 70115.5, overlap = 14.9375
PHY-3002 : Step(74): len = 68613.5, overlap = 12.8125
PHY-3002 : Step(75): len = 67537.5, overlap = 12.875
PHY-3002 : Step(76): len = 66965, overlap = 12.9375
PHY-3002 : Step(77): len = 66060.7, overlap = 13.5625
PHY-3002 : Step(78): len = 63088.5, overlap = 16.5625
PHY-3002 : Step(79): len = 62332.6, overlap = 16.375
PHY-3002 : Step(80): len = 61494.8, overlap = 14.25
PHY-3002 : Step(81): len = 60996.8, overlap = 16.5
PHY-3002 : Step(82): len = 60937.3, overlap = 16.5
PHY-3002 : Step(83): len = 60741.9, overlap = 14.1875
PHY-3002 : Step(84): len = 60330, overlap = 16.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000232379
PHY-3002 : Step(85): len = 60171.8, overlap = 16.375
PHY-3002 : Step(86): len = 60231.1, overlap = 16.375
PHY-3002 : Step(87): len = 60090.8, overlap = 16.3125
PHY-3002 : Step(88): len = 59749.8, overlap = 16.1875
PHY-3002 : Step(89): len = 59876.6, overlap = 13.9375
PHY-3002 : Step(90): len = 59984.1, overlap = 11.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000464757
PHY-3002 : Step(91): len = 59772.7, overlap = 13.875
PHY-3002 : Step(92): len = 59708.4, overlap = 13.9375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007037s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056150s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000596373
PHY-3002 : Step(93): len = 62143.1, overlap = 13.9375
PHY-3002 : Step(94): len = 62207.9, overlap = 13.75
PHY-3002 : Step(95): len = 61538.8, overlap = 13.7188
PHY-3002 : Step(96): len = 61427.8, overlap = 13.2812
PHY-3002 : Step(97): len = 61132, overlap = 12.6562
PHY-3002 : Step(98): len = 59551.6, overlap = 12.6562
PHY-3002 : Step(99): len = 58734.5, overlap = 11.0938
PHY-3002 : Step(100): len = 56643.9, overlap = 10.8438
PHY-3002 : Step(101): len = 55683.5, overlap = 11.375
PHY-3002 : Step(102): len = 54966.9, overlap = 10.7812
PHY-3002 : Step(103): len = 54546.4, overlap = 10.7188
PHY-3002 : Step(104): len = 53799.3, overlap = 10.8125
PHY-3002 : Step(105): len = 53471, overlap = 12.3125
PHY-3002 : Step(106): len = 53120.3, overlap = 12.5312
PHY-3002 : Step(107): len = 52325.3, overlap = 13
PHY-3002 : Step(108): len = 51558.3, overlap = 13.375
PHY-3002 : Step(109): len = 50909.6, overlap = 13.1875
PHY-3002 : Step(110): len = 50479.9, overlap = 13.9375
PHY-3002 : Step(111): len = 50062.6, overlap = 14.4062
PHY-3002 : Step(112): len = 49567.3, overlap = 15.2812
PHY-3002 : Step(113): len = 49179, overlap = 15.2812
PHY-3002 : Step(114): len = 49107.4, overlap = 15.2188
PHY-3002 : Step(115): len = 48732.9, overlap = 15.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00119275
PHY-3002 : Step(116): len = 48708.3, overlap = 15.2812
PHY-3002 : Step(117): len = 48701.6, overlap = 15.3438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00238549
PHY-3002 : Step(118): len = 48514.8, overlap = 15.2188
PHY-3002 : Step(119): len = 48421.8, overlap = 15.3438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057580s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.17293e-05
PHY-3002 : Step(120): len = 48912.7, overlap = 45.2188
PHY-3002 : Step(121): len = 50119.6, overlap = 44.8125
PHY-3002 : Step(122): len = 49816.8, overlap = 44.0625
PHY-3002 : Step(123): len = 49496.7, overlap = 44
PHY-3002 : Step(124): len = 49496.7, overlap = 44
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000123459
PHY-3002 : Step(125): len = 49570.9, overlap = 43.875
PHY-3002 : Step(126): len = 50776, overlap = 41.9062
PHY-3002 : Step(127): len = 50776, overlap = 41.9062
PHY-3002 : Step(128): len = 50425.2, overlap = 41.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000246917
PHY-3002 : Step(129): len = 51460.9, overlap = 37.5938
PHY-3002 : Step(130): len = 53097.4, overlap = 41.0312
PHY-3002 : Step(131): len = 53525.8, overlap = 40.625
PHY-3002 : Step(132): len = 53273, overlap = 40.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000493834
PHY-3002 : Step(133): len = 53199.3, overlap = 39.875
PHY-3002 : Step(134): len = 53591.9, overlap = 38.6562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000987669
PHY-3002 : Step(135): len = 53824.1, overlap = 35.5625
PHY-3002 : Step(136): len = 54224.2, overlap = 34.9062
PHY-3002 : Step(137): len = 54481.9, overlap = 33.3125
PHY-3002 : Step(138): len = 54758.4, overlap = 31.0312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7360, tnet num: 2076, tinst num: 1522, tnode num: 10346, tedge num: 12510.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.84 peak overflow 2.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2078.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58240, over cnt = 264(0%), over = 1007, worst = 21
PHY-1001 : End global iterations;  0.083599s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (112.1%)

PHY-1001 : Congestion index: top1 = 42.61, top5 = 25.71, top10 = 16.61, top15 = 11.73.
PHY-1001 : End incremental global routing;  0.133057s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (93.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064157s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.226430s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (96.6%)

OPT-1001 : Current memory(MB): used = 211, reserve = 174, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1603/2078.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58240, over cnt = 264(0%), over = 1007, worst = 21
PHY-1002 : len = 63048, over cnt = 174(0%), over = 460, worst = 18
PHY-1002 : len = 69064, over cnt = 35(0%), over = 45, worst = 5
PHY-1002 : len = 69480, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 69704, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106134s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (117.8%)

PHY-1001 : Congestion index: top1 = 36.83, top5 = 25.34, top10 = 18.34, top15 = 13.51.
OPT-1001 : End congestion update;  0.148696s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (115.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2076 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055194s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.206964s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (105.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 177, peak = 214.
OPT-1001 : End physical optimization;  0.703552s wall, 0.734375s user + 0.062500s system = 0.796875s CPU (113.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 165 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 118 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 618 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 985/1275 primitive instances ...
PHY-3001 : End packing;  0.045175s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 778 instances
RUN-1001 : 364 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1921 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1373 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 776 instances, 729 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54800.8, Over = 56.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6252, tnet num: 1919, tinst num: 776, tnode num: 8428, tedge num: 11035.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.288402s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.62471e-05
PHY-3002 : Step(139): len = 53747.6, overlap = 53.5
PHY-3002 : Step(140): len = 53034.2, overlap = 53.75
PHY-3002 : Step(141): len = 52583.4, overlap = 56.25
PHY-3002 : Step(142): len = 52708, overlap = 57.5
PHY-3002 : Step(143): len = 52950, overlap = 55
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.24943e-05
PHY-3002 : Step(144): len = 53458.8, overlap = 54
PHY-3002 : Step(145): len = 54116.7, overlap = 50.75
PHY-3002 : Step(146): len = 54683.7, overlap = 49.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000144989
PHY-3002 : Step(147): len = 55775.2, overlap = 48.5
PHY-3002 : Step(148): len = 56763.8, overlap = 45.75
PHY-3002 : Step(149): len = 57030, overlap = 44.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075945s wall, 0.062500s user + 0.078125s system = 0.140625s CPU (185.2%)

PHY-3001 : Trial Legalized: Len = 69025.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.048991s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00130592
PHY-3002 : Step(150): len = 66023.5, overlap = 6.25
PHY-3002 : Step(151): len = 64215.2, overlap = 9.5
PHY-3002 : Step(152): len = 62650, overlap = 10
PHY-3002 : Step(153): len = 61565.7, overlap = 11.5
PHY-3002 : Step(154): len = 61213.5, overlap = 12.5
PHY-3002 : Step(155): len = 60853.2, overlap = 15.25
PHY-3002 : Step(156): len = 60507.5, overlap = 17.75
PHY-3002 : Step(157): len = 60110.7, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00229793
PHY-3002 : Step(158): len = 60309.4, overlap = 18
PHY-3002 : Step(159): len = 60352.8, overlap = 17.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00459585
PHY-3002 : Step(160): len = 60452.5, overlap = 17.5
PHY-3002 : Step(161): len = 60520.2, overlap = 17.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005330s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (293.2%)

PHY-3001 : Legalized: Len = 63453.2, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005562s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 0, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 63613.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6252, tnet num: 1919, tinst num: 776, tnode num: 8428, tedge num: 11035.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 78/1921.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70200, over cnt = 152(0%), over = 229, worst = 7
PHY-1002 : len = 71024, over cnt = 98(0%), over = 123, worst = 3
PHY-1002 : len = 72456, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 72552, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.153058s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (91.9%)

PHY-1001 : Congestion index: top1 = 31.42, top5 = 23.21, top10 = 18.09, top15 = 14.05.
PHY-1001 : End incremental global routing;  0.206582s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (90.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064505s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (121.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.301555s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.4%)

OPT-1001 : Current memory(MB): used = 217, reserve = 179, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1678/1921.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006343s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (246.3%)

PHY-1001 : Congestion index: top1 = 31.42, top5 = 23.21, top10 = 18.09, top15 = 14.05.
OPT-1001 : End congestion update;  0.053651s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056445s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.112094s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (97.6%)

OPT-1001 : Current memory(MB): used = 219, reserve = 182, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054464s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1678/1921.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006234s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.42, top5 = 23.21, top10 = 18.09, top15 = 14.05.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056440s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.856143s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (98.6%)

RUN-1003 : finish command "place" in  5.342437s wall, 7.703125s user + 2.890625s system = 10.593750s CPU (198.3%)

RUN-1004 : used memory is 197 MB, reserved memory is 159 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 778 instances
RUN-1001 : 364 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1921 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1373 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6252, tnet num: 1919, tinst num: 776, tnode num: 8428, tedge num: 11035.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 364 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1919 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69240, over cnt = 149(0%), over = 232, worst = 7
PHY-1002 : len = 70120, over cnt = 93(0%), over = 122, worst = 3
PHY-1002 : len = 71672, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 71768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133785s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (105.1%)

PHY-1001 : Congestion index: top1 = 31.31, top5 = 22.92, top10 = 17.80, top15 = 13.87.
PHY-1001 : End global routing;  0.183440s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 201, peak = 238.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 464, peak = 497.
PHY-1001 : End build detailed router design. 3.100132s wall, 3.046875s user + 0.031250s system = 3.078125s CPU (99.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32680, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.302647s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 529.
PHY-1001 : End phase 1; 1.308471s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 28% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184392, over cnt = 36(0%), over = 36, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 499, peak = 532.
PHY-1001 : End initial routed; 1.272025s wall, 2.078125s user + 0.140625s system = 2.218750s CPU (174.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1699(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.506   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.081   |  -0.242   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.365234s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 533, reserve = 500, peak = 533.
PHY-1001 : End phase 2; 1.637356s wall, 2.437500s user + 0.140625s system = 2.578125s CPU (157.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184392, over cnt = 36(0%), over = 36, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015585s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (100.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184256, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031925s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184304, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020524s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (76.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1699(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.506   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.081   |  -0.242   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350790s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.163282s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.7%)

PHY-1001 : Current memory(MB): used = 547, reserve = 515, peak = 547.
PHY-1001 : End phase 3; 0.697192s wall, 0.687500s user + 0.000000s system = 0.687500s CPU (98.6%)

PHY-1003 : Routed, final wirelength = 184304
PHY-1001 : Current memory(MB): used = 548, reserve = 515, peak = 548.
PHY-1001 : End export database. 0.011028s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (141.7%)

PHY-1001 : End detail routing;  6.926687s wall, 7.671875s user + 0.187500s system = 7.859375s CPU (113.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6252, tnet num: 1919, tinst num: 776, tnode num: 8428, tedge num: 11035.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_15.sr slack -81ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_22.sr slack -73ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6272, tnet num: 1929, tinst num: 786, tnode num: 8448, tedge num: 11055.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.929012s wall, 2.890625s user + 0.187500s system = 3.078125s CPU (105.1%)

RUN-1003 : finish command "route" in  10.370083s wall, 11.062500s user + 0.390625s system = 11.453125s CPU (110.4%)

RUN-1004 : used memory is 522 MB, reserved memory is 490 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      807   out of  19600    4.12%
#reg                      989   out of  19600    5.05%
#le                      1425
  #lut only               436   out of   1425   30.60%
  #reg only               618   out of   1425   43.37%
  #lut&reg                371   out of   1425   26.04%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         436
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1425   |600     |207     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1020   |288     |114     |836     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |20      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |436    |110     |39      |350     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |61     |45      |6       |49      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |9       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |15      |0       |17      |0       |0       |
|    integ                   |Integration                                      |130    |18      |14      |104     |0       |0       |
|    modu                    |Modulation                                       |98     |30      |21      |94      |0       |1       |
|    rs422                   |Rs422Output                                      |304    |89      |29      |249     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |21      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |111    |104     |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |32     |25      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |23     |23      |0       |20      |0       |0       |
|    U2                      |Ctrl_Data                                        |56     |56      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1347  
    #2          2       302   
    #3          3       119   
    #4          4        16   
    #5        5-10       75   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.97            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6272, tnet num: 1929, tinst num: 786, tnode num: 8448, tedge num: 11055.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1929 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 786
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1931, pip num: 14368
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1314 valid insts, and 37998 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.900107s wall, 16.984375s user + 0.062500s system = 17.046875s CPU (587.8%)

RUN-1004 : used memory is 518 MB, reserved memory is 485 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240124_114040.log"
