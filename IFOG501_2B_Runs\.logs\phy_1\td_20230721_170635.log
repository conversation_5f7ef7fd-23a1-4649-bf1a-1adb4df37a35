============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 17:06:35 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1658 instances
RUN-0007 : 382 luts, 1016 seqs, 138 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2198 nets
RUN-1001 : 1640 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1656 instances, 382 luts, 1016 seqs, 211 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7830, tnet num: 2196, tinst num: 1656, tnode num: 11090, tedge num: 13215.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.284914s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (104.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 594001
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1656.
PHY-3001 : End clustering;  0.000025s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 487420, overlap = 13.5
PHY-3002 : Step(2): len = 362428, overlap = 13.5
PHY-3002 : Step(3): len = 330721, overlap = 18
PHY-3002 : Step(4): len = 319111, overlap = 18
PHY-3002 : Step(5): len = 308604, overlap = 15.75
PHY-3002 : Step(6): len = 302347, overlap = 15.75
PHY-3002 : Step(7): len = 290094, overlap = 13.5
PHY-3002 : Step(8): len = 281241, overlap = 20.25
PHY-3002 : Step(9): len = 273518, overlap = 20.25
PHY-3002 : Step(10): len = 267178, overlap = 20.25
PHY-3002 : Step(11): len = 258545, overlap = 20.25
PHY-3002 : Step(12): len = 254093, overlap = 20.25
PHY-3002 : Step(13): len = 247489, overlap = 20.25
PHY-3002 : Step(14): len = 240772, overlap = 20.25
PHY-3002 : Step(15): len = 234653, overlap = 20.25
PHY-3002 : Step(16): len = 231656, overlap = 20.25
PHY-3002 : Step(17): len = 223545, overlap = 20.25
PHY-3002 : Step(18): len = 218678, overlap = 20.25
PHY-3002 : Step(19): len = 214899, overlap = 20.25
PHY-3002 : Step(20): len = 209280, overlap = 20.25
PHY-3002 : Step(21): len = 201796, overlap = 20.25
PHY-3002 : Step(22): len = 200019, overlap = 20.25
PHY-3002 : Step(23): len = 192435, overlap = 18
PHY-3002 : Step(24): len = 168907, overlap = 20.25
PHY-3002 : Step(25): len = 163528, overlap = 20.25
PHY-3002 : Step(26): len = 161878, overlap = 20.25
PHY-3002 : Step(27): len = 140038, overlap = 20.25
PHY-3002 : Step(28): len = 136322, overlap = 20.25
PHY-3002 : Step(29): len = 134246, overlap = 20.25
PHY-3002 : Step(30): len = 131735, overlap = 20.25
PHY-3002 : Step(31): len = 130021, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103677
PHY-3002 : Step(32): len = 132186, overlap = 6.75
PHY-3002 : Step(33): len = 131436, overlap = 9
PHY-3002 : Step(34): len = 129617, overlap = 15.75
PHY-3002 : Step(35): len = 127006, overlap = 13.5
PHY-3002 : Step(36): len = 123139, overlap = 9
PHY-3002 : Step(37): len = 121058, overlap = 11.25
PHY-3002 : Step(38): len = 118304, overlap = 11.25
PHY-3002 : Step(39): len = 117788, overlap = 11.25
PHY-3002 : Step(40): len = 109202, overlap = 9
PHY-3002 : Step(41): len = 107148, overlap = 9
PHY-3002 : Step(42): len = 104821, overlap = 11.25
PHY-3002 : Step(43): len = 103662, overlap = 11.25
PHY-3002 : Step(44): len = 102169, overlap = 13.5
PHY-3002 : Step(45): len = 100846, overlap = 11.25
PHY-3002 : Step(46): len = 97640.3, overlap = 6.75
PHY-3002 : Step(47): len = 95653.9, overlap = 9
PHY-3002 : Step(48): len = 93565.5, overlap = 11.25
PHY-3002 : Step(49): len = 92773.9, overlap = 11.25
PHY-3002 : Step(50): len = 89318.2, overlap = 9
PHY-3002 : Step(51): len = 87191.5, overlap = 11.25
PHY-3002 : Step(52): len = 85375.2, overlap = 11.375
PHY-3002 : Step(53): len = 84746.9, overlap = 11.4375
PHY-3002 : Step(54): len = 83740.5, overlap = 9.3125
PHY-3002 : Step(55): len = 83054, overlap = 9.3125
PHY-3002 : Step(56): len = 82365.8, overlap = 9.3125
PHY-3002 : Step(57): len = 81527.4, overlap = 11.6875
PHY-3002 : Step(58): len = 80602.3, overlap = 11.875
PHY-3002 : Step(59): len = 80108.2, overlap = 11.6875
PHY-3002 : Step(60): len = 78982.3, overlap = 7.25
PHY-3002 : Step(61): len = 77063.2, overlap = 6.9375
PHY-3002 : Step(62): len = 75943.1, overlap = 11.4375
PHY-3002 : Step(63): len = 74588.8, overlap = 11.4375
PHY-3002 : Step(64): len = 74282.1, overlap = 11.4375
PHY-3002 : Step(65): len = 74208, overlap = 6.9375
PHY-3002 : Step(66): len = 73116.8, overlap = 9.1875
PHY-3002 : Step(67): len = 71258.6, overlap = 13.5
PHY-3002 : Step(68): len = 70460.7, overlap = 11.25
PHY-3002 : Step(69): len = 69092.2, overlap = 6.75
PHY-3002 : Step(70): len = 68709.6, overlap = 11.25
PHY-3002 : Step(71): len = 67093.9, overlap = 14
PHY-3002 : Step(72): len = 66176, overlap = 12.25
PHY-3002 : Step(73): len = 65118.9, overlap = 8.75
PHY-3002 : Step(74): len = 64316, overlap = 11
PHY-3002 : Step(75): len = 63149.4, overlap = 13.25
PHY-3002 : Step(76): len = 62726.4, overlap = 13.25
PHY-3002 : Step(77): len = 62951.5, overlap = 13
PHY-3002 : Step(78): len = 62830.5, overlap = 8.5
PHY-3002 : Step(79): len = 62078.3, overlap = 10.75
PHY-3002 : Step(80): len = 61344.9, overlap = 10.75
PHY-3002 : Step(81): len = 60552.4, overlap = 13
PHY-3002 : Step(82): len = 59669.3, overlap = 14.25
PHY-3002 : Step(83): len = 59772.6, overlap = 9.25
PHY-3002 : Step(84): len = 59469.7, overlap = 4.75
PHY-3002 : Step(85): len = 58260.4, overlap = 9
PHY-3002 : Step(86): len = 57707.8, overlap = 6.75
PHY-3002 : Step(87): len = 57342.3, overlap = 9
PHY-3002 : Step(88): len = 56071.5, overlap = 6.75
PHY-3002 : Step(89): len = 55107.8, overlap = 9
PHY-3002 : Step(90): len = 54553.8, overlap = 9
PHY-3002 : Step(91): len = 54159.6, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000207353
PHY-3002 : Step(92): len = 54485.2, overlap = 9
PHY-3002 : Step(93): len = 54545.7, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000414707
PHY-3002 : Step(94): len = 54610.6, overlap = 2.25
PHY-3002 : Step(95): len = 54604.5, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007580s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (206.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062802s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00626709
PHY-3002 : Step(96): len = 55911.2, overlap = 8.28125
PHY-3002 : Step(97): len = 55238.4, overlap = 7.65625
PHY-3002 : Step(98): len = 54794.3, overlap = 7.53125
PHY-3002 : Step(99): len = 54220.4, overlap = 6.3125
PHY-3002 : Step(100): len = 54293.5, overlap = 6.625
PHY-3002 : Step(101): len = 53288.5, overlap = 6.0625
PHY-3002 : Step(102): len = 52595.2, overlap = 5.125
PHY-3002 : Step(103): len = 51528.4, overlap = 4.8125
PHY-3002 : Step(104): len = 50862.5, overlap = 4.4375
PHY-3002 : Step(105): len = 50246, overlap = 5.0625
PHY-3002 : Step(106): len = 49264.3, overlap = 7.75
PHY-3002 : Step(107): len = 48839, overlap = 8.4375
PHY-3002 : Step(108): len = 48517, overlap = 8.875
PHY-3002 : Step(109): len = 48138.9, overlap = 7.3125
PHY-3002 : Step(110): len = 47565.1, overlap = 7.375
PHY-3002 : Step(111): len = 47089.3, overlap = 7.9375
PHY-3002 : Step(112): len = 46602.1, overlap = 8.0625
PHY-3002 : Step(113): len = 46361.2, overlap = 8.375
PHY-3002 : Step(114): len = 46121.7, overlap = 8.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061373s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.30916e-05
PHY-3002 : Step(115): len = 46122.9, overlap = 60.9375
PHY-3002 : Step(116): len = 46365.3, overlap = 61
PHY-3002 : Step(117): len = 46315.5, overlap = 59.8125
PHY-3002 : Step(118): len = 46431.1, overlap = 59.2188
PHY-3002 : Step(119): len = 46406.5, overlap = 59.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000146183
PHY-3002 : Step(120): len = 46589.7, overlap = 52.75
PHY-3002 : Step(121): len = 46815.9, overlap = 52
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000283228
PHY-3002 : Step(122): len = 47184.2, overlap = 51.8125
PHY-3002 : Step(123): len = 47888.7, overlap = 46.1875
PHY-3002 : Step(124): len = 49220.1, overlap = 41.5938
PHY-3002 : Step(125): len = 50109, overlap = 37.2188
PHY-3002 : Step(126): len = 50277.8, overlap = 34.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7830, tnet num: 2196, tinst num: 1656, tnode num: 11090, tedge num: 13215.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 103.28 peak overflow 2.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53024, over cnt = 246(0%), over = 1082, worst = 24
PHY-1001 : End global iterations;  0.077854s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.3%)

PHY-1001 : Congestion index: top1 = 44.05, top5 = 25.42, top10 = 16.27, top15 = 11.47.
PHY-1001 : End incremental global routing;  0.127503s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067098s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.1%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1617 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1658 instances, 382 luts, 1018 seqs, 211 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 50464.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7838, tnet num: 2198, tinst num: 1658, tnode num: 11104, tedge num: 13227.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.303943s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (97.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(127): len = 50530.4, overlap = 0.6875
PHY-3002 : Step(128): len = 50528.3, overlap = 0.6875
PHY-3002 : Step(129): len = 50517.1, overlap = 0.75
PHY-3002 : Step(130): len = 50518.7, overlap = 0.875
PHY-3002 : Step(131): len = 50521.4, overlap = 0.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059705s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000759383
PHY-3002 : Step(132): len = 50521.4, overlap = 34.25
PHY-3002 : Step(133): len = 50521.4, overlap = 34.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00151877
PHY-3002 : Step(134): len = 50524.4, overlap = 34.25
PHY-3002 : Step(135): len = 50524.4, overlap = 34.25
PHY-3001 : Final: Len = 50524.4, Over = 34.25
PHY-3001 : End incremental placement;  0.474621s wall, 0.437500s user + 0.093750s system = 0.531250s CPU (111.9%)

OPT-1001 : Total overflow 103.41 peak overflow 2.59
OPT-1001 : End high-fanout net optimization;  0.704778s wall, 0.781250s user + 0.093750s system = 0.875000s CPU (124.2%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1682/2200.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53184, over cnt = 244(0%), over = 1071, worst = 24
PHY-1002 : len = 59192, over cnt = 186(0%), over = 489, worst = 24
PHY-1002 : len = 64904, over cnt = 46(0%), over = 50, worst = 4
PHY-1002 : len = 65416, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 66048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.100654s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (124.2%)

PHY-1001 : Congestion index: top1 = 39.42, top5 = 26.15, top10 = 18.56, top15 = 13.58.
OPT-1001 : End congestion update;  0.146315s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (106.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060069s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.209298s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (104.5%)

OPT-1001 : Current memory(MB): used = 216, reserve = 181, peak = 220.
OPT-1001 : End physical optimization;  1.188554s wall, 1.343750s user + 0.125000s system = 1.468750s CPU (123.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 382 LUT to BLE ...
SYN-4008 : Packed 382 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 829 remaining SEQ's ...
SYN-4005 : Packed 110 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 719 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1101/1419 primitive instances ...
PHY-3001 : End packing;  0.050368s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (124.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 839 instances
RUN-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-1001 : 1476 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 837 instances, 790 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50310.6, Over = 61.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 837, tnode num: 8915, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.311516s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.78947e-05
PHY-3002 : Step(136): len = 49898.5, overlap = 62
PHY-3002 : Step(137): len = 49662.2, overlap = 58.75
PHY-3002 : Step(138): len = 49122.3, overlap = 62
PHY-3002 : Step(139): len = 48698.5, overlap = 64.25
PHY-3002 : Step(140): len = 48527.1, overlap = 64.75
PHY-3002 : Step(141): len = 48376.2, overlap = 66.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.57894e-05
PHY-3002 : Step(142): len = 48530.9, overlap = 65.25
PHY-3002 : Step(143): len = 49051.3, overlap = 62
PHY-3002 : Step(144): len = 49911.2, overlap = 56.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000111579
PHY-3002 : Step(145): len = 50383.2, overlap = 56
PHY-3002 : Step(146): len = 50794.1, overlap = 54.25
PHY-3002 : Step(147): len = 51446.9, overlap = 52.5
PHY-3002 : Step(148): len = 52250.8, overlap = 50.5
PHY-3002 : Step(149): len = 52932.4, overlap = 51.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.108308s wall, 0.062500s user + 0.093750s system = 0.156250s CPU (144.3%)

PHY-3001 : Trial Legalized: Len = 66559.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052028s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000492373
PHY-3002 : Step(150): len = 63866, overlap = 6.25
PHY-3002 : Step(151): len = 61158, overlap = 11.75
PHY-3002 : Step(152): len = 59146.8, overlap = 15.25
PHY-3002 : Step(153): len = 57855.5, overlap = 15.75
PHY-3002 : Step(154): len = 56860.8, overlap = 21
PHY-3002 : Step(155): len = 56424.8, overlap = 22.25
PHY-3002 : Step(156): len = 56198.8, overlap = 22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000984745
PHY-3002 : Step(157): len = 56550.7, overlap = 22.75
PHY-3002 : Step(158): len = 56773.9, overlap = 22.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00196949
PHY-3002 : Step(159): len = 56918.2, overlap = 23
PHY-3002 : Step(160): len = 57027, overlap = 23.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005372s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 61818.8, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005970s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 5, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 61776.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 837, tnode num: 8915, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 48/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67960, over cnt = 161(0%), over = 238, worst = 6
PHY-1002 : len = 69152, over cnt = 64(0%), over = 72, worst = 2
PHY-1002 : len = 69960, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 70152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118074s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (145.6%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.65, top10 = 17.64, top15 = 14.09.
PHY-1001 : End incremental global routing;  0.169342s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (129.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060212s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (103.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.260823s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (119.8%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1790/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006249s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.11, top5 = 22.65, top10 = 17.64, top15 = 14.09.
OPT-1001 : End congestion update;  0.054186s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049842s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 799 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 837 instances, 790 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 61809.6, Over = 0
PHY-3001 : End spreading;  0.005064s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (308.5%)

PHY-3001 : Final: Len = 61809.6, Over = 0
PHY-3001 : End incremental legalization;  0.034658s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.2%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150959s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.2%)

OPT-1001 : Current memory(MB): used = 225, reserve = 189, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048019s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1786/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70184, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 70184, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 70216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.025302s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (123.5%)

PHY-1001 : Congestion index: top1 = 32.20, top5 = 22.69, top10 = 17.67, top15 = 14.11.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051343s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.890012s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (103.6%)

RUN-1003 : finish command "place" in  5.665485s wall, 9.437500s user + 2.812500s system = 12.250000s CPU (216.2%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 839 instances
RUN-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-1001 : 1476 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 837, tnode num: 8915, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67448, over cnt = 168(0%), over = 249, worst = 6
PHY-1002 : len = 68776, over cnt = 67(0%), over = 74, worst = 2
PHY-1002 : len = 69688, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 69816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123220s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (126.8%)

PHY-1001 : Congestion index: top1 = 32.41, top5 = 22.56, top10 = 17.56, top15 = 14.04.
PHY-1001 : End global routing;  0.172487s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (117.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 206, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 501, reserve = 469, peak = 501.
PHY-1001 : End build detailed router design. 3.285424s wall, 3.250000s user + 0.031250s system = 3.281250s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33920, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.281151s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 535.
PHY-1001 : End phase 1; 1.286827s wall, 1.281250s user + 0.015625s system = 1.296875s CPU (100.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179304, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End initial routed; 0.982636s wall, 2.078125s user + 0.046875s system = 2.125000s CPU (216.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1804(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.594   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.368928s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.6%)

PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End phase 2; 1.351660s wall, 2.453125s user + 0.046875s system = 2.500000s CPU (185.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179304, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014395s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179272, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032460s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (96.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179392, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025875s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (60.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1804(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.594   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.360859s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.176982s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.1%)

PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End phase 3; 0.733954s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (100.1%)

PHY-1003 : Routed, final wirelength = 179392
PHY-1001 : Current memory(MB): used = 552, reserve = 521, peak = 552.
PHY-1001 : End export database. 0.009508s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (164.3%)

PHY-1001 : End detail routing;  6.849683s wall, 7.906250s user + 0.109375s system = 8.015625s CPU (117.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 837, tnode num: 8915, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.741818s wall, 8.828125s user + 0.109375s system = 8.937500s CPU (115.4%)

RUN-1004 : used memory is 506 MB, reserved memory is 476 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      807   out of  19600    4.12%
#reg                     1076   out of  19600    5.49%
#le                      1526
  #lut only               450   out of   1526   29.49%
  #reg only               719   out of   1526   47.12%
  #lut&reg                357   out of   1526   23.39%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1526   |596     |211     |1107    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1149   |309     |127     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |18      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |541    |135     |58      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |0       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |15      |0       |29      |0       |0       |
|    integ                   |Integration                                      |137    |15      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |100    |19      |15      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |98      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |102    |89      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |19     |13      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |48     |48      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |77      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1440  
    #2          2       307   
    #3          3       113   
    #4          4        12   
    #5        5-10       81   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2024, tinst num: 837, tnode num: 8915, tedge num: 11532.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 837
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2026, pip num: 14713
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1272 valid insts, and 38869 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.085985s wall, 17.140625s user + 0.031250s system = 17.171875s CPU (556.4%)

RUN-1004 : used memory is 520 MB, reserved memory is 487 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_170635.log"
