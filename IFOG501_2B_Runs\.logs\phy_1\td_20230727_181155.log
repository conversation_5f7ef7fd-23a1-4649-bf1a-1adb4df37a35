============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jul 27 18:11:55 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 20 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1609 instances
RUN-0007 : 368 luts, 992 seqs, 128 mslices, 72 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2135 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1603 nets have 2 pins
RUN-1001 : 416 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     254     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 22
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1607 instances, 368 luts, 992 seqs, 200 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7613, tnet num: 2133, tinst num: 1607, tnode num: 10809, tedge num: 12815.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.295646s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 567644
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1607.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 476314, overlap = 20.25
PHY-3002 : Step(2): len = 443472, overlap = 20.25
PHY-3002 : Step(3): len = 425808, overlap = 20.25
PHY-3002 : Step(4): len = 409877, overlap = 18
PHY-3002 : Step(5): len = 400621, overlap = 18
PHY-3002 : Step(6): len = 389082, overlap = 18
PHY-3002 : Step(7): len = 379529, overlap = 20.25
PHY-3002 : Step(8): len = 367224, overlap = 20.25
PHY-3002 : Step(9): len = 357635, overlap = 20.25
PHY-3002 : Step(10): len = 347242, overlap = 18
PHY-3002 : Step(11): len = 337650, overlap = 18
PHY-3002 : Step(12): len = 328022, overlap = 18
PHY-3002 : Step(13): len = 318953, overlap = 20.25
PHY-3002 : Step(14): len = 308294, overlap = 20.25
PHY-3002 : Step(15): len = 300753, overlap = 20.25
PHY-3002 : Step(16): len = 290643, overlap = 20.25
PHY-3002 : Step(17): len = 282472, overlap = 20.25
PHY-3002 : Step(18): len = 273276, overlap = 20.25
PHY-3002 : Step(19): len = 266168, overlap = 20.25
PHY-3002 : Step(20): len = 257247, overlap = 20.25
PHY-3002 : Step(21): len = 250892, overlap = 20.25
PHY-3002 : Step(22): len = 243943, overlap = 20.25
PHY-3002 : Step(23): len = 237539, overlap = 20.25
PHY-3002 : Step(24): len = 229930, overlap = 20.25
PHY-3002 : Step(25): len = 224753, overlap = 20.25
PHY-3002 : Step(26): len = 220079, overlap = 20.25
PHY-3002 : Step(27): len = 213371, overlap = 20.25
PHY-3002 : Step(28): len = 208164, overlap = 20.25
PHY-3002 : Step(29): len = 204885, overlap = 20.25
PHY-3002 : Step(30): len = 198961, overlap = 20.25
PHY-3002 : Step(31): len = 191199, overlap = 20.25
PHY-3002 : Step(32): len = 187316, overlap = 20.25
PHY-3002 : Step(33): len = 184837, overlap = 20.25
PHY-3002 : Step(34): len = 176647, overlap = 20.25
PHY-3002 : Step(35): len = 167326, overlap = 20.25
PHY-3002 : Step(36): len = 165103, overlap = 20.25
PHY-3002 : Step(37): len = 161730, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000130271
PHY-3002 : Step(38): len = 162124, overlap = 15.75
PHY-3002 : Step(39): len = 161045, overlap = 15.75
PHY-3002 : Step(40): len = 158411, overlap = 18
PHY-3002 : Step(41): len = 156376, overlap = 13.5
PHY-3002 : Step(42): len = 146298, overlap = 9
PHY-3002 : Step(43): len = 139962, overlap = 13.5
PHY-3002 : Step(44): len = 139166, overlap = 11.25
PHY-3002 : Step(45): len = 136585, overlap = 11.25
PHY-3002 : Step(46): len = 135156, overlap = 9
PHY-3002 : Step(47): len = 128606, overlap = 13.5
PHY-3002 : Step(48): len = 126248, overlap = 9
PHY-3002 : Step(49): len = 123684, overlap = 11.25
PHY-3002 : Step(50): len = 122850, overlap = 11.25
PHY-3002 : Step(51): len = 118968, overlap = 13.5
PHY-3002 : Step(52): len = 117117, overlap = 13.5
PHY-3002 : Step(53): len = 114670, overlap = 13.5
PHY-3002 : Step(54): len = 113728, overlap = 11.25
PHY-3002 : Step(55): len = 110534, overlap = 11.25
PHY-3002 : Step(56): len = 108579, overlap = 11.25
PHY-3002 : Step(57): len = 106200, overlap = 9
PHY-3002 : Step(58): len = 104814, overlap = 11.25
PHY-3002 : Step(59): len = 101873, overlap = 11.25
PHY-3002 : Step(60): len = 100974, overlap = 11.25
PHY-3002 : Step(61): len = 98954, overlap = 11.25
PHY-3002 : Step(62): len = 95471.7, overlap = 11.25
PHY-3002 : Step(63): len = 92704.3, overlap = 11.25
PHY-3002 : Step(64): len = 92661.3, overlap = 11.25
PHY-3002 : Step(65): len = 91069.8, overlap = 11.25
PHY-3002 : Step(66): len = 89825, overlap = 9
PHY-3002 : Step(67): len = 88003, overlap = 9
PHY-3002 : Step(68): len = 87065.3, overlap = 9
PHY-3002 : Step(69): len = 85182.5, overlap = 9
PHY-3002 : Step(70): len = 84418.7, overlap = 11.25
PHY-3002 : Step(71): len = 83041.9, overlap = 9
PHY-3002 : Step(72): len = 81349.6, overlap = 9
PHY-3002 : Step(73): len = 80545.2, overlap = 13.5
PHY-3002 : Step(74): len = 79671.9, overlap = 13.5
PHY-3002 : Step(75): len = 79090.9, overlap = 9
PHY-3002 : Step(76): len = 77262.1, overlap = 9
PHY-3002 : Step(77): len = 74614.6, overlap = 9
PHY-3002 : Step(78): len = 72763.9, overlap = 9
PHY-3002 : Step(79): len = 72680.6, overlap = 11.25
PHY-3002 : Step(80): len = 72026.2, overlap = 11.25
PHY-3002 : Step(81): len = 71112.8, overlap = 11.25
PHY-3002 : Step(82): len = 69922.3, overlap = 9
PHY-3002 : Step(83): len = 69424.2, overlap = 13.5
PHY-3002 : Step(84): len = 67972.8, overlap = 11.25
PHY-3002 : Step(85): len = 67052.1, overlap = 11.25
PHY-3002 : Step(86): len = 66459.4, overlap = 11.25
PHY-3002 : Step(87): len = 65738.3, overlap = 13.5
PHY-3002 : Step(88): len = 65071.6, overlap = 11.25
PHY-3002 : Step(89): len = 64562.1, overlap = 13.5
PHY-3002 : Step(90): len = 64459.8, overlap = 13.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000260543
PHY-3002 : Step(91): len = 64308.9, overlap = 13.5
PHY-3002 : Step(92): len = 64350.2, overlap = 13.5
PHY-3002 : Step(93): len = 64277.6, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000521086
PHY-3002 : Step(94): len = 64427.5, overlap = 9
PHY-3002 : Step(95): len = 64514.6, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005384s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060268s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(96): len = 66223, overlap = 3.0625
PHY-3002 : Step(97): len = 64252.9, overlap = 2.25
PHY-3002 : Step(98): len = 62619.8, overlap = 2.1875
PHY-3002 : Step(99): len = 61083.8, overlap = 2.3125
PHY-3002 : Step(100): len = 59940.2, overlap = 2.3125
PHY-3002 : Step(101): len = 59266.8, overlap = 2.625
PHY-3002 : Step(102): len = 57446.1, overlap = 2.3125
PHY-3002 : Step(103): len = 55562.8, overlap = 2.9375
PHY-3002 : Step(104): len = 53811.4, overlap = 4.5625
PHY-3002 : Step(105): len = 53099.5, overlap = 4.5625
PHY-3002 : Step(106): len = 52087, overlap = 8.75
PHY-3002 : Step(107): len = 51407, overlap = 9
PHY-3002 : Step(108): len = 50660.1, overlap = 8.5625
PHY-3002 : Step(109): len = 50193.3, overlap = 8.375
PHY-3002 : Step(110): len = 49383.2, overlap = 8
PHY-3002 : Step(111): len = 48746.2, overlap = 8.375
PHY-3002 : Step(112): len = 48395.1, overlap = 8.375
PHY-3002 : Step(113): len = 47659.9, overlap = 9.1875
PHY-3002 : Step(114): len = 47500.5, overlap = 9
PHY-3002 : Step(115): len = 47236.7, overlap = 11.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000783493
PHY-3002 : Step(116): len = 47264.9, overlap = 9.75
PHY-3002 : Step(117): len = 47191.3, overlap = 9.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00156699
PHY-3002 : Step(118): len = 47153.8, overlap = 9.875
PHY-3002 : Step(119): len = 47250.1, overlap = 9.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060567s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000107376
PHY-3002 : Step(120): len = 47583.5, overlap = 50.4062
PHY-3002 : Step(121): len = 48135.4, overlap = 42.125
PHY-3002 : Step(122): len = 48958.8, overlap = 44.3438
PHY-3002 : Step(123): len = 49602.4, overlap = 42.4688
PHY-3002 : Step(124): len = 49683.5, overlap = 43.4375
PHY-3002 : Step(125): len = 49446.5, overlap = 35.6562
PHY-3002 : Step(126): len = 48899.6, overlap = 34
PHY-3002 : Step(127): len = 48658.7, overlap = 34.7812
PHY-3002 : Step(128): len = 48498.5, overlap = 32.6875
PHY-3002 : Step(129): len = 48354.9, overlap = 30.8438
PHY-3002 : Step(130): len = 48093.1, overlap = 31.75
PHY-3002 : Step(131): len = 47686.2, overlap = 31.9375
PHY-3002 : Step(132): len = 47348.5, overlap = 31.9688
PHY-3002 : Step(133): len = 47154.9, overlap = 33.75
PHY-3002 : Step(134): len = 46874.9, overlap = 32.5625
PHY-3002 : Step(135): len = 46851.1, overlap = 31.6875
PHY-3002 : Step(136): len = 46673.2, overlap = 32.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000214752
PHY-3002 : Step(137): len = 46637.4, overlap = 32.6562
PHY-3002 : Step(138): len = 46696.4, overlap = 32.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000389796
PHY-3002 : Step(139): len = 47072.7, overlap = 28.9688
PHY-3002 : Step(140): len = 47107.3, overlap = 28.9375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7613, tnet num: 2133, tinst num: 1607, tnode num: 10809, tedge num: 12815.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 77.16 peak overflow 2.88
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2135.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49976, over cnt = 214(0%), over = 979, worst = 21
PHY-1001 : End global iterations;  0.080270s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.3%)

PHY-1001 : Congestion index: top1 = 43.58, top5 = 24.39, top10 = 15.22, top15 = 10.72.
PHY-1001 : End incremental global routing;  0.131582s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065953s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.229505s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (102.1%)

OPT-1001 : Current memory(MB): used = 213, reserve = 176, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1606/2135.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 49976, over cnt = 214(0%), over = 979, worst = 21
PHY-1002 : len = 54936, over cnt = 181(0%), over = 531, worst = 13
PHY-1002 : len = 57816, over cnt = 89(0%), over = 243, worst = 13
PHY-1002 : len = 61752, over cnt = 17(0%), over = 19, worst = 2
PHY-1002 : len = 62056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.077968s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (160.3%)

PHY-1001 : Congestion index: top1 = 37.52, top5 = 24.61, top10 = 17.17, top15 = 12.54.
OPT-1001 : End congestion update;  0.120511s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (116.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2133 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060323s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.183769s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (119.0%)

OPT-1001 : Current memory(MB): used = 215, reserve = 179, peak = 215.
OPT-1001 : End physical optimization;  0.694402s wall, 0.703125s user + 0.031250s system = 0.734375s CPU (105.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 810 remaining SEQ's ...
SYN-4005 : Packed 102 SEQ with LUT/SLICE
SYN-4006 : 106 single LUT's are left
SYN-4006 : 708 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1076/1383 primitive instances ...
PHY-3001 : End packing;  0.046766s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 814 instances
RUN-1001 : 382 mslices, 383 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1967 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1443 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 812 instances, 765 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 47247.8, Over = 52.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6382, tnet num: 1965, tinst num: 812, tnode num: 8677, tedge num: 11177.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.309426s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.31246e-05
PHY-3002 : Step(141): len = 46884.9, overlap = 55.5
PHY-3002 : Step(142): len = 46789.7, overlap = 58
PHY-3002 : Step(143): len = 46757.2, overlap = 61
PHY-3002 : Step(144): len = 46623.7, overlap = 60.5
PHY-3002 : Step(145): len = 46627.8, overlap = 61
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.62491e-05
PHY-3002 : Step(146): len = 46771.4, overlap = 58.5
PHY-3002 : Step(147): len = 46973.6, overlap = 57.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.37117e-05
PHY-3002 : Step(148): len = 47495.5, overlap = 55
PHY-3002 : Step(149): len = 48723.3, overlap = 53
PHY-3002 : Step(150): len = 49238, overlap = 52.5
PHY-3002 : Step(151): len = 49491, overlap = 51.25
PHY-3002 : Step(152): len = 49693.8, overlap = 50
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.060065s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (286.1%)

PHY-3001 : Trial Legalized: Len = 63886.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052091s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000512217
PHY-3002 : Step(153): len = 60865.7, overlap = 7.25
PHY-3002 : Step(154): len = 58929.4, overlap = 11.75
PHY-3002 : Step(155): len = 57023.9, overlap = 15.75
PHY-3002 : Step(156): len = 55750.3, overlap = 18.5
PHY-3002 : Step(157): len = 55110.8, overlap = 22.75
PHY-3002 : Step(158): len = 54775, overlap = 21.25
PHY-3002 : Step(159): len = 54410.6, overlap = 24.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00102443
PHY-3002 : Step(160): len = 54721.7, overlap = 23.5
PHY-3002 : Step(161): len = 54927, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00204887
PHY-3002 : Step(162): len = 55120.7, overlap = 22
PHY-3002 : Step(163): len = 55278.4, overlap = 21.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004811s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 59233.3, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005737s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 4, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 59373.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6382, tnet num: 1965, tinst num: 812, tnode num: 8677, tedge num: 11177.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 136/1967.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66480, over cnt = 140(0%), over = 212, worst = 8
PHY-1002 : len = 67184, over cnt = 69(0%), over = 86, worst = 4
PHY-1002 : len = 67952, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 68088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.105545s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (118.4%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.52, top10 = 17.49, top15 = 13.62.
PHY-1001 : End incremental global routing;  0.154604s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (111.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058543s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.242473s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (109.5%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1740/1967.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005591s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.52, top10 = 17.49, top15 = 13.62.
OPT-1001 : End congestion update;  0.051141s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051370s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 774 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 812 instances, 765 slices, 24 macros(200 instances: 128 mslices 72 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 59381.8, Over = 0
PHY-3001 : End spreading;  0.005481s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 59381.8, Over = 0
PHY-3001 : End incremental legalization;  0.033171s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (471.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.148451s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (168.4%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048966s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1736/1967.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68088, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007313s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (213.7%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.52, top10 = 17.50, top15 = 13.62.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048648s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.206897
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.850170s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (113.9%)

RUN-1003 : finish command "place" in  5.071466s wall, 8.265625s user + 2.421875s system = 10.687500s CPU (210.7%)

RUN-1004 : used memory is 202 MB, reserved memory is 166 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 814 instances
RUN-1001 : 382 mslices, 383 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1967 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1443 nets have 2 pins
RUN-1001 : 408 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6382, tnet num: 1965, tinst num: 812, tnode num: 8677, tedge num: 11177.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 382 mslices, 383 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64880, over cnt = 147(0%), over = 226, worst = 8
PHY-1002 : len = 65856, over cnt = 81(0%), over = 98, worst = 4
PHY-1002 : len = 66864, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 66952, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 66984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134409s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (116.2%)

PHY-1001 : Congestion index: top1 = 30.58, top5 = 22.02, top10 = 17.18, top15 = 13.39.
PHY-1001 : End global routing;  0.183946s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (118.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 204, peak = 239.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 467, peak = 499.
PHY-1001 : End build detailed router design. 3.240373s wall, 3.203125s user + 0.031250s system = 3.234375s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34408, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.253722s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 531, reserve = 501, peak = 531.
PHY-1001 : End phase 1; 1.259361s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 176184, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End initial routed; 0.950742s wall, 1.500000s user + 0.046875s system = 1.546875s CPU (162.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1754(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.218   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.359933s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 1.310755s wall, 1.859375s user + 0.046875s system = 1.906250s CPU (145.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 176184, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015488s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (100.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 176112, over cnt = 11(0%), over = 11, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.022604s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (138.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 176088, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.025088s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (124.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 176088, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.019216s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (81.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1754(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.218   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.364282s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.165391s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.9%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.736670s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.7%)

PHY-1003 : Routed, final wirelength = 176088
PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End export database. 0.010368s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (150.7%)

PHY-1001 : End detail routing;  6.736049s wall, 7.250000s user + 0.078125s system = 7.328125s CPU (108.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6382, tnet num: 1965, tinst num: 812, tnode num: 8677, tedge num: 11177.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.650233s wall, 8.203125s user + 0.078125s system = 8.281250s CPU (108.2%)

RUN-1004 : used memory is 503 MB, reserved memory is 470 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      773   out of  19600    3.94%
#reg                     1047   out of  19600    5.34%
#le                      1481
  #lut only               434   out of   1481   29.30%
  #reg only               708   out of   1481   47.81%
  #lut&reg                339   out of   1481   22.89%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         454
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1481   |573     |200     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1106   |300     |121     |901     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |41     |35      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |518    |104     |53      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |4       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |143    |17      |14      |117     |0       |0       |
|    modu                    |Modulation                                       |61     |23      |14      |57      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |95      |29      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |93     |78      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |17     |14      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |39     |36      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1407  
    #2          2       286   
    #3          3       109   
    #4          4        13   
    #5        5-10       80   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6382, tnet num: 1965, tinst num: 812, tnode num: 8677, tedge num: 11177.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1965 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 812
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1967, pip num: 14351
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1260 valid insts, and 37771 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.024534s wall, 16.734375s user + 0.062500s system = 16.796875s CPU (555.4%)

RUN-1004 : used memory is 517 MB, reserved memory is 488 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230727_181155.log"
