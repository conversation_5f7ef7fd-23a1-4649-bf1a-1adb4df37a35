============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 18:26:11 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6833292967936"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1637 instances
RUN-0007 : 373 luts, 989 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2202 nets
RUN-1001 : 1653 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     205     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1635 instances, 373 luts, 989 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7790, tnet num: 2200, tinst num: 1635, tnode num: 11013, tedge num: 13153.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.299233s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 629959
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1635.
PHY-3001 : End clustering;  0.000032s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 578699, overlap = 18
PHY-3002 : Step(2): len = 460934, overlap = 9
PHY-3002 : Step(3): len = 389859, overlap = 11.25
PHY-3002 : Step(4): len = 369335, overlap = 15.75
PHY-3002 : Step(5): len = 353154, overlap = 18
PHY-3002 : Step(6): len = 343300, overlap = 20.25
PHY-3002 : Step(7): len = 332420, overlap = 20.25
PHY-3002 : Step(8): len = 322262, overlap = 20.25
PHY-3002 : Step(9): len = 313676, overlap = 20.25
PHY-3002 : Step(10): len = 302863, overlap = 20.25
PHY-3002 : Step(11): len = 295151, overlap = 20.25
PHY-3002 : Step(12): len = 287053, overlap = 20.25
PHY-3002 : Step(13): len = 279457, overlap = 20.25
PHY-3002 : Step(14): len = 271516, overlap = 20.25
PHY-3002 : Step(15): len = 267147, overlap = 20.25
PHY-3002 : Step(16): len = 259199, overlap = 20.25
PHY-3002 : Step(17): len = 252745, overlap = 20.25
PHY-3002 : Step(18): len = 247726, overlap = 20.25
PHY-3002 : Step(19): len = 243114, overlap = 20.25
PHY-3002 : Step(20): len = 235353, overlap = 20.25
PHY-3002 : Step(21): len = 231826, overlap = 20.25
PHY-3002 : Step(22): len = 224485, overlap = 20.25
PHY-3002 : Step(23): len = 220654, overlap = 20.25
PHY-3002 : Step(24): len = 215898, overlap = 20.25
PHY-3002 : Step(25): len = 208721, overlap = 20.25
PHY-3002 : Step(26): len = 203217, overlap = 20.25
PHY-3002 : Step(27): len = 201447, overlap = 20.25
PHY-3002 : Step(28): len = 189276, overlap = 20.25
PHY-3002 : Step(29): len = 172332, overlap = 21
PHY-3002 : Step(30): len = 168409, overlap = 21
PHY-3002 : Step(31): len = 166107, overlap = 20.75
PHY-3002 : Step(32): len = 122481, overlap = 18
PHY-3002 : Step(33): len = 119842, overlap = 18
PHY-3002 : Step(34): len = 117924, overlap = 18
PHY-3002 : Step(35): len = 114771, overlap = 18
PHY-3002 : Step(36): len = 109937, overlap = 18
PHY-3002 : Step(37): len = 107892, overlap = 18
PHY-3002 : Step(38): len = 106318, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.2222e-05
PHY-3002 : Step(39): len = 108229, overlap = 15.75
PHY-3002 : Step(40): len = 107247, overlap = 11.25
PHY-3002 : Step(41): len = 105087, overlap = 18
PHY-3002 : Step(42): len = 103821, overlap = 15.75
PHY-3002 : Step(43): len = 100888, overlap = 13.5
PHY-3002 : Step(44): len = 99018.6, overlap = 13.5
PHY-3002 : Step(45): len = 97588.6, overlap = 11.25
PHY-3002 : Step(46): len = 94555.4, overlap = 13.5
PHY-3002 : Step(47): len = 93561.3, overlap = 13.5
PHY-3002 : Step(48): len = 92236, overlap = 15.75
PHY-3002 : Step(49): len = 90662.6, overlap = 13.5
PHY-3002 : Step(50): len = 86755.2, overlap = 13.5
PHY-3002 : Step(51): len = 85227, overlap = 11.25
PHY-3002 : Step(52): len = 84297.6, overlap = 11.25
PHY-3002 : Step(53): len = 83024.2, overlap = 11.25
PHY-3002 : Step(54): len = 82405.4, overlap = 11.25
PHY-3002 : Step(55): len = 80420.3, overlap = 11.25
PHY-3002 : Step(56): len = 77573.1, overlap = 13.5
PHY-3002 : Step(57): len = 73124, overlap = 9
PHY-3002 : Step(58): len = 72410, overlap = 9
PHY-3002 : Step(59): len = 71864.7, overlap = 11.25
PHY-3002 : Step(60): len = 70969.7, overlap = 11.25
PHY-3002 : Step(61): len = 69982, overlap = 11.25
PHY-3002 : Step(62): len = 69629.9, overlap = 11.25
PHY-3002 : Step(63): len = 69302.8, overlap = 11.25
PHY-3002 : Step(64): len = 68499, overlap = 11.25
PHY-3002 : Step(65): len = 67402.2, overlap = 9
PHY-3002 : Step(66): len = 66018.5, overlap = 9
PHY-3002 : Step(67): len = 65183.9, overlap = 11.25
PHY-3002 : Step(68): len = 64466, overlap = 11.25
PHY-3002 : Step(69): len = 62620.3, overlap = 11.25
PHY-3002 : Step(70): len = 60501.7, overlap = 14.75
PHY-3002 : Step(71): len = 60007.1, overlap = 12.5
PHY-3002 : Step(72): len = 59082.5, overlap = 12.5
PHY-3002 : Step(73): len = 58957.7, overlap = 12.75
PHY-3002 : Step(74): len = 58864.7, overlap = 12.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000184444
PHY-3002 : Step(75): len = 58938.6, overlap = 10.5
PHY-3002 : Step(76): len = 58971.4, overlap = 8.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000368888
PHY-3002 : Step(77): len = 59144.3, overlap = 8.25
PHY-3002 : Step(78): len = 59205.2, overlap = 8.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006829s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (228.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069892s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(79): len = 62748.3, overlap = 11.0938
PHY-3002 : Step(80): len = 61647.8, overlap = 11.0312
PHY-3002 : Step(81): len = 60736.1, overlap = 11.1562
PHY-3002 : Step(82): len = 59888.8, overlap = 11.6875
PHY-3002 : Step(83): len = 58826.7, overlap = 11.0625
PHY-3002 : Step(84): len = 57942.2, overlap = 11.4062
PHY-3002 : Step(85): len = 56512.8, overlap = 11.0938
PHY-3002 : Step(86): len = 55244.2, overlap = 10.4375
PHY-3002 : Step(87): len = 53673.4, overlap = 9.84375
PHY-3002 : Step(88): len = 52475, overlap = 10.0625
PHY-3002 : Step(89): len = 51454.4, overlap = 13.5312
PHY-3002 : Step(90): len = 50677.2, overlap = 14.9688
PHY-3002 : Step(91): len = 50098.3, overlap = 12.3125
PHY-3002 : Step(92): len = 48970.3, overlap = 14.5
PHY-3002 : Step(93): len = 48460.2, overlap = 14.5625
PHY-3002 : Step(94): len = 48268.9, overlap = 14.75
PHY-3002 : Step(95): len = 48328.6, overlap = 14.875
PHY-3002 : Step(96): len = 47978.5, overlap = 15
PHY-3002 : Step(97): len = 47475.3, overlap = 13.9375
PHY-3002 : Step(98): len = 46816.3, overlap = 16.0625
PHY-3002 : Step(99): len = 46250, overlap = 16.625
PHY-3002 : Step(100): len = 46173.1, overlap = 16.7188
PHY-3002 : Step(101): len = 45896.9, overlap = 17.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000535807
PHY-3002 : Step(102): len = 45774.4, overlap = 17.625
PHY-3002 : Step(103): len = 45680.9, overlap = 17.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00107161
PHY-3002 : Step(104): len = 45775.2, overlap = 17.8125
PHY-3002 : Step(105): len = 45739.5, overlap = 17.2188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066882s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.52815e-05
PHY-3002 : Step(106): len = 46057.8, overlap = 71.7812
PHY-3002 : Step(107): len = 46405.7, overlap = 68.5312
PHY-3002 : Step(108): len = 47040.2, overlap = 60.4688
PHY-3002 : Step(109): len = 47104.3, overlap = 54.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000190563
PHY-3002 : Step(110): len = 47706.4, overlap = 51.4375
PHY-3002 : Step(111): len = 47947.1, overlap = 55.3125
PHY-3002 : Step(112): len = 48409, overlap = 54.3438
PHY-3002 : Step(113): len = 49218.7, overlap = 45.1875
PHY-3002 : Step(114): len = 50033.1, overlap = 41.8438
PHY-3002 : Step(115): len = 49631.3, overlap = 41.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000381126
PHY-3002 : Step(116): len = 49623.9, overlap = 41.0625
PHY-3002 : Step(117): len = 49970.2, overlap = 39.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7790, tnet num: 2200, tinst num: 1635, tnode num: 11013, tedge num: 13153.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 98.41 peak overflow 3.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2202.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52632, over cnt = 233(0%), over = 1133, worst = 19
PHY-1001 : End global iterations;  0.075165s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (207.9%)

PHY-1001 : Congestion index: top1 = 46.59, top5 = 25.35, top10 = 15.49, top15 = 10.87.
PHY-1001 : End incremental global routing;  0.126674s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (160.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.075002s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1596 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 1640 instances, 373 luts, 994 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 50217
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7810, tnet num: 2205, tinst num: 1640, tnode num: 11048, tedge num: 13183.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2205 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.328559s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(118): len = 50321.5, overlap = 2.09375
PHY-3002 : Step(119): len = 50363.2, overlap = 2.09375
PHY-3002 : Step(120): len = 50350.1, overlap = 2.09375
PHY-3002 : Step(121): len = 50357.9, overlap = 2.09375
PHY-3002 : Step(122): len = 50357.9, overlap = 2.09375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2205 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065937s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000376204
PHY-3002 : Step(123): len = 50366.6, overlap = 39.625
PHY-3002 : Step(124): len = 50366.6, overlap = 39.625
PHY-3001 : Final: Len = 50366.6, Over = 39.625
PHY-3001 : End incremental placement;  0.494478s wall, 0.500000s user + 0.062500s system = 0.562500s CPU (113.8%)

OPT-1001 : Total overflow 98.41 peak overflow 3.00
OPT-1001 : End high-fanout net optimization;  0.733292s wall, 0.781250s user + 0.093750s system = 0.875000s CPU (119.3%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1609/2207.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52864, over cnt = 233(0%), over = 1129, worst = 19
PHY-1002 : len = 60304, over cnt = 168(0%), over = 513, worst = 19
PHY-1002 : len = 65944, over cnt = 38(0%), over = 87, worst = 15
PHY-1002 : len = 66664, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 66816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104798s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (119.3%)

PHY-1001 : Congestion index: top1 = 40.78, top5 = 26.36, top10 = 18.11, top15 = 13.09.
OPT-1001 : End congestion update;  0.152826s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2205 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062747s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.218229s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (107.4%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 220.
OPT-1001 : End physical optimization;  1.244414s wall, 1.296875s user + 0.093750s system = 1.390625s CPU (111.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 187 SEQ to BLE.
SYN-4003 : Packing 807 remaining SEQ's ...
SYN-4005 : Packed 97 SEQ with LUT/SLICE
SYN-4006 : 110 single LUT's are left
SYN-4006 : 710 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1083/1416 primitive instances ...
PHY-3001 : End packing;  0.054851s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2035 nets
RUN-1001 : 1491 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50727.6, Over = 66.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6571, tnet num: 2033, tinst num: 841, tnode num: 8913, tedge num: 11537.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.329649s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (104.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.33638e-05
PHY-3002 : Step(125): len = 50186.4, overlap = 67.25
PHY-3002 : Step(126): len = 49686, overlap = 69.5
PHY-3002 : Step(127): len = 49538.4, overlap = 71
PHY-3002 : Step(128): len = 49037.1, overlap = 71
PHY-3002 : Step(129): len = 48991.9, overlap = 71
PHY-3002 : Step(130): len = 48849, overlap = 71
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.67275e-05
PHY-3002 : Step(131): len = 49111.2, overlap = 68.75
PHY-3002 : Step(132): len = 49472.7, overlap = 68.75
PHY-3002 : Step(133): len = 49748.4, overlap = 67.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.34551e-05
PHY-3002 : Step(134): len = 50635.5, overlap = 67.75
PHY-3002 : Step(135): len = 51633.2, overlap = 68.25
PHY-3002 : Step(136): len = 51937.6, overlap = 68
PHY-3002 : Step(137): len = 52365.5, overlap = 66.25
PHY-3002 : Step(138): len = 52698.8, overlap = 66.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.089931s wall, 0.062500s user + 0.125000s system = 0.187500s CPU (208.5%)

PHY-3001 : Trial Legalized: Len = 67296.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058152s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000737072
PHY-3002 : Step(139): len = 64299.7, overlap = 6.75
PHY-3002 : Step(140): len = 62314.2, overlap = 11.75
PHY-3002 : Step(141): len = 60493.8, overlap = 20
PHY-3002 : Step(142): len = 59199, overlap = 22.5
PHY-3002 : Step(143): len = 58480.7, overlap = 26.75
PHY-3002 : Step(144): len = 58010.3, overlap = 29.75
PHY-3002 : Step(145): len = 57580, overlap = 28.5
PHY-3002 : Step(146): len = 57266.7, overlap = 28.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00147414
PHY-3002 : Step(147): len = 57688.6, overlap = 27.75
PHY-3002 : Step(148): len = 57840.5, overlap = 27.5
PHY-3002 : Step(149): len = 57851.6, overlap = 27
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00294829
PHY-3002 : Step(150): len = 58021.7, overlap = 26.5
PHY-3002 : Step(151): len = 58081.4, overlap = 26.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005300s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63360.5, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006555s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (238.4%)

PHY-3001 : 6 instances has been re-located, deltaX = 2, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 63424.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6571, tnet num: 2033, tinst num: 841, tnode num: 8913, tedge num: 11537.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 71/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69752, over cnt = 152(0%), over = 228, worst = 6
PHY-1002 : len = 70520, over cnt = 70(0%), over = 113, worst = 6
PHY-1002 : len = 71912, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.150369s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.5%)

PHY-1001 : Congestion index: top1 = 32.03, top5 = 22.66, top10 = 17.76, top15 = 14.06.
PHY-1001 : End incremental global routing;  0.209663s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (104.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066350s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.307874s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1802/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006434s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.03, top5 = 22.66, top10 = 17.76, top15 = 14.06.
OPT-1001 : End congestion update;  0.060493s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057108s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 803 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63472.6, Over = 0
PHY-3001 : End spreading;  0.006013s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63472.6, Over = 0
PHY-3001 : End incremental legalization;  0.040790s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (76.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.173784s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (152.8%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054974s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1790/2035.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72008, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.018293s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.4%)

PHY-1001 : Congestion index: top1 = 32.03, top5 = 22.65, top10 = 17.78, top15 = 14.07.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055057s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.978966s wall, 1.078125s user + 0.000000s system = 1.078125s CPU (110.1%)

RUN-1003 : finish command "place" in  6.234141s wall, 10.046875s user + 3.640625s system = 13.687500s CPU (219.6%)

RUN-1004 : used memory is 202 MB, reserved memory is 165 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2035 nets
RUN-1001 : 1491 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6571, tnet num: 2033, tinst num: 841, tnode num: 8913, tedge num: 11537.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69200, over cnt = 147(0%), over = 227, worst = 7
PHY-1002 : len = 69968, over cnt = 72(0%), over = 118, worst = 7
PHY-1002 : len = 71496, over cnt = 4(0%), over = 5, worst = 2
PHY-1002 : len = 71576, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.155644s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (120.5%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 22.61, top10 = 17.69, top15 = 14.01.
PHY-1001 : End global routing;  0.211153s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (118.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 203, peak = 245.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 498, reserve = 467, peak = 498.
PHY-1001 : End build detailed router design. 3.418071s wall, 3.390625s user + 0.031250s system = 3.421875s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33888, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.505736s wall, 1.500000s user + 0.015625s system = 1.515625s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 531, reserve = 501, peak = 531.
PHY-1001 : End phase 1; 1.512514s wall, 1.515625s user + 0.015625s system = 1.531250s CPU (101.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181744, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 502, peak = 532.
PHY-1001 : End initial routed; 1.203750s wall, 2.031250s user + 0.062500s system = 2.093750s CPU (173.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1799(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.440   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.392871s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End phase 2; 1.596720s wall, 2.421875s user + 0.062500s system = 2.484375s CPU (155.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181744, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016317s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (95.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181696, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029023s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (215.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181744, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.027632s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181776, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.023370s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (133.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1799(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.440   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.397666s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (98.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.185771s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End phase 3; 0.815564s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (103.5%)

PHY-1003 : Routed, final wirelength = 181776
PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End export database. 0.010179s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (153.5%)

PHY-1001 : End detail routing;  7.542969s wall, 8.343750s user + 0.125000s system = 8.468750s CPU (112.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6571, tnet num: 2033, tinst num: 841, tnode num: 8913, tedge num: 11537.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.544353s wall, 9.359375s user + 0.140625s system = 9.500000s CPU (111.2%)

RUN-1004 : used memory is 503 MB, reserved memory is 471 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      829   out of  19600    4.23%
#reg                     1074   out of  19600    5.48%
#le                      1539
  #lut only               465   out of   1539   30.21%
  #reg only               710   out of   1539   46.13%
  #lut&reg                364   out of   1539   23.65%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         470
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1539   |603     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1145   |312     |132     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |38     |31      |7       |25      |0       |0       |
|    demodu                  |Demodulation                                     |540    |116     |58      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |160    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |13      |0       |25      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |87     |27      |21      |82      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |100     |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |21      |3       |15      |0       |0       |
|  u_uart                    |UART_Control                                     |99     |83      |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |20      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |40     |36      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1455  
    #2          2       306   
    #3          3       102   
    #4          4        20   
    #5        5-10       78   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6571, tnet num: 2033, tinst num: 841, tnode num: 8913, tedge num: 11537.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2033 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 841
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2035, pip num: 14732
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1340 valid insts, and 39095 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.573409s wall, 20.078125s user + 0.062500s system = 20.140625s CPU (563.6%)

RUN-1004 : used memory is 523 MB, reserved memory is 494 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_182611.log"
