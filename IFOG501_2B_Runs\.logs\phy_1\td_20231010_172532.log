============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Oct 10 17:25:33 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1628 instances
RUN-0007 : 373 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2198 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1637 nets have 2 pins
RUN-1001 : 446 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1626 instances, 373 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7791, tnet num: 2196, tinst num: 1626, tnode num: 11031, tedge num: 13175.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.314384s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (104.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 595421
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1626.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 473969, overlap = 20.25
PHY-3002 : Step(2): len = 438374, overlap = 20.25
PHY-3002 : Step(3): len = 396957, overlap = 20.25
PHY-3002 : Step(4): len = 368861, overlap = 20.25
PHY-3002 : Step(5): len = 359927, overlap = 20.25
PHY-3002 : Step(6): len = 342802, overlap = 18
PHY-3002 : Step(7): len = 325526, overlap = 18
PHY-3002 : Step(8): len = 317191, overlap = 18
PHY-3002 : Step(9): len = 310787, overlap = 20.25
PHY-3002 : Step(10): len = 294415, overlap = 20.25
PHY-3002 : Step(11): len = 287182, overlap = 20.25
PHY-3002 : Step(12): len = 281759, overlap = 20.25
PHY-3002 : Step(13): len = 274720, overlap = 20.25
PHY-3002 : Step(14): len = 266449, overlap = 20.25
PHY-3002 : Step(15): len = 261653, overlap = 20.25
PHY-3002 : Step(16): len = 255229, overlap = 20.25
PHY-3002 : Step(17): len = 248710, overlap = 20.25
PHY-3002 : Step(18): len = 241916, overlap = 20.25
PHY-3002 : Step(19): len = 237722, overlap = 20.25
PHY-3002 : Step(20): len = 230562, overlap = 20.25
PHY-3002 : Step(21): len = 226020, overlap = 20.25
PHY-3002 : Step(22): len = 221187, overlap = 20.25
PHY-3002 : Step(23): len = 216245, overlap = 20.25
PHY-3002 : Step(24): len = 211545, overlap = 20.25
PHY-3002 : Step(25): len = 206909, overlap = 20.25
PHY-3002 : Step(26): len = 201726, overlap = 20.25
PHY-3002 : Step(27): len = 197603, overlap = 20.25
PHY-3002 : Step(28): len = 192975, overlap = 20.25
PHY-3002 : Step(29): len = 188822, overlap = 20.25
PHY-3002 : Step(30): len = 184747, overlap = 20.25
PHY-3002 : Step(31): len = 178814, overlap = 20.25
PHY-3002 : Step(32): len = 174500, overlap = 20.25
PHY-3002 : Step(33): len = 171927, overlap = 20.25
PHY-3002 : Step(34): len = 166023, overlap = 20.25
PHY-3002 : Step(35): len = 154823, overlap = 20.25
PHY-3002 : Step(36): len = 151090, overlap = 20.25
PHY-3002 : Step(37): len = 149313, overlap = 20.25
PHY-3002 : Step(38): len = 121301, overlap = 20.25
PHY-3002 : Step(39): len = 112065, overlap = 20.25
PHY-3002 : Step(40): len = 111154, overlap = 20.25
PHY-3002 : Step(41): len = 107775, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113967
PHY-3002 : Step(42): len = 109027, overlap = 13.5
PHY-3002 : Step(43): len = 109480, overlap = 11.25
PHY-3002 : Step(44): len = 107411, overlap = 13.5
PHY-3002 : Step(45): len = 104858, overlap = 18
PHY-3002 : Step(46): len = 104403, overlap = 13.5
PHY-3002 : Step(47): len = 103582, overlap = 11.25
PHY-3002 : Step(48): len = 101787, overlap = 11.25
PHY-3002 : Step(49): len = 98303.8, overlap = 15.75
PHY-3002 : Step(50): len = 97421, overlap = 15.75
PHY-3002 : Step(51): len = 94965, overlap = 11.25
PHY-3002 : Step(52): len = 94098.4, overlap = 6.75
PHY-3002 : Step(53): len = 90485.8, overlap = 15.75
PHY-3002 : Step(54): len = 88789, overlap = 13.5
PHY-3002 : Step(55): len = 86394, overlap = 11.25
PHY-3002 : Step(56): len = 85735.1, overlap = 9
PHY-3002 : Step(57): len = 83881.9, overlap = 13.5
PHY-3002 : Step(58): len = 80976, overlap = 9
PHY-3002 : Step(59): len = 77755.2, overlap = 13.5
PHY-3002 : Step(60): len = 77480.3, overlap = 13.5
PHY-3002 : Step(61): len = 76511.6, overlap = 9
PHY-3002 : Step(62): len = 74816.3, overlap = 13.5
PHY-3002 : Step(63): len = 74217, overlap = 11.25
PHY-3002 : Step(64): len = 72899.7, overlap = 13.5
PHY-3002 : Step(65): len = 71218.3, overlap = 13.5
PHY-3002 : Step(66): len = 70963.3, overlap = 11.25
PHY-3002 : Step(67): len = 70359.8, overlap = 11.25
PHY-3002 : Step(68): len = 69308.3, overlap = 13.5
PHY-3002 : Step(69): len = 66439.8, overlap = 13.8125
PHY-3002 : Step(70): len = 65762.7, overlap = 9.3125
PHY-3002 : Step(71): len = 63902.6, overlap = 9.4375
PHY-3002 : Step(72): len = 63141.7, overlap = 9.4375
PHY-3002 : Step(73): len = 62228.6, overlap = 12
PHY-3002 : Step(74): len = 61554.9, overlap = 12.125
PHY-3002 : Step(75): len = 60724.8, overlap = 11.9375
PHY-3002 : Step(76): len = 59970.7, overlap = 15.0625
PHY-3002 : Step(77): len = 60001.6, overlap = 15.0625
PHY-3002 : Step(78): len = 59833.9, overlap = 15.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000227934
PHY-3002 : Step(79): len = 59602.4, overlap = 15.125
PHY-3002 : Step(80): len = 59676.9, overlap = 12.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000455868
PHY-3002 : Step(81): len = 59811, overlap = 12.5625
PHY-3002 : Step(82): len = 59829.5, overlap = 12.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.021471s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (218.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.073598s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00249628
PHY-3002 : Step(83): len = 62474.5, overlap = 12.2812
PHY-3002 : Step(84): len = 61887.4, overlap = 13.3438
PHY-3002 : Step(85): len = 60891.8, overlap = 12.7812
PHY-3002 : Step(86): len = 59080.3, overlap = 12.875
PHY-3002 : Step(87): len = 58006.1, overlap = 12.5
PHY-3002 : Step(88): len = 56604.4, overlap = 12.7188
PHY-3002 : Step(89): len = 55251.2, overlap = 13.2812
PHY-3002 : Step(90): len = 54454, overlap = 13.4688
PHY-3002 : Step(91): len = 52411.1, overlap = 14.7188
PHY-3002 : Step(92): len = 51808.5, overlap = 14.4062
PHY-3002 : Step(93): len = 51391.1, overlap = 15.875
PHY-3002 : Step(94): len = 50937.8, overlap = 16.3125
PHY-3002 : Step(95): len = 50419, overlap = 14.9062
PHY-3002 : Step(96): len = 50127.6, overlap = 14.8125
PHY-3002 : Step(97): len = 49919.6, overlap = 14.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.096063s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000176628
PHY-3002 : Step(98): len = 50782.8, overlap = 52.8125
PHY-3002 : Step(99): len = 51764.4, overlap = 44.2812
PHY-3002 : Step(100): len = 51998.5, overlap = 40.5625
PHY-3002 : Step(101): len = 52576.3, overlap = 41.4375
PHY-3002 : Step(102): len = 53060.7, overlap = 41.3125
PHY-3002 : Step(103): len = 53015.5, overlap = 36.6875
PHY-3002 : Step(104): len = 52765.2, overlap = 38.3438
PHY-3002 : Step(105): len = 52132.9, overlap = 37
PHY-3002 : Step(106): len = 51636.1, overlap = 37.2188
PHY-3002 : Step(107): len = 51353.3, overlap = 36.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000353255
PHY-3002 : Step(108): len = 51287.7, overlap = 36.1562
PHY-3002 : Step(109): len = 51223.6, overlap = 36.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00070651
PHY-3002 : Step(110): len = 51660.4, overlap = 34.0938
PHY-3002 : Step(111): len = 51626.7, overlap = 34.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7791, tnet num: 2196, tinst num: 1626, tnode num: 11031, tedge num: 13175.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 91.09 peak overflow 3.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55592, over cnt = 237(0%), over = 931, worst = 22
PHY-1001 : End global iterations;  0.100780s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (124.0%)

PHY-1001 : Congestion index: top1 = 41.47, top5 = 24.40, top10 = 15.57, top15 = 11.16.
PHY-1001 : End incremental global routing;  0.165533s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (113.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.084351s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1587 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1628 instances, 373 luts, 987 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 51769.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7799, tnet num: 2198, tinst num: 1628, tnode num: 11045, tedge num: 13187.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.355269s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(112): len = 51817.4, overlap = 2
PHY-3002 : Step(113): len = 51833.1, overlap = 2.125
PHY-3002 : Step(114): len = 51836, overlap = 2.0625
PHY-3002 : Step(115): len = 51833.5, overlap = 2.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.086957s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000844671
PHY-3002 : Step(116): len = 51833.1, overlap = 34.3125
PHY-3002 : Step(117): len = 51860, overlap = 34.125
PHY-3001 : Final: Len = 51860, Over = 34.125
PHY-3001 : End incremental placement;  0.617454s wall, 0.687500s user + 0.187500s system = 0.875000s CPU (141.7%)

OPT-1001 : Total overflow 91.03 peak overflow 3.00
OPT-1001 : End high-fanout net optimization;  0.905675s wall, 0.953125s user + 0.218750s system = 1.171875s CPU (129.4%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1710/2200.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55768, over cnt = 239(0%), over = 930, worst = 22
PHY-1002 : len = 62888, over cnt = 193(0%), over = 410, worst = 14
PHY-1002 : len = 66048, over cnt = 71(0%), over = 118, worst = 7
PHY-1002 : len = 67776, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 68144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.175367s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (160.4%)

PHY-1001 : Congestion index: top1 = 38.58, top5 = 24.89, top10 = 17.71, top15 = 13.17.
OPT-1001 : End congestion update;  0.237070s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (138.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.084534s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (110.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.324743s wall, 0.390625s user + 0.031250s system = 0.421875s CPU (129.9%)

OPT-1001 : Current memory(MB): used = 218, reserve = 184, peak = 220.
OPT-1001 : End physical optimization;  1.528660s wall, 1.609375s user + 0.265625s system = 1.875000s CPU (122.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 103 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1075/1403 primitive instances ...
PHY-3001 : End packing;  0.060873s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1479 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 835 instances, 788 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51829, Over = 63
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 835, tnode num: 8924, tedge num: 11546.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.357373s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.56773e-05
PHY-3002 : Step(118): len = 51515.7, overlap = 63.75
PHY-3002 : Step(119): len = 51492.2, overlap = 65.5
PHY-3002 : Step(120): len = 51422.6, overlap = 67
PHY-3002 : Step(121): len = 50998.3, overlap = 68
PHY-3002 : Step(122): len = 50771.1, overlap = 69.5
PHY-3002 : Step(123): len = 50700.1, overlap = 68.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.13546e-05
PHY-3002 : Step(124): len = 51212.4, overlap = 64.25
PHY-3002 : Step(125): len = 51669.8, overlap = 60.75
PHY-3002 : Step(126): len = 52005, overlap = 59.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000102709
PHY-3002 : Step(127): len = 52738.1, overlap = 55.75
PHY-3002 : Step(128): len = 53727.7, overlap = 52.25
PHY-3002 : Step(129): len = 54032, overlap = 52.75
PHY-3002 : Step(130): len = 54356.6, overlap = 53.5
PHY-3002 : Step(131): len = 54651.5, overlap = 50.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.130813s wall, 0.125000s user + 0.093750s system = 0.218750s CPU (167.2%)

PHY-3001 : Trial Legalized: Len = 69770.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059809s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000691447
PHY-3002 : Step(132): len = 67038.2, overlap = 7.25
PHY-3002 : Step(133): len = 64425.5, overlap = 11
PHY-3002 : Step(134): len = 62463.1, overlap = 15.5
PHY-3002 : Step(135): len = 61174.5, overlap = 17.25
PHY-3002 : Step(136): len = 60355.3, overlap = 16
PHY-3002 : Step(137): len = 59806.2, overlap = 19
PHY-3002 : Step(138): len = 59556.6, overlap = 21.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00138289
PHY-3002 : Step(139): len = 59879.1, overlap = 19.25
PHY-3002 : Step(140): len = 59988.9, overlap = 19.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00276579
PHY-3002 : Step(141): len = 60096.9, overlap = 19.5
PHY-3002 : Step(142): len = 60096.9, overlap = 19.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004893s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (319.3%)

PHY-3001 : Legalized: Len = 64712.9, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006347s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 7 instances has been re-located, deltaX = 1, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 64838.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 835, tnode num: 8924, tedge num: 11546.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 57/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71488, over cnt = 115(0%), over = 171, worst = 7
PHY-1002 : len = 72160, over cnt = 52(0%), over = 58, worst = 2
PHY-1002 : len = 72840, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 72936, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.213827s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (146.1%)

PHY-1001 : Congestion index: top1 = 32.41, top5 = 23.06, top10 = 17.67, top15 = 13.86.
PHY-1001 : End incremental global routing;  0.280742s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (133.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070485s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.385124s wall, 0.453125s user + 0.031250s system = 0.484375s CPU (125.8%)

OPT-1001 : Current memory(MB): used = 221, reserve = 186, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1788/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006856s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.41, top5 = 23.06, top10 = 17.67, top15 = 13.86.
OPT-1001 : End congestion update;  0.060302s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061986s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 797 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 835 instances, 788 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64895, Over = 0
PHY-3001 : End spreading;  0.008788s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (177.8%)

PHY-3001 : Final: Len = 64895, Over = 0
PHY-3001 : End incremental legalization;  0.050864s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.190330s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.5%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071542s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1777/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73040, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73040, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.025547s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (305.8%)

PHY-1001 : Congestion index: top1 = 32.56, top5 = 23.07, top10 = 17.69, top15 = 13.85.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065579s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.123451s wall, 1.218750s user + 0.046875s system = 1.265625s CPU (112.7%)

RUN-1003 : finish command "place" in  6.776113s wall, 9.671875s user + 3.046875s system = 12.718750s CPU (187.7%)

RUN-1004 : used memory is 204 MB, reserved memory is 169 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1479 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 835, tnode num: 8924, tedge num: 11546.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71056, over cnt = 117(0%), over = 176, worst = 7
PHY-1002 : len = 71808, over cnt = 50(0%), over = 61, worst = 3
PHY-1002 : len = 72512, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.176349s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (132.9%)

PHY-1001 : Congestion index: top1 = 32.52, top5 = 22.94, top10 = 17.59, top15 = 13.74.
PHY-1001 : End global routing;  0.240846s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (123.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 202, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 498, reserve = 466, peak = 498.
PHY-1001 : End build detailed router design. 7.543285s wall, 7.453125s user + 0.062500s system = 7.515625s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33936, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.753878s wall, 1.718750s user + 0.015625s system = 1.734375s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 530, reserve = 500, peak = 530.
PHY-1001 : End phase 1; 1.761330s wall, 1.734375s user + 0.015625s system = 1.750000s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 185504, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 501, peak = 531.
PHY-1001 : End initial routed; 1.335080s wall, 2.312500s user + 0.125000s system = 2.437500s CPU (182.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.375   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.426   |   8   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.403613s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 2; 1.738797s wall, 2.718750s user + 0.125000s system = 2.843750s CPU (163.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 185504, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015587s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (100.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 185520, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.040565s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (115.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 185576, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.024115s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (324.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 185592, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.023610s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (66.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.375   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.426   |   8   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.418468s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (100.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.198553s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (94.4%)

PHY-1001 : Current memory(MB): used = 547, reserve = 517, peak = 547.
PHY-1001 : End phase 3; 0.862927s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (105.0%)

PHY-1003 : Routed, final wirelength = 185592
PHY-1001 : Current memory(MB): used = 548, reserve = 517, peak = 548.
PHY-1001 : End export database. 0.014232s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.8%)

PHY-1001 : End detail routing;  12.143307s wall, 13.031250s user + 0.218750s system = 13.250000s CPU (109.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2031, tinst num: 835, tnode num: 8924, tedge num: 11546.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[22] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[25] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[26] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[33] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[6] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6610, tnet num: 2051, tinst num: 855, tnode num: 8964, tedge num: 11586.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.803213s wall, 3.890625s user + 0.265625s system = 4.156250s CPU (109.3%)

RUN-1003 : finish command "route" in  16.642439s wall, 17.625000s user + 0.531250s system = 18.156250s CPU (109.1%)

RUN-1004 : used memory is 524 MB, reserved memory is 493 MB, peak memory is 554 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      858   out of  19600    4.38%
#reg                     1076   out of  19600    5.49%
#le                      1560
  #lut only               484   out of   1560   31.03%
  #reg only               702   out of   1560   45.00%
  #lut&reg                374   out of   1560   23.97%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1560   |637     |221     |1107    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1155   |336     |128     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |23      |6       |24      |0       |0       |
|    demodu                  |Demodulation                                     |560    |156     |53      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |62      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |0       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |17      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |91     |31      |21      |87      |0       |1       |
|    rs422                   |Rs422Output                                      |307    |86      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |108    |90      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |24     |18      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |48     |44      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1463  
    #2          2       314   
    #3          3       104   
    #4          4        18   
    #5        5-10       81   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6610, tnet num: 2051, tinst num: 855, tnode num: 8964, tedge num: 11586.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2051 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 855
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2053, pip num: 14946
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1361 valid insts, and 39728 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.416235s wall, 18.781250s user + 0.046875s system = 18.828125s CPU (551.1%)

RUN-1004 : used memory is 553 MB, reserved memory is 523 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231010_172532.log"
