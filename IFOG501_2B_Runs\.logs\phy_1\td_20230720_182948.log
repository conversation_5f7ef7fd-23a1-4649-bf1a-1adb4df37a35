============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jul 20 18:29:48 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1603 instances
RUN-0007 : 368 luts, 972 seqs, 139 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2159 nets
RUN-1001 : 1627 nets have 2 pins
RUN-1001 : 417 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1601 instances, 368 luts, 972 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7654, tnet num: 2157, tinst num: 1601, tnode num: 10841, tedge num: 12918.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.310677s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 588962
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1601.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 479401, overlap = 20.25
PHY-3002 : Step(2): len = 441844, overlap = 20.25
PHY-3002 : Step(3): len = 373526, overlap = 13.5
PHY-3002 : Step(4): len = 331753, overlap = 15.75
PHY-3002 : Step(5): len = 311045, overlap = 18
PHY-3002 : Step(6): len = 291416, overlap = 20.25
PHY-3002 : Step(7): len = 285194, overlap = 20.25
PHY-3002 : Step(8): len = 277973, overlap = 20.25
PHY-3002 : Step(9): len = 273649, overlap = 20.25
PHY-3002 : Step(10): len = 267336, overlap = 20.25
PHY-3002 : Step(11): len = 261393, overlap = 20.25
PHY-3002 : Step(12): len = 255236, overlap = 20.25
PHY-3002 : Step(13): len = 250283, overlap = 20.25
PHY-3002 : Step(14): len = 243166, overlap = 20.25
PHY-3002 : Step(15): len = 237895, overlap = 20.25
PHY-3002 : Step(16): len = 233462, overlap = 20.25
PHY-3002 : Step(17): len = 228150, overlap = 20.25
PHY-3002 : Step(18): len = 222752, overlap = 20.25
PHY-3002 : Step(19): len = 218714, overlap = 20.25
PHY-3002 : Step(20): len = 212461, overlap = 20.25
PHY-3002 : Step(21): len = 208444, overlap = 20.25
PHY-3002 : Step(22): len = 204278, overlap = 20.25
PHY-3002 : Step(23): len = 199945, overlap = 20.25
PHY-3002 : Step(24): len = 193746, overlap = 20.25
PHY-3002 : Step(25): len = 189723, overlap = 20.25
PHY-3002 : Step(26): len = 186083, overlap = 20.25
PHY-3002 : Step(27): len = 180442, overlap = 20.25
PHY-3002 : Step(28): len = 174040, overlap = 20.25
PHY-3002 : Step(29): len = 171404, overlap = 20.25
PHY-3002 : Step(30): len = 167459, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000132959
PHY-3002 : Step(31): len = 168865, overlap = 15.75
PHY-3002 : Step(32): len = 166612, overlap = 15.75
PHY-3002 : Step(33): len = 164311, overlap = 15.75
PHY-3002 : Step(34): len = 159563, overlap = 13.5
PHY-3002 : Step(35): len = 154052, overlap = 11.25
PHY-3002 : Step(36): len = 150669, overlap = 15.75
PHY-3002 : Step(37): len = 147957, overlap = 13.5
PHY-3002 : Step(38): len = 145539, overlap = 6.75
PHY-3002 : Step(39): len = 141637, overlap = 9
PHY-3002 : Step(40): len = 138032, overlap = 13.5
PHY-3002 : Step(41): len = 133885, overlap = 9
PHY-3002 : Step(42): len = 132140, overlap = 6.75
PHY-3002 : Step(43): len = 129153, overlap = 9
PHY-3002 : Step(44): len = 117365, overlap = 11.25
PHY-3002 : Step(45): len = 115316, overlap = 6.75
PHY-3002 : Step(46): len = 113087, overlap = 9
PHY-3002 : Step(47): len = 112351, overlap = 9
PHY-3002 : Step(48): len = 111007, overlap = 9
PHY-3002 : Step(49): len = 108685, overlap = 11.25
PHY-3002 : Step(50): len = 105976, overlap = 11.25
PHY-3002 : Step(51): len = 104826, overlap = 11.25
PHY-3002 : Step(52): len = 103147, overlap = 6.75
PHY-3002 : Step(53): len = 100373, overlap = 9
PHY-3002 : Step(54): len = 98129.7, overlap = 6.75
PHY-3002 : Step(55): len = 97568.8, overlap = 6.75
PHY-3002 : Step(56): len = 95440.9, overlap = 11.25
PHY-3002 : Step(57): len = 93193.1, overlap = 13.5
PHY-3002 : Step(58): len = 91509.4, overlap = 15.75
PHY-3002 : Step(59): len = 90673.9, overlap = 11.25
PHY-3002 : Step(60): len = 88593.1, overlap = 9
PHY-3002 : Step(61): len = 87513.9, overlap = 9
PHY-3002 : Step(62): len = 85230.2, overlap = 4.625
PHY-3002 : Step(63): len = 84154.4, overlap = 6.875
PHY-3002 : Step(64): len = 82483.7, overlap = 14
PHY-3002 : Step(65): len = 82118.3, overlap = 13.9375
PHY-3002 : Step(66): len = 79396.8, overlap = 7.6875
PHY-3002 : Step(67): len = 77163.3, overlap = 10.125
PHY-3002 : Step(68): len = 75866.5, overlap = 12.375
PHY-3002 : Step(69): len = 75493.3, overlap = 16.75
PHY-3002 : Step(70): len = 75039.8, overlap = 14.5
PHY-3002 : Step(71): len = 73573.4, overlap = 9.8125
PHY-3002 : Step(72): len = 72015.5, overlap = 9.75
PHY-3002 : Step(73): len = 70506, overlap = 14.25
PHY-3002 : Step(74): len = 70032.7, overlap = 11.9375
PHY-3002 : Step(75): len = 68496.3, overlap = 11.5625
PHY-3002 : Step(76): len = 67629, overlap = 9.375
PHY-3002 : Step(77): len = 67541.6, overlap = 7.125
PHY-3002 : Step(78): len = 67463.7, overlap = 7.125
PHY-3002 : Step(79): len = 67002.8, overlap = 11.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000265919
PHY-3002 : Step(80): len = 66894.8, overlap = 11.625
PHY-3002 : Step(81): len = 66877.4, overlap = 7.125
PHY-3002 : Step(82): len = 66765.7, overlap = 7.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000531838
PHY-3002 : Step(83): len = 66711.8, overlap = 7.125
PHY-3002 : Step(84): len = 66710.6, overlap = 7.125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007222s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070553s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(85): len = 68836.7, overlap = 5.625
PHY-3002 : Step(86): len = 67302, overlap = 5.9375
PHY-3002 : Step(87): len = 65983.2, overlap = 5.25
PHY-3002 : Step(88): len = 63983, overlap = 5.9375
PHY-3002 : Step(89): len = 62741.3, overlap = 5.8125
PHY-3002 : Step(90): len = 61290.9, overlap = 6.1875
PHY-3002 : Step(91): len = 59890.9, overlap = 5.9375
PHY-3002 : Step(92): len = 58718.1, overlap = 5.78125
PHY-3002 : Step(93): len = 57352.8, overlap = 5.65625
PHY-3002 : Step(94): len = 56288.9, overlap = 3.65625
PHY-3002 : Step(95): len = 54116.2, overlap = 3.65625
PHY-3002 : Step(96): len = 52885.9, overlap = 4.4375
PHY-3002 : Step(97): len = 52308.4, overlap = 4.96875
PHY-3002 : Step(98): len = 51583.6, overlap = 5.34375
PHY-3002 : Step(99): len = 50933.7, overlap = 6.15625
PHY-3002 : Step(100): len = 50331.6, overlap = 5.28125
PHY-3002 : Step(101): len = 50057.3, overlap = 5.5
PHY-3002 : Step(102): len = 49498.4, overlap = 5.875
PHY-3002 : Step(103): len = 49006.5, overlap = 5.9375
PHY-3002 : Step(104): len = 48461.4, overlap = 6.3125
PHY-3002 : Step(105): len = 48104.5, overlap = 8.15625
PHY-3002 : Step(106): len = 48033.8, overlap = 8.03125
PHY-3002 : Step(107): len = 47632.2, overlap = 7.65625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000382041
PHY-3002 : Step(108): len = 47593.8, overlap = 10.3438
PHY-3002 : Step(109): len = 47176.9, overlap = 10.1562
PHY-3002 : Step(110): len = 47292.2, overlap = 9.78125
PHY-3002 : Step(111): len = 47288.9, overlap = 9.53125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000764082
PHY-3002 : Step(112): len = 47231.3, overlap = 9.59375
PHY-3002 : Step(113): len = 47030.4, overlap = 10.4688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.081418s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (96.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000103216
PHY-3002 : Step(114): len = 47399.4, overlap = 52.7812
PHY-3002 : Step(115): len = 48174.7, overlap = 54.125
PHY-3002 : Step(116): len = 49164.8, overlap = 48.6875
PHY-3002 : Step(117): len = 49987, overlap = 48.0625
PHY-3002 : Step(118): len = 49737.4, overlap = 45.5938
PHY-3002 : Step(119): len = 49281.5, overlap = 46.4375
PHY-3002 : Step(120): len = 49117.6, overlap = 46.0625
PHY-3002 : Step(121): len = 48953.6, overlap = 46.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000206433
PHY-3002 : Step(122): len = 49038.3, overlap = 46.5312
PHY-3002 : Step(123): len = 49373, overlap = 45.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000412866
PHY-3002 : Step(124): len = 49566, overlap = 44.0625
PHY-3002 : Step(125): len = 50876.2, overlap = 40.5938
PHY-3002 : Step(126): len = 51357.4, overlap = 38.0938
PHY-3002 : Step(127): len = 51156.4, overlap = 35.875
PHY-3002 : Step(128): len = 51156.4, overlap = 35.875
PHY-3002 : Step(129): len = 50982.8, overlap = 35.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7654, tnet num: 2157, tinst num: 1601, tnode num: 10841, tedge num: 12918.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 81.75 peak overflow 3.91
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2159.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52976, over cnt = 223(0%), over = 929, worst = 21
PHY-1001 : End global iterations;  0.067235s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (139.4%)

PHY-1001 : Congestion index: top1 = 40.60, top5 = 24.28, top10 = 15.50, top15 = 10.89.
PHY-1001 : End incremental global routing;  0.119339s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (130.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.075578s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.231997s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (114.5%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1654/2159.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52976, over cnt = 223(0%), over = 929, worst = 21
PHY-1002 : len = 59496, over cnt = 137(0%), over = 329, worst = 19
PHY-1002 : len = 63416, over cnt = 29(0%), over = 55, worst = 7
PHY-1002 : len = 64088, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 64184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096411s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (113.4%)

PHY-1001 : Congestion index: top1 = 35.95, top5 = 24.61, top10 = 17.31, top15 = 12.53.
OPT-1001 : End congestion update;  0.147252s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2157 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066321s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.216352s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.3%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.765463s wall, 0.781250s user + 0.031250s system = 0.812500s CPU (106.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 112 SEQ with LUT/SLICE
SYN-4006 : 96 single LUT's are left
SYN-4006 : 678 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1046/1367 primitive instances ...
PHY-3001 : End packing;  0.059364s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 813 instances
RUN-1001 : 382 mslices, 382 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1991 nets
RUN-1001 : 1468 nets have 2 pins
RUN-1001 : 406 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 811 instances, 764 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50655, Over = 63
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6435, tnet num: 1989, tinst num: 811, tnode num: 8733, tedge num: 11291.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.330641s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.35785e-05
PHY-3002 : Step(130): len = 50019.9, overlap = 62.25
PHY-3002 : Step(131): len = 49700.2, overlap = 63.75
PHY-3002 : Step(132): len = 49519.7, overlap = 66.25
PHY-3002 : Step(133): len = 49273.6, overlap = 68.5
PHY-3002 : Step(134): len = 49335.2, overlap = 67
PHY-3002 : Step(135): len = 49376, overlap = 65.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.71571e-05
PHY-3002 : Step(136): len = 49792, overlap = 64
PHY-3002 : Step(137): len = 50156.1, overlap = 61.75
PHY-3002 : Step(138): len = 50353.3, overlap = 63
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.25144e-05
PHY-3002 : Step(139): len = 51633.2, overlap = 60.5
PHY-3002 : Step(140): len = 52618, overlap = 59
PHY-3002 : Step(141): len = 52669.1, overlap = 58
PHY-3002 : Step(142): len = 52909.7, overlap = 56.25
PHY-3002 : Step(143): len = 53095.7, overlap = 55
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.095506s wall, 0.015625s user + 0.046875s system = 0.062500s CPU (65.4%)

PHY-3001 : Trial Legalized: Len = 66882.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058188s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000405497
PHY-3002 : Step(144): len = 64621.6, overlap = 4.75
PHY-3002 : Step(145): len = 62251.4, overlap = 12.75
PHY-3002 : Step(146): len = 60140, overlap = 17.5
PHY-3002 : Step(147): len = 58961.2, overlap = 20
PHY-3002 : Step(148): len = 58355.6, overlap = 22.5
PHY-3002 : Step(149): len = 57953, overlap = 22.75
PHY-3002 : Step(150): len = 57466.6, overlap = 22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000810993
PHY-3002 : Step(151): len = 57919, overlap = 20.75
PHY-3002 : Step(152): len = 58153, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00162199
PHY-3002 : Step(153): len = 58356.8, overlap = 19.5
PHY-3002 : Step(154): len = 58502, overlap = 19.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004854s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62534.8, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006154s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (253.9%)

PHY-3001 : 12 instances has been re-located, deltaX = 0, deltaY = 10, maxDist = 1.
PHY-3001 : Final: Len = 62618.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6435, tnet num: 1989, tinst num: 811, tnode num: 8733, tedge num: 11291.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 92/1991.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68840, over cnt = 133(0%), over = 220, worst = 7
PHY-1002 : len = 69376, over cnt = 70(0%), over = 118, worst = 7
PHY-1002 : len = 70832, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 70912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117482s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (146.3%)

PHY-1001 : Congestion index: top1 = 31.81, top5 = 22.72, top10 = 17.82, top15 = 13.97.
PHY-1001 : End incremental global routing;  0.174228s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (125.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067781s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.276591s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (118.6%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1782/1991.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007232s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.81, top5 = 22.72, top10 = 17.82, top15 = 13.97.
OPT-1001 : End congestion update;  0.063350s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (74.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057761s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.123266s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (88.7%)

OPT-1001 : Current memory(MB): used = 221, reserve = 186, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060319s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1782/1991.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70912, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006665s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (234.4%)

PHY-1001 : Congestion index: top1 = 31.81, top5 = 22.72, top10 = 17.82, top15 = 13.97.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054840s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.900236s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (104.1%)

RUN-1003 : finish command "place" in  6.086421s wall, 9.609375s user + 2.875000s system = 12.484375s CPU (205.1%)

RUN-1004 : used memory is 203 MB, reserved memory is 168 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 813 instances
RUN-1001 : 382 mslices, 382 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1991 nets
RUN-1001 : 1468 nets have 2 pins
RUN-1001 : 406 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6435, tnet num: 1989, tinst num: 811, tnode num: 8733, tedge num: 11291.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 382 mslices, 382 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68208, over cnt = 138(0%), over = 224, worst = 7
PHY-1002 : len = 68792, over cnt = 73(0%), over = 119, worst = 7
PHY-1002 : len = 70432, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117441s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (146.3%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 22.55, top10 = 17.69, top15 = 13.87.
PHY-1001 : End global routing;  0.171181s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (127.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 498, reserve = 468, peak = 498.
PHY-1001 : End build detailed router design. 3.500468s wall, 3.484375s user + 0.031250s system = 3.515625s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33200, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.507314s wall, 1.468750s user + 0.015625s system = 1.484375s CPU (98.5%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.514257s wall, 1.468750s user + 0.015625s system = 1.484375s CPU (98.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183456, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End initial routed; 1.174413s wall, 2.281250s user + 0.062500s system = 2.343750s CPU (199.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1766(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.760   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.394212s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.568736s wall, 2.671875s user + 0.062500s system = 2.734375s CPU (174.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183456, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016623s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (94.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 183392, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024507s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (191.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 183472, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.027929s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (111.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 183488, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021100s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1766(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.760   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.407987s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.186922s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.812222s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (102.0%)

PHY-1003 : Routed, final wirelength = 183488
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.011279s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (138.5%)

PHY-1001 : End detail routing;  7.602915s wall, 8.640625s user + 0.109375s system = 8.750000s CPU (115.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6435, tnet num: 1989, tinst num: 811, tnode num: 8733, tedge num: 11291.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.566700s wall, 9.640625s user + 0.125000s system = 9.765625s CPU (114.0%)

RUN-1004 : used memory is 527 MB, reserved memory is 498 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      801   out of  19600    4.09%
#reg                     1047   out of  19600    5.34%
#le                      1479
  #lut only               432   out of   1479   29.21%
  #reg only               678   out of   1479   45.84%
  #lut&reg                369   out of   1479   24.95%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         458
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1479   |587     |214     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1087   |294     |121     |892     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |21      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |521    |116     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |47     |0       |0       |47      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |14      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |14      |0       |30      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |63     |28      |14      |59      |0       |1       |
|    rs422                   |Rs422Output                                      |308    |87      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |102    |89      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |19     |16      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |49     |46      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1432  
    #2          2       284   
    #3          3       108   
    #4          4        14   
    #5        5-10       82   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6435, tnet num: 1989, tinst num: 811, tnode num: 8733, tedge num: 11291.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1989 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 811
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1991, pip num: 14646
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1313 valid insts, and 38689 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.677923s wall, 19.937500s user + 0.046875s system = 19.984375s CPU (543.4%)

RUN-1004 : used memory is 549 MB, reserved memory is 518 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230720_182948.log"
