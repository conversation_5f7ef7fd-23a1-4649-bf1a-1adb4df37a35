============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 11:12:24 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1625 instances
RUN-0007 : 366 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2196 nets
RUN-1001 : 1640 nets have 2 pins
RUN-1001 : 445 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1623 instances, 366 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2194, tinst num: 1623, tnode num: 11024, tedge num: 13159.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.290432s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (96.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 648516
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1623.
PHY-3001 : End clustering;  0.000024s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 520648, overlap = 20.25
PHY-3002 : Step(2): len = 478240, overlap = 20.25
PHY-3002 : Step(3): len = 434756, overlap = 15.75
PHY-3002 : Step(4): len = 407637, overlap = 13.5
PHY-3002 : Step(5): len = 394663, overlap = 18
PHY-3002 : Step(6): len = 382578, overlap = 15.75
PHY-3002 : Step(7): len = 366776, overlap = 18
PHY-3002 : Step(8): len = 333011, overlap = 18
PHY-3002 : Step(9): len = 324363, overlap = 18
PHY-3002 : Step(10): len = 317603, overlap = 18
PHY-3002 : Step(11): len = 280429, overlap = 18
PHY-3002 : Step(12): len = 257124, overlap = 20.25
PHY-3002 : Step(13): len = 249913, overlap = 20.25
PHY-3002 : Step(14): len = 247787, overlap = 20.25
PHY-3002 : Step(15): len = 241049, overlap = 20.25
PHY-3002 : Step(16): len = 233622, overlap = 20.25
PHY-3002 : Step(17): len = 230057, overlap = 20.25
PHY-3002 : Step(18): len = 225166, overlap = 20.25
PHY-3002 : Step(19): len = 213599, overlap = 20.25
PHY-3002 : Step(20): len = 210853, overlap = 20.25
PHY-3002 : Step(21): len = 207269, overlap = 20.25
PHY-3002 : Step(22): len = 200130, overlap = 20.25
PHY-3002 : Step(23): len = 192969, overlap = 20.25
PHY-3002 : Step(24): len = 190419, overlap = 20.25
PHY-3002 : Step(25): len = 184796, overlap = 18
PHY-3002 : Step(26): len = 178844, overlap = 18
PHY-3002 : Step(27): len = 173812, overlap = 18
PHY-3002 : Step(28): len = 171270, overlap = 18
PHY-3002 : Step(29): len = 165260, overlap = 18
PHY-3002 : Step(30): len = 157618, overlap = 18
PHY-3002 : Step(31): len = 153858, overlap = 18
PHY-3002 : Step(32): len = 151278, overlap = 18
PHY-3002 : Step(33): len = 146213, overlap = 13.5
PHY-3002 : Step(34): len = 142275, overlap = 15.75
PHY-3002 : Step(35): len = 135911, overlap = 18
PHY-3002 : Step(36): len = 133793, overlap = 18
PHY-3002 : Step(37): len = 130240, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000110044
PHY-3002 : Step(38): len = 132126, overlap = 11.25
PHY-3002 : Step(39): len = 131553, overlap = 11.25
PHY-3002 : Step(40): len = 130426, overlap = 15.75
PHY-3002 : Step(41): len = 128961, overlap = 15.75
PHY-3002 : Step(42): len = 126819, overlap = 13.5
PHY-3002 : Step(43): len = 125716, overlap = 6.75
PHY-3002 : Step(44): len = 123070, overlap = 6.75
PHY-3002 : Step(45): len = 118563, overlap = 11.25
PHY-3002 : Step(46): len = 115642, overlap = 13.5
PHY-3002 : Step(47): len = 115561, overlap = 11.25
PHY-3002 : Step(48): len = 112246, overlap = 9
PHY-3002 : Step(49): len = 109273, overlap = 13.5
PHY-3002 : Step(50): len = 108046, overlap = 11.25
PHY-3002 : Step(51): len = 103395, overlap = 9
PHY-3002 : Step(52): len = 102266, overlap = 6.75
PHY-3002 : Step(53): len = 100244, overlap = 9
PHY-3002 : Step(54): len = 98107.2, overlap = 9
PHY-3002 : Step(55): len = 95043.4, overlap = 9
PHY-3002 : Step(56): len = 94640.1, overlap = 9
PHY-3002 : Step(57): len = 92683.8, overlap = 9
PHY-3002 : Step(58): len = 91626.9, overlap = 9
PHY-3002 : Step(59): len = 88322.1, overlap = 11.25
PHY-3002 : Step(60): len = 87249.3, overlap = 9
PHY-3002 : Step(61): len = 85663.8, overlap = 9
PHY-3002 : Step(62): len = 83427.2, overlap = 11.25
PHY-3002 : Step(63): len = 81316.7, overlap = 11.25
PHY-3002 : Step(64): len = 79128.1, overlap = 9
PHY-3002 : Step(65): len = 77166.2, overlap = 11.3125
PHY-3002 : Step(66): len = 76988.8, overlap = 7.0625
PHY-3002 : Step(67): len = 74786.3, overlap = 11.5
PHY-3002 : Step(68): len = 72323.5, overlap = 13.5
PHY-3002 : Step(69): len = 70414.6, overlap = 15.75
PHY-3002 : Step(70): len = 70111.2, overlap = 13.5
PHY-3002 : Step(71): len = 69475.8, overlap = 6.75
PHY-3002 : Step(72): len = 68610.1, overlap = 6.75
PHY-3002 : Step(73): len = 68123.1, overlap = 11.25
PHY-3002 : Step(74): len = 67941.3, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000220088
PHY-3002 : Step(75): len = 68037.6, overlap = 11.25
PHY-3002 : Step(76): len = 68223.7, overlap = 6.75
PHY-3002 : Step(77): len = 68252.3, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000440176
PHY-3002 : Step(78): len = 67998.7, overlap = 6.75
PHY-3002 : Step(79): len = 67900.3, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005931s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063039s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (99.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(80): len = 69907.4, overlap = 4.625
PHY-3002 : Step(81): len = 68587.7, overlap = 3.96875
PHY-3002 : Step(82): len = 67769.9, overlap = 3.75
PHY-3002 : Step(83): len = 66046.6, overlap = 4.0625
PHY-3002 : Step(84): len = 64773.3, overlap = 2.6875
PHY-3002 : Step(85): len = 63165, overlap = 3.5625
PHY-3002 : Step(86): len = 61510, overlap = 2.4375
PHY-3002 : Step(87): len = 60315.4, overlap = 2.125
PHY-3002 : Step(88): len = 58280, overlap = 1.9375
PHY-3002 : Step(89): len = 56625, overlap = 3.0625
PHY-3002 : Step(90): len = 55384.6, overlap = 4.65625
PHY-3002 : Step(91): len = 54717.8, overlap = 3.9375
PHY-3002 : Step(92): len = 53989.8, overlap = 3.65625
PHY-3002 : Step(93): len = 53466.3, overlap = 3.40625
PHY-3002 : Step(94): len = 51858.5, overlap = 2.59375
PHY-3002 : Step(95): len = 51331, overlap = 2.03125
PHY-3002 : Step(96): len = 50775.9, overlap = 2.0625
PHY-3002 : Step(97): len = 50197, overlap = 4.75
PHY-3002 : Step(98): len = 49397, overlap = 7.875
PHY-3002 : Step(99): len = 49105.9, overlap = 8.6875
PHY-3002 : Step(100): len = 48693.9, overlap = 8.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0002803
PHY-3002 : Step(101): len = 48627.2, overlap = 8.375
PHY-3002 : Step(102): len = 48376.3, overlap = 6.96875
PHY-3002 : Step(103): len = 48471.6, overlap = 7.21875
PHY-3002 : Step(104): len = 48463, overlap = 7.90625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0005606
PHY-3002 : Step(105): len = 48330.3, overlap = 7.90625
PHY-3002 : Step(106): len = 48363.9, overlap = 9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059476s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100619
PHY-3002 : Step(107): len = 48349.7, overlap = 61.0625
PHY-3002 : Step(108): len = 48954.4, overlap = 57.1875
PHY-3002 : Step(109): len = 49427.4, overlap = 59.2812
PHY-3002 : Step(110): len = 49427.4, overlap = 55.75
PHY-3002 : Step(111): len = 49214, overlap = 54.5312
PHY-3002 : Step(112): len = 49305.6, overlap = 54.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000201238
PHY-3002 : Step(113): len = 49294.7, overlap = 50.625
PHY-3002 : Step(114): len = 49550.5, overlap = 49.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000402476
PHY-3002 : Step(115): len = 49939.5, overlap = 48.75
PHY-3002 : Step(116): len = 50577.6, overlap = 43.5312
PHY-3002 : Step(117): len = 51777.6, overlap = 40.125
PHY-3002 : Step(118): len = 51802.3, overlap = 34.5625
PHY-3002 : Step(119): len = 51847.8, overlap = 34.4688
PHY-3002 : Step(120): len = 51713.8, overlap = 29.0625
PHY-3002 : Step(121): len = 51577.1, overlap = 27.8438
PHY-3002 : Step(122): len = 51593.2, overlap = 27.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2194, tinst num: 1623, tnode num: 11024, tedge num: 13159.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.03 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2196.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54544, over cnt = 256(0%), over = 1022, worst = 16
PHY-1001 : End global iterations;  0.074469s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (83.9%)

PHY-1001 : Congestion index: top1 = 40.50, top5 = 25.09, top10 = 16.05, top15 = 11.40.
PHY-1001 : End incremental global routing;  0.123013s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (101.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069971s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.224234s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.6%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1702/2196.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54544, over cnt = 256(0%), over = 1022, worst = 16
PHY-1002 : len = 59672, over cnt = 194(0%), over = 463, worst = 13
PHY-1002 : len = 64816, over cnt = 32(0%), over = 50, worst = 8
PHY-1002 : len = 65560, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 65672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.090734s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (137.8%)

PHY-1001 : Congestion index: top1 = 34.85, top5 = 24.61, top10 = 17.75, top15 = 13.20.
OPT-1001 : End congestion update;  0.133074s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (129.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2194 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062418s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.198125s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (118.3%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.701648s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (104.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 366 LUT to BLE ...
SYN-4008 : Packed 366 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 702 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1068/1400 primitive instances ...
PHY-3001 : End packing;  0.050693s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2028 nets
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51634.4, Over = 53.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6561, tnet num: 2026, tinst num: 835, tnode num: 8922, tedge num: 11530.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.309829s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.7316e-05
PHY-3002 : Step(123): len = 50949.3, overlap = 58
PHY-3002 : Step(124): len = 50776.5, overlap = 58
PHY-3002 : Step(125): len = 50582.7, overlap = 60.5
PHY-3002 : Step(126): len = 50466.8, overlap = 60
PHY-3002 : Step(127): len = 50519.2, overlap = 59.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.4632e-05
PHY-3002 : Step(128): len = 50537.4, overlap = 60
PHY-3002 : Step(129): len = 50785.9, overlap = 59.25
PHY-3002 : Step(130): len = 51500.7, overlap = 57.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000109264
PHY-3002 : Step(131): len = 51890.1, overlap = 56.25
PHY-3002 : Step(132): len = 52610.7, overlap = 55.25
PHY-3002 : Step(133): len = 53568.6, overlap = 48
PHY-3002 : Step(134): len = 54023.6, overlap = 46
PHY-3002 : Step(135): len = 54357.3, overlap = 44.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.104945s wall, 0.078125s user + 0.109375s system = 0.187500s CPU (178.7%)

PHY-3001 : Trial Legalized: Len = 66135.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050115s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000625715
PHY-3002 : Step(136): len = 63368.2, overlap = 6.75
PHY-3002 : Step(137): len = 61205.4, overlap = 13.25
PHY-3002 : Step(138): len = 59418.2, overlap = 19.25
PHY-3002 : Step(139): len = 58584.9, overlap = 21.75
PHY-3002 : Step(140): len = 57935.4, overlap = 25
PHY-3002 : Step(141): len = 57524.7, overlap = 26.25
PHY-3002 : Step(142): len = 57189.4, overlap = 27.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00125143
PHY-3002 : Step(143): len = 57553.5, overlap = 27.5
PHY-3002 : Step(144): len = 57742.7, overlap = 28.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00250286
PHY-3002 : Step(145): len = 57932.1, overlap = 27.75
PHY-3002 : Step(146): len = 58084.9, overlap = 27.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005405s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (289.1%)

PHY-3001 : Legalized: Len = 62526.2, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006155s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 7 instances has been re-located, deltaX = 0, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 62606.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6561, tnet num: 2026, tinst num: 835, tnode num: 8922, tedge num: 11530.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 91/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68504, over cnt = 172(0%), over = 260, worst = 7
PHY-1002 : len = 69088, over cnt = 113(0%), over = 163, worst = 7
PHY-1002 : len = 71144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107454s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (130.9%)

PHY-1001 : Congestion index: top1 = 31.90, top5 = 22.96, top10 = 17.89, top15 = 14.12.
PHY-1001 : End incremental global routing;  0.158071s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (118.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063438s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.251990s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (111.6%)

OPT-1001 : Current memory(MB): used = 221, reserve = 186, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1789/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006430s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.90, top5 = 22.96, top10 = 17.89, top15 = 14.12.
OPT-1001 : End congestion update;  0.054165s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059579s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 797 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62615.4, Over = 0
PHY-3001 : End spreading;  0.005225s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (299.0%)

PHY-3001 : Final: Len = 62615.4, Over = 0
PHY-3001 : End incremental legalization;  0.034033s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (137.7%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.160020s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (97.6%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057009s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1785/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008412s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.90, top5 = 22.96, top10 = 17.90, top15 = 14.12.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052062s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.551724
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.878201s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (103.2%)

RUN-1003 : finish command "place" in  5.212020s wall, 8.671875s user + 2.593750s system = 11.265625s CPU (216.1%)

RUN-1004 : used memory is 206 MB, reserved memory is 170 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2028 nets
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6561, tnet num: 2026, tinst num: 835, tnode num: 8922, tedge num: 11530.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68016, over cnt = 162(0%), over = 256, worst = 7
PHY-1002 : len = 68672, over cnt = 102(0%), over = 157, worst = 7
PHY-1002 : len = 70840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148212s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (73.8%)

PHY-1001 : Congestion index: top1 = 31.36, top5 = 22.70, top10 = 17.71, top15 = 13.97.
PHY-1001 : End global routing;  0.196469s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (87.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 205, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.306916s wall, 3.203125s user + 0.031250s system = 3.234375s CPU (97.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34616, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.263174s wall, 1.234375s user + 0.031250s system = 1.265625s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 532, reserve = 504, peak = 532.
PHY-1001 : End phase 1; 1.270178s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (100.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184160, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 505, peak = 534.
PHY-1001 : End initial routed; 1.125856s wall, 2.031250s user + 0.046875s system = 2.078125s CPU (184.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.458   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.369500s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (97.3%)

PHY-1001 : Current memory(MB): used = 536, reserve = 506, peak = 536.
PHY-1001 : End phase 2; 1.495462s wall, 2.390625s user + 0.046875s system = 2.437500s CPU (163.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184160, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014270s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184112, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025455s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (61.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184128, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.018015s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (86.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.458   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.369613s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.179695s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (95.6%)

PHY-1001 : Current memory(MB): used = 550, reserve = 520, peak = 550.
PHY-1001 : End phase 3; 0.730363s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (100.5%)

PHY-1003 : Routed, final wirelength = 184128
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.011119s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (140.5%)

PHY-1001 : End detail routing;  7.004977s wall, 7.765625s user + 0.125000s system = 7.890625s CPU (112.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6561, tnet num: 2026, tinst num: 835, tnode num: 8922, tedge num: 11530.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.951940s wall, 8.671875s user + 0.140625s system = 8.812500s CPU (110.8%)

RUN-1004 : used memory is 526 MB, reserved memory is 497 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      819   out of  19600    4.18%
#reg                     1075   out of  19600    5.48%
#le                      1521
  #lut only               446   out of   1521   29.32%
  #reg only               702   out of   1521   46.15%
  #lut&reg                373   out of   1521   24.52%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         480
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1521   |594     |225     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1124   |293     |135     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |22      |9       |22      |0       |0       |
|    demodu                  |Demodulation                                     |528    |119     |57      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |4       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |138    |16      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |85     |29      |21      |81      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |86      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |21      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |111    |98      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |29      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |54     |51      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1445  
    #2          2       309   
    #3          3       109   
    #4          4        18   
    #5        5-10       75   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6561, tnet num: 2026, tinst num: 835, tnode num: 8922, tedge num: 11530.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 835
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2028, pip num: 14736
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1312 valid insts, and 39096 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.175137s wall, 18.796875s user + 0.078125s system = 18.875000s CPU (594.5%)

RUN-1004 : used memory is 549 MB, reserved memory is 518 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_111224.log"
