============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 10:24:35 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 1 view nodes, 5 trigger nets, 5 data nets.
KIT-1004 : Chipwatcher code = 1101110111110100
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=1,BUS_DIN_NUM=5,BUS_CTRL_NUM=14,BUS_WIDTH='{32'sb0101},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=36) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=36) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=1,BUS_DIN_NUM=5,BUS_CTRL_NUM=14,BUS_WIDTH='{32'sb0101},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=5,BUS_CTRL_NUM=14,BUS_WIDTH='{32'sb0101},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0101) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=1,BUS_DIN_NUM=5,BUS_CTRL_NUM=14,BUS_WIDTH='{32'sb0101},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=36)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=36)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=1,BUS_DIN_NUM=5,BUS_CTRL_NUM=14,BUS_WIDTH='{32'sb0101},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=5,BUS_CTRL_NUM=14,BUS_WIDTH='{32'sb0101},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0101)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 1488/7 useful/useless nets, 914/2 useful/useless insts
SYN-1016 : Merged 12 instances.
SYN-1032 : 1365/2 useful/useless nets, 791/2 useful/useless insts
SYN-1032 : 1345/20 useful/useless nets, 1132/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 171 better
SYN-1014 : Optimize round 2
SYN-1032 : 1239/15 useful/useless nets, 1026/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-2571 : Optimize after map_dsp, round 1, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 5 instances.
SYN-2501 : Optimize round 1, 10 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 1528/2 useful/useless nets, 1319/1 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5063, tnet num: 1528, tinst num: 1318, tnode num: 6377, tedge num: 7713.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1528 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 113 (3.98), #lev = 6 (2.08)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 113 (3.98), #lev = 6 (2.08)
SYN-3001 : Logic optimization runtime opt =   0.01 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 271 instances into 113 LUTs, name keeping = 80%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 168 DFF/LATCH to SEQ ...
SYN-4009 : Pack 6 carry chain into lslice
SYN-4007 : Packing 83 adder to BLE ...
SYN-4008 : Packed 83 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 WARNING: The kept net uart/Macc_data[14] will be merged to another kept net CtrlData/Macc_data[14]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (91 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 915 instances
RUN-0007 : 355 luts, 397 seqs, 88 mslices, 57 lslices, 8 pads, 5 brams, 0 dsps
RUN-1001 : There are total 1131 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 704 nets have 2 pins
RUN-1001 : 298 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 35 nets have [11 - 20] pins
RUN-1001 : 13 nets have [21 - 99] pins
RUN-1001 : 2 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     111     
RUN-1001 :   No   |  No   |  Yes  |     79      
RUN-1001 :   No   |  Yes  |  No   |     71      
RUN-1001 :   Yes  |  No   |  No   |     49      
RUN-1001 :   Yes  |  No   |  Yes  |     87      
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   6   |     10     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 15
PHY-3001 : Initial placement ...
PHY-3001 : design contains 913 instances, 355 luts, 397 seqs, 145 slices, 24 macros(145 instances: 88 mslices 57 lslices)
PHY-0007 : Cell area utilization is 3%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4228, tnet num: 1129, tinst num: 913, tnode num: 5442, tedge num: 6957.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.088587s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (88.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 251549
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 913.
PHY-3001 : End clustering;  0.000054s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 3%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 193202, overlap = 11.25
PHY-3002 : Step(2): len = 160445, overlap = 11.25
PHY-3002 : Step(3): len = 142221, overlap = 11.25
PHY-3002 : Step(4): len = 127472, overlap = 12.625
PHY-3002 : Step(5): len = 113688, overlap = 12
PHY-3002 : Step(6): len = 100657, overlap = 11.25
PHY-3002 : Step(7): len = 85810.6, overlap = 11.25
PHY-3002 : Step(8): len = 76862.5, overlap = 11.6875
PHY-3002 : Step(9): len = 68084.2, overlap = 11.5
PHY-3002 : Step(10): len = 62258, overlap = 11.25
PHY-3002 : Step(11): len = 58087.4, overlap = 6.75
PHY-3002 : Step(12): len = 52951, overlap = 4.5
PHY-3002 : Step(13): len = 47938.2, overlap = 6.75
PHY-3002 : Step(14): len = 44786.5, overlap = 6.75
PHY-3002 : Step(15): len = 42724.5, overlap = 2.25
PHY-3002 : Step(16): len = 39803.9, overlap = 0
PHY-3002 : Step(17): len = 38118.9, overlap = 6.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000282284
PHY-3002 : Step(18): len = 37534.3, overlap = 9
PHY-3002 : Step(19): len = 35605.7, overlap = 6.75
PHY-3002 : Step(20): len = 34752.8, overlap = 6.75
PHY-3002 : Step(21): len = 32480.6, overlap = 4.5
PHY-3002 : Step(22): len = 31464.5, overlap = 9
PHY-3002 : Step(23): len = 31128, overlap = 4.5
PHY-3002 : Step(24): len = 29674.4, overlap = 4.625
PHY-3002 : Step(25): len = 28746.5, overlap = 6.875
PHY-3002 : Step(26): len = 27507.9, overlap = 6.875
PHY-3002 : Step(27): len = 26672.9, overlap = 4.6875
PHY-3002 : Step(28): len = 25757.9, overlap = 4.6875
PHY-3002 : Step(29): len = 25286.4, overlap = 7.0625
PHY-3002 : Step(30): len = 25031.2, overlap = 9.3125
PHY-3002 : Step(31): len = 25039.1, overlap = 9.3125
PHY-3002 : Step(32): len = 24593.8, overlap = 4.75
PHY-3002 : Step(33): len = 24538.9, overlap = 4.75
PHY-3002 : Step(34): len = 24351.2, overlap = 4.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000564567
PHY-3002 : Step(35): len = 24330, overlap = 4.75
PHY-3002 : Step(36): len = 24182.9, overlap = 4.8125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005578s wall, 0.000000s user + 0.046875s system = 0.046875s CPU (840.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.022947s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (136.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(37): len = 24162.2, overlap = 3.78125
PHY-3002 : Step(38): len = 24258.4, overlap = 3.78125
PHY-3002 : Step(39): len = 23951, overlap = 3.71875
PHY-3002 : Step(40): len = 23924.1, overlap = 3.71875
PHY-3002 : Step(41): len = 23992.5, overlap = 3.65625
PHY-3002 : Step(42): len = 23511.6, overlap = 3
PHY-3002 : Step(43): len = 23518.5, overlap = 2.875
PHY-3002 : Step(44): len = 23508.1, overlap = 2.71875
PHY-3002 : Step(45): len = 23313.3, overlap = 2.0625
PHY-3002 : Step(46): len = 23220.5, overlap = 1.71875
PHY-3002 : Step(47): len = 23025.5, overlap = 2.09375
PHY-3002 : Step(48): len = 23038.2, overlap = 0.9375
PHY-3002 : Step(49): len = 22794.9, overlap = 0.9375
PHY-3002 : Step(50): len = 22794.5, overlap = 1.65625
PHY-3002 : Step(51): len = 22418.7, overlap = 2
PHY-3002 : Step(52): len = 22451.6, overlap = 3.75
PHY-3002 : Step(53): len = 22022.2, overlap = 7.875
PHY-3002 : Step(54): len = 21842.6, overlap = 10.7188
PHY-3002 : Step(55): len = 21798.3, overlap = 10.8438
PHY-3002 : Step(56): len = 21467.9, overlap = 12.625
PHY-3002 : Step(57): len = 21199.1, overlap = 12.9375
PHY-3002 : Step(58): len = 21092.6, overlap = 12.9062
PHY-3002 : Step(59): len = 20935.7, overlap = 12.125
PHY-3002 : Step(60): len = 20834.5, overlap = 10.2812
PHY-3002 : Step(61): len = 20577.2, overlap = 8.6875
PHY-3002 : Step(62): len = 20470.4, overlap = 7.125
PHY-3002 : Step(63): len = 20273, overlap = 5.40625
PHY-3002 : Step(64): len = 20162.8, overlap = 5.625
PHY-3002 : Step(65): len = 20178.6, overlap = 7.0625
PHY-3002 : Step(66): len = 20054.3, overlap = 8.46875
PHY-3002 : Step(67): len = 20006.5, overlap = 9
PHY-3002 : Step(68): len = 19806.3, overlap = 9.96875
PHY-3002 : Step(69): len = 19839.9, overlap = 10.375
PHY-3002 : Step(70): len = 19705.6, overlap = 10.7812
PHY-3002 : Step(71): len = 19750.2, overlap = 11.4688
PHY-3002 : Step(72): len = 19391.5, overlap = 11.6562
PHY-3002 : Step(73): len = 19321.5, overlap = 11.6562
PHY-3002 : Step(74): len = 19140.8, overlap = 12.6875
PHY-3002 : Step(75): len = 19097, overlap = 12.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00856723
PHY-3002 : Step(76): len = 19001.5, overlap = 11.8125
PHY-3002 : Step(77): len = 19001.5, overlap = 11.8125
PHY-3002 : Step(78): len = 18943.4, overlap = 11.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.022249s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (70.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.48853e-05
PHY-3002 : Step(79): len = 21181.2, overlap = 30.0625
PHY-3002 : Step(80): len = 21722.4, overlap = 28.2812
PHY-3002 : Step(81): len = 21482.2, overlap = 28.6875
PHY-3002 : Step(82): len = 21518.8, overlap = 28.625
PHY-3002 : Step(83): len = 21667.3, overlap = 28.0938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000149771
PHY-3002 : Step(84): len = 21457.6, overlap = 28.4688
PHY-3002 : Step(85): len = 21578.2, overlap = 27.8438
PHY-3002 : Step(86): len = 21785.3, overlap = 27.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000299541
PHY-3002 : Step(87): len = 21805.5, overlap = 27.625
PHY-3002 : Step(88): len = 21938, overlap = 28.0938
PHY-3002 : Step(89): len = 22253.7, overlap = 28.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000599082
PHY-3002 : Step(90): len = 22191.8, overlap = 25.5938
PHY-3002 : Step(91): len = 22290.8, overlap = 25.6562
PHY-3002 : Step(92): len = 22428.2, overlap = 25.1875
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00119816
PHY-3002 : Step(93): len = 22695.9, overlap = 22.7188
PHY-3002 : Step(94): len = 22895.1, overlap = 22.0312
PHY-3002 : Step(95): len = 23209.5, overlap = 20.8125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4228, tnet num: 1129, tinst num: 913, tnode num: 5442, tedge num: 6957.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 55.44 peak overflow 3.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1131.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 25008, over cnt = 106(0%), over = 506, worst = 16
PHY-1001 : End global iterations;  0.042075s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (222.8%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 14.47, top10 = 8.34, top15 = 5.75.
PHY-1001 : End incremental global routing;  0.089771s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (156.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.027312s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (114.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.132419s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (141.6%)

OPT-1001 : Current memory(MB): used = 180, reserve = 133, peak = 180.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 773/1131.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 25008, over cnt = 106(0%), over = 506, worst = 16
PHY-1002 : len = 29592, over cnt = 70(0%), over = 98, worst = 8
PHY-1002 : len = 30480, over cnt = 19(0%), over = 32, worst = 4
PHY-1002 : len = 30504, over cnt = 9(0%), over = 11, worst = 3
PHY-1002 : len = 30712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.060160s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.9%)

PHY-1001 : Congestion index: top1 = 29.05, top5 = 16.07, top10 = 9.69, top15 = 6.81.
OPT-1001 : End congestion update;  0.099145s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1129 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.021421s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (145.9%)

OPT-0007 : Start: WNS 4043 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.120716s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.5%)

OPT-1001 : Current memory(MB): used = 181, reserve = 135, peak = 181.
OPT-1001 : End physical optimization;  0.341222s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (114.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 355 LUT to BLE ...
SYN-4008 : Packed 355 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 226 remaining SEQ's ...
SYN-4005 : Packed 107 SEQ with LUT/SLICE
SYN-4006 : 99 single LUT's are left
SYN-4006 : 119 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 474/769 primitive instances ...
PHY-3001 : End packing;  0.025042s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (124.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 431 instances
RUN-1001 : 206 mslices, 207 lslices, 8 pads, 5 brams, 0 dsps
RUN-1001 : There are total 962 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 532 nets have 2 pins
RUN-1001 : 300 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 30 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 2 nets have 100+ pins
PHY-3001 : design contains 429 instances, 413 slices, 24 macros(145 instances: 88 mslices 57 lslices)
PHY-3001 : Cell area utilization is 5%
PHY-3001 : After packing: Len = 23142.6, Over = 25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 3627, tnet num: 960, tinst num: 429, tnode num: 4536, tedge num: 6256.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.105915s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (88.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000102732
PHY-3002 : Step(96): len = 22715.4, overlap = 26.75
PHY-3002 : Step(97): len = 22580, overlap = 29
PHY-3002 : Step(98): len = 22302.6, overlap = 28.75
PHY-3002 : Step(99): len = 22158.8, overlap = 30
PHY-3002 : Step(100): len = 22040.5, overlap = 29.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000205463
PHY-3002 : Step(101): len = 21791.9, overlap = 29.75
PHY-3002 : Step(102): len = 21824.9, overlap = 30.5
PHY-3002 : Step(103): len = 21851.6, overlap = 29.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000410926
PHY-3002 : Step(104): len = 22087.1, overlap = 27.5
PHY-3002 : Step(105): len = 22261.5, overlap = 25.75
PHY-3002 : Step(106): len = 22424.6, overlap = 24
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.048752s wall, 0.046875s user + 0.078125s system = 0.125000s CPU (256.4%)

PHY-3001 : Trial Legalized: Len = 31103.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.019507s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (80.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(107): len = 27730.4, overlap = 5.5
PHY-3002 : Step(108): len = 25621.3, overlap = 10.25
PHY-3002 : Step(109): len = 24008.9, overlap = 12.25
PHY-3002 : Step(110): len = 23193.2, overlap = 15.5
PHY-3002 : Step(111): len = 22804.8, overlap = 19
PHY-3002 : Step(112): len = 22786.8, overlap = 19.25
PHY-3002 : Step(113): len = 22525.8, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000240743
PHY-3002 : Step(114): len = 22442.5, overlap = 20.25
PHY-3002 : Step(115): len = 22482, overlap = 19.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000481486
PHY-3002 : Step(116): len = 22437.6, overlap = 18.5
PHY-3002 : Step(117): len = 22498.4, overlap = 17.75
PHY-3002 : Step(118): len = 22507.5, overlap = 16.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006130s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 26493.4, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.003440s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (454.1%)

PHY-3001 : 6 instances has been re-located, deltaX = 2, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 26531.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 3627, tnet num: 960, tinst num: 429, tnode num: 4536, tedge num: 6256.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 38/962.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 30160, over cnt = 90(0%), over = 145, worst = 4
PHY-1002 : len = 31112, over cnt = 39(0%), over = 43, worst = 3
PHY-1002 : len = 31536, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 31568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.073396s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (191.6%)

PHY-1001 : Congestion index: top1 = 25.50, top5 = 16.16, top10 = 10.16, top15 = 7.18.
PHY-1001 : End incremental global routing;  0.117241s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (159.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.022978s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (136.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.153508s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (152.7%)

OPT-1001 : Current memory(MB): used = 184, reserve = 138, peak = 185.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 806/962.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 31568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003014s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 25.50, top5 = 16.16, top10 = 10.16, top15 = 7.18.
OPT-1001 : End congestion update;  0.042991s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (109.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.019085s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (81.9%)

OPT-0007 : Start: WNS 3851 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.062222s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.4%)

OPT-1001 : Current memory(MB): used = 184, reserve = 138, peak = 185.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.018062s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (86.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 806/962.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 31568, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003080s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (507.4%)

PHY-1001 : Congestion index: top1 = 25.50, top5 = 16.16, top10 = 10.16, top15 = 7.18.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.018569s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (84.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3851 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 25.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3851ps with logic level 6 
OPT-1001 : End physical optimization;  0.396592s wall, 0.437500s user + 0.031250s system = 0.468750s CPU (118.2%)

RUN-1003 : finish command "place" in  2.854376s wall, 4.390625s user + 2.015625s system = 6.406250s CPU (224.4%)

RUN-1004 : used memory is 175 MB, reserved memory is 129 MB, peak memory is 185 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 431 instances
RUN-1001 : 206 mslices, 207 lslices, 8 pads, 5 brams, 0 dsps
RUN-1001 : There are total 962 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 532 nets have 2 pins
RUN-1001 : 300 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 30 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 2 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 3627, tnet num: 960, tinst num: 429, tnode num: 4536, tedge num: 6256.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 206 mslices, 207 lslices, 8 pads, 5 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 29752, over cnt = 95(0%), over = 148, worst = 4
PHY-1002 : len = 30840, over cnt = 38(0%), over = 40, worst = 2
PHY-1002 : len = 31280, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 31328, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.076382s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (143.2%)

PHY-1001 : Congestion index: top1 = 25.19, top5 = 15.97, top10 = 10.04, top15 = 7.07.
PHY-1001 : End global routing;  0.120213s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (130.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 203, reserve = 157, peak = 203.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 463, reserve = 422, peak = 463.
PHY-1001 : End build detailed router design. 3.174971s wall, 3.140625s user + 0.015625s system = 3.156250s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 20816, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.629411s wall, 0.578125s user + 0.046875s system = 0.625000s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 495, reserve = 455, peak = 495.
PHY-1001 : End phase 1; 0.636365s wall, 0.593750s user + 0.046875s system = 0.640625s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 85% nets.
PHY-1022 : len = 94152, over cnt = 27(0%), over = 28, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 495, reserve = 455, peak = 495.
PHY-1001 : End initial routed; 0.664489s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (134.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/825(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.925   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.169674s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.3%)

PHY-1001 : Current memory(MB): used = 497, reserve = 456, peak = 497.
PHY-1001 : End phase 2; 0.834269s wall, 1.046875s user + 0.015625s system = 1.062500s CPU (127.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 94152, over cnt = 27(0%), over = 28, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.008580s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (182.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 94024, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.020985s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 94096, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.020879s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (149.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 94144, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.014279s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/825(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.925   |   0.000   |   0   
RUN-1001 :   Hold   |   0.112   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.131979s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.094948s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 510, reserve = 469, peak = 510.
PHY-1001 : End phase 3; 0.405432s wall, 0.406250s user + 0.015625s system = 0.421875s CPU (104.1%)

PHY-1003 : Routed, final wirelength = 94144
PHY-1001 : Current memory(MB): used = 510, reserve = 469, peak = 510.
PHY-1001 : End export database. 0.008182s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  5.233275s wall, 5.359375s user + 0.109375s system = 5.468750s CPU (104.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 3627, tnet num: 960, tinst num: 429, tnode num: 4536, tedge num: 6256.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  5.640754s wall, 5.781250s user + 0.125000s system = 5.906250s CPU (104.7%)

RUN-1004 : used memory is 467 MB, reserved memory is 425 MB, peak memory is 510 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      656   out of  19600    3.35%
#reg                      401   out of  19600    2.05%
#le                       775
  #lut only               374   out of    775   48.26%
  #reg only               119   out of    775   15.35%
  #lut&reg                282   out of    775   36.39%
#dsp                        0   out of     29    0.00%
#bram                       5   out of     64    7.81%
  #bram9k                   5
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     3
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    198
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         57
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       NONE    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+---------------------------------------------------------------------------------------------------------+
|Instance                            |Module         |le     |lut     |ripple  |seq     |bram    |dsp     |
+---------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B     |775    |511     |145     |406     |5       |0       |
|  CLK120                            |global_clock   |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData       |90     |72      |8       |60      |0       |0       |
|    usms                            |Time_1ms       |27     |12      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER     |201    |119     |21      |129     |0       |0       |
|  wendu                             |DS18B20        |169    |126     |43      |43      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER |313    |192     |73      |168     |0       |0       |
|    wrapper_cwc_top                 |cwc_top        |313    |192     |73      |168     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int    |89     |44      |0       |83      |0       |0       |
|        reg_inst                    |register       |87     |42      |0       |81      |0       |0       |
|        tap_inst                    |tap            |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger        |224    |148     |73      |85      |0       |0       |
|        bus_inst                    |bus_top        |20     |12      |8       |4       |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det        |20     |12      |8       |4       |0       |0       |
|        emb_ctrl_inst               |emb_ctrl       |118    |85      |33      |55      |0       |0       |
+---------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout    Nets  
    #1         1       523   
    #2         2       214   
    #3         3        67   
    #4         4        19   
    #5        5-10      90   
    #6       11-50      29   
    #7       51-100     1    
  Average     2.53           

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 3627, tnet num: 960, tinst num: 429, tnode num: 4536, tedge num: 6256.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 960 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 8e6a1bb32a356f67c6637396d09e332ef10525b7f24b5c8fe4b4aabd5c3a66eb -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 429
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 962, pip num: 7991
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 879 valid insts, and 23527 bits set as '1'.
BIT-1004 : the usercode register value: 00000000111001111101110111110100
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.131756s wall, 10.093750s user + 0.140625s system = 10.234375s CPU (480.1%)

RUN-1004 : used memory is 480 MB, reserved memory is 440 MB, peak memory is 625 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_102435.log"
