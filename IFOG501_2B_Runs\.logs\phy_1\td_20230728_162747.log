============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 28 16:27:48 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1636 instances
RUN-0007 : 373 luts, 1008 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2174 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1616 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1634 instances, 373 luts, 1008 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7761, tnet num: 2172, tinst num: 1634, tnode num: 11017, tedge num: 13121.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2172 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.291905s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (101.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 631717
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1634.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 528202, overlap = 20.25
PHY-3002 : Step(2): len = 490430, overlap = 20.25
PHY-3002 : Step(3): len = 404349, overlap = 13.5
PHY-3002 : Step(4): len = 342087, overlap = 15.75
PHY-3002 : Step(5): len = 311095, overlap = 15.75
PHY-3002 : Step(6): len = 295925, overlap = 18
PHY-3002 : Step(7): len = 287176, overlap = 20.25
PHY-3002 : Step(8): len = 278318, overlap = 20.25
PHY-3002 : Step(9): len = 272037, overlap = 20.25
PHY-3002 : Step(10): len = 261253, overlap = 18
PHY-3002 : Step(11): len = 254678, overlap = 18
PHY-3002 : Step(12): len = 248995, overlap = 18
PHY-3002 : Step(13): len = 242240, overlap = 18
PHY-3002 : Step(14): len = 235307, overlap = 18
PHY-3002 : Step(15): len = 231502, overlap = 18
PHY-3002 : Step(16): len = 224474, overlap = 18
PHY-3002 : Step(17): len = 218115, overlap = 18
PHY-3002 : Step(18): len = 214310, overlap = 18
PHY-3002 : Step(19): len = 210575, overlap = 18
PHY-3002 : Step(20): len = 200563, overlap = 18
PHY-3002 : Step(21): len = 195041, overlap = 18
PHY-3002 : Step(22): len = 192954, overlap = 18
PHY-3002 : Step(23): len = 186896, overlap = 18
PHY-3002 : Step(24): len = 168552, overlap = 18
PHY-3002 : Step(25): len = 164579, overlap = 18
PHY-3002 : Step(26): len = 163074, overlap = 18
PHY-3002 : Step(27): len = 135380, overlap = 18
PHY-3002 : Step(28): len = 128912, overlap = 18
PHY-3002 : Step(29): len = 127400, overlap = 18
PHY-3002 : Step(30): len = 122954, overlap = 18
PHY-3002 : Step(31): len = 121686, overlap = 18
PHY-3002 : Step(32): len = 118029, overlap = 18
PHY-3002 : Step(33): len = 116114, overlap = 18
PHY-3002 : Step(34): len = 112863, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.11202e-05
PHY-3002 : Step(35): len = 115231, overlap = 15.75
PHY-3002 : Step(36): len = 115472, overlap = 11.25
PHY-3002 : Step(37): len = 113311, overlap = 18
PHY-3002 : Step(38): len = 111444, overlap = 18
PHY-3002 : Step(39): len = 109579, overlap = 18
PHY-3002 : Step(40): len = 109104, overlap = 11.25
PHY-3002 : Step(41): len = 105800, overlap = 11.25
PHY-3002 : Step(42): len = 103085, overlap = 13.5
PHY-3002 : Step(43): len = 101718, overlap = 13.5
PHY-3002 : Step(44): len = 100397, overlap = 6.75
PHY-3002 : Step(45): len = 93578.7, overlap = 13.5
PHY-3002 : Step(46): len = 91881.1, overlap = 11.25
PHY-3002 : Step(47): len = 90427.3, overlap = 13.5
PHY-3002 : Step(48): len = 89536.6, overlap = 13.5
PHY-3002 : Step(49): len = 87987.9, overlap = 11.25
PHY-3002 : Step(50): len = 87304.8, overlap = 13.5
PHY-3002 : Step(51): len = 87121.1, overlap = 13.5
PHY-3002 : Step(52): len = 86495.2, overlap = 15.75
PHY-3002 : Step(53): len = 85253.3, overlap = 11.25
PHY-3002 : Step(54): len = 84877.5, overlap = 13.5
PHY-3002 : Step(55): len = 83743, overlap = 11.25
PHY-3002 : Step(56): len = 81673.2, overlap = 11.25
PHY-3002 : Step(57): len = 81386.6, overlap = 11.25
PHY-3002 : Step(58): len = 81117.8, overlap = 9
PHY-3002 : Step(59): len = 80654, overlap = 9
PHY-3002 : Step(60): len = 78981.7, overlap = 15.8125
PHY-3002 : Step(61): len = 76066.3, overlap = 11.75
PHY-3002 : Step(62): len = 73345.3, overlap = 12
PHY-3002 : Step(63): len = 73145.1, overlap = 11.9375
PHY-3002 : Step(64): len = 72482.4, overlap = 11.9375
PHY-3002 : Step(65): len = 72129.9, overlap = 12.1875
PHY-3002 : Step(66): len = 71952.4, overlap = 14.4375
PHY-3002 : Step(67): len = 71185.3, overlap = 12.1875
PHY-3002 : Step(68): len = 70068.6, overlap = 12.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00016224
PHY-3002 : Step(69): len = 70638.2, overlap = 12.125
PHY-3002 : Step(70): len = 70781.4, overlap = 9.875
PHY-3002 : Step(71): len = 70540.4, overlap = 9.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000324481
PHY-3002 : Step(72): len = 70763.8, overlap = 10.125
PHY-3002 : Step(73): len = 70781.6, overlap = 10.0625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006734s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2172 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060271s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(74): len = 74059.2, overlap = 6.6875
PHY-3002 : Step(75): len = 72391.6, overlap = 5.78125
PHY-3002 : Step(76): len = 70936.4, overlap = 5.21875
PHY-3002 : Step(77): len = 69425.2, overlap = 4.8125
PHY-3002 : Step(78): len = 68367.5, overlap = 4.6875
PHY-3002 : Step(79): len = 66765.5, overlap = 4.6875
PHY-3002 : Step(80): len = 64641, overlap = 4.5
PHY-3002 : Step(81): len = 63601.1, overlap = 4.3125
PHY-3002 : Step(82): len = 62870.1, overlap = 4.4375
PHY-3002 : Step(83): len = 61735.5, overlap = 4.375
PHY-3002 : Step(84): len = 60181, overlap = 4.5625
PHY-3002 : Step(85): len = 59293.2, overlap = 5.25
PHY-3002 : Step(86): len = 58683, overlap = 5.625
PHY-3002 : Step(87): len = 57848.1, overlap = 6.4375
PHY-3002 : Step(88): len = 57140.7, overlap = 7.4375
PHY-3002 : Step(89): len = 56332.4, overlap = 7.5
PHY-3002 : Step(90): len = 56059.3, overlap = 7
PHY-3002 : Step(91): len = 55012.2, overlap = 7.9375
PHY-3002 : Step(92): len = 53934.5, overlap = 8.25
PHY-3002 : Step(93): len = 53131.3, overlap = 8.3125
PHY-3002 : Step(94): len = 52138.6, overlap = 6.6875
PHY-3002 : Step(95): len = 51401.6, overlap = 9.8125
PHY-3002 : Step(96): len = 51065.2, overlap = 10.25
PHY-3002 : Step(97): len = 50835.8, overlap = 10.6875
PHY-3002 : Step(98): len = 50626.4, overlap = 10.6562
PHY-3002 : Step(99): len = 50291, overlap = 10.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000385289
PHY-3002 : Step(100): len = 50188.1, overlap = 10.5312
PHY-3002 : Step(101): len = 50188.1, overlap = 10.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000770577
PHY-3002 : Step(102): len = 50191.9, overlap = 10.5312
PHY-3002 : Step(103): len = 50211.9, overlap = 10.4688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2172 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.076868s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000101818
PHY-3002 : Step(104): len = 50833.6, overlap = 57.0938
PHY-3002 : Step(105): len = 51202.9, overlap = 55.875
PHY-3002 : Step(106): len = 51127.8, overlap = 54.0312
PHY-3002 : Step(107): len = 51214.6, overlap = 55.0312
PHY-3002 : Step(108): len = 51342.1, overlap = 53.625
PHY-3002 : Step(109): len = 51182.2, overlap = 52.8125
PHY-3002 : Step(110): len = 51218.2, overlap = 51.5938
PHY-3002 : Step(111): len = 51279, overlap = 52.125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000203637
PHY-3002 : Step(112): len = 51581.3, overlap = 52
PHY-3002 : Step(113): len = 51786.4, overlap = 51.1562
PHY-3002 : Step(114): len = 52631.3, overlap = 45.8438
PHY-3002 : Step(115): len = 52710.8, overlap = 46.0312
PHY-3002 : Step(116): len = 52802.6, overlap = 45.25
PHY-3002 : Step(117): len = 52832.4, overlap = 46.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000407273
PHY-3002 : Step(118): len = 53049.9, overlap = 45.9375
PHY-3002 : Step(119): len = 53323, overlap = 44.5
PHY-3002 : Step(120): len = 53532.9, overlap = 42.8125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000814546
PHY-3002 : Step(121): len = 54176.5, overlap = 41.7188
PHY-3002 : Step(122): len = 54398.4, overlap = 41.2188
PHY-3002 : Step(123): len = 54700.1, overlap = 39.5938
PHY-3002 : Step(124): len = 54902, overlap = 37.3438
PHY-3002 : Step(125): len = 54929.1, overlap = 36.9375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00162909
PHY-3002 : Step(126): len = 54865.3, overlap = 36.8125
PHY-3002 : Step(127): len = 54845.2, overlap = 36.4688
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00325819
PHY-3002 : Step(128): len = 54907.8, overlap = 35.875
PHY-3002 : Step(129): len = 54889.8, overlap = 35.1562
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00651637
PHY-3002 : Step(130): len = 55031.1, overlap = 34.5312
PHY-3002 : Step(131): len = 55149.9, overlap = 30.7812
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.0130327
PHY-3002 : Step(132): len = 55043.6, overlap = 30.9375
PHY-3002 : Step(133): len = 54991.3, overlap = 31.2188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7761, tnet num: 2172, tinst num: 1634, tnode num: 11017, tedge num: 13121.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 80.94 peak overflow 2.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2174.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58512, over cnt = 254(0%), over = 1074, worst = 16
PHY-1001 : End global iterations;  0.071950s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (152.0%)

PHY-1001 : Congestion index: top1 = 43.12, top5 = 26.31, top10 = 17.29, top15 = 12.25.
PHY-1001 : End incremental global routing;  0.123556s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (139.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2172 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071309s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.226861s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (124.0%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1701/2174.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58512, over cnt = 254(0%), over = 1074, worst = 16
PHY-1002 : len = 64216, over cnt = 169(0%), over = 392, worst = 13
PHY-1002 : len = 68568, over cnt = 23(0%), over = 27, worst = 3
PHY-1002 : len = 69072, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 69104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.101636s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (138.4%)

PHY-1001 : Congestion index: top1 = 36.57, top5 = 25.78, top10 = 18.72, top15 = 13.85.
OPT-1001 : End congestion update;  0.146851s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (117.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2172 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062025s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.211460s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (110.8%)

OPT-1001 : Current memory(MB): used = 216, reserve = 180, peak = 216.
OPT-1001 : End physical optimization;  0.714601s wall, 0.750000s user + 0.031250s system = 0.781250s CPU (109.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 112 SEQ with LUT/SLICE
SYN-4006 : 103 single LUT's are left
SYN-4006 : 715 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1088/1401 primitive instances ...
PHY-3001 : End packing;  0.051358s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 829 instances
RUN-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2007 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1461 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 827 instances, 780 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55203.6, Over = 57.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6524, tnet num: 2005, tinst num: 827, tnode num: 8879, tedge num: 11466.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2005 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.323332s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.39186e-05
PHY-3002 : Step(134): len = 54287.7, overlap = 58.5
PHY-3002 : Step(135): len = 53995.1, overlap = 61
PHY-3002 : Step(136): len = 53654.6, overlap = 62.75
PHY-3002 : Step(137): len = 53582.4, overlap = 62.25
PHY-3002 : Step(138): len = 53582.6, overlap = 61.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.78373e-05
PHY-3002 : Step(139): len = 53676.8, overlap = 60.75
PHY-3002 : Step(140): len = 54073.7, overlap = 59
PHY-3002 : Step(141): len = 54509.8, overlap = 56.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000135675
PHY-3002 : Step(142): len = 55231.3, overlap = 56.5
PHY-3002 : Step(143): len = 56173.5, overlap = 54
PHY-3002 : Step(144): len = 56546.1, overlap = 50
PHY-3002 : Step(145): len = 56746.2, overlap = 48.75
PHY-3002 : Step(146): len = 56757.7, overlap = 46.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.082685s wall, 0.062500s user + 0.046875s system = 0.109375s CPU (132.3%)

PHY-3001 : Trial Legalized: Len = 69211.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2005 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055290s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000815169
PHY-3002 : Step(147): len = 66943.3, overlap = 3.5
PHY-3002 : Step(148): len = 64892.9, overlap = 12.5
PHY-3002 : Step(149): len = 63551.2, overlap = 16.25
PHY-3002 : Step(150): len = 62429.7, overlap = 21
PHY-3002 : Step(151): len = 61814.1, overlap = 23.25
PHY-3002 : Step(152): len = 61189.2, overlap = 26.25
PHY-3002 : Step(153): len = 60860.9, overlap = 27.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00163034
PHY-3002 : Step(154): len = 61090.8, overlap = 26.75
PHY-3002 : Step(155): len = 61205.4, overlap = 27.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00326067
PHY-3002 : Step(156): len = 61273.2, overlap = 27.5
PHY-3002 : Step(157): len = 61333.4, overlap = 27.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005207s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64856.6, Over = 0
PHY-3001 : Spreading special nets. 10 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005724s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (273.0%)

PHY-3001 : 13 instances has been re-located, deltaX = 2, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 65058.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6524, tnet num: 2005, tinst num: 827, tnode num: 8879, tedge num: 11466.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 147/2007.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72576, over cnt = 142(0%), over = 222, worst = 8
PHY-1002 : len = 73512, over cnt = 76(0%), over = 91, worst = 3
PHY-1002 : len = 74552, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 74776, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 74872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.139789s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (156.5%)

PHY-1001 : Congestion index: top1 = 33.17, top5 = 23.54, top10 = 18.52, top15 = 14.75.
PHY-1001 : End incremental global routing;  0.194072s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (136.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2005 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065373s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.291231s wall, 0.328125s user + 0.046875s system = 0.375000s CPU (128.8%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1797/2007.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006581s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.17, top5 = 23.54, top10 = 18.52, top15 = 14.75.
OPT-1001 : End congestion update;  0.053829s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2005 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055795s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.111644s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.0%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2005 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053023s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1797/2007.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007265s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (215.1%)

PHY-1001 : Congestion index: top1 = 33.17, top5 = 23.54, top10 = 18.52, top15 = 14.75.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2005 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051509s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.881623s wall, 0.906250s user + 0.046875s system = 0.953125s CPU (108.1%)

RUN-1003 : finish command "place" in  5.403048s wall, 8.015625s user + 2.468750s system = 10.484375s CPU (194.0%)

RUN-1004 : used memory is 201 MB, reserved memory is 166 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 829 instances
RUN-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2007 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1461 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6524, tnet num: 2005, tinst num: 827, tnode num: 8879, tedge num: 11466.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2005 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71312, over cnt = 150(0%), over = 239, worst = 8
PHY-1002 : len = 72448, over cnt = 93(0%), over = 115, worst = 3
PHY-1002 : len = 73216, over cnt = 43(0%), over = 55, worst = 3
PHY-1002 : len = 73920, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 74064, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.162787s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (105.6%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 23.30, top10 = 18.29, top15 = 14.61.
PHY-1001 : End global routing;  0.213137s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (110.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 238.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 498, reserve = 467, peak = 498.
PHY-1001 : End build detailed router design. 3.342505s wall, 3.296875s user + 0.031250s system = 3.328125s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34712, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.409170s wall, 1.375000s user + 0.046875s system = 1.421875s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 531, reserve = 500, peak = 532.
PHY-1001 : End phase 1; 1.416019s wall, 1.390625s user + 0.046875s system = 1.437500s CPU (101.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 187744, over cnt = 36(0%), over = 36, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End initial routed; 1.216997s wall, 2.046875s user + 0.046875s system = 2.093750s CPU (172.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1788(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.617   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.382766s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.0%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.599880s wall, 2.421875s user + 0.046875s system = 2.468750s CPU (154.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 187744, over cnt = 36(0%), over = 36, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016207s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (96.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 187600, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026293s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (118.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 187448, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.026341s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (118.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1788(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.617   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.384961s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.185109s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.3%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.767898s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.7%)

PHY-1003 : Routed, final wirelength = 187448
PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End export database. 0.010368s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (150.7%)

PHY-1001 : End detail routing;  7.319339s wall, 8.062500s user + 0.125000s system = 8.187500s CPU (111.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6524, tnet num: 2005, tinst num: 827, tnode num: 8879, tedge num: 11466.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.285751s wall, 9.046875s user + 0.125000s system = 9.171875s CPU (110.7%)

RUN-1004 : used memory is 503 MB, reserved memory is 472 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      788   out of  19600    4.02%
#reg                     1072   out of  19600    5.47%
#le                      1503
  #lut only               431   out of   1503   28.68%
  #reg only               715   out of   1503   47.57%
  #lut&reg                357   out of   1503   23.75%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1503   |582     |206     |1103    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1139   |308     |122     |921     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |35     |28      |7       |19      |0       |0       |
|    demodu                  |Demodulation                                     |524    |112     |53      |435     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |132     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |0       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |31     |18      |0       |31      |0       |0       |
|    integ                   |Integration                                      |136    |16      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |101    |28      |15      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |101     |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |23      |4       |16      |0       |0       |
|  u_uart                    |UART_Control                                     |86     |73      |7       |49      |0       |0       |
|    U0                      |speed_select_Tx                                  |28     |21      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |37     |34      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |80      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1425  
    #2          2       307   
    #3          3       103   
    #4          4        18   
    #5        5-10       79   
    #6        11-50      31   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6524, tnet num: 2005, tinst num: 827, tnode num: 8879, tedge num: 11466.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2005 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 827
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2007, pip num: 15002
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1246 valid insts, and 39286 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.279909s wall, 19.093750s user + 0.031250s system = 19.125000s CPU (583.1%)

RUN-1004 : used memory is 522 MB, reserved memory is 497 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230728_162747.log"
