============================================================
   Tang Dynasty, V5.6.151449
      Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_5.6.5_SP3_151.449/bin/td.exe
   Built at =   17:28:21 May 30 2024
   Run by =     Administrator
   Run Date =   Tue Jul 22 16:18:20 2025

   Run on =     TLH-022
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256_DACC/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net dq_dup_1 will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
USR-1006 : Generating FPGA-IP Autogen constraints ...
RUN-1002 : start command "get_pins config_inst.jtck"
RUN-1002 : start command "create_clock -name jtck -period 100 "
RUN-1102 : create_clock: clock name: jtck, type: 0, period: 100000, rise: 0, fall: 50000.
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_uncertainty -hold 0.1 "
RUN-1002 : start command "get_clocks jtck"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import ChipWatcher
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256_DACC/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  3.934947s wall, 3.828125s user + 0.093750s system = 3.921875s CPU (99.7%)

RUN-1004 : used memory is 524 MB, reserved memory is 597 MB, peak memory is 538 MB
