============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 14:52:51 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1623 instances
RUN-0007 : 368 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2193 nets
RUN-1001 : 1636 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1621 instances, 368 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7774, tnet num: 2191, tinst num: 1621, tnode num: 11014, tedge num: 13150.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.285407s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (104.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 595154
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1621.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 473578, overlap = 20.25
PHY-3002 : Step(2): len = 436677, overlap = 20.25
PHY-3002 : Step(3): len = 392919, overlap = 20.25
PHY-3002 : Step(4): len = 363509, overlap = 18
PHY-3002 : Step(5): len = 353133, overlap = 20.25
PHY-3002 : Step(6): len = 339554, overlap = 20.25
PHY-3002 : Step(7): len = 324190, overlap = 18
PHY-3002 : Step(8): len = 315164, overlap = 18
PHY-3002 : Step(9): len = 309128, overlap = 18
PHY-3002 : Step(10): len = 297627, overlap = 20.25
PHY-3002 : Step(11): len = 289590, overlap = 20.25
PHY-3002 : Step(12): len = 283729, overlap = 20.25
PHY-3002 : Step(13): len = 277067, overlap = 20.25
PHY-3002 : Step(14): len = 267540, overlap = 20.25
PHY-3002 : Step(15): len = 262540, overlap = 20.25
PHY-3002 : Step(16): len = 256963, overlap = 20.25
PHY-3002 : Step(17): len = 249164, overlap = 20.25
PHY-3002 : Step(18): len = 242802, overlap = 20.25
PHY-3002 : Step(19): len = 239370, overlap = 20.25
PHY-3002 : Step(20): len = 231366, overlap = 20.25
PHY-3002 : Step(21): len = 222533, overlap = 20.25
PHY-3002 : Step(22): len = 217898, overlap = 20.25
PHY-3002 : Step(23): len = 215086, overlap = 20.25
PHY-3002 : Step(24): len = 202205, overlap = 20.25
PHY-3002 : Step(25): len = 197354, overlap = 20.25
PHY-3002 : Step(26): len = 194883, overlap = 20.25
PHY-3002 : Step(27): len = 181269, overlap = 20.25
PHY-3002 : Step(28): len = 165473, overlap = 20.25
PHY-3002 : Step(29): len = 164313, overlap = 20.25
PHY-3002 : Step(30): len = 155918, overlap = 20.25
PHY-3002 : Step(31): len = 133014, overlap = 15.75
PHY-3002 : Step(32): len = 128433, overlap = 20.25
PHY-3002 : Step(33): len = 124811, overlap = 20.25
PHY-3002 : Step(34): len = 122761, overlap = 20.25
PHY-3002 : Step(35): len = 120341, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000118484
PHY-3002 : Step(36): len = 121178, overlap = 13.5
PHY-3002 : Step(37): len = 120418, overlap = 13.5
PHY-3002 : Step(38): len = 119073, overlap = 15.75
PHY-3002 : Step(39): len = 118112, overlap = 15.75
PHY-3002 : Step(40): len = 115739, overlap = 13.5
PHY-3002 : Step(41): len = 114033, overlap = 13.5
PHY-3002 : Step(42): len = 111672, overlap = 15.75
PHY-3002 : Step(43): len = 107338, overlap = 11.25
PHY-3002 : Step(44): len = 103782, overlap = 9
PHY-3002 : Step(45): len = 103037, overlap = 11.25
PHY-3002 : Step(46): len = 99715.8, overlap = 15.75
PHY-3002 : Step(47): len = 94563.3, overlap = 15.75
PHY-3002 : Step(48): len = 92623.4, overlap = 11.25
PHY-3002 : Step(49): len = 91950.8, overlap = 11.25
PHY-3002 : Step(50): len = 90736.5, overlap = 11.25
PHY-3002 : Step(51): len = 89634.2, overlap = 13.5
PHY-3002 : Step(52): len = 88110, overlap = 13.5
PHY-3002 : Step(53): len = 87481.6, overlap = 13.5
PHY-3002 : Step(54): len = 85799.9, overlap = 9
PHY-3002 : Step(55): len = 85038.8, overlap = 4.5
PHY-3002 : Step(56): len = 83245, overlap = 6.75
PHY-3002 : Step(57): len = 82218.4, overlap = 13.5
PHY-3002 : Step(58): len = 80683.3, overlap = 13.5
PHY-3002 : Step(59): len = 79503.3, overlap = 13.5
PHY-3002 : Step(60): len = 77267.5, overlap = 11.25
PHY-3002 : Step(61): len = 76240.2, overlap = 13.5
PHY-3002 : Step(62): len = 74914.5, overlap = 13.5
PHY-3002 : Step(63): len = 74332.7, overlap = 11.25
PHY-3002 : Step(64): len = 70834.1, overlap = 13.6875
PHY-3002 : Step(65): len = 68960.7, overlap = 13.9375
PHY-3002 : Step(66): len = 67616.7, overlap = 14
PHY-3002 : Step(67): len = 66523.4, overlap = 14.125
PHY-3002 : Step(68): len = 65666.1, overlap = 11.875
PHY-3002 : Step(69): len = 65003.6, overlap = 14.125
PHY-3002 : Step(70): len = 64759.7, overlap = 11.9375
PHY-3002 : Step(71): len = 64340.9, overlap = 11.8125
PHY-3002 : Step(72): len = 64043.6, overlap = 11.875
PHY-3002 : Step(73): len = 63496, overlap = 14.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000236969
PHY-3002 : Step(74): len = 63650.7, overlap = 12.125
PHY-3002 : Step(75): len = 63654.4, overlap = 14.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000473937
PHY-3002 : Step(76): len = 63713.8, overlap = 14.375
PHY-3002 : Step(77): len = 63683.6, overlap = 11.9375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005182s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (301.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059502s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00428464
PHY-3002 : Step(78): len = 65901.2, overlap = 8.625
PHY-3002 : Step(79): len = 64312.3, overlap = 9.9375
PHY-3002 : Step(80): len = 62970.4, overlap = 9.125
PHY-3002 : Step(81): len = 61178.1, overlap = 10.7188
PHY-3002 : Step(82): len = 59941.3, overlap = 10.9062
PHY-3002 : Step(83): len = 58355.2, overlap = 11.625
PHY-3002 : Step(84): len = 56670.1, overlap = 11.8125
PHY-3002 : Step(85): len = 55306.6, overlap = 12.875
PHY-3002 : Step(86): len = 53806.5, overlap = 13.1562
PHY-3002 : Step(87): len = 52477.9, overlap = 14.25
PHY-3002 : Step(88): len = 51106.3, overlap = 15.4688
PHY-3002 : Step(89): len = 50451.5, overlap = 15.6562
PHY-3002 : Step(90): len = 49944.8, overlap = 15.9375
PHY-3002 : Step(91): len = 49186.4, overlap = 15.7188
PHY-3002 : Step(92): len = 48355.8, overlap = 17
PHY-3002 : Step(93): len = 48269.8, overlap = 17.9062
PHY-3002 : Step(94): len = 48220.3, overlap = 18.7812
PHY-3002 : Step(95): len = 47950.2, overlap = 18.6875
PHY-3002 : Step(96): len = 47522.5, overlap = 19.625
PHY-3002 : Step(97): len = 47479.6, overlap = 19.8438
PHY-3002 : Step(98): len = 47283.1, overlap = 20.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00856928
PHY-3002 : Step(99): len = 47367.5, overlap = 20.2812
PHY-3002 : Step(100): len = 47292.9, overlap = 21.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064657s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000119685
PHY-3002 : Step(101): len = 47637.6, overlap = 54.2812
PHY-3002 : Step(102): len = 47732.4, overlap = 56.9062
PHY-3002 : Step(103): len = 48889.8, overlap = 46.5938
PHY-3002 : Step(104): len = 49478.7, overlap = 45.2188
PHY-3002 : Step(105): len = 49453, overlap = 44.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000239371
PHY-3002 : Step(106): len = 49469.5, overlap = 43.9062
PHY-3002 : Step(107): len = 50051.6, overlap = 38.0625
PHY-3002 : Step(108): len = 50282.4, overlap = 33.2812
PHY-3002 : Step(109): len = 50185.2, overlap = 33.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000478741
PHY-3002 : Step(110): len = 50553.7, overlap = 31.0312
PHY-3002 : Step(111): len = 50734.1, overlap = 30.7188
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7774, tnet num: 2191, tinst num: 1621, tnode num: 11014, tedge num: 13150.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.38 peak overflow 3.19
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2193.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53480, over cnt = 228(0%), over = 898, worst = 18
PHY-1001 : End global iterations;  0.066708s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (140.5%)

PHY-1001 : Congestion index: top1 = 40.88, top5 = 24.29, top10 = 15.18, top15 = 10.72.
PHY-1001 : End incremental global routing;  0.116298s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (120.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071260s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.217931s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (107.5%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1678/2193.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53480, over cnt = 228(0%), over = 898, worst = 18
PHY-1002 : len = 59800, over cnt = 160(0%), over = 334, worst = 9
PHY-1002 : len = 63128, over cnt = 40(0%), over = 66, worst = 6
PHY-1002 : len = 64056, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 64224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.076009s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (123.3%)

PHY-1001 : Congestion index: top1 = 36.10, top5 = 24.59, top10 = 16.88, top15 = 12.30.
OPT-1001 : End congestion update;  0.125738s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (124.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055814s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.184007s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (110.4%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.674631s wall, 0.703125s user + 0.031250s system = 0.734375s CPU (108.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 709 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1077/1405 primitive instances ...
PHY-3001 : End packing;  0.051455s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 846 instances
RUN-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2025 nets
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 844 instances, 797 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50584.4, Over = 62
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2023, tinst num: 844, tnode num: 8932, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.310389s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.29899e-05
PHY-3002 : Step(112): len = 50177.7, overlap = 64
PHY-3002 : Step(113): len = 50016.2, overlap = 63.5
PHY-3002 : Step(114): len = 49639.5, overlap = 65.75
PHY-3002 : Step(115): len = 49717, overlap = 66
PHY-3002 : Step(116): len = 49728.3, overlap = 66
PHY-3002 : Step(117): len = 49812.2, overlap = 66.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.59799e-05
PHY-3002 : Step(118): len = 49903.3, overlap = 65.75
PHY-3002 : Step(119): len = 50060.7, overlap = 64.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.43955e-05
PHY-3002 : Step(120): len = 50618, overlap = 62
PHY-3002 : Step(121): len = 52069.7, overlap = 54.75
PHY-3002 : Step(122): len = 53211.2, overlap = 52.75
PHY-3002 : Step(123): len = 53257.6, overlap = 53
PHY-3002 : Step(124): len = 53300.2, overlap = 52.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.070909s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (242.4%)

PHY-3001 : Trial Legalized: Len = 66782.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052378s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000468507
PHY-3002 : Step(125): len = 64256.5, overlap = 8
PHY-3002 : Step(126): len = 62452.1, overlap = 11.75
PHY-3002 : Step(127): len = 61024.5, overlap = 13.75
PHY-3002 : Step(128): len = 60056.7, overlap = 13.25
PHY-3002 : Step(129): len = 59370.6, overlap = 18.5
PHY-3002 : Step(130): len = 58813.5, overlap = 21.25
PHY-3002 : Step(131): len = 58507.8, overlap = 24.5
PHY-3002 : Step(132): len = 58254.6, overlap = 25.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000937013
PHY-3002 : Step(133): len = 58690.1, overlap = 24.5
PHY-3002 : Step(134): len = 58868.1, overlap = 24
PHY-3002 : Step(135): len = 58785.8, overlap = 24
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00187403
PHY-3002 : Step(136): len = 59006.7, overlap = 24
PHY-3002 : Step(137): len = 59006.7, overlap = 24
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004914s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (317.9%)

PHY-3001 : Legalized: Len = 63722.7, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005875s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 2, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 63850.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2023, tinst num: 844, tnode num: 8932, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 83/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69784, over cnt = 122(0%), over = 189, worst = 9
PHY-1002 : len = 70816, over cnt = 61(0%), over = 67, worst = 3
PHY-1002 : len = 71088, over cnt = 45(0%), over = 48, worst = 2
PHY-1002 : len = 71776, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118785s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (131.5%)

PHY-1001 : Congestion index: top1 = 30.73, top5 = 22.59, top10 = 17.39, top15 = 13.62.
PHY-1001 : End incremental global routing;  0.169097s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (129.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059069s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.257833s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (121.2%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1791/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005915s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.73, top5 = 22.59, top10 = 17.39, top15 = 13.62.
OPT-1001 : End congestion update;  0.052235s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056664s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 806 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 844 instances, 797 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63899.4, Over = 0
PHY-3001 : End spreading;  0.005077s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63899.4, Over = 0
PHY-3001 : End incremental legalization;  0.035055s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.1%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 0 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.156590s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (169.6%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055789s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1787/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007499s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (625.1%)

PHY-1001 : Congestion index: top1 = 30.73, top5 = 22.61, top10 = 17.40, top15 = 13.62.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059969s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.884238s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (121.9%)

RUN-1003 : finish command "place" in  4.976478s wall, 6.921875s user + 2.218750s system = 9.140625s CPU (183.7%)

RUN-1004 : used memory is 201 MB, reserved memory is 164 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 846 instances
RUN-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2025 nets
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2023, tinst num: 844, tnode num: 8932, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69104, over cnt = 126(0%), over = 198, worst = 8
PHY-1002 : len = 70008, over cnt = 81(0%), over = 99, worst = 4
PHY-1002 : len = 71008, over cnt = 19(0%), over = 24, worst = 4
PHY-1002 : len = 71344, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109368s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (142.9%)

PHY-1001 : Congestion index: top1 = 30.47, top5 = 22.43, top10 = 17.31, top15 = 13.54.
PHY-1001 : End global routing;  0.159446s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (127.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 204, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 499, reserve = 467, peak = 499.
PHY-1001 : End build detailed router design. 3.205675s wall, 3.156250s user + 0.046875s system = 3.203125s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33200, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.241637s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 531, reserve = 500, peak = 531.
PHY-1001 : End phase 1; 1.247740s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183496, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 501, peak = 534.
PHY-1001 : End initial routed; 1.033451s wall, 1.890625s user + 0.062500s system = 1.953125s CPU (189.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.439   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.380007s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (94.6%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 1.413545s wall, 2.250000s user + 0.062500s system = 2.312500s CPU (163.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183496, over cnt = 33(0%), over = 33, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014238s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (109.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 183232, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031554s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (198.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 183328, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.024214s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (129.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.439   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.376709s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.176681s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.3%)

PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End phase 3; 0.746190s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (102.6%)

PHY-1003 : Routed, final wirelength = 183328
PHY-1001 : Current memory(MB): used = 552, reserve = 521, peak = 552.
PHY-1001 : End export database. 0.010019s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (156.0%)

PHY-1001 : End detail routing;  6.809271s wall, 7.593750s user + 0.140625s system = 7.734375s CPU (113.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2023, tinst num: 844, tnode num: 8932, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.710860s wall, 8.500000s user + 0.171875s system = 8.671875s CPU (112.5%)

RUN-1004 : used memory is 504 MB, reserved memory is 475 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      816   out of  19600    4.16%
#reg                     1074   out of  19600    5.48%
#le                      1525
  #lut only               451   out of   1525   29.57%
  #reg only               709   out of   1525   46.49%
  #lut&reg                365   out of   1525   23.93%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    45
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1525   |595     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1121   |292     |128     |925     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |28     |22      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |524    |117     |53      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |0       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |15      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |136    |17      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |92     |30      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |83      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |108    |93      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |19     |15      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |53     |50      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1435  
    #2          2       317   
    #3          3       107   
    #4          4        14   
    #5        5-10       80   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6570, tnet num: 2023, tinst num: 844, tnode num: 8932, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 844
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2025, pip num: 14680
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1349 valid insts, and 38977 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.153391s wall, 18.578125s user + 0.078125s system = 18.656250s CPU (591.6%)

RUN-1004 : used memory is 521 MB, reserved memory is 490 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_145250.log"
