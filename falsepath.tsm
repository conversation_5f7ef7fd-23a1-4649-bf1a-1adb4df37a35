eagle_s20
13 0 0 0 0 0 0 0
0.000 0.000 IFOG501_2B eagle_s20 EG4S20NG88 Detail NA 9 2
False path: set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]
13 7 7 1
hold check
21 3
Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
22 1152381626143224020410402429463047381090789577275568020190173295804416.000000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
24 1000.000000 0.000000 0.000000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[0] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30.mi[1]


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
41 0.000000 1 1
Timing path: signal_process/demodu/fifo/ram_inst/reg2_syn_173.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
signal_process/demodu/fifo/ram_inst/reg2_syn_173.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
43 1000.000000 0.000000 0.000000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[3] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30.mi[0]


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
60 0.000000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
62 1000.000000 0.000000 0.000000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[2] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27.mi[1]




False path: set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]
79 7 7 1
hold check
87 3
Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
88 0.000000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
90 1000.000000 0.000000 0.000000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[0] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[1]


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
107 0.000000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_42.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_42.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
109 1000.000000 0.000000 0.000000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[2] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[0]


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
126 0.000000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
128 1000.000000 0.000000 0.000000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[1] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.mi[1]





Timing group statistics: 

	Exceptions:

		Check Type:	MIN
		----------------------------------------------------------------------------------------------------
		       Path Num     Constraint                                                                      
		              7     set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]
		              7     set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]

