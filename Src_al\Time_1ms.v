`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 03/28/2023 
// Design Name: 	IMU_SCHA634
// Module Name:    	Time_1ms 
// Project Name: 	IMU_SCHA634
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	Time counter
// Revision 1.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module Time_1ms
(
    input		clk,	//88Mhz
    input		rst_n,   
    input		start_tm,//   
	output reg 	clk_1ms,//1ms
	output reg 	clk_1us	//1us
);

reg [7:0] 	cnt_us;
reg [15:0] 	cnt_ms;
reg			clk_1us_reg;
reg			start_tm_reg;
wire		start_tm_pos;/*synthesis keep*/

//start_tm reg
always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		start_tm_reg <= 1'b0;
	else
		start_tm_reg <= start_tm;
end

assign start_tm_pos = ~start_tm_reg & start_tm;

//us
always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		cnt_us <= 8'd0;
	else if((cnt_us == 8'd21)||(start_tm_pos == 1'b1))//500ns zeroing every 30 clocks(500ns*100m/1000m=50)
		cnt_us <= 8'd0;
	else
		cnt_us <= cnt_us + 1'd1;
end

//Generate 1us clock
always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		clk_1us <= 1'b0;
	else  if(cnt_us == 8'd21)//Per 500ns
		clk_1us <= ~clk_1us; //Clock reversal
	else
		clk_1us <= clk_1us;
end

//us
always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		cnt_ms <= 16'd0;
	else if((cnt_ms == 16'd21999)||(start_tm_pos == 1'b1))//500ns zeroing every 30 clocks(500ns*100m/1000m=50)
		cnt_ms <= 16'd0;
	else
		cnt_ms <= cnt_ms + 1'd1;
end

//Generate 1us clock
always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		clk_1ms <= 1'b0;
	else  if(cnt_ms == 16'd21999)//Per 500ns
		clk_1ms <= ~clk_1ms; //Clock reversal
	else
		clk_1ms <= clk_1ms;
end

//ms
/* always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		clk_1us_reg <= 1'b0;
	else
		clk_1us_reg <= clk_1us;
end

always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		cnt_ms <= 16'd0;
	else if(~clk_1us_reg & clk_1us)begin//500us zeroing every 30 clocks(500us*100m/1000m=50)
		if(cnt_ms < 16'd399)
			cnt_ms <= cnt_ms + 16'd1;
		else 
			cnt_ms <= 16'd0;
	end
	else
		cnt_ms <= cnt_ms;
end

//Generate 1us clock
always @(posedge clk or negedge rst_n)begin
	if(!rst_n)
		clk_1ms <= 1'b0;
	else if(cnt_ms < 16'd199)//Per 500us
		clk_1ms <= 1'b0; //Clock reversal
	else
		clk_1ms <= 1'b1;
end */

endmodule

