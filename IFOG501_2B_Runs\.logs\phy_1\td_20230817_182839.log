============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug 17 18:28:40 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1627 instances
RUN-0007 : 367 luts, 985 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2197 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1637 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1625 instances, 367 luts, 985 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7787, tnet num: 2195, tinst num: 1625, tnode num: 11027, tedge num: 13169.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283282s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 586648
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1625.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 473175, overlap = 11.25
PHY-3002 : Step(2): len = 347610, overlap = 18
PHY-3002 : Step(3): len = 308353, overlap = 15.75
PHY-3002 : Step(4): len = 293621, overlap = 18
PHY-3002 : Step(5): len = 283224, overlap = 18
PHY-3002 : Step(6): len = 278815, overlap = 15.75
PHY-3002 : Step(7): len = 272617, overlap = 15.75
PHY-3002 : Step(8): len = 266184, overlap = 18
PHY-3002 : Step(9): len = 261842, overlap = 18
PHY-3002 : Step(10): len = 256566, overlap = 18
PHY-3002 : Step(11): len = 248143, overlap = 18
PHY-3002 : Step(12): len = 242918, overlap = 18
PHY-3002 : Step(13): len = 239900, overlap = 18
PHY-3002 : Step(14): len = 230508, overlap = 18
PHY-3002 : Step(15): len = 223091, overlap = 18
PHY-3002 : Step(16): len = 220281, overlap = 18
PHY-3002 : Step(17): len = 214566, overlap = 18
PHY-3002 : Step(18): len = 206111, overlap = 18
PHY-3002 : Step(19): len = 202896, overlap = 18
PHY-3002 : Step(20): len = 198833, overlap = 18
PHY-3002 : Step(21): len = 187942, overlap = 20.25
PHY-3002 : Step(22): len = 182256, overlap = 20.25
PHY-3002 : Step(23): len = 180338, overlap = 20.25
PHY-3002 : Step(24): len = 158468, overlap = 18
PHY-3002 : Step(25): len = 147563, overlap = 20.25
PHY-3002 : Step(26): len = 145421, overlap = 20.25
PHY-3002 : Step(27): len = 141288, overlap = 20.25
PHY-3002 : Step(28): len = 138964, overlap = 20.25
PHY-3002 : Step(29): len = 134768, overlap = 20.25
PHY-3002 : Step(30): len = 126893, overlap = 20.25
PHY-3002 : Step(31): len = 122184, overlap = 20.25
PHY-3002 : Step(32): len = 118960, overlap = 20.25
PHY-3002 : Step(33): len = 117636, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.53127e-05
PHY-3002 : Step(34): len = 118942, overlap = 11.25
PHY-3002 : Step(35): len = 118514, overlap = 9
PHY-3002 : Step(36): len = 116267, overlap = 15.75
PHY-3002 : Step(37): len = 114224, overlap = 11.25
PHY-3002 : Step(38): len = 111124, overlap = 13.5
PHY-3002 : Step(39): len = 108096, overlap = 6.75
PHY-3002 : Step(40): len = 104780, overlap = 11.25
PHY-3002 : Step(41): len = 103864, overlap = 9
PHY-3002 : Step(42): len = 99429.6, overlap = 13.5
PHY-3002 : Step(43): len = 94203, overlap = 6.75
PHY-3002 : Step(44): len = 91832.1, overlap = 9.25
PHY-3002 : Step(45): len = 89742.3, overlap = 13.6875
PHY-3002 : Step(46): len = 89254.7, overlap = 16.1875
PHY-3002 : Step(47): len = 88554.6, overlap = 7.25
PHY-3002 : Step(48): len = 85397.9, overlap = 5
PHY-3002 : Step(49): len = 83242.6, overlap = 9.875
PHY-3002 : Step(50): len = 82013.4, overlap = 9.875
PHY-3002 : Step(51): len = 80103.6, overlap = 14.375
PHY-3002 : Step(52): len = 78843, overlap = 9.6875
PHY-3002 : Step(53): len = 77951.7, overlap = 7.5
PHY-3002 : Step(54): len = 76279.7, overlap = 7.375
PHY-3002 : Step(55): len = 75371.3, overlap = 9.6875
PHY-3002 : Step(56): len = 73231.5, overlap = 11.4375
PHY-3002 : Step(57): len = 72967.2, overlap = 9.125
PHY-3002 : Step(58): len = 72178.9, overlap = 11.3125
PHY-3002 : Step(59): len = 71678.3, overlap = 6.75
PHY-3002 : Step(60): len = 70222.9, overlap = 9
PHY-3002 : Step(61): len = 68691.2, overlap = 11.25
PHY-3002 : Step(62): len = 66929.2, overlap = 9
PHY-3002 : Step(63): len = 65895.6, overlap = 9
PHY-3002 : Step(64): len = 65263.7, overlap = 9
PHY-3002 : Step(65): len = 64094.3, overlap = 11.25
PHY-3002 : Step(66): len = 63449.3, overlap = 11.25
PHY-3002 : Step(67): len = 62575.4, overlap = 11.25
PHY-3002 : Step(68): len = 61890.9, overlap = 11.25
PHY-3002 : Step(69): len = 61779.5, overlap = 11.25
PHY-3002 : Step(70): len = 61446.2, overlap = 9
PHY-3002 : Step(71): len = 61046.1, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000170625
PHY-3002 : Step(72): len = 61074.2, overlap = 9
PHY-3002 : Step(73): len = 61031, overlap = 9
PHY-3002 : Step(74): len = 60794.1, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000341251
PHY-3002 : Step(75): len = 61156.9, overlap = 4.5
PHY-3002 : Step(76): len = 61184.9, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008161s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (574.4%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061899s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(77): len = 63467, overlap = 3.6875
PHY-3002 : Step(78): len = 62230.1, overlap = 3.875
PHY-3002 : Step(79): len = 61474.3, overlap = 3.1875
PHY-3002 : Step(80): len = 60186.4, overlap = 3.8125
PHY-3002 : Step(81): len = 58956.5, overlap = 3.6875
PHY-3002 : Step(82): len = 57662.5, overlap = 3.125
PHY-3002 : Step(83): len = 56599, overlap = 2.8125
PHY-3002 : Step(84): len = 55651.3, overlap = 3.1875
PHY-3002 : Step(85): len = 54296.9, overlap = 3.15625
PHY-3002 : Step(86): len = 53432.2, overlap = 4.40625
PHY-3002 : Step(87): len = 52890.1, overlap = 4.4375
PHY-3002 : Step(88): len = 52357.4, overlap = 4.75
PHY-3002 : Step(89): len = 51687, overlap = 2.9375
PHY-3002 : Step(90): len = 51018.4, overlap = 3.5625
PHY-3002 : Step(91): len = 50744.8, overlap = 3.0625
PHY-3002 : Step(92): len = 50423.2, overlap = 3.25
PHY-3002 : Step(93): len = 49787.8, overlap = 4.25
PHY-3002 : Step(94): len = 49484.8, overlap = 6
PHY-3002 : Step(95): len = 49231.3, overlap = 5.625
PHY-3002 : Step(96): len = 49209.2, overlap = 5.5625
PHY-3002 : Step(97): len = 48997.8, overlap = 5.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000766317
PHY-3002 : Step(98): len = 48954, overlap = 5.6875
PHY-3002 : Step(99): len = 48956.9, overlap = 5.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00153263
PHY-3002 : Step(100): len = 48953.7, overlap = 5.6875
PHY-3002 : Step(101): len = 48994.6, overlap = 5.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.078649s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000126622
PHY-3002 : Step(102): len = 50100.2, overlap = 58.1875
PHY-3002 : Step(103): len = 50929.7, overlap = 49.9688
PHY-3002 : Step(104): len = 51025.1, overlap = 48.5938
PHY-3002 : Step(105): len = 50640.8, overlap = 48.7812
PHY-3002 : Step(106): len = 50607, overlap = 48.375
PHY-3002 : Step(107): len = 50633.6, overlap = 44.9688
PHY-3002 : Step(108): len = 50881.7, overlap = 43.5625
PHY-3002 : Step(109): len = 50621.4, overlap = 42.625
PHY-3002 : Step(110): len = 50630.5, overlap = 42.3125
PHY-3002 : Step(111): len = 50700.6, overlap = 40.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000253245
PHY-3002 : Step(112): len = 50815.2, overlap = 39.75
PHY-3002 : Step(113): len = 50908, overlap = 36.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00050649
PHY-3002 : Step(114): len = 51905.5, overlap = 34.8438
PHY-3002 : Step(115): len = 52877.7, overlap = 36
PHY-3002 : Step(116): len = 53223.3, overlap = 35.7188
PHY-3002 : Step(117): len = 53017.6, overlap = 35.9375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7787, tnet num: 2195, tinst num: 1625, tnode num: 11027, tedge num: 13169.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 94.59 peak overflow 3.44
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2197.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56528, over cnt = 242(0%), over = 989, worst = 24
PHY-1001 : End global iterations;  0.101421s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (154.1%)

PHY-1001 : Congestion index: top1 = 42.56, top5 = 25.09, top10 = 16.67, top15 = 11.89.
PHY-1001 : End incremental global routing;  0.152456s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (143.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.091777s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.1%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1586 has valid locations, 4 needs to be replaced
PHY-3001 : design contains 1628 instances, 367 luts, 988 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 53160.6
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7799, tnet num: 2198, tinst num: 1628, tnode num: 11048, tedge num: 13187.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.346304s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (103.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(118): len = 53214.6, overlap = 2.21875
PHY-3002 : Step(119): len = 53223.1, overlap = 2.40625
PHY-3002 : Step(120): len = 53223, overlap = 2.40625
PHY-3002 : Step(121): len = 53212.3, overlap = 2.40625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059423s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.62673e-05
PHY-3002 : Step(122): len = 53212.3, overlap = 36.0625
PHY-3002 : Step(123): len = 53212.3, overlap = 36.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000132535
PHY-3002 : Step(124): len = 53211.5, overlap = 35.875
PHY-3002 : Step(125): len = 53211.5, overlap = 35.875
PHY-3001 : Final: Len = 53211.5, Over = 35.875
PHY-3001 : End incremental placement;  0.525174s wall, 0.578125s user + 0.078125s system = 0.656250s CPU (125.0%)

OPT-1001 : Total overflow 94.53 peak overflow 3.44
OPT-1001 : End high-fanout net optimization;  0.819223s wall, 1.000000s user + 0.125000s system = 1.125000s CPU (137.3%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1667/2200.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56688, over cnt = 242(0%), over = 984, worst = 24
PHY-1002 : len = 63416, over cnt = 159(0%), over = 356, worst = 24
PHY-1002 : len = 67296, over cnt = 24(0%), over = 34, worst = 5
PHY-1002 : len = 67888, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 68128, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.149667s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (114.8%)

PHY-1001 : Congestion index: top1 = 38.21, top5 = 25.71, top10 = 18.48, top15 = 13.62.
OPT-1001 : End congestion update;  0.202918s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (107.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2198 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065669s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.271035s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (103.8%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 221.
OPT-1001 : End physical optimization;  1.415965s wall, 1.593750s user + 0.125000s system = 1.718750s CPU (121.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 806 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 116 single LUT's are left
SYN-4006 : 712 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1079/1412 primitive instances ...
PHY-3001 : End packing;  0.072848s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 846 instances
RUN-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1469 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 81 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 844 instances, 797 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52835.2, Over = 63
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2031, tinst num: 844, tnode num: 8921, tedge num: 11556.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.390357s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.95905e-05
PHY-3002 : Step(126): len = 52154.1, overlap = 62
PHY-3002 : Step(127): len = 51359.9, overlap = 62.5
PHY-3002 : Step(128): len = 51073.3, overlap = 64
PHY-3002 : Step(129): len = 51244.3, overlap = 64
PHY-3002 : Step(130): len = 51368.9, overlap = 65.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.9181e-05
PHY-3002 : Step(131): len = 51567.6, overlap = 62.5
PHY-3002 : Step(132): len = 51776.5, overlap = 62
PHY-3002 : Step(133): len = 51816.2, overlap = 60.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000118362
PHY-3002 : Step(134): len = 52789.4, overlap = 58.5
PHY-3002 : Step(135): len = 54120.2, overlap = 56.25
PHY-3002 : Step(136): len = 54659.5, overlap = 55
PHY-3002 : Step(137): len = 54811, overlap = 54.75
PHY-3002 : Step(138): len = 54800.1, overlap = 54.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.118423s wall, 0.093750s user + 0.140625s system = 0.234375s CPU (197.9%)

PHY-3001 : Trial Legalized: Len = 69368.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062649s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000974107
PHY-3002 : Step(139): len = 66135.9, overlap = 9.5
PHY-3002 : Step(140): len = 63959.9, overlap = 14
PHY-3002 : Step(141): len = 62269.5, overlap = 18.5
PHY-3002 : Step(142): len = 60967.6, overlap = 18
PHY-3002 : Step(143): len = 60075.2, overlap = 18.75
PHY-3002 : Step(144): len = 59474.9, overlap = 21.5
PHY-3002 : Step(145): len = 59170.9, overlap = 23.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00188603
PHY-3002 : Step(146): len = 59501.7, overlap = 23.25
PHY-3002 : Step(147): len = 59534.9, overlap = 23.25
PHY-3002 : Step(148): len = 59525.5, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00377206
PHY-3002 : Step(149): len = 59623.5, overlap = 23.25
PHY-3002 : Step(150): len = 59647.9, overlap = 23
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005477s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64005.3, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005598s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 0, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 64181.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2031, tinst num: 844, tnode num: 8921, tedge num: 11556.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 60/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70752, over cnt = 138(0%), over = 220, worst = 8
PHY-1002 : len = 71440, over cnt = 96(0%), over = 128, worst = 5
PHY-1002 : len = 72536, over cnt = 20(0%), over = 28, worst = 5
PHY-1002 : len = 72960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.124426s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (163.2%)

PHY-1001 : Congestion index: top1 = 32.35, top5 = 22.93, top10 = 17.79, top15 = 14.13.
PHY-1001 : End incremental global routing;  0.181566s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (137.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077714s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.297471s wall, 0.328125s user + 0.046875s system = 0.375000s CPU (126.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1794/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008741s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.35, top5 = 22.93, top10 = 17.79, top15 = 14.13.
OPT-1001 : End congestion update;  0.060443s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (51.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063233s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.125347s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (74.8%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061100s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1794/2033.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006427s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (243.1%)

PHY-1001 : Congestion index: top1 = 32.35, top5 = 22.93, top10 = 17.79, top15 = 14.13.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050918s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.933781s wall, 0.890625s user + 0.046875s system = 0.937500s CPU (100.4%)

RUN-1003 : finish command "place" in  6.156669s wall, 8.593750s user + 3.500000s system = 12.093750s CPU (196.4%)

RUN-1004 : used memory is 204 MB, reserved memory is 167 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 846 instances
RUN-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2033 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1469 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 81 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2031, tinst num: 844, tnode num: 8921, tedge num: 11556.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70136, over cnt = 135(0%), over = 217, worst = 8
PHY-1002 : len = 70848, over cnt = 92(0%), over = 128, worst = 5
PHY-1002 : len = 71944, over cnt = 20(0%), over = 28, worst = 5
PHY-1002 : len = 72352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.185660s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (117.8%)

PHY-1001 : Congestion index: top1 = 32.24, top5 = 22.83, top10 = 17.71, top15 = 14.01.
PHY-1001 : End global routing;  0.246739s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (114.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 201, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 466, peak = 500.
PHY-1001 : End build detailed router design. 3.916878s wall, 3.796875s user + 0.125000s system = 3.921875s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35448, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.517127s wall, 1.515625s user + 0.000000s system = 1.515625s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 531, reserve = 499, peak = 531.
PHY-1001 : End phase 1; 1.523186s wall, 1.531250s user + 0.000000s system = 1.531250s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 190296, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 532.
PHY-1001 : End initial routed; 1.392295s wall, 2.359375s user + 0.203125s system = 2.562500s CPU (184.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1796(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.277   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.394546s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 536, reserve = 503, peak = 536.
PHY-1001 : End phase 2; 1.786951s wall, 2.750000s user + 0.203125s system = 2.953125s CPU (165.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 190296, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015242s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 190312, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031539s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (99.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 190344, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.025693s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (121.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 190376, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.027330s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (114.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1796(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.277   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.404832s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.200881s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End phase 3; 0.841775s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (100.2%)

PHY-1003 : Routed, final wirelength = 190376
PHY-1001 : Current memory(MB): used = 550, reserve = 517, peak = 550.
PHY-1001 : End export database. 0.013696s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (114.1%)

PHY-1001 : End detail routing;  8.269303s wall, 9.109375s user + 0.328125s system = 9.437500s CPU (114.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2031, tinst num: 844, tnode num: 8921, tedge num: 11556.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  9.387625s wall, 10.218750s user + 0.359375s system = 10.578125s CPU (112.7%)

RUN-1004 : used memory is 522 MB, reserved memory is 490 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      825   out of  19600    4.21%
#reg                     1077   out of  19600    5.49%
#le                      1537
  #lut only               460   out of   1537   29.93%
  #reg only               712   out of   1537   46.32%
  #lut&reg                365   out of   1537   23.75%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1537   |599     |226     |1108    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1131   |294     |133     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |17      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |534    |126     |58      |435     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |160    |61      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |4       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |13      |0       |26      |0       |0       |
|    integ                   |Integration                                      |137    |17      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |92     |28      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |83      |29      |250     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |112    |97      |7       |51      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |18     |15      |0       |12      |0       |0       |
|    U2                      |Ctrl_Data                                        |58     |54      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |78      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1433  
    #2          2       325   
    #3          3       107   
    #4          4        11   
    #5        5-10       83   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2031, tinst num: 844, tnode num: 8921, tedge num: 11556.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2031 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 844
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2033, pip num: 14868
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1359 valid insts, and 39316 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.532556s wall, 18.953125s user + 0.109375s system = 19.062500s CPU (539.6%)

RUN-1004 : used memory is 544 MB, reserved memory is 510 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230817_182839.log"
