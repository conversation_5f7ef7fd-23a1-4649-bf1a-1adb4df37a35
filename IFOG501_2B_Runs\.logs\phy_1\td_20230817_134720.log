============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug 17 13:47:20 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1633 instances
RUN-0007 : 374 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2203 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1641 nets have 2 pins
RUN-1001 : 447 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1631 instances, 374 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7801, tnet num: 2201, tinst num: 1631, tnode num: 11041, tedge num: 13185.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.286003s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (103.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 576124
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1631.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 549475, overlap = 20.25
PHY-3002 : Step(2): len = 441466, overlap = 13.5
PHY-3002 : Step(3): len = 368605, overlap = 20.25
PHY-3002 : Step(4): len = 349810, overlap = 18
PHY-3002 : Step(5): len = 340712, overlap = 18
PHY-3002 : Step(6): len = 330445, overlap = 20.25
PHY-3002 : Step(7): len = 322822, overlap = 20.25
PHY-3002 : Step(8): len = 316158, overlap = 20.25
PHY-3002 : Step(9): len = 308670, overlap = 20.25
PHY-3002 : Step(10): len = 301374, overlap = 20.25
PHY-3002 : Step(11): len = 296045, overlap = 20.25
PHY-3002 : Step(12): len = 288693, overlap = 20.25
PHY-3002 : Step(13): len = 282578, overlap = 20.25
PHY-3002 : Step(14): len = 277348, overlap = 20.25
PHY-3002 : Step(15): len = 271558, overlap = 20.25
PHY-3002 : Step(16): len = 265706, overlap = 20.25
PHY-3002 : Step(17): len = 260508, overlap = 20.25
PHY-3002 : Step(18): len = 254861, overlap = 20.25
PHY-3002 : Step(19): len = 249137, overlap = 20.25
PHY-3002 : Step(20): len = 244628, overlap = 20.25
PHY-3002 : Step(21): len = 239197, overlap = 20.25
PHY-3002 : Step(22): len = 233650, overlap = 20.25
PHY-3002 : Step(23): len = 228393, overlap = 20.25
PHY-3002 : Step(24): len = 223757, overlap = 20.25
PHY-3002 : Step(25): len = 219778, overlap = 20.25
PHY-3002 : Step(26): len = 211108, overlap = 20.25
PHY-3002 : Step(27): len = 206604, overlap = 20.25
PHY-3002 : Step(28): len = 204001, overlap = 20.25
PHY-3002 : Step(29): len = 190942, overlap = 20.25
PHY-3002 : Step(30): len = 179002, overlap = 20.25
PHY-3002 : Step(31): len = 177215, overlap = 20.25
PHY-3002 : Step(32): len = 165548, overlap = 20.25
PHY-3002 : Step(33): len = 121210, overlap = 18
PHY-3002 : Step(34): len = 117669, overlap = 20.25
PHY-3002 : Step(35): len = 115524, overlap = 18
PHY-3002 : Step(36): len = 112222, overlap = 15.75
PHY-3002 : Step(37): len = 108735, overlap = 18
PHY-3002 : Step(38): len = 105753, overlap = 18
PHY-3002 : Step(39): len = 104378, overlap = 18
PHY-3002 : Step(40): len = 99922, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.03323e-05
PHY-3002 : Step(41): len = 101598, overlap = 13.5
PHY-3002 : Step(42): len = 101224, overlap = 11.25
PHY-3002 : Step(43): len = 99974.5, overlap = 15.75
PHY-3002 : Step(44): len = 98938.3, overlap = 11.25
PHY-3002 : Step(45): len = 96071.6, overlap = 13.5
PHY-3002 : Step(46): len = 94365.8, overlap = 4.5
PHY-3002 : Step(47): len = 92868.7, overlap = 11.25
PHY-3002 : Step(48): len = 91145, overlap = 11.25
PHY-3002 : Step(49): len = 89851.7, overlap = 11.25
PHY-3002 : Step(50): len = 86167.4, overlap = 11.25
PHY-3002 : Step(51): len = 83728, overlap = 9
PHY-3002 : Step(52): len = 81864.6, overlap = 11.25
PHY-3002 : Step(53): len = 81344.7, overlap = 11.25
PHY-3002 : Step(54): len = 80050.1, overlap = 11.25
PHY-3002 : Step(55): len = 79258.6, overlap = 11.25
PHY-3002 : Step(56): len = 78613.4, overlap = 11.25
PHY-3002 : Step(57): len = 75304.1, overlap = 4.5
PHY-3002 : Step(58): len = 74476, overlap = 6.75
PHY-3002 : Step(59): len = 72938.5, overlap = 9
PHY-3002 : Step(60): len = 72218.7, overlap = 11.25
PHY-3002 : Step(61): len = 72303.7, overlap = 11.25
PHY-3002 : Step(62): len = 72113.8, overlap = 9
PHY-3002 : Step(63): len = 71434.1, overlap = 9
PHY-3002 : Step(64): len = 69472.7, overlap = 11.25
PHY-3002 : Step(65): len = 67891.8, overlap = 11.25
PHY-3002 : Step(66): len = 67493, overlap = 11.25
PHY-3002 : Step(67): len = 67418.9, overlap = 9
PHY-3002 : Step(68): len = 66609.3, overlap = 9
PHY-3002 : Step(69): len = 65054.5, overlap = 11.25
PHY-3002 : Step(70): len = 63784.7, overlap = 9
PHY-3002 : Step(71): len = 61918.4, overlap = 9
PHY-3002 : Step(72): len = 60977.6, overlap = 6.75
PHY-3002 : Step(73): len = 60842.6, overlap = 9
PHY-3002 : Step(74): len = 60189.9, overlap = 9
PHY-3002 : Step(75): len = 59532.8, overlap = 13.5
PHY-3002 : Step(76): len = 59139.6, overlap = 11.25
PHY-3002 : Step(77): len = 58294.2, overlap = 11.25
PHY-3002 : Step(78): len = 57594.8, overlap = 9
PHY-3002 : Step(79): len = 57368.8, overlap = 9
PHY-3002 : Step(80): len = 57238.8, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000160665
PHY-3002 : Step(81): len = 57279.4, overlap = 9
PHY-3002 : Step(82): len = 57245.5, overlap = 9
PHY-3002 : Step(83): len = 57169.1, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000321329
PHY-3002 : Step(84): len = 57290.1, overlap = 6.75
PHY-3002 : Step(85): len = 57213.6, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007124s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064435s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(86): len = 60545.7, overlap = 8.5625
PHY-3002 : Step(87): len = 59504.2, overlap = 8.375
PHY-3002 : Step(88): len = 59008.3, overlap = 8.3125
PHY-3002 : Step(89): len = 57996.2, overlap = 7.34375
PHY-3002 : Step(90): len = 57776.4, overlap = 7.9375
PHY-3002 : Step(91): len = 56638.8, overlap = 7.46875
PHY-3002 : Step(92): len = 55902.1, overlap = 7.28125
PHY-3002 : Step(93): len = 54696, overlap = 6
PHY-3002 : Step(94): len = 53818.9, overlap = 5.90625
PHY-3002 : Step(95): len = 53141.1, overlap = 6.53125
PHY-3002 : Step(96): len = 52795.9, overlap = 5.09375
PHY-3002 : Step(97): len = 52098.9, overlap = 4.65625
PHY-3002 : Step(98): len = 51899.2, overlap = 4.53125
PHY-3002 : Step(99): len = 51170.5, overlap = 4.15625
PHY-3002 : Step(100): len = 51082.2, overlap = 3.9375
PHY-3002 : Step(101): len = 50042.9, overlap = 3.53125
PHY-3002 : Step(102): len = 48419.7, overlap = 6.03125
PHY-3002 : Step(103): len = 47356.7, overlap = 9
PHY-3002 : Step(104): len = 47098, overlap = 9.09375
PHY-3002 : Step(105): len = 47029.6, overlap = 8.78125
PHY-3002 : Step(106): len = 46656.7, overlap = 10.4062
PHY-3002 : Step(107): len = 46564.8, overlap = 13.9062
PHY-3002 : Step(108): len = 46095.8, overlap = 16.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000301798
PHY-3002 : Step(109): len = 45990.8, overlap = 12.0312
PHY-3002 : Step(110): len = 46035.1, overlap = 11.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000603597
PHY-3002 : Step(111): len = 46025.9, overlap = 12.75
PHY-3002 : Step(112): len = 46202.9, overlap = 11.9062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062997s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.68289e-05
PHY-3002 : Step(113): len = 46246.2, overlap = 60.5938
PHY-3002 : Step(114): len = 46723, overlap = 62.7812
PHY-3002 : Step(115): len = 47132.9, overlap = 62.625
PHY-3002 : Step(116): len = 46976.8, overlap = 62.6875
PHY-3002 : Step(117): len = 47058, overlap = 60.9688
PHY-3002 : Step(118): len = 47150.8, overlap = 61.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000133658
PHY-3002 : Step(119): len = 47321, overlap = 61.5625
PHY-3002 : Step(120): len = 47451.7, overlap = 61.5625
PHY-3002 : Step(121): len = 48509.3, overlap = 60.4375
PHY-3002 : Step(122): len = 49407.5, overlap = 51.9688
PHY-3002 : Step(123): len = 49247.9, overlap = 51.0625
PHY-3002 : Step(124): len = 49227.7, overlap = 50.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000267315
PHY-3002 : Step(125): len = 49260.3, overlap = 49.9375
PHY-3002 : Step(126): len = 49260.3, overlap = 49.9375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7801, tnet num: 2201, tinst num: 1631, tnode num: 11041, tedge num: 13185.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 111.38 peak overflow 3.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2203.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53000, over cnt = 238(0%), over = 1160, worst = 24
PHY-1001 : End global iterations;  0.075494s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (124.2%)

PHY-1001 : Congestion index: top1 = 47.26, top5 = 26.68, top10 = 16.26, top15 = 11.41.
PHY-1001 : End incremental global routing;  0.125792s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (124.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2201 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070110s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.1%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1592 has valid locations, 2 needs to be replaced
PHY-3001 : design contains 1632 instances, 375 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 49465.6
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7804, tnet num: 2202, tinst num: 1632, tnode num: 11044, tedge num: 13189.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2202 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.316819s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(127): len = 49413.3, overlap = 5.03125
PHY-3002 : Step(128): len = 49417.3, overlap = 5.03125
PHY-3002 : Step(129): len = 49417.3, overlap = 5.03125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2202 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071786s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(130): len = 49411.1, overlap = 49.9375
PHY-3002 : Step(131): len = 49411.1, overlap = 49.9375
PHY-3001 : Final: Len = 49411.1, Over = 49.9375
PHY-3001 : End incremental placement;  0.468834s wall, 0.531250s user + 0.046875s system = 0.578125s CPU (123.3%)

OPT-1001 : Total overflow 111.56 peak overflow 3.50
OPT-1001 : End high-fanout net optimization;  0.703399s wall, 0.781250s user + 0.062500s system = 0.843750s CPU (120.0%)

OPT-1001 : Current memory(MB): used = 222, reserve = 185, peak = 222.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1670/2204.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53048, over cnt = 238(0%), over = 1153, worst = 23
PHY-1002 : len = 62960, over cnt = 178(0%), over = 384, worst = 14
PHY-1002 : len = 64808, over cnt = 69(0%), over = 165, worst = 8
PHY-1002 : len = 67384, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 68048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096893s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (112.9%)

PHY-1001 : Congestion index: top1 = 41.96, top5 = 26.76, top10 = 18.84, top15 = 13.55.
OPT-1001 : End congestion update;  0.141213s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (110.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2202 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060594s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.204292s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (107.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 222.
OPT-1001 : End physical optimization;  1.181253s wall, 1.265625s user + 0.062500s system = 1.328125s CPU (112.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 375 LUT to BLE ...
SYN-4008 : Packed 375 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 103 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 700 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1075/1407 primitive instances ...
PHY-3001 : End packing;  0.052712s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 844 instances
RUN-1001 : 398 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2036 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1480 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 842 instances, 795 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49079, Over = 80.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6583, tnet num: 2034, tinst num: 842, tnode num: 8936, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.311465s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.6816e-05
PHY-3002 : Step(132): len = 48667.5, overlap = 84.25
PHY-3002 : Step(133): len = 48570.8, overlap = 85.75
PHY-3002 : Step(134): len = 48078.1, overlap = 87.75
PHY-3002 : Step(135): len = 48062.6, overlap = 89.5
PHY-3002 : Step(136): len = 48066.1, overlap = 86.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.3632e-05
PHY-3002 : Step(137): len = 48199.1, overlap = 84
PHY-3002 : Step(138): len = 48561, overlap = 81.25
PHY-3002 : Step(139): len = 49723.3, overlap = 76.75
PHY-3002 : Step(140): len = 50470.5, overlap = 72
PHY-3002 : Step(141): len = 50842.1, overlap = 71.75
PHY-3002 : Step(142): len = 50931.2, overlap = 71.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 6.7264e-05
PHY-3002 : Step(143): len = 51277, overlap = 71.5
PHY-3002 : Step(144): len = 51495.7, overlap = 72
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.099998s wall, 0.031250s user + 0.125000s system = 0.156250s CPU (156.3%)

PHY-3001 : Trial Legalized: Len = 68921.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054876s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000497785
PHY-3002 : Step(145): len = 65690.6, overlap = 6.25
PHY-3002 : Step(146): len = 63467.2, overlap = 15.75
PHY-3002 : Step(147): len = 61617.9, overlap = 18.75
PHY-3002 : Step(148): len = 60602.7, overlap = 22.5
PHY-3002 : Step(149): len = 59725.4, overlap = 25.5
PHY-3002 : Step(150): len = 59376.8, overlap = 29.75
PHY-3002 : Step(151): len = 59085.6, overlap = 30.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000995571
PHY-3002 : Step(152): len = 59559.8, overlap = 28.75
PHY-3002 : Step(153): len = 59756.2, overlap = 29
PHY-3002 : Step(154): len = 59772, overlap = 29.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00199114
PHY-3002 : Step(155): len = 59888.1, overlap = 29.25
PHY-3002 : Step(156): len = 59888.1, overlap = 29.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005136s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64100.4, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005921s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 2, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 64236.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6583, tnet num: 2034, tinst num: 842, tnode num: 8936, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 19/2036.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70512, over cnt = 146(0%), over = 228, worst = 6
PHY-1002 : len = 71440, over cnt = 82(0%), over = 101, worst = 3
PHY-1002 : len = 72632, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 72776, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.138411s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (135.5%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 23.38, top10 = 18.14, top15 = 14.08.
PHY-1001 : End incremental global routing;  0.190906s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (122.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060316s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.282357s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (116.2%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1801/2036.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006298s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 23.38, top10 = 18.14, top15 = 14.08.
OPT-1001 : End congestion update;  0.054529s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049973s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (125.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.106200s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (103.0%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049484s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1801/2036.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006106s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 23.38, top10 = 18.14, top15 = 14.08.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051940s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.836469s wall, 0.859375s user + 0.031250s system = 0.890625s CPU (106.5%)

RUN-1003 : finish command "place" in  5.855404s wall, 9.828125s user + 2.687500s system = 12.515625s CPU (213.7%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 844 instances
RUN-1001 : 398 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2036 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1480 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6583, tnet num: 2034, tinst num: 842, tnode num: 8936, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70248, over cnt = 142(0%), over = 223, worst = 6
PHY-1002 : len = 71152, over cnt = 82(0%), over = 100, worst = 3
PHY-1002 : len = 72448, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 72512, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72560, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.154134s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (152.1%)

PHY-1001 : Congestion index: top1 = 31.81, top5 = 23.29, top10 = 18.06, top15 = 14.04.
PHY-1001 : End global routing;  0.205013s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (137.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 206, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 503, reserve = 470, peak = 503.
PHY-1001 : End build detailed router design. 4.871621s wall, 4.828125s user + 0.062500s system = 4.890625s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35000, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.797280s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 1; 1.803728s wall, 1.796875s user + 0.000000s system = 1.796875s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186768, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End initial routed; 1.781823s wall, 2.906250s user + 0.234375s system = 3.140625s CPU (176.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.309   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.540967s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 538, reserve = 505, peak = 538.
PHY-1001 : End phase 2; 2.322921s wall, 3.453125s user + 0.234375s system = 3.687500s CPU (158.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186768, over cnt = 30(0%), over = 30, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.024380s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (128.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186824, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.048700s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (96.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186840, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.025868s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (120.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 186840, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.046851s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1800(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.309   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.507409s wall, 0.484375s user + 0.015625s system = 0.500000s CPU (98.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.208773s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.3%)

PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End phase 3; 1.071417s wall, 1.031250s user + 0.031250s system = 1.062500s CPU (99.2%)

PHY-1003 : Routed, final wirelength = 186840
PHY-1001 : Current memory(MB): used = 553, reserve = 520, peak = 553.
PHY-1001 : End export database. 0.011500s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  10.323657s wall, 11.343750s user + 0.343750s system = 11.687500s CPU (113.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6583, tnet num: 2034, tinst num: 842, tnode num: 8936, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  11.428351s wall, 12.468750s user + 0.390625s system = 12.859375s CPU (112.5%)

RUN-1004 : used memory is 528 MB, reserved memory is 497 MB, peak memory is 553 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      829   out of  19600    4.23%
#reg                     1074   out of  19600    5.48%
#le                      1529
  #lut only               455   out of   1529   29.76%
  #reg only               700   out of   1529   45.78%
  #lut&reg                374   out of   1529   24.46%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1529   |604     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1125   |300     |132     |921     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |529    |124     |57      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |0       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |12      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |16      |0       |28      |0       |0       |
|    integ                   |Integration                                      |140    |16      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |86     |29      |21      |82      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |89      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |111    |97      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |18     |17      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |57     |52      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1444  
    #2          2       317   
    #3          3       103   
    #4          4        20   
    #5        5-10       77   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6583, tnet num: 2034, tinst num: 842, tnode num: 8936, tedge num: 11560.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2034 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 842
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2036, pip num: 14790
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1287 valid insts, and 39226 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.195042s wall, 17.718750s user + 0.078125s system = 17.796875s CPU (557.0%)

RUN-1004 : used memory is 552 MB, reserved memory is 519 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230817_134720.log"
