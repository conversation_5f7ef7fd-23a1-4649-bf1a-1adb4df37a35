============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug  3 16:15:51 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1607 instances
RUN-0007 : 373 luts, 971 seqs, 139 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2161 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1627 nets have 2 pins
RUN-1001 : 418 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1605 instances, 373 luts, 971 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7662, tnet num: 2159, tinst num: 1605, tnode num: 10843, tedge num: 12926.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2159 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.273015s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (103.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 601661
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1605.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 490802, overlap = 20.25
PHY-3002 : Step(2): len = 452435, overlap = 20.25
PHY-3002 : Step(3): len = 429132, overlap = 20.25
PHY-3002 : Step(4): len = 412474, overlap = 20.25
PHY-3002 : Step(5): len = 399440, overlap = 20.25
PHY-3002 : Step(6): len = 385643, overlap = 20.25
PHY-3002 : Step(7): len = 374037, overlap = 20.25
PHY-3002 : Step(8): len = 362274, overlap = 20.25
PHY-3002 : Step(9): len = 352154, overlap = 20.25
PHY-3002 : Step(10): len = 341268, overlap = 18
PHY-3002 : Step(11): len = 332076, overlap = 18
PHY-3002 : Step(12): len = 322435, overlap = 18
PHY-3002 : Step(13): len = 315438, overlap = 20.25
PHY-3002 : Step(14): len = 306027, overlap = 20.25
PHY-3002 : Step(15): len = 299624, overlap = 20.25
PHY-3002 : Step(16): len = 291889, overlap = 20.25
PHY-3002 : Step(17): len = 285534, overlap = 20.25
PHY-3002 : Step(18): len = 277933, overlap = 20.25
PHY-3002 : Step(19): len = 272760, overlap = 20.25
PHY-3002 : Step(20): len = 266498, overlap = 20.25
PHY-3002 : Step(21): len = 260298, overlap = 20.25
PHY-3002 : Step(22): len = 254854, overlap = 20.25
PHY-3002 : Step(23): len = 250226, overlap = 20.25
PHY-3002 : Step(24): len = 245059, overlap = 20.25
PHY-3002 : Step(25): len = 238501, overlap = 20.25
PHY-3002 : Step(26): len = 234255, overlap = 20.25
PHY-3002 : Step(27): len = 229657, overlap = 20.25
PHY-3002 : Step(28): len = 224589, overlap = 20.25
PHY-3002 : Step(29): len = 220437, overlap = 20.25
PHY-3002 : Step(30): len = 216537, overlap = 20.25
PHY-3002 : Step(31): len = 209978, overlap = 20.25
PHY-3002 : Step(32): len = 204633, overlap = 20.25
PHY-3002 : Step(33): len = 201951, overlap = 20.25
PHY-3002 : Step(34): len = 197088, overlap = 20.25
PHY-3002 : Step(35): len = 185811, overlap = 20.25
PHY-3002 : Step(36): len = 180460, overlap = 20.25
PHY-3002 : Step(37): len = 178768, overlap = 20.25
PHY-3002 : Step(38): len = 164332, overlap = 20.25
PHY-3002 : Step(39): len = 128371, overlap = 20.25
PHY-3002 : Step(40): len = 125517, overlap = 20.25
PHY-3002 : Step(41): len = 123540, overlap = 20.25
PHY-3002 : Step(42): len = 118282, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00012102
PHY-3002 : Step(43): len = 119853, overlap = 11.25
PHY-3002 : Step(44): len = 118100, overlap = 9
PHY-3002 : Step(45): len = 115997, overlap = 18
PHY-3002 : Step(46): len = 114387, overlap = 18
PHY-3002 : Step(47): len = 115163, overlap = 18
PHY-3002 : Step(48): len = 113304, overlap = 9
PHY-3002 : Step(49): len = 110828, overlap = 9
PHY-3002 : Step(50): len = 108979, overlap = 13.5
PHY-3002 : Step(51): len = 107321, overlap = 13.5
PHY-3002 : Step(52): len = 107213, overlap = 9
PHY-3002 : Step(53): len = 103773, overlap = 13.5
PHY-3002 : Step(54): len = 101463, overlap = 11.25
PHY-3002 : Step(55): len = 99645.6, overlap = 13.5
PHY-3002 : Step(56): len = 99630.9, overlap = 15.75
PHY-3002 : Step(57): len = 97794.4, overlap = 11.25
PHY-3002 : Step(58): len = 96051, overlap = 11.25
PHY-3002 : Step(59): len = 94599.1, overlap = 11.25
PHY-3002 : Step(60): len = 94503.6, overlap = 11.25
PHY-3002 : Step(61): len = 93763.3, overlap = 6.75
PHY-3002 : Step(62): len = 91947.9, overlap = 9
PHY-3002 : Step(63): len = 89530.8, overlap = 13.5
PHY-3002 : Step(64): len = 89104.6, overlap = 13.5
PHY-3002 : Step(65): len = 87884.7, overlap = 6.75
PHY-3002 : Step(66): len = 86802.9, overlap = 4.5
PHY-3002 : Step(67): len = 83288.2, overlap = 9
PHY-3002 : Step(68): len = 81896.8, overlap = 12.0625
PHY-3002 : Step(69): len = 80611.4, overlap = 12.375
PHY-3002 : Step(70): len = 80555.7, overlap = 10.125
PHY-3002 : Step(71): len = 79667.6, overlap = 10.1875
PHY-3002 : Step(72): len = 78164.1, overlap = 12.1875
PHY-3002 : Step(73): len = 77274.3, overlap = 16.5625
PHY-3002 : Step(74): len = 75952.4, overlap = 14.1875
PHY-3002 : Step(75): len = 75542.7, overlap = 11.625
PHY-3002 : Step(76): len = 74376.9, overlap = 13.9375
PHY-3002 : Step(77): len = 70937.1, overlap = 9.75
PHY-3002 : Step(78): len = 70359.1, overlap = 14.375
PHY-3002 : Step(79): len = 70304.3, overlap = 14.375
PHY-3002 : Step(80): len = 70569.5, overlap = 12.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00024204
PHY-3002 : Step(81): len = 70503.6, overlap = 9.8125
PHY-3002 : Step(82): len = 70481, overlap = 9.8125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00048408
PHY-3002 : Step(83): len = 70370.3, overlap = 9.8125
PHY-3002 : Step(84): len = 70436.1, overlap = 12.125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007595s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2159 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066569s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(85): len = 73584.7, overlap = 7.5625
PHY-3002 : Step(86): len = 71868.5, overlap = 7.3125
PHY-3002 : Step(87): len = 69859.5, overlap = 7.3125
PHY-3002 : Step(88): len = 68350.4, overlap = 8.3125
PHY-3002 : Step(89): len = 67056.2, overlap = 7.6875
PHY-3002 : Step(90): len = 66043.5, overlap = 8.25
PHY-3002 : Step(91): len = 63358.7, overlap = 8
PHY-3002 : Step(92): len = 60928.1, overlap = 7.28125
PHY-3002 : Step(93): len = 59538.5, overlap = 7.40625
PHY-3002 : Step(94): len = 58742.2, overlap = 7.15625
PHY-3002 : Step(95): len = 58099.4, overlap = 6.65625
PHY-3002 : Step(96): len = 57095.2, overlap = 6.375
PHY-3002 : Step(97): len = 56336.1, overlap = 7.65625
PHY-3002 : Step(98): len = 55612.8, overlap = 8.6875
PHY-3002 : Step(99): len = 54749.2, overlap = 8.75
PHY-3002 : Step(100): len = 54197.5, overlap = 9.0625
PHY-3002 : Step(101): len = 53673.9, overlap = 10.0625
PHY-3002 : Step(102): len = 53036.9, overlap = 10.1562
PHY-3002 : Step(103): len = 52472, overlap = 11.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00123141
PHY-3002 : Step(104): len = 52473.6, overlap = 11.2188
PHY-3002 : Step(105): len = 52325.5, overlap = 11.1562
PHY-3002 : Step(106): len = 52301.2, overlap = 11.0938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2159 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056872s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000173666
PHY-3002 : Step(107): len = 52920.2, overlap = 53.2812
PHY-3002 : Step(108): len = 52726.9, overlap = 53.1875
PHY-3002 : Step(109): len = 53186.1, overlap = 39.1562
PHY-3002 : Step(110): len = 53533.8, overlap = 37.9062
PHY-3002 : Step(111): len = 53325.3, overlap = 35.7812
PHY-3002 : Step(112): len = 53152.8, overlap = 34.7812
PHY-3002 : Step(113): len = 53168.6, overlap = 30.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000347333
PHY-3002 : Step(114): len = 53102.6, overlap = 29.9062
PHY-3002 : Step(115): len = 53726.5, overlap = 30.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000694666
PHY-3002 : Step(116): len = 53884.1, overlap = 31.6875
PHY-3002 : Step(117): len = 54301.2, overlap = 30.6562
PHY-3002 : Step(118): len = 54747.6, overlap = 27.7188
PHY-3002 : Step(119): len = 54979.8, overlap = 27.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7662, tnet num: 2159, tinst num: 1605, tnode num: 10843, tedge num: 12926.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.72 peak overflow 3.16
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2161.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58128, over cnt = 254(0%), over = 1106, worst = 21
PHY-1001 : End global iterations;  0.060228s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (207.5%)

PHY-1001 : Congestion index: top1 = 43.32, top5 = 26.00, top10 = 16.85, top15 = 12.04.
PHY-1001 : End incremental global routing;  0.112000s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (153.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2159 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067936s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1566 has valid locations, 5 needs to be replaced
PHY-3001 : design contains 1609 instances, 373 luts, 975 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 55080.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7678, tnet num: 2163, tinst num: 1609, tnode num: 10871, tedge num: 12950.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2163 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.320898s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (102.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(120): len = 55165.2, overlap = 2.4375
PHY-3002 : Step(121): len = 55191.1, overlap = 2.5625
PHY-3002 : Step(122): len = 55279.8, overlap = 2.75
PHY-3002 : Step(123): len = 55261.6, overlap = 2.6875
PHY-3002 : Step(124): len = 55261.6, overlap = 2.6875
PHY-3002 : Step(125): len = 55268.4, overlap = 2.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2163 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059199s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000656812
PHY-3002 : Step(126): len = 55268.2, overlap = 28.2188
PHY-3002 : Step(127): len = 55268.2, overlap = 28.2188
PHY-3001 : Final: Len = 55268.2, Over = 28.2188
PHY-3001 : End incremental placement;  0.500891s wall, 0.718750s user + 0.078125s system = 0.796875s CPU (159.1%)

OPT-1001 : Total overflow 89.91 peak overflow 3.16
OPT-1001 : End high-fanout net optimization;  0.714975s wall, 0.968750s user + 0.109375s system = 1.078125s CPU (150.8%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1727/2165.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58448, over cnt = 254(0%), over = 1099, worst = 21
PHY-1002 : len = 65152, over cnt = 200(0%), over = 524, worst = 15
PHY-1002 : len = 70376, over cnt = 55(0%), over = 106, worst = 11
PHY-1002 : len = 71480, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.091417s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (119.6%)

PHY-1001 : Congestion index: top1 = 37.26, top5 = 25.92, top10 = 18.91, top15 = 14.11.
OPT-1001 : End congestion update;  0.138130s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (124.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2163 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057228s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.197858s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (118.5%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 220.
OPT-1001 : End physical optimization;  1.195845s wall, 1.484375s user + 0.109375s system = 1.593750s CPU (133.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 794 remaining SEQ's ...
SYN-4005 : Packed 87 SEQ with LUT/SLICE
SYN-4006 : 124 single LUT's are left
SYN-4006 : 707 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1080/1401 primitive instances ...
PHY-3001 : End packing;  0.049286s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 832 instances
RUN-1001 : 391 mslices, 392 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1999 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1464 nets have 2 pins
RUN-1001 : 415 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 830 instances, 783 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55148.6, Over = 63.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6460, tnet num: 1997, tinst num: 830, tnode num: 8758, tedge num: 11326.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.297574s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.0256e-05
PHY-3002 : Step(128): len = 54742.1, overlap = 62.5
PHY-3002 : Step(129): len = 54605.4, overlap = 61.25
PHY-3002 : Step(130): len = 54212.8, overlap = 59
PHY-3002 : Step(131): len = 53959.2, overlap = 63
PHY-3002 : Step(132): len = 53893.8, overlap = 64.5
PHY-3002 : Step(133): len = 53817.5, overlap = 64.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.0512e-05
PHY-3002 : Step(134): len = 53837.4, overlap = 65
PHY-3002 : Step(135): len = 54338.4, overlap = 60.5
PHY-3002 : Step(136): len = 54799.4, overlap = 58.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000121024
PHY-3002 : Step(137): len = 55877.1, overlap = 53.5
PHY-3002 : Step(138): len = 56697.8, overlap = 53.25
PHY-3002 : Step(139): len = 56714.6, overlap = 52
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.083159s wall, 0.046875s user + 0.062500s system = 0.109375s CPU (131.5%)

PHY-3001 : Trial Legalized: Len = 70724.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.047503s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000640085
PHY-3002 : Step(140): len = 68172.6, overlap = 6.25
PHY-3002 : Step(141): len = 65712.4, overlap = 13
PHY-3002 : Step(142): len = 63374.3, overlap = 16.25
PHY-3002 : Step(143): len = 62000.5, overlap = 21.5
PHY-3002 : Step(144): len = 61473.6, overlap = 24.5
PHY-3002 : Step(145): len = 61070, overlap = 25.5
PHY-3002 : Step(146): len = 60569.7, overlap = 27.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00128017
PHY-3002 : Step(147): len = 60853.4, overlap = 27.5
PHY-3002 : Step(148): len = 60947.6, overlap = 27
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00256034
PHY-3002 : Step(149): len = 61026.7, overlap = 26.25
PHY-3002 : Step(150): len = 61136.5, overlap = 26
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005007s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64679.1, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005371s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 2, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 64907.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6460, tnet num: 1997, tinst num: 830, tnode num: 8758, tedge num: 11326.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 55/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70816, over cnt = 136(0%), over = 203, worst = 7
PHY-1002 : len = 71672, over cnt = 75(0%), over = 92, worst = 3
PHY-1002 : len = 72752, over cnt = 5(0%), over = 6, worst = 2
PHY-1002 : len = 72872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.115895s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (134.8%)

PHY-1001 : Congestion index: top1 = 31.83, top5 = 22.48, top10 = 17.53, top15 = 14.07.
PHY-1001 : End incremental global routing;  0.167952s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (120.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059516s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.256844s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (115.6%)

OPT-1001 : Current memory(MB): used = 221, reserve = 186, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1787/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007892s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (198.0%)

PHY-1001 : Congestion index: top1 = 31.83, top5 = 22.48, top10 = 17.53, top15 = 14.07.
OPT-1001 : End congestion update;  0.054724s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056812s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.113366s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (96.5%)

OPT-1001 : Current memory(MB): used = 224, reserve = 188, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048614s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (128.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1787/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005510s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (283.6%)

PHY-1001 : Congestion index: top1 = 31.83, top5 = 22.48, top10 = 17.53, top15 = 14.07.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048006s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.344828
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.808554s wall, 0.812500s user + 0.046875s system = 0.859375s CPU (106.3%)

RUN-1003 : finish command "place" in  5.688962s wall, 8.281250s user + 2.578125s system = 10.859375s CPU (190.9%)

RUN-1004 : used memory is 209 MB, reserved memory is 172 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 832 instances
RUN-1001 : 391 mslices, 392 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1999 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1464 nets have 2 pins
RUN-1001 : 415 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6460, tnet num: 1997, tinst num: 830, tnode num: 8758, tedge num: 11326.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 391 mslices, 392 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70312, over cnt = 137(0%), over = 204, worst = 7
PHY-1002 : len = 71104, over cnt = 80(0%), over = 103, worst = 3
PHY-1002 : len = 71944, over cnt = 30(0%), over = 37, worst = 3
PHY-1002 : len = 72360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121510s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (141.4%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 22.34, top10 = 17.44, top15 = 13.98.
PHY-1001 : End global routing;  0.171452s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (127.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 206, peak = 250.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 469, peak = 501.
PHY-1001 : End build detailed router design. 3.264440s wall, 3.250000s user + 0.000000s system = 3.250000s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33944, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.277109s wall, 1.265625s user + 0.031250s system = 1.296875s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 535.
PHY-1001 : End phase 1; 1.282989s wall, 1.265625s user + 0.046875s system = 1.312500s CPU (102.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184432, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 535.
PHY-1001 : End initial routed; 1.103382s wall, 2.109375s user + 0.062500s system = 2.171875s CPU (196.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1773(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.593   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.358313s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End phase 2; 1.461790s wall, 2.468750s user + 0.062500s system = 2.531250s CPU (173.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184432, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016191s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (96.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184464, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023139s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (202.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184504, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.026510s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (176.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1773(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.593   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.410476s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.0%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.170048s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 550, reserve = 517, peak = 550.
PHY-1001 : End phase 3; 0.766447s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (104.0%)

PHY-1003 : Routed, final wirelength = 184504
PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End export database. 0.009434s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (165.6%)

PHY-1001 : End detail routing;  6.963537s wall, 7.984375s user + 0.109375s system = 8.093750s CPU (116.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6460, tnet num: 1997, tinst num: 830, tnode num: 8758, tedge num: 11326.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.847969s wall, 8.859375s user + 0.171875s system = 9.031250s CPU (115.1%)

RUN-1004 : used memory is 526 MB, reserved memory is 497 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      807   out of  19600    4.12%
#reg                     1049   out of  19600    5.35%
#le                      1514
  #lut only               465   out of   1514   30.71%
  #reg only               707   out of   1514   46.70%
  #lut&reg                342   out of   1514   22.59%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         459
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1514   |593     |214     |1080    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1098   |281     |121     |896     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |41     |30      |7       |25      |0       |0       |
|    demodu                  |Demodulation                                     |522    |117     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |54     |2       |0       |54      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |24     |13      |0       |24      |0       |0       |
|    integ                   |Integration                                      |139    |15      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |63     |19      |14      |59      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |83      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |21     |17      |4       |16      |0       |0       |
|  u_uart                    |UART_Control                                     |119    |101     |7       |51      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |25     |20      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |58     |53      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |214    |169     |45      |78      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1428  
    #2          2       293   
    #3          3       104   
    #4          4        18   
    #5        5-10       82   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6460, tnet num: 1997, tinst num: 830, tnode num: 8758, tedge num: 11326.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 830
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1999, pip num: 14722
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1293 valid insts, and 38839 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.071769s wall, 17.921875s user + 0.031250s system = 17.953125s CPU (584.5%)

RUN-1004 : used memory is 549 MB, reserved memory is 518 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230803_161551.log"
