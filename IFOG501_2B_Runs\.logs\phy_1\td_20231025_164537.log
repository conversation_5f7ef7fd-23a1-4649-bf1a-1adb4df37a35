============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Oct 25 16:45:37 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1552 instances
RUN-0007 : 384 luts, 907 seqs, 137 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2106 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1560 nets have 2 pins
RUN-1001 : 430 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1550 instances, 384 luts, 907 seqs, 212 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7440, tnet num: 2104, tinst num: 1550, tnode num: 10424, tedge num: 12613.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2104 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.306014s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (102.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 541690
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1550.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 455757, overlap = 13.5
PHY-3002 : Step(2): len = 410328, overlap = 15.75
PHY-3002 : Step(3): len = 391631, overlap = 20.25
PHY-3002 : Step(4): len = 374366, overlap = 20.25
PHY-3002 : Step(5): len = 368757, overlap = 18
PHY-3002 : Step(6): len = 362139, overlap = 20.25
PHY-3002 : Step(7): len = 351038, overlap = 18
PHY-3002 : Step(8): len = 342268, overlap = 20.25
PHY-3002 : Step(9): len = 336408, overlap = 18
PHY-3002 : Step(10): len = 325790, overlap = 20.25
PHY-3002 : Step(11): len = 318490, overlap = 15.75
PHY-3002 : Step(12): len = 312542, overlap = 20.25
PHY-3002 : Step(13): len = 303420, overlap = 13.5
PHY-3002 : Step(14): len = 295677, overlap = 15.75
PHY-3002 : Step(15): len = 291146, overlap = 13.5
PHY-3002 : Step(16): len = 284409, overlap = 15.75
PHY-3002 : Step(17): len = 276186, overlap = 13.5
PHY-3002 : Step(18): len = 271213, overlap = 15.75
PHY-3002 : Step(19): len = 266566, overlap = 13.5
PHY-3002 : Step(20): len = 258253, overlap = 13.5
PHY-3002 : Step(21): len = 253255, overlap = 11.25
PHY-3002 : Step(22): len = 249400, overlap = 13.5
PHY-3002 : Step(23): len = 239962, overlap = 13.5
PHY-3002 : Step(24): len = 233949, overlap = 13.5
PHY-3002 : Step(25): len = 231016, overlap = 11.25
PHY-3002 : Step(26): len = 219944, overlap = 13.5
PHY-3002 : Step(27): len = 208502, overlap = 13.5
PHY-3002 : Step(28): len = 205348, overlap = 13.5
PHY-3002 : Step(29): len = 200778, overlap = 13.5
PHY-3002 : Step(30): len = 173221, overlap = 13.5
PHY-3002 : Step(31): len = 165887, overlap = 11.25
PHY-3002 : Step(32): len = 164786, overlap = 13.5
PHY-3002 : Step(33): len = 140958, overlap = 15.75
PHY-3002 : Step(34): len = 132229, overlap = 13.5
PHY-3002 : Step(35): len = 130245, overlap = 13.5
PHY-3002 : Step(36): len = 126573, overlap = 15.75
PHY-3002 : Step(37): len = 123541, overlap = 13.5
PHY-3002 : Step(38): len = 121598, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000110456
PHY-3002 : Step(39): len = 121202, overlap = 13.5
PHY-3002 : Step(40): len = 120908, overlap = 13.5
PHY-3002 : Step(41): len = 120071, overlap = 11.25
PHY-3002 : Step(42): len = 119334, overlap = 13.5
PHY-3002 : Step(43): len = 117858, overlap = 13.5
PHY-3002 : Step(44): len = 109105, overlap = 13.5
PHY-3002 : Step(45): len = 105609, overlap = 9
PHY-3002 : Step(46): len = 103770, overlap = 11.25
PHY-3002 : Step(47): len = 102798, overlap = 11.25
PHY-3002 : Step(48): len = 100788, overlap = 11.25
PHY-3002 : Step(49): len = 98835.8, overlap = 11.25
PHY-3002 : Step(50): len = 97878.4, overlap = 11.25
PHY-3002 : Step(51): len = 93898.3, overlap = 13.5
PHY-3002 : Step(52): len = 92318.1, overlap = 13.5
PHY-3002 : Step(53): len = 90855.9, overlap = 13.5
PHY-3002 : Step(54): len = 89171.1, overlap = 11.25
PHY-3002 : Step(55): len = 87744, overlap = 11.25
PHY-3002 : Step(56): len = 84096.4, overlap = 13.5
PHY-3002 : Step(57): len = 81425.5, overlap = 13.5
PHY-3002 : Step(58): len = 79637.2, overlap = 13.5
PHY-3002 : Step(59): len = 78588.4, overlap = 13.5
PHY-3002 : Step(60): len = 77045.3, overlap = 9
PHY-3002 : Step(61): len = 74650.1, overlap = 11.25
PHY-3002 : Step(62): len = 73990.6, overlap = 11.3125
PHY-3002 : Step(63): len = 72517.5, overlap = 11.4375
PHY-3002 : Step(64): len = 70754, overlap = 11.625
PHY-3002 : Step(65): len = 70654.5, overlap = 12.0625
PHY-3002 : Step(66): len = 69293.4, overlap = 12
PHY-3002 : Step(67): len = 68409.2, overlap = 13.1875
PHY-3002 : Step(68): len = 68123.2, overlap = 13.5
PHY-3002 : Step(69): len = 67428.2, overlap = 13.6875
PHY-3002 : Step(70): len = 66883.4, overlap = 14
PHY-3002 : Step(71): len = 66131.3, overlap = 14.1875
PHY-3002 : Step(72): len = 65765.5, overlap = 14.375
PHY-3002 : Step(73): len = 64073.5, overlap = 17.6875
PHY-3002 : Step(74): len = 61951.2, overlap = 16.125
PHY-3002 : Step(75): len = 61375.3, overlap = 16.1875
PHY-3002 : Step(76): len = 60675.5, overlap = 16.0625
PHY-3002 : Step(77): len = 59572.5, overlap = 15.7812
PHY-3002 : Step(78): len = 59122.3, overlap = 13.4062
PHY-3002 : Step(79): len = 58079.9, overlap = 15.6562
PHY-3002 : Step(80): len = 57730.8, overlap = 15.75
PHY-3002 : Step(81): len = 57381.1, overlap = 17.75
PHY-3002 : Step(82): len = 56629.5, overlap = 17.5625
PHY-3002 : Step(83): len = 56201.6, overlap = 17.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000220911
PHY-3002 : Step(84): len = 56458.9, overlap = 17.3125
PHY-3002 : Step(85): len = 56435.3, overlap = 17.375
PHY-3002 : Step(86): len = 56404.7, overlap = 17.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000441822
PHY-3002 : Step(87): len = 56273.9, overlap = 15.1875
PHY-3002 : Step(88): len = 56224.8, overlap = 15.1875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006113s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (255.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2104 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056813s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000326548
PHY-3002 : Step(89): len = 60096.6, overlap = 17.8125
PHY-3002 : Step(90): len = 59282.2, overlap = 17.375
PHY-3002 : Step(91): len = 57820.8, overlap = 17.2812
PHY-3002 : Step(92): len = 56646.8, overlap = 18.125
PHY-3002 : Step(93): len = 55841.2, overlap = 18.5625
PHY-3002 : Step(94): len = 54805.6, overlap = 15.5625
PHY-3002 : Step(95): len = 54086.5, overlap = 17.2812
PHY-3002 : Step(96): len = 53450.5, overlap = 15.5625
PHY-3002 : Step(97): len = 53247.5, overlap = 18.7188
PHY-3002 : Step(98): len = 52977.8, overlap = 22.5312
PHY-3002 : Step(99): len = 52302.6, overlap = 21.6562
PHY-3002 : Step(100): len = 51138, overlap = 21.9062
PHY-3002 : Step(101): len = 50355.4, overlap = 24.2188
PHY-3002 : Step(102): len = 49996.6, overlap = 23.6875
PHY-3002 : Step(103): len = 49511.1, overlap = 22.8438
PHY-3002 : Step(104): len = 49136.5, overlap = 21.0625
PHY-3002 : Step(105): len = 48824.9, overlap = 20.8438
PHY-3002 : Step(106): len = 48227.9, overlap = 20.625
PHY-3002 : Step(107): len = 47640.1, overlap = 20.6875
PHY-3002 : Step(108): len = 47479.1, overlap = 21.0938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000653097
PHY-3002 : Step(109): len = 47549.3, overlap = 21.125
PHY-3002 : Step(110): len = 47599.5, overlap = 21.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00130619
PHY-3002 : Step(111): len = 47417.8, overlap = 21.0312
PHY-3002 : Step(112): len = 47452.8, overlap = 21.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2104 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059447s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.16426e-05
PHY-3002 : Step(113): len = 47625, overlap = 43.25
PHY-3002 : Step(114): len = 49081.2, overlap = 48
PHY-3002 : Step(115): len = 49716.9, overlap = 48.0312
PHY-3002 : Step(116): len = 49206.3, overlap = 48.0312
PHY-3002 : Step(117): len = 48719, overlap = 47.5312
PHY-3002 : Step(118): len = 48713.1, overlap = 44.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103285
PHY-3002 : Step(119): len = 48830.6, overlap = 43.2188
PHY-3002 : Step(120): len = 49578.9, overlap = 42.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00020657
PHY-3002 : Step(121): len = 50287.2, overlap = 41.625
PHY-3002 : Step(122): len = 51244, overlap = 40.7812
PHY-3002 : Step(123): len = 52378.9, overlap = 37.3438
PHY-3002 : Step(124): len = 52134.8, overlap = 37.1562
PHY-3002 : Step(125): len = 51954.1, overlap = 37.1562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000413141
PHY-3002 : Step(126): len = 52388.6, overlap = 31.7812
PHY-3002 : Step(127): len = 53698.7, overlap = 32.7812
PHY-3002 : Step(128): len = 53706.3, overlap = 31.9375
PHY-3002 : Step(129): len = 52983.6, overlap = 30.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7440, tnet num: 2104, tinst num: 1550, tnode num: 10424, tedge num: 12613.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.12 peak overflow 2.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2106.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56360, over cnt = 251(0%), over = 975, worst = 14
PHY-1001 : End global iterations;  0.090139s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (86.7%)

PHY-1001 : Congestion index: top1 = 44.48, top5 = 26.16, top10 = 16.62, top15 = 11.68.
PHY-1001 : End incremental global routing;  0.138574s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (90.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2104 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066770s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.234689s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (93.2%)

OPT-1001 : Current memory(MB): used = 213, reserve = 175, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1635/2106.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56360, over cnt = 251(0%), over = 975, worst = 14
PHY-1002 : len = 64496, over cnt = 164(0%), over = 295, worst = 9
PHY-1002 : len = 66376, over cnt = 64(0%), over = 101, worst = 9
PHY-1002 : len = 67808, over cnt = 14(0%), over = 14, worst = 1
PHY-1002 : len = 68384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.088654s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (158.6%)

PHY-1001 : Congestion index: top1 = 40.11, top5 = 25.93, top10 = 18.62, top15 = 13.56.
OPT-1001 : End congestion update;  0.129445s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (144.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2104 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053571s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.186058s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (126.0%)

OPT-1001 : Current memory(MB): used = 216, reserve = 178, peak = 216.
OPT-1001 : End physical optimization;  0.711142s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (103.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 384 LUT to BLE ...
SYN-4008 : Packed 384 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 111 SEQ with LUT/SLICE
SYN-4006 : 120 single LUT's are left
SYN-4006 : 625 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1009/1304 primitive instances ...
PHY-3001 : End packing;  0.045062s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 788 instances
RUN-1001 : 369 mslices, 370 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1943 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1403 nets have 2 pins
RUN-1001 : 424 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 786 instances, 739 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 53205.4, Over = 54.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6310, tnet num: 1941, tinst num: 786, tnode num: 8490, tedge num: 11105.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.313176s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.06279e-05
PHY-3002 : Step(130): len = 52675.1, overlap = 54.25
PHY-3002 : Step(131): len = 52185.2, overlap = 55.75
PHY-3002 : Step(132): len = 51521.5, overlap = 56.5
PHY-3002 : Step(133): len = 51231.7, overlap = 57.75
PHY-3002 : Step(134): len = 50967.8, overlap = 59
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.12559e-05
PHY-3002 : Step(135): len = 51245.1, overlap = 59
PHY-3002 : Step(136): len = 52195, overlap = 56
PHY-3002 : Step(137): len = 52816.8, overlap = 53.5
PHY-3002 : Step(138): len = 52753.2, overlap = 53.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000122512
PHY-3002 : Step(139): len = 53351.5, overlap = 53.25
PHY-3002 : Step(140): len = 54073.4, overlap = 52.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.097649s wall, 0.046875s user + 0.062500s system = 0.109375s CPU (112.0%)

PHY-3001 : Trial Legalized: Len = 69450.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046550s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00101353
PHY-3002 : Step(141): len = 66176.2, overlap = 4.5
PHY-3002 : Step(142): len = 63810.6, overlap = 9.25
PHY-3002 : Step(143): len = 61834.4, overlap = 13
PHY-3002 : Step(144): len = 60722.4, overlap = 14.25
PHY-3002 : Step(145): len = 59969.9, overlap = 14.75
PHY-3002 : Step(146): len = 59372.8, overlap = 17.75
PHY-3002 : Step(147): len = 59059, overlap = 18.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00202706
PHY-3002 : Step(148): len = 59335.8, overlap = 19.25
PHY-3002 : Step(149): len = 59457, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00405412
PHY-3002 : Step(150): len = 59493.7, overlap = 18.75
PHY-3002 : Step(151): len = 59493.7, overlap = 18.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005190s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63857.7, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005874s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (266.0%)

PHY-3001 : 11 instances has been re-located, deltaX = 2, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 63917.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6310, tnet num: 1941, tinst num: 786, tnode num: 8490, tedge num: 11105.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 46/1943.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70016, over cnt = 154(0%), over = 240, worst = 7
PHY-1002 : len = 71176, over cnt = 83(0%), over = 104, worst = 4
PHY-1002 : len = 72288, over cnt = 15(0%), over = 19, worst = 3
PHY-1002 : len = 72584, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146638s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.6%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 22.78, top10 = 17.93, top15 = 14.01.
PHY-1001 : End incremental global routing;  0.195415s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058063s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.282311s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (105.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1723/1943.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72616, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005450s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 22.78, top10 = 17.93, top15 = 14.01.
OPT-1001 : End congestion update;  0.050131s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044995s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 748 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 786 instances, 739 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63948.4, Over = 0
PHY-3001 : End spreading;  0.004915s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63948.4, Over = 0
PHY-3001 : End incremental legalization;  0.034224s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.143694s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (97.9%)

OPT-1001 : Current memory(MB): used = 224, reserve = 187, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047523s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1719/1943.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72640, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72640, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024406s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (128.0%)

PHY-1001 : Congestion index: top1 = 32.03, top5 = 22.79, top10 = 17.94, top15 = 14.03.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045928s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.888375s wall, 0.890625s user + 0.015625s system = 0.906250s CPU (102.0%)

RUN-1003 : finish command "place" in  5.327872s wall, 8.468750s user + 2.890625s system = 11.359375s CPU (213.2%)

RUN-1004 : used memory is 201 MB, reserved memory is 164 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 788 instances
RUN-1001 : 369 mslices, 370 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1943 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1403 nets have 2 pins
RUN-1001 : 424 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6310, tnet num: 1941, tinst num: 786, tnode num: 8490, tedge num: 11105.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 369 mslices, 370 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69616, over cnt = 157(0%), over = 245, worst = 7
PHY-1002 : len = 70784, over cnt = 85(0%), over = 114, worst = 4
PHY-1002 : len = 71944, over cnt = 18(0%), over = 29, worst = 4
PHY-1002 : len = 72408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.139710s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (111.8%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 22.70, top10 = 17.90, top15 = 14.00.
PHY-1001 : End global routing;  0.188839s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (107.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 203, peak = 239.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 466, peak = 500.
PHY-1001 : End build detailed router design. 5.401293s wall, 5.343750s user + 0.062500s system = 5.406250s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32744, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.235733s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (100.6%)

PHY-1001 : Current memory(MB): used = 532, reserve = 499, peak = 532.
PHY-1001 : End phase 1; 2.243033s wall, 2.234375s user + 0.015625s system = 2.250000s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186968, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 500, peak = 533.
PHY-1001 : End initial routed; 2.151024s wall, 3.281250s user + 0.156250s system = 3.437500s CPU (159.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1717(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.162   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.369   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.669951s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 535, reserve = 501, peak = 535.
PHY-1001 : End phase 2; 2.821194s wall, 3.953125s user + 0.156250s system = 4.109375s CPU (145.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186968, over cnt = 34(0%), over = 34, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.033779s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186936, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.063914s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (146.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186944, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.036906s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (127.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 186944, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.041670s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (112.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1717(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.162   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.369   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.673566s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (99.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.328489s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End phase 3; 1.500272s wall, 1.484375s user + 0.046875s system = 1.531250s CPU (102.1%)

PHY-1003 : Routed, final wirelength = 186944
PHY-1001 : Current memory(MB): used = 549, reserve = 516, peak = 549.
PHY-1001 : End export database. 0.012115s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (129.0%)

PHY-1001 : End detail routing;  12.212223s wall, 13.250000s user + 0.296875s system = 13.546875s CPU (110.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6310, tnet num: 1941, tinst num: 786, tnode num: 8490, tedge num: 11105.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[30] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6356, tnet num: 1964, tinst num: 809, tnode num: 8536, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  5.911421s wall, 5.718750s user + 0.250000s system = 5.968750s CPU (101.0%)

RUN-1003 : finish command "route" in  18.675747s wall, 19.515625s user + 0.562500s system = 20.078125s CPU (107.5%)

RUN-1004 : used memory is 506 MB, reserved memory is 476 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      856   out of  19600    4.37%
#reg                      989   out of  19600    5.05%
#le                      1481
  #lut only               492   out of   1481   33.22%
  #reg only               625   out of   1481   42.20%
  #lut&reg                364   out of   1481   24.58%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         441
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1481   |644     |212     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1075   |330     |120     |836     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |37     |30      |7       |22      |0       |0       |
|    demodu                  |Demodulation                                     |477    |137     |44      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |35      |6       |48      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |6       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |18     |13      |0       |18      |0       |0       |
|    integ                   |Integration                                      |131    |20      |14      |105     |0       |0       |
|    modu                    |Modulation                                       |93     |25      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |307    |93      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |116    |109     |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |29      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |25     |25      |0       |13      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |55      |0       |29      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1390  
    #2          2       284   
    #3          3       128   
    #4          4        12   
    #5        5-10       80   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6356, tnet num: 1964, tinst num: 809, tnode num: 8536, tedge num: 11151.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1964 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 809
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1966, pip num: 14589
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1328 valid insts, and 38769 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.010020s wall, 19.937500s user + 0.171875s system = 20.109375s CPU (501.5%)

RUN-1004 : used memory is 522 MB, reserved memory is 490 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231025_164537.log"
