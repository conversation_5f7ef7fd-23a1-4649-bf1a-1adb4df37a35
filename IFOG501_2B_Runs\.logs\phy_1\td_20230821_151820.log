============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Aug 21 15:18:20 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1659 instances
RUN-0007 : 395 luts, 1009 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2199 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1641 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 80 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1657 instances, 395 luts, 1009 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7865, tnet num: 2197, tinst num: 1657, tnode num: 11127, tedge num: 13284.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.286763s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 633361
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1657.
PHY-3001 : End clustering;  0.000027s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 530449, overlap = 20.25
PHY-3002 : Step(2): len = 490602, overlap = 20.25
PHY-3002 : Step(3): len = 402470, overlap = 18
PHY-3002 : Step(4): len = 346308, overlap = 15.75
PHY-3002 : Step(5): len = 310128, overlap = 13.5
PHY-3002 : Step(6): len = 289049, overlap = 18
PHY-3002 : Step(7): len = 280955, overlap = 18
PHY-3002 : Step(8): len = 274906, overlap = 20.25
PHY-3002 : Step(9): len = 264169, overlap = 20.25
PHY-3002 : Step(10): len = 255484, overlap = 20.25
PHY-3002 : Step(11): len = 250427, overlap = 20.25
PHY-3002 : Step(12): len = 244666, overlap = 20.25
PHY-3002 : Step(13): len = 234103, overlap = 20.25
PHY-3002 : Step(14): len = 229792, overlap = 20.25
PHY-3002 : Step(15): len = 224888, overlap = 20.25
PHY-3002 : Step(16): len = 218416, overlap = 20.25
PHY-3002 : Step(17): len = 213188, overlap = 20.25
PHY-3002 : Step(18): len = 209406, overlap = 20.25
PHY-3002 : Step(19): len = 200048, overlap = 20.25
PHY-3002 : Step(20): len = 196676, overlap = 20.25
PHY-3002 : Step(21): len = 193026, overlap = 20.25
PHY-3002 : Step(22): len = 187906, overlap = 20.25
PHY-3002 : Step(23): len = 178782, overlap = 20.25
PHY-3002 : Step(24): len = 176509, overlap = 20.25
PHY-3002 : Step(25): len = 172215, overlap = 20.25
PHY-3002 : Step(26): len = 158678, overlap = 20.25
PHY-3002 : Step(27): len = 151894, overlap = 20.25
PHY-3002 : Step(28): len = 150916, overlap = 20.25
PHY-3002 : Step(29): len = 140730, overlap = 20.25
PHY-3002 : Step(30): len = 130367, overlap = 18
PHY-3002 : Step(31): len = 127079, overlap = 18
PHY-3002 : Step(32): len = 125057, overlap = 18
PHY-3002 : Step(33): len = 122807, overlap = 18
PHY-3002 : Step(34): len = 119118, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.09768e-05
PHY-3002 : Step(35): len = 120716, overlap = 15.75
PHY-3002 : Step(36): len = 119816, overlap = 13.5
PHY-3002 : Step(37): len = 117534, overlap = 15.75
PHY-3002 : Step(38): len = 115769, overlap = 18
PHY-3002 : Step(39): len = 111452, overlap = 13.5
PHY-3002 : Step(40): len = 109288, overlap = 9
PHY-3002 : Step(41): len = 106525, overlap = 11.25
PHY-3002 : Step(42): len = 104385, overlap = 11.25
PHY-3002 : Step(43): len = 101836, overlap = 15.75
PHY-3002 : Step(44): len = 100075, overlap = 11.25
PHY-3002 : Step(45): len = 96554.9, overlap = 11.25
PHY-3002 : Step(46): len = 95763.8, overlap = 11.25
PHY-3002 : Step(47): len = 93398.6, overlap = 11.25
PHY-3002 : Step(48): len = 90360.6, overlap = 11.25
PHY-3002 : Step(49): len = 87630, overlap = 6.75
PHY-3002 : Step(50): len = 87110.9, overlap = 9
PHY-3002 : Step(51): len = 85154.3, overlap = 9.375
PHY-3002 : Step(52): len = 84443.1, overlap = 9.4375
PHY-3002 : Step(53): len = 82578.7, overlap = 7.625
PHY-3002 : Step(54): len = 81232.9, overlap = 10.4688
PHY-3002 : Step(55): len = 79603, overlap = 10.4688
PHY-3002 : Step(56): len = 78602.3, overlap = 13.2812
PHY-3002 : Step(57): len = 77369.3, overlap = 8.625
PHY-3002 : Step(58): len = 75463.8, overlap = 13.125
PHY-3002 : Step(59): len = 73473.9, overlap = 13.0625
PHY-3002 : Step(60): len = 72911.1, overlap = 13.3125
PHY-3002 : Step(61): len = 72073.8, overlap = 13.375
PHY-3002 : Step(62): len = 71300, overlap = 13.625
PHY-3002 : Step(63): len = 71033.4, overlap = 13.4375
PHY-3002 : Step(64): len = 69873.5, overlap = 11.3125
PHY-3002 : Step(65): len = 69028.4, overlap = 9.1875
PHY-3002 : Step(66): len = 68485.9, overlap = 11.4375
PHY-3002 : Step(67): len = 67909.9, overlap = 9.1875
PHY-3002 : Step(68): len = 67332.1, overlap = 11.375
PHY-3002 : Step(69): len = 66032.9, overlap = 9
PHY-3002 : Step(70): len = 64707.5, overlap = 8.9375
PHY-3002 : Step(71): len = 64054.5, overlap = 11.0625
PHY-3002 : Step(72): len = 63430.5, overlap = 11.125
PHY-3002 : Step(73): len = 62837.2, overlap = 11.0625
PHY-3002 : Step(74): len = 62543.2, overlap = 13.3125
PHY-3002 : Step(75): len = 62223, overlap = 8.75
PHY-3002 : Step(76): len = 61758.7, overlap = 11.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000161954
PHY-3002 : Step(77): len = 61859.7, overlap = 8.9375
PHY-3002 : Step(78): len = 61926.5, overlap = 8.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000323907
PHY-3002 : Step(79): len = 61934.1, overlap = 8.9375
PHY-3002 : Step(80): len = 61911.7, overlap = 8.875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014009s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (334.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.119093s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (105.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000572744
PHY-3002 : Step(81): len = 65280.1, overlap = 7.78125
PHY-3002 : Step(82): len = 64401.4, overlap = 7.6875
PHY-3002 : Step(83): len = 63747, overlap = 7.5625
PHY-3002 : Step(84): len = 62692.9, overlap = 6.96875
PHY-3002 : Step(85): len = 61476.5, overlap = 6.6875
PHY-3002 : Step(86): len = 60292.8, overlap = 5.875
PHY-3002 : Step(87): len = 59027.3, overlap = 5.5625
PHY-3002 : Step(88): len = 58169.6, overlap = 5.53125
PHY-3002 : Step(89): len = 56709.8, overlap = 5.0625
PHY-3002 : Step(90): len = 55133.6, overlap = 5.375
PHY-3002 : Step(91): len = 53822.2, overlap = 8.4375
PHY-3002 : Step(92): len = 53164.7, overlap = 9.5625
PHY-3002 : Step(93): len = 52747.3, overlap = 10.0625
PHY-3002 : Step(94): len = 52275.9, overlap = 10
PHY-3002 : Step(95): len = 51638.7, overlap = 9.96875
PHY-3002 : Step(96): len = 51494.6, overlap = 10.1875
PHY-3002 : Step(97): len = 51544.9, overlap = 9.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.137418s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000195514
PHY-3002 : Step(98): len = 51861.8, overlap = 49.125
PHY-3002 : Step(99): len = 51924.6, overlap = 45.5625
PHY-3002 : Step(100): len = 52763.6, overlap = 44.7812
PHY-3002 : Step(101): len = 53753.4, overlap = 38.5312
PHY-3002 : Step(102): len = 54101.7, overlap = 38.1875
PHY-3002 : Step(103): len = 53999.4, overlap = 35.3125
PHY-3002 : Step(104): len = 53707.1, overlap = 35.6562
PHY-3002 : Step(105): len = 53520.5, overlap = 36.4375
PHY-3002 : Step(106): len = 53291.5, overlap = 37
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000391028
PHY-3002 : Step(107): len = 53315.7, overlap = 36.6562
PHY-3002 : Step(108): len = 53463.6, overlap = 36.0625
PHY-3002 : Step(109): len = 53511.4, overlap = 34.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000782057
PHY-3002 : Step(110): len = 53621.8, overlap = 33.9688
PHY-3002 : Step(111): len = 53673, overlap = 34.1562
PHY-3002 : Step(112): len = 53945.4, overlap = 33.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7865, tnet num: 2197, tinst num: 1657, tnode num: 11127, tedge num: 13284.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.22 peak overflow 2.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2199.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56944, over cnt = 235(0%), over = 945, worst = 24
PHY-1001 : End global iterations;  0.111079s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (239.1%)

PHY-1001 : Congestion index: top1 = 42.93, top5 = 24.72, top10 = 16.23, top15 = 11.56.
PHY-1001 : End incremental global routing;  0.198429s wall, 0.234375s user + 0.093750s system = 0.328125s CPU (165.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.117397s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (106.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.370840s wall, 0.406250s user + 0.093750s system = 0.500000s CPU (134.8%)

OPT-1001 : Current memory(MB): used = 212, reserve = 177, peak = 212.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1682/2199.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56944, over cnt = 235(0%), over = 945, worst = 24
PHY-1002 : len = 63848, over cnt = 160(0%), over = 359, worst = 14
PHY-1002 : len = 67208, over cnt = 35(0%), over = 49, worst = 6
PHY-1002 : len = 68152, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 68632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148925s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (125.9%)

PHY-1001 : Congestion index: top1 = 38.34, top5 = 25.01, top10 = 17.76, top15 = 13.16.
OPT-1001 : End congestion update;  0.219797s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (128.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.099977s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (93.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.326172s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (115.0%)

OPT-1001 : Current memory(MB): used = 215, reserve = 180, peak = 215.
OPT-1001 : End physical optimization;  1.218334s wall, 1.390625s user + 0.093750s system = 1.484375s CPU (121.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 395 LUT to BLE ...
SYN-4008 : Packed 395 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 96 SEQ with LUT/SLICE
SYN-4006 : 133 single LUT's are left
SYN-4006 : 731 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1126/1439 primitive instances ...
PHY-3001 : End packing;  0.088886s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 847 instances
RUN-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2031 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 426 nets have [3 - 5] pins
RUN-1001 : 82 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 845 instances, 798 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53813.4, Over = 65
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6621, tnet num: 2029, tinst num: 845, tnode num: 8969, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2029 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.498111s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.32942e-05
PHY-3002 : Step(113): len = 53337.1, overlap = 64
PHY-3002 : Step(114): len = 53208.4, overlap = 62.25
PHY-3002 : Step(115): len = 53187.8, overlap = 59.75
PHY-3002 : Step(116): len = 52849.5, overlap = 63.75
PHY-3002 : Step(117): len = 52723.1, overlap = 64.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.65884e-05
PHY-3002 : Step(118): len = 53255.3, overlap = 58.5
PHY-3002 : Step(119): len = 53611.7, overlap = 56.5
PHY-3002 : Step(120): len = 53929.7, overlap = 54
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000133177
PHY-3002 : Step(121): len = 54374, overlap = 51.75
PHY-3002 : Step(122): len = 54842.4, overlap = 51
PHY-3002 : Step(123): len = 55621.7, overlap = 44
PHY-3002 : Step(124): len = 56020.8, overlap = 42.5
PHY-3002 : Step(125): len = 56240.2, overlap = 43.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.194922s wall, 0.218750s user + 0.234375s system = 0.453125s CPU (232.5%)

PHY-3001 : Trial Legalized: Len = 68557.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2029 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.087660s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000919162
PHY-3002 : Step(126): len = 66342.6, overlap = 4
PHY-3002 : Step(127): len = 64087.4, overlap = 7.5
PHY-3002 : Step(128): len = 62041.7, overlap = 14.75
PHY-3002 : Step(129): len = 61117.5, overlap = 15.75
PHY-3002 : Step(130): len = 60362.5, overlap = 18
PHY-3002 : Step(131): len = 59892.7, overlap = 22
PHY-3002 : Step(132): len = 59627.5, overlap = 24.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00183832
PHY-3002 : Step(133): len = 59737.5, overlap = 24.5
PHY-3002 : Step(134): len = 59836.1, overlap = 23.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00367665
PHY-3002 : Step(135): len = 59949.4, overlap = 23.75
PHY-3002 : Step(136): len = 59949.4, overlap = 23.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.010142s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (154.1%)

PHY-3001 : Legalized: Len = 64229.3, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.010655s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.6%)

PHY-3001 : 12 instances has been re-located, deltaX = 4, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 64409.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6621, tnet num: 2029, tinst num: 845, tnode num: 8969, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 65/2031.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70472, over cnt = 134(0%), over = 183, worst = 7
PHY-1002 : len = 71264, over cnt = 77(0%), over = 84, worst = 3
PHY-1002 : len = 72248, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.176554s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (115.0%)

PHY-1001 : Congestion index: top1 = 31.36, top5 = 22.65, top10 = 17.35, top15 = 13.78.
PHY-1001 : End incremental global routing;  0.250737s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (112.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2029 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.087625s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.385681s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (109.4%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1797/2031.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009150s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.36, top5 = 22.65, top10 = 17.35, top15 = 13.78.
OPT-1001 : End congestion update;  0.072494s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2029 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071342s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 807 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 845 instances, 798 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64489.4, Over = 0
PHY-3001 : End spreading;  0.006779s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64489.4, Over = 0
PHY-3001 : End incremental legalization;  0.050661s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (92.5%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.212702s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (117.5%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2029 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070795s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1793/2031.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010850s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (144.0%)

PHY-1001 : Congestion index: top1 = 31.49, top5 = 22.68, top10 = 17.37, top15 = 13.79.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2029 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070255s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.317242s wall, 1.359375s user + 0.046875s system = 1.406250s CPU (106.8%)

RUN-1003 : finish command "place" in  8.343929s wall, 12.843750s user + 4.593750s system = 17.437500s CPU (209.0%)

RUN-1004 : used memory is 200 MB, reserved memory is 165 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 847 instances
RUN-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2031 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1481 nets have 2 pins
RUN-1001 : 426 nets have [3 - 5] pins
RUN-1001 : 82 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6621, tnet num: 2029, tinst num: 845, tnode num: 8969, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2029 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69944, over cnt = 133(0%), over = 183, worst = 7
PHY-1002 : len = 70720, over cnt = 80(0%), over = 92, worst = 2
PHY-1002 : len = 71728, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 71904, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.166408s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (131.5%)

PHY-1001 : Congestion index: top1 = 31.53, top5 = 22.59, top10 = 17.27, top15 = 13.71.
PHY-1001 : End global routing;  0.236281s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (125.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 206, peak = 240.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 502, reserve = 470, peak = 502.
PHY-1001 : End build detailed router design. 4.432868s wall, 4.390625s user + 0.062500s system = 4.453125s CPU (100.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33656, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.667001s wall, 1.656250s user + 0.015625s system = 1.671875s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End phase 1; 1.674635s wall, 1.656250s user + 0.015625s system = 1.671875s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 184704, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 536.
PHY-1001 : End initial routed; 1.436900s wall, 2.437500s user + 0.109375s system = 2.546875s CPU (177.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1812(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.263   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.481704s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (97.3%)

PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End phase 2; 1.918719s wall, 2.906250s user + 0.109375s system = 3.015625s CPU (157.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184704, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.020188s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (154.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184480, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.044241s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (141.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184528, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.031821s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (98.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1812(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.263   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.484195s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (103.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.236819s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 551, reserve = 521, peak = 551.
PHY-1001 : End phase 3; 0.976807s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (102.4%)

PHY-1003 : Routed, final wirelength = 184528
PHY-1001 : Current memory(MB): used = 552, reserve = 521, peak = 552.
PHY-1001 : End export database. 0.011738s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  9.250893s wall, 10.187500s user + 0.187500s system = 10.375000s CPU (112.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6621, tnet num: 2029, tinst num: 845, tnode num: 8969, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  10.469931s wall, 11.437500s user + 0.203125s system = 11.640625s CPU (111.2%)

RUN-1004 : used memory is 506 MB, reserved memory is 476 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      812   out of  19600    4.14%
#reg                     1074   out of  19600    5.48%
#le                      1543
  #lut only               469   out of   1543   30.40%
  #reg only               731   out of   1543   47.38%
  #lut&reg                343   out of   1543   22.23%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1543   |606     |206     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1136   |292     |122     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |29     |23      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |524    |115     |53      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |164    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |53     |0       |0       |53      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |24     |10      |0       |24      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |31     |18      |0       |31      |0       |0       |
|    integ                   |Integration                                      |138    |20      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |104    |23      |15      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |311    |86      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |127    |111     |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |25     |18      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |65     |65      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1445  
    #2          2       296   
    #3          3       116   
    #4          4        14   
    #5        5-10       86   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.97            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6621, tnet num: 2029, tinst num: 845, tnode num: 8969, tedge num: 11622.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2029 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 845
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2031, pip num: 14912
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1329 valid insts, and 39049 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  6.260218s wall, 33.578125s user + 0.125000s system = 33.703125s CPU (538.4%)

RUN-1004 : used memory is 521 MB, reserved memory is 490 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230821_151820.log"
