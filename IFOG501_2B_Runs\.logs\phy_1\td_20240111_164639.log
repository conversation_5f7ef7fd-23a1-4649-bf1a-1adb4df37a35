============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jan 11 16:46:39 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1526 instances
RUN-0007 : 369 luts, 901 seqs, 132 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2081 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1534 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 68 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1524 instances, 369 luts, 901 seqs, 207 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7372, tnet num: 2079, tinst num: 1524, tnode num: 10359, tedge num: 12530.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.317302s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (103.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 581562
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1524.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 507160, overlap = 15.75
PHY-3002 : Step(2): len = 473149, overlap = 15.75
PHY-3002 : Step(3): len = 452805, overlap = 15.75
PHY-3002 : Step(4): len = 441719, overlap = 15.75
PHY-3002 : Step(5): len = 426692, overlap = 15.75
PHY-3002 : Step(6): len = 411829, overlap = 13.5
PHY-3002 : Step(7): len = 400142, overlap = 18
PHY-3002 : Step(8): len = 392224, overlap = 18
PHY-3002 : Step(9): len = 377642, overlap = 15.75
PHY-3002 : Step(10): len = 369387, overlap = 20.25
PHY-3002 : Step(11): len = 361787, overlap = 18
PHY-3002 : Step(12): len = 352503, overlap = 20.25
PHY-3002 : Step(13): len = 342818, overlap = 18
PHY-3002 : Step(14): len = 336487, overlap = 20.25
PHY-3002 : Step(15): len = 328945, overlap = 18
PHY-3002 : Step(16): len = 318887, overlap = 20.25
PHY-3002 : Step(17): len = 309420, overlap = 18
PHY-3002 : Step(18): len = 304741, overlap = 18
PHY-3002 : Step(19): len = 293940, overlap = 15.75
PHY-3002 : Step(20): len = 281904, overlap = 18
PHY-3002 : Step(21): len = 276865, overlap = 18
PHY-3002 : Step(22): len = 271051, overlap = 18
PHY-3002 : Step(23): len = 248181, overlap = 18
PHY-3002 : Step(24): len = 239385, overlap = 18
PHY-3002 : Step(25): len = 236768, overlap = 18
PHY-3002 : Step(26): len = 226189, overlap = 15.75
PHY-3002 : Step(27): len = 210566, overlap = 15.75
PHY-3002 : Step(28): len = 206025, overlap = 15.75
PHY-3002 : Step(29): len = 203351, overlap = 15.75
PHY-3002 : Step(30): len = 174162, overlap = 15.75
PHY-3002 : Step(31): len = 165592, overlap = 15.75
PHY-3002 : Step(32): len = 164053, overlap = 15.75
PHY-3002 : Step(33): len = 156672, overlap = 13.5
PHY-3002 : Step(34): len = 151997, overlap = 13.5
PHY-3002 : Step(35): len = 149312, overlap = 15.75
PHY-3002 : Step(36): len = 145650, overlap = 13.5
PHY-3002 : Step(37): len = 141693, overlap = 15.75
PHY-3002 : Step(38): len = 139330, overlap = 15.75
PHY-3002 : Step(39): len = 133548, overlap = 15.75
PHY-3002 : Step(40): len = 129983, overlap = 15.75
PHY-3002 : Step(41): len = 128172, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000103972
PHY-3002 : Step(42): len = 127865, overlap = 13.5
PHY-3002 : Step(43): len = 127099, overlap = 15.75
PHY-3002 : Step(44): len = 126071, overlap = 13.5
PHY-3002 : Step(45): len = 124944, overlap = 15.75
PHY-3002 : Step(46): len = 123978, overlap = 18
PHY-3002 : Step(47): len = 120400, overlap = 13.5
PHY-3002 : Step(48): len = 115063, overlap = 11.25
PHY-3002 : Step(49): len = 113950, overlap = 11.25
PHY-3002 : Step(50): len = 112169, overlap = 11.25
PHY-3002 : Step(51): len = 108090, overlap = 13.5
PHY-3002 : Step(52): len = 107317, overlap = 13.5
PHY-3002 : Step(53): len = 105424, overlap = 13.5
PHY-3002 : Step(54): len = 102991, overlap = 11.25
PHY-3002 : Step(55): len = 102236, overlap = 13.5
PHY-3002 : Step(56): len = 100496, overlap = 13.5
PHY-3002 : Step(57): len = 97477.8, overlap = 13.5
PHY-3002 : Step(58): len = 94613.8, overlap = 15.75
PHY-3002 : Step(59): len = 93637.6, overlap = 15.8125
PHY-3002 : Step(60): len = 91580.7, overlap = 15.8125
PHY-3002 : Step(61): len = 90041.4, overlap = 11.25
PHY-3002 : Step(62): len = 88011.2, overlap = 11.25
PHY-3002 : Step(63): len = 85053.8, overlap = 9
PHY-3002 : Step(64): len = 82483.5, overlap = 11.4375
PHY-3002 : Step(65): len = 81861.9, overlap = 11.75
PHY-3002 : Step(66): len = 80376.4, overlap = 12.25
PHY-3002 : Step(67): len = 76514.3, overlap = 12.1875
PHY-3002 : Step(68): len = 75074.1, overlap = 14.6875
PHY-3002 : Step(69): len = 73919.7, overlap = 14.8125
PHY-3002 : Step(70): len = 73292.4, overlap = 14.9375
PHY-3002 : Step(71): len = 73111.6, overlap = 17.25
PHY-3002 : Step(72): len = 72509, overlap = 12.625
PHY-3002 : Step(73): len = 71925.2, overlap = 12.9375
PHY-3002 : Step(74): len = 71706.7, overlap = 13
PHY-3002 : Step(75): len = 70643.4, overlap = 13.375
PHY-3002 : Step(76): len = 69878.5, overlap = 11.625
PHY-3002 : Step(77): len = 68221, overlap = 14.0625
PHY-3002 : Step(78): len = 65791.6, overlap = 14.5
PHY-3002 : Step(79): len = 65755.5, overlap = 12.375
PHY-3002 : Step(80): len = 65265, overlap = 12.5625
PHY-3002 : Step(81): len = 64400.3, overlap = 14.6875
PHY-3002 : Step(82): len = 64027.2, overlap = 14.6875
PHY-3002 : Step(83): len = 63193.6, overlap = 14.75
PHY-3002 : Step(84): len = 62293.6, overlap = 17.0625
PHY-3002 : Step(85): len = 62316.5, overlap = 14.9375
PHY-3002 : Step(86): len = 61051.3, overlap = 12.125
PHY-3002 : Step(87): len = 60541.7, overlap = 14.3125
PHY-3002 : Step(88): len = 60087.7, overlap = 11.9375
PHY-3002 : Step(89): len = 59138.9, overlap = 13.875
PHY-3002 : Step(90): len = 58951.9, overlap = 15.75
PHY-3002 : Step(91): len = 58559.1, overlap = 11.1875
PHY-3002 : Step(92): len = 58183, overlap = 10.9375
PHY-3002 : Step(93): len = 58085.1, overlap = 10.75
PHY-3002 : Step(94): len = 57433.8, overlap = 10.5
PHY-3002 : Step(95): len = 56884.2, overlap = 10.3125
PHY-3002 : Step(96): len = 56305.1, overlap = 10.125
PHY-3002 : Step(97): len = 55733.1, overlap = 9.75
PHY-3002 : Step(98): len = 55602.2, overlap = 9.5625
PHY-3002 : Step(99): len = 55426.1, overlap = 9.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000207943
PHY-3002 : Step(100): len = 55552.6, overlap = 9.375
PHY-3002 : Step(101): len = 55550.7, overlap = 9.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000415886
PHY-3002 : Step(102): len = 55548.5, overlap = 10.375
PHY-3002 : Step(103): len = 55540.1, overlap = 10.3125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014658s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (106.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.115264s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (94.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000263128
PHY-3002 : Step(104): len = 59682, overlap = 18.2188
PHY-3002 : Step(105): len = 59672.6, overlap = 18.0312
PHY-3002 : Step(106): len = 58629.5, overlap = 18.2812
PHY-3002 : Step(107): len = 58776.8, overlap = 18.5938
PHY-3002 : Step(108): len = 58096.7, overlap = 19.0625
PHY-3002 : Step(109): len = 57618.5, overlap = 17.3125
PHY-3002 : Step(110): len = 57022.8, overlap = 18
PHY-3002 : Step(111): len = 56568.4, overlap = 18.8125
PHY-3002 : Step(112): len = 55734.4, overlap = 19.9688
PHY-3002 : Step(113): len = 54983.9, overlap = 21.625
PHY-3002 : Step(114): len = 53806, overlap = 22.2188
PHY-3002 : Step(115): len = 52835.7, overlap = 20.3438
PHY-3002 : Step(116): len = 52439.5, overlap = 20.5312
PHY-3002 : Step(117): len = 51807.7, overlap = 20.6562
PHY-3002 : Step(118): len = 51655.8, overlap = 20.25
PHY-3002 : Step(119): len = 50956.3, overlap = 19.9062
PHY-3002 : Step(120): len = 50636.2, overlap = 19.2812
PHY-3002 : Step(121): len = 50263.5, overlap = 18.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000526256
PHY-3002 : Step(122): len = 50042.3, overlap = 18.5312
PHY-3002 : Step(123): len = 50002.3, overlap = 17.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00105251
PHY-3002 : Step(124): len = 50074.6, overlap = 18.2812
PHY-3002 : Step(125): len = 50175.5, overlap = 17.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.109563s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.74513e-05
PHY-3002 : Step(126): len = 50302.6, overlap = 46.2812
PHY-3002 : Step(127): len = 51027.9, overlap = 44.3125
PHY-3002 : Step(128): len = 51505.1, overlap = 44.7188
PHY-3002 : Step(129): len = 51262.2, overlap = 43.4062
PHY-3002 : Step(130): len = 50921.2, overlap = 43.6875
PHY-3002 : Step(131): len = 50682.5, overlap = 43.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000154903
PHY-3002 : Step(132): len = 50731, overlap = 40.9375
PHY-3002 : Step(133): len = 52058.1, overlap = 40.75
PHY-3002 : Step(134): len = 52722.1, overlap = 40.5938
PHY-3002 : Step(135): len = 52525.4, overlap = 38.5312
PHY-3002 : Step(136): len = 52191.3, overlap = 39.2812
PHY-3002 : Step(137): len = 52072.3, overlap = 39.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000309805
PHY-3002 : Step(138): len = 52267.6, overlap = 38.4375
PHY-3002 : Step(139): len = 52752.7, overlap = 39.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00061961
PHY-3002 : Step(140): len = 53032.8, overlap = 37
PHY-3002 : Step(141): len = 53337.7, overlap = 33.2812
PHY-3002 : Step(142): len = 54287.2, overlap = 29.9688
PHY-3002 : Step(143): len = 54639.9, overlap = 28.8125
PHY-3002 : Step(144): len = 54356.7, overlap = 26.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7372, tnet num: 2079, tinst num: 1524, tnode num: 10359, tedge num: 12530.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.38 peak overflow 2.97
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2081.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56488, over cnt = 244(0%), over = 944, worst = 17
PHY-1001 : End global iterations;  0.136648s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (160.1%)

PHY-1001 : Congestion index: top1 = 42.65, top5 = 25.85, top10 = 16.34, top15 = 11.51.
PHY-1001 : End incremental global routing;  0.211608s wall, 0.250000s user + 0.046875s system = 0.296875s CPU (140.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.093283s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (83.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.348375s wall, 0.375000s user + 0.046875s system = 0.421875s CPU (121.1%)

OPT-1001 : Current memory(MB): used = 210, reserve = 173, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1582/2081.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56488, over cnt = 244(0%), over = 944, worst = 17
PHY-1002 : len = 62064, over cnt = 157(0%), over = 371, worst = 17
PHY-1002 : len = 67272, over cnt = 24(0%), over = 28, worst = 3
PHY-1002 : len = 67552, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 67768, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.141092s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (110.7%)

PHY-1001 : Congestion index: top1 = 36.59, top5 = 25.14, top10 = 18.14, top15 = 13.29.
OPT-1001 : End congestion update;  0.201254s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (108.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2079 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080628s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.286223s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (109.2%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : End physical optimization;  1.091293s wall, 1.125000s user + 0.062500s system = 1.187500s CPU (108.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 165 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 115 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 621 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 990/1280 primitive instances ...
PHY-3001 : End packing;  0.066860s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 779 instances
RUN-1001 : 365 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1924 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1383 nets have 2 pins
RUN-1001 : 427 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 777 instances, 730 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54067.4, Over = 54.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6248, tnet num: 1922, tinst num: 777, tnode num: 8421, tedge num: 11026.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.441161s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.23844e-05
PHY-3002 : Step(145): len = 52966.8, overlap = 53.75
PHY-3002 : Step(146): len = 52057.3, overlap = 52.25
PHY-3002 : Step(147): len = 51794.7, overlap = 51.25
PHY-3002 : Step(148): len = 51953.4, overlap = 50.75
PHY-3002 : Step(149): len = 51766, overlap = 51.75
PHY-3002 : Step(150): len = 51581.1, overlap = 51
PHY-3002 : Step(151): len = 51439.1, overlap = 53.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.47688e-05
PHY-3002 : Step(152): len = 51678.3, overlap = 52.5
PHY-3002 : Step(153): len = 52105, overlap = 49
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000125711
PHY-3002 : Step(154): len = 53668, overlap = 48
PHY-3002 : Step(155): len = 53991.9, overlap = 47.5
PHY-3002 : Step(156): len = 54500.2, overlap = 48.75
PHY-3002 : Step(157): len = 54788.4, overlap = 48
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.146101s wall, 0.109375s user + 0.234375s system = 0.343750s CPU (235.3%)

PHY-3001 : Trial Legalized: Len = 70332.9
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.077902s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00113662
PHY-3002 : Step(158): len = 66699.7, overlap = 7.75
PHY-3002 : Step(159): len = 65369.6, overlap = 11.5
PHY-3002 : Step(160): len = 62880.3, overlap = 13
PHY-3002 : Step(161): len = 61420.2, overlap = 14.5
PHY-3002 : Step(162): len = 60863.4, overlap = 17.25
PHY-3002 : Step(163): len = 60347.7, overlap = 15.5
PHY-3002 : Step(164): len = 59959, overlap = 15
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.002267
PHY-3002 : Step(165): len = 60165.3, overlap = 15.25
PHY-3002 : Step(166): len = 60308, overlap = 15.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.004534
PHY-3002 : Step(167): len = 60321.3, overlap = 15
PHY-3002 : Step(168): len = 60317.6, overlap = 15.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009886s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64389.7, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.010989s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (142.2%)

PHY-3001 : 14 instances has been re-located, deltaX = 6, deltaY = 11, maxDist = 2.
PHY-3001 : Final: Len = 64633.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6248, tnet num: 1922, tinst num: 777, tnode num: 8421, tedge num: 11026.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 49/1924.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70896, over cnt = 128(0%), over = 161, worst = 5
PHY-1002 : len = 71344, over cnt = 61(0%), over = 71, worst = 2
PHY-1002 : len = 72160, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 72272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.200162s wall, 0.250000s user + 0.062500s system = 0.312500s CPU (156.1%)

PHY-1001 : Congestion index: top1 = 30.93, top5 = 22.53, top10 = 17.85, top15 = 13.93.
PHY-1001 : End incremental global routing;  0.273554s wall, 0.312500s user + 0.062500s system = 0.375000s CPU (137.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.082880s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (113.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.399705s wall, 0.437500s user + 0.062500s system = 0.500000s CPU (125.1%)

OPT-1001 : Current memory(MB): used = 216, reserve = 180, peak = 216.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1677/1924.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72272, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008423s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.93, top5 = 22.53, top10 = 17.85, top15 = 13.93.
OPT-1001 : End congestion update;  0.070063s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064201s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 739 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 777 instances, 730 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64625.4, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.010417s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 1, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 64657.4, Over = 0
PHY-3001 : End incremental legalization;  0.052180s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (479.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.207084s wall, 0.390625s user + 0.015625s system = 0.406250s CPU (196.2%)

OPT-1001 : Current memory(MB): used = 221, reserve = 184, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074108s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1665/1924.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72312, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010983s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.84, top5 = 22.45, top10 = 17.83, top15 = 13.92.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066043s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.448276
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.294682s wall, 1.500000s user + 0.078125s system = 1.578125s CPU (121.9%)

RUN-1003 : finish command "place" in  9.493557s wall, 15.203125s user + 5.828125s system = 21.031250s CPU (221.5%)

RUN-1004 : used memory is 198 MB, reserved memory is 161 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 779 instances
RUN-1001 : 365 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1924 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1383 nets have 2 pins
RUN-1001 : 427 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6248, tnet num: 1922, tinst num: 777, tnode num: 8421, tedge num: 11026.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 365 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70064, over cnt = 138(0%), over = 174, worst = 5
PHY-1002 : len = 70496, over cnt = 76(0%), over = 88, worst = 2
PHY-1002 : len = 71280, over cnt = 20(0%), over = 25, worst = 2
PHY-1002 : len = 71624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.220297s wall, 0.218750s user + 0.062500s system = 0.281250s CPU (127.7%)

PHY-1001 : Congestion index: top1 = 30.52, top5 = 22.29, top10 = 17.67, top15 = 13.80.
PHY-1001 : End global routing;  0.299249s wall, 0.281250s user + 0.062500s system = 0.343750s CPU (114.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 201, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 496, reserve = 463, peak = 496.
PHY-1001 : End build detailed router design. 4.713986s wall, 4.562500s user + 0.187500s system = 4.750000s CPU (100.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33456, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.646324s wall, 1.625000s user + 0.015625s system = 1.640625s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 528, reserve = 498, peak = 528.
PHY-1001 : End phase 1; 1.652755s wall, 1.640625s user + 0.015625s system = 1.656250s CPU (100.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183704, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 498, peak = 529.
PHY-1001 : End initial routed; 1.848268s wall, 2.859375s user + 0.156250s system = 3.015625s CPU (163.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1702(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.340   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.543   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.473924s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 531, reserve = 500, peak = 531.
PHY-1001 : End phase 2; 2.322361s wall, 3.312500s user + 0.171875s system = 3.484375s CPU (150.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183704, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018063s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (173.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 183512, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.036682s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (213.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 183560, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021763s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (71.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1702(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.340   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.543   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.455379s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (102.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.219552s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 546, reserve = 514, peak = 546.
PHY-1001 : End phase 3; 0.909966s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (104.7%)

PHY-1003 : Routed, final wirelength = 183560
PHY-1001 : Current memory(MB): used = 546, reserve = 515, peak = 546.
PHY-1001 : End export database. 0.010428s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  9.829609s wall, 10.687500s user + 0.375000s system = 11.062500s CPU (112.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6248, tnet num: 1922, tinst num: 777, tnode num: 8421, tedge num: 11026.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[21] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[23] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[24] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[6] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6292, tnet num: 1944, tinst num: 799, tnode num: 8465, tedge num: 11070.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.873670s wall, 3.812500s user + 0.250000s system = 4.062500s CPU (104.9%)

RUN-1003 : finish command "route" in  14.503078s wall, 15.281250s user + 0.687500s system = 15.968750s CPU (110.1%)

RUN-1004 : used memory is 538 MB, reserved memory is 508 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      830   out of  19600    4.23%
#reg                      990   out of  19600    5.05%
#le                      1451
  #lut only               461   out of   1451   31.77%
  #reg only               621   out of   1451   42.80%
  #lut&reg                369   out of   1451   25.43%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         436
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1451   |623     |207     |1021    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1063   |326     |117     |839     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |46     |37      |9       |21      |0       |0       |
|    demodu                  |Demodulation                                     |457    |121     |39      |346     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |30      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |9       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|    integ                   |Integration                                      |136    |19      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |93     |34      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |306    |95      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |20      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |101    |93      |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |30     |23      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |20     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |51     |51      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1369  
    #2          2       282   
    #3          3       132   
    #4          4        13   
    #5        5-10       78   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6292, tnet num: 1944, tinst num: 799, tnode num: 8465, tedge num: 11070.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1944 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 799
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1946, pip num: 14489
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1321 valid insts, and 38403 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.595144s wall, 29.296875s user + 0.046875s system = 29.343750s CPU (524.5%)

RUN-1004 : used memory is 545 MB, reserved memory is 511 MB, peak memory is 680 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240111_164639.log"
