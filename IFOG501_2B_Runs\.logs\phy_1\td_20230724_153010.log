============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 15:30:10 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1624 instances
RUN-0007 : 369 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2194 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1632 nets have 2 pins
RUN-1001 : 448 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1622 instances, 369 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2192, tinst num: 1622, tnode num: 11021, tedge num: 13163.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.296436s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 596342
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1622.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 475907, overlap = 20.25
PHY-3002 : Step(2): len = 439928, overlap = 20.25
PHY-3002 : Step(3): len = 395285, overlap = 20.25
PHY-3002 : Step(4): len = 368613, overlap = 20.25
PHY-3002 : Step(5): len = 359666, overlap = 20.25
PHY-3002 : Step(6): len = 350387, overlap = 20.25
PHY-3002 : Step(7): len = 329931, overlap = 18
PHY-3002 : Step(8): len = 300641, overlap = 20.25
PHY-3002 : Step(9): len = 296371, overlap = 20.25
PHY-3002 : Step(10): len = 291442, overlap = 20.25
PHY-3002 : Step(11): len = 278955, overlap = 20.25
PHY-3002 : Step(12): len = 267336, overlap = 20.25
PHY-3002 : Step(13): len = 264028, overlap = 20.25
PHY-3002 : Step(14): len = 255053, overlap = 20.25
PHY-3002 : Step(15): len = 242657, overlap = 20.25
PHY-3002 : Step(16): len = 237920, overlap = 20.25
PHY-3002 : Step(17): len = 233526, overlap = 20.25
PHY-3002 : Step(18): len = 225880, overlap = 20.25
PHY-3002 : Step(19): len = 220088, overlap = 20.25
PHY-3002 : Step(20): len = 216897, overlap = 20.25
PHY-3002 : Step(21): len = 211853, overlap = 20.25
PHY-3002 : Step(22): len = 204919, overlap = 20.25
PHY-3002 : Step(23): len = 200408, overlap = 20.25
PHY-3002 : Step(24): len = 197306, overlap = 20.25
PHY-3002 : Step(25): len = 192295, overlap = 20.25
PHY-3002 : Step(26): len = 186073, overlap = 20.25
PHY-3002 : Step(27): len = 182945, overlap = 20.25
PHY-3002 : Step(28): len = 178890, overlap = 20.25
PHY-3002 : Step(29): len = 174156, overlap = 20.25
PHY-3002 : Step(30): len = 168944, overlap = 20.25
PHY-3002 : Step(31): len = 166402, overlap = 20.25
PHY-3002 : Step(32): len = 160552, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000140148
PHY-3002 : Step(33): len = 162362, overlap = 13.5
PHY-3002 : Step(34): len = 160640, overlap = 13.5
PHY-3002 : Step(35): len = 157976, overlap = 18
PHY-3002 : Step(36): len = 155947, overlap = 15.75
PHY-3002 : Step(37): len = 149239, overlap = 11.25
PHY-3002 : Step(38): len = 143910, overlap = 11.25
PHY-3002 : Step(39): len = 140902, overlap = 15.75
PHY-3002 : Step(40): len = 138826, overlap = 9
PHY-3002 : Step(41): len = 134208, overlap = 11.25
PHY-3002 : Step(42): len = 127730, overlap = 11.25
PHY-3002 : Step(43): len = 124154, overlap = 9
PHY-3002 : Step(44): len = 123497, overlap = 11.25
PHY-3002 : Step(45): len = 120300, overlap = 11.25
PHY-3002 : Step(46): len = 117865, overlap = 11.25
PHY-3002 : Step(47): len = 114948, overlap = 9
PHY-3002 : Step(48): len = 113412, overlap = 9
PHY-3002 : Step(49): len = 111500, overlap = 9
PHY-3002 : Step(50): len = 106637, overlap = 9
PHY-3002 : Step(51): len = 101275, overlap = 9
PHY-3002 : Step(52): len = 101015, overlap = 9
PHY-3002 : Step(53): len = 98094.5, overlap = 11.25
PHY-3002 : Step(54): len = 97073.6, overlap = 13.5
PHY-3002 : Step(55): len = 96121.9, overlap = 11.25
PHY-3002 : Step(56): len = 94781, overlap = 9
PHY-3002 : Step(57): len = 91402.2, overlap = 11.25
PHY-3002 : Step(58): len = 90209.9, overlap = 11.25
PHY-3002 : Step(59): len = 88486.3, overlap = 15.75
PHY-3002 : Step(60): len = 87484.8, overlap = 9
PHY-3002 : Step(61): len = 84760.5, overlap = 9.625
PHY-3002 : Step(62): len = 83887.7, overlap = 9.75
PHY-3002 : Step(63): len = 82193.5, overlap = 9.75
PHY-3002 : Step(64): len = 80603.3, overlap = 14.3125
PHY-3002 : Step(65): len = 78832.4, overlap = 12.125
PHY-3002 : Step(66): len = 78342.3, overlap = 12.1875
PHY-3002 : Step(67): len = 76627.8, overlap = 9.75
PHY-3002 : Step(68): len = 75655.4, overlap = 10
PHY-3002 : Step(69): len = 73495.2, overlap = 10
PHY-3002 : Step(70): len = 72553.9, overlap = 14.375
PHY-3002 : Step(71): len = 71441.7, overlap = 14.125
PHY-3002 : Step(72): len = 70791.3, overlap = 11.875
PHY-3002 : Step(73): len = 67535.3, overlap = 12.1875
PHY-3002 : Step(74): len = 66370.4, overlap = 9.5625
PHY-3002 : Step(75): len = 65192.8, overlap = 9.5
PHY-3002 : Step(76): len = 65024.3, overlap = 11.625
PHY-3002 : Step(77): len = 64082.5, overlap = 13.9375
PHY-3002 : Step(78): len = 62583, overlap = 11.9375
PHY-3002 : Step(79): len = 61450, overlap = 10
PHY-3002 : Step(80): len = 61440.1, overlap = 10.0625
PHY-3002 : Step(81): len = 61237.7, overlap = 10.125
PHY-3002 : Step(82): len = 61114.1, overlap = 14.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000280296
PHY-3002 : Step(83): len = 61092.9, overlap = 12.375
PHY-3002 : Step(84): len = 61060, overlap = 12.125
PHY-3002 : Step(85): len = 60842.3, overlap = 12.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000560593
PHY-3002 : Step(86): len = 60933.1, overlap = 12.25
PHY-3002 : Step(87): len = 60945.4, overlap = 12.3125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005508s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (283.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.080001s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00566397
PHY-3002 : Step(88): len = 63211.7, overlap = 11.1875
PHY-3002 : Step(89): len = 62339.2, overlap = 11.3125
PHY-3002 : Step(90): len = 61515, overlap = 11.1875
PHY-3002 : Step(91): len = 59667.7, overlap = 11.625
PHY-3002 : Step(92): len = 58517.1, overlap = 11.1562
PHY-3002 : Step(93): len = 57162.5, overlap = 12.3125
PHY-3002 : Step(94): len = 55649.8, overlap = 11.625
PHY-3002 : Step(95): len = 54854.3, overlap = 12.0938
PHY-3002 : Step(96): len = 52769.9, overlap = 13.5938
PHY-3002 : Step(97): len = 51424.7, overlap = 14.875
PHY-3002 : Step(98): len = 50598.3, overlap = 15.7188
PHY-3002 : Step(99): len = 50262.4, overlap = 16.7188
PHY-3002 : Step(100): len = 49737, overlap = 15.4062
PHY-3002 : Step(101): len = 49237.5, overlap = 15.5
PHY-3002 : Step(102): len = 48870.2, overlap = 15.875
PHY-3002 : Step(103): len = 48730.1, overlap = 16.3438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065646s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000149111
PHY-3002 : Step(104): len = 49606, overlap = 50.9688
PHY-3002 : Step(105): len = 49960.1, overlap = 54.5938
PHY-3002 : Step(106): len = 50581.1, overlap = 49.5
PHY-3002 : Step(107): len = 51282.5, overlap = 45.2188
PHY-3002 : Step(108): len = 51723.9, overlap = 40.9375
PHY-3002 : Step(109): len = 51486.2, overlap = 40.8438
PHY-3002 : Step(110): len = 51292.8, overlap = 40.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000298223
PHY-3002 : Step(111): len = 51616.4, overlap = 40.0938
PHY-3002 : Step(112): len = 53137.4, overlap = 36.9688
PHY-3002 : Step(113): len = 53547.1, overlap = 34.875
PHY-3002 : Step(114): len = 52811.1, overlap = 34.7188
PHY-3002 : Step(115): len = 52811.1, overlap = 34.7188
PHY-3002 : Step(116): len = 52431.7, overlap = 33.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000596446
PHY-3002 : Step(117): len = 52627, overlap = 34.125
PHY-3002 : Step(118): len = 52760.9, overlap = 33.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2192, tinst num: 1622, tnode num: 11021, tedge num: 13163.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 94.34 peak overflow 3.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2194.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56048, over cnt = 248(0%), over = 945, worst = 18
PHY-1001 : End global iterations;  0.079836s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (117.4%)

PHY-1001 : Congestion index: top1 = 41.31, top5 = 24.47, top10 = 15.97, top15 = 11.42.
PHY-1001 : End incremental global routing;  0.131985s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (106.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073967s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.238532s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (104.8%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1703/2194.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56048, over cnt = 248(0%), over = 945, worst = 18
PHY-1002 : len = 63160, over cnt = 150(0%), over = 296, worst = 18
PHY-1002 : len = 66200, over cnt = 31(0%), over = 54, worst = 5
PHY-1002 : len = 66608, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 67024, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096793s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (145.3%)

PHY-1001 : Congestion index: top1 = 37.91, top5 = 24.87, top10 = 17.78, top15 = 13.02.
OPT-1001 : End congestion update;  0.143416s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (130.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061657s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.208066s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (127.7%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.743875s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (109.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 95 SEQ with LUT/SLICE
SYN-4006 : 111 single LUT's are left
SYN-4006 : 708 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1077/1405 primitive instances ...
PHY-3001 : End packing;  0.053831s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 840 instances
RUN-1001 : 396 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 838 instances, 791 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52710.6, Over = 59
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6553, tnet num: 2024, tinst num: 838, tnode num: 8903, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.331255s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (103.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.83856e-05
PHY-3002 : Step(119): len = 52132.2, overlap = 58.25
PHY-3002 : Step(120): len = 51116.2, overlap = 59.75
PHY-3002 : Step(121): len = 51163.1, overlap = 62.75
PHY-3002 : Step(122): len = 51159.2, overlap = 62.25
PHY-3002 : Step(123): len = 50982.1, overlap = 63.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.67713e-05
PHY-3002 : Step(124): len = 51141, overlap = 61.25
PHY-3002 : Step(125): len = 51425.8, overlap = 59.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000113543
PHY-3002 : Step(126): len = 52031.4, overlap = 57.25
PHY-3002 : Step(127): len = 53923.6, overlap = 53.5
PHY-3002 : Step(128): len = 54367.2, overlap = 51.5
PHY-3002 : Step(129): len = 54575.5, overlap = 49.75
PHY-3002 : Step(130): len = 54717.2, overlap = 48.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.113016s wall, 0.093750s user + 0.078125s system = 0.171875s CPU (152.1%)

PHY-3001 : Trial Legalized: Len = 69329
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057863s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00105986
PHY-3002 : Step(131): len = 66270.8, overlap = 6
PHY-3002 : Step(132): len = 64163.2, overlap = 10.25
PHY-3002 : Step(133): len = 62280.6, overlap = 14
PHY-3002 : Step(134): len = 60865.6, overlap = 17.25
PHY-3002 : Step(135): len = 60060.3, overlap = 16.75
PHY-3002 : Step(136): len = 59439.5, overlap = 21.25
PHY-3002 : Step(137): len = 59029.8, overlap = 22
PHY-3002 : Step(138): len = 58779.1, overlap = 23
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00211972
PHY-3002 : Step(139): len = 59026, overlap = 22.75
PHY-3002 : Step(140): len = 59099.5, overlap = 21.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00423945
PHY-3002 : Step(141): len = 59241.8, overlap = 21.5
PHY-3002 : Step(142): len = 59241.8, overlap = 21.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008570s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (182.3%)

PHY-3001 : Legalized: Len = 64162.4, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.008495s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (183.9%)

PHY-3001 : 7 instances has been re-located, deltaX = 2, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 64218.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6553, tnet num: 2024, tinst num: 838, tnode num: 8903, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 101/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70656, over cnt = 147(0%), over = 193, worst = 4
PHY-1002 : len = 71304, over cnt = 58(0%), over = 69, worst = 4
PHY-1002 : len = 72080, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133982s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (128.3%)

PHY-1001 : Congestion index: top1 = 31.25, top5 = 22.37, top10 = 17.43, top15 = 13.82.
PHY-1001 : End incremental global routing;  0.188826s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (115.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067838s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.288218s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (108.4%)

OPT-1001 : Current memory(MB): used = 219, reserve = 185, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1779/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006760s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (231.2%)

PHY-1001 : Congestion index: top1 = 31.25, top5 = 22.37, top10 = 17.43, top15 = 13.82.
OPT-1001 : End congestion update;  0.059081s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057090s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 800 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 838 instances, 791 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64287.6, Over = 0
PHY-3001 : End spreading;  0.005920s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64287.6, Over = 0
PHY-3001 : End incremental legalization;  0.039142s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (119.8%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.169446s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (101.4%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056831s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1767/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72144, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 72152, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.027778s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (281.3%)

PHY-1001 : Congestion index: top1 = 31.34, top5 = 22.39, top10 = 17.46, top15 = 13.83.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055779s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.962646s wall, 1.000000s user + 0.046875s system = 1.046875s CPU (108.7%)

RUN-1003 : finish command "place" in  5.688287s wall, 9.062500s user + 2.718750s system = 11.781250s CPU (207.1%)

RUN-1004 : used memory is 202 MB, reserved memory is 167 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 840 instances
RUN-1001 : 396 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6553, tnet num: 2024, tinst num: 838, tnode num: 8903, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70008, over cnt = 147(0%), over = 201, worst = 4
PHY-1002 : len = 70888, over cnt = 57(0%), over = 68, worst = 4
PHY-1002 : len = 71728, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71728, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.140055s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (156.2%)

PHY-1001 : Congestion index: top1 = 31.27, top5 = 22.34, top10 = 17.40, top15 = 13.73.
PHY-1001 : End global routing;  0.194958s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (144.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 206, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 498, reserve = 467, peak = 498.
PHY-1001 : End build detailed router design. 3.444295s wall, 3.296875s user + 0.031250s system = 3.328125s CPU (96.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34224, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.465616s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.472106s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184176, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End initial routed; 1.217335s wall, 2.156250s user + 0.062500s system = 2.218750s CPU (182.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.488   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.393391s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.610829s wall, 2.546875s user + 0.062500s system = 2.609375s CPU (162.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184176, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016115s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184120, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028114s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (55.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184128, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020160s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (77.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.488   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.384416s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.189722s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.8%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.766764s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.9%)

PHY-1003 : Routed, final wirelength = 184128
PHY-1001 : Current memory(MB): used = 550, reserve = 520, peak = 550.
PHY-1001 : End export database. 0.011342s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (137.8%)

PHY-1001 : End detail routing;  7.503656s wall, 8.265625s user + 0.109375s system = 8.375000s CPU (111.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6553, tnet num: 2024, tinst num: 838, tnode num: 8903, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.474727s wall, 9.312500s user + 0.125000s system = 9.437500s CPU (111.4%)

RUN-1004 : used memory is 503 MB, reserved memory is 472 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      814   out of  19600    4.15%
#reg                     1074   out of  19600    5.48%
#le                      1522
  #lut only               448   out of   1522   29.43%
  #reg only               708   out of   1522   46.52%
  #lut&reg                366   out of   1522   24.05%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1522   |593     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1117   |290     |128     |921     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |32     |26      |6       |23      |0       |0       |
|    demodu                  |Demodulation                                     |519    |112     |53      |428     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |0       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |15      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |92     |30      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |307    |81      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |110    |94      |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |22     |18      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |52     |48      |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1435  
    #2          2       316   
    #3          3       108   
    #4          4        17   
    #5        5-10       77   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6553, tnet num: 2024, tinst num: 838, tnode num: 8903, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 838
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2026, pip num: 14705
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1376 valid insts, and 38841 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.693850s wall, 19.921875s user + 0.093750s system = 20.015625s CPU (541.9%)

RUN-1004 : used memory is 519 MB, reserved memory is 490 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_153010.log"
