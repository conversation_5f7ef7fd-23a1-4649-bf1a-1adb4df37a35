============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 11:53:21 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1628 instances
RUN-0007 : 369 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2198 nets
RUN-1001 : 1638 nets have 2 pins
RUN-1001 : 448 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1626 instances, 369 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7785, tnet num: 2196, tinst num: 1626, tnode num: 11025, tedge num: 13162.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.287687s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (103.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 588746
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1626.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 557641, overlap = 20.25
PHY-3002 : Step(2): len = 440734, overlap = 15.75
PHY-3002 : Step(3): len = 374028, overlap = 15.75
PHY-3002 : Step(4): len = 354415, overlap = 11.25
PHY-3002 : Step(5): len = 337511, overlap = 15.75
PHY-3002 : Step(6): len = 330639, overlap = 15.75
PHY-3002 : Step(7): len = 321239, overlap = 15.75
PHY-3002 : Step(8): len = 314059, overlap = 15.75
PHY-3002 : Step(9): len = 306110, overlap = 18
PHY-3002 : Step(10): len = 300117, overlap = 18
PHY-3002 : Step(11): len = 291879, overlap = 18
PHY-3002 : Step(12): len = 287325, overlap = 20.25
PHY-3002 : Step(13): len = 279396, overlap = 20.25
PHY-3002 : Step(14): len = 274206, overlap = 20.25
PHY-3002 : Step(15): len = 268776, overlap = 20.25
PHY-3002 : Step(16): len = 263806, overlap = 20.25
PHY-3002 : Step(17): len = 254865, overlap = 20.25
PHY-3002 : Step(18): len = 250906, overlap = 20.25
PHY-3002 : Step(19): len = 245974, overlap = 20.25
PHY-3002 : Step(20): len = 240586, overlap = 20.25
PHY-3002 : Step(21): len = 234200, overlap = 20.25
PHY-3002 : Step(22): len = 231159, overlap = 20.25
PHY-3002 : Step(23): len = 224278, overlap = 20.25
PHY-3002 : Step(24): len = 219658, overlap = 20.25
PHY-3002 : Step(25): len = 215328, overlap = 20.25
PHY-3002 : Step(26): len = 211501, overlap = 20.25
PHY-3002 : Step(27): len = 202711, overlap = 20.25
PHY-3002 : Step(28): len = 198898, overlap = 20.25
PHY-3002 : Step(29): len = 195933, overlap = 20.25
PHY-3002 : Step(30): len = 188116, overlap = 20.25
PHY-3002 : Step(31): len = 175964, overlap = 20.25
PHY-3002 : Step(32): len = 174057, overlap = 20.25
PHY-3002 : Step(33): len = 169827, overlap = 20.25
PHY-3002 : Step(34): len = 136049, overlap = 20.25
PHY-3002 : Step(35): len = 128612, overlap = 20.25
PHY-3002 : Step(36): len = 127865, overlap = 20.25
PHY-3002 : Step(37): len = 123837, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.92866e-05
PHY-3002 : Step(38): len = 124631, overlap = 11.25
PHY-3002 : Step(39): len = 123425, overlap = 9
PHY-3002 : Step(40): len = 121416, overlap = 13.5
PHY-3002 : Step(41): len = 118036, overlap = 15.75
PHY-3002 : Step(42): len = 116456, overlap = 6.75
PHY-3002 : Step(43): len = 115059, overlap = 4.5
PHY-3002 : Step(44): len = 110676, overlap = 13.5
PHY-3002 : Step(45): len = 109799, overlap = 11.25
PHY-3002 : Step(46): len = 107070, overlap = 11.25
PHY-3002 : Step(47): len = 105032, overlap = 11.25
PHY-3002 : Step(48): len = 101925, overlap = 13.5
PHY-3002 : Step(49): len = 100066, overlap = 11.25
PHY-3002 : Step(50): len = 97968.7, overlap = 6.75
PHY-3002 : Step(51): len = 97032.3, overlap = 6.75
PHY-3002 : Step(52): len = 94121.2, overlap = 11.25
PHY-3002 : Step(53): len = 93270.4, overlap = 11.25
PHY-3002 : Step(54): len = 91777.4, overlap = 11.25
PHY-3002 : Step(55): len = 89633.1, overlap = 6.75
PHY-3002 : Step(56): len = 86049.4, overlap = 6.75
PHY-3002 : Step(57): len = 85543.1, overlap = 11.25
PHY-3002 : Step(58): len = 84473.5, overlap = 11.25
PHY-3002 : Step(59): len = 82724.1, overlap = 13.5
PHY-3002 : Step(60): len = 82121.5, overlap = 11.25
PHY-3002 : Step(61): len = 81532.4, overlap = 11.25
PHY-3002 : Step(62): len = 80577.1, overlap = 6.75
PHY-3002 : Step(63): len = 79269.8, overlap = 6.75
PHY-3002 : Step(64): len = 77715.9, overlap = 11.25
PHY-3002 : Step(65): len = 77074.8, overlap = 11.25
PHY-3002 : Step(66): len = 75566.4, overlap = 6.75
PHY-3002 : Step(67): len = 74602.8, overlap = 9
PHY-3002 : Step(68): len = 72660.7, overlap = 11.25
PHY-3002 : Step(69): len = 71171.1, overlap = 11.25
PHY-3002 : Step(70): len = 71089.1, overlap = 13.5
PHY-3002 : Step(71): len = 70652.1, overlap = 6.75
PHY-3002 : Step(72): len = 67455.9, overlap = 6.75
PHY-3002 : Step(73): len = 64308.1, overlap = 11.25
PHY-3002 : Step(74): len = 63835.9, overlap = 11.25
PHY-3002 : Step(75): len = 63873.3, overlap = 6.75
PHY-3002 : Step(76): len = 63818.5, overlap = 6.75
PHY-3002 : Step(77): len = 63228, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000198573
PHY-3002 : Step(78): len = 63235.3, overlap = 9
PHY-3002 : Step(79): len = 63228.6, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000397146
PHY-3002 : Step(80): len = 63326, overlap = 6.75
PHY-3002 : Step(81): len = 63351.2, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006660s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065219s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(82): len = 66737.3, overlap = 3.6875
PHY-3002 : Step(83): len = 65500.7, overlap = 3.6875
PHY-3002 : Step(84): len = 64754.6, overlap = 3.375
PHY-3002 : Step(85): len = 63170.5, overlap = 2.9375
PHY-3002 : Step(86): len = 61766.2, overlap = 3
PHY-3002 : Step(87): len = 60420.1, overlap = 2.5625
PHY-3002 : Step(88): len = 58841.8, overlap = 2.5625
PHY-3002 : Step(89): len = 57650.4, overlap = 2.6875
PHY-3002 : Step(90): len = 55435.2, overlap = 2.875
PHY-3002 : Step(91): len = 53570.2, overlap = 4.6875
PHY-3002 : Step(92): len = 52141.5, overlap = 5.625
PHY-3002 : Step(93): len = 51050.4, overlap = 5.25
PHY-3002 : Step(94): len = 50306.9, overlap = 5.3125
PHY-3002 : Step(95): len = 49909.7, overlap = 5.125
PHY-3002 : Step(96): len = 49153.6, overlap = 4.8125
PHY-3002 : Step(97): len = 48749.1, overlap = 4.6875
PHY-3002 : Step(98): len = 48179.5, overlap = 5.1875
PHY-3002 : Step(99): len = 47979.2, overlap = 5.375
PHY-3002 : Step(100): len = 47842.7, overlap = 5.3125
PHY-3002 : Step(101): len = 47655.9, overlap = 5.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000491418
PHY-3002 : Step(102): len = 47460.7, overlap = 5.75
PHY-3002 : Step(103): len = 47460.7, overlap = 5.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000982835
PHY-3002 : Step(104): len = 47568.5, overlap = 5.6875
PHY-3002 : Step(105): len = 47619.8, overlap = 5.5625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061918s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000100611
PHY-3002 : Step(106): len = 48184.8, overlap = 53.375
PHY-3002 : Step(107): len = 48616.7, overlap = 45.9375
PHY-3002 : Step(108): len = 49303.2, overlap = 45.0625
PHY-3002 : Step(109): len = 49286.9, overlap = 44.7812
PHY-3002 : Step(110): len = 49249.3, overlap = 47.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000201222
PHY-3002 : Step(111): len = 49387.3, overlap = 46.6875
PHY-3002 : Step(112): len = 49691.3, overlap = 44.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000402444
PHY-3002 : Step(113): len = 50110.1, overlap = 42.375
PHY-3002 : Step(114): len = 50618.7, overlap = 41.6562
PHY-3002 : Step(115): len = 50886.5, overlap = 41.25
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7785, tnet num: 2196, tinst num: 1626, tnode num: 11025, tedge num: 13162.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 96.31 peak overflow 2.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54360, over cnt = 271(0%), over = 1181, worst = 20
PHY-1001 : End global iterations;  0.056130s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (167.0%)

PHY-1001 : Congestion index: top1 = 44.76, top5 = 26.27, top10 = 16.47, top15 = 11.56.
PHY-1001 : End incremental global routing;  0.105777s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (118.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067485s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.204297s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (114.7%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1705/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54360, over cnt = 271(0%), over = 1181, worst = 20
PHY-1002 : len = 60960, over cnt = 222(0%), over = 638, worst = 20
PHY-1002 : len = 67704, over cnt = 54(0%), over = 56, worst = 2
PHY-1002 : len = 68624, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 69056, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.105827s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (118.1%)

PHY-1001 : Congestion index: top1 = 38.43, top5 = 26.69, top10 = 18.94, top15 = 13.73.
OPT-1001 : End congestion update;  0.150035s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (104.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061695s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.214348s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (102.1%)

OPT-1001 : Current memory(MB): used = 216, reserve = 181, peak = 216.
OPT-1001 : End physical optimization;  0.694802s wall, 0.687500s user + 0.062500s system = 0.750000s CPU (107.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 95 SEQ with LUT/SLICE
SYN-4006 : 114 single LUT's are left
SYN-4006 : 708 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1077/1409 primitive instances ...
PHY-3001 : End packing;  0.051890s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-1001 : 1476 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50823.8, Over = 67.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6556, tnet num: 2028, tinst num: 835, tnode num: 8901, tedge num: 11523.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.307523s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.70522e-05
PHY-3002 : Step(116): len = 50237.3, overlap = 68.75
PHY-3002 : Step(117): len = 49997.6, overlap = 69.5
PHY-3002 : Step(118): len = 49531.2, overlap = 67.5
PHY-3002 : Step(119): len = 49615, overlap = 71
PHY-3002 : Step(120): len = 49444.4, overlap = 71.5
PHY-3002 : Step(121): len = 49538.7, overlap = 70.75
PHY-3002 : Step(122): len = 49599.7, overlap = 73.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.41043e-05
PHY-3002 : Step(123): len = 49722.1, overlap = 72.25
PHY-3002 : Step(124): len = 50155.3, overlap = 72
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.30116e-05
PHY-3002 : Step(125): len = 50560.1, overlap = 71.25
PHY-3002 : Step(126): len = 51693.4, overlap = 67.5
PHY-3002 : Step(127): len = 52102.6, overlap = 64
PHY-3002 : Step(128): len = 52791.9, overlap = 62.5
PHY-3002 : Step(129): len = 52807.3, overlap = 61.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.072910s wall, 0.109375s user + 0.109375s system = 0.218750s CPU (300.0%)

PHY-3001 : Trial Legalized: Len = 68761.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050983s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000469225
PHY-3002 : Step(130): len = 65444.6, overlap = 10
PHY-3002 : Step(131): len = 63024.3, overlap = 16.5
PHY-3002 : Step(132): len = 61123.8, overlap = 19.75
PHY-3002 : Step(133): len = 59952.9, overlap = 20.75
PHY-3002 : Step(134): len = 59415.9, overlap = 20.75
PHY-3002 : Step(135): len = 59260.1, overlap = 19.5
PHY-3002 : Step(136): len = 58894.2, overlap = 19.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00093845
PHY-3002 : Step(137): len = 59297.2, overlap = 19.25
PHY-3002 : Step(138): len = 59567.3, overlap = 19.25
PHY-3002 : Step(139): len = 59568.8, overlap = 19
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0018769
PHY-3002 : Step(140): len = 59714.8, overlap = 19.5
PHY-3002 : Step(141): len = 59714.8, overlap = 19.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004819s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64403.7, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005713s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 2, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 64457.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6556, tnet num: 2028, tinst num: 835, tnode num: 8901, tedge num: 11523.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 49/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70720, over cnt = 135(0%), over = 203, worst = 6
PHY-1002 : len = 71616, over cnt = 69(0%), over = 78, worst = 3
PHY-1002 : len = 72496, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 72624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.105698s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (118.3%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 23.16, top10 = 18.13, top15 = 14.22.
PHY-1001 : End incremental global routing;  0.156442s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (119.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065463s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.252455s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (111.4%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006387s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 23.16, top10 = 18.13, top15 = 14.22.
OPT-1001 : End congestion update;  0.052234s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050977s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 797 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 835 instances, 788 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64522.8, Over = 0
PHY-3001 : End spreading;  0.005318s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (293.8%)

PHY-3001 : Final: Len = 64522.8, Over = 0
PHY-3001 : End incremental legalization;  0.035792s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (131.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.154801s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.9%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050841s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1779/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007594s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 23.18, top10 = 18.14, top15 = 14.22.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049234s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.856597s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (102.1%)

RUN-1003 : finish command "place" in  4.994063s wall, 7.765625s user + 2.656250s system = 10.421875s CPU (208.7%)

RUN-1004 : used memory is 202 MB, reserved memory is 166 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 837 instances
RUN-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-1001 : 1476 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6556, tnet num: 2028, tinst num: 835, tnode num: 8901, tedge num: 11523.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 394 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70240, over cnt = 132(0%), over = 200, worst = 6
PHY-1002 : len = 71120, over cnt = 67(0%), over = 79, worst = 3
PHY-1002 : len = 72032, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 72160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121751s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (141.2%)

PHY-1001 : Congestion index: top1 = 31.51, top5 = 23.03, top10 = 18.02, top15 = 14.14.
PHY-1001 : End global routing;  0.171524s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (127.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 206, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.231925s wall, 3.218750s user + 0.015625s system = 3.234375s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33936, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.267715s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 531, reserve = 502, peak = 531.
PHY-1001 : End phase 1; 1.273370s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (100.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 188600, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End initial routed; 1.093504s wall, 2.125000s user + 0.203125s system = 2.328125s CPU (212.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.387   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.377196s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End phase 2; 1.470785s wall, 2.500000s user + 0.203125s system = 2.703125s CPU (183.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 188600, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015062s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 188544, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025464s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (122.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 188528, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.017542s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (89.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.387   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.377411s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.178312s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.4%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End phase 3; 0.735759s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (99.8%)

PHY-1003 : Routed, final wirelength = 188528
PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End export database. 0.009394s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (166.3%)

PHY-1001 : End detail routing;  6.905528s wall, 7.906250s user + 0.234375s system = 8.140625s CPU (117.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6556, tnet num: 2028, tinst num: 835, tnode num: 8901, tedge num: 11523.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.829500s wall, 8.843750s user + 0.265625s system = 9.109375s CPU (116.3%)

RUN-1004 : used memory is 501 MB, reserved memory is 471 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      823   out of  19600    4.20%
#reg                     1074   out of  19600    5.48%
#le                      1531
  #lut only               457   out of   1531   29.85%
  #reg only               708   out of   1531   46.24%
  #lut&reg                366   out of   1531   23.91%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                            Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                             470
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                             109
#3        wendu/clk_us                    GCLK               mslice             signal_process/ctrl_signal/clk_DA_n3_syn_13.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                                     11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                             1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                                   1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1531   |598     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1124   |293     |132     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |25      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |526    |119     |57      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |159    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |14      |0       |26      |0       |0       |
|    integ                   |Integration                                      |140    |16      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |89     |37      |21      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |79      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |22     |17      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |114    |98      |7       |51      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |18      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |54     |52      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |75      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1440  
    #2          2       312   
    #3          3       111   
    #4          4        17   
    #5        5-10       77   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6556, tnet num: 2028, tinst num: 835, tnode num: 8901, tedge num: 11523.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 835
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2030, pip num: 14780
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1294 valid insts, and 39243 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.304918s wall, 18.359375s user + 0.062500s system = 18.421875s CPU (557.4%)

RUN-1004 : used memory is 521 MB, reserved memory is 488 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_115321.log"
