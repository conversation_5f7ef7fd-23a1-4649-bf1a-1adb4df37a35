============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 14:55:58 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1647 instances
RUN-0007 : 380 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2217 nets
RUN-1001 : 1658 nets have 2 pins
RUN-1001 : 444 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1645 instances, 380 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7834, tnet num: 2215, tinst num: 1645, tnode num: 11072, tedge num: 13222.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.304497s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (97.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 544502
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1645.
PHY-3001 : End clustering;  0.000022s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 490856, overlap = 20.25
PHY-3002 : Step(2): len = 409722, overlap = 13.5
PHY-3002 : Step(3): len = 340012, overlap = 13.5
PHY-3002 : Step(4): len = 333423, overlap = 15.75
PHY-3002 : Step(5): len = 324815, overlap = 13.5
PHY-3002 : Step(6): len = 318105, overlap = 15.75
PHY-3002 : Step(7): len = 313106, overlap = 18
PHY-3002 : Step(8): len = 304025, overlap = 18
PHY-3002 : Step(9): len = 297009, overlap = 20.25
PHY-3002 : Step(10): len = 292423, overlap = 20.25
PHY-3002 : Step(11): len = 285216, overlap = 20.25
PHY-3002 : Step(12): len = 278894, overlap = 20.25
PHY-3002 : Step(13): len = 273800, overlap = 20.25
PHY-3002 : Step(14): len = 266656, overlap = 20.25
PHY-3002 : Step(15): len = 262055, overlap = 20.25
PHY-3002 : Step(16): len = 257267, overlap = 20.25
PHY-3002 : Step(17): len = 249865, overlap = 20.25
PHY-3002 : Step(18): len = 244437, overlap = 20.25
PHY-3002 : Step(19): len = 241145, overlap = 20.25
PHY-3002 : Step(20): len = 233937, overlap = 20.25
PHY-3002 : Step(21): len = 224564, overlap = 20.25
PHY-3002 : Step(22): len = 220935, overlap = 20.25
PHY-3002 : Step(23): len = 217507, overlap = 20.25
PHY-3002 : Step(24): len = 205555, overlap = 20.25
PHY-3002 : Step(25): len = 198550, overlap = 20.25
PHY-3002 : Step(26): len = 196682, overlap = 20.25
PHY-3002 : Step(27): len = 188373, overlap = 20.25
PHY-3002 : Step(28): len = 157996, overlap = 20.25
PHY-3002 : Step(29): len = 154937, overlap = 20.25
PHY-3002 : Step(30): len = 153509, overlap = 20.25
PHY-3002 : Step(31): len = 145899, overlap = 20.25
PHY-3002 : Step(32): len = 142623, overlap = 20.25
PHY-3002 : Step(33): len = 141340, overlap = 20.25
PHY-3002 : Step(34): len = 136813, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000151131
PHY-3002 : Step(35): len = 138493, overlap = 11.25
PHY-3002 : Step(36): len = 137589, overlap = 9
PHY-3002 : Step(37): len = 135610, overlap = 11.25
PHY-3002 : Step(38): len = 133094, overlap = 13.5
PHY-3002 : Step(39): len = 129548, overlap = 9
PHY-3002 : Step(40): len = 127513, overlap = 6.75
PHY-3002 : Step(41): len = 125763, overlap = 4.5
PHY-3002 : Step(42): len = 121858, overlap = 4.5
PHY-3002 : Step(43): len = 121345, overlap = 4.5
PHY-3002 : Step(44): len = 118830, overlap = 6.75
PHY-3002 : Step(45): len = 116367, overlap = 6.75
PHY-3002 : Step(46): len = 113579, overlap = 4.5
PHY-3002 : Step(47): len = 111836, overlap = 9
PHY-3002 : Step(48): len = 109400, overlap = 4.5
PHY-3002 : Step(49): len = 106632, overlap = 4.5
PHY-3002 : Step(50): len = 104354, overlap = 4.5
PHY-3002 : Step(51): len = 104051, overlap = 4.5
PHY-3002 : Step(52): len = 102860, overlap = 6.75
PHY-3002 : Step(53): len = 100646, overlap = 9
PHY-3002 : Step(54): len = 98034.6, overlap = 6.75
PHY-3002 : Step(55): len = 97073.1, overlap = 6.75
PHY-3002 : Step(56): len = 95382.3, overlap = 4.5
PHY-3002 : Step(57): len = 94260.3, overlap = 4.5
PHY-3002 : Step(58): len = 92714.3, overlap = 4.5
PHY-3002 : Step(59): len = 92069.7, overlap = 6.75
PHY-3002 : Step(60): len = 89792.5, overlap = 11.25
PHY-3002 : Step(61): len = 88528.4, overlap = 6.75
PHY-3002 : Step(62): len = 87455.5, overlap = 6.75
PHY-3002 : Step(63): len = 86438.5, overlap = 6.75
PHY-3002 : Step(64): len = 85020.3, overlap = 4.5
PHY-3002 : Step(65): len = 83085.7, overlap = 6.75
PHY-3002 : Step(66): len = 82044.9, overlap = 6.75
PHY-3002 : Step(67): len = 81831.6, overlap = 4.5
PHY-3002 : Step(68): len = 81153.7, overlap = 4.5
PHY-3002 : Step(69): len = 79808.2, overlap = 4.5
PHY-3002 : Step(70): len = 78964.4, overlap = 6.75
PHY-3002 : Step(71): len = 78161.1, overlap = 6.75
PHY-3002 : Step(72): len = 77080.7, overlap = 4.5
PHY-3002 : Step(73): len = 75372.1, overlap = 4.5
PHY-3002 : Step(74): len = 73949.1, overlap = 6.75
PHY-3002 : Step(75): len = 73641.9, overlap = 6.75
PHY-3002 : Step(76): len = 72997.7, overlap = 4.5
PHY-3002 : Step(77): len = 72036.4, overlap = 4.5
PHY-3002 : Step(78): len = 70759.6, overlap = 6.75
PHY-3002 : Step(79): len = 69133.9, overlap = 9
PHY-3002 : Step(80): len = 67792, overlap = 6.75
PHY-3002 : Step(81): len = 67507.7, overlap = 6.75
PHY-3002 : Step(82): len = 67363.5, overlap = 6.75
PHY-3002 : Step(83): len = 66946.7, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000302261
PHY-3002 : Step(84): len = 66977.1, overlap = 6.75
PHY-3002 : Step(85): len = 66987.8, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000604523
PHY-3002 : Step(86): len = 66949.3, overlap = 6.75
PHY-3002 : Step(87): len = 66992.6, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008890s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (175.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066579s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(88): len = 69831.8, overlap = 3.9375
PHY-3002 : Step(89): len = 68910.7, overlap = 3.25
PHY-3002 : Step(90): len = 67960.1, overlap = 2.9375
PHY-3002 : Step(91): len = 65875.6, overlap = 3.1875
PHY-3002 : Step(92): len = 64545.4, overlap = 2.875
PHY-3002 : Step(93): len = 63319.9, overlap = 3.3125
PHY-3002 : Step(94): len = 61950.1, overlap = 2.5625
PHY-3002 : Step(95): len = 60661.7, overlap = 5.40625
PHY-3002 : Step(96): len = 58918.3, overlap = 5.125
PHY-3002 : Step(97): len = 57341.5, overlap = 4.4375
PHY-3002 : Step(98): len = 56270, overlap = 5.125
PHY-3002 : Step(99): len = 55605.2, overlap = 4.4375
PHY-3002 : Step(100): len = 54986.6, overlap = 4.375
PHY-3002 : Step(101): len = 54232.5, overlap = 5.125
PHY-3002 : Step(102): len = 53162, overlap = 5.6875
PHY-3002 : Step(103): len = 52621.7, overlap = 5.75
PHY-3002 : Step(104): len = 51978.1, overlap = 7.5
PHY-3002 : Step(105): len = 51196.1, overlap = 7.375
PHY-3002 : Step(106): len = 50622.6, overlap = 6.6875
PHY-3002 : Step(107): len = 50346, overlap = 6.9375
PHY-3002 : Step(108): len = 49728.8, overlap = 8.8125
PHY-3002 : Step(109): len = 48493, overlap = 10.25
PHY-3002 : Step(110): len = 47731.1, overlap = 11.7188
PHY-3002 : Step(111): len = 47135.7, overlap = 12.5
PHY-3002 : Step(112): len = 46524.1, overlap = 16.125
PHY-3002 : Step(113): len = 45809.9, overlap = 19.9062
PHY-3002 : Step(114): len = 45640.5, overlap = 16.875
PHY-3002 : Step(115): len = 45370.3, overlap = 15.3125
PHY-3002 : Step(116): len = 45255.7, overlap = 17.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000163307
PHY-3002 : Step(117): len = 45127.8, overlap = 17.6562
PHY-3002 : Step(118): len = 45042.6, overlap = 14.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000326615
PHY-3002 : Step(119): len = 44917.3, overlap = 13.625
PHY-3002 : Step(120): len = 45207.2, overlap = 11.7188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072294s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.69123e-05
PHY-3002 : Step(121): len = 45255, overlap = 65.9375
PHY-3002 : Step(122): len = 45784.7, overlap = 64.7812
PHY-3002 : Step(123): len = 46366.6, overlap = 62.0625
PHY-3002 : Step(124): len = 46431.5, overlap = 60.2188
PHY-3002 : Step(125): len = 46418.8, overlap = 62.7188
PHY-3002 : Step(126): len = 46399.3, overlap = 62.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.38246e-05
PHY-3002 : Step(127): len = 46551.1, overlap = 61.4688
PHY-3002 : Step(128): len = 46798.9, overlap = 56.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000147649
PHY-3002 : Step(129): len = 47023.1, overlap = 55.5312
PHY-3002 : Step(130): len = 48111.7, overlap = 51.5
PHY-3002 : Step(131): len = 49042.4, overlap = 49.6875
PHY-3002 : Step(132): len = 48966.7, overlap = 49.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000295299
PHY-3002 : Step(133): len = 49169.4, overlap = 50.5312
PHY-3002 : Step(134): len = 49999.4, overlap = 42.8438
PHY-3002 : Step(135): len = 51253.4, overlap = 34.2188
PHY-3002 : Step(136): len = 51280.8, overlap = 32.6875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7834, tnet num: 2215, tinst num: 1645, tnode num: 11072, tedge num: 13222.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 88.78 peak overflow 3.44
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54064, over cnt = 247(0%), over = 1100, worst = 19
PHY-1001 : End global iterations;  0.091281s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (154.1%)

PHY-1001 : Congestion index: top1 = 46.49, top5 = 25.59, top10 = 16.27, top15 = 11.50.
PHY-1001 : End incremental global routing;  0.144358s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (140.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.076316s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.253785s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (123.1%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1674/2217.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54064, over cnt = 247(0%), over = 1100, worst = 19
PHY-1002 : len = 62744, over cnt = 146(0%), over = 287, worst = 11
PHY-1002 : len = 66040, over cnt = 32(0%), over = 49, worst = 8
PHY-1002 : len = 66440, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 66752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.111976s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (167.4%)

PHY-1001 : Congestion index: top1 = 40.78, top5 = 25.61, top10 = 18.30, top15 = 13.43.
OPT-1001 : End congestion update;  0.158387s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (148.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2215 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.076536s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.237682s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (131.5%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.806281s wall, 0.937500s user + 0.078125s system = 1.015625s CPU (126.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 107 SEQ with LUT/SLICE
SYN-4006 : 104 single LUT's are left
SYN-4006 : 696 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1076/1409 primitive instances ...
PHY-3001 : End packing;  0.061611s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 844 instances
RUN-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 842 instances, 795 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51642, Over = 60.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 842, tnode num: 8922, tedge num: 11574.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.343666s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.34532e-05
PHY-3002 : Step(137): len = 51133.4, overlap = 58.75
PHY-3002 : Step(138): len = 50654.4, overlap = 61
PHY-3002 : Step(139): len = 50113.8, overlap = 62.75
PHY-3002 : Step(140): len = 49742.4, overlap = 62.5
PHY-3002 : Step(141): len = 49612.5, overlap = 64
PHY-3002 : Step(142): len = 49551.8, overlap = 65.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.69064e-05
PHY-3002 : Step(143): len = 49814.5, overlap = 65.25
PHY-3002 : Step(144): len = 50234.7, overlap = 63
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.35448e-05
PHY-3002 : Step(145): len = 50959.2, overlap = 61.25
PHY-3002 : Step(146): len = 53110.3, overlap = 56.5
PHY-3002 : Step(147): len = 53604.8, overlap = 55.5
PHY-3002 : Step(148): len = 53584.2, overlap = 55.25
PHY-3002 : Step(149): len = 53573.8, overlap = 54.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.118768s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (144.7%)

PHY-3001 : Trial Legalized: Len = 66194.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056741s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00033806
PHY-3002 : Step(150): len = 63332.6, overlap = 6.75
PHY-3002 : Step(151): len = 61330.2, overlap = 14.25
PHY-3002 : Step(152): len = 59938.5, overlap = 19.25
PHY-3002 : Step(153): len = 58827, overlap = 21
PHY-3002 : Step(154): len = 58187.9, overlap = 21.5
PHY-3002 : Step(155): len = 57755.4, overlap = 23.5
PHY-3002 : Step(156): len = 57448.9, overlap = 25.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00067612
PHY-3002 : Step(157): len = 58100.3, overlap = 24.5
PHY-3002 : Step(158): len = 58363.2, overlap = 24.25
PHY-3002 : Step(159): len = 58363.2, overlap = 24.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00135224
PHY-3002 : Step(160): len = 58798.2, overlap = 22.75
PHY-3002 : Step(161): len = 58987.3, overlap = 22.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005287s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (295.5%)

PHY-3001 : Legalized: Len = 63694.3, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006968s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 4, deltaY = 11, maxDist = 2.
PHY-3001 : Final: Len = 63954.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 842, tnode num: 8922, tedge num: 11574.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 119/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70728, over cnt = 144(0%), over = 207, worst = 7
PHY-1002 : len = 71584, over cnt = 69(0%), over = 82, worst = 3
PHY-1002 : len = 72208, over cnt = 27(0%), over = 27, worst = 1
PHY-1002 : len = 72640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.152365s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.8%)

PHY-1001 : Congestion index: top1 = 34.09, top5 = 23.66, top10 = 18.46, top15 = 14.48.
PHY-1001 : End incremental global routing;  0.208556s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (104.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070756s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.312828s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (104.9%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1803/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007058s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 34.09, top5 = 23.66, top10 = 18.46, top15 = 14.48.
OPT-1001 : End congestion update;  0.058887s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057111s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 804 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 842 instances, 795 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63987, Over = 0
PHY-3001 : End spreading;  0.005744s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (272.0%)

PHY-3001 : Final: Len = 63987, Over = 0
PHY-3001 : End incremental legalization;  0.037690s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (124.4%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.167692s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.5%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057788s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (81.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1800/2042.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72632, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.021623s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (144.5%)

PHY-1001 : Congestion index: top1 = 34.14, top5 = 23.64, top10 = 18.45, top15 = 14.49.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057785s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.022954s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (105.4%)

RUN-1003 : finish command "place" in  6.633082s wall, 11.031250s user + 2.875000s system = 13.906250s CPU (209.6%)

RUN-1004 : used memory is 205 MB, reserved memory is 168 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 844 instances
RUN-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2042 nets
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 842, tnode num: 8922, tedge num: 11574.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69704, over cnt = 139(0%), over = 195, worst = 7
PHY-1002 : len = 70560, over cnt = 71(0%), over = 81, worst = 3
PHY-1002 : len = 71408, over cnt = 18(0%), over = 18, worst = 1
PHY-1002 : len = 71632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.158003s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (98.9%)

PHY-1001 : Congestion index: top1 = 33.49, top5 = 23.24, top10 = 18.17, top15 = 14.29.
PHY-1001 : End global routing;  0.214578s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (101.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 203, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 501, reserve = 468, peak = 501.
PHY-1001 : End build detailed router design. 3.439973s wall, 3.359375s user + 0.093750s system = 3.453125s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.445856s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 533.
PHY-1001 : End phase 1; 1.453158s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180752, over cnt = 15(0%), over = 15, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 535.
PHY-1001 : End initial routed; 1.186194s wall, 2.078125s user + 0.078125s system = 2.156250s CPU (181.8%)

PHY-1001 : Update timing.....
PHY-1001 : 4/1806(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.205   |  -0.205   |   1   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.397254s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (102.3%)

PHY-1001 : Current memory(MB): used = 536, reserve = 503, peak = 536.
PHY-1001 : End phase 2; 1.583545s wall, 2.484375s user + 0.078125s system = 2.562500s CPU (161.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS 0.204ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.010173s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (153.6%)

PHY-1022 : len = 180752, over cnt = 16(0%), over = 16, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.027816s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (112.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180760, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023220s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (134.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180784, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025892s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (241.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1806(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.204   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.401362s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (101.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.202972s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (100.1%)

PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End phase 3; 0.811233s wall, 0.812500s user + 0.031250s system = 0.843750s CPU (104.0%)

PHY-1003 : Routed, final wirelength = 180784
PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End export database. 0.011343s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (137.7%)

PHY-1001 : End detail routing;  7.495368s wall, 8.296875s user + 0.203125s system = 8.500000s CPU (113.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 842, tnode num: 8922, tedge num: 11574.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.480848s wall, 9.265625s user + 0.218750s system = 9.484375s CPU (111.8%)

RUN-1004 : used memory is 527 MB, reserved memory is 494 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      839   out of  19600    4.28%
#reg                     1074   out of  19600    5.48%
#le                      1535
  #lut only               461   out of   1535   30.03%
  #reg only               696   out of   1535   45.34%
  #lut&reg                378   out of   1535   24.63%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    36
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1535   |613     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1146   |324     |133     |926     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |33      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |540    |116     |58      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |159    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |47     |2       |0       |47      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |14      |0       |28      |0       |0       |
|    integ                   |Integration                                      |138    |28      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |94     |28      |21      |90      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |104     |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |20     |15      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |98     |84      |7       |46      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |19     |16      |0       |14      |0       |0       |
|    U2                      |Ctrl_Data                                        |42     |40      |0       |16      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |77      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1453  
    #2          2       313   
    #3          3       108   
    #4          4        16   
    #5        5-10       79   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6591, tnet num: 2040, tinst num: 842, tnode num: 8922, tedge num: 11574.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2040 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 842
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2042, pip num: 14718
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1335 valid insts, and 39098 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.193435s wall, 21.500000s user + 0.109375s system = 21.609375s CPU (515.3%)

RUN-1004 : used memory is 550 MB, reserved memory is 518 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_145558.log"
