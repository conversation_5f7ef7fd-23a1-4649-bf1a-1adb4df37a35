============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 16:59:17 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1640 instances
RUN-0007 : 376 luts, 1009 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2180 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1621 nets have 2 pins
RUN-1001 : 445 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 14 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1638 instances, 376 luts, 1009 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7783, tnet num: 2178, tinst num: 1638, tnode num: 11045, tedge num: 13158.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2178 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.282407s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 636394
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1638.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 521683, overlap = 20.25
PHY-3002 : Step(2): len = 482725, overlap = 20.25
PHY-3002 : Step(3): len = 442062, overlap = 20.25
PHY-3002 : Step(4): len = 414963, overlap = 20.25
PHY-3002 : Step(5): len = 406035, overlap = 18
PHY-3002 : Step(6): len = 394521, overlap = 15.75
PHY-3002 : Step(7): len = 321427, overlap = 20.25
PHY-3002 : Step(8): len = 271787, overlap = 18
PHY-3002 : Step(9): len = 262857, overlap = 20.25
PHY-3002 : Step(10): len = 258462, overlap = 18
PHY-3002 : Step(11): len = 251748, overlap = 18
PHY-3002 : Step(12): len = 245628, overlap = 18
PHY-3002 : Step(13): len = 238322, overlap = 18
PHY-3002 : Step(14): len = 233742, overlap = 18
PHY-3002 : Step(15): len = 223465, overlap = 18
PHY-3002 : Step(16): len = 217227, overlap = 18
PHY-3002 : Step(17): len = 213083, overlap = 18
PHY-3002 : Step(18): len = 207114, overlap = 18
PHY-3002 : Step(19): len = 197212, overlap = 18
PHY-3002 : Step(20): len = 194459, overlap = 18
PHY-3002 : Step(21): len = 190187, overlap = 18
PHY-3002 : Step(22): len = 171042, overlap = 18
PHY-3002 : Step(23): len = 162860, overlap = 18
PHY-3002 : Step(24): len = 161712, overlap = 18
PHY-3002 : Step(25): len = 145378, overlap = 15.75
PHY-3002 : Step(26): len = 138850, overlap = 15.75
PHY-3002 : Step(27): len = 136241, overlap = 18
PHY-3002 : Step(28): len = 133629, overlap = 18
PHY-3002 : Step(29): len = 131372, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00013041
PHY-3002 : Step(30): len = 133272, overlap = 18
PHY-3002 : Step(31): len = 134578, overlap = 11.25
PHY-3002 : Step(32): len = 132306, overlap = 13.5
PHY-3002 : Step(33): len = 129373, overlap = 18
PHY-3002 : Step(34): len = 126413, overlap = 15.75
PHY-3002 : Step(35): len = 126247, overlap = 9
PHY-3002 : Step(36): len = 123348, overlap = 15.75
PHY-3002 : Step(37): len = 120498, overlap = 15.75
PHY-3002 : Step(38): len = 118427, overlap = 13.5
PHY-3002 : Step(39): len = 115380, overlap = 18
PHY-3002 : Step(40): len = 114166, overlap = 13.5
PHY-3002 : Step(41): len = 111599, overlap = 13.5
PHY-3002 : Step(42): len = 109706, overlap = 13.5
PHY-3002 : Step(43): len = 106752, overlap = 15.75
PHY-3002 : Step(44): len = 105414, overlap = 13.5
PHY-3002 : Step(45): len = 103278, overlap = 13.5
PHY-3002 : Step(46): len = 102512, overlap = 9
PHY-3002 : Step(47): len = 97569, overlap = 11.25
PHY-3002 : Step(48): len = 94515, overlap = 9
PHY-3002 : Step(49): len = 93162.6, overlap = 9
PHY-3002 : Step(50): len = 92731.6, overlap = 6.75
PHY-3002 : Step(51): len = 91993.2, overlap = 11.25
PHY-3002 : Step(52): len = 91068.9, overlap = 11.25
PHY-3002 : Step(53): len = 90685.5, overlap = 11.25
PHY-3002 : Step(54): len = 90640.5, overlap = 11.25
PHY-3002 : Step(55): len = 89777.8, overlap = 6.75
PHY-3002 : Step(56): len = 87643.5, overlap = 9
PHY-3002 : Step(57): len = 85610.5, overlap = 11.25
PHY-3002 : Step(58): len = 84282.3, overlap = 9
PHY-3002 : Step(59): len = 83898.3, overlap = 11.25
PHY-3002 : Step(60): len = 82740.8, overlap = 11.25
PHY-3002 : Step(61): len = 81904.2, overlap = 11.25
PHY-3002 : Step(62): len = 81515, overlap = 11.25
PHY-3002 : Step(63): len = 81367, overlap = 11.25
PHY-3002 : Step(64): len = 80754.2, overlap = 11.25
PHY-3002 : Step(65): len = 79358.1, overlap = 6.75
PHY-3002 : Step(66): len = 78162.9, overlap = 6.75
PHY-3002 : Step(67): len = 76828.6, overlap = 6.75
PHY-3002 : Step(68): len = 76568.6, overlap = 9
PHY-3002 : Step(69): len = 75185.8, overlap = 9
PHY-3002 : Step(70): len = 74327.1, overlap = 11.25
PHY-3002 : Step(71): len = 74095.4, overlap = 11.25
PHY-3002 : Step(72): len = 73268.1, overlap = 9
PHY-3002 : Step(73): len = 72474.6, overlap = 4.5
PHY-3002 : Step(74): len = 72146.7, overlap = 6.75
PHY-3002 : Step(75): len = 70702.5, overlap = 11.25
PHY-3002 : Step(76): len = 69199.8, overlap = 11.25
PHY-3002 : Step(77): len = 68896.9, overlap = 11.25
PHY-3002 : Step(78): len = 68537.3, overlap = 11.25
PHY-3002 : Step(79): len = 67772.2, overlap = 9
PHY-3002 : Step(80): len = 67209.2, overlap = 11.25
PHY-3002 : Step(81): len = 66967.3, overlap = 13.5
PHY-3002 : Step(82): len = 65223.3, overlap = 11.25
PHY-3002 : Step(83): len = 63688.8, overlap = 13.5
PHY-3002 : Step(84): len = 62723.8, overlap = 13.5
PHY-3002 : Step(85): len = 62385, overlap = 13.5
PHY-3002 : Step(86): len = 61586.1, overlap = 13.5
PHY-3002 : Step(87): len = 61022.2, overlap = 11.25
PHY-3002 : Step(88): len = 59359.5, overlap = 11.25
PHY-3002 : Step(89): len = 58753.4, overlap = 9
PHY-3002 : Step(90): len = 58135.5, overlap = 11.25
PHY-3002 : Step(91): len = 58031.1, overlap = 11.25
PHY-3002 : Step(92): len = 58023.9, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000260821
PHY-3002 : Step(93): len = 58138.3, overlap = 11.25
PHY-3002 : Step(94): len = 58178.5, overlap = 11.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000521642
PHY-3002 : Step(95): len = 58241.1, overlap = 9
PHY-3002 : Step(96): len = 58296.8, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005606s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2178 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064701s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(97): len = 61059.3, overlap = 1.6875
PHY-3002 : Step(98): len = 60008.8, overlap = 1.125
PHY-3002 : Step(99): len = 59253.3, overlap = 1.4375
PHY-3002 : Step(100): len = 58384.9, overlap = 1.125
PHY-3002 : Step(101): len = 57818.2, overlap = 1.3125
PHY-3002 : Step(102): len = 56562.7, overlap = 1.5
PHY-3002 : Step(103): len = 56019.6, overlap = 1.9375
PHY-3002 : Step(104): len = 55322.1, overlap = 3.9375
PHY-3002 : Step(105): len = 54539.2, overlap = 3.5625
PHY-3002 : Step(106): len = 54147.3, overlap = 4.25
PHY-3002 : Step(107): len = 53921.7, overlap = 4.375
PHY-3002 : Step(108): len = 52513.5, overlap = 4.625
PHY-3002 : Step(109): len = 52305.3, overlap = 4.875
PHY-3002 : Step(110): len = 51844, overlap = 5.125
PHY-3002 : Step(111): len = 51537.9, overlap = 5.125
PHY-3002 : Step(112): len = 51205.8, overlap = 5.6875
PHY-3002 : Step(113): len = 51181.7, overlap = 5.6875
PHY-3002 : Step(114): len = 50865.6, overlap = 5.9375
PHY-3002 : Step(115): len = 50750, overlap = 5.8125
PHY-3002 : Step(116): len = 50701, overlap = 5.8125
PHY-3002 : Step(117): len = 50544.9, overlap = 5.75
PHY-3002 : Step(118): len = 50344.5, overlap = 5.75
PHY-3002 : Step(119): len = 50148.7, overlap = 5.875
PHY-3002 : Step(120): len = 49876.7, overlap = 4.375
PHY-3002 : Step(121): len = 49240.7, overlap = 5.1875
PHY-3002 : Step(122): len = 48506.4, overlap = 6.1875
PHY-3002 : Step(123): len = 47808.2, overlap = 7
PHY-3002 : Step(124): len = 47366.7, overlap = 6.9375
PHY-3002 : Step(125): len = 47182, overlap = 6.9375
PHY-3002 : Step(126): len = 47182, overlap = 6.9375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000404415
PHY-3002 : Step(127): len = 47141.3, overlap = 6.875
PHY-3002 : Step(128): len = 47227, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000808829
PHY-3002 : Step(129): len = 47166, overlap = 6.6875
PHY-3002 : Step(130): len = 47349.6, overlap = 6.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2178 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064444s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00011138
PHY-3002 : Step(131): len = 47483.2, overlap = 54.3438
PHY-3002 : Step(132): len = 47974.3, overlap = 53.0938
PHY-3002 : Step(133): len = 48210.8, overlap = 51
PHY-3002 : Step(134): len = 47793.3, overlap = 50.5625
PHY-3002 : Step(135): len = 47779.8, overlap = 50.25
PHY-3002 : Step(136): len = 47724.4, overlap = 50.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000222761
PHY-3002 : Step(137): len = 47818.8, overlap = 50.7812
PHY-3002 : Step(138): len = 47910.1, overlap = 50.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000360427
PHY-3002 : Step(139): len = 48440.7, overlap = 50.625
PHY-3002 : Step(140): len = 48896.4, overlap = 50.0938
PHY-3002 : Step(141): len = 50636.7, overlap = 40.375
PHY-3002 : Step(142): len = 50684.2, overlap = 40.7188
PHY-3002 : Step(143): len = 50636, overlap = 41.5938
PHY-3002 : Step(144): len = 50439.4, overlap = 40.9062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000720854
PHY-3002 : Step(145): len = 50612.6, overlap = 41.0312
PHY-3002 : Step(146): len = 50732, overlap = 41.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7783, tnet num: 2178, tinst num: 1638, tnode num: 11045, tedge num: 13158.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 95.12 peak overflow 4.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2180.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53720, over cnt = 243(0%), over = 1143, worst = 19
PHY-1001 : End global iterations;  0.068376s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (182.8%)

PHY-1001 : Congestion index: top1 = 44.07, top5 = 25.55, top10 = 15.88, top15 = 11.19.
PHY-1001 : End incremental global routing;  0.117193s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (160.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2178 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068328s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.214651s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (131.0%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1678/2180.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53720, over cnt = 243(0%), over = 1143, worst = 19
PHY-1002 : len = 61408, over cnt = 165(0%), over = 393, worst = 13
PHY-1002 : len = 65240, over cnt = 46(0%), over = 71, worst = 12
PHY-1002 : len = 66472, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 66696, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095558s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (114.5%)

PHY-1001 : Congestion index: top1 = 39.14, top5 = 25.72, top10 = 18.06, top15 = 13.25.
OPT-1001 : End congestion update;  0.140293s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (111.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2178 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061139s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.204064s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (107.2%)

OPT-1001 : Current memory(MB): used = 216, reserve = 181, peak = 216.
OPT-1001 : End physical optimization;  0.723295s wall, 0.765625s user + 0.031250s system = 0.796875s CPU (110.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 109 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 718 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1094/1407 primitive instances ...
PHY-3001 : End packing;  0.049500s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 831 instances
RUN-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2012 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1463 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 829 instances, 782 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51125.6, Over = 70.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6531, tnet num: 2010, tinst num: 829, tnode num: 8875, tedge num: 11486.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.310147s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.96561e-05
PHY-3002 : Step(147): len = 50547.9, overlap = 69
PHY-3002 : Step(148): len = 50231, overlap = 67.25
PHY-3002 : Step(149): len = 50066.2, overlap = 68.75
PHY-3002 : Step(150): len = 50005.5, overlap = 67.5
PHY-3002 : Step(151): len = 50121, overlap = 66.75
PHY-3002 : Step(152): len = 50104.2, overlap = 66
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.93123e-05
PHY-3002 : Step(153): len = 50153.8, overlap = 65.75
PHY-3002 : Step(154): len = 50322.3, overlap = 65.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000100376
PHY-3002 : Step(155): len = 50681.5, overlap = 64.25
PHY-3002 : Step(156): len = 51591.1, overlap = 60.5
PHY-3002 : Step(157): len = 52878.9, overlap = 57.5
PHY-3002 : Step(158): len = 53228.5, overlap = 55
PHY-3002 : Step(159): len = 53392.5, overlap = 52
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.097710s wall, 0.140625s user + 0.140625s system = 0.281250s CPU (287.8%)

PHY-3001 : Trial Legalized: Len = 69658.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049403s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000923019
PHY-3002 : Step(160): len = 66839.9, overlap = 6
PHY-3002 : Step(161): len = 64457.2, overlap = 11.75
PHY-3002 : Step(162): len = 62303.4, overlap = 15.25
PHY-3002 : Step(163): len = 61347, overlap = 19
PHY-3002 : Step(164): len = 60520.7, overlap = 22.25
PHY-3002 : Step(165): len = 59654.7, overlap = 23.75
PHY-3002 : Step(166): len = 59080.9, overlap = 24
PHY-3002 : Step(167): len = 58941.7, overlap = 25.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00184604
PHY-3002 : Step(168): len = 59313.5, overlap = 24
PHY-3002 : Step(169): len = 59419.5, overlap = 24.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00369207
PHY-3002 : Step(170): len = 59465, overlap = 24
PHY-3002 : Step(171): len = 59465, overlap = 24
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004941s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (316.3%)

PHY-3001 : Legalized: Len = 64169.1, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005518s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 0, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 64247.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6531, tnet num: 2010, tinst num: 829, tnode num: 8875, tedge num: 11486.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 89/2012.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69856, over cnt = 143(0%), over = 196, worst = 4
PHY-1002 : len = 70528, over cnt = 57(0%), over = 67, worst = 3
PHY-1002 : len = 71136, over cnt = 14(0%), over = 16, worst = 2
PHY-1002 : len = 71408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.108455s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (187.3%)

PHY-1001 : Congestion index: top1 = 31.68, top5 = 22.64, top10 = 17.56, top15 = 13.80.
PHY-1001 : End incremental global routing;  0.162478s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (153.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059193s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.251189s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (136.8%)

OPT-1001 : Current memory(MB): used = 217, reserve = 183, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1784/2012.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006271s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.68, top5 = 22.64, top10 = 17.56, top15 = 13.80.
OPT-1001 : End congestion update;  0.054314s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048042s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 791 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 829 instances, 782 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64262.6, Over = 0
PHY-3001 : End spreading;  0.005049s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (309.5%)

PHY-3001 : Final: Len = 64262.6, Over = 0
PHY-3001 : End incremental legalization;  0.035072s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.1%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.150317s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.9%)

OPT-1001 : Current memory(MB): used = 222, reserve = 187, peak = 222.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046980s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1776/2012.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008928s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.72, top5 = 22.64, top10 = 17.58, top15 = 13.80.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047575s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.859298s wall, 0.906250s user + 0.046875s system = 0.953125s CPU (110.9%)

RUN-1003 : finish command "place" in  5.439883s wall, 9.734375s user + 2.781250s system = 12.515625s CPU (230.1%)

RUN-1004 : used memory is 201 MB, reserved memory is 166 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 831 instances
RUN-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2012 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1463 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6531, tnet num: 2010, tinst num: 829, tnode num: 8875, tedge num: 11486.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69368, over cnt = 149(0%), over = 203, worst = 4
PHY-1002 : len = 70032, over cnt = 58(0%), over = 69, worst = 3
PHY-1002 : len = 70840, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 70920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.105583s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (133.2%)

PHY-1001 : Congestion index: top1 = 31.40, top5 = 22.60, top10 = 17.46, top15 = 13.68.
PHY-1001 : End global routing;  0.153857s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (121.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 203, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 466, peak = 497.
PHY-1001 : End build detailed router design. 3.250053s wall, 3.187500s user + 0.062500s system = 3.250000s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32376, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.293017s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End phase 1; 1.299440s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 181560, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 500, peak = 532.
PHY-1001 : End initial routed; 1.107412s wall, 1.968750s user + 0.203125s system = 2.171875s CPU (196.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.303   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.380867s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (102.6%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 2; 1.488365s wall, 2.359375s user + 0.203125s system = 2.562500s CPU (172.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181560, over cnt = 32(0%), over = 32, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016252s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (96.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181416, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033090s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181352, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.025533s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (122.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181376, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021113s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (148.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.303   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.364479s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.187008s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 547, reserve = 515, peak = 547.
PHY-1001 : End phase 3; 0.772514s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (99.1%)

PHY-1003 : Routed, final wirelength = 181376
PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End export database. 0.010654s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.7%)

PHY-1001 : End detail routing;  7.007962s wall, 7.796875s user + 0.265625s system = 8.062500s CPU (115.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6531, tnet num: 2010, tinst num: 829, tnode num: 8875, tedge num: 11486.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.894123s wall, 8.734375s user + 0.265625s system = 9.000000s CPU (114.0%)

RUN-1004 : used memory is 499 MB, reserved memory is 468 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      792   out of  19600    4.04%
#reg                     1074   out of  19600    5.48%
#le                      1510
  #lut only               436   out of   1510   28.87%
  #reg only               718   out of   1510   47.55%
  #lut&reg                356   out of   1510   23.58%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       469
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       108
#3        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_11.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1510   |586     |206     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1136   |301     |122     |925     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |30      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |521    |110     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |61      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |2       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |10      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |16      |0       |29      |0       |0       |
|    integ                   |Integration                                      |137    |17      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |102    |24      |15      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |310    |95      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |93     |81      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |34     |34      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |216    |171     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1427  
    #2          2       308   
    #3          3       109   
    #4          4        17   
    #5        5-10       78   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6531, tnet num: 2010, tinst num: 829, tnode num: 8875, tedge num: 11486.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2010 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 829
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2012, pip num: 14764
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1305 valid insts, and 38870 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.090545s wall, 18.015625s user + 0.078125s system = 18.093750s CPU (585.5%)

RUN-1004 : used memory is 543 MB, reserved memory is 511 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_165917.log"
