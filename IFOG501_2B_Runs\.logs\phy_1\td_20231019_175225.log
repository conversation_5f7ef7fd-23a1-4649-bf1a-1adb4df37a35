============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 19 17:52:25 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1528 instances
RUN-0007 : 360 luts, 907 seqs, 137 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2082 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1534 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1526 instances, 360 luts, 907 seqs, 212 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7342, tnet num: 2080, tinst num: 1526, tnode num: 10326, tedge num: 12465.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.396259s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (102.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 542109
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1526.
PHY-3001 : End clustering;  0.000024s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 457081, overlap = 13.5
PHY-3002 : Step(2): len = 415576, overlap = 15.75
PHY-3002 : Step(3): len = 394997, overlap = 15.75
PHY-3002 : Step(4): len = 377222, overlap = 20.25
PHY-3002 : Step(5): len = 371923, overlap = 18
PHY-3002 : Step(6): len = 366055, overlap = 20.25
PHY-3002 : Step(7): len = 347990, overlap = 18
PHY-3002 : Step(8): len = 339038, overlap = 20.25
PHY-3002 : Step(9): len = 334180, overlap = 13.5
PHY-3002 : Step(10): len = 319166, overlap = 18
PHY-3002 : Step(11): len = 310625, overlap = 13.5
PHY-3002 : Step(12): len = 306257, overlap = 15.75
PHY-3002 : Step(13): len = 295880, overlap = 15.75
PHY-3002 : Step(14): len = 285080, overlap = 15.75
PHY-3002 : Step(15): len = 281698, overlap = 13.5
PHY-3002 : Step(16): len = 276642, overlap = 15.75
PHY-3002 : Step(17): len = 267138, overlap = 15.75
PHY-3002 : Step(18): len = 262297, overlap = 13.5
PHY-3002 : Step(19): len = 258187, overlap = 13.5
PHY-3002 : Step(20): len = 251689, overlap = 13.5
PHY-3002 : Step(21): len = 247001, overlap = 13.5
PHY-3002 : Step(22): len = 242589, overlap = 13.5
PHY-3002 : Step(23): len = 237568, overlap = 13.5
PHY-3002 : Step(24): len = 232107, overlap = 13.5
PHY-3002 : Step(25): len = 227161, overlap = 13.5
PHY-3002 : Step(26): len = 222169, overlap = 13.5
PHY-3002 : Step(27): len = 218133, overlap = 13.5
PHY-3002 : Step(28): len = 212855, overlap = 13.5
PHY-3002 : Step(29): len = 206932, overlap = 13.5
PHY-3002 : Step(30): len = 202333, overlap = 13.5
PHY-3002 : Step(31): len = 198928, overlap = 13.5
PHY-3002 : Step(32): len = 191658, overlap = 13.5
PHY-3002 : Step(33): len = 186077, overlap = 13.5
PHY-3002 : Step(34): len = 183174, overlap = 13.5
PHY-3002 : Step(35): len = 178499, overlap = 13.5
PHY-3002 : Step(36): len = 167418, overlap = 15.75
PHY-3002 : Step(37): len = 162089, overlap = 15.75
PHY-3002 : Step(38): len = 159990, overlap = 15.75
PHY-3002 : Step(39): len = 151027, overlap = 15.75
PHY-3002 : Step(40): len = 120153, overlap = 13.5
PHY-3002 : Step(41): len = 117447, overlap = 11.25
PHY-3002 : Step(42): len = 115948, overlap = 13.5
PHY-3002 : Step(43): len = 106552, overlap = 18
PHY-3002 : Step(44): len = 101910, overlap = 13.5
PHY-3002 : Step(45): len = 100405, overlap = 13.5
PHY-3002 : Step(46): len = 99109.3, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000109974
PHY-3002 : Step(47): len = 99776.4, overlap = 13.5
PHY-3002 : Step(48): len = 99585.7, overlap = 11.25
PHY-3002 : Step(49): len = 99063, overlap = 11.25
PHY-3002 : Step(50): len = 98340.6, overlap = 11.25
PHY-3002 : Step(51): len = 94726.8, overlap = 13.5
PHY-3002 : Step(52): len = 91026.3, overlap = 13.5
PHY-3002 : Step(53): len = 89613.1, overlap = 13.5
PHY-3002 : Step(54): len = 88449.8, overlap = 11.25
PHY-3002 : Step(55): len = 86857.6, overlap = 11.25
PHY-3002 : Step(56): len = 85270.9, overlap = 11.25
PHY-3002 : Step(57): len = 83506.1, overlap = 11.25
PHY-3002 : Step(58): len = 80828.3, overlap = 11.25
PHY-3002 : Step(59): len = 79533.3, overlap = 15.75
PHY-3002 : Step(60): len = 78567.6, overlap = 13.5
PHY-3002 : Step(61): len = 77187.8, overlap = 9
PHY-3002 : Step(62): len = 73486.1, overlap = 13.5625
PHY-3002 : Step(63): len = 72057.2, overlap = 9.3125
PHY-3002 : Step(64): len = 71385.2, overlap = 9.5625
PHY-3002 : Step(65): len = 69815.4, overlap = 12
PHY-3002 : Step(66): len = 69133.1, overlap = 14.3125
PHY-3002 : Step(67): len = 68487.4, overlap = 12.3125
PHY-3002 : Step(68): len = 67550, overlap = 14.4375
PHY-3002 : Step(69): len = 66215.6, overlap = 11.375
PHY-3002 : Step(70): len = 65111.6, overlap = 13.625
PHY-3002 : Step(71): len = 63758.5, overlap = 16
PHY-3002 : Step(72): len = 62252.2, overlap = 13.9375
PHY-3002 : Step(73): len = 61758.5, overlap = 14
PHY-3002 : Step(74): len = 61487.2, overlap = 12.0625
PHY-3002 : Step(75): len = 60637.5, overlap = 16.4375
PHY-3002 : Step(76): len = 59671.3, overlap = 16.5
PHY-3002 : Step(77): len = 58788.3, overlap = 14.3125
PHY-3002 : Step(78): len = 58032.2, overlap = 11.6875
PHY-3002 : Step(79): len = 57616.3, overlap = 11.625
PHY-3002 : Step(80): len = 57444.4, overlap = 16.1875
PHY-3002 : Step(81): len = 57592.2, overlap = 16.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000219948
PHY-3002 : Step(82): len = 57597.6, overlap = 16.125
PHY-3002 : Step(83): len = 57571.5, overlap = 13.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000439895
PHY-3002 : Step(84): len = 57590.5, overlap = 13.9375
PHY-3002 : Step(85): len = 57600.3, overlap = 14
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009411s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (332.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.084055s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (111.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00063347
PHY-3002 : Step(86): len = 61388.6, overlap = 16.625
PHY-3002 : Step(87): len = 60504.8, overlap = 17.4375
PHY-3002 : Step(88): len = 59525.3, overlap = 17.5312
PHY-3002 : Step(89): len = 58146.7, overlap = 18.1562
PHY-3002 : Step(90): len = 57034.7, overlap = 20.7188
PHY-3002 : Step(91): len = 55246.3, overlap = 18.6562
PHY-3002 : Step(92): len = 54275.7, overlap = 19.2188
PHY-3002 : Step(93): len = 53622.3, overlap = 19.1875
PHY-3002 : Step(94): len = 53430.2, overlap = 18.8438
PHY-3002 : Step(95): len = 53127.2, overlap = 18.2812
PHY-3002 : Step(96): len = 53094.6, overlap = 19.5625
PHY-3002 : Step(97): len = 52836.8, overlap = 19.1875
PHY-3002 : Step(98): len = 52096.8, overlap = 19.0938
PHY-3002 : Step(99): len = 51702.5, overlap = 18.7812
PHY-3002 : Step(100): len = 51602.6, overlap = 19.5
PHY-3002 : Step(101): len = 51016.4, overlap = 21.5938
PHY-3002 : Step(102): len = 50080.5, overlap = 16.2812
PHY-3002 : Step(103): len = 49568.2, overlap = 16.2812
PHY-3002 : Step(104): len = 49087.5, overlap = 12.7812
PHY-3002 : Step(105): len = 48634.7, overlap = 12.625
PHY-3002 : Step(106): len = 48313.5, overlap = 11.7812
PHY-3002 : Step(107): len = 48214.5, overlap = 11.9062
PHY-3002 : Step(108): len = 47926.1, overlap = 12.5938
PHY-3002 : Step(109): len = 47464.2, overlap = 12.5
PHY-3002 : Step(110): len = 46969.2, overlap = 11.25
PHY-3002 : Step(111): len = 46898, overlap = 15.0625
PHY-3002 : Step(112): len = 46625.5, overlap = 14.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00126694
PHY-3002 : Step(113): len = 46325, overlap = 14.6875
PHY-3002 : Step(114): len = 46236.6, overlap = 15.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00253388
PHY-3002 : Step(115): len = 46125.9, overlap = 15
PHY-3002 : Step(116): len = 46125.9, overlap = 15
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.084874s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (110.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.25618e-05
PHY-3002 : Step(117): len = 47030.3, overlap = 56.0625
PHY-3002 : Step(118): len = 47488.6, overlap = 55.6875
PHY-3002 : Step(119): len = 48013.2, overlap = 55.125
PHY-3002 : Step(120): len = 47784.2, overlap = 55.25
PHY-3002 : Step(121): len = 47427.1, overlap = 54.4688
PHY-3002 : Step(122): len = 47229.3, overlap = 49.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.51235e-05
PHY-3002 : Step(123): len = 47369.6, overlap = 48.5312
PHY-3002 : Step(124): len = 48081, overlap = 46.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000170247
PHY-3002 : Step(125): len = 48108.6, overlap = 39.25
PHY-3002 : Step(126): len = 49696.4, overlap = 37.2188
PHY-3002 : Step(127): len = 50614.6, overlap = 36.7812
PHY-3002 : Step(128): len = 50388.2, overlap = 36.6875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000340494
PHY-3002 : Step(129): len = 50767.4, overlap = 36.6875
PHY-3002 : Step(130): len = 51563.3, overlap = 39.4688
PHY-3002 : Step(131): len = 52061.7, overlap = 37.0938
PHY-3002 : Step(132): len = 52031, overlap = 36.5625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7342, tnet num: 2080, tinst num: 1526, tnode num: 10326, tedge num: 12465.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 96.47 peak overflow 3.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54096, over cnt = 233(0%), over = 1023, worst = 16
PHY-1001 : End global iterations;  0.120271s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (142.9%)

PHY-1001 : Congestion index: top1 = 45.28, top5 = 25.63, top10 = 15.89, top15 = 11.20.
PHY-1001 : End incremental global routing;  0.198787s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (117.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.101212s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (108.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.344629s wall, 0.343750s user + 0.046875s system = 0.390625s CPU (113.3%)

OPT-1001 : Current memory(MB): used = 210, reserve = 174, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1568/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54096, over cnt = 233(0%), over = 1023, worst = 16
PHY-1002 : len = 62368, over cnt = 144(0%), over = 284, worst = 13
PHY-1002 : len = 65440, over cnt = 42(0%), over = 58, worst = 5
PHY-1002 : len = 66216, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 66280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128961s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (121.2%)

PHY-1001 : Congestion index: top1 = 38.06, top5 = 25.79, top10 = 18.10, top15 = 13.17.
OPT-1001 : End congestion update;  0.191748s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (114.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.114979s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (95.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.312450s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (110.0%)

OPT-1001 : Current memory(MB): used = 214, reserve = 177, peak = 214.
OPT-1001 : End physical optimization;  1.054452s wall, 1.156250s user + 0.062500s system = 1.218750s CPU (115.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 360 LUT to BLE ...
SYN-4008 : Packed 360 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 113 SEQ with LUT/SLICE
SYN-4006 : 93 single LUT's are left
SYN-4006 : 623 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 983/1278 primitive instances ...
PHY-3001 : End packing;  0.073026s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 780 instances
RUN-1001 : 365 mslices, 366 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1919 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1378 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 778 instances, 731 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 51695.6, Over = 68
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6194, tnet num: 1917, tinst num: 778, tnode num: 8364, tedge num: 10927.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.565111s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.78572e-05
PHY-3002 : Step(133): len = 50624.3, overlap = 67.25
PHY-3002 : Step(134): len = 50060.9, overlap = 66
PHY-3002 : Step(135): len = 49684, overlap = 67
PHY-3002 : Step(136): len = 49779.7, overlap = 67.75
PHY-3002 : Step(137): len = 49635.2, overlap = 67
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.57145e-05
PHY-3002 : Step(138): len = 50179.8, overlap = 62.25
PHY-3002 : Step(139): len = 51458.5, overlap = 61
PHY-3002 : Step(140): len = 51894.1, overlap = 58.75
PHY-3002 : Step(141): len = 51476.5, overlap = 58.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000111429
PHY-3002 : Step(142): len = 52134.4, overlap = 57.5
PHY-3002 : Step(143): len = 53009.3, overlap = 56
PHY-3002 : Step(144): len = 53894.8, overlap = 55.25
PHY-3002 : Step(145): len = 54221.4, overlap = 55.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.178714s wall, 0.109375s user + 0.265625s system = 0.375000s CPU (209.8%)

PHY-3001 : Trial Legalized: Len = 68991.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.104677s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (104.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000899291
PHY-3002 : Step(146): len = 65580.7, overlap = 7
PHY-3002 : Step(147): len = 62982.8, overlap = 10.75
PHY-3002 : Step(148): len = 60637.5, overlap = 14.25
PHY-3002 : Step(149): len = 59533.1, overlap = 18.75
PHY-3002 : Step(150): len = 58755.7, overlap = 20
PHY-3002 : Step(151): len = 58387.2, overlap = 22.5
PHY-3002 : Step(152): len = 58171.5, overlap = 21.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00179858
PHY-3002 : Step(153): len = 58449.4, overlap = 19.75
PHY-3002 : Step(154): len = 58552, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00359716
PHY-3002 : Step(155): len = 58642.1, overlap = 20.5
PHY-3002 : Step(156): len = 58773.1, overlap = 21
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009147s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62779, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.009792s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 0, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 62799, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6194, tnet num: 1917, tinst num: 778, tnode num: 8364, tedge num: 10927.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 172/1919.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70528, over cnt = 137(0%), over = 212, worst = 8
PHY-1002 : len = 71160, over cnt = 61(0%), over = 77, worst = 4
PHY-1002 : len = 71888, over cnt = 13(0%), over = 14, worst = 2
PHY-1002 : len = 72104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.227382s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.16, top10 = 18.09, top15 = 14.04.
PHY-1001 : End incremental global routing;  0.308814s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (111.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.099474s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.455664s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (106.3%)

OPT-1001 : Current memory(MB): used = 217, reserve = 180, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1709/1919.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008913s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.16, top10 = 18.09, top15 = 14.04.
OPT-1001 : End congestion update;  0.082118s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (114.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.075471s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (82.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 740 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 778 instances, 731 slices, 23 macros(212 instances: 137 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62813.6, Over = 0
PHY-3001 : End spreading;  0.008312s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (188.0%)

PHY-3001 : Final: Len = 62813.6, Over = 0
PHY-3001 : End incremental legalization;  0.055076s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.232418s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (134.5%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066275s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1701/1919.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72088, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72088, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.031615s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (98.8%)

PHY-1001 : Congestion index: top1 = 32.26, top5 = 23.15, top10 = 18.08, top15 = 14.04.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070691s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.862069
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.567809s wall, 1.625000s user + 0.046875s system = 1.671875s CPU (106.6%)

RUN-1003 : finish command "place" in  9.285108s wall, 13.140625s user + 5.515625s system = 18.656250s CPU (200.9%)

RUN-1004 : used memory is 200 MB, reserved memory is 163 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 780 instances
RUN-1001 : 365 mslices, 366 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1919 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1378 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 67 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6194, tnet num: 1917, tinst num: 778, tnode num: 8364, tedge num: 10927.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 365 mslices, 366 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1917 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68272, over cnt = 150(0%), over = 226, worst = 7
PHY-1002 : len = 69352, over cnt = 67(0%), over = 82, worst = 4
PHY-1002 : len = 70224, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 70408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.224903s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (104.2%)

PHY-1001 : Congestion index: top1 = 31.36, top5 = 22.53, top10 = 17.61, top15 = 13.71.
PHY-1001 : End global routing;  0.300034s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (104.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 200, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 495, reserve = 462, peak = 495.
PHY-1001 : End build detailed router design. 4.647182s wall, 4.546875s user + 0.062500s system = 4.609375s CPU (99.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31560, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.813937s wall, 1.796875s user + 0.015625s system = 1.812500s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 527, reserve = 495, peak = 527.
PHY-1001 : End phase 1; 1.820912s wall, 1.812500s user + 0.015625s system = 1.828125s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179352, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 496, peak = 529.
PHY-1001 : End initial routed; 1.849647s wall, 2.734375s user + 0.109375s system = 2.843750s CPU (153.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1693(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.691   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.321   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.519261s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 530, reserve = 498, peak = 530.
PHY-1001 : End phase 2; 2.369020s wall, 3.250000s user + 0.109375s system = 3.359375s CPU (141.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179352, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.019154s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (81.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179304, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030370s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179352, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.023856s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (131.0%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 179368, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021271s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (73.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1693(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.691   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.321   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.514585s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.257185s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (97.2%)

PHY-1001 : Current memory(MB): used = 545, reserve = 513, peak = 545.
PHY-1001 : End phase 3; 1.038090s wall, 1.031250s user + 0.000000s system = 1.031250s CPU (99.3%)

PHY-1003 : Routed, final wirelength = 179368
PHY-1001 : Current memory(MB): used = 546, reserve = 514, peak = 546.
PHY-1001 : End export database. 0.011486s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (136.0%)

PHY-1001 : End detail routing;  10.137790s wall, 10.906250s user + 0.203125s system = 11.109375s CPU (109.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6194, tnet num: 1917, tinst num: 778, tnode num: 8364, tedge num: 10927.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6226, tnet num: 1933, tinst num: 794, tnode num: 8396, tedge num: 10959.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  4.192805s wall, 4.250000s user + 0.218750s system = 4.468750s CPU (106.6%)

RUN-1003 : finish command "route" in  15.132437s wall, 15.953125s user + 0.437500s system = 16.390625s CPU (108.3%)

RUN-1004 : used memory is 523 MB, reserved memory is 491 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      816   out of  19600    4.16%
#reg                      989   out of  19600    5.05%
#le                      1439
  #lut only               450   out of   1439   31.27%
  #reg only               623   out of   1439   43.29%
  #lut&reg                366   out of   1439   25.43%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         436
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1439   |604     |212     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1050   |313     |120     |840     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |29     |22      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |461    |116     |44      |346     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |27      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|    integ                   |Integration                                      |137    |31      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |93     |36      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |309    |92      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |21     |16      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |100    |87      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |44     |41      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1358  
    #2          2       308   
    #3          3       108   
    #4          4        15   
    #5        5-10       75   
    #6        11-50      27   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6226, tnet num: 1933, tinst num: 794, tnode num: 8396, tedge num: 10959.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 794
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1935, pip num: 14238
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1287 valid insts, and 37804 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.779078s wall, 24.843750s user + 0.093750s system = 24.937500s CPU (521.8%)

RUN-1004 : used memory is 520 MB, reserved memory is 492 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231019_175225.log"
