============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Aug 18 10:02:20 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1629 instances
RUN-0007 : 383 luts, 978 seqs, 144 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2183 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1648 nets have 2 pins
RUN-1001 : 416 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1627 instances, 383 luts, 978 seqs, 219 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7719, tnet num: 2181, tinst num: 1627, tnode num: 10898, tedge num: 12996.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.281798s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (94.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 541741
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1627.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 442839, overlap = 20.25
PHY-3002 : Step(2): len = 338490, overlap = 15.75
PHY-3002 : Step(3): len = 317335, overlap = 15.75
PHY-3002 : Step(4): len = 304369, overlap = 20.25
PHY-3002 : Step(5): len = 297997, overlap = 20.25
PHY-3002 : Step(6): len = 291619, overlap = 20.25
PHY-3002 : Step(7): len = 287343, overlap = 20.25
PHY-3002 : Step(8): len = 279592, overlap = 18
PHY-3002 : Step(9): len = 273351, overlap = 18
PHY-3002 : Step(10): len = 269204, overlap = 18
PHY-3002 : Step(11): len = 262857, overlap = 18
PHY-3002 : Step(12): len = 257821, overlap = 18
PHY-3002 : Step(13): len = 253633, overlap = 20.25
PHY-3002 : Step(14): len = 248125, overlap = 20.25
PHY-3002 : Step(15): len = 243542, overlap = 20.25
PHY-3002 : Step(16): len = 238114, overlap = 20.25
PHY-3002 : Step(17): len = 233105, overlap = 20.25
PHY-3002 : Step(18): len = 228597, overlap = 20.25
PHY-3002 : Step(19): len = 224746, overlap = 20.25
PHY-3002 : Step(20): len = 216464, overlap = 18
PHY-3002 : Step(21): len = 210646, overlap = 20.25
PHY-3002 : Step(22): len = 207905, overlap = 20.25
PHY-3002 : Step(23): len = 201401, overlap = 20.25
PHY-3002 : Step(24): len = 189054, overlap = 15.75
PHY-3002 : Step(25): len = 185914, overlap = 18
PHY-3002 : Step(26): len = 182540, overlap = 18
PHY-3002 : Step(27): len = 132760, overlap = 15.75
PHY-3002 : Step(28): len = 127527, overlap = 13.5
PHY-3002 : Step(29): len = 126211, overlap = 13.5
PHY-3002 : Step(30): len = 122883, overlap = 9
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00018914
PHY-3002 : Step(31): len = 122745, overlap = 9
PHY-3002 : Step(32): len = 121220, overlap = 4.5
PHY-3002 : Step(33): len = 118793, overlap = 6.75
PHY-3002 : Step(34): len = 116730, overlap = 11.25
PHY-3002 : Step(35): len = 114288, overlap = 4.5
PHY-3002 : Step(36): len = 113588, overlap = 4.5
PHY-3002 : Step(37): len = 110835, overlap = 4.5
PHY-3002 : Step(38): len = 109742, overlap = 4.5
PHY-3002 : Step(39): len = 108987, overlap = 4.5
PHY-3002 : Step(40): len = 108508, overlap = 4.5
PHY-3002 : Step(41): len = 106223, overlap = 11.25
PHY-3002 : Step(42): len = 102484, overlap = 6.75
PHY-3002 : Step(43): len = 97624.1, overlap = 4.5
PHY-3002 : Step(44): len = 97079.6, overlap = 2.25
PHY-3002 : Step(45): len = 95311.3, overlap = 6.75
PHY-3002 : Step(46): len = 94215.7, overlap = 11.25
PHY-3002 : Step(47): len = 93469.4, overlap = 6.75
PHY-3002 : Step(48): len = 92263, overlap = 4.5
PHY-3002 : Step(49): len = 91040.8, overlap = 4.5
PHY-3002 : Step(50): len = 88934.1, overlap = 6.8125
PHY-3002 : Step(51): len = 87417.1, overlap = 6.9375
PHY-3002 : Step(52): len = 85999.7, overlap = 4.5
PHY-3002 : Step(53): len = 84709.7, overlap = 6.75
PHY-3002 : Step(54): len = 81128.5, overlap = 11.5
PHY-3002 : Step(55): len = 79566.9, overlap = 7.5625
PHY-3002 : Step(56): len = 78576, overlap = 5.25
PHY-3002 : Step(57): len = 77541.2, overlap = 8
PHY-3002 : Step(58): len = 74628.6, overlap = 9.9375
PHY-3002 : Step(59): len = 73001.1, overlap = 9.75
PHY-3002 : Step(60): len = 72364.1, overlap = 7.5
PHY-3002 : Step(61): len = 71629.2, overlap = 5.3125
PHY-3002 : Step(62): len = 71042.4, overlap = 5.375
PHY-3002 : Step(63): len = 69702.2, overlap = 5.0625
PHY-3002 : Step(64): len = 67328.9, overlap = 12.375
PHY-3002 : Step(65): len = 66382.9, overlap = 7.875
PHY-3002 : Step(66): len = 65682.5, overlap = 5.5625
PHY-3002 : Step(67): len = 65221.2, overlap = 10.125
PHY-3002 : Step(68): len = 64789.6, overlap = 9.8125
PHY-3002 : Step(69): len = 64050.8, overlap = 9.8125
PHY-3002 : Step(70): len = 63337.5, overlap = 7.6875
PHY-3002 : Step(71): len = 62778.1, overlap = 7.875
PHY-3002 : Step(72): len = 62047.2, overlap = 7.875
PHY-3002 : Step(73): len = 61853.5, overlap = 7.8125
PHY-3002 : Step(74): len = 61558.1, overlap = 5.5625
PHY-3002 : Step(75): len = 60915.7, overlap = 7.9375
PHY-3002 : Step(76): len = 60152.9, overlap = 5.875
PHY-3002 : Step(77): len = 59910.1, overlap = 5.875
PHY-3002 : Step(78): len = 59620.8, overlap = 8.125
PHY-3002 : Step(79): len = 59040.3, overlap = 8
PHY-3002 : Step(80): len = 58413.5, overlap = 8
PHY-3002 : Step(81): len = 57803.5, overlap = 7.9375
PHY-3002 : Step(82): len = 56286.7, overlap = 8.25
PHY-3002 : Step(83): len = 55025.3, overlap = 10.5
PHY-3002 : Step(84): len = 53225.2, overlap = 10.5
PHY-3002 : Step(85): len = 53236.7, overlap = 10.5
PHY-3002 : Step(86): len = 53070.9, overlap = 6
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000378281
PHY-3002 : Step(87): len = 52956.3, overlap = 6
PHY-3002 : Step(88): len = 52904.6, overlap = 3.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000756562
PHY-3002 : Step(89): len = 52929.1, overlap = 3.75
PHY-3002 : Step(90): len = 52878, overlap = 3.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008719s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (179.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.075993s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (102.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(91): len = 54487, overlap = 10.8125
PHY-3002 : Step(92): len = 53680.5, overlap = 10.25
PHY-3002 : Step(93): len = 53188.2, overlap = 9.375
PHY-3002 : Step(94): len = 52370.1, overlap = 9.375
PHY-3002 : Step(95): len = 51868, overlap = 9.1875
PHY-3002 : Step(96): len = 50739, overlap = 10.1875
PHY-3002 : Step(97): len = 49903.9, overlap = 10.375
PHY-3002 : Step(98): len = 48489.9, overlap = 11.3125
PHY-3002 : Step(99): len = 48252.3, overlap = 11.3125
PHY-3002 : Step(100): len = 47761.6, overlap = 12.5625
PHY-3002 : Step(101): len = 47140.5, overlap = 13.8125
PHY-3002 : Step(102): len = 46121.8, overlap = 16.125
PHY-3002 : Step(103): len = 45832, overlap = 18.125
PHY-3002 : Step(104): len = 45522.8, overlap = 19.0625
PHY-3002 : Step(105): len = 45376.2, overlap = 19.9375
PHY-3002 : Step(106): len = 44730.8, overlap = 22.5
PHY-3002 : Step(107): len = 44400, overlap = 22.6875
PHY-3002 : Step(108): len = 44318.2, overlap = 23.4375
PHY-3002 : Step(109): len = 44246.1, overlap = 23.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00111579
PHY-3002 : Step(110): len = 44119, overlap = 24.3125
PHY-3002 : Step(111): len = 44140.7, overlap = 24.4062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00223158
PHY-3002 : Step(112): len = 44116.5, overlap = 24.5625
PHY-3002 : Step(113): len = 44132.2, overlap = 25.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065680s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.82648e-05
PHY-3002 : Step(114): len = 44361.9, overlap = 70.0312
PHY-3002 : Step(115): len = 44685.7, overlap = 66.5312
PHY-3002 : Step(116): len = 45256.3, overlap = 61.4062
PHY-3002 : Step(117): len = 45722.7, overlap = 50.3125
PHY-3002 : Step(118): len = 45764.6, overlap = 50.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00013653
PHY-3002 : Step(119): len = 45793.5, overlap = 49.4375
PHY-3002 : Step(120): len = 46239.9, overlap = 46.625
PHY-3002 : Step(121): len = 46528.3, overlap = 41.6562
PHY-3002 : Step(122): len = 46450.6, overlap = 36.7812
PHY-3002 : Step(123): len = 46414.6, overlap = 36.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000273059
PHY-3002 : Step(124): len = 46530.7, overlap = 35.8438
PHY-3002 : Step(125): len = 47178.3, overlap = 36.0625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7719, tnet num: 2181, tinst num: 1627, tnode num: 10898, tedge num: 12996.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.88 peak overflow 3.44
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51272, over cnt = 253(0%), over = 1117, worst = 21
PHY-1001 : End global iterations;  0.070423s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.9%)

PHY-1001 : Congestion index: top1 = 43.51, top5 = 25.68, top10 = 16.14, top15 = 11.36.
PHY-1001 : End incremental global routing;  0.118092s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (92.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065397s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (119.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.212933s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (95.4%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1664/2183.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51272, over cnt = 253(0%), over = 1117, worst = 21
PHY-1002 : len = 58440, over cnt = 173(0%), over = 398, worst = 21
PHY-1002 : len = 62784, over cnt = 31(0%), over = 49, worst = 8
PHY-1002 : len = 63400, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 63496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.098482s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (95.2%)

PHY-1001 : Congestion index: top1 = 37.35, top5 = 25.85, top10 = 18.24, top15 = 13.25.
OPT-1001 : End congestion update;  0.144397s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (108.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2181 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069945s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.217049s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.8%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : End physical optimization;  0.695097s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (114.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 383 LUT to BLE ...
SYN-4008 : Packed 383 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 90 SEQ with LUT/SLICE
SYN-4006 : 127 single LUT's are left
SYN-4006 : 700 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1083/1409 primitive instances ...
PHY-3001 : End packing;  0.053417s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 836 instances
RUN-1001 : 394 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2009 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1482 nets have 2 pins
RUN-1001 : 406 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 834 instances, 787 slices, 26 macros(219 instances: 144 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 47412.8, Over = 59.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6488, tnet num: 2007, tinst num: 834, tnode num: 8778, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.316059s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (103.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.98231e-05
PHY-3002 : Step(126): len = 46905.3, overlap = 60.25
PHY-3002 : Step(127): len = 46420.3, overlap = 62
PHY-3002 : Step(128): len = 46418.9, overlap = 65.5
PHY-3002 : Step(129): len = 46062.4, overlap = 66
PHY-3002 : Step(130): len = 45964.4, overlap = 64.75
PHY-3002 : Step(131): len = 45961.7, overlap = 64.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.96463e-05
PHY-3002 : Step(132): len = 46123.1, overlap = 64
PHY-3002 : Step(133): len = 46448.5, overlap = 62.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.44395e-05
PHY-3002 : Step(134): len = 46862.3, overlap = 61.25
PHY-3002 : Step(135): len = 47937.5, overlap = 56.5
PHY-3002 : Step(136): len = 49037.5, overlap = 50.75
PHY-3002 : Step(137): len = 49212.3, overlap = 51
PHY-3002 : Step(138): len = 49526.9, overlap = 51.5
PHY-3002 : Step(139): len = 49819.9, overlap = 49.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075321s wall, 0.062500s user + 0.109375s system = 0.171875s CPU (228.2%)

PHY-3001 : Trial Legalized: Len = 64069.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050765s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000464353
PHY-3002 : Step(140): len = 61104.4, overlap = 4.25
PHY-3002 : Step(141): len = 59213.1, overlap = 8.25
PHY-3002 : Step(142): len = 57707.1, overlap = 14.75
PHY-3002 : Step(143): len = 56853.8, overlap = 18
PHY-3002 : Step(144): len = 56225.5, overlap = 19.25
PHY-3002 : Step(145): len = 55715.3, overlap = 20
PHY-3002 : Step(146): len = 55360.4, overlap = 21
PHY-3002 : Step(147): len = 54959.9, overlap = 19.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000928705
PHY-3002 : Step(148): len = 55383.6, overlap = 18
PHY-3002 : Step(149): len = 55498.6, overlap = 18.25
PHY-3002 : Step(150): len = 55551, overlap = 19.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00185741
PHY-3002 : Step(151): len = 55667.9, overlap = 19.75
PHY-3002 : Step(152): len = 55692, overlap = 19.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005025s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 60091.6, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006013s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (259.8%)

PHY-3001 : 5 instances has been re-located, deltaX = 0, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 60195.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6488, tnet num: 2007, tinst num: 834, tnode num: 8778, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 91/2009.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66872, over cnt = 140(0%), over = 200, worst = 6
PHY-1002 : len = 67456, over cnt = 93(0%), over = 117, worst = 3
PHY-1002 : len = 68488, over cnt = 29(0%), over = 33, worst = 2
PHY-1002 : len = 68920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122436s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (102.1%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.63, top10 = 17.51, top15 = 13.85.
PHY-1001 : End incremental global routing;  0.172952s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (90.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055909s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.257889s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (96.9%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1779/2009.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005968s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (261.8%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.63, top10 = 17.51, top15 = 13.85.
OPT-1001 : End congestion update;  0.050371s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047573s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.099438s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.3%)

OPT-1001 : Current memory(MB): used = 224, reserve = 188, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046332s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1779/2009.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005489s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.80, top5 = 22.63, top10 = 17.51, top15 = 13.85.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047448s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.275862
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.800060s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (97.6%)

RUN-1003 : finish command "place" in  5.242795s wall, 7.546875s user + 2.828125s system = 10.375000s CPU (197.9%)

RUN-1004 : used memory is 203 MB, reserved memory is 167 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 836 instances
RUN-1001 : 394 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2009 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1482 nets have 2 pins
RUN-1001 : 406 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6488, tnet num: 2007, tinst num: 834, tnode num: 8778, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 394 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66072, over cnt = 147(0%), over = 208, worst = 7
PHY-1002 : len = 66832, over cnt = 95(0%), over = 119, worst = 3
PHY-1002 : len = 68080, over cnt = 14(0%), over = 16, worst = 2
PHY-1002 : len = 68304, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125291s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (124.7%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.47, top10 = 17.34, top15 = 13.72.
PHY-1001 : End global routing;  0.174487s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (116.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 206, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 467, peak = 499.
PHY-1001 : End build detailed router design. 3.219248s wall, 3.218750s user + 0.015625s system = 3.234375s CPU (100.5%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35232, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.277827s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (97.8%)

PHY-1001 : Current memory(MB): used = 531, reserve = 500, peak = 531.
PHY-1001 : End phase 1; 1.284238s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (98.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181624, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 501, peak = 534.
PHY-1001 : End initial routed; 0.979672s wall, 2.031250s user + 0.140625s system = 2.171875s CPU (221.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1779(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.718   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.359368s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End phase 2; 1.339122s wall, 2.390625s user + 0.140625s system = 2.531250s CPU (189.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181624, over cnt = 23(0%), over = 23, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013988s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181568, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025438s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (61.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181584, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022619s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (138.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1779(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.718   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.368802s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (101.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.178220s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.4%)

PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End phase 3; 0.726380s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (101.1%)

PHY-1003 : Routed, final wirelength = 181584
PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End export database. 0.010839s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (144.2%)

PHY-1001 : End detail routing;  6.765090s wall, 7.781250s user + 0.171875s system = 7.953125s CPU (117.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6488, tnet num: 2007, tinst num: 834, tnode num: 8778, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.672170s wall, 8.718750s user + 0.171875s system = 8.890625s CPU (115.9%)

RUN-1004 : used memory is 502 MB, reserved memory is 471 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      826   out of  19600    4.21%
#reg                     1045   out of  19600    5.33%
#le                      1526
  #lut only               481   out of   1526   31.52%
  #reg only               700   out of   1526   45.87%
  #lut&reg                345   out of   1526   22.61%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         454
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1526   |607     |219     |1076    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1108   |287     |126     |890     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |33     |26      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |539    |125     |58      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |159    |60      |20      |128     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |0       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |16      |0       |27      |0       |0       |
|    integ                   |Integration                                      |139    |14      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |57     |20      |14      |53      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |78      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |24      |4       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |116    |104     |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |58     |58      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |219    |174     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1446  
    #2          2       282   
    #3          3       111   
    #4          4        13   
    #5        5-10       82   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6488, tnet num: 2007, tinst num: 834, tnode num: 8778, tedge num: 11360.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2007 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 834
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2009, pip num: 14573
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1310 valid insts, and 38639 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.116589s wall, 17.140625s user + 0.015625s system = 17.156250s CPU (550.5%)

RUN-1004 : used memory is 517 MB, reserved memory is 485 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230818_100219.log"
