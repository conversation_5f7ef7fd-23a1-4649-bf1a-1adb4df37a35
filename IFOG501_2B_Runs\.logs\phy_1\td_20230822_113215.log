============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Aug 22 11:32:15 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1629 instances
RUN-0007 : 369 luts, 985 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2199 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1641 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1627 instances, 369 luts, 985 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7791, tnet num: 2197, tinst num: 1627, tnode num: 11031, tedge num: 13173.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.299359s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (94.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 592638
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1627.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 544234, overlap = 20.25
PHY-3002 : Step(2): len = 439164, overlap = 15.75
PHY-3002 : Step(3): len = 364169, overlap = 11.25
PHY-3002 : Step(4): len = 349486, overlap = 15.75
PHY-3002 : Step(5): len = 340962, overlap = 15.75
PHY-3002 : Step(6): len = 333106, overlap = 15.75
PHY-3002 : Step(7): len = 323746, overlap = 15.75
PHY-3002 : Step(8): len = 313850, overlap = 15.75
PHY-3002 : Step(9): len = 308767, overlap = 15.75
PHY-3002 : Step(10): len = 298756, overlap = 15.75
PHY-3002 : Step(11): len = 290661, overlap = 18
PHY-3002 : Step(12): len = 284389, overlap = 18
PHY-3002 : Step(13): len = 279522, overlap = 18
PHY-3002 : Step(14): len = 270046, overlap = 18
PHY-3002 : Step(15): len = 265149, overlap = 18
PHY-3002 : Step(16): len = 259853, overlap = 18
PHY-3002 : Step(17): len = 253228, overlap = 18
PHY-3002 : Step(18): len = 244903, overlap = 18
PHY-3002 : Step(19): len = 242004, overlap = 18
PHY-3002 : Step(20): len = 235140, overlap = 18
PHY-3002 : Step(21): len = 226501, overlap = 18
PHY-3002 : Step(22): len = 222042, overlap = 18
PHY-3002 : Step(23): len = 218894, overlap = 18
PHY-3002 : Step(24): len = 200111, overlap = 18
PHY-3002 : Step(25): len = 196145, overlap = 18
PHY-3002 : Step(26): len = 193720, overlap = 18
PHY-3002 : Step(27): len = 183525, overlap = 20.25
PHY-3002 : Step(28): len = 167733, overlap = 20.25
PHY-3002 : Step(29): len = 165944, overlap = 20.25
PHY-3002 : Step(30): len = 162639, overlap = 20.25
PHY-3002 : Step(31): len = 155749, overlap = 20.25
PHY-3002 : Step(32): len = 151835, overlap = 20.25
PHY-3002 : Step(33): len = 145758, overlap = 20.25
PHY-3002 : Step(34): len = 143374, overlap = 20.25
PHY-3002 : Step(35): len = 139582, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.72761e-05
PHY-3002 : Step(36): len = 140669, overlap = 13.5
PHY-3002 : Step(37): len = 139302, overlap = 11.25
PHY-3002 : Step(38): len = 136727, overlap = 15.75
PHY-3002 : Step(39): len = 134457, overlap = 13.5
PHY-3002 : Step(40): len = 129035, overlap = 18
PHY-3002 : Step(41): len = 128092, overlap = 9
PHY-3002 : Step(42): len = 125271, overlap = 11.25
PHY-3002 : Step(43): len = 121208, overlap = 9
PHY-3002 : Step(44): len = 111713, overlap = 13.5
PHY-3002 : Step(45): len = 109991, overlap = 11.25
PHY-3002 : Step(46): len = 108181, overlap = 13.5
PHY-3002 : Step(47): len = 107021, overlap = 11.25
PHY-3002 : Step(48): len = 106593, overlap = 11.25
PHY-3002 : Step(49): len = 104916, overlap = 6.75
PHY-3002 : Step(50): len = 99066.8, overlap = 9
PHY-3002 : Step(51): len = 96348.8, overlap = 6.75
PHY-3002 : Step(52): len = 94812.1, overlap = 9
PHY-3002 : Step(53): len = 94202.8, overlap = 9
PHY-3002 : Step(54): len = 92465.4, overlap = 9
PHY-3002 : Step(55): len = 90630.3, overlap = 9
PHY-3002 : Step(56): len = 87803.5, overlap = 9
PHY-3002 : Step(57): len = 84635.4, overlap = 9
PHY-3002 : Step(58): len = 83319.7, overlap = 11.25
PHY-3002 : Step(59): len = 83300.4, overlap = 11.25
PHY-3002 : Step(60): len = 81096.2, overlap = 9
PHY-3002 : Step(61): len = 79436.8, overlap = 6.75
PHY-3002 : Step(62): len = 78700.3, overlap = 9
PHY-3002 : Step(63): len = 77335.3, overlap = 9
PHY-3002 : Step(64): len = 74989, overlap = 11.25
PHY-3002 : Step(65): len = 73390.5, overlap = 9
PHY-3002 : Step(66): len = 72349.1, overlap = 9
PHY-3002 : Step(67): len = 71588.8, overlap = 11.25
PHY-3002 : Step(68): len = 70539.9, overlap = 11.25
PHY-3002 : Step(69): len = 70100.5, overlap = 9
PHY-3002 : Step(70): len = 68611.4, overlap = 6.75
PHY-3002 : Step(71): len = 67762.6, overlap = 9
PHY-3002 : Step(72): len = 66085.9, overlap = 9
PHY-3002 : Step(73): len = 65425.4, overlap = 11.25
PHY-3002 : Step(74): len = 64438.2, overlap = 11.25
PHY-3002 : Step(75): len = 64054.6, overlap = 11.25
PHY-3002 : Step(76): len = 63437.5, overlap = 9
PHY-3002 : Step(77): len = 62481.9, overlap = 9
PHY-3002 : Step(78): len = 61949.9, overlap = 11.25
PHY-3002 : Step(79): len = 60815.2, overlap = 11.25
PHY-3002 : Step(80): len = 59954.8, overlap = 9
PHY-3002 : Step(81): len = 59472.4, overlap = 9
PHY-3002 : Step(82): len = 58504, overlap = 11.25
PHY-3002 : Step(83): len = 57463.3, overlap = 11.25
PHY-3002 : Step(84): len = 57110.6, overlap = 11.25
PHY-3002 : Step(85): len = 56434.4, overlap = 9
PHY-3002 : Step(86): len = 56076.2, overlap = 9
PHY-3002 : Step(87): len = 55835.7, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000194552
PHY-3002 : Step(88): len = 56068.8, overlap = 6.75
PHY-3002 : Step(89): len = 56124.1, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000389104
PHY-3002 : Step(90): len = 56183, overlap = 6.75
PHY-3002 : Step(91): len = 56227.4, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008315s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (187.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065038s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(92): len = 60219.3, overlap = 6.375
PHY-3002 : Step(93): len = 59404.7, overlap = 6.1875
PHY-3002 : Step(94): len = 58897.9, overlap = 6.25
PHY-3002 : Step(95): len = 57994, overlap = 7.3125
PHY-3002 : Step(96): len = 57530.9, overlap = 7.4375
PHY-3002 : Step(97): len = 56378.1, overlap = 7.625
PHY-3002 : Step(98): len = 55329.4, overlap = 7.3125
PHY-3002 : Step(99): len = 54640.4, overlap = 6.125
PHY-3002 : Step(100): len = 53244.3, overlap = 3.8125
PHY-3002 : Step(101): len = 52854.4, overlap = 3.4375
PHY-3002 : Step(102): len = 52671.7, overlap = 4.1875
PHY-3002 : Step(103): len = 52194.9, overlap = 4.1875
PHY-3002 : Step(104): len = 51550.5, overlap = 6.125
PHY-3002 : Step(105): len = 51592, overlap = 6.625
PHY-3002 : Step(106): len = 51292, overlap = 9.0625
PHY-3002 : Step(107): len = 50309.7, overlap = 11.8125
PHY-3002 : Step(108): len = 49310.8, overlap = 12.8125
PHY-3002 : Step(109): len = 48657.6, overlap = 13.1562
PHY-3002 : Step(110): len = 47918.2, overlap = 13.8125
PHY-3002 : Step(111): len = 47849.8, overlap = 12.9375
PHY-3002 : Step(112): len = 47645.4, overlap = 13.9062
PHY-3002 : Step(113): len = 47703.9, overlap = 14
PHY-3002 : Step(114): len = 47609, overlap = 15.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000314951
PHY-3002 : Step(115): len = 47286.1, overlap = 15.0625
PHY-3002 : Step(116): len = 47181.5, overlap = 15.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000629901
PHY-3002 : Step(117): len = 47207.7, overlap = 14.9375
PHY-3002 : Step(118): len = 47343.3, overlap = 15
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060734s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.0228e-05
PHY-3002 : Step(119): len = 47762.4, overlap = 61.5312
PHY-3002 : Step(120): len = 47959.6, overlap = 57.0312
PHY-3002 : Step(121): len = 49052.8, overlap = 58.1875
PHY-3002 : Step(122): len = 50048.4, overlap = 54.8438
PHY-3002 : Step(123): len = 50238.8, overlap = 46.625
PHY-3002 : Step(124): len = 49985.7, overlap = 47.2812
PHY-3002 : Step(125): len = 49487.5, overlap = 46.8438
PHY-3002 : Step(126): len = 49342.5, overlap = 46.75
PHY-3002 : Step(127): len = 49015.4, overlap = 40.4375
PHY-3002 : Step(128): len = 48931.8, overlap = 40.6562
PHY-3002 : Step(129): len = 49065.8, overlap = 40.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000160456
PHY-3002 : Step(130): len = 48942.4, overlap = 40.5
PHY-3002 : Step(131): len = 49434.9, overlap = 40.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000320912
PHY-3002 : Step(132): len = 49538.6, overlap = 40.1875
PHY-3002 : Step(133): len = 50763.7, overlap = 37.875
PHY-3002 : Step(134): len = 51809.4, overlap = 32.875
PHY-3002 : Step(135): len = 51903.7, overlap = 31.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7791, tnet num: 2197, tinst num: 1627, tnode num: 11031, tedge num: 13173.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 83.16 peak overflow 3.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2199.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54952, over cnt = 248(0%), over = 1144, worst = 20
PHY-1001 : End global iterations;  0.078851s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (118.9%)

PHY-1001 : Congestion index: top1 = 46.42, top5 = 25.65, top10 = 16.15, top15 = 11.53.
PHY-1001 : End incremental global routing;  0.128518s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (109.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067440s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.7%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1588 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1629 instances, 369 luts, 987 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 52024.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7799, tnet num: 2199, tinst num: 1629, tnode num: 11045, tedge num: 13185.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.304726s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (102.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(136): len = 52035.9, overlap = 2.28125
PHY-3002 : Step(137): len = 52065.4, overlap = 2.40625
PHY-3002 : Step(138): len = 52066.1, overlap = 2.46875
PHY-3002 : Step(139): len = 52046.3, overlap = 2.46875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059621s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000470781
PHY-3002 : Step(140): len = 52071.5, overlap = 31.9062
PHY-3002 : Step(141): len = 52094.4, overlap = 31.9062
PHY-3001 : Final: Len = 52094.4, Over = 31.9062
PHY-3001 : End incremental placement;  0.447416s wall, 0.515625s user + 0.062500s system = 0.578125s CPU (129.2%)

OPT-1001 : Total overflow 83.16 peak overflow 3.66
OPT-1001 : End high-fanout net optimization;  0.680054s wall, 0.734375s user + 0.078125s system = 0.812500s CPU (119.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1707/2201.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55080, over cnt = 249(0%), over = 1142, worst = 20
PHY-1002 : len = 62632, over cnt = 181(0%), over = 495, worst = 16
PHY-1002 : len = 68248, over cnt = 43(0%), over = 56, worst = 5
PHY-1002 : len = 68984, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 69496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109727s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (185.1%)

PHY-1001 : Congestion index: top1 = 40.67, top5 = 26.19, top10 = 18.68, top15 = 13.84.
OPT-1001 : End congestion update;  0.157326s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (158.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057789s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.217772s wall, 0.265625s user + 0.046875s system = 0.312500s CPU (143.5%)

OPT-1001 : Current memory(MB): used = 221, reserve = 184, peak = 221.
OPT-1001 : End physical optimization;  1.167854s wall, 1.375000s user + 0.125000s system = 1.500000s CPU (128.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 93 SEQ with LUT/SLICE
SYN-4006 : 110 single LUT's are left
SYN-4006 : 712 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1081/1414 primitive instances ...
PHY-3001 : End packing;  0.050064s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 846 instances
RUN-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2034 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 844 instances, 797 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51963.4, Over = 61.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2032, tinst num: 844, tnode num: 8926, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.304615s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.50424e-05
PHY-3002 : Step(142): len = 51451.7, overlap = 61
PHY-3002 : Step(143): len = 50853.4, overlap = 63
PHY-3002 : Step(144): len = 50579.4, overlap = 62.75
PHY-3002 : Step(145): len = 50732.8, overlap = 62.25
PHY-3002 : Step(146): len = 50482.9, overlap = 63.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.00847e-05
PHY-3002 : Step(147): len = 50769.8, overlap = 62.75
PHY-3002 : Step(148): len = 51883, overlap = 61.75
PHY-3002 : Step(149): len = 52001.6, overlap = 61
PHY-3002 : Step(150): len = 52116.3, overlap = 59.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000100169
PHY-3002 : Step(151): len = 52349.7, overlap = 59.25
PHY-3002 : Step(152): len = 53543.9, overlap = 58.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.092784s wall, 0.031250s user + 0.156250s system = 0.187500s CPU (202.1%)

PHY-3001 : Trial Legalized: Len = 69562.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055252s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0006254
PHY-3002 : Step(153): len = 66098.3, overlap = 8.75
PHY-3002 : Step(154): len = 63707.1, overlap = 14
PHY-3002 : Step(155): len = 62260.7, overlap = 14
PHY-3002 : Step(156): len = 60978.4, overlap = 20.25
PHY-3002 : Step(157): len = 60093.4, overlap = 21
PHY-3002 : Step(158): len = 59641, overlap = 21.75
PHY-3002 : Step(159): len = 59252.9, overlap = 24.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0012508
PHY-3002 : Step(160): len = 59679.5, overlap = 24.75
PHY-3002 : Step(161): len = 59814.4, overlap = 24.5
PHY-3002 : Step(162): len = 59784.5, overlap = 24.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0025016
PHY-3002 : Step(163): len = 59967.7, overlap = 24.75
PHY-3002 : Step(164): len = 59967.7, overlap = 24.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004901s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64864.8, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005534s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (282.3%)

PHY-3001 : 13 instances has been re-located, deltaX = 3, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 64984.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2032, tinst num: 844, tnode num: 8926, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 83/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71224, over cnt = 136(0%), over = 212, worst = 7
PHY-1002 : len = 72208, over cnt = 66(0%), over = 79, worst = 4
PHY-1002 : len = 72896, over cnt = 22(0%), over = 28, worst = 3
PHY-1002 : len = 73408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106746s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (131.7%)

PHY-1001 : Congestion index: top1 = 32.33, top5 = 22.91, top10 = 17.92, top15 = 14.24.
PHY-1001 : End incremental global routing;  0.159023s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (137.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062636s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.251674s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (118.0%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1795/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006296s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (248.2%)

PHY-1001 : Congestion index: top1 = 32.33, top5 = 22.91, top10 = 17.92, top15 = 14.24.
OPT-1001 : End congestion update;  0.052294s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051341s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 806 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 844 instances, 797 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65069.4, Over = 0
PHY-3001 : End spreading;  0.005212s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 65069.4, Over = 0
PHY-3001 : End incremental legalization;  0.035138s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (177.9%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.151114s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (113.7%)

OPT-1001 : Current memory(MB): used = 224, reserve = 190, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047693s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1788/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008496s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (183.9%)

PHY-1001 : Congestion index: top1 = 32.41, top5 = 22.94, top10 = 17.93, top15 = 14.25.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050287s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.857594s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (107.5%)

RUN-1003 : finish command "place" in  5.850385s wall, 8.390625s user + 3.328125s system = 11.718750s CPU (200.3%)

RUN-1004 : used memory is 203 MB, reserved memory is 167 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 846 instances
RUN-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2034 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 433 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2032, tinst num: 844, tnode num: 8926, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 399 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71048, over cnt = 133(0%), over = 207, worst = 7
PHY-1002 : len = 72104, over cnt = 68(0%), over = 81, worst = 4
PHY-1002 : len = 72872, over cnt = 17(0%), over = 19, worst = 2
PHY-1002 : len = 73208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112700s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (138.6%)

PHY-1001 : Congestion index: top1 = 32.87, top5 = 22.92, top10 = 17.85, top15 = 14.16.
PHY-1001 : End global routing;  0.162180s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (125.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 468, peak = 500.
PHY-1001 : End build detailed router design. 3.165315s wall, 3.125000s user + 0.031250s system = 3.156250s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34360, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.284789s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (102.2%)

PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 534.
PHY-1001 : End phase 1; 1.290411s wall, 1.296875s user + 0.015625s system = 1.312500s CPU (101.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 186704, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End initial routed; 1.238789s wall, 2.359375s user + 0.062500s system = 2.421875s CPU (195.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1797(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.561   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.403274s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 1.642154s wall, 2.765625s user + 0.062500s system = 2.828125s CPU (172.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 186704, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016099s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 186680, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023391s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (66.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 186696, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.018909s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (165.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1797(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.561   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.391963s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (99.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.185429s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End phase 3; 0.758139s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (98.9%)

PHY-1003 : Routed, final wirelength = 186696
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.009494s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.043807s wall, 8.109375s user + 0.125000s system = 8.234375s CPU (116.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2032, tinst num: 844, tnode num: 8926, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.920490s wall, 9.000000s user + 0.156250s system = 9.156250s CPU (115.6%)

RUN-1004 : used memory is 505 MB, reserved memory is 473 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      825   out of  19600    4.21%
#reg                     1076   out of  19600    5.49%
#le                      1537
  #lut only               461   out of   1537   29.99%
  #reg only               712   out of   1537   46.32%
  #lut&reg                364   out of   1537   23.68%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       473
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       108
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_10.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1537   |599     |226     |1107    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1139   |303     |133     |926     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |20      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |536    |123     |58      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |52     |2       |0       |52      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |13      |0       |28      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |93     |22      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |318    |98      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |99     |83      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |41     |37      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |216    |171     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1447  
    #2          2       311   
    #3          3       100   
    #4          4        22   
    #5        5-10       80   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6573, tnet num: 2032, tinst num: 844, tnode num: 8926, tedge num: 11552.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 844
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2034, pip num: 14781
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1346 valid insts, and 39184 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.380623s wall, 19.203125s user + 0.015625s system = 19.218750s CPU (568.5%)

RUN-1004 : used memory is 523 MB, reserved memory is 491 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230822_113215.log"
