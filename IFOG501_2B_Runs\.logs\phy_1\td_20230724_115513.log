============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 11:55:13 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/ADDA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 3 view nodes, 58 trigger nets, 58 data nets.
KIT-1004 : Chipwatcher code = 0100011100111111
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=3,BUS_DIN_NUM=58,BUS_CTRL_NUM=128,BUS_WIDTH='{32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb0100000,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb01000100,32'sb01100100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=150) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=150) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=3,BUS_DIN_NUM=58,BUS_CTRL_NUM=128,BUS_WIDTH='{32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb0100000,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb01000100,32'sb01100100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=3,BUS_DIN_NUM=58,BUS_CTRL_NUM=128,BUS_WIDTH='{32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb0100000,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb01000100,32'sb01100100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=3,BUS_DIN_NUM=58,BUS_CTRL_NUM=128,BUS_WIDTH='{32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb0100000,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb01000100,32'sb01100100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=150)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=150)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=3,BUS_DIN_NUM=58,BUS_CTRL_NUM=128,BUS_WIDTH='{32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb0100000,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb01000100,32'sb01100100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=3,BUS_DIN_NUM=58,BUS_CTRL_NUM=128,BUS_WIDTH='{32'sb0100000,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb0100000,32'sb0101110},BUS_CTRL_POS='{32'sb0,32'sb01000100,32'sb01100100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3920/17 useful/useless nets, 2344/6 useful/useless insts
SYN-1016 : Merged 20 instances.
SYN-1032 : 3549/16 useful/useless nets, 2843/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 460 better
SYN-1014 : Optimize round 2
SYN-1032 : 3179/45 useful/useless nets, 2473/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 3251/441 useful/useless nets, 2596/63 useful/useless insts
SYN-1016 : Merged 69 instances.
SYN-2571 : Optimize after map_dsp, round 1, 573 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 58 instances.
SYN-2501 : Optimize round 1, 118 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 3753/5 useful/useless nets, 3098/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 14533, tnet num: 3753, tinst num: 3097, tnode num: 19446, tedge num: 23329.
TMR-2508 : Levelizing timing graph completed, there are 85 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3753 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 283 (3.26), #lev = 7 (1.54)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 277 (3.31), #lev = 6 (1.62)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 574 instances into 277 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 467 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 164 adder to BLE ...
SYN-4008 : Packed 164 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.899720s wall, 1.765625s user + 0.093750s system = 1.859375s CPU (97.9%)

RUN-1004 : used memory is 177 MB, reserved memory is 139 MB, peak memory is 214 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (321 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2565 instances
RUN-0007 : 657 luts, 1459 seqs, 233 mslices, 114 lslices, 34 pads, 56 brams, 5 dsps
RUN-1001 : There are total 3221 nets
RUN-1001 : 2135 nets have 2 pins
RUN-1001 : 872 nets have [3 - 5] pins
RUN-1001 : 117 nets have [6 - 10] pins
RUN-1001 : 53 nets have [11 - 20] pins
RUN-1001 : 33 nets have [21 - 99] pins
RUN-1001 : 11 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     271     
RUN-1001 :   No   |  No   |  Yes  |     342     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     446     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    5    |  17   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 26
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2563 instances, 657 luts, 1459 seqs, 347 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-0007 : Cell area utilization is 7%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13388, tnet num: 3219, tinst num: 2563, tnode num: 18634, tedge num: 22674.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.403353s wall, 0.375000s user + 0.015625s system = 0.390625s CPU (96.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 831085
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2563.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 707670, overlap = 130.5
PHY-3002 : Step(2): len = 532410, overlap = 132.75
PHY-3002 : Step(3): len = 496030, overlap = 132.75
PHY-3002 : Step(4): len = 474086, overlap = 132.75
PHY-3002 : Step(5): len = 455767, overlap = 132.75
PHY-3002 : Step(6): len = 436937, overlap = 132.75
PHY-3002 : Step(7): len = 408255, overlap = 132.75
PHY-3002 : Step(8): len = 398574, overlap = 135
PHY-3002 : Step(9): len = 389876, overlap = 135
PHY-3002 : Step(10): len = 363102, overlap = 135
PHY-3002 : Step(11): len = 334114, overlap = 132.75
PHY-3002 : Step(12): len = 328649, overlap = 130.5
PHY-3002 : Step(13): len = 318364, overlap = 130.5
PHY-3002 : Step(14): len = 306076, overlap = 130.5
PHY-3002 : Step(15): len = 301026, overlap = 130.5
PHY-3002 : Step(16): len = 292614, overlap = 130.5
PHY-3002 : Step(17): len = 281850, overlap = 128.25
PHY-3002 : Step(18): len = 274330, overlap = 128.25
PHY-3002 : Step(19): len = 270099, overlap = 132.75
PHY-3002 : Step(20): len = 263008, overlap = 132.75
PHY-3002 : Step(21): len = 250300, overlap = 132.75
PHY-3002 : Step(22): len = 242938, overlap = 132.75
PHY-3002 : Step(23): len = 239434, overlap = 132.75
PHY-3002 : Step(24): len = 227950, overlap = 132.75
PHY-3002 : Step(25): len = 218459, overlap = 132.75
PHY-3002 : Step(26): len = 213826, overlap = 132.75
PHY-3002 : Step(27): len = 209382, overlap = 132.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.83921e-06
PHY-3002 : Step(28): len = 211421, overlap = 132.75
PHY-3002 : Step(29): len = 207403, overlap = 126
PHY-3002 : Step(30): len = 205374, overlap = 128.25
PHY-3002 : Step(31): len = 200201, overlap = 112.5
PHY-3002 : Step(32): len = 197186, overlap = 114.75
PHY-3002 : Step(33): len = 194578, overlap = 117
PHY-3002 : Step(34): len = 192208, overlap = 119.25
PHY-3002 : Step(35): len = 187462, overlap = 114.75
PHY-3002 : Step(36): len = 182905, overlap = 114.75
PHY-3002 : Step(37): len = 176932, overlap = 117
PHY-3002 : Step(38): len = 171775, overlap = 117
PHY-3002 : Step(39): len = 168062, overlap = 114.75
PHY-3002 : Step(40): len = 163847, overlap = 117
PHY-3002 : Step(41): len = 159274, overlap = 117
PHY-3002 : Step(42): len = 155640, overlap = 117
PHY-3002 : Step(43): len = 151654, overlap = 117
PHY-3002 : Step(44): len = 143944, overlap = 117
PHY-3002 : Step(45): len = 141147, overlap = 117
PHY-3002 : Step(46): len = 138254, overlap = 119.5
PHY-3002 : Step(47): len = 135663, overlap = 120.062
PHY-3002 : Step(48): len = 131658, overlap = 116.062
PHY-3002 : Step(49): len = 124060, overlap = 117.562
PHY-3002 : Step(50): len = 121046, overlap = 117.562
PHY-3002 : Step(51): len = 119216, overlap = 117.5
PHY-3002 : Step(52): len = 117520, overlap = 117.25
PHY-3002 : Step(53): len = 116801, overlap = 117.312
PHY-3002 : Step(54): len = 116125, overlap = 117.312
PHY-3002 : Step(55): len = 115488, overlap = 117.312
PHY-3002 : Step(56): len = 112366, overlap = 117.5
PHY-3002 : Step(57): len = 108901, overlap = 119.938
PHY-3002 : Step(58): len = 107432, overlap = 119.938
PHY-3002 : Step(59): len = 106653, overlap = 119.938
PHY-3002 : Step(60): len = 105638, overlap = 119.938
PHY-3002 : Step(61): len = 105256, overlap = 120
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.16784e-05
PHY-3002 : Step(62): len = 105416, overlap = 120.062
PHY-3002 : Step(63): len = 105456, overlap = 120.125
PHY-3002 : Step(64): len = 105289, overlap = 120.125
PHY-3002 : Step(65): len = 104974, overlap = 117.875
PHY-3002 : Step(66): len = 104474, overlap = 118.062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 2.33568e-05
PHY-3002 : Step(67): len = 104726, overlap = 115.812
PHY-3002 : Step(68): len = 104967, overlap = 115.625
PHY-3002 : Step(69): len = 104980, overlap = 117.938
PHY-3002 : Step(70): len = 104782, overlap = 118.062
PHY-3002 : Step(71): len = 104030, overlap = 120.375
PHY-3002 : Step(72): len = 102468, overlap = 120.312
PHY-3002 : Step(73): len = 101874, overlap = 120.312
PHY-3002 : Step(74): len = 101522, overlap = 120.375
PHY-3002 : Step(75): len = 100948, overlap = 120.375
PHY-3002 : Step(76): len = 100093, overlap = 120.562
PHY-3002 : Step(77): len = 99350.4, overlap = 121.125
PHY-3002 : Step(78): len = 98927.2, overlap = 121.188
PHY-3002 : Step(79): len = 98188.4, overlap = 121.5
PHY-3002 : Step(80): len = 97431.1, overlap = 121.812
PHY-3002 : Step(81): len = 96217.3, overlap = 122.25
PHY-3002 : Step(82): len = 95069.2, overlap = 122.312
PHY-3002 : Step(83): len = 93710.7, overlap = 121.875
PHY-3002 : Step(84): len = 92470.4, overlap = 121.5
PHY-3002 : Step(85): len = 90615, overlap = 121.125
PHY-3002 : Step(86): len = 89187.7, overlap = 120.938
PHY-3002 : Step(87): len = 88304.3, overlap = 120.875
PHY-3002 : Step(88): len = 86044.8, overlap = 118.625
PHY-3002 : Step(89): len = 84667.7, overlap = 116.125
PHY-3002 : Step(90): len = 84141.5, overlap = 118.375
PHY-3002 : Step(91): len = 83532.8, overlap = 118.375
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 4.67136e-05
PHY-3002 : Step(92): len = 83826, overlap = 116.125
PHY-3002 : Step(93): len = 84231.5, overlap = 113.875
PHY-3002 : Step(94): len = 84703.8, overlap = 113.875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 9.34273e-05
PHY-3002 : Step(95): len = 85183.1, overlap = 113.875
PHY-3002 : Step(96): len = 85296.7, overlap = 113.875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.011196s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.105195s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (104.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.24034e-05
PHY-3002 : Step(97): len = 102891, overlap = 21.7188
PHY-3002 : Step(98): len = 101694, overlap = 19.4688
PHY-3002 : Step(99): len = 100970, overlap = 18.9688
PHY-3002 : Step(100): len = 100217, overlap = 19.0625
PHY-3002 : Step(101): len = 99621.5, overlap = 20.9375
PHY-3002 : Step(102): len = 98351.2, overlap = 25
PHY-3002 : Step(103): len = 97224.1, overlap = 25
PHY-3002 : Step(104): len = 96049.6, overlap = 26.25
PHY-3002 : Step(105): len = 94666.4, overlap = 28.0938
PHY-3002 : Step(106): len = 92004.5, overlap = 29.6875
PHY-3002 : Step(107): len = 89753.6, overlap = 31.5
PHY-3002 : Step(108): len = 88576.6, overlap = 32.375
PHY-3002 : Step(109): len = 87749.7, overlap = 32.9375
PHY-3002 : Step(110): len = 87025.1, overlap = 32.875
PHY-3002 : Step(111): len = 86264.7, overlap = 32.4062
PHY-3002 : Step(112): len = 86023.4, overlap = 33.0625
PHY-3002 : Step(113): len = 85565.5, overlap = 33.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000144807
PHY-3002 : Step(114): len = 85342.4, overlap = 33.4062
PHY-3002 : Step(115): len = 84391, overlap = 34.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000289613
PHY-3002 : Step(116): len = 84405.6, overlap = 34.8438
PHY-3002 : Step(117): len = 84405.6, overlap = 34.8438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.108428s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (100.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.95839e-05
PHY-3002 : Step(118): len = 84715.5, overlap = 89.6562
PHY-3002 : Step(119): len = 84919.4, overlap = 88.875
PHY-3002 : Step(120): len = 85339.9, overlap = 87
PHY-3002 : Step(121): len = 85584.4, overlap = 87.3438
PHY-3002 : Step(122): len = 86090.4, overlap = 83.6562
PHY-3002 : Step(123): len = 86403.4, overlap = 82.6875
PHY-3002 : Step(124): len = 86512.9, overlap = 81.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000119168
PHY-3002 : Step(125): len = 86589, overlap = 77.4375
PHY-3002 : Step(126): len = 86890.7, overlap = 76.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000238336
PHY-3002 : Step(127): len = 87965.8, overlap = 73.5
PHY-3002 : Step(128): len = 89031.9, overlap = 70.7812
PHY-3002 : Step(129): len = 90893.1, overlap = 64.4688
PHY-3002 : Step(130): len = 90469.4, overlap = 63.8125
PHY-3002 : Step(131): len = 90403.4, overlap = 63.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000476078
PHY-3002 : Step(132): len = 91014.1, overlap = 60.4688
PHY-3002 : Step(133): len = 93441.2, overlap = 49.5
PHY-3002 : Step(134): len = 94809.1, overlap = 47.25
PHY-3002 : Step(135): len = 93933.4, overlap = 47.75
PHY-3002 : Step(136): len = 93606.4, overlap = 47.5625
PHY-3002 : Step(137): len = 92908, overlap = 44.0938
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000952156
PHY-3002 : Step(138): len = 93194.9, overlap = 43.1562
PHY-3002 : Step(139): len = 93505.3, overlap = 43.3125
PHY-3002 : Step(140): len = 93983.7, overlap = 42.1875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00190431
PHY-3002 : Step(141): len = 94194.3, overlap = 41.5312
PHY-3002 : Step(142): len = 94312.5, overlap = 41.7812
PHY-3002 : Step(143): len = 94641.5, overlap = 40.7188
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00380862
PHY-3002 : Step(144): len = 94670.6, overlap = 42.75
PHY-3002 : Step(145): len = 94670.6, overlap = 42.75
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.00761724
PHY-3002 : Step(146): len = 94902.3, overlap = 41.1875
PHY-3002 : Step(147): len = 95009.3, overlap = 40.9062
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0152345
PHY-3002 : Step(148): len = 95081.3, overlap = 40.875
PHY-3002 : Step(149): len = 95067, overlap = 40.75
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.030469
PHY-3002 : Step(150): len = 95078, overlap = 40.875
PHY-3002 : Step(151): len = 95078, overlap = 40.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13388, tnet num: 3219, tinst num: 2563, tnode num: 18634, tedge num: 22674.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 134.28 peak overflow 2.41
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/3221.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 118592, over cnt = 490(1%), over = 1655, worst = 17
PHY-1001 : End global iterations;  0.208405s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (120.0%)

PHY-1001 : Congestion index: top1 = 44.91, top5 = 31.68, top10 = 24.87, top15 = 20.50.
PHY-1001 : End incremental global routing;  0.273221s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (114.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.120913s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (90.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 2523 has valid locations, 52 needs to be replaced
PHY-3001 : design contains 2614 instances, 657 luts, 1510 seqs, 347 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 95684.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 13592, tnet num: 3270, tinst num: 2614, tnode num: 18991, tedge num: 22980.
TMR-2508 : Levelizing timing graph completed, there are 45 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3270 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.442336s wall, 0.406250s user + 0.031250s system = 0.437500s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(152): len = 96313.9, overlap = 1.375
PHY-3002 : Step(153): len = 96784.4, overlap = 1.4375
PHY-3002 : Step(154): len = 97268.5, overlap = 1.5
PHY-3002 : Step(155): len = 97389.7, overlap = 1.4375
PHY-3002 : Step(156): len = 97434, overlap = 1.4375
PHY-3002 : Step(157): len = 97434, overlap = 1.4375
PHY-3002 : Step(158): len = 97340.9, overlap = 1.375
PHY-3002 : Step(159): len = 97310.4, overlap = 1.375
PHY-3002 : Step(160): len = 97310.4, overlap = 1.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3270 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.107280s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (87.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00186493
PHY-3002 : Step(161): len = 97226.7, overlap = 41
PHY-3002 : Step(162): len = 97226.7, overlap = 41
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00372986
PHY-3002 : Step(163): len = 97193.2, overlap = 41.0625
PHY-3002 : Step(164): len = 97193.2, overlap = 41.0625
PHY-3001 : Final: Len = 97193.2, Over = 41.0625
PHY-3001 : End incremental placement;  0.753790s wall, 0.750000s user + 0.203125s system = 0.953125s CPU (126.4%)

OPT-1001 : Total overflow 135.41 peak overflow 2.50
OPT-1001 : End high-fanout net optimization;  1.215132s wall, 1.312500s user + 0.203125s system = 1.515625s CPU (124.7%)

OPT-1001 : Current memory(MB): used = 260, reserve = 220, peak = 260.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2509/3272.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 121352, over cnt = 491(1%), over = 1655, worst = 17
PHY-1002 : len = 129208, over cnt = 327(0%), over = 769, worst = 17
PHY-1002 : len = 135504, over cnt = 105(0%), over = 175, worst = 6
PHY-1002 : len = 137160, over cnt = 23(0%), over = 29, worst = 3
PHY-1002 : len = 137664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.255653s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (128.3%)

PHY-1001 : Congestion index: top1 = 39.81, top5 = 30.47, top10 = 25.04, top15 = 21.60.
OPT-1001 : End congestion update;  0.313759s wall, 0.328125s user + 0.046875s system = 0.375000s CPU (119.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3270 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.098869s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.8%)

OPT-0007 : Start: WNS -12348 TNS -292152 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -12348 TNS -292152 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.416681s wall, 0.421875s user + 0.046875s system = 0.468750s CPU (112.5%)

OPT-1001 : Current memory(MB): used = 260, reserve = 220, peak = 260.
OPT-1001 : End physical optimization;  2.022050s wall, 2.125000s user + 0.250000s system = 2.375000s CPU (117.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 657 LUT to BLE ...
SYN-4008 : Packed 657 LUT and 246 SEQ to BLE.
SYN-4003 : Packing 1264 remaining SEQ's ...
SYN-4005 : Packed 347 SEQ with LUT/SLICE
SYN-4006 : 99 single LUT's are left
SYN-4006 : 917 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1574/2081 primitive instances ...
PHY-3001 : End packing;  0.097838s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (111.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1296 instances
RUN-1001 : 597 mslices, 597 lslices, 34 pads, 56 brams, 5 dsps
RUN-1001 : There are total 3041 nets
RUN-1001 : 1913 nets have 2 pins
RUN-1001 : 914 nets have [3 - 5] pins
RUN-1001 : 121 nets have [6 - 10] pins
RUN-1001 : 51 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
PHY-3001 : design contains 1294 instances, 1194 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Cell area utilization is 15%
PHY-3001 : After packing: Len = 98525.6, Over = 78.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11648, tnet num: 3039, tinst num: 1294, tnode num: 15560, tedge num: 20264.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.437596s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.46371e-05
PHY-3002 : Step(165): len = 96473.6, overlap = 77.5
PHY-3002 : Step(166): len = 95411.3, overlap = 80.75
PHY-3002 : Step(167): len = 94524.7, overlap = 80.5
PHY-3002 : Step(168): len = 94194.4, overlap = 80.75
PHY-3002 : Step(169): len = 93993.4, overlap = 78.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.92743e-05
PHY-3002 : Step(170): len = 94418.5, overlap = 77.25
PHY-3002 : Step(171): len = 94960.6, overlap = 75
PHY-3002 : Step(172): len = 95346.4, overlap = 72.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000178549
PHY-3002 : Step(173): len = 96672.7, overlap = 68
PHY-3002 : Step(174): len = 98200.6, overlap = 65
PHY-3002 : Step(175): len = 98444.7, overlap = 63.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.174943s wall, 0.125000s user + 0.171875s system = 0.296875s CPU (169.7%)

PHY-3001 : Trial Legalized: Len = 116280
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.090328s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (103.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000591077
PHY-3002 : Step(176): len = 112459, overlap = 6.5
PHY-3002 : Step(177): len = 109056, overlap = 13.5
PHY-3002 : Step(178): len = 106619, overlap = 23.25
PHY-3002 : Step(179): len = 105008, overlap = 26
PHY-3002 : Step(180): len = 103910, overlap = 30.25
PHY-3002 : Step(181): len = 103350, overlap = 31.25
PHY-3002 : Step(182): len = 103049, overlap = 32.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00118215
PHY-3002 : Step(183): len = 103316, overlap = 31.75
PHY-3002 : Step(184): len = 103399, overlap = 32
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00236431
PHY-3002 : Step(185): len = 103582, overlap = 32.5
PHY-3002 : Step(186): len = 103754, overlap = 33.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007411s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (210.8%)

PHY-3001 : Legalized: Len = 111358, Over = 0
PHY-3001 : Spreading special nets. 37 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.012268s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (127.4%)

PHY-3001 : 49 instances has been re-located, deltaX = 22, deltaY = 34, maxDist = 2.
PHY-3001 : Final: Len = 112506, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11648, tnet num: 3039, tinst num: 1294, tnode num: 15560, tedge num: 20264.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 137/3041.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 139016, over cnt = 342(0%), over = 551, worst = 8
PHY-1002 : len = 140992, over cnt = 192(0%), over = 262, worst = 5
PHY-1002 : len = 143024, over cnt = 58(0%), over = 79, worst = 4
PHY-1002 : len = 143944, over cnt = 15(0%), over = 16, worst = 2
PHY-1002 : len = 144256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.296602s wall, 0.375000s user + 0.031250s system = 0.406250s CPU (137.0%)

PHY-1001 : Congestion index: top1 = 35.15, top5 = 28.27, top10 = 23.93, top15 = 21.17.
PHY-1001 : End incremental global routing;  0.378179s wall, 0.453125s user + 0.031250s system = 0.484375s CPU (128.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 3039 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.111606s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (98.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1254 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 1299 instances, 1199 slices, 42 macros(347 instances: 233 mslices 114 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 113562
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11687, tnet num: 3048, tinst num: 1299, tnode num: 15614, tedge num: 20322.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.476692s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(187): len = 113246, overlap = 0
PHY-3002 : Step(188): len = 113143, overlap = 0
PHY-3002 : Step(189): len = 113142, overlap = 0
PHY-3002 : Step(190): len = 113109, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 15%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.090025s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (104.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.*********
PHY-3002 : Step(191): len = 113138, overlap = 0.5
PHY-3002 : Step(192): len = 113138, overlap = 0.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 113199, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.008731s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (179.0%)

PHY-3001 : 2 instances has been re-located, deltaX = 6, deltaY = 0, maxDist = 3.
PHY-3001 : Final: Len = 113365, Over = 0
PHY-3001 : End incremental placement;  0.688283s wall, 0.687500s user + 0.125000s system = 0.812500s CPU (118.0%)

OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : End high-fanout net optimization;  1.245783s wall, 1.343750s user + 0.156250s system = 1.500000s CPU (120.4%)

OPT-1001 : Current memory(MB): used = 268, reserve = 227, peak = 268.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2627/3050.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 144840, over cnt = 8(0%), over = 9, worst = 2
PHY-1002 : len = 144856, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 144896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.047831s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.0%)

PHY-1001 : Congestion index: top1 = 35.15, top5 = 28.35, top10 = 24.02, top15 = 21.23.
OPT-1001 : End congestion update;  0.109580s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (99.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.095901s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.8%)

OPT-0007 : Start: WNS -12348 TNS -292152 NUM_FEPS 28
OPT-1001 : End path based optimization;  0.205909s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (98.6%)

OPT-1001 : Current memory(MB): used = 268, reserve = 227, peak = 268.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.104526s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (89.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2649/3050.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 144896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.013074s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (119.5%)

PHY-1001 : Congestion index: top1 = 35.15, top5 = 28.35, top10 = 24.02, top15 = 21.23.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.100348s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (109.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -12348 TNS -292152 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 34.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -12348ps with logic level 2 
RUN-1001 :       #2 path slack -12348ps with logic level 2 
RUN-1001 :       #3 path slack -12348ps with logic level 2 
RUN-1001 :       #4 path slack -12348ps with logic level 2 
RUN-1001 :       #5 path slack -12348ps with logic level 2 
RUN-1001 :       #6 path slack -12348ps with logic level 2 
RUN-1001 :       #7 path slack -12348ps with logic level 2 
RUN-1001 :       #8 path slack -12348ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 3050 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 3050 nets
OPT-1001 : End physical optimization;  2.157539s wall, 2.250000s user + 0.156250s system = 2.406250s CPU (111.5%)

RUN-1003 : finish command "place" in  10.653017s wall, 17.125000s user + 4.312500s system = 21.437500s CPU (201.2%)

RUN-1004 : used memory is 254 MB, reserved memory is 214 MB, peak memory is 268 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1301 instances
RUN-1001 : 597 mslices, 602 lslices, 34 pads, 56 brams, 5 dsps
RUN-1001 : There are total 3050 nets
RUN-1001 : 1916 nets have 2 pins
RUN-1001 : 915 nets have [3 - 5] pins
RUN-1001 : 124 nets have [6 - 10] pins
RUN-1001 : 53 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11687, tnet num: 3048, tinst num: 1299, tnode num: 15614, tedge num: 20322.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 597 mslices, 602 lslices, 34 pads, 56 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 3048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 138400, over cnt = 357(1%), over = 571, worst = 8
PHY-1002 : len = 140392, over cnt = 212(0%), over = 286, worst = 5
PHY-1002 : len = 143128, over cnt = 47(0%), over = 62, worst = 3
PHY-1002 : len = 144096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.295899s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (121.5%)

PHY-1001 : Congestion index: top1 = 34.61, top5 = 28.34, top10 = 23.98, top15 = 21.17.
PHY-1001 : End global routing;  0.364220s wall, 0.421875s user + 0.015625s system = 0.437500s CPU (120.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 281, reserve = 241, peak = 303.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-1001 : Current memory(MB): used = 541, reserve = 505, peak = 541.
PHY-1001 : End build detailed router design. 3.635832s wall, 3.593750s user + 0.031250s system = 3.625000s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 49488, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.671349s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 573, reserve = 539, peak = 573.
PHY-1001 : End phase 1; 1.677368s wall, 1.671875s user + 0.000000s system = 1.671875s CPU (99.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 461896, over cnt = 65(0%), over = 65, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 575, reserve = 540, peak = 575.
PHY-1001 : End initial routed; 5.849753s wall, 7.890625s user + 0.250000s system = 8.140625s CPU (139.2%)

PHY-1001 : Update timing.....
PHY-1001 : 12/2709(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.375   |  -226.768  |  28   
RUN-1001 :   Hold   |   0.096   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.534374s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (102.3%)

PHY-1001 : Current memory(MB): used = 577, reserve = 541, peak = 577.
PHY-1001 : End phase 2; 6.384217s wall, 8.437500s user + 0.250000s system = 8.687500s CPU (136.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS -9.371ns STNS -226.764ns FEP 28.
PHY-1001 : End OPT Iter 1; 0.023881s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.4%)

PHY-1022 : len = 461560, over cnt = 65(0%), over = 65, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.049436s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 460824, over cnt = 10(0%), over = 10, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.102052s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (122.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 460888, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.035790s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.3%)

PHY-1001 : Update timing.....
PHY-1001 : 12/2709(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.371   |  -226.764  |  28   
RUN-1001 :   Hold   |   0.096   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.535261s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (99.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 12 feed throughs used by 11 nets
PHY-1001 : End commit to database; 0.390778s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (104.0%)

PHY-1001 : Current memory(MB): used = 595, reserve = 559, peak = 595.
PHY-1001 : End phase 3; 1.262867s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (101.5%)

PHY-1001 : ===== Detail Route Phase 4 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS -9.371ns STNS -226.764ns FEP 28.
PHY-1001 : End OPT Iter 1; 0.029639s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.4%)

PHY-1022 : len = 460888, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End optimize timing; 0.058152s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.6%)

PHY-0007 : Phase: 4; Congestion: {, , , }; Timing: {-9.371ns, -226.764ns, 28}
PHY-1001 : Update timing.....
PHY-1001 : 12/2709(0%) critical/total net(s).
RUN-1001 : ---------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)   |  FEP  
RUN-1001 : ---------------------------------------
RUN-1001 :   Setup  |  -9.371   |  -226.764  |  28   
RUN-1001 :   Hold   |   0.096   |   0.000    |   0   
RUN-1001 : ---------------------------------------
PHY-1001 : End update timing; 0.546022s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 12 feed throughs used by 11 nets
PHY-1001 : End commit to database; 0.401797s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 596, reserve = 561, peak = 596.
PHY-1001 : End phase 4; 1.008526s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.2%)

PHY-1003 : Routed, final wirelength = 460888
PHY-1001 : Current memory(MB): used = 596, reserve = 561, peak = 596.
PHY-1001 : End export database. 0.013700s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (114.1%)

PHY-1001 : End detail routing;  14.182727s wall, 16.203125s user + 0.281250s system = 16.484375s CPU (116.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11687, tnet num: 3048, tinst num: 1299, tnode num: 15614, tedge num: 20322.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  15.573321s wall, 17.656250s user + 0.296875s system = 17.953125s CPU (115.3%)

RUN-1004 : used memory is 596 MB, reserved memory is 561 MB, peak memory is 596 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1366   out of  19600    6.97%
#reg                     1597   out of  19600    8.15%
#le                      2283
  #lut only               686   out of   2283   30.05%
  #reg only               917   out of   2283   40.17%
  #lut&reg                680   out of   2283   29.79%
#dsp                        5   out of     29   17.24%
#bram                      56   out of     64   87.50%
  #bram9k                  56
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       653
#2        config_inst_syn_9               GCLK               config             config_inst.jtck            175
#3        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       109
#4        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_11.q0    37
#5        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#6        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#7        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |2283   |1019    |347     |1628    |56      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1132   |325     |133     |925     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |32     |26      |6       |23      |0       |0       |
|    demodu                          |Demodulation                                     |544    |116     |58      |435     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |165    |60      |20      |132     |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |56     |0       |0       |56      |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |10      |0       |25      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |18      |0       |28      |0       |0       |
|    integ                           |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                            |Modulation                                       |86     |30      |21      |82      |0       |1       |
|    rs422                           |Rs422Output                                      |310    |120     |29      |253     |0       |4       |
|    trans                           |SquareWaveGenerator                              |21     |16      |5       |19      |0       |0       |
|  u_uart                            |UART_Control                                     |82     |72      |7       |58      |0       |0       |
|    U0                              |speed_select_Tx                                  |35     |28      |7       |19      |0       |0       |
|    U1                              |uart_tx                                          |16     |13      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data                                        |31     |31      |0       |23      |0       |0       |
|  wendu                             |DS18B20                                          |209    |164     |45      |79      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |761    |414     |121     |496     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |761    |414     |121     |496     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |316    |131     |0       |316     |0       |0       |
|        reg_inst                    |register                                         |313    |128     |0       |313     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |445    |283     |121     |180     |0       |0       |
|        bus_inst                    |bus_top                                          |194    |130     |64      |58      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |101    |67      |34      |32      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |43     |27      |16      |12      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det                                          |50     |36      |14      |14      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |139    |84      |29      |81      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1880  
    #2          2       554   
    #3          3       278   
    #4          4        83   
    #5        5-10      127   
    #6        11-50      66   
    #7       51-100      16   
    #8       101-500     3    
  Average     2.53            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11687, tnet num: 3048, tinst num: 1299, tnode num: 15614, tedge num: 20322.
TMR-2508 : Levelizing timing graph completed, there are 43 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 3048 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 5a09ad087eac1a3ba381ab6adb92597a5e1e0bc7e6a763456532d54ad15f3554 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1299
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 3050, pip num: 29564
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 12
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 2023 valid insts, and 75418 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000110100011100111111
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.888571s wall, 34.937500s user + 0.265625s system = 35.203125s CPU (597.8%)

RUN-1004 : used memory is 603 MB, reserved memory is 566 MB, peak memory is 738 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_115513.log"
