`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	SignalProcessing
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	
// Revision 1.01 - File Created
// Additional Comments: 
//Time sequence diagram
//   ___
//  |   |
//__|   |_________________________________________//clk_DA
//0 1   4
//     ________________________
//    |                        |
//____|                        |__________________//AD_valid
//0……59                       229
//                               ___
//                              |   |
//______________________________|   |_____________//demodulate
//0……………………………………………………………………………230 233
//                                     ___
//                                    |   |
//____________________________________|   |_______//integrate
//0……………………………………………………………………………………… 234 237
//                                          ___
//                                         |   |
//_________________________________________|   |__//output_drive
//0……………………………………………………………………………………………………… 238 244
//                                            ___
//                                           |   |
//___________________________________________|   |//modulate
//0………………………………………………………………………………………………………  242 245
//////////////////////////////////////////////////////////////////////////////////
module SignalProcessing
#(
	parameter acum_cnt=45,	//Sampling times
	parameter 	iWID_TRANS=9,		 //Set the number of transfers
	parameter 	iWID_AD=12,			 //AD width
	parameter 	iWID_DA=14,			 //DA width
	parameter	iWID_RS422=32,		 //RS422 DATA width
	parameter	iWID_SIGN=5,		 //SIGN DATA width
	parameter	wCLOSED=1'b1,		 //CLOSED loop or open loop
	parameter	iTRANSIT_TIME=246,	 //set transit-time 									
	parameter	iAD_VALID_START=70,	 //sample valid start point
	parameter	iFEEDBACK_SCALE=16,	 //feedback data begining point
	//parameter	bPOLAR=1,			 //Polarity selection
	parameter	iOUTPUT_SCALE=12,	 //output data begining point
	parameter	iDELAYED=88,		 //delay process counter for transmit signal
	parameter	DA_CONSTANT=800,	//b OF multiplier
	parameter	iTRANSMIT_COFF=800000//transmit signal divider coff
)
(
	input					rst_n,
	input					clk, //88MHz reference clock
	input					RxTransmit, //100HZ同步信号
	output					TxTransmit,
	input					clk_AD, //AD clock
	output					clk_DA, //DA clock
	output					transmit, //transmit siganl for interrupt
	input  [iWID_AD-1:0]	AD_DATA, //12 bits A/D data
	output [iWID_DA-1:0]	DA_DATA, //14 bits DA data
	output [iWID_RS422-1:0]	RS422_DATA //4 bytes data
);

localparam iWID_PROC=iWID_RS422+24;

wire 					modulate;
wire 					demodulate;
wire 					integrate;
wire 					output_drive;
wire 					AD_valid;
wire					polarity;
wire [iWID_PROC-1:0] 	demodu_data;
wire [iWID_PROC-1:0] 	ang_vel_data;
////////////////////////////////////////////////////////////////////////////////////////////	
	
//signal generater,used to generate clock used in a time//
SignalGenerator 
#(
	.iWID_TRANS(iWID_TRANS),            //sample valid end point
	.iTRANSIT_TIME(iTRANSIT_TIME),		//set half divider coff 	
	.iAD_VALID_START(iAD_VALID_START)   //Set the number of transfers		
)
ctrl_signal
(
	.clk(clk),                  //input
	.rst_n(rst_n),	            //input
	.clk_DA(clk_DA),            //output
	.polarity(polarity),        //output
	.modulate(modulate),        //output
	.AD_valid(AD_valid),        //output
	.integrate(integrate),      //output
	.demodulate(demodulate),    //output
	.output_drive(output_drive) //output
);
	
//demodulation//解调
Demodulation 
#(
	.acum_cnt(acum_cnt),
	.iWID_IN(iWID_AD),
	.iWID_OUT(iWID_PROC)
)
demodu
(                            
	.clk(clk),               //input
	.rst_n(rst_n),           //input
	.din(AD_DATA),           //input
	.clk_in(clk_AD),         //input
	.dout(demodu_data),      //output
	.AD_valid(AD_valid),     //input
	.polarity(polarity),     //input
	.demodulate(demodulate)  //input
);
	
//velocity//积分角速度
Integration 
#(
	.iWID_IN(iWID_PROC),
	.iWID_OUT(iWID_PROC)
)
integ
(
	.clk(clk),              //input
	.rst_n(rst_n),          //input
	.din(demodu_data),      //input
	.dout(ang_vel_data),    //output
	.polarity(polarity),    //input
	.integrate(integrate)   //input
);
	
//modulation and stair feedback,implemented by a single DA output//调制
Modulation
#(
	.iWID_IN(iWID_PROC),
	.iWID_OUT(iWID_DA),
	.wCLOSED(wCLOSED),
	.iWID_TRANS(iWID_TRANS),  
	.DA_CONSTANT(DA_CONSTANT),         
	.iFEEDBACK_SCALE(iFEEDBACK_SCALE)
)
modu
(
	.clk(clk),            //input
	.rst_n(rst_n),        //input
	.dout(DA_DATA),		  //output
	.din(ang_vel_data),   //input
	.modulate(modulate),  //input
	.polarity(polarity)   //input
);	
			
//angle displacement output//	
Rs422Output 
#(
	.iWID_IN(iWID_PROC),
	.iWID_OUT(iWID_RS422),
	.iDELAYED(iDELAYED),
	//.bPOLAR(bPOLAR),	 //Polarity selection
	.iOUTPUT_SCALE(iOUTPUT_SCALE)
) 
rs422
(
	.rst_n(rst_n),               //input
	.clk(clk),                   //input
	.dout(RS422_DATA),           //output
	.din(ang_vel_data),       	 //input
	.transmit(transmit),         //output
	.polarity(polarity),         //input
	.RxTransmit(RxTransmit),     //input
	.output_drive(modulate)  	 //input
);

//方波生成
SquareWaveGenerator 
#(
	.iTRANSMIT_COFF(iTRANSMIT_COFF) //transmit signal divider coff,10ms default
) 
trans
(
	.clk(clk),           //input
	.rst_n(rst_n),       //input
	.clk_out(TxTransmit) //output
);
		
endmodule

