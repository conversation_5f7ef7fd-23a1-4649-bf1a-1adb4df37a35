============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 26 09:13:28 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1532 instances
RUN-0007 : 384 luts, 894 seqs, 130 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2072 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1551 nets have 2 pins
RUN-1001 : 405 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1530 instances, 384 luts, 894 seqs, 205 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7320, tnet num: 2070, tinst num: 1530, tnode num: 10251, tedge num: 12381.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.333385s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 528754
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1530.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 443319, overlap = 20.25
PHY-3002 : Step(2): len = 407565, overlap = 20.25
PHY-3002 : Step(3): len = 386310, overlap = 15.75
PHY-3002 : Step(4): len = 367064, overlap = 20.25
PHY-3002 : Step(5): len = 360994, overlap = 20.25
PHY-3002 : Step(6): len = 348744, overlap = 20.25
PHY-3002 : Step(7): len = 340418, overlap = 20.25
PHY-3002 : Step(8): len = 334937, overlap = 20.25
PHY-3002 : Step(9): len = 310580, overlap = 15.75
PHY-3002 : Step(10): len = 286824, overlap = 13.5
PHY-3002 : Step(11): len = 279122, overlap = 11.25
PHY-3002 : Step(12): len = 275446, overlap = 9
PHY-3002 : Step(13): len = 268072, overlap = 11.25
PHY-3002 : Step(14): len = 260141, overlap = 15.75
PHY-3002 : Step(15): len = 257798, overlap = 13.5
PHY-3002 : Step(16): len = 249648, overlap = 15.75
PHY-3002 : Step(17): len = 242844, overlap = 15.75
PHY-3002 : Step(18): len = 238666, overlap = 15.75
PHY-3002 : Step(19): len = 235108, overlap = 15.75
PHY-3002 : Step(20): len = 219410, overlap = 15.75
PHY-3002 : Step(21): len = 213936, overlap = 15.75
PHY-3002 : Step(22): len = 210956, overlap = 15.75
PHY-3002 : Step(23): len = 201269, overlap = 13.5
PHY-3002 : Step(24): len = 191098, overlap = 13.5
PHY-3002 : Step(25): len = 189297, overlap = 13.5
PHY-3002 : Step(26): len = 183349, overlap = 13.5
PHY-3002 : Step(27): len = 166156, overlap = 15.75
PHY-3002 : Step(28): len = 160338, overlap = 15.75
PHY-3002 : Step(29): len = 157804, overlap = 18
PHY-3002 : Step(30): len = 152676, overlap = 15.75
PHY-3002 : Step(31): len = 150516, overlap = 15.75
PHY-3002 : Step(32): len = 143615, overlap = 13.5
PHY-3002 : Step(33): len = 136298, overlap = 13.5
PHY-3002 : Step(34): len = 133110, overlap = 13.5
PHY-3002 : Step(35): len = 130179, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000107822
PHY-3002 : Step(36): len = 129546, overlap = 13.5
PHY-3002 : Step(37): len = 128970, overlap = 15.75
PHY-3002 : Step(38): len = 127927, overlap = 13.5
PHY-3002 : Step(39): len = 126806, overlap = 13.5
PHY-3002 : Step(40): len = 125436, overlap = 13.5
PHY-3002 : Step(41): len = 121265, overlap = 11.25
PHY-3002 : Step(42): len = 115334, overlap = 13.5
PHY-3002 : Step(43): len = 113714, overlap = 13.5
PHY-3002 : Step(44): len = 112649, overlap = 11.25
PHY-3002 : Step(45): len = 108893, overlap = 13.5
PHY-3002 : Step(46): len = 105755, overlap = 11.25
PHY-3002 : Step(47): len = 103973, overlap = 13.5
PHY-3002 : Step(48): len = 102241, overlap = 11.25
PHY-3002 : Step(49): len = 100104, overlap = 13.5
PHY-3002 : Step(50): len = 98938.1, overlap = 13.5
PHY-3002 : Step(51): len = 94010.8, overlap = 15.75
PHY-3002 : Step(52): len = 88169.6, overlap = 13.5
PHY-3002 : Step(53): len = 86868.3, overlap = 13.5
PHY-3002 : Step(54): len = 85101.4, overlap = 13.5
PHY-3002 : Step(55): len = 83853, overlap = 13.5
PHY-3002 : Step(56): len = 83304.9, overlap = 13.5
PHY-3002 : Step(57): len = 82249.8, overlap = 11.25
PHY-3002 : Step(58): len = 80878.1, overlap = 11.25
PHY-3002 : Step(59): len = 79253.3, overlap = 13.5
PHY-3002 : Step(60): len = 76999.2, overlap = 13.5
PHY-3002 : Step(61): len = 75085.1, overlap = 13.5
PHY-3002 : Step(62): len = 73857, overlap = 9
PHY-3002 : Step(63): len = 73022.7, overlap = 11.25
PHY-3002 : Step(64): len = 70064.3, overlap = 10.6875
PHY-3002 : Step(65): len = 66242.1, overlap = 16.1875
PHY-3002 : Step(66): len = 65613.8, overlap = 16.0625
PHY-3002 : Step(67): len = 64771.4, overlap = 13.9375
PHY-3002 : Step(68): len = 64091.2, overlap = 11.6875
PHY-3002 : Step(69): len = 64110, overlap = 11.6875
PHY-3002 : Step(70): len = 63781.7, overlap = 13.875
PHY-3002 : Step(71): len = 62581.7, overlap = 16.1875
PHY-3002 : Step(72): len = 61575.6, overlap = 16.3125
PHY-3002 : Step(73): len = 60623.6, overlap = 16.25
PHY-3002 : Step(74): len = 59746.9, overlap = 13.875
PHY-3002 : Step(75): len = 59487.1, overlap = 16.25
PHY-3002 : Step(76): len = 58758.8, overlap = 14
PHY-3002 : Step(77): len = 58509.6, overlap = 16.125
PHY-3002 : Step(78): len = 57965.2, overlap = 13.8125
PHY-3002 : Step(79): len = 57182.8, overlap = 13.875
PHY-3002 : Step(80): len = 56484.4, overlap = 11.375
PHY-3002 : Step(81): len = 55368.3, overlap = 13.4375
PHY-3002 : Step(82): len = 54593.4, overlap = 18
PHY-3002 : Step(83): len = 54644.7, overlap = 15.75
PHY-3002 : Step(84): len = 54719.1, overlap = 15.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000215644
PHY-3002 : Step(85): len = 54730.6, overlap = 13.5625
PHY-3002 : Step(86): len = 54786, overlap = 13.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000431287
PHY-3002 : Step(87): len = 54773.1, overlap = 13.625
PHY-3002 : Step(88): len = 54847, overlap = 11.3125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008031s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (194.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.077089s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00060076
PHY-3002 : Step(89): len = 57687.5, overlap = 15.4688
PHY-3002 : Step(90): len = 58017.1, overlap = 13.4688
PHY-3002 : Step(91): len = 56866, overlap = 14.9062
PHY-3002 : Step(92): len = 55829.2, overlap = 14.9688
PHY-3002 : Step(93): len = 54794.9, overlap = 15.5938
PHY-3002 : Step(94): len = 54385.6, overlap = 13.1562
PHY-3002 : Step(95): len = 53805.1, overlap = 13.0938
PHY-3002 : Step(96): len = 53458.8, overlap = 12.8438
PHY-3002 : Step(97): len = 53321, overlap = 14.9375
PHY-3002 : Step(98): len = 53384.2, overlap = 14.4688
PHY-3002 : Step(99): len = 52416.6, overlap = 15.5938
PHY-3002 : Step(100): len = 51904.8, overlap = 16.5938
PHY-3002 : Step(101): len = 50992.5, overlap = 19.5938
PHY-3002 : Step(102): len = 50618.4, overlap = 19.8438
PHY-3002 : Step(103): len = 50104.4, overlap = 16.6562
PHY-3002 : Step(104): len = 49787.2, overlap = 16.5938
PHY-3002 : Step(105): len = 49412.5, overlap = 15.9062
PHY-3002 : Step(106): len = 49346.4, overlap = 16.2188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00120152
PHY-3002 : Step(107): len = 49204.2, overlap = 15.8438
PHY-3002 : Step(108): len = 49204.2, overlap = 15.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00240304
PHY-3002 : Step(109): len = 49255.9, overlap = 15.9688
PHY-3002 : Step(110): len = 49271, overlap = 15.9062
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061115s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (76.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.6146e-05
PHY-3002 : Step(111): len = 49322.8, overlap = 49.7812
PHY-3002 : Step(112): len = 50581, overlap = 43.8438
PHY-3002 : Step(113): len = 50577.3, overlap = 44.375
PHY-3002 : Step(114): len = 50023, overlap = 46.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000132292
PHY-3002 : Step(115): len = 50230.3, overlap = 46.5938
PHY-3002 : Step(116): len = 50750.2, overlap = 45.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000264584
PHY-3002 : Step(117): len = 50887.2, overlap = 44.1875
PHY-3002 : Step(118): len = 52102.2, overlap = 41.5
PHY-3002 : Step(119): len = 52546.5, overlap = 39.8438
PHY-3002 : Step(120): len = 52581.9, overlap = 38.5938
PHY-3002 : Step(121): len = 52581.9, overlap = 38.5938
PHY-3002 : Step(122): len = 52361.7, overlap = 38.4062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000529168
PHY-3002 : Step(123): len = 53465.8, overlap = 36.0312
PHY-3002 : Step(124): len = 53676, overlap = 32.9062
PHY-3002 : Step(125): len = 54055.7, overlap = 29.8125
PHY-3002 : Step(126): len = 54175.8, overlap = 29
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7320, tnet num: 2070, tinst num: 1530, tnode num: 10251, tedge num: 12381.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 76.97 peak overflow 1.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2072.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56528, over cnt = 243(0%), over = 987, worst = 17
PHY-1001 : End global iterations;  0.085057s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.9%)

PHY-1001 : Congestion index: top1 = 43.21, top5 = 26.19, top10 = 16.23, top15 = 11.42.
PHY-1001 : End incremental global routing;  0.137602s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (90.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069791s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.241860s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (96.9%)

OPT-1001 : Current memory(MB): used = 212, reserve = 175, peak = 212.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1569/2072.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56528, over cnt = 243(0%), over = 987, worst = 17
PHY-1002 : len = 62336, over cnt = 160(0%), over = 402, worst = 17
PHY-1002 : len = 66520, over cnt = 31(0%), over = 36, worst = 3
PHY-1002 : len = 67080, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 67144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.110330s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (113.3%)

PHY-1001 : Congestion index: top1 = 36.92, top5 = 25.54, top10 = 17.98, top15 = 13.05.
OPT-1001 : End congestion update;  0.156336s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (109.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2070 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056738s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.216579s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (108.2%)

OPT-1001 : Current memory(MB): used = 215, reserve = 177, peak = 215.
OPT-1001 : End physical optimization;  0.747599s wall, 0.843750s user + 0.015625s system = 0.859375s CPU (115.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 384 LUT to BLE ...
SYN-4008 : Packed 384 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 723 remaining SEQ's ...
SYN-4005 : Packed 120 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 603 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 987/1275 primitive instances ...
PHY-3001 : End packing;  0.056050s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (111.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 778 instances
RUN-1001 : 364 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1909 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1393 nets have 2 pins
RUN-1001 : 400 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 776 instances, 729 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54224, Over = 51
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6185, tnet num: 1907, tinst num: 776, tnode num: 8307, tedge num: 10862.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1907 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.334706s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (102.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.50283e-05
PHY-3002 : Step(127): len = 53443.5, overlap = 52.5
PHY-3002 : Step(128): len = 52766.8, overlap = 52
PHY-3002 : Step(129): len = 52252.1, overlap = 51.25
PHY-3002 : Step(130): len = 52158.9, overlap = 49
PHY-3002 : Step(131): len = 51965.2, overlap = 48.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.00566e-05
PHY-3002 : Step(132): len = 52347.2, overlap = 49
PHY-3002 : Step(133): len = 53623.8, overlap = 45.25
PHY-3002 : Step(134): len = 54047.4, overlap = 43.75
PHY-3002 : Step(135): len = 53697.4, overlap = 45
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000140113
PHY-3002 : Step(136): len = 54284.2, overlap = 44.5
PHY-3002 : Step(137): len = 55146.3, overlap = 43.25
PHY-3002 : Step(138): len = 55761.6, overlap = 44.25
PHY-3002 : Step(139): len = 55775.5, overlap = 47
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.109084s wall, 0.046875s user + 0.171875s system = 0.218750s CPU (200.5%)

PHY-3001 : Trial Legalized: Len = 68426.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1907 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052198s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000974984
PHY-3002 : Step(140): len = 64935, overlap = 6.25
PHY-3002 : Step(141): len = 62763.4, overlap = 10.5
PHY-3002 : Step(142): len = 61028.9, overlap = 12.75
PHY-3002 : Step(143): len = 60249.5, overlap = 17.75
PHY-3002 : Step(144): len = 59513.9, overlap = 17.75
PHY-3002 : Step(145): len = 59069.2, overlap = 16.75
PHY-3002 : Step(146): len = 58764.1, overlap = 16.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00194997
PHY-3002 : Step(147): len = 58951.2, overlap = 16.75
PHY-3002 : Step(148): len = 59036, overlap = 17.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00389994
PHY-3002 : Step(149): len = 59128.3, overlap = 17.25
PHY-3002 : Step(150): len = 59128.3, overlap = 17.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005924s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63309.8, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006150s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 7 instances has been re-located, deltaX = 1, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 63461.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6185, tnet num: 1907, tinst num: 776, tnode num: 8307, tedge num: 10862.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 41/1909.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69808, over cnt = 169(0%), over = 250, worst = 7
PHY-1002 : len = 70488, over cnt = 111(0%), over = 152, worst = 4
PHY-1002 : len = 72112, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 72264, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.169626s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (138.2%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 22.98, top10 = 17.95, top15 = 13.92.
PHY-1001 : End incremental global routing;  0.221336s wall, 0.234375s user + 0.046875s system = 0.281250s CPU (127.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1907 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059638s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.315402s wall, 0.328125s user + 0.046875s system = 0.375000s CPU (118.9%)

OPT-1001 : Current memory(MB): used = 218, reserve = 181, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1705/1909.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006662s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (234.5%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 22.98, top10 = 17.95, top15 = 13.92.
OPT-1001 : End congestion update;  0.060404s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1907 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048752s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (96.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.111089s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (112.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 183, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1907 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047185s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1705/1909.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72360, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006535s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 22.98, top10 = 17.95, top15 = 13.92.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1907 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050656s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.793103
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.898239s wall, 0.906250s user + 0.062500s system = 0.968750s CPU (107.8%)

RUN-1003 : finish command "place" in  6.346887s wall, 9.953125s user + 3.906250s system = 13.859375s CPU (218.4%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 778 instances
RUN-1001 : 364 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1909 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1393 nets have 2 pins
RUN-1001 : 400 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6185, tnet num: 1907, tinst num: 776, tnode num: 8307, tedge num: 10862.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 364 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1907 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69112, over cnt = 173(0%), over = 260, worst = 7
PHY-1002 : len = 69808, over cnt = 114(0%), over = 158, worst = 4
PHY-1002 : len = 71336, over cnt = 28(0%), over = 34, worst = 2
PHY-1002 : len = 71920, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.177363s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (132.1%)

PHY-1001 : Congestion index: top1 = 31.96, top5 = 22.94, top10 = 17.87, top15 = 13.89.
PHY-1001 : End global routing;  0.227453s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (123.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 201, peak = 238.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 465, peak = 497.
PHY-1001 : End build detailed router design. 3.537780s wall, 3.437500s user + 0.078125s system = 3.515625s CPU (99.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33584, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.458378s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 530, reserve = 498, peak = 530.
PHY-1001 : End phase 1; 1.464756s wall, 1.468750s user + 0.000000s system = 1.468750s CPU (100.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 185088, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 499, peak = 532.
PHY-1001 : End initial routed; 1.489177s wall, 2.156250s user + 0.171875s system = 2.328125s CPU (156.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1690(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.305   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.442   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366845s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.2%)

PHY-1001 : Current memory(MB): used = 533, reserve = 499, peak = 533.
PHY-1001 : End phase 2; 1.856115s wall, 2.531250s user + 0.171875s system = 2.703125s CPU (145.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 185088, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015229s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 185088, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027674s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (112.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 185152, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.024911s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (62.7%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 185184, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.023382s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (133.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1690(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.305   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.442   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.374161s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.183341s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.3%)

PHY-1001 : Current memory(MB): used = 547, reserve = 514, peak = 547.
PHY-1001 : End phase 3; 0.778507s wall, 0.781250s user + 0.000000s system = 0.781250s CPU (100.4%)

PHY-1003 : Routed, final wirelength = 185184
PHY-1001 : Current memory(MB): used = 547, reserve = 514, peak = 547.
PHY-1001 : End export database. 0.010165s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.839467s wall, 8.406250s user + 0.265625s system = 8.671875s CPU (110.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6185, tnet num: 1907, tinst num: 776, tnode num: 8307, tedge num: 10862.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[0] slack -36ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[1] slack -41ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[3] slack -96ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6225, tnet num: 1927, tinst num: 796, tnode num: 8347, tedge num: 10902.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.275239s wall, 3.406250s user + 0.218750s system = 3.625000s CPU (110.7%)

RUN-1003 : finish command "route" in  11.697917s wall, 12.437500s user + 0.500000s system = 12.937500s CPU (110.6%)

RUN-1004 : used memory is 530 MB, reserved memory is 497 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      836   out of  19600    4.27%
#reg                      962   out of  19600    4.91%
#le                      1439
  #lut only               477   out of   1439   33.15%
  #reg only               603   out of   1439   41.90%
  #lut&reg                359   out of   1439   24.95%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         425
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1439   |631     |205     |993     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1033   |319     |113     |812     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |32      |7       |25      |0       |0       |
|    demodu                  |Demodulation                                     |463    |129     |44      |343     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |61     |39      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |12      |0       |16      |0       |0       |
|    integ                   |Integration                                      |134    |31      |14      |108     |0       |0       |
|    modu                    |Modulation                                       |68     |19      |14      |64      |0       |1       |
|    rs422                   |Rs422Output                                      |310    |94      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |116    |107     |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |25     |25      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |54     |54      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1377  
    #2          2       264   
    #3          3       123   
    #4          4        13   
    #5        5-10       81   
    #6        11-50      27   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6225, tnet num: 1927, tinst num: 796, tnode num: 8347, tedge num: 10902.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1927 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 796
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1929, pip num: 14369
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1314 valid insts, and 38067 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.882574s wall, 20.109375s user + 0.046875s system = 20.156250s CPU (519.1%)

RUN-1004 : used memory is 546 MB, reserved memory is 512 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231026_091328.log"
