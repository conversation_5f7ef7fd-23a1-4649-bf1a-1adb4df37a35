/************************************************************\
 **     Copyright (c) 2012-2023 Anlogic Inc.
 **  All Right Reserved.\
\************************************************************/
/************************************************************\
 ** Log	:	This file is generated by Anlogic IP Generator.
 ** File	:	D:/Project/IFOG501_2B_20M/al_ip/fifo_1x1.v
 ** Date	:	2023 07 03
 ** TD version	:	5.6.71036
\************************************************************/

`timescale 1ns / 1ps

module fifo_1x1 (
	rst,
	di, clkw, we,
	do, clkr, re,
	empty_flag,
	full_flag 
);

	input rst;
	input [0:0] di;
	input clkw, we;
	input clkr,re;

	output [1:0] do;
	output empty_flag;
	output full_flag;

EG_LOGIC_FIFO #(
 	.DATA_WIDTH_W(1),
	.DATA_WIDTH_R(2),
	.DATA_DEPTH_W(1024),
	.DATA_DEPTH_R(512),
	.ENDIAN("BIG"),
	.RESETMODE("ASYNC"),
	.E(0),
	.F(1024),
	.ASYNC_RESET_RELEASE("SYNC"))
fifo_inst(
	.rst(rst),
	.di(di),
	.clkw(clkw),
	.we(we),
	.csw(3'b111),
	.do(do),
	.clkr(clkr),
	.re(re),
	.csr(3'b111),
	.ore(1'b0),
	.empty_flag(empty_flag),
	.aempty_flag(),
	.full_flag(full_flag),
	.afull_flag()

);

endmodule