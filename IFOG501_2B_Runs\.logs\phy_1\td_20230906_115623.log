============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Sep  6 11:56:23 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1616 instances
RUN-0007 : 357 luts, 984 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2186 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1639 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1614 instances, 357 luts, 984 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7728, tnet num: 2184, tinst num: 1614, tnode num: 10968, tedge num: 13072.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2184 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.278875s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 627803
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1614.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 508256, overlap = 20.25
PHY-3002 : Step(2): len = 469738, overlap = 20.25
PHY-3002 : Step(3): len = 431783, overlap = 20.25
PHY-3002 : Step(4): len = 403656, overlap = 20.25
PHY-3002 : Step(5): len = 395086, overlap = 20.25
PHY-3002 : Step(6): len = 383582, overlap = 20.25
PHY-3002 : Step(7): len = 367105, overlap = 18
PHY-3002 : Step(8): len = 356075, overlap = 15.75
PHY-3002 : Step(9): len = 348528, overlap = 15.75
PHY-3002 : Step(10): len = 331841, overlap = 15.75
PHY-3002 : Step(11): len = 321322, overlap = 18
PHY-3002 : Step(12): len = 314218, overlap = 18
PHY-3002 : Step(13): len = 303406, overlap = 20.25
PHY-3002 : Step(14): len = 289928, overlap = 20.25
PHY-3002 : Step(15): len = 285334, overlap = 20.25
PHY-3002 : Step(16): len = 277524, overlap = 20.25
PHY-3002 : Step(17): len = 262865, overlap = 20.25
PHY-3002 : Step(18): len = 256730, overlap = 20.25
PHY-3002 : Step(19): len = 253588, overlap = 20.25
PHY-3002 : Step(20): len = 237649, overlap = 20.25
PHY-3002 : Step(21): len = 229226, overlap = 20.25
PHY-3002 : Step(22): len = 226206, overlap = 20.25
PHY-3002 : Step(23): len = 219817, overlap = 20.25
PHY-3002 : Step(24): len = 200491, overlap = 20.25
PHY-3002 : Step(25): len = 197798, overlap = 20.25
PHY-3002 : Step(26): len = 194419, overlap = 20.25
PHY-3002 : Step(27): len = 186269, overlap = 20.25
PHY-3002 : Step(28): len = 181210, overlap = 20.25
PHY-3002 : Step(29): len = 177354, overlap = 20.25
PHY-3002 : Step(30): len = 173128, overlap = 20.25
PHY-3002 : Step(31): len = 171082, overlap = 20.25
PHY-3002 : Step(32): len = 159609, overlap = 20.25
PHY-3002 : Step(33): len = 151911, overlap = 20.25
PHY-3002 : Step(34): len = 149276, overlap = 20.25
PHY-3002 : Step(35): len = 146061, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00012804
PHY-3002 : Step(36): len = 147524, overlap = 13.5
PHY-3002 : Step(37): len = 146817, overlap = 11.25
PHY-3002 : Step(38): len = 144766, overlap = 15.75
PHY-3002 : Step(39): len = 142222, overlap = 15.75
PHY-3002 : Step(40): len = 137210, overlap = 18
PHY-3002 : Step(41): len = 132702, overlap = 13.5
PHY-3002 : Step(42): len = 130034, overlap = 13.5
PHY-3002 : Step(43): len = 127409, overlap = 13.5
PHY-3002 : Step(44): len = 120764, overlap = 15.75
PHY-3002 : Step(45): len = 119537, overlap = 9
PHY-3002 : Step(46): len = 117361, overlap = 13.5
PHY-3002 : Step(47): len = 114179, overlap = 15.75
PHY-3002 : Step(48): len = 114285, overlap = 11.25
PHY-3002 : Step(49): len = 111400, overlap = 11.25
PHY-3002 : Step(50): len = 108999, overlap = 11.25
PHY-3002 : Step(51): len = 105985, overlap = 11.25
PHY-3002 : Step(52): len = 105473, overlap = 9
PHY-3002 : Step(53): len = 102731, overlap = 11.25
PHY-3002 : Step(54): len = 101380, overlap = 18
PHY-3002 : Step(55): len = 98533.8, overlap = 15.75
PHY-3002 : Step(56): len = 96579.5, overlap = 9
PHY-3002 : Step(57): len = 94493.4, overlap = 11.25
PHY-3002 : Step(58): len = 93888.4, overlap = 15.75
PHY-3002 : Step(59): len = 89902.7, overlap = 13.5
PHY-3002 : Step(60): len = 88553, overlap = 11.25
PHY-3002 : Step(61): len = 86931.3, overlap = 11.4375
PHY-3002 : Step(62): len = 85933.3, overlap = 11.4375
PHY-3002 : Step(63): len = 84175.7, overlap = 7.1875
PHY-3002 : Step(64): len = 83086.1, overlap = 6.9375
PHY-3002 : Step(65): len = 76019.2, overlap = 9.0625
PHY-3002 : Step(66): len = 74693.7, overlap = 9
PHY-3002 : Step(67): len = 73485.9, overlap = 11.25
PHY-3002 : Step(68): len = 72477.8, overlap = 13.5
PHY-3002 : Step(69): len = 72560.4, overlap = 11.25
PHY-3002 : Step(70): len = 72686, overlap = 9
PHY-3002 : Step(71): len = 72497.9, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00025608
PHY-3002 : Step(72): len = 72136.4, overlap = 6.75
PHY-3002 : Step(73): len = 72016.1, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00051216
PHY-3002 : Step(74): len = 72086.8, overlap = 6.75
PHY-3002 : Step(75): len = 72136.4, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006429s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (243.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2184 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065396s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(76): len = 74621.5, overlap = 4.375
PHY-3002 : Step(77): len = 72786.5, overlap = 5.1875
PHY-3002 : Step(78): len = 71482.6, overlap = 4.1875
PHY-3002 : Step(79): len = 69673.2, overlap = 5.125
PHY-3002 : Step(80): len = 68479.1, overlap = 5
PHY-3002 : Step(81): len = 66787.8, overlap = 5.6875
PHY-3002 : Step(82): len = 65078, overlap = 4.6875
PHY-3002 : Step(83): len = 63852.5, overlap = 3.9375
PHY-3002 : Step(84): len = 62734.5, overlap = 4
PHY-3002 : Step(85): len = 61593.9, overlap = 3.375
PHY-3002 : Step(86): len = 59777.4, overlap = 7.125
PHY-3002 : Step(87): len = 58109.3, overlap = 7.9375
PHY-3002 : Step(88): len = 57001.4, overlap = 5.1875
PHY-3002 : Step(89): len = 55897.5, overlap = 4.75
PHY-3002 : Step(90): len = 55316.3, overlap = 3.0625
PHY-3002 : Step(91): len = 54435.9, overlap = 2.8125
PHY-3002 : Step(92): len = 54326.9, overlap = 3.625
PHY-3002 : Step(93): len = 53922, overlap = 3.8125
PHY-3002 : Step(94): len = 53071.6, overlap = 3.3125
PHY-3002 : Step(95): len = 51766, overlap = 2.875
PHY-3002 : Step(96): len = 51263.4, overlap = 4.1875
PHY-3002 : Step(97): len = 50871.2, overlap = 4.9375
PHY-3002 : Step(98): len = 50210.7, overlap = 5.8125
PHY-3002 : Step(99): len = 50163, overlap = 6.1875
PHY-3002 : Step(100): len = 49756.6, overlap = 6.0625
PHY-3002 : Step(101): len = 49516, overlap = 6.6875
PHY-3002 : Step(102): len = 49476.4, overlap = 7.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000361413
PHY-3002 : Step(103): len = 49259.3, overlap = 7.5
PHY-3002 : Step(104): len = 48919.8, overlap = 7.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000722826
PHY-3002 : Step(105): len = 49097.7, overlap = 8.125
PHY-3002 : Step(106): len = 49097.7, overlap = 8.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2184 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.073762s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.82535e-05
PHY-3002 : Step(107): len = 49307.4, overlap = 57.9375
PHY-3002 : Step(108): len = 49888.4, overlap = 58.0625
PHY-3002 : Step(109): len = 50251.3, overlap = 56.1562
PHY-3002 : Step(110): len = 49965.6, overlap = 56.2188
PHY-3002 : Step(111): len = 49922.5, overlap = 56.4688
PHY-3002 : Step(112): len = 49861.3, overlap = 55.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000176507
PHY-3002 : Step(113): len = 50185.7, overlap = 54.0625
PHY-3002 : Step(114): len = 50345.8, overlap = 52.5938
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00032486
PHY-3002 : Step(115): len = 50681.9, overlap = 49.9375
PHY-3002 : Step(116): len = 51443.2, overlap = 47.4375
PHY-3002 : Step(117): len = 52865.7, overlap = 39.1875
PHY-3002 : Step(118): len = 53366, overlap = 39.5625
PHY-3002 : Step(119): len = 53159.5, overlap = 35.25
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7728, tnet num: 2184, tinst num: 1614, tnode num: 10968, tedge num: 13072.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 95.59 peak overflow 2.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2186.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55136, over cnt = 259(0%), over = 1143, worst = 17
PHY-1001 : End global iterations;  0.064613s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (120.9%)

PHY-1001 : Congestion index: top1 = 42.48, top5 = 26.57, top10 = 16.39, top15 = 11.46.
PHY-1001 : End incremental global routing;  0.121272s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (116.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2184 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.094413s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (99.3%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1575 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 1620 instances, 357 luts, 990 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 53305
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7752, tnet num: 2190, tinst num: 1620, tnode num: 11010, tedge num: 13108.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2190 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.369967s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (92.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(120): len = 53453.9, overlap = 4.03125
PHY-3002 : Step(121): len = 53560.5, overlap = 4.15625
PHY-3002 : Step(122): len = 53637.3, overlap = 4.28125
PHY-3002 : Step(123): len = 53647.7, overlap = 4.28125
PHY-3002 : Step(124): len = 53625.8, overlap = 4.28125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2190 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069276s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000315652
PHY-3002 : Step(125): len = 53629, overlap = 35.5625
PHY-3002 : Step(126): len = 53629, overlap = 35.5625
PHY-3001 : Final: Len = 53629, Over = 35.5625
PHY-3001 : End incremental placement;  0.539931s wall, 0.609375s user + 0.140625s system = 0.750000s CPU (138.9%)

OPT-1001 : Total overflow 95.59 peak overflow 2.03
OPT-1001 : End high-fanout net optimization;  0.806173s wall, 0.984375s user + 0.171875s system = 1.156250s CPU (143.4%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1639/2192.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55544, over cnt = 259(0%), over = 1138, worst = 17
PHY-1002 : len = 63968, over cnt = 149(0%), over = 268, worst = 13
PHY-1002 : len = 66720, over cnt = 30(0%), over = 51, worst = 10
PHY-1002 : len = 67104, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 67376, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.118389s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (145.2%)

PHY-1001 : Congestion index: top1 = 37.59, top5 = 26.16, top10 = 18.40, top15 = 13.30.
OPT-1001 : End congestion update;  0.167695s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (121.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2190 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072304s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.245233s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (114.7%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 221.
OPT-1001 : End physical optimization;  1.383500s wall, 1.593750s user + 0.187500s system = 1.781250s CPU (128.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 357 LUT to BLE ...
SYN-4008 : Packed 357 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 809 remaining SEQ's ...
SYN-4005 : Packed 93 SEQ with LUT/SLICE
SYN-4006 : 104 single LUT's are left
SYN-4006 : 716 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1073/1406 primitive instances ...
PHY-3001 : End packing;  0.064584s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 839 instances
RUN-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1486 nets have 2 pins
RUN-1001 : 418 nets have [3 - 5] pins
RUN-1001 : 83 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 837 instances, 790 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53457.8, Over = 68.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6522, tnet num: 2024, tinst num: 837, tnode num: 8873, tedge num: 11468.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.382995s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (102.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.72226e-05
PHY-3002 : Step(127): len = 53030, overlap = 68.25
PHY-3002 : Step(128): len = 52604.8, overlap = 68.5
PHY-3002 : Step(129): len = 52628.3, overlap = 68.25
PHY-3002 : Step(130): len = 52343.8, overlap = 69.5
PHY-3002 : Step(131): len = 52068.4, overlap = 72
PHY-3002 : Step(132): len = 52103.9, overlap = 71
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.44452e-05
PHY-3002 : Step(133): len = 52487.3, overlap = 71
PHY-3002 : Step(134): len = 53059.3, overlap = 67.75
PHY-3002 : Step(135): len = 53656.2, overlap = 64.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00010889
PHY-3002 : Step(136): len = 54174.7, overlap = 63
PHY-3002 : Step(137): len = 55145.3, overlap = 59
PHY-3002 : Step(138): len = 55880.8, overlap = 58.75
PHY-3002 : Step(139): len = 55992.9, overlap = 58
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090893s wall, 0.062500s user + 0.093750s system = 0.156250s CPU (171.9%)

PHY-3001 : Trial Legalized: Len = 69707.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059313s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000752033
PHY-3002 : Step(140): len = 67088.3, overlap = 5.75
PHY-3002 : Step(141): len = 64564.9, overlap = 14
PHY-3002 : Step(142): len = 62791.5, overlap = 17
PHY-3002 : Step(143): len = 61733.7, overlap = 20.25
PHY-3002 : Step(144): len = 61105.8, overlap = 22.75
PHY-3002 : Step(145): len = 60482.6, overlap = 25.75
PHY-3002 : Step(146): len = 60176.7, overlap = 28
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00150407
PHY-3002 : Step(147): len = 60531.7, overlap = 27.75
PHY-3002 : Step(148): len = 60624.6, overlap = 27.25
PHY-3002 : Step(149): len = 60624.6, overlap = 27.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00300813
PHY-3002 : Step(150): len = 60825.2, overlap = 26.5
PHY-3002 : Step(151): len = 60825.2, overlap = 26.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005015s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 65483.1, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005964s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (262.0%)

PHY-3001 : 12 instances has been re-located, deltaX = 3, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 65731.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6522, tnet num: 2024, tinst num: 837, tnode num: 8873, tedge num: 11468.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 39/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71856, over cnt = 150(0%), over = 230, worst = 7
PHY-1002 : len = 72808, over cnt = 98(0%), over = 122, worst = 3
PHY-1002 : len = 74048, over cnt = 12(0%), over = 14, worst = 2
PHY-1002 : len = 74128, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 74256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.169553s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (156.7%)

PHY-1001 : Congestion index: top1 = 32.67, top5 = 23.42, top10 = 18.35, top15 = 14.41.
PHY-1001 : End incremental global routing;  0.225568s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (138.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067105s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.325350s wall, 0.390625s user + 0.031250s system = 0.421875s CPU (129.7%)

OPT-1001 : Current memory(MB): used = 221, reserve = 184, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1809/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007823s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.67, top5 = 23.42, top10 = 18.35, top15 = 14.41.
OPT-1001 : End congestion update;  0.071936s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058734s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 799 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 837 instances, 790 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 65771.8, Over = 0
PHY-3001 : End spreading;  0.007204s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 65771.8, Over = 0
PHY-3001 : End incremental legalization;  0.048209s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (129.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.196311s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.5%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064480s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1801/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 74304, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74304, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 74320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.028918s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (54.0%)

PHY-1001 : Congestion index: top1 = 32.67, top5 = 23.43, top10 = 18.35, top15 = 14.42.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057686s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.275862
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.108821s wall, 1.156250s user + 0.046875s system = 1.203125s CPU (108.5%)

RUN-1003 : finish command "place" in  6.219923s wall, 8.828125s user + 3.343750s system = 12.171875s CPU (195.7%)

RUN-1004 : used memory is 202 MB, reserved memory is 165 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 839 instances
RUN-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1486 nets have 2 pins
RUN-1001 : 418 nets have [3 - 5] pins
RUN-1001 : 83 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6522, tnet num: 2024, tinst num: 837, tnode num: 8873, tedge num: 11468.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 395 mslices, 395 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71528, over cnt = 150(0%), over = 231, worst = 7
PHY-1002 : len = 72480, over cnt = 95(0%), over = 118, worst = 3
PHY-1002 : len = 73376, over cnt = 37(0%), over = 44, worst = 2
PHY-1002 : len = 73800, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 73896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.183908s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (135.9%)

PHY-1001 : Congestion index: top1 = 33.08, top5 = 23.46, top10 = 18.29, top15 = 14.35.
PHY-1001 : End global routing;  0.248460s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (125.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 203, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 468, peak = 499.
PHY-1001 : End build detailed router design. 3.742702s wall, 3.703125s user + 0.031250s system = 3.734375s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32688, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.596689s wall, 1.578125s user + 0.031250s system = 1.609375s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 532, reserve = 502, peak = 532.
PHY-1001 : End phase 1; 1.603102s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (101.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184536, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End initial routed; 1.248685s wall, 2.562500s user + 0.265625s system = 2.828125s CPU (226.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1789(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.276   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.450481s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (97.1%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End phase 2; 1.699256s wall, 3.000000s user + 0.265625s system = 3.265625s CPU (192.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184536, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015272s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184488, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025396s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (184.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184520, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.032777s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (238.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 184520, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.019634s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (397.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1789(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.276   |   0.000   |   0   
RUN-1001 :   Hold   |   0.153   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.479956s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (97.7%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.179164s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.7%)

PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End phase 3; 0.899008s wall, 0.953125s user + 0.062500s system = 1.015625s CPU (113.0%)

PHY-1003 : Routed, final wirelength = 184520
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.009778s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (159.8%)

PHY-1001 : End detail routing;  8.153201s wall, 9.453125s user + 0.390625s system = 9.843750s CPU (120.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6522, tnet num: 2024, tinst num: 837, tnode num: 8873, tedge num: 11468.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  9.222181s wall, 10.546875s user + 0.421875s system = 10.968750s CPU (118.9%)

RUN-1004 : used memory is 526 MB, reserved memory is 497 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      815   out of  19600    4.16%
#reg                     1080   out of  19600    5.51%
#le                      1531
  #lut only               451   out of   1531   29.46%
  #reg only               716   out of   1531   46.77%
  #lut&reg                364   out of   1531   23.78%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1531   |589     |226     |1111    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1134   |290     |134     |925     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |35     |22      |7       |28      |0       |0       |
|    demodu                  |Demodulation                                     |530    |118     |58      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |54     |0       |0       |54      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |24     |15      |0       |24      |0       |0       |
|    integ                   |Integration                                      |139    |14      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |88     |30      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |309    |78      |29      |249     |0       |4       |
|    trans                   |SquareWaveGenerator                              |33     |28      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |113    |100     |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |33     |26      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |20     |20      |0       |14      |0       |0       |
|    U2                      |Ctrl_Data                                        |60     |54      |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |203    |158     |45      |75      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1450  
    #2          2       301   
    #3          3        99   
    #4          4        18   
    #5        5-10       84   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6522, tnet num: 2024, tinst num: 837, tnode num: 8873, tedge num: 11468.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 837
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2026, pip num: 14730
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1289 valid insts, and 38948 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.248636s wall, 18.546875s user + 0.062500s system = 18.609375s CPU (572.8%)

RUN-1004 : used memory is 549 MB, reserved memory is 517 MB, peak memory is 672 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230906_115623.log"
