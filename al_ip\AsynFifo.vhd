--------------------------------------------------------------
 --     Copyright (c) 2012-2023 Anlogic Inc.
 --  All Right Reserved.
--------------------------------------------------------------
 -- Log	:	This file is generated by Anlogic IP Generator.
 -- File	:	D:/GitProject/GitProject/Anlogic/IFOG501_2B/al_ip/AsynFifo.vhd
 -- Date	:	2023 04 19
 -- TD version	:	5.6.69102
--------------------------------------------------------------

LIBRARY ieee;
USE work.ALL;
	USE ieee.std_logic_1164.all;
LIBRARY eagle_macro;
	USE eagle_macro.EAGLE_COMPONENTS.all;

ENTITY AsynFifo IS
PORT (
	di	: IN STD_LOGIC_VECTOR(55 DOWNTO 0);

	rst	: IN STD_LOGIC;
	clkw	: IN STD_LOGIC;
	we	: IN STD_LOGIC;
	clkr	: IN STD_LOGIC;
	re	: IN STD_LOGIC;
	do	: OUT STD_LOGIC_VECTOR(55 DOWNTO 0);
	empty_flag		: OUT STD_LOGIC;
	full_flag		: OUT STD_LOGIC
	);
END AsynFifo;

ARCHITECTURE struct OF AsynFifo IS

	BEGIN
	fifo_inst : EG_LOGIC_FIFO
		GENERIC MAP (
			DATA_WIDTH_W			=> 56,
			DATA_DEPTH_W			=> 512,
			DATA_WIDTH_R			=> 56,
			DATA_DEPTH_R			=> 512,
			ENDIAN				=> "BIG",
			RESETMODE			=> "ASYNC",
			REGMODE_R			=> "NOREG",
			E					=> 0,
			F					=> 512,
			ASYNC_RESET_RELEASE	=> "SYNC"
		)
		PORT MAP (
			rst	=> rst,
			di	=> di,
			clkw	=> clkw,
			we	=> we,
			csw	=> "111",
			clkr	=> clkr,
			ore	=> '0',
			re	=> re,
			csr	=> "111",
			do	=> do,
			empty_flag	=> empty_flag,
			aempty_flag	=> OPEN,
			full_flag	=> full_flag,
			afull_flag	=> OPEN
		);

END struct;
