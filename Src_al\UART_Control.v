//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/22/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	UART_Control 
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	The module which is used to control uart communication
// Revision 1.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module UART_Control
#(
	parameter RX_SYS_FREQ =88000000, //接收输入时钟
    parameter RX_UART_BAUD=115200,    //接收波特率
	 parameter TX_SYS_FREQ =88000000, //发送输入时钟
	 parameter TX_UART_BAUD=115200     //发送波特率
)
(
	input					clk,//80MHz reference clock
	input					rst_n,
	input					rd_done,//温度数据读完
	input					transmit,//rs422数据已经准备好
	input  [15:0]			temp_data,
	input  [23:0]			Macc_data,
	input  [31:0]	RS422_DATA, //TX data
	output [7:0]			rx_data, //RX data
	output					TXD, //TX data serial out
	input					RXD  //RXD data serial in
);

wire [7:0]	tx_data;
wire		Txbps_start;
wire		Txclk_bps;
wire		Tx_done;
wire		tx_wr;
//TX	
speed_select_Tx
#(
    .SYS_FREQ(TX_SYS_FREQ),   //发送输入时钟  
	 .UART_BAUD(TX_UART_BAUD)  //发送波特率
)
U0
(
	.clk    	(clk),  
	.rst_n      (rst_n),
	.bps_start  (Txbps_start),
	.clk_bps    (Txclk_bps)
);

uart_tx U1(
	.clk   		(clk),   
	.rst_n      (rst_n),
	.clk_bps    (Txclk_bps),
	.data_tx    (tx_data),
	.tx_wr      (tx_wr),
	.uart_tx    (TXD),
    .tx_done    (Tx_done),
    .bps_start  (Txbps_start)
);	

Ctrl_Data U2(
	.clk 		(clk			),
	.rst_n      (rst_n			),
	.transmit 	(transmit		),
	.tx_done 	(Tx_done		),
	.rd_done 	(rd_done		),
	.tx_wr 		(tx_wr			),
	.data_Packet(RS422_DATA		),
	.temp_data	(temp_data		),
	.Macc_data	(Macc_data		),
	.tx_data  	(tx_data		) 
);
	
//RX
//uart_rx U4(
//	.clk    	(clk),  	
//    .rst_n      (rst_n),
//    .uart_rx    (RXD),
//    .clk_bps    (Rxclk_bps),
//    .bps_start  (Rxbps_start),
//    .rx_data    (Rx_data),
//    .rx_int     (Rx_int) 
//);
//
//speed_select_Rx
//#(
//    .SYS_FREQ(RX_SYS_FREQ),   //接收输入时钟
//    .UART_BAUD(RX_UART_BAUD)  //接收波特率//)
//U5
//(
//	.clk    	(clk),  
//	.rst_n      (rst_n),
//	.bps_start  (Rxbps_start),
//	.clk_bps    (Rxclk_bps)
//);	
	
endmodule
