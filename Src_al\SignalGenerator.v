`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	SignalGenerator
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	
// Revision 1.01 - File Created
// Additional Comments: 
//Time sequence diagram
//   ___
//  |   |
//__|   |_________________________________________//clk_DA
//0 1   4
//     ________________________
//    |                        |
//____|                        |__________________//AD_valid
//0……59                       229
//                               ___
//                              |   |
//______________________________|   |_____________//demodulate
//0……………………………………………………………………………230 233
//                                     ___
//                                    |   |
//____________________________________|   |_______//integrate
//0……………………………………………………………………………………… 234 237
//                                          ___
//                                         |   |
//_________________________________________|   |__//output_drive
//0……………………………………………………………………………………………………… 238 244
//                                            ___
//                                           |   |
//___________________________________________|   |//modulate
//0………………………………………………………………………………………………………  242 245
//////////////////////////////////////////////////////////////////////////////////
module SignalGenerator
#(
	parameter iWID_TRANS		=13,	//Set the number of transfers
	parameter iTRANSIT_TIME		=209,  	//set transit-time  181		
	parameter iAD_VALID_START	=100    //sample valid start point 80
)
(	
	input						rst_n,
	input						clk, 	 //120Mhz
	output reg					clk_DA,  //A/D sample clock
	output reg					AD_valid,/*synthesis keep*/
	output reg					demodulate,/*synthesis keep*/
	output reg					integrate,/*synthesis keep*/
	output reg					modulate,/*synthesis keep*/
	output reg					output_drive,/*synthesis keep*/
	output wire 				polarity/*synthesis keep*/
);		

localparam iDA			=3;
localparam iAD_VALID_END=iTRANSIT_TIME-16;	
localparam iDEMODU		=iTRANSIT_TIME-13;	
localparam iINTEGRATE	=iTRANSIT_TIME-9;			
localparam iMODU		=iTRANSIT_TIME-6;			
localparam iDA_CLOCK	=iTRANSIT_TIME-6;

reg	[15:0]				count;
reg	[iWID_TRANS-1:0]	count_trans;
//counter of trans time//
always @(posedge clk or negedge rst_n)begin
	if(~rst_n)begin
		count<=0;
		count_trans<=0;		
	end
	else if(count==iTRANSIT_TIME-1)begin
		count<=0;
		count_trans<=count_trans+1;	
	end
	else begin
		count<=count+1;
		count_trans<=count_trans;	
	end
end

assign polarity = count_trans[0];

//generator each clock//
//clk_DA
always @(posedge clk)begin
	if(count==iDA-1)
		clk_DA<=1'b1;
	else if(count==iDA+2) 
		clk_DA<=1'b0;
end

//AD_valid
always @(posedge clk)begin
	if(count==iAD_VALID_START-1)
		AD_valid<=1'b1;
	else if(count==iAD_VALID_END-1)
		AD_valid<=1'b0;
end

//demodulate
always @(posedge clk )begin
	if(count==iDEMODU-1)
		demodulate<=1'b1;
	else if(count==iDEMODU+2)
		demodulate<=1'b0;
end

//integrate
always @(posedge clk )begin
	if(count==iINTEGRATE-1)
		integrate<=1'b1;
	else if(count==iINTEGRATE+2)
		integrate<=1'b0;
end

//modulate

always @(posedge clk )begin
	if(count==iMODU-1)
		modulate<=1'b1;
	else if(count==iMODU+2)
		modulate<=1'b0;
end
	
endmodule
