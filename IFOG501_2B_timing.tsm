eagle_s20
13 3004 33 1158 3010 48204 0 0
0.218 0.114 IFOG501_2B eagle_s20 EG4S20NG88 Detail NA 19 8
clock: clk_in
15 0 0 0

clock: CLK120/pll_inst.clkc[0]
23 34790 2194 5
Setup check
33 3
Endpoint: u_uart/U2/tx_data_dy_b[3]_syn_29
33 0.218000 2 2
Timing path: wendu/reg4_syn_104.clk->u_uart/U2/tx_data_dy_b[3]_syn_29
wendu/reg4_syn_104.clk
u_uart/U2/tx_data_dy_b[3]_syn_29
35 0.218000 6.982000 6.764000 3 3
wendu/data[7] u_uart/U2/tx_data_dy_b[7]_syn_37.b[1]
u_uart/U2/tx_data_dy_b[7]_syn_16 wendu/reg4_syn_101.a[0]
u_uart/U2/tx_data_dy_b[7]_syn_18 u_uart/U2/tx_data_dy_b[3]_syn_29.b[0]

Timing path: wendu/reg4_syn_104.clk->u_uart/U2/tx_data_dy_b[3]_syn_29
wendu/reg4_syn_104.clk
u_uart/U2/tx_data_dy_b[3]_syn_29
75 0.577000 6.982000 6.405000 3 3
wendu/data[15] u_uart/U2/tx_data_dy_b[7]_syn_37.c[1]
u_uart/U2/tx_data_dy_b[7]_syn_16 wendu/reg4_syn_101.a[0]
u_uart/U2/tx_data_dy_b[7]_syn_18 u_uart/U2/tx_data_dy_b[3]_syn_29.b[0]


Endpoint: u_uart/U2/reg0_syn_57
115 0.237000 2 2
Timing path: wendu/reg4_syn_114.clk->u_uart/U2/reg0_syn_57
wendu/reg4_syn_114.clk
u_uart/U2/reg0_syn_57
117 0.237000 6.982000 6.745000 3 3
wendu/data[6] u_uart/U2/tx_data_dy_b[3]_syn_27.d[0]
u_uart/U2/mux2_syn_376 u_uart/U2/tx_data_dy_b[6]_syn_31.a[1]
u_uart/U2/mux2_syn_367 u_uart/U2/reg0_syn_57.b[1]

Timing path: wendu/reg4_syn_107.clk->u_uart/U2/reg0_syn_57
wendu/reg4_syn_107.clk
u_uart/U2/reg0_syn_57
157 0.636000 6.982000 6.346000 3 3
wendu/data[14] u_uart/U2/tx_data_dy_b[3]_syn_27.e[0]
u_uart/U2/mux2_syn_376 u_uart/U2/tx_data_dy_b[6]_syn_31.a[1]
u_uart/U2/mux2_syn_367 u_uart/U2/reg0_syn_57.b[1]


Endpoint: u_uart/U2/reg0_syn_57
197 0.240000 2 2
Timing path: wendu/reg4_syn_120.clk->u_uart/U2/reg0_syn_57
wendu/reg4_syn_120.clk
u_uart/U2/reg0_syn_57
199 0.240000 6.982000 6.742000 3 3
wendu/data[1] wendu/reg4_syn_99.d[0]
u_uart/U2/mux2_syn_96 u_uart/U2/tx_data_dy_b[1]_syn_28.a[1]
u_uart/U2/mux2_syn_87 u_uart/U2/reg0_syn_57.b[0]

Timing path: wendu/reg4_syn_120.clk->u_uart/U2/reg0_syn_57
wendu/reg4_syn_120.clk
u_uart/U2/reg0_syn_57
239 0.496000 6.982000 6.486000 3 3
wendu/data[9] wendu/reg4_syn_99.e[0]
u_uart/U2/mux2_syn_96 u_uart/U2/tx_data_dy_b[1]_syn_28.a[1]
u_uart/U2/mux2_syn_87 u_uart/U2/reg0_syn_57.b[0]



Hold check
279 3
Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_30
281 0.153000 7 3
Timing path: signal_process/demodu/fifo/reg0_syn_77.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/fifo/reg0_syn_77.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
283 0.153000 2.183000 2.336000 1 1
signal_process/demodu/fifo/rd_addr[5] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[9]

Timing path: signal_process/demodu/fifo/reg0_syn_80.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/fifo/reg0_syn_80.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
317 0.260000 2.183000 2.443000 1 1
signal_process/demodu/fifo/rd_addr[4] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[8]

Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
351 0.260000 2.183000 2.443000 1 1
signal_process/demodu/fifo/rd_addr[2] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addrb[6]


Endpoint: signal_process/demodu/reg9_syn_268
385 0.258000 1 1
Timing path: signal_process/demodu/reg11_syn_241.clk->signal_process/demodu/reg9_syn_268
signal_process/demodu/reg11_syn_241.clk
signal_process/demodu/reg9_syn_268
387 0.258000 2.191000 2.449000 0 1
signal_process/demodu/sample_sum[29] signal_process/demodu/reg9_syn_268.mi[0]


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_11
421 0.260000 7 3
Timing path: signal_process/demodu/fifo/reg0_syn_74.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/fifo/reg0_syn_74.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
423 0.260000 2.183000 2.443000 1 1
signal_process/demodu/fifo/rd_addr[1] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.addrb[5]

Timing path: signal_process/demodu/fifo/reg0_syn_74.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/fifo/reg0_syn_74.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
457 0.260000 2.183000 2.443000 1 1
signal_process/demodu/fifo/rd_addr[0] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.addrb[4]

Timing path: signal_process/demodu/fifo/reg0_syn_77.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_11
signal_process/demodu/fifo/reg0_syn_77.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_11
491 0.269000 2.183000 2.452000 1 1
signal_process/demodu/fifo/rd_addr[5] signal_process/demodu/fifo/ram_inst/ramread0_syn_11.addrb[9]



Recovery check
525 3
Endpoint: signal_process/demodu/fifo/ram_inst/reg2_syn_228
527 6.588000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/reg2_syn_228
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/reg2_syn_228
529 6.588000 10.245000 3.657000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/reg2_syn_228.sr


Endpoint: signal_process/demodu/fifo/ram_inst/reg2_syn_178
563 6.622000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/reg2_syn_178
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/reg2_syn_178
565 6.622000 10.245000 3.623000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/reg2_syn_178.sr


Endpoint: signal_process/demodu/fifo/ram_inst/reg2_syn_205
599 6.662000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/reg2_syn_205
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/reg2_syn_205
601 6.662000 10.245000 3.583000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/reg2_syn_205.sr



Removal check
635 3
Endpoint: signal_process/demodu/fifo/ram_inst/reg2_syn_234
637 0.246000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/reg2_syn_234
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/reg2_syn_234
639 0.246000 2.236000 2.482000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/reg2_syn_234.sr


Endpoint: signal_process/demodu/fifo/ram_inst/reg2_syn_237
673 0.254000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/ram_inst/reg2_syn_237
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/ram_inst/reg2_syn_237
675 0.254000 2.236000 2.490000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/ram_inst/reg2_syn_237.sr


Endpoint: signal_process/demodu/fifo/reg0_syn_77
709 0.335000 1 1
Timing path: signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk->signal_process/demodu/fifo/reg0_syn_77
signal_process/demodu/fifo/asy_r_rst1_reg_syn_4.clk
signal_process/demodu/fifo/reg0_syn_77
711 0.335000 2.236000 2.571000 0 1
signal_process/demodu/fifo/asy_r_rst1 signal_process/demodu/fifo/reg0_syn_77.sr



Period check
745 1
Endpoint: signal_process/modu/mult0_syn_2.clk
749 4.919000 1 0



clock: CLK120/pll_inst.clkc[3]
750 5474 574 4
Setup check
760 3
Endpoint: signal_process/demodu/AD_validcnt_b1[0]_syn_14
760 6.890000 1 1
Timing path: signal_process/ctrl_signal/AD_valid_reg_syn_27.clk->signal_process/demodu/AD_validcnt_b1[0]_syn_14
signal_process/ctrl_signal/AD_valid_reg_syn_27.clk
signal_process/demodu/AD_validcnt_b1[0]_syn_14
762 6.890000 10.383000 3.493000 0 1
signal_process/ctrl_signal/AD_valid signal_process/demodu/AD_validcnt_b1[0]_syn_14.mi[0]


Endpoint: signal_process/demodu/reg11_syn_205
796 11.353000 65 3
Timing path: signal_process/demodu/reg6_syn_48.clk->signal_process/demodu/reg11_syn_205
signal_process/demodu/reg6_syn_48.clk
signal_process/demodu/reg11_syn_205
798 11.353000 18.896000 7.543000 2 14
signal_process/demodu/din_reg1[6] signal_process/demodu/add1_syn_324.e[1]
signal_process/demodu/add1_syn_274 signal_process/demodu/add1_syn_325.fci
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_205.mi[1]

Timing path: signal_process/demodu/reg6_syn_50.clk->signal_process/demodu/reg11_syn_205
signal_process/demodu/reg6_syn_50.clk
signal_process/demodu/reg11_syn_205
858 11.479000 18.896000 7.417000 2 14
signal_process/demodu/din_reg1[5] signal_process/demodu/add1_syn_324.d[1]
signal_process/demodu/add1_syn_274 signal_process/demodu/add1_syn_325.fci
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_205.mi[1]

Timing path: signal_process/demodu/reg6_syn_46.clk->signal_process/demodu/reg11_syn_205
signal_process/demodu/reg6_syn_46.clk
signal_process/demodu/reg11_syn_205
918 11.491000 18.896000 7.405000 2 13
signal_process/demodu/din_reg1[8] signal_process/demodu/add1_syn_325.e[0]
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[52] signal_process/demodu/reg11_syn_205.mi[1]


Endpoint: signal_process/demodu/reg11_syn_205
976 11.679000 67 3
Timing path: signal_process/demodu/reg6_syn_48.clk->signal_process/demodu/reg11_syn_205
signal_process/demodu/reg6_syn_48.clk
signal_process/demodu/reg11_syn_205
978 11.679000 18.896000 7.217000 2 14
signal_process/demodu/din_reg1[6] signal_process/demodu/add1_syn_324.e[1]
signal_process/demodu/add1_syn_274 signal_process/demodu/add1_syn_325.fci
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[53] signal_process/demodu/reg11_syn_205.mi[0]

Timing path: signal_process/demodu/reg6_syn_50.clk->signal_process/demodu/reg11_syn_205
signal_process/demodu/reg6_syn_50.clk
signal_process/demodu/reg11_syn_205
1038 11.805000 18.896000 7.091000 2 14
signal_process/demodu/din_reg1[5] signal_process/demodu/add1_syn_324.d[1]
signal_process/demodu/add1_syn_274 signal_process/demodu/add1_syn_325.fci
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[53] signal_process/demodu/reg11_syn_205.mi[0]

Timing path: signal_process/demodu/reg6_syn_46.clk->signal_process/demodu/reg11_syn_205
signal_process/demodu/reg6_syn_46.clk
signal_process/demodu/reg11_syn_205
1098 11.817000 18.896000 7.079000 2 13
signal_process/demodu/din_reg1[8] signal_process/demodu/add1_syn_325.e[0]
signal_process/demodu/add1_syn_278 signal_process/demodu/add1_syn_326.fci
signal_process/demodu/add1_syn_282 signal_process/demodu/add1_syn_327.fci
signal_process/demodu/add1_syn_286 signal_process/demodu/add1_syn_328.fci
signal_process/demodu/add1_syn_290 signal_process/demodu/add1_syn_329.fci
signal_process/demodu/add1_syn_294 signal_process/demodu/add1_syn_330.fci
signal_process/demodu/add1_syn_298 signal_process/demodu/add1_syn_331.fci
signal_process/demodu/add1_syn_302 signal_process/demodu/add1_syn_332.fci
signal_process/demodu/add1_syn_306 signal_process/demodu/add1_syn_333.fci
signal_process/demodu/add1_syn_310 signal_process/demodu/add1_syn_334.fci
signal_process/demodu/add1_syn_314 signal_process/demodu/add1_syn_335.fci
signal_process/demodu/add1_syn_318 signal_process/demodu/add1_syn_336.fci
signal_process/demodu/sample_sum_b2[53] signal_process/demodu/reg11_syn_205.mi[0]



Hold check
1156 3
Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_49
1158 0.114000 9 3
Timing path: signal_process/demodu/reg8_syn_205.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_49
signal_process/demodu/reg8_syn_205.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_49
1160 0.114000 2.274000 2.388000 1 1
signal_process/demodu/latch_sample_sum[53] signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8]

Timing path: signal_process/demodu/reg8_syn_211.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_49
signal_process/demodu/reg8_syn_211.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_49
1194 0.186000 2.274000 2.460000 1 1
signal_process/demodu/latch_sample_sum[50] signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5]

Timing path: signal_process/demodu/reg8_syn_205.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_49
signal_process/demodu/reg8_syn_205.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_49
1228 0.195000 2.274000 2.469000 1 1
signal_process/demodu/latch_sample_sum[51] signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6]


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_30
1262 0.195000 9 3
Timing path: signal_process/demodu/reg8_syn_235.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/reg8_syn_235.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
1264 0.195000 2.274000 2.469000 1 1
signal_process/demodu/latch_sample_sum[32] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[5]

Timing path: signal_process/demodu/reg8_syn_232.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/reg8_syn_232.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
1298 0.195000 2.274000 2.469000 1 1
signal_process/demodu/latch_sample_sum[31] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[4]

Timing path: signal_process/demodu/reg8_syn_232.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/reg8_syn_232.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
1332 0.196000 2.274000 2.470000 1 1
signal_process/demodu/latch_sample_sum[35] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[8]


Endpoint: signal_process/demodu/fifo/ram_inst/ramread0_syn_30
1366 0.195000 9 3
Timing path: signal_process/demodu/reg8_syn_247.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/reg8_syn_247.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
1368 0.195000 2.274000 2.469000 1 1
signal_process/demodu/latch_sample_sum[26] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[8]

Timing path: signal_process/demodu/reg8_syn_247.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/reg8_syn_247.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
1402 0.205000 2.274000 2.479000 1 1
signal_process/demodu/latch_sample_sum[24] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[6]

Timing path: signal_process/demodu/reg8_syn_170.clk->signal_process/demodu/fifo/ram_inst/ramread0_syn_30
signal_process/demodu/reg8_syn_170.clk
signal_process/demodu/fifo/ram_inst/ramread0_syn_30
1436 0.205000 2.274000 2.479000 1 1
signal_process/demodu/latch_sample_sum[20] signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[2]



Recovery check
1470 3
Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_46
1472 15.229000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_46
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_46
1474 15.229000 18.712000 3.483000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_46.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
1508 15.261000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
1510 15.261000 18.712000 3.451000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_29
1544 15.261000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_29
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_29
1546 15.261000 18.712000 3.451000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_29.sr



Removal check
1580 3
Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_25
1582 0.218000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_25
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_25
1584 0.218000 2.327000 2.545000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_25.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_31
1618 0.228000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_31
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_31
1620 0.228000 2.327000 2.555000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_31.sr


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_27
1654 0.244000 1 1
Timing path: signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_27
signal_process/demodu/fifo/asy_w_rst1_reg_syn_4.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_27
1656 0.244000 2.311000 2.555000 0 1
signal_process/demodu/fifo/asy_w_rst1 signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_27.sr




clock: CLK120/pll_inst.clkc[4]
1692 0 0 0

clock: clk_us
1700 7902 204 2
Setup check
1710 3
Endpoint: dq_syn_2
1710 992.440000 131 3
Timing path: wendu/reg2_syn_141.clk->dq_syn_2
wendu/reg2_syn_141.clk
dq_syn_2
1712 992.440000 1003.313000 10.873000 5 14
wendu/cnt_us[2] wendu/lt0_syn_90.a[0]
wendu/lt0_syn_10 wendu/lt0_syn_93.fci
wendu/lt0_syn_14 wendu/lt0_syn_96.fci
wendu/lt0_syn_18 wendu/lt0_syn_99.fci
wendu/lt0_syn_22 wendu/lt0_syn_102.fci
wendu/lt0_syn_26 wendu/lt0_syn_105.fci
wendu/lt0_syn_30 wendu/lt0_syn_108.fci
wendu/lt0_syn_34 wendu/lt0_syn_111.fci
wendu/lt0_syn_38 wendu/lt0_syn_114.fci
wendu/lt0_syn_42 wendu/lt0_syn_116.fci
wendu/data_temp_b2_n wendu/cur_state[0]_syn_3750.a[1]
wendu/cur_state[0]_syn_3583 wendu/cur_state[0]_syn_3809.a[1]
wendu/cur_state[0]_syn_3585 wendu/cur_state[0]_syn_3764.b[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts

Timing path: wendu/reg2_syn_129.clk->dq_syn_2
wendu/reg2_syn_129.clk
dq_syn_2
1776 992.482000 1003.313000 10.831000 5 14
wendu/cnt_us[3] wendu/lt0_syn_90.a[1]
wendu/lt0_syn_10 wendu/lt0_syn_93.fci
wendu/lt0_syn_14 wendu/lt0_syn_96.fci
wendu/lt0_syn_18 wendu/lt0_syn_99.fci
wendu/lt0_syn_22 wendu/lt0_syn_102.fci
wendu/lt0_syn_26 wendu/lt0_syn_105.fci
wendu/lt0_syn_30 wendu/lt0_syn_108.fci
wendu/lt0_syn_34 wendu/lt0_syn_111.fci
wendu/lt0_syn_38 wendu/lt0_syn_114.fci
wendu/lt0_syn_42 wendu/lt0_syn_116.fci
wendu/data_temp_b2_n wendu/cur_state[0]_syn_3750.a[1]
wendu/cur_state[0]_syn_3583 wendu/cur_state[0]_syn_3809.a[1]
wendu/cur_state[0]_syn_3585 wendu/cur_state[0]_syn_3764.b[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts

Timing path: wendu/reg2_syn_141.clk->dq_syn_2
wendu/reg2_syn_141.clk
dq_syn_2
1840 992.504000 1003.313000 10.809000 5 14
wendu/cnt_us[1] wendu/lt2_syn_93.a[0]
wendu/lt2_syn_9 wendu/lt2_syn_96.fci
wendu/lt2_syn_13 wendu/lt2_syn_99.fci
wendu/lt2_syn_17 wendu/lt2_syn_102.fci
wendu/lt2_syn_21 wendu/lt2_syn_105.fci
wendu/lt2_syn_25 wendu/lt2_syn_108.fci
wendu/lt2_syn_29 wendu/lt2_syn_111.fci
wendu/lt2_syn_33 wendu/lt2_syn_114.fci
wendu/lt2_syn_37 wendu/lt2_syn_117.fci
wendu/lt2_syn_41 wendu/lt2_syn_120.fci
wendu/dq_en_n9 wendu/cur_state[0]_syn_3750.b[1]
wendu/cur_state[0]_syn_3583 wendu/cur_state[0]_syn_3809.a[1]
wendu/cur_state[0]_syn_3585 wendu/cur_state[0]_syn_3764.b[0]
wendu/cur_state[0]_syn_4 dq_syn_2.ts


Endpoint: u_uart/U2/tx_data_dy_b[6]_syn_31
1904 993.371000 57 3
Timing path: wendu/reg2_syn_144.clk->u_uart/U2/tx_data_dy_b[6]_syn_31
wendu/reg2_syn_144.clk
u_uart/U2/tx_data_dy_b[6]_syn_31
1906 993.371000 1003.385000 10.014000 6 6
wendu/cnt_us[5] wendu/cur_state[0]_syn_3784.b[1]
wendu/cur_state[0]_syn_3512 wendu/cur_state[0]_syn_3790.a[1]
wendu/cur_state[0]_syn_3514 wendu/cur_state[0]_syn_3811.b[1]
wendu/cur_state[0]_syn_3516 wendu/mux0_syn_5.a[0]
wendu/cur_state[0]_syn_3518 wendu/cur_state[0]_syn_3764.d[1]
wendu/cur_state[5]_syn_930 u_uart/U2/tx_data_dy_b[6]_syn_31.a[0]

Timing path: wendu/reg2_syn_129.clk->u_uart/U2/tx_data_dy_b[6]_syn_31
wendu/reg2_syn_129.clk
u_uart/U2/tx_data_dy_b[6]_syn_31
1954 993.416000 1003.385000 9.969000 6 6
wendu/cnt_us[3] wendu/cur_state[0]_syn_3804.d[1]
wendu/cur_state[0]_syn_3499 wendu/cur_state[0]_syn_3790.b[1]
wendu/cur_state[0]_syn_3514 wendu/cur_state[0]_syn_3811.b[1]
wendu/cur_state[0]_syn_3516 wendu/mux0_syn_5.a[0]
wendu/cur_state[0]_syn_3518 wendu/cur_state[0]_syn_3764.d[1]
wendu/cur_state[5]_syn_930 u_uart/U2/tx_data_dy_b[6]_syn_31.a[0]

Timing path: wendu/cur_state[0]_syn_3768.clk->u_uart/U2/tx_data_dy_b[6]_syn_31
wendu/cur_state[0]_syn_3768.clk
u_uart/U2/tx_data_dy_b[6]_syn_31
2002 993.606000 1003.385000 9.779000 6 6
wendu/cnt_us[10] wendu/cur_state[0]_syn_3802.a[0]
wendu/cur_state[0]_syn_3503 wendu/cur_state[0]_syn_3754.b[1]
wendu/cur_state[0]_syn_3505 wendu/cur_state[0]_syn_3811.d[1]
wendu/cur_state[0]_syn_3516 wendu/mux0_syn_5.a[0]
wendu/cur_state[0]_syn_3518 wendu/cur_state[0]_syn_3764.d[1]
wendu/cur_state[5]_syn_930 u_uart/U2/tx_data_dy_b[6]_syn_31.a[0]


Endpoint: wendu/reg4_syn_99
2050 993.371000 57 3
Timing path: wendu/reg2_syn_144.clk->wendu/reg4_syn_99
wendu/reg2_syn_144.clk
wendu/reg4_syn_99
2052 993.371000 1003.385000 10.014000 6 6
wendu/cnt_us[5] wendu/cur_state[0]_syn_3784.b[1]
wendu/cur_state[0]_syn_3512 wendu/cur_state[0]_syn_3790.a[1]
wendu/cur_state[0]_syn_3514 wendu/cur_state[0]_syn_3811.b[1]
wendu/cur_state[0]_syn_3516 wendu/mux0_syn_5.a[0]
wendu/cur_state[0]_syn_3518 wendu/cur_state[0]_syn_3764.d[1]
wendu/cur_state[5]_syn_930 wendu/reg4_syn_99.a[1]

Timing path: wendu/reg2_syn_129.clk->wendu/reg4_syn_99
wendu/reg2_syn_129.clk
wendu/reg4_syn_99
2100 993.416000 1003.385000 9.969000 6 6
wendu/cnt_us[3] wendu/cur_state[0]_syn_3804.d[1]
wendu/cur_state[0]_syn_3499 wendu/cur_state[0]_syn_3790.b[1]
wendu/cur_state[0]_syn_3514 wendu/cur_state[0]_syn_3811.b[1]
wendu/cur_state[0]_syn_3516 wendu/mux0_syn_5.a[0]
wendu/cur_state[0]_syn_3518 wendu/cur_state[0]_syn_3764.d[1]
wendu/cur_state[5]_syn_930 wendu/reg4_syn_99.a[1]

Timing path: wendu/cur_state[0]_syn_3768.clk->wendu/reg4_syn_99
wendu/cur_state[0]_syn_3768.clk
wendu/reg4_syn_99
2148 993.606000 1003.385000 9.779000 6 6
wendu/cnt_us[10] wendu/cur_state[0]_syn_3802.a[0]
wendu/cur_state[0]_syn_3503 wendu/cur_state[0]_syn_3754.b[1]
wendu/cur_state[0]_syn_3505 wendu/cur_state[0]_syn_3811.d[1]
wendu/cur_state[0]_syn_3516 wendu/mux0_syn_5.a[0]
wendu/cur_state[0]_syn_3518 wendu/cur_state[0]_syn_3764.d[1]
wendu/cur_state[5]_syn_930 wendu/reg4_syn_99.a[1]



Hold check
2196 3
Endpoint: wendu/reg5_syn_100
2198 0.435000 49 3
Timing path: wendu/reg5_syn_103.clk->wendu/reg5_syn_100
wendu/reg5_syn_103.clk
wendu/reg5_syn_100
2200 0.435000 2.955000 3.390000 1 1
wendu/data_temp[2] wendu/reg5_syn_100.d[1]

Timing path: wendu/reg5_syn_100.clk->wendu/reg5_syn_100
wendu/reg5_syn_100.clk
wendu/reg5_syn_100
2238 0.814000 2.955000 3.769000 1 1
wendu/data_temp[1] wendu/reg5_syn_100.c[1]

Timing path: wendu/next_state[0]_syn_52.clk->wendu/reg5_syn_100
wendu/next_state[0]_syn_52.clk
wendu/reg5_syn_100
2276 1.270000 2.955000 4.225000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3796.c[0]
wendu/cur_state[0]_syn_3555 wendu/reg5_syn_100.a[1]


Endpoint: wendu/reg5_syn_112
2316 0.435000 49 3
Timing path: wendu/reg5_syn_109.clk->wendu/reg5_syn_112
wendu/reg5_syn_109.clk
wendu/reg5_syn_112
2318 0.435000 2.955000 3.390000 1 1
wendu/data_temp[11] wendu/reg5_syn_112.d[0]

Timing path: wendu/reg5_syn_112.clk->wendu/reg5_syn_112
wendu/reg5_syn_112.clk
wendu/reg5_syn_112
2356 0.611000 2.955000 3.566000 1 1
wendu/data_temp[10] wendu/reg5_syn_112.c[0]

Timing path: wendu/next_state[0]_syn_52.clk->wendu/reg5_syn_112
wendu/next_state[0]_syn_52.clk
wendu/reg5_syn_112
2394 1.144000 2.955000 4.099000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3796.c[0]
wendu/cur_state[0]_syn_3555 wendu/reg5_syn_112.a[0]


Endpoint: wendu/reg5_syn_103
2434 0.443000 49 3
Timing path: wendu/reg5_syn_106.clk->wendu/reg5_syn_103
wendu/reg5_syn_106.clk
wendu/reg5_syn_103
2436 0.443000 2.955000 3.398000 1 1
wendu/data_temp[3] wendu/reg5_syn_103.d[1]

Timing path: wendu/reg5_syn_103.clk->wendu/reg5_syn_103
wendu/reg5_syn_103.clk
wendu/reg5_syn_103
2474 0.611000 2.955000 3.566000 1 1
wendu/data_temp[2] wendu/reg5_syn_103.c[1]

Timing path: wendu/next_state[0]_syn_52.clk->wendu/reg5_syn_103
wendu/next_state[0]_syn_52.clk
wendu/reg5_syn_103
2512 1.103000 2.955000 4.058000 2 2
wendu/cur_state[5] wendu/cur_state[0]_syn_3796.c[1]
wendu/cur_state[0]_syn_3557 wendu/reg5_syn_103.b[1]




Set input delay: 14.5ns max, and 14.5ns min. 
2552 24 24 2
Setup check
2562 3
Endpoint: AD_DATA[0]_syn_4
2562 3.080000 1 1
Timing path: AD_DATA[0]->AD_DATA[0]_syn_4
AD_DATA[0]
AD_DATA[0]_syn_4
2564 3.080000 18.804000 15.724000 0 1
AD_DATA[0] AD_DATA[0]_syn_4.ipad


Endpoint: AD_DATA[1]_syn_4
2596 3.080000 1 1
Timing path: AD_DATA[1]->AD_DATA[1]_syn_4
AD_DATA[1]
AD_DATA[1]_syn_4
2598 3.080000 18.804000 15.724000 0 1
AD_DATA[1] AD_DATA[1]_syn_4.ipad


Endpoint: AD_DATA[2]_syn_4
2630 3.080000 1 1
Timing path: AD_DATA[2]->AD_DATA[2]_syn_4
AD_DATA[2]
AD_DATA[2]_syn_4
2632 3.080000 18.804000 15.724000 0 1
AD_DATA[2] AD_DATA[2]_syn_4.ipad



Hold check
2664 3
Endpoint: AD_DATA[0]_syn_4
2666 13.126000 1 1
Timing path: AD_DATA[0]->AD_DATA[0]_syn_4
AD_DATA[0]
AD_DATA[0]_syn_4
2668 13.126000 2.226000 15.352000 0 1
AD_DATA[0] AD_DATA[0]_syn_4.ipad


Endpoint: AD_DATA[1]_syn_4
2700 13.126000 1 1
Timing path: AD_DATA[1]->AD_DATA[1]_syn_4
AD_DATA[1]
AD_DATA[1]_syn_4
2702 13.126000 2.226000 15.352000 0 1
AD_DATA[1] AD_DATA[1]_syn_4.ipad


Endpoint: AD_DATA[2]_syn_4
2734 13.126000 1 1
Timing path: AD_DATA[2]->AD_DATA[2]_syn_4
AD_DATA[2]
AD_DATA[2]_syn_4
2736 13.126000 2.226000 15.352000 0 1
AD_DATA[2] AD_DATA[2]_syn_4.ipad




Path delay: 8.032ns max
2768 7 7 1
Max path
2778 3
Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
2778 6.996000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_36.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
2780 6.996000 10.192000 3.196000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[0] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30.mi[1]


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
2813 7.002000 1 1
Timing path: signal_process/demodu/fifo/ram_inst/reg2_syn_173.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
signal_process/demodu/fifo/ram_inst/reg2_syn_173.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30
2815 7.002000 10.192000 3.190000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[3] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_30.mi[0]


Endpoint: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
2848 7.101000 1 1
Timing path: signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk->signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg0_syn_39.clk
signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27
2850 7.101000 10.192000 3.091000 0 1
signal_process/demodu/fifo/rd_to_wr_cross_inst/primary_addr_gray_reg[2] signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_27.mi[1]




Path delay: 8.032ns max
2885 7 7 1
Max path
2895 3
Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
2895 6.878000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28
2897 6.878000 10.326000 3.448000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[1] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_28.mi[1]


Endpoint: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
2930 6.996000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk->signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.clk
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31
2932 6.996000 10.326000 3.330000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[0] signal_process/demodu/fifo/wr_to_rd_cross_inst/reg3_syn_31.mi[1]


Endpoint: signal_process/demodu/fifo/sub0_syn_57
2965 7.089000 1 1
Timing path: signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk->signal_process/demodu/fifo/sub0_syn_57
signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_39.clk
signal_process/demodu/fifo/sub0_syn_57
2967 7.089000 10.326000 3.237000 0 1
signal_process/demodu/fifo/wr_to_rd_cross_inst/primary_addr_gray_reg[4] signal_process/demodu/fifo/sub0_syn_57.mi[1]





Timing group statistics: 
	Clock constraints: 
	  Clock Name                                  Min Period     Max Freq           Skew      Fanout            TNS
	  CLK120/pll_inst.clkc[0] (120.0MHz)             8.115ns     123.229MHz        0.480ns       454        0.000ns
	  CLK120/pll_inst.clkc[3] (60.0MHz)             13.586ns      73.605MHz        0.326ns       108        0.000ns
	  clk_us (1000.0KHz)                             7.560ns     132.275MHz        0.254ns        43        0.000ns
	Minimum input arrival time before clock: 1.224ns
	Maximum output required time after clock: no constraint path
	Maximum combinational path delay: no constraint path
Warning: No clock constraint on 1 clock net(s): 
	dlycnt_n1_syn_2

	Exceptions:

		Check Type:	MIN
		----------------------------------------------------------------------------------------------------
		       Path Num     Constraint                                                                      
		              7     set_false_path -hold -from [ get_regs {rd_to_wr_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {rd_to_wr_cross_inst/sync_r1[*]} ]
		              7     set_false_path -hold -from [ get_regs {wr_to_rd_cross_inst/primary_addr_gray_reg[*]} ] -to [ get_regs {wr_to_rd_cross_inst/sync_r1[*]} ]

