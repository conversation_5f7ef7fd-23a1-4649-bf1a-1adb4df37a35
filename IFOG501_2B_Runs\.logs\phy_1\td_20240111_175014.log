============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jan 11 17:50:14 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1527 instances
RUN-0007 : 368 luts, 899 seqs, 136 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2080 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1538 nets have 2 pins
RUN-1001 : 429 nets have [3 - 5] pins
RUN-1001 : 69 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     122     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1525 instances, 368 luts, 899 seqs, 211 slices, 23 macros(211 instances: 136 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7349, tnet num: 2078, tinst num: 1525, tnode num: 10332, tedge num: 12482.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.295416s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (100.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 575935
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1525.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 502140, overlap = 15.75
PHY-3002 : Step(2): len = 471445, overlap = 15.75
PHY-3002 : Step(3): len = 452436, overlap = 15.75
PHY-3002 : Step(4): len = 437930, overlap = 18
PHY-3002 : Step(5): len = 421128, overlap = 13.5
PHY-3002 : Step(6): len = 410296, overlap = 20.25
PHY-3002 : Step(7): len = 397078, overlap = 15.75
PHY-3002 : Step(8): len = 385155, overlap = 20.25
PHY-3002 : Step(9): len = 374554, overlap = 18
PHY-3002 : Step(10): len = 366905, overlap = 20.25
PHY-3002 : Step(11): len = 355127, overlap = 18
PHY-3002 : Step(12): len = 346778, overlap = 20.25
PHY-3002 : Step(13): len = 339443, overlap = 18
PHY-3002 : Step(14): len = 330426, overlap = 20.25
PHY-3002 : Step(15): len = 321291, overlap = 18
PHY-3002 : Step(16): len = 315474, overlap = 20.25
PHY-3002 : Step(17): len = 307304, overlap = 18
PHY-3002 : Step(18): len = 297523, overlap = 18
PHY-3002 : Step(19): len = 290768, overlap = 18
PHY-3002 : Step(20): len = 285507, overlap = 18
PHY-3002 : Step(21): len = 275091, overlap = 15.75
PHY-3002 : Step(22): len = 266947, overlap = 18
PHY-3002 : Step(23): len = 261504, overlap = 18
PHY-3002 : Step(24): len = 252555, overlap = 18
PHY-3002 : Step(25): len = 243298, overlap = 15.75
PHY-3002 : Step(26): len = 239662, overlap = 18
PHY-3002 : Step(27): len = 231148, overlap = 15.75
PHY-3002 : Step(28): len = 215611, overlap = 18
PHY-3002 : Step(29): len = 210235, overlap = 18
PHY-3002 : Step(30): len = 207291, overlap = 18
PHY-3002 : Step(31): len = 183597, overlap = 15.75
PHY-3002 : Step(32): len = 176248, overlap = 18
PHY-3002 : Step(33): len = 174187, overlap = 18
PHY-3002 : Step(34): len = 157511, overlap = 15.75
PHY-3002 : Step(35): len = 141372, overlap = 13.5
PHY-3002 : Step(36): len = 139929, overlap = 18
PHY-3002 : Step(37): len = 135693, overlap = 18
PHY-3002 : Step(38): len = 129144, overlap = 18
PHY-3002 : Step(39): len = 126479, overlap = 18
PHY-3002 : Step(40): len = 123586, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113709
PHY-3002 : Step(41): len = 123546, overlap = 13.5
PHY-3002 : Step(42): len = 123117, overlap = 13.5
PHY-3002 : Step(43): len = 122125, overlap = 13.5
PHY-3002 : Step(44): len = 121380, overlap = 13.5
PHY-3002 : Step(45): len = 120607, overlap = 11.25
PHY-3002 : Step(46): len = 115504, overlap = 15.75
PHY-3002 : Step(47): len = 111168, overlap = 11.25
PHY-3002 : Step(48): len = 110014, overlap = 11.25
PHY-3002 : Step(49): len = 107996, overlap = 11.25
PHY-3002 : Step(50): len = 104201, overlap = 11.25
PHY-3002 : Step(51): len = 103611, overlap = 11.25
PHY-3002 : Step(52): len = 102202, overlap = 11.25
PHY-3002 : Step(53): len = 100417, overlap = 15.75
PHY-3002 : Step(54): len = 99312.7, overlap = 15.75
PHY-3002 : Step(55): len = 97404.4, overlap = 15.75
PHY-3002 : Step(56): len = 94427.5, overlap = 11.25
PHY-3002 : Step(57): len = 93447.3, overlap = 11.25
PHY-3002 : Step(58): len = 91811.1, overlap = 13.5
PHY-3002 : Step(59): len = 89435.7, overlap = 15.75
PHY-3002 : Step(60): len = 87395.9, overlap = 13.5
PHY-3002 : Step(61): len = 86290.5, overlap = 13.5
PHY-3002 : Step(62): len = 84897.7, overlap = 11.25
PHY-3002 : Step(63): len = 83002.9, overlap = 13.5
PHY-3002 : Step(64): len = 80762.6, overlap = 13.5
PHY-3002 : Step(65): len = 79252.8, overlap = 13.5
PHY-3002 : Step(66): len = 77816.3, overlap = 13.5
PHY-3002 : Step(67): len = 76451, overlap = 15.75
PHY-3002 : Step(68): len = 74251, overlap = 15.75
PHY-3002 : Step(69): len = 72789.4, overlap = 13.6875
PHY-3002 : Step(70): len = 71462.5, overlap = 14.125
PHY-3002 : Step(71): len = 70800.7, overlap = 11.9375
PHY-3002 : Step(72): len = 69539, overlap = 12.1875
PHY-3002 : Step(73): len = 68898.4, overlap = 12.5
PHY-3002 : Step(74): len = 67771.8, overlap = 12.75
PHY-3002 : Step(75): len = 66169.5, overlap = 16.0625
PHY-3002 : Step(76): len = 65687.8, overlap = 16.1875
PHY-3002 : Step(77): len = 65052.7, overlap = 18.4375
PHY-3002 : Step(78): len = 64444.4, overlap = 16.375
PHY-3002 : Step(79): len = 63802.5, overlap = 16
PHY-3002 : Step(80): len = 63040, overlap = 16.375
PHY-3002 : Step(81): len = 62395.7, overlap = 16.375
PHY-3002 : Step(82): len = 62136, overlap = 16.3125
PHY-3002 : Step(83): len = 60989.1, overlap = 16.25
PHY-3002 : Step(84): len = 60906.1, overlap = 13.9375
PHY-3002 : Step(85): len = 60296.9, overlap = 11.5
PHY-3002 : Step(86): len = 59632.1, overlap = 13.5
PHY-3002 : Step(87): len = 59379.3, overlap = 15.625
PHY-3002 : Step(88): len = 59328.4, overlap = 15.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000227417
PHY-3002 : Step(89): len = 59074, overlap = 15.625
PHY-3002 : Step(90): len = 59024.8, overlap = 15.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000454835
PHY-3002 : Step(91): len = 59187.8, overlap = 15.625
PHY-3002 : Step(92): len = 59261.5, overlap = 13.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.018287s wall, 0.000000s user + 0.046875s system = 0.046875s CPU (256.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.074410s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (105.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000197078
PHY-3002 : Step(93): len = 61839.2, overlap = 16.7188
PHY-3002 : Step(94): len = 62072.9, overlap = 15.5625
PHY-3002 : Step(95): len = 60329.5, overlap = 14.9375
PHY-3002 : Step(96): len = 59800.9, overlap = 14.6562
PHY-3002 : Step(97): len = 58498.7, overlap = 12.2812
PHY-3002 : Step(98): len = 57888.9, overlap = 11.875
PHY-3002 : Step(99): len = 57635.9, overlap = 12.6875
PHY-3002 : Step(100): len = 56792.8, overlap = 12.6875
PHY-3002 : Step(101): len = 55971.2, overlap = 12.6875
PHY-3002 : Step(102): len = 55632, overlap = 14.5
PHY-3002 : Step(103): len = 54469.4, overlap = 14.5
PHY-3002 : Step(104): len = 53944.1, overlap = 14.0625
PHY-3002 : Step(105): len = 53629.3, overlap = 14.375
PHY-3002 : Step(106): len = 52913.2, overlap = 14.2188
PHY-3002 : Step(107): len = 52747.1, overlap = 14.625
PHY-3002 : Step(108): len = 52797.3, overlap = 14.875
PHY-3002 : Step(109): len = 52481, overlap = 15.9375
PHY-3002 : Step(110): len = 52381.2, overlap = 14.7812
PHY-3002 : Step(111): len = 51580.7, overlap = 16.8125
PHY-3002 : Step(112): len = 49807.3, overlap = 21.5938
PHY-3002 : Step(113): len = 48826.1, overlap = 20.7188
PHY-3002 : Step(114): len = 48446.2, overlap = 19.9688
PHY-3002 : Step(115): len = 47987.8, overlap = 19.2188
PHY-3002 : Step(116): len = 47941.1, overlap = 18.9688
PHY-3002 : Step(117): len = 47599.6, overlap = 19.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000394157
PHY-3002 : Step(118): len = 47313.8, overlap = 19.4688
PHY-3002 : Step(119): len = 46999.7, overlap = 19.4062
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000788314
PHY-3002 : Step(120): len = 46965.1, overlap = 19.5938
PHY-3002 : Step(121): len = 46986.6, overlap = 19.6562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064643s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.33685e-05
PHY-3002 : Step(122): len = 47327.2, overlap = 48.8438
PHY-3002 : Step(123): len = 48140.4, overlap = 49.5625
PHY-3002 : Step(124): len = 48354.4, overlap = 49.1562
PHY-3002 : Step(125): len = 47697.6, overlap = 42.1875
PHY-3002 : Step(126): len = 47679.9, overlap = 42.0312
PHY-3002 : Step(127): len = 47803.5, overlap = 41.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106737
PHY-3002 : Step(128): len = 47876, overlap = 41.7188
PHY-3002 : Step(129): len = 47876, overlap = 41.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0001727
PHY-3002 : Step(130): len = 48349.1, overlap = 41.1562
PHY-3002 : Step(131): len = 49615.3, overlap = 39.6875
PHY-3002 : Step(132): len = 50635.8, overlap = 38.1562
PHY-3002 : Step(133): len = 51415.4, overlap = 37.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000345401
PHY-3002 : Step(134): len = 51452.7, overlap = 37.3125
PHY-3002 : Step(135): len = 51670.3, overlap = 34.9062
PHY-3002 : Step(136): len = 51784.6, overlap = 30.0938
PHY-3002 : Step(137): len = 51719.9, overlap = 30.5312
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000690802
PHY-3002 : Step(138): len = 51998.1, overlap = 30.0312
PHY-3002 : Step(139): len = 52444.8, overlap = 29.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7349, tnet num: 2078, tinst num: 1525, tnode num: 10332, tedge num: 12482.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.28 peak overflow 3.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2080.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55920, over cnt = 238(0%), over = 953, worst = 22
PHY-1001 : End global iterations;  0.085841s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.0%)

PHY-1001 : Congestion index: top1 = 43.69, top5 = 25.35, top10 = 16.20, top15 = 11.49.
PHY-1001 : End incremental global routing;  0.140760s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070571s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.242165s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (96.8%)

OPT-1001 : Current memory(MB): used = 211, reserve = 174, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1563/2080.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55920, over cnt = 238(0%), over = 953, worst = 22
PHY-1002 : len = 59992, over cnt = 171(0%), over = 524, worst = 22
PHY-1002 : len = 65752, over cnt = 50(0%), over = 69, worst = 5
PHY-1002 : len = 66616, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 67176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094290s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (116.0%)

PHY-1001 : Congestion index: top1 = 37.35, top5 = 24.82, top10 = 17.79, top15 = 13.00.
OPT-1001 : End congestion update;  0.142947s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (109.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2078 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059595s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.205526s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (114.0%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : End physical optimization;  0.730967s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (113.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 163 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 109 SEQ with LUT/SLICE
SYN-4006 : 114 single LUT's are left
SYN-4006 : 627 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 995/1289 primitive instances ...
PHY-3001 : End packing;  0.048811s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 782 instances
RUN-1001 : 366 mslices, 367 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1925 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1389 nets have 2 pins
RUN-1001 : 423 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 780 instances, 733 slices, 23 macros(211 instances: 136 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52687, Over = 56.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6222, tnet num: 1923, tinst num: 780, tnode num: 8392, tedge num: 10966.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1923 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.312558s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.96072e-05
PHY-3002 : Step(140): len = 51913.7, overlap = 57.25
PHY-3002 : Step(141): len = 50960.3, overlap = 57.25
PHY-3002 : Step(142): len = 50667.6, overlap = 55.5
PHY-3002 : Step(143): len = 50693, overlap = 54.75
PHY-3002 : Step(144): len = 50562.9, overlap = 53.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.92144e-05
PHY-3002 : Step(145): len = 51252.2, overlap = 52
PHY-3002 : Step(146): len = 52532.4, overlap = 48
PHY-3002 : Step(147): len = 52770.1, overlap = 48.75
PHY-3002 : Step(148): len = 52436.1, overlap = 50.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000118429
PHY-3002 : Step(149): len = 53053.8, overlap = 49
PHY-3002 : Step(150): len = 53603.3, overlap = 48.75
PHY-3002 : Step(151): len = 54579.1, overlap = 45
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.110687s wall, 0.078125s user + 0.078125s system = 0.156250s CPU (141.2%)

PHY-3001 : Trial Legalized: Len = 67028.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1923 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050968s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000920193
PHY-3002 : Step(152): len = 63622.9, overlap = 4.75
PHY-3002 : Step(153): len = 61660.1, overlap = 8.75
PHY-3002 : Step(154): len = 60013.7, overlap = 11.25
PHY-3002 : Step(155): len = 59078.4, overlap = 12.75
PHY-3002 : Step(156): len = 58786.5, overlap = 15
PHY-3002 : Step(157): len = 58605.3, overlap = 16.25
PHY-3002 : Step(158): len = 58320.2, overlap = 17
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00184039
PHY-3002 : Step(159): len = 58512.8, overlap = 18.5
PHY-3002 : Step(160): len = 58663.9, overlap = 18.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00368077
PHY-3002 : Step(161): len = 58835.5, overlap = 17.75
PHY-3002 : Step(162): len = 58835.5, overlap = 17.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005611s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62732.4, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005804s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 2, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 62894.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6222, tnet num: 1923, tinst num: 780, tnode num: 8392, tedge num: 10966.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 56/1925.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69096, over cnt = 137(0%), over = 219, worst = 7
PHY-1002 : len = 70024, over cnt = 81(0%), over = 109, worst = 4
PHY-1002 : len = 71352, over cnt = 5(0%), over = 6, worst = 2
PHY-1002 : len = 71456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.143846s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (119.5%)

PHY-1001 : Congestion index: top1 = 31.10, top5 = 22.45, top10 = 17.57, top15 = 13.81.
PHY-1001 : End incremental global routing;  0.200095s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (117.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1923 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071634s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.302273s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (108.6%)

OPT-1001 : Current memory(MB): used = 214, reserve = 179, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1681/1925.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007066s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (221.1%)

PHY-1001 : Congestion index: top1 = 31.10, top5 = 22.45, top10 = 17.57, top15 = 13.81.
OPT-1001 : End congestion update;  0.057743s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1923 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064215s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 742 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 780 instances, 733 slices, 23 macros(211 instances: 136 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62943, Over = 0
PHY-3001 : End spreading;  0.006083s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (256.9%)

PHY-3001 : Final: Len = 62943, Over = 0
PHY-3001 : End incremental legalization;  0.037754s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (124.2%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 0 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.174304s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.6%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1923 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052955s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1677/1925.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71488, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.017358s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (90.0%)

PHY-1001 : Congestion index: top1 = 31.10, top5 = 22.48, top10 = 17.59, top15 = 13.81.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1923 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052034s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (60.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.551724
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.980496s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (102.0%)

RUN-1003 : finish command "place" in  6.502945s wall, 9.000000s user + 4.343750s system = 13.343750s CPU (205.2%)

RUN-1004 : used memory is 202 MB, reserved memory is 165 MB, peak memory is 219 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 782 instances
RUN-1001 : 366 mslices, 367 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1925 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1389 nets have 2 pins
RUN-1001 : 423 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6222, tnet num: 1923, tinst num: 780, tnode num: 8392, tedge num: 10966.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 366 mslices, 367 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1923 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68472, over cnt = 138(0%), over = 216, worst = 6
PHY-1002 : len = 69264, over cnt = 85(0%), over = 115, worst = 4
PHY-1002 : len = 70728, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 70792, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.166826s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (121.8%)

PHY-1001 : Congestion index: top1 = 30.56, top5 = 22.25, top10 = 17.48, top15 = 13.72.
PHY-1001 : End global routing;  0.218751s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (114.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 200, peak = 236.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 495, reserve = 465, peak = 495.
PHY-1001 : End build detailed router design. 3.405080s wall, 3.328125s user + 0.078125s system = 3.406250s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34512, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.348188s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 529, reserve = 499, peak = 529.
PHY-1001 : End phase 1; 1.354411s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 188280, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 500, peak = 530.
PHY-1001 : End initial routed; 1.485019s wall, 2.265625s user + 0.109375s system = 2.375000s CPU (159.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1700(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.520   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.076   |  -0.237   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.378542s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (103.2%)

PHY-1001 : Current memory(MB): used = 531, reserve = 501, peak = 531.
PHY-1001 : End phase 2; 1.863654s wall, 2.656250s user + 0.109375s system = 2.765625s CPU (148.4%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 188280, over cnt = 29(0%), over = 29, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016855s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (92.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 188240, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024551s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 188264, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025554s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1700(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.520   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.076   |  -0.237   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.355045s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (101.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.175572s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.9%)

PHY-1001 : Current memory(MB): used = 545, reserve = 514, peak = 545.
PHY-1001 : End phase 3; 0.728838s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (96.5%)

PHY-1003 : Routed, final wirelength = 188264
PHY-1001 : Current memory(MB): used = 546, reserve = 514, peak = 546.
PHY-1001 : End export database. 0.009932s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (157.3%)

PHY-1001 : End detail routing;  7.544524s wall, 8.234375s user + 0.203125s system = 8.437500s CPU (111.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6222, tnet num: 1923, tinst num: 780, tnode num: 8392, tedge num: 10966.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[1] slack -62ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.dia[1] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6250, tnet num: 1937, tinst num: 794, tnode num: 8420, tedge num: 10994.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.158525s wall, 3.140625s user + 0.171875s system = 3.312500s CPU (104.9%)

RUN-1003 : finish command "route" in  11.274908s wall, 11.937500s user + 0.406250s system = 12.343750s CPU (109.5%)

RUN-1004 : used memory is 526 MB, reserved memory is 499 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      819   out of  19600    4.18%
#reg                      988   out of  19600    5.04%
#le                      1446
  #lut only               458   out of   1446   31.67%
  #reg only               627   out of   1446   43.36%
  #lut&reg                361   out of   1446   24.97%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         436
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1446   |608     |211     |1019    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1032   |289     |118     |836     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |24     |18      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |445    |106     |43      |344     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |35      |6       |44      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |7       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |11      |0       |15      |0       |0       |
|    integ                   |Integration                                      |136    |36      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |94     |30      |21      |90      |0       |1       |
|    rs422                   |Rs422Output                                      |305    |76      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |123    |114     |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |27      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |20     |20      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |67     |67      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1367  
    #2          2       288   
    #3          3       120   
    #4          4        15   
    #5        5-10       76   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6250, tnet num: 1937, tinst num: 794, tnode num: 8420, tedge num: 10994.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1937 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 794
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1939, pip num: 14455
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1303 valid insts, and 38332 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.650724s wall, 18.796875s user + 0.078125s system = 18.875000s CPU (517.0%)

RUN-1004 : used memory is 546 MB, reserved memory is 513 MB, peak memory is 674 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240111_175014.log"
