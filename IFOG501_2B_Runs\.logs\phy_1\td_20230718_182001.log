============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 18:20:01 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1647 instances
RUN-0007 : 380 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2218 nets
RUN-1001 : 1657 nets have 2 pins
RUN-1001 : 447 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1645 instances, 380 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7841, tnet num: 2216, tinst num: 1645, tnode num: 11082, tedge num: 13235.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.281450s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 616830
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1645.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 484112, overlap = 20.5
PHY-3002 : Step(2): len = 360800, overlap = 15.75
PHY-3002 : Step(3): len = 333793, overlap = 18
PHY-3002 : Step(4): len = 323574, overlap = 20.25
PHY-3002 : Step(5): len = 313466, overlap = 18
PHY-3002 : Step(6): len = 306982, overlap = 18
PHY-3002 : Step(7): len = 299336, overlap = 18
PHY-3002 : Step(8): len = 293826, overlap = 18
PHY-3002 : Step(9): len = 283992, overlap = 18
PHY-3002 : Step(10): len = 278690, overlap = 20.25
PHY-3002 : Step(11): len = 272570, overlap = 20.25
PHY-3002 : Step(12): len = 266548, overlap = 20.25
PHY-3002 : Step(13): len = 260238, overlap = 20.25
PHY-3002 : Step(14): len = 256453, overlap = 20.25
PHY-3002 : Step(15): len = 250246, overlap = 20.25
PHY-3002 : Step(16): len = 244961, overlap = 20.25
PHY-3002 : Step(17): len = 238763, overlap = 20.25
PHY-3002 : Step(18): len = 235751, overlap = 20.25
PHY-3002 : Step(19): len = 227416, overlap = 20.25
PHY-3002 : Step(20): len = 222659, overlap = 20.25
PHY-3002 : Step(21): len = 218667, overlap = 20.25
PHY-3002 : Step(22): len = 214173, overlap = 20.25
PHY-3002 : Step(23): len = 206889, overlap = 20.25
PHY-3002 : Step(24): len = 203871, overlap = 20.25
PHY-3002 : Step(25): len = 199325, overlap = 20.25
PHY-3002 : Step(26): len = 178931, overlap = 20.25
PHY-3002 : Step(27): len = 171392, overlap = 20.25
PHY-3002 : Step(28): len = 170272, overlap = 20.25
PHY-3002 : Step(29): len = 138002, overlap = 15.75
PHY-3002 : Step(30): len = 124300, overlap = 18
PHY-3002 : Step(31): len = 122567, overlap = 18
PHY-3002 : Step(32): len = 119309, overlap = 18
PHY-3002 : Step(33): len = 113285, overlap = 18
PHY-3002 : Step(34): len = 111341, overlap = 18
PHY-3002 : Step(35): len = 109430, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.86996e-05
PHY-3002 : Step(36): len = 110826, overlap = 15.75
PHY-3002 : Step(37): len = 109750, overlap = 11.25
PHY-3002 : Step(38): len = 108471, overlap = 13.5
PHY-3002 : Step(39): len = 106620, overlap = 13.5
PHY-3002 : Step(40): len = 104013, overlap = 9
PHY-3002 : Step(41): len = 103655, overlap = 6.75
PHY-3002 : Step(42): len = 102831, overlap = 4.5
PHY-3002 : Step(43): len = 99015.1, overlap = 13.5
PHY-3002 : Step(44): len = 94404.4, overlap = 11.25
PHY-3002 : Step(45): len = 93011.5, overlap = 11.25
PHY-3002 : Step(46): len = 92888.8, overlap = 6.75
PHY-3002 : Step(47): len = 89394.9, overlap = 11.25
PHY-3002 : Step(48): len = 85807.8, overlap = 13.5
PHY-3002 : Step(49): len = 84124.5, overlap = 9
PHY-3002 : Step(50): len = 84319.9, overlap = 4.5
PHY-3002 : Step(51): len = 83471.7, overlap = 2.25
PHY-3002 : Step(52): len = 81985.7, overlap = 4.5
PHY-3002 : Step(53): len = 80730.2, overlap = 11.25
PHY-3002 : Step(54): len = 79158.2, overlap = 11.25
PHY-3002 : Step(55): len = 78318.5, overlap = 9
PHY-3002 : Step(56): len = 77898.3, overlap = 6.75
PHY-3002 : Step(57): len = 75924.3, overlap = 6.75
PHY-3002 : Step(58): len = 74139.5, overlap = 11.25
PHY-3002 : Step(59): len = 73087.1, overlap = 11.25
PHY-3002 : Step(60): len = 72055.1, overlap = 11.25
PHY-3002 : Step(61): len = 70389.5, overlap = 11.25
PHY-3002 : Step(62): len = 69779.2, overlap = 11.25
PHY-3002 : Step(63): len = 68000.6, overlap = 11.375
PHY-3002 : Step(64): len = 67148.9, overlap = 11.375
PHY-3002 : Step(65): len = 67397.1, overlap = 12.125
PHY-3002 : Step(66): len = 67275.8, overlap = 7.625
PHY-3002 : Step(67): len = 66295.7, overlap = 9.8125
PHY-3002 : Step(68): len = 65043.6, overlap = 12.125
PHY-3002 : Step(69): len = 64242, overlap = 12.125
PHY-3002 : Step(70): len = 63043.5, overlap = 9.875
PHY-3002 : Step(71): len = 60015.9, overlap = 7.4375
PHY-3002 : Step(72): len = 57270.8, overlap = 7.0625
PHY-3002 : Step(73): len = 56430.7, overlap = 9.4375
PHY-3002 : Step(74): len = 56105.2, overlap = 9.4375
PHY-3002 : Step(75): len = 55884.9, overlap = 11.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000157399
PHY-3002 : Step(76): len = 56029.4, overlap = 9.4375
PHY-3002 : Step(77): len = 56090.5, overlap = 9.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000314798
PHY-3002 : Step(78): len = 56234.1, overlap = 7.1875
PHY-3002 : Step(79): len = 56292, overlap = 4.9375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007133s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (219.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071923s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(80): len = 59142, overlap = 7.8125
PHY-3002 : Step(81): len = 58327.5, overlap = 7.90625
PHY-3002 : Step(82): len = 57878.4, overlap = 7.9375
PHY-3002 : Step(83): len = 56542.3, overlap = 7.875
PHY-3002 : Step(84): len = 55850.4, overlap = 7.625
PHY-3002 : Step(85): len = 54087.5, overlap = 10.1875
PHY-3002 : Step(86): len = 53430.5, overlap = 10.75
PHY-3002 : Step(87): len = 52685, overlap = 10.25
PHY-3002 : Step(88): len = 51878.5, overlap = 10.25
PHY-3002 : Step(89): len = 50423.1, overlap = 10.6875
PHY-3002 : Step(90): len = 49891.6, overlap = 11.25
PHY-3002 : Step(91): len = 49583.2, overlap = 11
PHY-3002 : Step(92): len = 49212.1, overlap = 10.5625
PHY-3002 : Step(93): len = 48623.6, overlap = 8.875
PHY-3002 : Step(94): len = 48312.8, overlap = 8.5
PHY-3002 : Step(95): len = 48325.9, overlap = 8.875
PHY-3002 : Step(96): len = 48053.4, overlap = 10
PHY-3002 : Step(97): len = 47430.5, overlap = 12.3125
PHY-3002 : Step(98): len = 46916.2, overlap = 14
PHY-3002 : Step(99): len = 46646.2, overlap = 13.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000442503
PHY-3002 : Step(100): len = 46671.9, overlap = 11.6875
PHY-3002 : Step(101): len = 46604.5, overlap = 11.25
PHY-3002 : Step(102): len = 46530.7, overlap = 11.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000885007
PHY-3002 : Step(103): len = 46552.1, overlap = 10.9375
PHY-3002 : Step(104): len = 46552.1, overlap = 10.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072405s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000104671
PHY-3002 : Step(105): len = 46705.9, overlap = 59.7188
PHY-3002 : Step(106): len = 47340.7, overlap = 57.875
PHY-3002 : Step(107): len = 47712.1, overlap = 56.0312
PHY-3002 : Step(108): len = 47704.3, overlap = 56.125
PHY-3002 : Step(109): len = 47760.7, overlap = 56.3125
PHY-3002 : Step(110): len = 47670.6, overlap = 55.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000209341
PHY-3002 : Step(111): len = 47730.7, overlap = 55.0625
PHY-3002 : Step(112): len = 47942.5, overlap = 55.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000418682
PHY-3002 : Step(113): len = 48038.5, overlap = 55.0625
PHY-3002 : Step(114): len = 49258.9, overlap = 44.4688
PHY-3002 : Step(115): len = 50903.9, overlap = 37.4688
PHY-3002 : Step(116): len = 51486.6, overlap = 35.875
PHY-3002 : Step(117): len = 51260.9, overlap = 29.9688
PHY-3002 : Step(118): len = 50707.2, overlap = 29.3438
PHY-3002 : Step(119): len = 50095.2, overlap = 28.4688
PHY-3002 : Step(120): len = 49733.5, overlap = 28.9375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7841, tnet num: 2216, tinst num: 1645, tnode num: 11082, tedge num: 13235.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 83.22 peak overflow 4.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2218.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52608, over cnt = 240(0%), over = 947, worst = 14
PHY-1001 : End global iterations;  0.060473s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (206.7%)

PHY-1001 : Congestion index: top1 = 38.71, top5 = 23.71, top10 = 15.29, top15 = 10.86.
PHY-1001 : End incremental global routing;  0.108881s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (157.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065855s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (118.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.205038s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (137.2%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1632/2218.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52608, over cnt = 240(0%), over = 947, worst = 14
PHY-1002 : len = 57120, over cnt = 153(0%), over = 443, worst = 14
PHY-1002 : len = 62208, over cnt = 29(0%), over = 41, worst = 4
PHY-1002 : len = 62824, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 62952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.091938s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (119.0%)

PHY-1001 : Congestion index: top1 = 35.13, top5 = 23.58, top10 = 16.90, top15 = 12.54.
OPT-1001 : End congestion update;  0.134256s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (104.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058464s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.195303s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.0%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.683216s wall, 0.718750s user + 0.031250s system = 0.750000s CPU (109.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 96 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 707 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1087/1420 primitive instances ...
PHY-3001 : End packing;  0.050922s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 849 instances
RUN-1001 : 400 mslices, 400 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2043 nets
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 847 instances, 800 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49981.2, Over = 57.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6608, tnet num: 2041, tinst num: 847, tnode num: 8960, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2041 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.310916s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.41885e-05
PHY-3002 : Step(121): len = 49636.8, overlap = 58
PHY-3002 : Step(122): len = 49671.6, overlap = 60.75
PHY-3002 : Step(123): len = 49378.3, overlap = 61.25
PHY-3002 : Step(124): len = 49248.7, overlap = 60.5
PHY-3002 : Step(125): len = 49316.4, overlap = 62.25
PHY-3002 : Step(126): len = 49150.6, overlap = 61
PHY-3002 : Step(127): len = 49138.3, overlap = 61.75
PHY-3002 : Step(128): len = 48944.1, overlap = 62.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.83769e-05
PHY-3002 : Step(129): len = 49562.8, overlap = 62.25
PHY-3002 : Step(130): len = 50111.5, overlap = 60
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.67539e-05
PHY-3002 : Step(131): len = 50547.7, overlap = 57.75
PHY-3002 : Step(132): len = 50993.3, overlap = 56.75
PHY-3002 : Step(133): len = 51754.7, overlap = 54.5
PHY-3002 : Step(134): len = 52408.4, overlap = 51.5
PHY-3002 : Step(135): len = 52699, overlap = 51
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.093613s wall, 0.078125s user + 0.093750s system = 0.171875s CPU (183.6%)

PHY-3001 : Trial Legalized: Len = 66121.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2041 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049842s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00060033
PHY-3002 : Step(136): len = 63695.5, overlap = 3.75
PHY-3002 : Step(137): len = 61450.9, overlap = 8.75
PHY-3002 : Step(138): len = 59783.5, overlap = 13.75
PHY-3002 : Step(139): len = 58907.9, overlap = 16.25
PHY-3002 : Step(140): len = 58393.9, overlap = 20
PHY-3002 : Step(141): len = 58116.1, overlap = 20
PHY-3002 : Step(142): len = 57832.4, overlap = 20.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00120066
PHY-3002 : Step(143): len = 58169.7, overlap = 20.5
PHY-3002 : Step(144): len = 58300.5, overlap = 19.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00240132
PHY-3002 : Step(145): len = 58422.3, overlap = 20
PHY-3002 : Step(146): len = 58535.6, overlap = 19.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004848s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63420.1, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006151s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (254.0%)

PHY-3001 : 8 instances has been re-located, deltaX = 0, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 63462.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6608, tnet num: 2041, tinst num: 847, tnode num: 8960, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 65/2043.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69528, over cnt = 154(0%), over = 245, worst = 7
PHY-1002 : len = 70880, over cnt = 65(0%), over = 77, worst = 3
PHY-1002 : len = 71544, over cnt = 14(0%), over = 18, worst = 2
PHY-1002 : len = 71768, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 71832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127208s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (147.4%)

PHY-1001 : Congestion index: top1 = 31.06, top5 = 22.37, top10 = 17.70, top15 = 14.18.
PHY-1001 : End incremental global routing;  0.177787s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (131.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2041 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058907s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.274127s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (119.7%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1807/2043.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71832, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015119s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (103.3%)

PHY-1001 : Congestion index: top1 = 31.06, top5 = 22.37, top10 = 17.70, top15 = 14.18.
OPT-1001 : End congestion update;  0.061163s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2041 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053457s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 809 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 847 instances, 800 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63495.2, Over = 0
PHY-3001 : End spreading;  0.005285s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63495.2, Over = 0
PHY-3001 : End incremental legalization;  0.037011s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (126.7%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.163507s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (95.6%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2041 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048302s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1803/2043.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71880, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009273s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (168.5%)

PHY-1001 : Congestion index: top1 = 31.06, top5 = 22.35, top10 = 17.70, top15 = 14.18.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2041 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049167s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.551724
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.894742s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (104.8%)

RUN-1003 : finish command "place" in  5.112742s wall, 8.093750s user + 1.937500s system = 10.031250s CPU (196.2%)

RUN-1004 : used memory is 206 MB, reserved memory is 170 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 849 instances
RUN-1001 : 400 mslices, 400 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2043 nets
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6608, tnet num: 2041, tinst num: 847, tnode num: 8960, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 400 mslices, 400 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2041 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69056, over cnt = 155(0%), over = 248, worst = 7
PHY-1002 : len = 70488, over cnt = 70(0%), over = 81, worst = 3
PHY-1002 : len = 70888, over cnt = 37(0%), over = 45, worst = 3
PHY-1002 : len = 71392, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131133s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (119.2%)

PHY-1001 : Congestion index: top1 = 30.88, top5 = 22.24, top10 = 17.57, top15 = 14.10.
PHY-1001 : End global routing;  0.179790s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (113.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 204, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.277061s wall, 3.218750s user + 0.046875s system = 3.265625s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34224, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.245607s wall, 1.234375s user + 0.015625s system = 1.250000s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 532, reserve = 503, peak = 532.
PHY-1001 : End phase 1; 1.251423s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (101.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181624, over cnt = 36(0%), over = 36, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End initial routed; 1.058299s wall, 1.828125s user + 0.062500s system = 1.890625s CPU (178.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1807(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.184   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.377683s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 1.436065s wall, 2.203125s user + 0.062500s system = 2.265625s CPU (157.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181624, over cnt = 36(0%), over = 36, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014364s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181432, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029311s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (106.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181448, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.023382s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (66.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1807(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.184   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.362687s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.184270s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (101.8%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End phase 3; 0.735837s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (101.9%)

PHY-1003 : Routed, final wirelength = 181448
PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End export database. 0.009683s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.894844s wall, 7.609375s user + 0.125000s system = 7.734375s CPU (112.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6608, tnet num: 2041, tinst num: 847, tnode num: 8960, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.797160s wall, 8.546875s user + 0.125000s system = 8.671875s CPU (111.2%)

RUN-1004 : used memory is 526 MB, reserved memory is 497 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      837   out of  19600    4.27%
#reg                     1075   out of  19600    5.48%
#le                      1544
  #lut only               469   out of   1544   30.38%
  #reg only               707   out of   1544   45.79%
  #lut&reg                368   out of   1544   23.83%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         479
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1544   |611     |226     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1150   |314     |136     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |30      |9       |20      |0       |0       |
|    demodu                  |Demodulation                                     |543    |119     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |61      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |0       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |13      |0       |26      |0       |0       |
|    integ                   |Integration                                      |141    |18      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |88     |37      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |89      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |21      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |104    |90      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |30     |22      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |19     |15      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |53      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1453  
    #2          2       315   
    #3          3       109   
    #4          4        16   
    #5        5-10       77   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6608, tnet num: 2041, tinst num: 847, tnode num: 8960, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2041 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 847
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2043, pip num: 14770
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1338 valid insts, and 39296 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.172867s wall, 18.375000s user + 0.078125s system = 18.453125s CPU (581.6%)

RUN-1004 : used memory is 549 MB, reserved memory is 516 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_182001.log"
