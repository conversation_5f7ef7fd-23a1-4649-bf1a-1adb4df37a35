============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Oct 18 14:06:45 2023

   Run on =     TLH-022
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  3.898399s wall, 3.812500s user + 0.203125s system = 4.015625s CPU (103.0%)

RUN-1004 : used memory is 581 MB, reserved memory is 549 MB, peak memory is 583 MB
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file Src_al/DS18B20.v
HDL-1007 : analyze verilog file Src_al/Demodulation.v
HDL-1007 : analyze verilog file Src_al/Integration.v
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-1007 : analyze verilog file Src_al/Rs422Output.v
HDL-1007 : analyze verilog file Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file Src_al/UART_Control.v
HDL-1007 : analyze verilog file Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file Src_al/uart_tx.v
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
RUN-1001 : reset_run syn_1 phy_1.
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file Src_al/DS18B20.v
HDL-1007 : analyze verilog file Src_al/Demodulation.v
HDL-1007 : analyze verilog file Src_al/Integration.v
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-1007 : analyze verilog file Src_al/Rs422Output.v
HDL-1007 : analyze verilog file Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file Src_al/UART_Control.v
HDL-1007 : analyze verilog file Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file Src_al/uart_tx.v
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/Asys_fifo56X16.v
HDL-8007 ERROR: cannot find port 'dout' on this module in Src_al/Demodulation.v(148)
HDL-8007 ERROR: cannot find port 'valid' on this module in Src_al/Demodulation.v(149)
HDL-8007 ERROR: cannot find port 'afull' on this module in Src_al/Demodulation.v(152)
HDL-8007 ERROR: cannot find port 'aempty' on this module in Src_al/Demodulation.v(153)
HDL-8007 ERROR: cannot find port 'wrusedw' on this module in Src_al/Demodulation.v(154)
HDL-8007 ERROR: cannot find port 'rdusedw' on this module in Src_al/Demodulation.v(155)
HDL-8007 ERROR: cannot find port 'dout' on this module in Src_al/Demodulation.v(148)
HDL-8007 ERROR: cannot find port 'valid' on this module in Src_al/Demodulation.v(149)
HDL-8007 ERROR: cannot find port 'afull' on this module in Src_al/Demodulation.v(152)
HDL-8007 ERROR: cannot find port 'aempty' on this module in Src_al/Demodulation.v(153)
HDL-8007 ERROR: cannot find port 'wrusedw' on this module in Src_al/Demodulation.v(154)
HDL-8007 ERROR: cannot find port 'rdusedw' on this module in Src_al/Demodulation.v(155)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file Src_al/DS18B20.v
HDL-1007 : analyze verilog file Src_al/Demodulation.v
HDL-1007 : analyze verilog file Src_al/Integration.v
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-1007 : analyze verilog file Src_al/Rs422Output.v
HDL-1007 : analyze verilog file Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file Src_al/UART_Control.v
HDL-1007 : analyze verilog file Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file Src_al/uart_tx.v
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file Src_al/DS18B20.v
HDL-1007 : analyze verilog file Src_al/Demodulation.v
HDL-1007 : analyze verilog file Src_al/Integration.v
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-1007 : analyze verilog file Src_al/Rs422Output.v
HDL-1007 : analyze verilog file Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file Src_al/UART_Control.v
HDL-1007 : analyze verilog file Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file Src_al/uart_tx.v
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/Asys_fifo56X16.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
RUN-1001 : open_run syn_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/syn_1/IFOG501_2B_elaborate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P19'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8403 ERROR: P3 in Constraints/IFOG_11FB.adc should not be assigned as a user pin.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P18'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P17'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P60'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P59'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P57'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P61'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P62'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P63'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P55'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P52'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P51'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P50'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P49'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P48'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P47'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P45'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at 'PACKREG = ON'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P77'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'PULLTYPE = PULLUP'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P74'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = PULLUP'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P76'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P79'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P54'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = NONE'.
GUI-8402 ERROR: Error found at 'SLEWRATE = FAST'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P34'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'PULLTYPE = PULLUP'.
GUI-8402 ERROR: Error found at Find error at 'LOCATION = P87'.
GUI-8402 ERROR: Error found at 'IOSTANDARD = LVCMOS33'.
GUI-8402 ERROR: Error found at 'DRIVESTRENGTH = 8'.
GUI-8402 ERROR: Error found at 'PULLTYPE = PULLUP'.
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  4.925412s wall, 4.843750s user + 0.234375s system = 5.078125s CPU (103.1%)

RUN-1004 : used memory is 602 MB, reserved memory is 567 MB, peak memory is 603 MB
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.423692s wall, 1.359375s user + 0.109375s system = 1.468750s CPU (103.2%)

RUN-1004 : used memory is 612 MB, reserved memory is 580 MB, peak memory is 612 MB
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-6001 WARNING: syn_1: run failed.
RUN-1001 : open_run syn_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/syn_1/IFOG501_2B_elaborate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.307450s wall, 1.265625s user + 0.078125s system = 1.343750s CPU (102.8%)

RUN-1004 : used memory is 617 MB, reserved memory is 585 MB, peak memory is 617 MB
