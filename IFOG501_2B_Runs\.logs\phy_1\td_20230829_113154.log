============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Aug 29 11:31:55 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1659 instances
RUN-0007 : 392 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2229 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1672 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1657 instances, 392 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7901, tnet num: 2227, tinst num: 1657, tnode num: 11139, tedge num: 13333.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2227 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.287064s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (92.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 566988
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1657.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 516282, overlap = 18
PHY-3002 : Step(2): len = 428773, overlap = 20.25
PHY-3002 : Step(3): len = 365781, overlap = 15.75
PHY-3002 : Step(4): len = 348333, overlap = 11.25
PHY-3002 : Step(5): len = 340321, overlap = 13.5
PHY-3002 : Step(6): len = 329662, overlap = 15.75
PHY-3002 : Step(7): len = 320383, overlap = 15.75
PHY-3002 : Step(8): len = 314005, overlap = 15.75
PHY-3002 : Step(9): len = 307446, overlap = 18
PHY-3002 : Step(10): len = 299650, overlap = 18
PHY-3002 : Step(11): len = 293356, overlap = 18
PHY-3002 : Step(12): len = 287654, overlap = 18
PHY-3002 : Step(13): len = 281924, overlap = 20.25
PHY-3002 : Step(14): len = 276970, overlap = 20.25
PHY-3002 : Step(15): len = 271319, overlap = 20.25
PHY-3002 : Step(16): len = 264788, overlap = 20.25
PHY-3002 : Step(17): len = 259763, overlap = 20.25
PHY-3002 : Step(18): len = 255468, overlap = 20.25
PHY-3002 : Step(19): len = 250297, overlap = 20.25
PHY-3002 : Step(20): len = 244360, overlap = 20.25
PHY-3002 : Step(21): len = 239631, overlap = 20.25
PHY-3002 : Step(22): len = 234753, overlap = 20.25
PHY-3002 : Step(23): len = 230068, overlap = 20.25
PHY-3002 : Step(24): len = 225288, overlap = 20.25
PHY-3002 : Step(25): len = 220132, overlap = 20.25
PHY-3002 : Step(26): len = 215778, overlap = 20.25
PHY-3002 : Step(27): len = 211298, overlap = 20.25
PHY-3002 : Step(28): len = 205841, overlap = 20.25
PHY-3002 : Step(29): len = 201939, overlap = 20.25
PHY-3002 : Step(30): len = 195467, overlap = 20.25
PHY-3002 : Step(31): len = 191381, overlap = 20.25
PHY-3002 : Step(32): len = 187484, overlap = 20.25
PHY-3002 : Step(33): len = 182584, overlap = 20.25
PHY-3002 : Step(34): len = 175998, overlap = 20.25
PHY-3002 : Step(35): len = 172842, overlap = 20.25
PHY-3002 : Step(36): len = 168690, overlap = 20.25
PHY-3002 : Step(37): len = 144576, overlap = 20.25
PHY-3002 : Step(38): len = 136615, overlap = 20.5
PHY-3002 : Step(39): len = 135315, overlap = 18.3438
PHY-3002 : Step(40): len = 122881, overlap = 18.9062
PHY-3002 : Step(41): len = 107088, overlap = 16.7812
PHY-3002 : Step(42): len = 105192, overlap = 19.0312
PHY-3002 : Step(43): len = 102868, overlap = 19.0312
PHY-3002 : Step(44): len = 101229, overlap = 14.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.99822e-05
PHY-3002 : Step(45): len = 102170, overlap = 14.5312
PHY-3002 : Step(46): len = 101672, overlap = 10.0312
PHY-3002 : Step(47): len = 100763, overlap = 12.2812
PHY-3002 : Step(48): len = 100094, overlap = 10.0312
PHY-3002 : Step(49): len = 98520.4, overlap = 10.0312
PHY-3002 : Step(50): len = 97392.5, overlap = 12.25
PHY-3002 : Step(51): len = 96463.7, overlap = 7.75
PHY-3002 : Step(52): len = 92304.5, overlap = 12.25
PHY-3002 : Step(53): len = 91103.8, overlap = 12.25
PHY-3002 : Step(54): len = 89904.2, overlap = 12.25
PHY-3002 : Step(55): len = 88883.4, overlap = 12.25
PHY-3002 : Step(56): len = 84761.7, overlap = 7.75
PHY-3002 : Step(57): len = 83254.2, overlap = 10
PHY-3002 : Step(58): len = 81889.5, overlap = 10
PHY-3002 : Step(59): len = 80544.8, overlap = 12.25
PHY-3002 : Step(60): len = 79319.6, overlap = 10
PHY-3002 : Step(61): len = 79107, overlap = 10
PHY-3002 : Step(62): len = 78748.2, overlap = 7.75
PHY-3002 : Step(63): len = 77739.7, overlap = 7.75
PHY-3002 : Step(64): len = 76662.7, overlap = 12.25
PHY-3002 : Step(65): len = 74844, overlap = 12.25
PHY-3002 : Step(66): len = 74445.1, overlap = 7.75
PHY-3002 : Step(67): len = 74079.9, overlap = 5.5
PHY-3002 : Step(68): len = 72267.2, overlap = 12.25
PHY-3002 : Step(69): len = 70053.5, overlap = 12.25
PHY-3002 : Step(70): len = 70445.5, overlap = 12.25
PHY-3002 : Step(71): len = 70388.3, overlap = 7.75
PHY-3002 : Step(72): len = 70164.7, overlap = 7.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000179964
PHY-3002 : Step(73): len = 70148.2, overlap = 10
PHY-3002 : Step(74): len = 70075.4, overlap = 12.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000359929
PHY-3002 : Step(75): len = 70026.2, overlap = 7.75
PHY-3002 : Step(76): len = 70049.1, overlap = 5.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008042s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (194.3%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2227 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.084790s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (110.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(77): len = 73017.7, overlap = 8.0625
PHY-3002 : Step(78): len = 72007.1, overlap = 8.5625
PHY-3002 : Step(79): len = 70799.8, overlap = 8.09375
PHY-3002 : Step(80): len = 68257, overlap = 8.28125
PHY-3002 : Step(81): len = 66306.3, overlap = 7.4375
PHY-3002 : Step(82): len = 64766.4, overlap = 7.375
PHY-3002 : Step(83): len = 62523.6, overlap = 7.3125
PHY-3002 : Step(84): len = 61165.8, overlap = 6.6875
PHY-3002 : Step(85): len = 59427.6, overlap = 7.28125
PHY-3002 : Step(86): len = 56873.9, overlap = 9.9375
PHY-3002 : Step(87): len = 55245.6, overlap = 7.375
PHY-3002 : Step(88): len = 54745.1, overlap = 7.25
PHY-3002 : Step(89): len = 53899.8, overlap = 6.1875
PHY-3002 : Step(90): len = 53277.4, overlap = 6.1875
PHY-3002 : Step(91): len = 51870.6, overlap = 6.1875
PHY-3002 : Step(92): len = 51532, overlap = 9.15625
PHY-3002 : Step(93): len = 50846.3, overlap = 9.46875
PHY-3002 : Step(94): len = 50318.3, overlap = 9.0625
PHY-3002 : Step(95): len = 49618.4, overlap = 9.75
PHY-3002 : Step(96): len = 49316.7, overlap = 10.0625
PHY-3002 : Step(97): len = 48686.3, overlap = 14.0938
PHY-3002 : Step(98): len = 48015.9, overlap = 15.9375
PHY-3002 : Step(99): len = 47468.1, overlap = 19.2188
PHY-3002 : Step(100): len = 47096.4, overlap = 21.7812
PHY-3002 : Step(101): len = 46822.4, overlap = 22.0938
PHY-3002 : Step(102): len = 46409, overlap = 22.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000178924
PHY-3002 : Step(103): len = 46302.7, overlap = 22.8438
PHY-3002 : Step(104): len = 46268.7, overlap = 22.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000357849
PHY-3002 : Step(105): len = 46554.7, overlap = 20.0938
PHY-3002 : Step(106): len = 46554.7, overlap = 20.0938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2227 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.116292s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (94.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.97247e-05
PHY-3002 : Step(107): len = 46828.6, overlap = 66.7812
PHY-3002 : Step(108): len = 47837.9, overlap = 65.4375
PHY-3002 : Step(109): len = 49075.5, overlap = 61.9375
PHY-3002 : Step(110): len = 48751.8, overlap = 61.625
PHY-3002 : Step(111): len = 48675.9, overlap = 61.3438
PHY-3002 : Step(112): len = 48720.7, overlap = 59.5312
PHY-3002 : Step(113): len = 48569.7, overlap = 59.5312
PHY-3002 : Step(114): len = 48498.9, overlap = 59.375
PHY-3002 : Step(115): len = 48566.5, overlap = 59.1875
PHY-3002 : Step(116): len = 48649.6, overlap = 53.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000139449
PHY-3002 : Step(117): len = 49075.6, overlap = 52.6875
PHY-3002 : Step(118): len = 49520.2, overlap = 52.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000278899
PHY-3002 : Step(119): len = 49665.7, overlap = 52.2188
PHY-3002 : Step(120): len = 51151.4, overlap = 47.0625
PHY-3002 : Step(121): len = 53025.7, overlap = 42.625
PHY-3002 : Step(122): len = 53709.2, overlap = 39.0625
PHY-3002 : Step(123): len = 53774.6, overlap = 37.4062
PHY-3002 : Step(124): len = 53213.7, overlap = 33.75
PHY-3002 : Step(125): len = 52646.7, overlap = 32.9375
PHY-3002 : Step(126): len = 52410.2, overlap = 33.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7901, tnet num: 2227, tinst num: 1657, tnode num: 11139, tedge num: 13333.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.12 peak overflow 2.97
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2229.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55392, over cnt = 224(0%), over = 927, worst = 21
PHY-1001 : End global iterations;  0.114506s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (95.5%)

PHY-1001 : Congestion index: top1 = 43.99, top5 = 24.32, top10 = 15.87, top15 = 11.33.
PHY-1001 : End incremental global routing;  0.173526s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (99.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2227 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.070415s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.9%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1617 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 1662 instances, 395 luts, 994 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 52755.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7918, tnet num: 2232, tinst num: 1662, tnode num: 11162, tedge num: 13357.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2232 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.380362s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (98.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(127): len = 52798.6, overlap = 1.71875
PHY-3002 : Step(128): len = 52811.6, overlap = 1.625
PHY-3002 : Step(129): len = 52781.9, overlap = 1.5
PHY-3002 : Step(130): len = 52783.6, overlap = 1.5
PHY-3002 : Step(131): len = 52783.6, overlap = 1.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2232 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.078019s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00366154
PHY-3002 : Step(132): len = 52811.1, overlap = 33.1875
PHY-3002 : Step(133): len = 52803.1, overlap = 33.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00732308
PHY-3002 : Step(134): len = 52831.3, overlap = 33.1875
PHY-3002 : Step(135): len = 52831.3, overlap = 33.1875
PHY-3001 : Final: Len = 52831.3, Over = 33.1875
PHY-3001 : End incremental placement;  0.580681s wall, 0.640625s user + 0.156250s system = 0.796875s CPU (137.2%)

OPT-1001 : Total overflow 89.19 peak overflow 2.97
OPT-1001 : End high-fanout net optimization;  0.861349s wall, 0.906250s user + 0.171875s system = 1.078125s CPU (125.2%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1629/2234.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55824, over cnt = 224(0%), over = 914, worst = 21
PHY-1002 : len = 62216, over cnt = 163(0%), over = 344, worst = 11
PHY-1002 : len = 65232, over cnt = 40(0%), over = 65, worst = 11
PHY-1002 : len = 66192, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 66608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.147418s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (127.2%)

PHY-1001 : Congestion index: top1 = 38.53, top5 = 24.94, top10 = 17.63, top15 = 13.06.
OPT-1001 : End congestion update;  0.204952s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (122.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2232 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062935s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.274646s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (113.8%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 223.
OPT-1001 : End physical optimization;  1.487521s wall, 1.562500s user + 0.171875s system = 1.734375s CPU (116.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 395 LUT to BLE ...
SYN-4008 : Packed 395 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 77 SEQ with LUT/SLICE
SYN-4006 : 149 single LUT's are left
SYN-4006 : 728 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1123/1456 primitive instances ...
PHY-3001 : End packing;  0.080241s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (116.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 867 instances
RUN-1001 : 409 mslices, 409 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2060 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1509 nets have 2 pins
RUN-1001 : 425 nets have [3 - 5] pins
RUN-1001 : 81 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 865 instances, 818 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52941.6, Over = 63
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6674, tnet num: 2058, tinst num: 865, tnode num: 9013, tedge num: 11705.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.401600s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (97.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 1.98365e-05
PHY-3002 : Step(136): len = 52349.2, overlap = 64.75
PHY-3002 : Step(137): len = 51918, overlap = 64.25
PHY-3002 : Step(138): len = 51635, overlap = 68
PHY-3002 : Step(139): len = 51294.8, overlap = 66.5
PHY-3002 : Step(140): len = 51123.9, overlap = 66.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.9673e-05
PHY-3002 : Step(141): len = 51313.3, overlap = 66.75
PHY-3002 : Step(142): len = 52010.4, overlap = 66.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.93461e-05
PHY-3002 : Step(143): len = 52707.8, overlap = 64.5
PHY-3002 : Step(144): len = 54571.1, overlap = 61.25
PHY-3002 : Step(145): len = 54949.5, overlap = 61
PHY-3002 : Step(146): len = 54792.5, overlap = 61.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.085141s wall, 0.062500s user + 0.125000s system = 0.187500s CPU (220.2%)

PHY-3001 : Trial Legalized: Len = 67284.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055481s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000368517
PHY-3002 : Step(147): len = 64570.2, overlap = 8.75
PHY-3002 : Step(148): len = 62695.5, overlap = 17.25
PHY-3002 : Step(149): len = 61523.6, overlap = 23.25
PHY-3002 : Step(150): len = 60946.1, overlap = 27
PHY-3002 : Step(151): len = 60601.7, overlap = 29.25
PHY-3002 : Step(152): len = 60319, overlap = 29.5
PHY-3002 : Step(153): len = 60011.6, overlap = 29
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000737033
PHY-3002 : Step(154): len = 60562.7, overlap = 29
PHY-3002 : Step(155): len = 60804.5, overlap = 29
PHY-3002 : Step(156): len = 60899.9, overlap = 28.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00147407
PHY-3002 : Step(157): len = 61128.2, overlap = 28
PHY-3002 : Step(158): len = 61302.4, overlap = 27.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007759s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (201.4%)

PHY-3001 : Legalized: Len = 65942.2, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005991s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (260.8%)

PHY-3001 : 11 instances has been re-located, deltaX = 3, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 66072.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6674, tnet num: 2058, tinst num: 865, tnode num: 9013, tedge num: 11705.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 77/2060.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72984, over cnt = 156(0%), over = 238, worst = 7
PHY-1002 : len = 74080, over cnt = 62(0%), over = 79, worst = 4
PHY-1002 : len = 74728, over cnt = 24(0%), over = 31, worst = 3
PHY-1002 : len = 75096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.176067s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (133.1%)

PHY-1001 : Congestion index: top1 = 32.13, top5 = 23.41, top10 = 18.60, top15 = 14.71.
PHY-1001 : End incremental global routing;  0.254145s wall, 0.281250s user + 0.031250s system = 0.312500s CPU (123.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.092985s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.388823s wall, 0.421875s user + 0.031250s system = 0.453125s CPU (116.5%)

OPT-1001 : Current memory(MB): used = 222, reserve = 188, peak = 223.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1825/2060.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 75096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010854s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (144.0%)

PHY-1001 : Congestion index: top1 = 32.13, top5 = 23.41, top10 = 18.60, top15 = 14.71.
OPT-1001 : End congestion update;  0.084252s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072330s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (108.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 827 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 865 instances, 818 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 66110.6, Over = 0
PHY-3001 : End spreading;  0.006805s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (229.6%)

PHY-3001 : Final: Len = 66110.6, Over = 0
PHY-3001 : End incremental legalization;  0.043632s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (107.4%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.217435s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (100.6%)

OPT-1001 : Current memory(MB): used = 228, reserve = 193, peak = 228.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065763s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1819/2060.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 75176, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 75176, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 75192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.035737s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (131.2%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 23.41, top10 = 18.62, top15 = 14.72.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061286s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.517241
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.187792s wall, 1.218750s user + 0.031250s system = 1.250000s CPU (105.2%)

RUN-1003 : finish command "place" in  6.772929s wall, 8.812500s user + 3.359375s system = 12.171875s CPU (179.7%)

RUN-1004 : used memory is 205 MB, reserved memory is 169 MB, peak memory is 228 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 867 instances
RUN-1001 : 409 mslices, 409 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2060 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1509 nets have 2 pins
RUN-1001 : 425 nets have [3 - 5] pins
RUN-1001 : 81 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6674, tnet num: 2058, tinst num: 865, tnode num: 9013, tedge num: 11705.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 409 mslices, 409 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72328, over cnt = 166(0%), over = 256, worst = 7
PHY-1002 : len = 73176, over cnt = 102(0%), over = 136, worst = 4
PHY-1002 : len = 74376, over cnt = 14(0%), over = 18, worst = 3
PHY-1002 : len = 74672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.112212s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (181.0%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.37, top10 = 18.55, top15 = 14.62.
PHY-1001 : End global routing;  0.165994s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (160.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 244, reserve = 210, peak = 250.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 505, reserve = 472, peak = 505.
PHY-1001 : End build detailed router design. 3.254508s wall, 3.187500s user + 0.078125s system = 3.265625s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34576, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.352312s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 536, reserve = 506, peak = 536.
PHY-1001 : End phase 1; 1.357997s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 187096, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 537, reserve = 506, peak = 538.
PHY-1001 : End initial routed; 1.133224s wall, 2.250000s user + 0.062500s system = 2.312500s CPU (204.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1823(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.126   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.369660s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 540, reserve = 509, peak = 540.
PHY-1001 : End phase 2; 1.502995s wall, 2.625000s user + 0.062500s system = 2.687500s CPU (178.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 187096, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016361s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (95.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 187024, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027503s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 187120, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022941s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (68.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1823(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.126   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.375314s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.186060s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 555, reserve = 524, peak = 555.
PHY-1001 : End phase 3; 0.753809s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (101.6%)

PHY-1003 : Routed, final wirelength = 187120
PHY-1001 : Current memory(MB): used = 556, reserve = 525, peak = 556.
PHY-1001 : End export database. 0.010214s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.062914s wall, 8.093750s user + 0.171875s system = 8.265625s CPU (117.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6674, tnet num: 2058, tinst num: 865, tnode num: 9013, tedge num: 11705.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.042636s wall, 9.109375s user + 0.234375s system = 9.343750s CPU (116.2%)

RUN-1004 : used memory is 509 MB, reserved memory is 482 MB, peak memory is 556 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      853   out of  19600    4.35%
#reg                     1076   out of  19600    5.49%
#le                      1581
  #lut only               505   out of   1581   31.94%
  #reg only               728   out of   1581   46.05%
  #lut&reg                348   out of   1581   22.01%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         476
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    38
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1581   |627     |226     |1107    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1160   |309     |133     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |33      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |542    |119     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |0       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |138    |15      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |93     |22      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |94      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |125    |108     |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |29      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |24     |20      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |65     |59      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1473  
    #2          2       289   
    #3          3       120   
    #4          4        16   
    #5        5-10       87   
    #6        11-50      30   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6674, tnet num: 2058, tinst num: 865, tnode num: 9013, tedge num: 11705.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2058 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 865
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2060, pip num: 15014
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1300 valid insts, and 39861 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.140939s wall, 18.453125s user + 0.062500s system = 18.515625s CPU (589.5%)

RUN-1004 : used memory is 526 MB, reserved memory is 494 MB, peak memory is 675 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230829_113154.log"
