============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 18:41:11 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1632 instances
RUN-0007 : 368 luts, 1009 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2172 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1615 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1630 instances, 368 luts, 1009 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7752, tnet num: 2170, tinst num: 1630, tnode num: 11014, tedge num: 13112.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.287407s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (97.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 635205
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1630.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 531175, overlap = 20.25
PHY-3002 : Step(2): len = 491506, overlap = 20.25
PHY-3002 : Step(3): len = 404164, overlap = 18
PHY-3002 : Step(4): len = 338944, overlap = 13.5
PHY-3002 : Step(5): len = 306649, overlap = 15.75
PHY-3002 : Step(6): len = 292493, overlap = 18
PHY-3002 : Step(7): len = 283653, overlap = 20.25
PHY-3002 : Step(8): len = 275525, overlap = 20.25
PHY-3002 : Step(9): len = 270071, overlap = 20.25
PHY-3002 : Step(10): len = 258526, overlap = 20.25
PHY-3002 : Step(11): len = 251553, overlap = 20.25
PHY-3002 : Step(12): len = 246880, overlap = 20.25
PHY-3002 : Step(13): len = 240509, overlap = 20.25
PHY-3002 : Step(14): len = 226959, overlap = 20.25
PHY-3002 : Step(15): len = 222996, overlap = 20.25
PHY-3002 : Step(16): len = 218885, overlap = 20.25
PHY-3002 : Step(17): len = 207652, overlap = 20.25
PHY-3002 : Step(18): len = 202700, overlap = 20.25
PHY-3002 : Step(19): len = 200080, overlap = 20.25
PHY-3002 : Step(20): len = 191258, overlap = 20.25
PHY-3002 : Step(21): len = 183513, overlap = 20.25
PHY-3002 : Step(22): len = 180938, overlap = 20.25
PHY-3002 : Step(23): len = 177326, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000129903
PHY-3002 : Step(24): len = 179484, overlap = 15.75
PHY-3002 : Step(25): len = 177454, overlap = 11.25
PHY-3002 : Step(26): len = 174911, overlap = 15.75
PHY-3002 : Step(27): len = 168754, overlap = 11.25
PHY-3002 : Step(28): len = 163076, overlap = 13.5
PHY-3002 : Step(29): len = 160895, overlap = 15.75
PHY-3002 : Step(30): len = 157885, overlap = 11.25
PHY-3002 : Step(31): len = 154040, overlap = 6.75
PHY-3002 : Step(32): len = 150167, overlap = 13.5
PHY-3002 : Step(33): len = 148340, overlap = 11.25
PHY-3002 : Step(34): len = 145794, overlap = 11.25
PHY-3002 : Step(35): len = 142066, overlap = 11.25
PHY-3002 : Step(36): len = 138864, overlap = 11.25
PHY-3002 : Step(37): len = 133997, overlap = 9
PHY-3002 : Step(38): len = 132677, overlap = 6.75
PHY-3002 : Step(39): len = 129251, overlap = 9
PHY-3002 : Step(40): len = 126918, overlap = 11.25
PHY-3002 : Step(41): len = 123769, overlap = 9
PHY-3002 : Step(42): len = 122861, overlap = 11.25
PHY-3002 : Step(43): len = 118189, overlap = 9
PHY-3002 : Step(44): len = 113857, overlap = 6.75
PHY-3002 : Step(45): len = 111677, overlap = 6.75
PHY-3002 : Step(46): len = 111109, overlap = 9
PHY-3002 : Step(47): len = 104515, overlap = 9.75
PHY-3002 : Step(48): len = 99727.3, overlap = 10.375
PHY-3002 : Step(49): len = 98610.4, overlap = 9.5
PHY-3002 : Step(50): len = 97371.2, overlap = 9.5
PHY-3002 : Step(51): len = 96422, overlap = 9.5
PHY-3002 : Step(52): len = 96042, overlap = 7
PHY-3002 : Step(53): len = 94182.6, overlap = 9
PHY-3002 : Step(54): len = 92433.7, overlap = 6.75
PHY-3002 : Step(55): len = 91362.4, overlap = 11.25
PHY-3002 : Step(56): len = 89235.1, overlap = 6.75
PHY-3002 : Step(57): len = 85533.4, overlap = 9
PHY-3002 : Step(58): len = 83391.4, overlap = 6.8125
PHY-3002 : Step(59): len = 82363.3, overlap = 6.875
PHY-3002 : Step(60): len = 80928.4, overlap = 9.3125
PHY-3002 : Step(61): len = 80751.1, overlap = 9.3125
PHY-3002 : Step(62): len = 80852.6, overlap = 9.3125
PHY-3002 : Step(63): len = 80648.7, overlap = 9.3125
PHY-3002 : Step(64): len = 79549, overlap = 7.0625
PHY-3002 : Step(65): len = 78083.4, overlap = 9.3125
PHY-3002 : Step(66): len = 77832.4, overlap = 11.5625
PHY-3002 : Step(67): len = 77199.8, overlap = 9.3125
PHY-3002 : Step(68): len = 76377.2, overlap = 11.625
PHY-3002 : Step(69): len = 74552.7, overlap = 7.5625
PHY-3002 : Step(70): len = 72770.3, overlap = 7.6875
PHY-3002 : Step(71): len = 72489.1, overlap = 10.125
PHY-3002 : Step(72): len = 71754.6, overlap = 10.125
PHY-3002 : Step(73): len = 70765.7, overlap = 9.9375
PHY-3002 : Step(74): len = 68764.2, overlap = 9.9375
PHY-3002 : Step(75): len = 66893.3, overlap = 12
PHY-3002 : Step(76): len = 65809, overlap = 12
PHY-3002 : Step(77): len = 65530.5, overlap = 11.8125
PHY-3002 : Step(78): len = 64973.5, overlap = 14.25
PHY-3002 : Step(79): len = 64190.8, overlap = 11.5625
PHY-3002 : Step(80): len = 63916.3, overlap = 11.3125
PHY-3002 : Step(81): len = 63158.8, overlap = 11.3125
PHY-3002 : Step(82): len = 60716.9, overlap = 9.0625
PHY-3002 : Step(83): len = 59089.7, overlap = 9.0625
PHY-3002 : Step(84): len = 59241.3, overlap = 9.0625
PHY-3002 : Step(85): len = 59055.8, overlap = 9.3125
PHY-3002 : Step(86): len = 58565.4, overlap = 13.625
PHY-3002 : Step(87): len = 57950.6, overlap = 13.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000259805
PHY-3002 : Step(88): len = 57916.8, overlap = 13.8125
PHY-3002 : Step(89): len = 58023.7, overlap = 13.8125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00051961
PHY-3002 : Step(90): len = 57967.6, overlap = 11.625
PHY-3002 : Step(91): len = 58021, overlap = 11.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006289s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061372s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00179064
PHY-3002 : Step(92): len = 61438.5, overlap = 2.9375
PHY-3002 : Step(93): len = 60290.4, overlap = 2.9375
PHY-3002 : Step(94): len = 59902.4, overlap = 3.0625
PHY-3002 : Step(95): len = 59033.8, overlap = 2.6875
PHY-3002 : Step(96): len = 58080.3, overlap = 3.25
PHY-3002 : Step(97): len = 57069.5, overlap = 3.625
PHY-3002 : Step(98): len = 56038.7, overlap = 3.8125
PHY-3002 : Step(99): len = 55182.5, overlap = 4
PHY-3002 : Step(100): len = 53862.4, overlap = 7.875
PHY-3002 : Step(101): len = 52804.3, overlap = 8.75
PHY-3002 : Step(102): len = 52321.2, overlap = 8.625
PHY-3002 : Step(103): len = 51683.7, overlap = 8.6875
PHY-3002 : Step(104): len = 51176.3, overlap = 8.4375
PHY-3002 : Step(105): len = 50452.2, overlap = 10.375
PHY-3002 : Step(106): len = 50274.8, overlap = 10.6875
PHY-3002 : Step(107): len = 50058.1, overlap = 10.8125
PHY-3002 : Step(108): len = 49346.7, overlap = 11.0625
PHY-3002 : Step(109): len = 48546.9, overlap = 11.6875
PHY-3002 : Step(110): len = 47601.8, overlap = 11.0312
PHY-3002 : Step(111): len = 47279.4, overlap = 12.1562
PHY-3002 : Step(112): len = 47306.4, overlap = 13.3438
PHY-3002 : Step(113): len = 46998.9, overlap = 14.6562
PHY-3002 : Step(114): len = 46581.4, overlap = 14.8438
PHY-3002 : Step(115): len = 46581.4, overlap = 14.8438
PHY-3002 : Step(116): len = 46411.2, overlap = 14.7188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063751s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.6944e-05
PHY-3002 : Step(117): len = 46466.1, overlap = 67.4062
PHY-3002 : Step(118): len = 46710.5, overlap = 64.4688
PHY-3002 : Step(119): len = 47267.6, overlap = 63.6562
PHY-3002 : Step(120): len = 47717.4, overlap = 52.75
PHY-3002 : Step(121): len = 47766, overlap = 49.75
PHY-3002 : Step(122): len = 47847.4, overlap = 50.0312
PHY-3002 : Step(123): len = 47938.7, overlap = 49.75
PHY-3002 : Step(124): len = 47777.8, overlap = 50.3438
PHY-3002 : Step(125): len = 47851.7, overlap = 49.6875
PHY-3002 : Step(126): len = 47918.5, overlap = 49.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000153888
PHY-3002 : Step(127): len = 47901.2, overlap = 49.9375
PHY-3002 : Step(128): len = 47949, overlap = 50.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000307776
PHY-3002 : Step(129): len = 48514.8, overlap = 46.1562
PHY-3002 : Step(130): len = 49026.2, overlap = 42.75
PHY-3002 : Step(131): len = 50342.4, overlap = 37.5312
PHY-3002 : Step(132): len = 49994.1, overlap = 37.4375
PHY-3002 : Step(133): len = 49482.2, overlap = 37.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7752, tnet num: 2170, tinst num: 1630, tnode num: 11014, tedge num: 13112.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.97 peak overflow 3.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2172.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52640, over cnt = 256(0%), over = 1098, worst = 21
PHY-1001 : End global iterations;  0.063542s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (172.1%)

PHY-1001 : Congestion index: top1 = 43.17, top5 = 25.15, top10 = 15.85, top15 = 11.16.
PHY-1001 : End incremental global routing;  0.114245s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (150.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067123s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.212037s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (125.3%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1645/2172.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52640, over cnt = 256(0%), over = 1098, worst = 21
PHY-1002 : len = 58816, over cnt = 196(0%), over = 503, worst = 17
PHY-1002 : len = 63800, over cnt = 53(0%), over = 72, worst = 8
PHY-1002 : len = 64680, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 65208, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.099835s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (109.6%)

PHY-1001 : Congestion index: top1 = 38.73, top5 = 25.43, top10 = 17.98, top15 = 13.15.
OPT-1001 : End congestion update;  0.144125s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (108.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060105s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.206859s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (105.7%)

OPT-1001 : Current memory(MB): used = 216, reserve = 180, peak = 216.
OPT-1001 : End physical optimization;  0.704918s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (108.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 108 SEQ with LUT/SLICE
SYN-4006 : 97 single LUT's are left
SYN-4006 : 719 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1087/1400 primitive instances ...
PHY-3001 : End packing;  0.050776s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 829 instances
RUN-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2004 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1454 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 827 instances, 780 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49859, Over = 64.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6525, tnet num: 2002, tinst num: 827, tnode num: 8878, tedge num: 11484.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.321962s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.31542e-05
PHY-3002 : Step(134): len = 49550.4, overlap = 67
PHY-3002 : Step(135): len = 49426.1, overlap = 66.75
PHY-3002 : Step(136): len = 48880.2, overlap = 66.75
PHY-3002 : Step(137): len = 48726.9, overlap = 68
PHY-3002 : Step(138): len = 48844, overlap = 68
PHY-3002 : Step(139): len = 49108.5, overlap = 67.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.63084e-05
PHY-3002 : Step(140): len = 49104.7, overlap = 66.25
PHY-3002 : Step(141): len = 49242, overlap = 66.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.6723e-05
PHY-3002 : Step(142): len = 49633.8, overlap = 65
PHY-3002 : Step(143): len = 51029.4, overlap = 56.75
PHY-3002 : Step(144): len = 52460.3, overlap = 45.75
PHY-3002 : Step(145): len = 52287.6, overlap = 45.25
PHY-3002 : Step(146): len = 52248, overlap = 44.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.065514s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (214.6%)

PHY-3001 : Trial Legalized: Len = 65089.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052275s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000491314
PHY-3002 : Step(147): len = 63078.3, overlap = 3
PHY-3002 : Step(148): len = 61108.2, overlap = 10.25
PHY-3002 : Step(149): len = 59784.9, overlap = 15.5
PHY-3002 : Step(150): len = 58596, overlap = 18.5
PHY-3002 : Step(151): len = 57800.1, overlap = 21
PHY-3002 : Step(152): len = 57270.7, overlap = 24.25
PHY-3002 : Step(153): len = 56999.2, overlap = 25.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000982627
PHY-3002 : Step(154): len = 57409.3, overlap = 25
PHY-3002 : Step(155): len = 57512.9, overlap = 25.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00196525
PHY-3002 : Step(156): len = 57643.8, overlap = 24
PHY-3002 : Step(157): len = 57756.3, overlap = 23
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004937s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62132, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005427s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 7 instances has been re-located, deltaX = 0, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 62158, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6525, tnet num: 2002, tinst num: 827, tnode num: 8878, tedge num: 11484.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 53/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68200, over cnt = 147(0%), over = 228, worst = 7
PHY-1002 : len = 69032, over cnt = 102(0%), over = 130, worst = 4
PHY-1002 : len = 70272, over cnt = 26(0%), over = 28, worst = 2
PHY-1002 : len = 70752, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.135325s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (161.6%)

PHY-1001 : Congestion index: top1 = 31.23, top5 = 22.58, top10 = 17.85, top15 = 14.06.
PHY-1001 : End incremental global routing;  0.184302s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (144.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059934s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.275023s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (130.7%)

OPT-1001 : Current memory(MB): used = 221, reserve = 184, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1775/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005806s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.23, top5 = 22.58, top10 = 17.85, top15 = 14.06.
OPT-1001 : End congestion update;  0.051163s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051790s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.104645s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (104.5%)

OPT-1001 : Current memory(MB): used = 223, reserve = 186, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050256s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1775/2004.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70800, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006886s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (226.9%)

PHY-1001 : Congestion index: top1 = 31.23, top5 = 22.58, top10 = 17.85, top15 = 14.06.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050310s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.839373s wall, 0.953125s user + 0.031250s system = 0.984375s CPU (117.3%)

RUN-1003 : finish command "place" in  5.298428s wall, 8.484375s user + 2.828125s system = 11.312500s CPU (213.5%)

RUN-1004 : used memory is 201 MB, reserved memory is 164 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 829 instances
RUN-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2004 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1454 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6525, tnet num: 2002, tinst num: 827, tnode num: 8878, tedge num: 11484.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67936, over cnt = 143(0%), over = 224, worst = 7
PHY-1002 : len = 68792, over cnt = 101(0%), over = 129, worst = 5
PHY-1002 : len = 70160, over cnt = 18(0%), over = 20, worst = 2
PHY-1002 : len = 70504, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123479s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (139.2%)

PHY-1001 : Congestion index: top1 = 31.14, top5 = 22.45, top10 = 17.73, top15 = 13.98.
PHY-1001 : End global routing;  0.173298s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (126.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 205, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 468, peak = 500.
PHY-1001 : End build detailed router design. 3.417868s wall, 3.296875s user + 0.062500s system = 3.359375s CPU (98.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34672, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.290089s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.296784s wall, 1.296875s user + 0.000000s system = 1.296875s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181504, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End initial routed; 1.002706s wall, 1.937500s user + 0.062500s system = 2.000000s CPU (199.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1785(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.166   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.378775s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End phase 2; 1.381584s wall, 2.312500s user + 0.062500s system = 2.375000s CPU (171.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181504, over cnt = 21(0%), over = 21, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014638s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (106.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181304, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.023932s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181320, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019729s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (79.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1785(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.166   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.373135s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.183884s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (102.0%)

PHY-1001 : Current memory(MB): used = 551, reserve = 518, peak = 551.
PHY-1001 : End phase 3; 0.728897s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (100.8%)

PHY-1003 : Routed, final wirelength = 181320
PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End export database. 0.009950s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.017047s wall, 7.828125s user + 0.125000s system = 7.953125s CPU (113.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6525, tnet num: 2002, tinst num: 827, tnode num: 8878, tedge num: 11484.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.922278s wall, 8.765625s user + 0.140625s system = 8.906250s CPU (112.4%)

RUN-1004 : used memory is 504 MB, reserved memory is 471 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      788   out of  19600    4.02%
#reg                     1074   out of  19600    5.48%
#le                      1507
  #lut only               433   out of   1507   28.73%
  #reg only               719   out of   1507   47.71%
  #lut&reg                355   out of   1507   23.56%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         471
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    45
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1507   |582     |206     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1124   |290     |122     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |29     |23      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |524    |115     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |53     |0       |0       |53      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |12      |0       |26      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |16      |0       |30      |0       |0       |
|    integ                   |Integration                                      |138    |28      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |98     |22      |15      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |85      |29      |256     |0       |4       |
|    trans                   |SquareWaveGenerator                              |22     |17      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |106    |92      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |23     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |46     |46      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1418  
    #2          2       308   
    #3          3       109   
    #4          4        17   
    #5        5-10       79   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6525, tnet num: 2002, tinst num: 827, tnode num: 8878, tedge num: 11484.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2002 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 827
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2004, pip num: 14735
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1283 valid insts, and 38714 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.249716s wall, 18.468750s user + 0.078125s system = 18.546875s CPU (570.7%)

RUN-1004 : used memory is 520 MB, reserved memory is 489 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_184111.log"
