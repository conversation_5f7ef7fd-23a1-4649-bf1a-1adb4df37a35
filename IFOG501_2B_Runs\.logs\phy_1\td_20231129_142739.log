============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 14:27:39 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/DA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 15 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0001010001101011
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=15,BUS_DIN_NUM=26,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb011,32'sb0100,32'sb0101,32'sb0110,32'sb0111,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb010010,32'sb011000,32'sb011110,32'sb0100100,32'sb0101010,32'sb0110000,32'sb0110110,32'sb0111100,32'sb01000010,32'sb01001000,32'sb01001110,32'sb01010100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=134) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=134) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=15,BUS_DIN_NUM=26,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb011,32'sb0100,32'sb0101,32'sb0110,32'sb0111,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb010010,32'sb011000,32'sb011110,32'sb0100100,32'sb0101010,32'sb0110000,32'sb0110110,32'sb0111100,32'sb01000010,32'sb01001000,32'sb01001110,32'sb01010100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=15,BUS_DIN_NUM=26,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb011,32'sb0100,32'sb0101,32'sb0110,32'sb0111,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb010010,32'sb011000,32'sb011110,32'sb0100100,32'sb0101010,32'sb0110000,32'sb0110110,32'sb0111100,32'sb01000010,32'sb01001000,32'sb01001110,32'sb01010100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=15,BUS_DIN_NUM=26,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb011,32'sb0100,32'sb0101,32'sb0110,32'sb0111,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb010010,32'sb011000,32'sb011110,32'sb0100100,32'sb0101010,32'sb0110000,32'sb0110110,32'sb0111100,32'sb01000010,32'sb01001000,32'sb01001110,32'sb01010100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=134)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=134)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=15,BUS_DIN_NUM=26,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb011,32'sb0100,32'sb0101,32'sb0110,32'sb0111,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb010010,32'sb011000,32'sb011110,32'sb0100100,32'sb0101010,32'sb0110000,32'sb0110110,32'sb0111100,32'sb01000010,32'sb01001000,32'sb01001110,32'sb01010100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=15,BUS_DIN_NUM=26,BUS_CTRL_NUM=112,BUS_WIDTH='{32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb011,32'sb0100,32'sb0101,32'sb0110,32'sb0111,32'sb01000,32'sb01001,32'sb01010,32'sb01011,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb010010,32'sb011000,32'sb011110,32'sb0100100,32'sb0101010,32'sb0110000,32'sb0110110,32'sb0111100,32'sb01000010,32'sb01001000,32'sb01001110,32'sb01010100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3645/49 useful/useless nets, 2231/44 useful/useless insts
SYN-1016 : Merged 198 instances.
SYN-1032 : 3128/44 useful/useless nets, 2488/40 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1017 : Remove 2 const input seq instances
SYN-1002 :     cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[0]$bus_nodes/din_r1_reg
SYN-1002 :     cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[0]$bus_nodes/din_r2_reg
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1016 : Merged 1 instances.
SYN-1015 : Optimize round 1, 379 better
SYN-1014 : Optimize round 2
SYN-1032 : 2814/15 useful/useless nets, 2174/16 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1015 : Optimize round 2, 81 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 14 better
SYN-1014 : Optimize round 2
SYN-1015 : Optimize round 2, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2796/157 useful/useless nets, 2179/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 12 instances.
SYN-2501 : Optimize round 1, 26 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 19 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 3168/5 useful/useless nets, 2551/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11643, tnet num: 3168, tinst num: 2550, tnode num: 15609, tedge num: 18635.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3168 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 171 (3.77), #lev = 8 (1.98)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 174 (3.72), #lev = 7 (2.27)
SYN-3001 : Logic optimization runtime opt =   0.03 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 478 instances into 174 LUTs, name keeping = 67%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 373 DFF/LATCH to SEQ ...
SYN-4009 : Pack 6 carry chain into lslice
SYN-4007 : Packing 97 adder to BLE ...
SYN-4008 : Packed 97 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  1.077295s wall, 1.046875s user + 0.031250s system = 1.078125s CPU (100.1%)

RUN-1004 : used memory is 167 MB, reserved memory is 127 MB, peak memory is 198 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  2.176828s wall, 1.984375s user + 0.187500s system = 2.171875s CPU (99.8%)

RUN-1004 : used memory is 168 MB, reserved memory is 127 MB, peak memory is 198 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (275 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2093 instances
RUN-0007 : 541 luts, 1201 seqs, 182 mslices, 96 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2712 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1887 nets have 2 pins
RUN-1001 : 654 nets have [3 - 5] pins
RUN-1001 : 101 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     192     
RUN-1001 :   No   |  No   |  Yes  |     224     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     275     
RUN-1001 :   Yes  |  No   |  Yes  |     400     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  15   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 25
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2091 instances, 541 luts, 1201 seqs, 278 slices, 33 macros(278 instances: 182 mslices 96 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10582, tnet num: 2710, tinst num: 2091, tnode num: 14849, tedge num: 17931.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.463674s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (97.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 717261
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2091.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 618414, overlap = 67.5
PHY-3002 : Step(2): len = 577512, overlap = 69.75
PHY-3002 : Step(3): len = 557029, overlap = 69.75
PHY-3002 : Step(4): len = 540840, overlap = 63
PHY-3002 : Step(5): len = 528340, overlap = 69.75
PHY-3002 : Step(6): len = 510743, overlap = 67.5
PHY-3002 : Step(7): len = 497313, overlap = 69.75
PHY-3002 : Step(8): len = 489376, overlap = 65.25
PHY-3002 : Step(9): len = 473550, overlap = 63
PHY-3002 : Step(10): len = 462362, overlap = 65.25
PHY-3002 : Step(11): len = 451771, overlap = 63
PHY-3002 : Step(12): len = 442369, overlap = 65.25
PHY-3002 : Step(13): len = 426141, overlap = 60.75
PHY-3002 : Step(14): len = 417312, overlap = 65.25
PHY-3002 : Step(15): len = 407311, overlap = 60.75
PHY-3002 : Step(16): len = 395025, overlap = 65.25
PHY-3002 : Step(17): len = 379504, overlap = 58.5
PHY-3002 : Step(18): len = 373192, overlap = 63
PHY-3002 : Step(19): len = 357716, overlap = 58.5
PHY-3002 : Step(20): len = 346684, overlap = 63
PHY-3002 : Step(21): len = 338712, overlap = 56.25
PHY-3002 : Step(22): len = 329924, overlap = 60.75
PHY-3002 : Step(23): len = 320785, overlap = 56.25
PHY-3002 : Step(24): len = 314799, overlap = 58.5
PHY-3002 : Step(25): len = 306492, overlap = 54
PHY-3002 : Step(26): len = 300394, overlap = 54
PHY-3002 : Step(27): len = 291394, overlap = 54
PHY-3002 : Step(28): len = 286540, overlap = 58.5
PHY-3002 : Step(29): len = 278787, overlap = 60.75
PHY-3002 : Step(30): len = 272270, overlap = 60.75
PHY-3002 : Step(31): len = 264889, overlap = 60.75
PHY-3002 : Step(32): len = 260537, overlap = 60.75
PHY-3002 : Step(33): len = 248857, overlap = 67.5
PHY-3002 : Step(34): len = 242704, overlap = 65.25
PHY-3002 : Step(35): len = 237663, overlap = 65.25
PHY-3002 : Step(36): len = 231080, overlap = 65.25
PHY-3002 : Step(37): len = 220353, overlap = 63
PHY-3002 : Step(38): len = 216833, overlap = 63
PHY-3002 : Step(39): len = 211706, overlap = 63
PHY-3002 : Step(40): len = 203232, overlap = 63
PHY-3002 : Step(41): len = 195420, overlap = 60.75
PHY-3002 : Step(42): len = 193823, overlap = 65.25
PHY-3002 : Step(43): len = 180022, overlap = 65.25
PHY-3002 : Step(44): len = 141434, overlap = 65.375
PHY-3002 : Step(45): len = 138440, overlap = 69.75
PHY-3002 : Step(46): len = 136331, overlap = 69.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.39374e-05
PHY-3002 : Step(47): len = 138043, overlap = 67.5
PHY-3002 : Step(48): len = 136081, overlap = 60.75
PHY-3002 : Step(49): len = 133343, overlap = 60.75
PHY-3002 : Step(50): len = 130643, overlap = 59
PHY-3002 : Step(51): len = 129410, overlap = 56.5
PHY-3002 : Step(52): len = 128526, overlap = 56.5
PHY-3002 : Step(53): len = 127640, overlap = 56.5
PHY-3002 : Step(54): len = 124252, overlap = 58.5
PHY-3002 : Step(55): len = 120978, overlap = 58.5
PHY-3002 : Step(56): len = 119844, overlap = 56.25
PHY-3002 : Step(57): len = 117601, overlap = 54
PHY-3002 : Step(58): len = 114238, overlap = 51.75
PHY-3002 : Step(59): len = 110566, overlap = 51.8125
PHY-3002 : Step(60): len = 108935, overlap = 54
PHY-3002 : Step(61): len = 107881, overlap = 54
PHY-3002 : Step(62): len = 107118, overlap = 54
PHY-3002 : Step(63): len = 106479, overlap = 54
PHY-3002 : Step(64): len = 104355, overlap = 54.125
PHY-3002 : Step(65): len = 101809, overlap = 54.4375
PHY-3002 : Step(66): len = 101047, overlap = 54.375
PHY-3002 : Step(67): len = 99554.3, overlap = 54.4375
PHY-3002 : Step(68): len = 96707.2, overlap = 54.6875
PHY-3002 : Step(69): len = 93037.9, overlap = 56.5625
PHY-3002 : Step(70): len = 91321.7, overlap = 56.625
PHY-3002 : Step(71): len = 90294.6, overlap = 56.75
PHY-3002 : Step(72): len = 88451.9, overlap = 54.5625
PHY-3002 : Step(73): len = 87338.1, overlap = 55.625
PHY-3002 : Step(74): len = 86629.1, overlap = 55.625
PHY-3002 : Step(75): len = 84966.5, overlap = 58.25
PHY-3002 : Step(76): len = 81321.6, overlap = 56.125
PHY-3002 : Step(77): len = 79298.7, overlap = 59.0625
PHY-3002 : Step(78): len = 77763.6, overlap = 54.8125
PHY-3002 : Step(79): len = 77132.8, overlap = 57.375
PHY-3002 : Step(80): len = 76712.7, overlap = 57.625
PHY-3002 : Step(81): len = 75848.7, overlap = 55.875
PHY-3002 : Step(82): len = 74813.2, overlap = 58.8125
PHY-3002 : Step(83): len = 73696.3, overlap = 58.9375
PHY-3002 : Step(84): len = 72752.4, overlap = 59.375
PHY-3002 : Step(85): len = 71747, overlap = 59.8125
PHY-3002 : Step(86): len = 70904.5, overlap = 60.125
PHY-3002 : Step(87): len = 70231.9, overlap = 60.1875
PHY-3002 : Step(88): len = 69744.4, overlap = 59.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.78748e-05
PHY-3002 : Step(89): len = 69778.4, overlap = 60.9375
PHY-3002 : Step(90): len = 69758.3, overlap = 60.9375
PHY-3002 : Step(91): len = 69670, overlap = 58.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00017575
PHY-3002 : Step(92): len = 70005.5, overlap = 58.75
PHY-3002 : Step(93): len = 70200.2, overlap = 58.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.019225s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (243.8%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.112934s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (96.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000135091
PHY-3002 : Step(94): len = 82159.1, overlap = 20.8125
PHY-3002 : Step(95): len = 82598.8, overlap = 21.75
PHY-3002 : Step(96): len = 81062.3, overlap = 23.4375
PHY-3002 : Step(97): len = 80178.8, overlap = 22.9375
PHY-3002 : Step(98): len = 78494.6, overlap = 24.125
PHY-3002 : Step(99): len = 77162.6, overlap = 25.8438
PHY-3002 : Step(100): len = 75517.3, overlap = 24
PHY-3002 : Step(101): len = 74094.3, overlap = 25.9062
PHY-3002 : Step(102): len = 71590, overlap = 28.8125
PHY-3002 : Step(103): len = 69949.5, overlap = 32.0938
PHY-3002 : Step(104): len = 68455.6, overlap = 36.5312
PHY-3002 : Step(105): len = 67060.2, overlap = 40.0938
PHY-3002 : Step(106): len = 65528.1, overlap = 42.4062
PHY-3002 : Step(107): len = 64269.8, overlap = 40.5938
PHY-3002 : Step(108): len = 63537.4, overlap = 40.0938
PHY-3002 : Step(109): len = 62408.9, overlap = 39.625
PHY-3002 : Step(110): len = 61897.7, overlap = 39.25
PHY-3002 : Step(111): len = 61418.4, overlap = 41.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000270182
PHY-3002 : Step(112): len = 61086.8, overlap = 41.9688
PHY-3002 : Step(113): len = 60705.5, overlap = 41.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000540365
PHY-3002 : Step(114): len = 60614.3, overlap = 41.7812
PHY-3002 : Step(115): len = 60686.3, overlap = 41.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.130281s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (95.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.02576e-05
PHY-3002 : Step(116): len = 60509.3, overlap = 79.5312
PHY-3002 : Step(117): len = 61121.5, overlap = 74.8438
PHY-3002 : Step(118): len = 61252.2, overlap = 73.0938
PHY-3002 : Step(119): len = 61310.9, overlap = 76.375
PHY-3002 : Step(120): len = 61811.7, overlap = 73.125
PHY-3002 : Step(121): len = 61848.9, overlap = 67.5938
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.05151e-05
PHY-3002 : Step(122): len = 61734.5, overlap = 67.4062
PHY-3002 : Step(123): len = 62337.7, overlap = 66.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00012103
PHY-3002 : Step(124): len = 62425.7, overlap = 65.9062
PHY-3002 : Step(125): len = 64091.9, overlap = 54.75
PHY-3002 : Step(126): len = 65704.5, overlap = 46.5938
PHY-3002 : Step(127): len = 66093.4, overlap = 45.125
PHY-3002 : Step(128): len = 66108.5, overlap = 45.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10582, tnet num: 2710, tinst num: 2091, tnode num: 14849, tedge num: 17931.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 113.41 peak overflow 3.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2712.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73632, over cnt = 303(0%), over = 1240, worst = 17
PHY-1001 : End global iterations;  0.165525s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (113.3%)

PHY-1001 : Congestion index: top1 = 43.58, top5 = 27.57, top10 = 19.58, top15 = 14.49.
PHY-1001 : End incremental global routing;  0.240768s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (103.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.141531s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.445730s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (101.7%)

OPT-1001 : Current memory(MB): used = 232, reserve = 190, peak = 232.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1904/2712.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73632, over cnt = 303(0%), over = 1240, worst = 17
PHY-1002 : len = 81712, over cnt = 242(0%), over = 571, worst = 14
PHY-1002 : len = 86952, over cnt = 112(0%), over = 202, worst = 9
PHY-1002 : len = 90040, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 90600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.256605s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (115.7%)

PHY-1001 : Congestion index: top1 = 39.98, top5 = 27.91, top10 = 21.37, top15 = 16.76.
OPT-1001 : End congestion update;  0.318332s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (108.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2710 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.088548s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (105.9%)

OPT-0007 : Start: WNS -2897 TNS -53924 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2897 TNS -53924 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.410950s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (106.5%)

OPT-1001 : Current memory(MB): used = 237, reserve = 195, peak = 237.
OPT-1001 : End physical optimization;  1.275317s wall, 1.312500s user + 0.046875s system = 1.359375s CPU (106.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 541 LUT to BLE ...
SYN-4008 : Packed 541 LUT and 241 SEQ to BLE.
SYN-4003 : Packing 960 remaining SEQ's ...
SYN-4005 : Packed 243 SEQ with LUT/SLICE
SYN-4006 : 95 single LUT's are left
SYN-4006 : 717 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1258/1643 primitive instances ...
PHY-3001 : End packing;  0.085543s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1013 instances
RUN-1001 : 470 mslices, 470 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2480 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1662 nets have 2 pins
RUN-1001 : 647 nets have [3 - 5] pins
RUN-1001 : 102 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
PHY-3001 : design contains 1011 instances, 940 slices, 33 macros(278 instances: 182 mslices 96 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : After packing: Len = 67260.4, Over = 79.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8863, tnet num: 2478, tinst num: 1011, tnode num: 11884, tedge num: 15531.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.457791s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.17834e-05
PHY-3002 : Step(129): len = 66337.4, overlap = 78.75
PHY-3002 : Step(130): len = 65541, overlap = 78.5
PHY-3002 : Step(131): len = 65358.7, overlap = 80
PHY-3002 : Step(132): len = 64927.1, overlap = 78.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.35667e-05
PHY-3002 : Step(133): len = 65474.9, overlap = 75.75
PHY-3002 : Step(134): len = 66460.3, overlap = 74.75
PHY-3002 : Step(135): len = 67552.1, overlap = 68.5
PHY-3002 : Step(136): len = 68211.9, overlap = 67.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.71335e-05
PHY-3002 : Step(137): len = 68557.1, overlap = 64.5
PHY-3002 : Step(138): len = 69304.8, overlap = 63.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.206845s wall, 0.125000s user + 0.203125s system = 0.328125s CPU (158.6%)

PHY-3001 : Trial Legalized: Len = 92848.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.087539s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (107.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000456968
PHY-3002 : Step(139): len = 87223, overlap = 6.5
PHY-3002 : Step(140): len = 83119.2, overlap = 16.5
PHY-3002 : Step(141): len = 80384.6, overlap = 22
PHY-3002 : Step(142): len = 78855.5, overlap = 29.25
PHY-3002 : Step(143): len = 77790.9, overlap = 31
PHY-3002 : Step(144): len = 77149.2, overlap = 32.25
PHY-3002 : Step(145): len = 76702.7, overlap = 31.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000913936
PHY-3002 : Step(146): len = 77277.9, overlap = 31.75
PHY-3002 : Step(147): len = 77541.9, overlap = 30.5
PHY-3002 : Step(148): len = 77658, overlap = 30.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00182787
PHY-3002 : Step(149): len = 77810.1, overlap = 30.25
PHY-3002 : Step(150): len = 77854.6, overlap = 29.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008294s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 83605.3, Over = 0
PHY-3001 : Spreading special nets. 39 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.012845s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 52 instances has been re-located, deltaX = 40, deltaY = 24, maxDist = 2.
PHY-3001 : Final: Len = 84761.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8863, tnet num: 2478, tinst num: 1011, tnode num: 11884, tedge num: 15531.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 56/2480.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 98768, over cnt = 238(0%), over = 387, worst = 7
PHY-1002 : len = 100200, over cnt = 146(0%), over = 203, worst = 6
PHY-1002 : len = 102080, over cnt = 53(0%), over = 59, worst = 2
PHY-1002 : len = 102800, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 102816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.309767s wall, 0.312500s user + 0.109375s system = 0.421875s CPU (136.2%)

PHY-1001 : Congestion index: top1 = 33.69, top5 = 27.04, top10 = 21.81, top15 = 18.21.
PHY-1001 : End incremental global routing;  0.386056s wall, 0.390625s user + 0.109375s system = 0.500000s CPU (129.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.098282s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (95.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.540753s wall, 0.546875s user + 0.109375s system = 0.656250s CPU (121.4%)

OPT-1001 : Current memory(MB): used = 240, reserve = 198, peak = 241.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2170/2480.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 102816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.012308s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.69, top5 = 27.04, top10 = 21.81, top15 = 18.21.
OPT-1001 : End congestion update;  0.087030s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (89.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.086179s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (108.8%)

OPT-0007 : Start: WNS -2897 TNS -54324 NUM_FEPS 28
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 972 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 1011 instances, 940 slices, 33 macros(278 instances: 182 mslices 96 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Initial: Len = 84730.4, Over = 0
PHY-3001 : Spreading special nets. 1 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.008680s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 1 instances has been re-located, deltaX = 1, deltaY = 0, maxDist = 1.
PHY-3001 : Final: Len = 84762.4, Over = 0
PHY-3001 : End incremental legalization;  0.069848s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (290.8%)

OPT-0007 : Iter 1: improved WNS -2897 TNS -54324 NUM_FEPS 28 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS -2897 TNS -54324 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.267221s wall, 0.359375s user + 0.031250s system = 0.390625s CPU (146.2%)

OPT-1001 : Current memory(MB): used = 245, reserve = 203, peak = 246.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.085690s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2170/2480.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 102816, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010841s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (144.1%)

PHY-1001 : Congestion index: top1 = 33.69, top5 = 27.04, top10 = 21.81, top15 = 18.21.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.092993s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (100.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -2897 TNS -54324 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -2897ps with logic level 2 
RUN-1001 :       #2 path slack -2897ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2480 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2480 nets
OPT-1001 : End physical optimization;  1.541351s wall, 1.625000s user + 0.156250s system = 1.781250s CPU (115.6%)

RUN-1003 : finish command "place" in  9.606588s wall, 14.359375s user + 4.765625s system = 19.125000s CPU (199.1%)

RUN-1004 : used memory is 222 MB, reserved memory is 180 MB, peak memory is 246 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1013 instances
RUN-1001 : 470 mslices, 470 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2480 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1662 nets have 2 pins
RUN-1001 : 647 nets have [3 - 5] pins
RUN-1001 : 102 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8863, tnet num: 2478, tinst num: 1011, tnode num: 11884, tedge num: 15531.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 470 mslices, 470 lslices, 34 pads, 28 brams, 4 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2478 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 97440, over cnt = 242(0%), over = 416, worst = 7
PHY-1002 : len = 99064, over cnt = 142(0%), over = 211, worst = 6
PHY-1002 : len = 100992, over cnt = 42(0%), over = 50, worst = 2
PHY-1002 : len = 101608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.287570s wall, 0.343750s user + 0.046875s system = 0.390625s CPU (135.8%)

PHY-1001 : Congestion index: top1 = 33.53, top5 = 26.79, top10 = 21.60, top15 = 18.06.
PHY-1001 : End global routing;  0.368091s wall, 0.406250s user + 0.046875s system = 0.453125s CPU (123.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 261, reserve = 219, peak = 266.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 520, reserve = 482, peak = 520.
PHY-1001 : End build detailed router design. 4.405724s wall, 4.312500s user + 0.078125s system = 4.390625s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 36640, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.542146s wall, 2.500000s user + 0.015625s system = 2.515625s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 553, reserve = 516, peak = 553.
PHY-1001 : End phase 1; 2.548812s wall, 2.500000s user + 0.015625s system = 2.515625s CPU (98.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 303080, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 554, reserve = 516, peak = 555.
PHY-1001 : End initial routed; 4.038147s wall, 4.859375s user + 0.171875s system = 5.031250s CPU (124.6%)

PHY-1001 : Update timing.....
PHY-1001 : 3/2198(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.016   |  -0.031   |   2   
RUN-1001 :   Hold   |  -0.111   |  -0.205   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.547411s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (97.0%)

PHY-1001 : Current memory(MB): used = 556, reserve = 519, peak = 556.
PHY-1001 : End phase 2; 4.585671s wall, 5.390625s user + 0.171875s system = 5.562500s CPU (121.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS 0.029ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.026715s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (117.0%)

PHY-1022 : len = 303096, over cnt = 61(0%), over = 61, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.050897s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 302616, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.087405s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (160.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 302832, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.042436s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (110.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2198(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.029   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.205   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.567478s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (99.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.338973s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 572, reserve = 535, peak = 572.
PHY-1001 : End phase 3; 1.284324s wall, 1.312500s user + 0.015625s system = 1.328125s CPU (103.4%)

PHY-1003 : Routed, final wirelength = 302832
PHY-1001 : Current memory(MB): used = 572, reserve = 536, peak = 572.
PHY-1001 : End export database. 0.012743s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (122.6%)

PHY-1001 : End detail routing;  13.089311s wall, 13.781250s user + 0.281250s system = 14.062500s CPU (107.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8863, tnet num: 2478, tinst num: 1011, tnode num: 11884, tedge num: 15531.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[18] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[4] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[32] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8881, tnet num: 2487, tinst num: 1020, tnode num: 11902, tedge num: 15549.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  5.139404s wall, 5.109375s user + 0.234375s system = 5.343750s CPU (104.0%)

RUN-1003 : finish command "route" in  19.121817s wall, 19.812500s user + 0.578125s system = 20.390625s CPU (106.6%)

RUN-1004 : used memory is 548 MB, reserved memory is 512 MB, peak memory is 572 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1115   out of  19600    5.69%
#reg                     1275   out of  19600    6.51%
#le                      1832
  #lut only               557   out of   1832   30.40%
  #reg only               717   out of   1832   39.14%
  #lut&reg                558   out of   1832   30.46%
#dsp                        4   out of     29   13.79%
#bram                      28   out of     64   43.75%
  #bram9k                  28
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         372
#2        config_inst_syn_9               GCLK               config             config_inst.jtck              144
#3        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         99
#4        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di               78
#5        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       NONE    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       NONE    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       NONE    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       NONE    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       NONE    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       NONE    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       NONE    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       NONE    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       NONE    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       NONE    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       NONE    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       NONE    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       NONE    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       NONE    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+--------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                             |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+--------------------------------------------------------------------------------------------------------------------------------------------+
|top                                  |IFOG501_2B                                       |1832   |837     |278     |1292    |28      |4       |
|  CLK120                             |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                     |SignalProcessing                                 |930    |282     |114     |746     |4       |4       |
|    ctrl_signal                      |SignalGenerator                                  |25     |19      |6       |20      |0       |0       |
|    demodu                           |Demodulation                                     |442    |114     |44      |345     |4       |0       |
|      fifo                           |Asys_fifo56X16                                   |54     |29      |6       |45      |4       |0       |
|        ram_inst                     |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst          |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst          |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |12     |7       |0       |12      |0       |0       |
|    integ                            |Integration                                      |141    |39      |14      |115     |0       |0       |
|    rs422                            |Rs422Output                                      |302    |95      |45      |248     |0       |4       |
|    trans                            |SquareWaveGenerator                              |20     |15      |5       |18      |0       |0       |
|  u_uart                             |UART_Control                                     |113    |105     |7       |62      |0       |0       |
|    U0                               |speed_select_Tx                                  |29     |22      |7       |17      |0       |0       |
|    U1                               |uart_tx                                          |18     |17      |0       |17      |0       |0       |
|    U2                               |Ctrl_Data                                        |66     |66      |0       |28      |0       |0       |
|  wendu                              |DS18B20                                          |208    |163     |45      |72      |0       |0       |
|  cw_top                             |CW_TOP_WRAPPER                                   |513    |252     |79      |372     |0       |0       |
|    wrapper_cwc_top                  |cwc_top                                          |513    |252     |79      |372     |0       |0       |
|      cfg_int_inst                   |cwc_cfg_int                                      |270    |105     |0       |269     |0       |0       |
|        reg_inst                     |register                                         |267    |102     |0       |266     |0       |0       |
|        tap_inst                     |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                   |trigger                                          |243    |147     |79      |103     |0       |0       |
|        bus_inst                     |bus_top                                          |39     |17      |14      |15      |0       |0       |
|          BUS_DETECTOR[14]$bus_nodes |bus_det                                          |39     |17      |14      |15      |0       |0       |
|        emb_ctrl_inst                |emb_ctrl                                         |109    |76      |33      |48      |0       |0       |
+--------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1636  
    #2          2       460   
    #3          3       174   
    #4          4        13   
    #5        5-10      107   
    #6        11-50      49   
    #7       51-100      4    
    #8       101-500     2    
  Average     2.29            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8881, tnet num: 2487, tinst num: 1020, tnode num: 11902, tedge num: 15549.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2487 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 8c0bef7d7f4a23ca399149266bad52a96c0817f4101cbf2ed9a6d4b976597300 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1020
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2489, pip num: 21287
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1640 valid insts, and 55423 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000001010001101011
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  6.408252s wall, 31.593750s user + 0.109375s system = 31.703125s CPU (494.7%)

RUN-1004 : used memory is 547 MB, reserved memory is 510 MB, peak memory is 695 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231129_142739.log"
