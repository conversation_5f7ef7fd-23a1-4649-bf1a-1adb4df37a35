============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Sep  7 15:22:58 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 301 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 301 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1645 instances
RUN-0007 : 378 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2215 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1655 nets have 2 pins
RUN-1001 : 444 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1643 instances, 378 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7840, tnet num: 2213, tinst num: 1643, tnode num: 11078, tedge num: 13239.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2213 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.305932s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (102.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 568159
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1643.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 517480, overlap = 18
PHY-3002 : Step(2): len = 421765, overlap = 20.25
PHY-3002 : Step(3): len = 360810, overlap = 13.5
PHY-3002 : Step(4): len = 339364, overlap = 11.25
PHY-3002 : Step(5): len = 331320, overlap = 13.5
PHY-3002 : Step(6): len = 323035, overlap = 15.75
PHY-3002 : Step(7): len = 315223, overlap = 18
PHY-3002 : Step(8): len = 307735, overlap = 18
PHY-3002 : Step(9): len = 302722, overlap = 18
PHY-3002 : Step(10): len = 293092, overlap = 20.25
PHY-3002 : Step(11): len = 286971, overlap = 20.25
PHY-3002 : Step(12): len = 281462, overlap = 20.25
PHY-3002 : Step(13): len = 276455, overlap = 20.25
PHY-3002 : Step(14): len = 268620, overlap = 20.25
PHY-3002 : Step(15): len = 264837, overlap = 20.25
PHY-3002 : Step(16): len = 258781, overlap = 20.25
PHY-3002 : Step(17): len = 253323, overlap = 20.25
PHY-3002 : Step(18): len = 247312, overlap = 20.25
PHY-3002 : Step(19): len = 243948, overlap = 20.25
PHY-3002 : Step(20): len = 234729, overlap = 20.25
PHY-3002 : Step(21): len = 230157, overlap = 20.25
PHY-3002 : Step(22): len = 226544, overlap = 20.25
PHY-3002 : Step(23): len = 219517, overlap = 20.25
PHY-3002 : Step(24): len = 211963, overlap = 20.25
PHY-3002 : Step(25): len = 210098, overlap = 20.25
PHY-3002 : Step(26): len = 199843, overlap = 20.25
PHY-3002 : Step(27): len = 180133, overlap = 20.25
PHY-3002 : Step(28): len = 176297, overlap = 20.25
PHY-3002 : Step(29): len = 173083, overlap = 20.25
PHY-3002 : Step(30): len = 134565, overlap = 15.75
PHY-3002 : Step(31): len = 130039, overlap = 15.75
PHY-3002 : Step(32): len = 127612, overlap = 18
PHY-3002 : Step(33): len = 125548, overlap = 18
PHY-3002 : Step(34): len = 121180, overlap = 18
PHY-3002 : Step(35): len = 117531, overlap = 18
PHY-3002 : Step(36): len = 115893, overlap = 18
PHY-3002 : Step(37): len = 112819, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.2436e-05
PHY-3002 : Step(38): len = 114096, overlap = 18
PHY-3002 : Step(39): len = 113382, overlap = 11.25
PHY-3002 : Step(40): len = 112307, overlap = 9
PHY-3002 : Step(41): len = 110441, overlap = 11.25
PHY-3002 : Step(42): len = 108008, overlap = 9
PHY-3002 : Step(43): len = 107233, overlap = 11.25
PHY-3002 : Step(44): len = 107096, overlap = 4.5
PHY-3002 : Step(45): len = 102619, overlap = 13.5
PHY-3002 : Step(46): len = 96435, overlap = 13.5
PHY-3002 : Step(47): len = 94682.4, overlap = 11.25
PHY-3002 : Step(48): len = 94847.9, overlap = 6.75
PHY-3002 : Step(49): len = 93253.4, overlap = 9
PHY-3002 : Step(50): len = 87842, overlap = 11.25
PHY-3002 : Step(51): len = 86651.8, overlap = 11.25
PHY-3002 : Step(52): len = 85756.2, overlap = 9
PHY-3002 : Step(53): len = 84456.9, overlap = 4.5
PHY-3002 : Step(54): len = 83317.3, overlap = 9
PHY-3002 : Step(55): len = 82619, overlap = 11.25
PHY-3002 : Step(56): len = 81232, overlap = 11.25
PHY-3002 : Step(57): len = 80472.3, overlap = 11.25
PHY-3002 : Step(58): len = 79896, overlap = 9
PHY-3002 : Step(59): len = 79358.9, overlap = 6.75
PHY-3002 : Step(60): len = 77184.8, overlap = 9
PHY-3002 : Step(61): len = 76147.9, overlap = 11.25
PHY-3002 : Step(62): len = 74916, overlap = 9
PHY-3002 : Step(63): len = 74021.9, overlap = 9
PHY-3002 : Step(64): len = 73300.6, overlap = 9
PHY-3002 : Step(65): len = 70995.5, overlap = 6.75
PHY-3002 : Step(66): len = 68859.7, overlap = 11.25
PHY-3002 : Step(67): len = 67541, overlap = 11.25
PHY-3002 : Step(68): len = 66701.1, overlap = 11.25
PHY-3002 : Step(69): len = 65827.5, overlap = 11.25
PHY-3002 : Step(70): len = 65604, overlap = 6.75
PHY-3002 : Step(71): len = 64271.1, overlap = 9
PHY-3002 : Step(72): len = 62910.4, overlap = 11.25
PHY-3002 : Step(73): len = 61812.6, overlap = 11.25
PHY-3002 : Step(74): len = 60805.9, overlap = 11.25
PHY-3002 : Step(75): len = 59861.6, overlap = 13.5
PHY-3002 : Step(76): len = 59310.6, overlap = 11.25
PHY-3002 : Step(77): len = 59109.1, overlap = 11.25
PHY-3002 : Step(78): len = 58488.5, overlap = 11.25
PHY-3002 : Step(79): len = 57912.3, overlap = 11.25
PHY-3002 : Step(80): len = 57515.9, overlap = 11.25
PHY-3002 : Step(81): len = 56422.4, overlap = 11.25
PHY-3002 : Step(82): len = 55392, overlap = 11.25
PHY-3002 : Step(83): len = 54722.4, overlap = 11.25
PHY-3002 : Step(84): len = 54090.6, overlap = 11.25
PHY-3002 : Step(85): len = 53796.8, overlap = 11.25
PHY-3002 : Step(86): len = 53350.5, overlap = 11.25
PHY-3002 : Step(87): len = 53096.1, overlap = 11.25
PHY-3002 : Step(88): len = 52777.4, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000164872
PHY-3002 : Step(89): len = 53021.3, overlap = 11.25
PHY-3002 : Step(90): len = 53060.1, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000329744
PHY-3002 : Step(91): len = 52981.8, overlap = 6.75
PHY-3002 : Step(92): len = 52963.2, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006030s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2213 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064183s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 56614.1, overlap = 7.875
PHY-3002 : Step(94): len = 55663.6, overlap = 8.125
PHY-3002 : Step(95): len = 55220.4, overlap = 8.125
PHY-3002 : Step(96): len = 54554.1, overlap = 7.0625
PHY-3002 : Step(97): len = 54148.3, overlap = 6.875
PHY-3002 : Step(98): len = 53456, overlap = 6
PHY-3002 : Step(99): len = 52883.7, overlap = 4.1875
PHY-3002 : Step(100): len = 52458, overlap = 3.8125
PHY-3002 : Step(101): len = 51734.5, overlap = 3.875
PHY-3002 : Step(102): len = 51446.4, overlap = 4.25
PHY-3002 : Step(103): len = 51259.5, overlap = 4.625
PHY-3002 : Step(104): len = 50586.2, overlap = 5.0625
PHY-3002 : Step(105): len = 50159.9, overlap = 5.3125
PHY-3002 : Step(106): len = 49953.7, overlap = 5.28125
PHY-3002 : Step(107): len = 49485.8, overlap = 6.53125
PHY-3002 : Step(108): len = 49351.4, overlap = 6.53125
PHY-3002 : Step(109): len = 49288.7, overlap = 6.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000971334
PHY-3002 : Step(110): len = 48992.8, overlap = 6.5625
PHY-3002 : Step(111): len = 49037.2, overlap = 5.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00194267
PHY-3002 : Step(112): len = 48914.9, overlap = 5.875
PHY-3002 : Step(113): len = 48888.8, overlap = 5.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2213 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071043s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000133283
PHY-3002 : Step(114): len = 49060, overlap = 49.125
PHY-3002 : Step(115): len = 49850.7, overlap = 48.5938
PHY-3002 : Step(116): len = 50401.5, overlap = 47
PHY-3002 : Step(117): len = 50000.3, overlap = 46.4375
PHY-3002 : Step(118): len = 49856.8, overlap = 46.375
PHY-3002 : Step(119): len = 49767.3, overlap = 45.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000266567
PHY-3002 : Step(120): len = 50048.5, overlap = 44.6875
PHY-3002 : Step(121): len = 50341.8, overlap = 44.5938
PHY-3002 : Step(122): len = 51208.3, overlap = 45.3125
PHY-3002 : Step(123): len = 51987, overlap = 44.5938
PHY-3002 : Step(124): len = 52369.5, overlap = 44.4688
PHY-3002 : Step(125): len = 52008.4, overlap = 41.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000533133
PHY-3002 : Step(126): len = 51895.9, overlap = 41.1562
PHY-3002 : Step(127): len = 52036, overlap = 40.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7840, tnet num: 2213, tinst num: 1643, tnode num: 11078, tedge num: 13239.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 97.50 peak overflow 3.47
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2215.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55536, over cnt = 265(0%), over = 1075, worst = 25
PHY-1001 : End global iterations;  0.071244s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (131.6%)

PHY-1001 : Congestion index: top1 = 44.98, top5 = 25.20, top10 = 16.12, top15 = 11.40.
PHY-1001 : End incremental global routing;  0.119629s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (104.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2213 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066334s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.216631s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.0%)

OPT-1001 : Current memory(MB): used = 215, reserve = 178, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1676/2215.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55536, over cnt = 265(0%), over = 1075, worst = 25
PHY-1002 : len = 62360, over cnt = 161(0%), over = 383, worst = 15
PHY-1002 : len = 66760, over cnt = 36(0%), over = 62, worst = 8
PHY-1002 : len = 67584, over cnt = 7(0%), over = 8, worst = 2
PHY-1002 : len = 67776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.107259s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (131.1%)

PHY-1001 : Congestion index: top1 = 38.53, top5 = 25.36, top10 = 17.94, top15 = 13.30.
OPT-1001 : End congestion update;  0.150300s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (124.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2213 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068731s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.221435s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (120.0%)

OPT-1001 : Current memory(MB): used = 219, reserve = 181, peak = 219.
OPT-1001 : End physical optimization;  0.713944s wall, 0.828125s user + 0.015625s system = 0.843750s CPU (118.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 378 LUT to BLE ...
SYN-4008 : Packed 378 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 105 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 698 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1076/1409 primitive instances ...
PHY-3001 : End packing;  0.048378s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2040 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51932.6, Over = 66.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6596, tnet num: 2038, tinst num: 841, tnode num: 8938, tedge num: 11585.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2038 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.317221s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (103.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.48209e-05
PHY-3002 : Step(128): len = 51483.6, overlap = 65
PHY-3002 : Step(129): len = 50935.9, overlap = 62.75
PHY-3002 : Step(130): len = 50605.5, overlap = 62.25
PHY-3002 : Step(131): len = 50666.5, overlap = 63.5
PHY-3002 : Step(132): len = 50523.6, overlap = 63.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.96418e-05
PHY-3002 : Step(133): len = 50615.6, overlap = 62.5
PHY-3002 : Step(134): len = 51747.4, overlap = 59.5
PHY-3002 : Step(135): len = 52341.7, overlap = 57.5
PHY-3002 : Step(136): len = 52263.4, overlap = 57.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.92837e-05
PHY-3002 : Step(137): len = 52653.3, overlap = 56.75
PHY-3002 : Step(138): len = 53698, overlap = 53.75
PHY-3002 : Step(139): len = 54308.7, overlap = 52.75
PHY-3002 : Step(140): len = 54505.5, overlap = 53.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.079741s wall, 0.062500s user + 0.140625s system = 0.203125s CPU (254.7%)

PHY-3001 : Trial Legalized: Len = 69029.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2038 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051529s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0005899
PHY-3002 : Step(141): len = 65463.4, overlap = 9.75
PHY-3002 : Step(142): len = 62947, overlap = 15.25
PHY-3002 : Step(143): len = 61228.3, overlap = 17.25
PHY-3002 : Step(144): len = 60055.9, overlap = 20
PHY-3002 : Step(145): len = 59482.5, overlap = 25
PHY-3002 : Step(146): len = 59105.4, overlap = 26.75
PHY-3002 : Step(147): len = 58745.1, overlap = 28.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0011798
PHY-3002 : Step(148): len = 59191.1, overlap = 27.5
PHY-3002 : Step(149): len = 59316.7, overlap = 26.75
PHY-3002 : Step(150): len = 59303.9, overlap = 27.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0023596
PHY-3002 : Step(151): len = 59422.2, overlap = 26.75
PHY-3002 : Step(152): len = 59481.6, overlap = 25.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004952s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64148, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006006s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (260.2%)

PHY-3001 : 4 instances has been re-located, deltaX = 1, deltaY = 3, maxDist = 1.
PHY-3001 : Final: Len = 64172, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6596, tnet num: 2038, tinst num: 841, tnode num: 8938, tedge num: 11585.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 98/2040.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70632, over cnt = 138(0%), over = 219, worst = 7
PHY-1002 : len = 71616, over cnt = 75(0%), over = 91, worst = 4
PHY-1002 : len = 72616, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 72696, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114451s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (122.9%)

PHY-1001 : Congestion index: top1 = 31.98, top5 = 22.34, top10 = 17.50, top15 = 14.03.
PHY-1001 : End incremental global routing;  0.164454s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (114.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2038 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062991s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.257303s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (109.3%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1794/2040.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72712, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005831s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.98, top5 = 22.34, top10 = 17.50, top15 = 14.03.
OPT-1001 : End congestion update;  0.051453s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2038 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.110153s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (99.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 803 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64237.6, Over = 0
PHY-3001 : End spreading;  0.005000s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64237.6, Over = 0
PHY-3001 : End incremental legalization;  0.035909s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (174.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.211822s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (118.0%)

OPT-1001 : Current memory(MB): used = 226, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2038 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047835s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1790/2040.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72784, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.011693s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.09, top5 = 22.36, top10 = 17.52, top15 = 14.04.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2038 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048976s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (127.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.916884s wall, 0.984375s user + 0.031250s system = 1.015625s CPU (110.8%)

RUN-1003 : finish command "place" in  5.499379s wall, 8.937500s user + 3.000000s system = 11.937500s CPU (217.1%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 227 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2040 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1483 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6596, tnet num: 2038, tinst num: 841, tnode num: 8938, tedge num: 11585.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2038 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69856, over cnt = 137(0%), over = 212, worst = 6
PHY-1002 : len = 70888, over cnt = 75(0%), over = 87, worst = 4
PHY-1002 : len = 71936, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71968, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106111s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (162.0%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 22.08, top10 = 17.36, top15 = 13.92.
PHY-1001 : End global routing;  0.155540s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (140.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 205, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 503, reserve = 470, peak = 503.
PHY-1001 : End build detailed router design. 3.202564s wall, 3.187500s user + 0.015625s system = 3.203125s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33192, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.267708s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End phase 1; 1.273419s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183016, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 503, peak = 537.
PHY-1001 : End initial routed; 1.034665s wall, 2.046875s user + 0.078125s system = 2.125000s CPU (205.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1803(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.444   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.282   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.372451s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 538, reserve = 505, peak = 538.
PHY-1001 : End phase 2; 1.407203s wall, 2.421875s user + 0.078125s system = 2.500000s CPU (177.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183016, over cnt = 42(0%), over = 42, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014864s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (105.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182792, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029917s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (208.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182808, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019665s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (79.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1803(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.444   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.282   |   6   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.372708s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.179589s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (95.7%)

PHY-1001 : Current memory(MB): used = 551, reserve = 519, peak = 551.
PHY-1001 : End phase 3; 0.736367s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (104.0%)

PHY-1003 : Routed, final wirelength = 182808
PHY-1001 : Current memory(MB): used = 552, reserve = 519, peak = 552.
PHY-1001 : End export database. 0.009488s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (164.7%)

PHY-1001 : End detail routing;  6.810101s wall, 7.843750s user + 0.093750s system = 7.937500s CPU (116.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6596, tnet num: 2038, tinst num: 841, tnode num: 8938, tedge num: 11585.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[32] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[34] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[6] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6624, tnet num: 2052, tinst num: 855, tnode num: 8966, tedge num: 11613.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.163125s wall, 3.312500s user + 0.109375s system = 3.421875s CPU (108.2%)

RUN-1003 : finish command "route" in  10.481344s wall, 11.703125s user + 0.218750s system = 11.921875s CPU (113.7%)

RUN-1004 : used memory is 545 MB, reserved memory is 515 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      862   out of  19600    4.40%
#reg                     1074   out of  19600    5.48%
#le                      1560
  #lut only               486   out of   1560   31.15%
  #reg only               698   out of   1560   44.74%
  #lut&reg                376   out of   1560   24.10%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         472
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1560   |636     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1161   |336     |133     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |569    |161     |58      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |62      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |53     |4       |0       |53      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |13      |0       |25      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |88     |33      |21      |84      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |82      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |31     |26      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |110    |97      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |18     |14      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |57     |55      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |206    |161     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1461  
    #2          2       318   
    #3          3       105   
    #4          4        18   
    #5        5-10       79   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6624, tnet num: 2052, tinst num: 855, tnode num: 8966, tedge num: 11613.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2052 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 855
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2054, pip num: 14881
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1321 valid insts, and 39652 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.040216s wall, 17.625000s user + 0.062500s system = 17.687500s CPU (581.8%)

RUN-1004 : used memory is 554 MB, reserved memory is 519 MB, peak memory is 688 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230907_152258.log"
