============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Oct 23 19:16:41 2023

   Run on =     TLH-022
============================================================
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  4.711523s wall, 4.656250s user + 0.218750s system = 4.875000s CPU (103.5%)

RUN-1004 : used memory is 513 MB, reserved memory is 467 MB, peak memory is 513 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file Src_al/DS18B20.v
HDL-1007 : analyze verilog file Src_al/Demodulation.v
HDL-1007 : analyze verilog file Src_al/Integration.v
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-1007 : analyze verilog file Src_al/Rs422Output.v
HDL-1007 : analyze verilog file Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file Src_al/UART_Control.v
HDL-1007 : analyze verilog file Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file Src_al/uart_tx.v
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-6001 WARNING: Failed to reset phy_1: some files can't be removed in D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.584751s wall, 1.265625s user + 0.437500s system = 1.703125s CPU (107.5%)

RUN-1004 : used memory is 485 MB, reserved memory is 486 MB, peak memory is 519 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.261506s wall, 1.234375s user + 0.093750s system = 1.328125s CPU (105.3%)

RUN-1004 : used memory is 488 MB, reserved memory is 488 MB, peak memory is 519 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.237834s wall, 1.156250s user + 0.156250s system = 1.312500s CPU (106.0%)

RUN-1004 : used memory is 496 MB, reserved memory is 496 MB, peak memory is 519 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file Src_al/DS18B20.v
HDL-1007 : analyze verilog file Src_al/Demodulation.v
HDL-1007 : analyze verilog file Src_al/Integration.v
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-1007 : analyze verilog file Src_al/Rs422Output.v
HDL-1007 : analyze verilog file Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file Src_al/UART_Control.v
HDL-1007 : analyze verilog file Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file Src_al/uart_tx.v
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.262922s wall, 1.218750s user + 0.062500s system = 1.281250s CPU (101.5%)

RUN-1004 : used memory is 501 MB, reserved memory is 502 MB, peak memory is 519 MB
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file Src_al/DS18B20.v
HDL-1007 : analyze verilog file Src_al/Demodulation.v
HDL-1007 : analyze verilog file Src_al/Integration.v
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-1007 : analyze verilog file Src_al/Rs422Output.v
HDL-1007 : analyze verilog file Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file Src_al/UART_Control.v
HDL-1007 : analyze verilog file Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file Src_al/uart_tx.v
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.236660s wall, 1.250000s user + 0.015625s system = 1.265625s CPU (102.3%)

RUN-1004 : used memory is 506 MB, reserved memory is 507 MB, peak memory is 519 MB
RUN-1002 : start command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode jtag -spd 7 -sec 64 -cable 0"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.464887s wall, 0.046875s user + 0.078125s system = 0.125000s CPU (8.5%)

RUN-1004 : used memory is 216 MB, reserved memory is 607 MB, peak memory is 519 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode jtag -spd 7 -sec 64 -cable 0" in  1.624928s wall, 0.125000s user + 0.125000s system = 0.250000s CPU (15.4%)

RUN-1004 : used memory is 216 MB, reserved memory is 607 MB, peak memory is 519 MB
GUI-1001 : Downloading succeeded!
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.394619s wall, 1.281250s user + 0.109375s system = 1.390625s CPU (99.7%)

RUN-1004 : used memory is 558 MB, reserved memory is 600 MB, peak memory is 558 MB
RUN-1002 : start command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode jtag -spd 7 -sec 64 -cable 0"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.465191s wall, 0.046875s user + 0.062500s system = 0.109375s CPU (7.5%)

RUN-1004 : used memory is 582 MB, reserved memory is 622 MB, peak memory is 600 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode jtag -spd 7 -sec 64 -cable 0" in  1.623285s wall, 0.125000s user + 0.062500s system = 0.187500s CPU (11.6%)

RUN-1004 : used memory is 582 MB, reserved memory is 622 MB, peak memory is 600 MB
GUI-1001 : Downloading succeeded!
RUN-1002 : start command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode program_spi -v -spd 7 -sec 64 -cable 0 -flashsize 128"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
PRG-1001 : SPI Flash ID is:  ef
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m program_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1003 : finish command "bit_to_vec -chip EAGLE_20K_BGA256X -m program_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit" in  1.465953s wall, 1.437500s user + 0.109375s system = 1.546875s CPU (105.5%)

RUN-1004 : used memory is 906 MB, reserved memory is 961 MB, peak memory is 916 MB
RUN-1002 : start command "program_spi -cable 0 -spd 7"
RUN-1003 : finish command "program_spi -cable 0 -spd 7" in  11.490175s wall, 1.765625s user + 0.359375s system = 2.125000s CPU (18.5%)

RUN-1004 : used memory is 907 MB, reserved memory is 963 MB, peak memory is 916 MB
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m verify_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -spd 4"
RUN-1003 : finish command "program -cable 0 -spd 4" in  7.310726s wall, 0.296875s user + 0.484375s system = 0.781250s CPU (10.7%)

RUN-1004 : used memory is 717 MB, reserved memory is 763 MB, peak memory is 916 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode program_spi -v -spd 7 -sec 64 -cable 0 -flashsize 128" in  22.887636s wall, 5.062500s user + 1.015625s system = 6.078125s CPU (26.6%)

RUN-1004 : used memory is 717 MB, reserved memory is 763 MB, peak memory is 916 MB
GUI-1001 : Downloading succeeded!
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.254844s wall, 1.203125s user + 0.078125s system = 1.281250s CPU (102.1%)

RUN-1004 : used memory is 697 MB, reserved memory is 762 MB, peak memory is 916 MB
RUN-1002 : start command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode program_spi -v -spd 7 -sec 64 -cable 0 -flashsize 128"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
PRG-1001 : SPI Flash ID is:  ef
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m program_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1003 : finish command "bit_to_vec -chip EAGLE_20K_BGA256X -m program_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit" in  1.513658s wall, 1.406250s user + 0.187500s system = 1.593750s CPU (105.3%)

RUN-1004 : used memory is 910 MB, reserved memory is 958 MB, peak memory is 920 MB
RUN-1002 : start command "program_spi -cable 0 -spd 7"
RUN-1003 : finish command "program_spi -cable 0 -spd 7" in  11.584402s wall, 1.656250s user + 0.390625s system = 2.046875s CPU (17.7%)

RUN-1004 : used memory is 911 MB, reserved memory is 960 MB, peak memory is 920 MB
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m verify_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -spd 4"
RUN-1003 : finish command "program -cable 0 -spd 4" in  7.358533s wall, 0.406250s user + 0.515625s system = 0.921875s CPU (12.5%)

RUN-1004 : used memory is 728 MB, reserved memory is 776 MB, peak memory is 920 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode program_spi -v -spd 7 -sec 64 -cable 0 -flashsize 128" in  23.183980s wall, 4.984375s user + 1.187500s system = 6.171875s CPU (26.6%)

RUN-1004 : used memory is 728 MB, reserved memory is 776 MB, peak memory is 920 MB
GUI-1001 : Downloading succeeded!
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1002 : start command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode jtag -spd 7 -sec 64 -cable 0"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.470618s wall, 0.015625s user + 0.093750s system = 0.109375s CPU (7.4%)

RUN-1004 : used memory is 737 MB, reserved memory is 785 MB, peak memory is 920 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode jtag -spd 7 -sec 64 -cable 0" in  1.659979s wall, 0.093750s user + 0.109375s system = 0.203125s CPU (12.2%)

RUN-1004 : used memory is 737 MB, reserved memory is 785 MB, peak memory is 920 MB
GUI-1001 : Downloading succeeded!
RUN-1002 : start command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode jtag -spd 7 -sec 64 -cable 0"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m jtag_burst -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -mode svf -spd 7 -p"
RUN-1003 : finish command "program -cable 0 -mode svf -spd 7 -p" in  1.465160s wall, 0.046875s user + 0.062500s system = 0.109375s CPU (7.5%)

RUN-1004 : used memory is 739 MB, reserved memory is 787 MB, peak memory is 920 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode jtag -spd 7 -sec 64 -cable 0" in  1.622721s wall, 0.125000s user + 0.078125s system = 0.203125s CPU (12.5%)

RUN-1004 : used memory is 739 MB, reserved memory is 787 MB, peak memory is 920 MB
GUI-1001 : Downloading succeeded!
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.225561s wall, 1.187500s user + 0.031250s system = 1.218750s CPU (99.4%)

RUN-1004 : used memory is 749 MB, reserved memory is 797 MB, peak memory is 920 MB
RUN-1002 : start command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode program_spi -v -spd 7 -sec 64 -cable 0 -flashsize 128"
PRG-2014 : Chip validation success: EAGLE_20K_BGA256X
PRG-1001 : SPI Flash ID is:  ef
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m program_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1003 : finish command "bit_to_vec -chip EAGLE_20K_BGA256X -m program_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit" in  1.635568s wall, 1.625000s user + 0.015625s system = 1.640625s CPU (100.3%)

RUN-1004 : used memory is 923 MB, reserved memory is 980 MB, peak memory is 934 MB
RUN-1002 : start command "program_spi -cable 0 -spd 7"
RUN-1003 : finish command "program_spi -cable 0 -spd 7" in  12.063916s wall, 1.921875s user + 0.812500s system = 2.734375s CPU (22.7%)

RUN-1004 : used memory is 925 MB, reserved memory is 982 MB, peak memory is 934 MB
RUN-1002 : start command "bit_to_vec -chip EAGLE_20K_BGA256X -m verify_spi -freq 4 -bit IFOG501_2B_Runs/phy_1/IFOG501_2B.bit"
RUN-1002 : start command "program -cable 0 -spd 4"
RUN-1003 : finish command "program -cable 0 -spd 4" in  7.153548s wall, 0.281250s user + 0.421875s system = 0.703125s CPU (9.8%)

RUN-1004 : used memory is 735 MB, reserved memory is 786 MB, peak memory is 934 MB
RUN-1003 : finish command "download -bit IFOG501_2B_Runs\phy_1\IFOG501_2B.bit -mode program_spi -v -spd 7 -sec 64 -cable 0 -flashsize 128" in  23.598220s wall, 5.406250s user + 1.343750s system = 6.750000s CPU (28.6%)

RUN-1004 : used memory is 735 MB, reserved memory is 786 MB, peak memory is 934 MB
GUI-1001 : Downloading succeeded!
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.889898s wall, 1.296875s user + 0.546875s system = 1.843750s CPU (97.6%)

RUN-1004 : used memory is 540 MB, reserved memory is 779 MB, peak memory is 934 MB
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.266871s wall, 1.218750s user + 0.046875s system = 1.265625s CPU (99.9%)

RUN-1004 : used memory is 551 MB, reserved memory is 783 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.329639s wall, 1.250000s user + 0.156250s system = 1.406250s CPU (105.8%)

RUN-1004 : used memory is 545 MB, reserved memory is 786 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.241146s wall, 1.218750s user + 0.125000s system = 1.343750s CPU (108.3%)

RUN-1004 : used memory is 554 MB, reserved memory is 792 MB, peak memory is 934 MB
TMR-3509 : Import timing summary.
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.246074s wall, 1.203125s user + 0.078125s system = 1.281250s CPU (102.8%)

RUN-1004 : used memory is 555 MB, reserved memory is 789 MB, peak memory is 934 MB
TMR-3509 : Import timing summary.
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 2 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.514097s wall, 1.031250s user + 0.546875s system = 1.578125s CPU (104.2%)

RUN-1004 : used memory is 545 MB, reserved memory is 788 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 6 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.205255s wall, 1.187500s user + 0.062500s system = 1.250000s CPU (103.7%)

RUN-1004 : used memory is 555 MB, reserved memory is 793 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.237280s wall, 1.234375s user + 0.046875s system = 1.281250s CPU (103.6%)

RUN-1004 : used memory is 570 MB, reserved memory is 800 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.266717s wall, 1.265625s user + 0.062500s system = 1.328125s CPU (104.8%)

RUN-1004 : used memory is 573 MB, reserved memory is 804 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 3 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.314453s wall, 1.359375s user + 0.062500s system = 1.421875s CPU (108.2%)

RUN-1004 : used memory is 578 MB, reserved memory is 809 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.336626s wall, 1.328125s user + 0.031250s system = 1.359375s CPU (101.7%)

RUN-1004 : used memory is 589 MB, reserved memory is 816 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.211371s wall, 1.140625s user + 0.046875s system = 1.187500s CPU (98.0%)

RUN-1004 : used memory is 589 MB, reserved memory is 815 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.198935s wall, 1.187500s user + 0.031250s system = 1.218750s CPU (101.7%)

RUN-1004 : used memory is 598 MB, reserved memory is 820 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 5 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.204323s wall, 1.203125s user + 0.078125s system = 1.281250s CPU (106.4%)

RUN-1004 : used memory is 592 MB, reserved memory is 815 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.204708s wall, 1.187500s user + 0.078125s system = 1.265625s CPU (105.1%)

RUN-1004 : used memory is 601 MB, reserved memory is 820 MB, peak memory is 934 MB
HDL-1007 : analyze verilog file Src_al/Modulation.v
HDL-5007 WARNING: 'd' is not declared in Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in Src_al/IFOG501_2B.v(81)
RUN-1001 : reset_run syn_1 phy_1.
GUI-6001 WARNING: File D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B.bit does not exist!
RUN-1001 : launch_runs syn_1 phy_1  -jobs 6.
RUN-1001 : syn_1: run complete.
RUN-1001 : phy_1: run complete.
RUN-1001 : open_run phy_1.
RUN-1002 : start command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : eco open net = 0
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1003 : finish command "import_db D:/Project/IFOG501_2B_20M_PLL_256/IFOG501_2B_Runs/phy_1/IFOG501_2B_pr.db" in  1.203384s wall, 1.171875s user + 0.078125s system = 1.250000s CPU (103.9%)

RUN-1004 : used memory is 604 MB, reserved memory is 825 MB, peak memory is 934 MB
