============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Sep  6 11:51:36 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1642 instances
RUN-0007 : 376 luts, 991 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2212 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1653 nets have 2 pins
RUN-1001 : 448 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1640 instances, 376 luts, 991 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7821, tnet num: 2210, tinst num: 1640, tnode num: 11059, tedge num: 13206.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2210 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.277412s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 591939
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1640.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 463233, overlap = 20.25
PHY-3002 : Step(2): len = 351876, overlap = 13.5
PHY-3002 : Step(3): len = 323887, overlap = 20.25
PHY-3002 : Step(4): len = 311858, overlap = 18
PHY-3002 : Step(5): len = 304498, overlap = 20.25
PHY-3002 : Step(6): len = 295624, overlap = 18
PHY-3002 : Step(7): len = 291152, overlap = 18
PHY-3002 : Step(8): len = 284052, overlap = 18
PHY-3002 : Step(9): len = 278146, overlap = 18
PHY-3002 : Step(10): len = 271733, overlap = 20.25
PHY-3002 : Step(11): len = 267402, overlap = 20.25
PHY-3002 : Step(12): len = 259756, overlap = 20.25
PHY-3002 : Step(13): len = 255197, overlap = 20.25
PHY-3002 : Step(14): len = 250193, overlap = 20.25
PHY-3002 : Step(15): len = 243939, overlap = 20.25
PHY-3002 : Step(16): len = 236999, overlap = 20.25
PHY-3002 : Step(17): len = 234370, overlap = 20.25
PHY-3002 : Step(18): len = 225287, overlap = 20.25
PHY-3002 : Step(19): len = 218879, overlap = 20.25
PHY-3002 : Step(20): len = 215312, overlap = 20.25
PHY-3002 : Step(21): len = 210842, overlap = 20.25
PHY-3002 : Step(22): len = 201554, overlap = 20.25
PHY-3002 : Step(23): len = 198272, overlap = 20.25
PHY-3002 : Step(24): len = 194621, overlap = 20.25
PHY-3002 : Step(25): len = 187551, overlap = 18
PHY-3002 : Step(26): len = 177938, overlap = 18
PHY-3002 : Step(27): len = 175933, overlap = 18
PHY-3002 : Step(28): len = 171503, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.75124e-05
PHY-3002 : Step(29): len = 172167, overlap = 15.75
PHY-3002 : Step(30): len = 170854, overlap = 13.5
PHY-3002 : Step(31): len = 166599, overlap = 15.75
PHY-3002 : Step(32): len = 162221, overlap = 9
PHY-3002 : Step(33): len = 158121, overlap = 11.25
PHY-3002 : Step(34): len = 156193, overlap = 9
PHY-3002 : Step(35): len = 151255, overlap = 13.5
PHY-3002 : Step(36): len = 148569, overlap = 4.5
PHY-3002 : Step(37): len = 144906, overlap = 6.75
PHY-3002 : Step(38): len = 139452, overlap = 9
PHY-3002 : Step(39): len = 133062, overlap = 11.25
PHY-3002 : Step(40): len = 132109, overlap = 11.25
PHY-3002 : Step(41): len = 127760, overlap = 6.75
PHY-3002 : Step(42): len = 122134, overlap = 11.25
PHY-3002 : Step(43): len = 118974, overlap = 11.25
PHY-3002 : Step(44): len = 117281, overlap = 11.25
PHY-3002 : Step(45): len = 114811, overlap = 11.25
PHY-3002 : Step(46): len = 113564, overlap = 11.25
PHY-3002 : Step(47): len = 110683, overlap = 11.25
PHY-3002 : Step(48): len = 106320, overlap = 6.75
PHY-3002 : Step(49): len = 102829, overlap = 4.5
PHY-3002 : Step(50): len = 101800, overlap = 4.5
PHY-3002 : Step(51): len = 99947.3, overlap = 6.75
PHY-3002 : Step(52): len = 97008.9, overlap = 13.5
PHY-3002 : Step(53): len = 95993.6, overlap = 11.25
PHY-3002 : Step(54): len = 94892.7, overlap = 6.75
PHY-3002 : Step(55): len = 94130.6, overlap = 6.75
PHY-3002 : Step(56): len = 90887.8, overlap = 4.5
PHY-3002 : Step(57): len = 89182.8, overlap = 6.75
PHY-3002 : Step(58): len = 87564.3, overlap = 4.5
PHY-3002 : Step(59): len = 86432.8, overlap = 4.5
PHY-3002 : Step(60): len = 84176.7, overlap = 11.25
PHY-3002 : Step(61): len = 83690, overlap = 11.25
PHY-3002 : Step(62): len = 82611.4, overlap = 6.75
PHY-3002 : Step(63): len = 81571.7, overlap = 9
PHY-3002 : Step(64): len = 80333.5, overlap = 4.5
PHY-3002 : Step(65): len = 79347, overlap = 9
PHY-3002 : Step(66): len = 78416.2, overlap = 6.75
PHY-3002 : Step(67): len = 77641.2, overlap = 11.25
PHY-3002 : Step(68): len = 76717.3, overlap = 15.75
PHY-3002 : Step(69): len = 75321.3, overlap = 6.75
PHY-3002 : Step(70): len = 74001.7, overlap = 6.75
PHY-3002 : Step(71): len = 73430.4, overlap = 6.75
PHY-3002 : Step(72): len = 71887.3, overlap = 9
PHY-3002 : Step(73): len = 71115.1, overlap = 9
PHY-3002 : Step(74): len = 68728.3, overlap = 11.25
PHY-3002 : Step(75): len = 66690.6, overlap = 11.25
PHY-3002 : Step(76): len = 65942.2, overlap = 11.25
PHY-3002 : Step(77): len = 65363.2, overlap = 13.5
PHY-3002 : Step(78): len = 64900.2, overlap = 4.5
PHY-3002 : Step(79): len = 64422.9, overlap = 4.5
PHY-3002 : Step(80): len = 63602.5, overlap = 4.5
PHY-3002 : Step(81): len = 61999.7, overlap = 11.25
PHY-3002 : Step(82): len = 61315.3, overlap = 11.25
PHY-3002 : Step(83): len = 60803.7, overlap = 11.25
PHY-3002 : Step(84): len = 60150.2, overlap = 11.25
PHY-3002 : Step(85): len = 59796.3, overlap = 11.25
PHY-3002 : Step(86): len = 59665.8, overlap = 11.25
PHY-3002 : Step(87): len = 58533.9, overlap = 11.25
PHY-3002 : Step(88): len = 57528.4, overlap = 11.25
PHY-3002 : Step(89): len = 57043.2, overlap = 13.5
PHY-3002 : Step(90): len = 56647.6, overlap = 13.5
PHY-3002 : Step(91): len = 56423.9, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000195025
PHY-3002 : Step(92): len = 56495, overlap = 11.25
PHY-3002 : Step(93): len = 56489, overlap = 11.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00039005
PHY-3002 : Step(94): len = 56504.1, overlap = 9
PHY-3002 : Step(95): len = 56594.9, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006412s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2210 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062488s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(96): len = 60345.2, overlap = 5.375
PHY-3002 : Step(97): len = 59222.4, overlap = 4.875
PHY-3002 : Step(98): len = 58701.2, overlap = 4.125
PHY-3002 : Step(99): len = 57902.6, overlap = 4.625
PHY-3002 : Step(100): len = 57688.6, overlap = 5.375
PHY-3002 : Step(101): len = 56461.6, overlap = 5.3125
PHY-3002 : Step(102): len = 55475.1, overlap = 5.4375
PHY-3002 : Step(103): len = 54392, overlap = 5.375
PHY-3002 : Step(104): len = 53901.5, overlap = 5.25
PHY-3002 : Step(105): len = 51903.5, overlap = 4.75
PHY-3002 : Step(106): len = 51095.7, overlap = 4.4375
PHY-3002 : Step(107): len = 50721.9, overlap = 4.8125
PHY-3002 : Step(108): len = 50246.4, overlap = 5.0625
PHY-3002 : Step(109): len = 49646.3, overlap = 6.625
PHY-3002 : Step(110): len = 49504.3, overlap = 8.0625
PHY-3002 : Step(111): len = 49028.7, overlap = 9.9375
PHY-3002 : Step(112): len = 48449, overlap = 15.375
PHY-3002 : Step(113): len = 47242.5, overlap = 18.4375
PHY-3002 : Step(114): len = 46587.9, overlap = 18.9688
PHY-3002 : Step(115): len = 46077, overlap = 18.875
PHY-3002 : Step(116): len = 45620.3, overlap = 19.5625
PHY-3002 : Step(117): len = 45166.1, overlap = 20.3125
PHY-3002 : Step(118): len = 44775.3, overlap = 20.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000355741
PHY-3002 : Step(119): len = 44630.4, overlap = 19.8125
PHY-3002 : Step(120): len = 44687, overlap = 19.75
PHY-3002 : Step(121): len = 44707.2, overlap = 19.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000711481
PHY-3002 : Step(122): len = 44581.5, overlap = 19.8125
PHY-3002 : Step(123): len = 44581.5, overlap = 19.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2210 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.075920s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (102.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.4077e-05
PHY-3002 : Step(124): len = 45564.8, overlap = 69.125
PHY-3002 : Step(125): len = 46259.3, overlap = 60.9062
PHY-3002 : Step(126): len = 46221.1, overlap = 54.0625
PHY-3002 : Step(127): len = 45896, overlap = 56.2188
PHY-3002 : Step(128): len = 45924.5, overlap = 55.9062
PHY-3002 : Step(129): len = 45851, overlap = 56.2188
PHY-3002 : Step(130): len = 45843.7, overlap = 54
PHY-3002 : Step(131): len = 45965, overlap = 53.875
PHY-3002 : Step(132): len = 46163.5, overlap = 52.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000128154
PHY-3002 : Step(133): len = 46265.3, overlap = 51.1562
PHY-3002 : Step(134): len = 46443.3, overlap = 49.9688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000256308
PHY-3002 : Step(135): len = 47159.5, overlap = 47.8438
PHY-3002 : Step(136): len = 47658.9, overlap = 44.2812
PHY-3002 : Step(137): len = 48612.8, overlap = 36.0625
PHY-3002 : Step(138): len = 48407, overlap = 34.375
PHY-3002 : Step(139): len = 48223.3, overlap = 32.9062
PHY-3002 : Step(140): len = 48223.3, overlap = 32.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7821, tnet num: 2210, tinst num: 1640, tnode num: 11059, tedge num: 13206.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 89.81 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2212.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52488, over cnt = 259(0%), over = 1149, worst = 22
PHY-1001 : End global iterations;  0.077343s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (80.8%)

PHY-1001 : Congestion index: top1 = 45.13, top5 = 26.08, top10 = 16.29, top15 = 11.46.
PHY-1001 : End incremental global routing;  0.128969s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (84.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2210 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071360s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.5%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1601 has valid locations, 7 needs to be replaced
PHY-3001 : design contains 1646 instances, 376 luts, 997 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 48354.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7845, tnet num: 2216, tinst num: 1646, tnode num: 11101, tedge num: 13242.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.311839s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(141): len = 48545.1, overlap = 2
PHY-3002 : Step(142): len = 48584.8, overlap = 2
PHY-3002 : Step(143): len = 48581.7, overlap = 2.0625
PHY-3002 : Step(144): len = 48583.6, overlap = 2.0625
PHY-3002 : Step(145): len = 48583.6, overlap = 2.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065035s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000192038
PHY-3002 : Step(146): len = 48613.4, overlap = 32.8438
PHY-3002 : Step(147): len = 48613.4, overlap = 32.8438
PHY-3001 : Final: Len = 48613.4, Over = 32.8438
PHY-3001 : End incremental placement;  0.472691s wall, 0.484375s user + 0.031250s system = 0.515625s CPU (109.1%)

OPT-1001 : Total overflow 89.75 peak overflow 3.09
OPT-1001 : End high-fanout net optimization;  0.709483s wall, 0.687500s user + 0.046875s system = 0.734375s CPU (103.5%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1666/2218.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52800, over cnt = 260(0%), over = 1147, worst = 22
PHY-1002 : len = 60328, over cnt = 195(0%), over = 510, worst = 18
PHY-1002 : len = 66368, over cnt = 51(0%), over = 54, worst = 2
PHY-1002 : len = 67160, over cnt = 11(0%), over = 11, worst = 1
PHY-1002 : len = 67536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.095100s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (131.4%)

PHY-1001 : Congestion index: top1 = 40.73, top5 = 26.02, top10 = 18.79, top15 = 13.76.
OPT-1001 : End congestion update;  0.139792s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (123.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072674s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.215209s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (116.2%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : End physical optimization;  1.201085s wall, 1.203125s user + 0.046875s system = 1.250000s CPU (104.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 376 LUT to BLE ...
SYN-4008 : Packed 376 LUT and 188 SEQ to BLE.
SYN-4003 : Packing 809 remaining SEQ's ...
SYN-4005 : Packed 91 SEQ with LUT/SLICE
SYN-4006 : 116 single LUT's are left
SYN-4006 : 718 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1094/1427 primitive instances ...
PHY-3001 : End packing;  0.053945s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 849 instances
RUN-1001 : 400 mslices, 400 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2045 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1491 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 847 instances, 800 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 48804.6, Over = 64
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2043, tinst num: 847, tnode num: 8986, tedge num: 11620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.322293s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.02807e-05
PHY-3002 : Step(148): len = 48198.3, overlap = 64.25
PHY-3002 : Step(149): len = 48108.8, overlap = 63.75
PHY-3002 : Step(150): len = 47914.7, overlap = 66.5
PHY-3002 : Step(151): len = 47890.9, overlap = 67.5
PHY-3002 : Step(152): len = 47989, overlap = 66
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.05614e-05
PHY-3002 : Step(153): len = 48119.9, overlap = 66.75
PHY-3002 : Step(154): len = 48775, overlap = 63.75
PHY-3002 : Step(155): len = 49436.3, overlap = 60.75
PHY-3002 : Step(156): len = 49480.7, overlap = 61
PHY-3002 : Step(157): len = 49559.9, overlap = 58.75
PHY-3002 : Step(158): len = 49695.4, overlap = 57.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 8.11229e-05
PHY-3002 : Step(159): len = 50192.7, overlap = 56.25
PHY-3002 : Step(160): len = 51330.5, overlap = 50.25
PHY-3002 : Step(161): len = 51774.3, overlap = 49.25
PHY-3002 : Step(162): len = 51785.2, overlap = 48
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.110079s wall, 0.046875s user + 0.062500s system = 0.109375s CPU (99.4%)

PHY-3001 : Trial Legalized: Len = 66087.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051722s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000502318
PHY-3002 : Step(163): len = 63388.9, overlap = 6
PHY-3002 : Step(164): len = 61119.1, overlap = 11.75
PHY-3002 : Step(165): len = 59302.4, overlap = 18.25
PHY-3002 : Step(166): len = 58368.1, overlap = 22.25
PHY-3002 : Step(167): len = 57825.3, overlap = 23.5
PHY-3002 : Step(168): len = 57557, overlap = 24.75
PHY-3002 : Step(169): len = 57214.7, overlap = 26.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00100464
PHY-3002 : Step(170): len = 57693.5, overlap = 24.25
PHY-3002 : Step(171): len = 57876.1, overlap = 25
PHY-3002 : Step(172): len = 57876.1, overlap = 25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00200927
PHY-3002 : Step(173): len = 58055.3, overlap = 25
PHY-3002 : Step(174): len = 58123.5, overlap = 25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005549s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62695.7, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005802s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (269.3%)

PHY-3001 : 16 instances has been re-located, deltaX = 6, deltaY = 14, maxDist = 2.
PHY-3001 : Final: Len = 62821.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2043, tinst num: 847, tnode num: 8986, tedge num: 11620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 34/2045.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68776, over cnt = 145(0%), over = 215, worst = 7
PHY-1002 : len = 69872, over cnt = 64(0%), over = 69, worst = 2
PHY-1002 : len = 70608, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 70680, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 70744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.144331s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (119.1%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 22.66, top10 = 17.73, top15 = 14.09.
PHY-1001 : End incremental global routing;  0.198280s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (118.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059378s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.289750s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (113.2%)

OPT-1001 : Current memory(MB): used = 222, reserve = 188, peak = 224.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1801/2045.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008943s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (174.7%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 22.66, top10 = 17.73, top15 = 14.09.
OPT-1001 : End congestion update;  0.059321s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.4%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053302s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.114275s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (95.7%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051374s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1801/2045.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70744, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007897s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (197.9%)

PHY-1001 : Congestion index: top1 = 31.44, top5 = 22.66, top10 = 17.73, top15 = 14.09.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057404s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.864061s wall, 0.890625s user + 0.000000s system = 0.890625s CPU (103.1%)

RUN-1003 : finish command "place" in  6.458102s wall, 9.453125s user + 3.140625s system = 12.593750s CPU (195.0%)

RUN-1004 : used memory is 204 MB, reserved memory is 168 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 849 instances
RUN-1001 : 400 mslices, 400 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2045 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1491 nets have 2 pins
RUN-1001 : 441 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2043, tinst num: 847, tnode num: 8986, tedge num: 11620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 400 mslices, 400 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68408, over cnt = 148(0%), over = 218, worst = 7
PHY-1002 : len = 69600, over cnt = 64(0%), over = 70, worst = 3
PHY-1002 : len = 70336, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 70488, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.137595s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (124.9%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 22.54, top10 = 17.63, top15 = 14.02.
PHY-1001 : End global routing;  0.187811s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (116.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 206, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 503, reserve = 472, peak = 503.
PHY-1001 : End build detailed router design. 3.285507s wall, 3.203125s user + 0.078125s system = 3.281250s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34656, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.338603s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (101.6%)

PHY-1001 : Current memory(MB): used = 537, reserve = 505, peak = 537.
PHY-1001 : End phase 1; 1.344432s wall, 1.343750s user + 0.015625s system = 1.359375s CPU (101.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180040, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 538, reserve = 506, peak = 538.
PHY-1001 : End initial routed; 1.109491s wall, 1.875000s user + 0.156250s system = 2.031250s CPU (183.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1808(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.566   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.379061s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 539, reserve = 507, peak = 539.
PHY-1001 : End phase 2; 1.488659s wall, 2.250000s user + 0.156250s system = 2.406250s CPU (161.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180040, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015680s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (99.6%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179872, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028753s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (217.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179936, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.029506s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.9%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 179936, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.019102s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (163.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1808(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.566   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.372666s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (100.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.169132s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (92.4%)

PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End phase 3; 0.752011s wall, 0.765625s user + 0.015625s system = 0.781250s CPU (103.9%)

PHY-1003 : Routed, final wirelength = 179936
PHY-1001 : Current memory(MB): used = 552, reserve = 521, peak = 552.
PHY-1001 : End export database. 0.009976s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.067693s wall, 7.765625s user + 0.265625s system = 8.031250s CPU (113.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2043, tinst num: 847, tnode num: 8986, tedge num: 11620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.978955s wall, 8.687500s user + 0.281250s system = 8.968750s CPU (112.4%)

RUN-1004 : used memory is 505 MB, reserved memory is 473 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      834   out of  19600    4.26%
#reg                     1080   out of  19600    5.51%
#le                      1552
  #lut only               472   out of   1552   30.41%
  #reg only               718   out of   1552   46.26%
  #lut&reg                362   out of   1552   23.32%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    45
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1552   |608     |226     |1111    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1148   |301     |134     |929     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |36     |23      |7       |26      |0       |0       |
|    demodu                  |Demodulation                                     |545    |121     |58      |435     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |137    |18      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |87     |33      |21      |83      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |82      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |110    |98      |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |23     |19      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |51     |51      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |68      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1455  
    #2          2       312   
    #3          3       106   
    #4          4        23   
    #5        5-10       75   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2043, tinst num: 847, tnode num: 8986, tedge num: 11620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2043 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 847
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2045, pip num: 14772
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1290 valid insts, and 39195 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.271214s wall, 18.187500s user + 0.109375s system = 18.296875s CPU (559.3%)

RUN-1004 : used memory is 520 MB, reserved memory is 487 MB, peak memory is 667 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230906_115136.log"
