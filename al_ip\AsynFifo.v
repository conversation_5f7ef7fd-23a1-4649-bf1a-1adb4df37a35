/************************************************************\
 **     Copyright (c) 2012-2023 Anlogic Inc.
 **  All Right Reserved.\
\************************************************************/
/************************************************************\
 ** Log	:	This file is generated by Anlogic IP Generator.
 ** File	:	D:/GitProject/GitProject/Anlogic/IFOG501_2B/al_ip/AsynFifo.v
 ** Date	:	2023 04 19
 ** TD version	:	5.6.69102
\************************************************************/

`timescale 1ns / 1ps

module AsynFifo (
	rst,
	di, clkw, we,
	do, clkr, re,
	empty_flag,
	full_flag 
);

	input rst;
	input [55:0] di;
	input clkw, we;
	input clkr,re;

	output [55:0] do;
	output empty_flag;
	output full_flag;

EG_LOGIC_FIFO #(
 	.DATA_WIDTH_W(56),
	.DATA_WIDTH_R(56),
	.DATA_DEPTH_W(512),
	.DATA_DEPTH_R(512),
	.ENDIAN("BIG"),
	.RESETMODE("ASYNC"),
	.E(0),
	.F(512),
	.ASYNC_RESET_RELEASE("SYNC"))
fifo_inst(
	.rst(rst),
	.di(di),
	.clkw(clkw),
	.we(we),
	.csw(3'b111),
	.do(do),
	.clkr(clkr),
	.re(re),
	.csr(3'b111),
	.ore(1'b0),
	.empty_flag(empty_flag),
	.aempty_flag(),
	.full_flag(full_flag),
	.afull_flag()

);

endmodule