============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Aug  7 18:25:58 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1624 instances
RUN-0007 : 369 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2194 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1632 nets have 2 pins
RUN-1001 : 448 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1622 instances, 369 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2192, tinst num: 1622, tnode num: 11021, tedge num: 13163.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.271819s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (103.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 596786
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1622.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 476352, overlap = 20.25
PHY-3002 : Step(2): len = 440427, overlap = 20.25
PHY-3002 : Step(3): len = 395761, overlap = 20.25
PHY-3002 : Step(4): len = 369074, overlap = 20.25
PHY-3002 : Step(5): len = 360107, overlap = 20.25
PHY-3002 : Step(6): len = 350795, overlap = 20.25
PHY-3002 : Step(7): len = 330278, overlap = 18
PHY-3002 : Step(8): len = 301136, overlap = 20.25
PHY-3002 : Step(9): len = 296724, overlap = 20.25
PHY-3002 : Step(10): len = 291890, overlap = 20.25
PHY-3002 : Step(11): len = 279327, overlap = 20.25
PHY-3002 : Step(12): len = 267871, overlap = 20.25
PHY-3002 : Step(13): len = 264213, overlap = 20.25
PHY-3002 : Step(14): len = 255533, overlap = 20.25
PHY-3002 : Step(15): len = 238298, overlap = 20.25
PHY-3002 : Step(16): len = 232719, overlap = 20.25
PHY-3002 : Step(17): len = 228705, overlap = 20.25
PHY-3002 : Step(18): len = 216437, overlap = 20.25
PHY-3002 : Step(19): len = 211979, overlap = 20.25
PHY-3002 : Step(20): len = 208266, overlap = 20.25
PHY-3002 : Step(21): len = 204127, overlap = 20.25
PHY-3002 : Step(22): len = 200303, overlap = 20.25
PHY-3002 : Step(23): len = 195751, overlap = 20.25
PHY-3002 : Step(24): len = 190466, overlap = 20.25
PHY-3002 : Step(25): len = 186800, overlap = 20.25
PHY-3002 : Step(26): len = 182203, overlap = 20.25
PHY-3002 : Step(27): len = 177351, overlap = 20.25
PHY-3002 : Step(28): len = 173169, overlap = 20.25
PHY-3002 : Step(29): len = 169800, overlap = 20.25
PHY-3002 : Step(30): len = 163845, overlap = 20.25
PHY-3002 : Step(31): len = 158870, overlap = 20.25
PHY-3002 : Step(32): len = 156279, overlap = 20.25
PHY-3002 : Step(33): len = 150809, overlap = 20.25
PHY-3002 : Step(34): len = 141951, overlap = 20.25
PHY-3002 : Step(35): len = 139298, overlap = 20.25
PHY-3002 : Step(36): len = 136914, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000127651
PHY-3002 : Step(37): len = 139081, overlap = 13.5
PHY-3002 : Step(38): len = 138187, overlap = 13.5
PHY-3002 : Step(39): len = 136672, overlap = 11.25
PHY-3002 : Step(40): len = 133013, overlap = 15.75
PHY-3002 : Step(41): len = 129646, overlap = 15.75
PHY-3002 : Step(42): len = 125586, overlap = 9
PHY-3002 : Step(43): len = 122688, overlap = 11.25
PHY-3002 : Step(44): len = 120781, overlap = 15.75
PHY-3002 : Step(45): len = 116675, overlap = 13.5
PHY-3002 : Step(46): len = 114135, overlap = 11.25
PHY-3002 : Step(47): len = 110478, overlap = 13.5
PHY-3002 : Step(48): len = 109463, overlap = 11.25
PHY-3002 : Step(49): len = 106084, overlap = 13.5
PHY-3002 : Step(50): len = 103488, overlap = 11.25
PHY-3002 : Step(51): len = 101265, overlap = 13.5
PHY-3002 : Step(52): len = 98372.8, overlap = 11.25
PHY-3002 : Step(53): len = 97638.8, overlap = 13.5
PHY-3002 : Step(54): len = 94931.9, overlap = 9
PHY-3002 : Step(55): len = 93935.9, overlap = 9
PHY-3002 : Step(56): len = 92063.3, overlap = 15.75
PHY-3002 : Step(57): len = 89500.8, overlap = 18
PHY-3002 : Step(58): len = 87046.3, overlap = 13.5
PHY-3002 : Step(59): len = 86783.7, overlap = 13.5
PHY-3002 : Step(60): len = 85097.7, overlap = 11.25
PHY-3002 : Step(61): len = 82562, overlap = 11.25
PHY-3002 : Step(62): len = 80648.1, overlap = 11.25
PHY-3002 : Step(63): len = 79751.5, overlap = 9
PHY-3002 : Step(64): len = 77868.8, overlap = 9
PHY-3002 : Step(65): len = 77063.5, overlap = 12.1875
PHY-3002 : Step(66): len = 74976.4, overlap = 9.875
PHY-3002 : Step(67): len = 70844, overlap = 9.875
PHY-3002 : Step(68): len = 68828.6, overlap = 10.0625
PHY-3002 : Step(69): len = 68733.4, overlap = 12.3125
PHY-3002 : Step(70): len = 67625.2, overlap = 16.75
PHY-3002 : Step(71): len = 66717.4, overlap = 11.5625
PHY-3002 : Step(72): len = 62936.8, overlap = 9.375
PHY-3002 : Step(73): len = 62158.8, overlap = 11.75
PHY-3002 : Step(74): len = 61130.2, overlap = 9.8125
PHY-3002 : Step(75): len = 60663.6, overlap = 9.625
PHY-3002 : Step(76): len = 59604, overlap = 12
PHY-3002 : Step(77): len = 59053.3, overlap = 12.1875
PHY-3002 : Step(78): len = 58307.9, overlap = 14.375
PHY-3002 : Step(79): len = 57692.1, overlap = 15
PHY-3002 : Step(80): len = 56713.3, overlap = 10.625
PHY-3002 : Step(81): len = 56154.3, overlap = 8.4375
PHY-3002 : Step(82): len = 55523.3, overlap = 8.875
PHY-3002 : Step(83): len = 54541.1, overlap = 13.5625
PHY-3002 : Step(84): len = 53363.6, overlap = 11.25
PHY-3002 : Step(85): len = 52406.3, overlap = 16.3125
PHY-3002 : Step(86): len = 51918.1, overlap = 14.0625
PHY-3002 : Step(87): len = 51461, overlap = 14.25
PHY-3002 : Step(88): len = 51168.9, overlap = 14.375
PHY-3002 : Step(89): len = 50490.5, overlap = 11.5312
PHY-3002 : Step(90): len = 49775.1, overlap = 11.625
PHY-3002 : Step(91): len = 49362.4, overlap = 16.1875
PHY-3002 : Step(92): len = 49145.9, overlap = 16.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000255301
PHY-3002 : Step(93): len = 49111.8, overlap = 16.5625
PHY-3002 : Step(94): len = 49109.1, overlap = 14.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000510603
PHY-3002 : Step(95): len = 49212.9, overlap = 14.3125
PHY-3002 : Step(96): len = 49222.7, overlap = 14.3125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006339s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (246.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061268s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00111244
PHY-3002 : Step(97): len = 51418, overlap = 15.9375
PHY-3002 : Step(98): len = 50798.1, overlap = 15.8438
PHY-3002 : Step(99): len = 50535.6, overlap = 15.5938
PHY-3002 : Step(100): len = 50397.6, overlap = 15.6562
PHY-3002 : Step(101): len = 50066.6, overlap = 16.0625
PHY-3002 : Step(102): len = 49387.8, overlap = 16.0625
PHY-3002 : Step(103): len = 48675.9, overlap = 15.9375
PHY-3002 : Step(104): len = 48392.1, overlap = 16.2812
PHY-3002 : Step(105): len = 47936.6, overlap = 16.4688
PHY-3002 : Step(106): len = 47339.2, overlap = 18.1875
PHY-3002 : Step(107): len = 47108.4, overlap = 18.7812
PHY-3002 : Step(108): len = 46904.6, overlap = 18.875
PHY-3002 : Step(109): len = 46855.3, overlap = 18.875
PHY-3002 : Step(110): len = 46703.3, overlap = 18.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00222488
PHY-3002 : Step(111): len = 46680.6, overlap = 18.75
PHY-3002 : Step(112): len = 46548.5, overlap = 19.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00444977
PHY-3002 : Step(113): len = 46544.9, overlap = 19.125
PHY-3002 : Step(114): len = 46538, overlap = 19.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059975s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.04621e-05
PHY-3002 : Step(115): len = 46993.6, overlap = 62.375
PHY-3002 : Step(116): len = 47360.8, overlap = 61.625
PHY-3002 : Step(117): len = 48525.7, overlap = 50.1875
PHY-3002 : Step(118): len = 48958.1, overlap = 49.1875
PHY-3002 : Step(119): len = 49108.7, overlap = 51
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000180924
PHY-3002 : Step(120): len = 49071.7, overlap = 50.5
PHY-3002 : Step(121): len = 49429.3, overlap = 45.5
PHY-3002 : Step(122): len = 49545.6, overlap = 44.375
PHY-3002 : Step(123): len = 49556.2, overlap = 43.0938
PHY-3002 : Step(124): len = 49610.7, overlap = 42.3125
PHY-3002 : Step(125): len = 49503.5, overlap = 41.5625
PHY-3002 : Step(126): len = 49424, overlap = 40.5
PHY-3002 : Step(127): len = 49241.8, overlap = 39.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000361848
PHY-3002 : Step(128): len = 49441.3, overlap = 38.8438
PHY-3002 : Step(129): len = 49618.9, overlap = 38.6562
PHY-3002 : Step(130): len = 49950.1, overlap = 39.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000723696
PHY-3002 : Step(131): len = 50424.2, overlap = 37.4375
PHY-3002 : Step(132): len = 50891.9, overlap = 36.7812
PHY-3002 : Step(133): len = 51415.6, overlap = 33.7188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00144739
PHY-3002 : Step(134): len = 51507.3, overlap = 33.2812
PHY-3002 : Step(135): len = 51647.6, overlap = 34.0938
PHY-3002 : Step(136): len = 51759.3, overlap = 33.9375
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00289479
PHY-3002 : Step(137): len = 52092.1, overlap = 32.875
PHY-3002 : Step(138): len = 52284.5, overlap = 32.7812
PHY-3002 : Step(139): len = 52355.4, overlap = 32.5
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00578957
PHY-3002 : Step(140): len = 52305.4, overlap = 32.375
PHY-3002 : Step(141): len = 52284.1, overlap = 32.5312
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.0115791
PHY-3002 : Step(142): len = 52401.6, overlap = 32.2812
PHY-3002 : Step(143): len = 52480.9, overlap = 32.2188
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0231583
PHY-3002 : Step(144): len = 52523.5, overlap = 32.0938
PHY-3002 : Step(145): len = 52573.8, overlap = 32.0938
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7781, tnet num: 2192, tinst num: 1622, tnode num: 11021, tedge num: 13163.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 90.09 peak overflow 4.91
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2194.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56984, over cnt = 244(0%), over = 1086, worst = 17
PHY-1001 : End global iterations;  0.061835s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (176.9%)

PHY-1001 : Congestion index: top1 = 46.34, top5 = 25.79, top10 = 16.55, top15 = 11.85.
PHY-1001 : End incremental global routing;  0.113387s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (151.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066533s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.210346s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (126.3%)

OPT-1001 : Current memory(MB): used = 215, reserve = 179, peak = 215.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1702/2194.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56984, over cnt = 244(0%), over = 1086, worst = 17
PHY-1002 : len = 63720, over cnt = 173(0%), over = 432, worst = 11
PHY-1002 : len = 68024, over cnt = 58(0%), over = 77, worst = 4
PHY-1002 : len = 69304, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 69400, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.096137s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.5%)

PHY-1001 : Congestion index: top1 = 40.02, top5 = 25.77, top10 = 18.55, top15 = 13.76.
OPT-1001 : End congestion update;  0.138037s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2192 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056184s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.4%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196671s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 182, peak = 219.
OPT-1001 : End physical optimization;  0.674909s wall, 0.703125s user + 0.031250s system = 0.734375s CPU (108.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 92 SEQ with LUT/SLICE
SYN-4006 : 114 single LUT's are left
SYN-4006 : 711 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1080/1408 primitive instances ...
PHY-3001 : End packing;  0.047838s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 845 instances
RUN-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 843 instances, 796 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52680.6, Over = 59.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6588, tnet num: 2024, tinst num: 843, tnode num: 8957, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.302770s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.90737e-05
PHY-3002 : Step(146): len = 51687.3, overlap = 61
PHY-3002 : Step(147): len = 51025.5, overlap = 61.75
PHY-3002 : Step(148): len = 50622.6, overlap = 62.5
PHY-3002 : Step(149): len = 50459.5, overlap = 63.25
PHY-3002 : Step(150): len = 50366.1, overlap = 64.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.81474e-05
PHY-3002 : Step(151): len = 50761.7, overlap = 62.75
PHY-3002 : Step(152): len = 51138.4, overlap = 60.5
PHY-3002 : Step(153): len = 51890.2, overlap = 59
PHY-3002 : Step(154): len = 52545.2, overlap = 55.25
PHY-3002 : Step(155): len = 52586.9, overlap = 55
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000116295
PHY-3002 : Step(156): len = 52833.9, overlap = 54.5
PHY-3002 : Step(157): len = 53029.4, overlap = 54.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.066858s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (233.7%)

PHY-3001 : Trial Legalized: Len = 67030.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054861s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000471716
PHY-3002 : Step(158): len = 64043.9, overlap = 6.5
PHY-3002 : Step(159): len = 61252.2, overlap = 13
PHY-3002 : Step(160): len = 60012.2, overlap = 19
PHY-3002 : Step(161): len = 58968.8, overlap = 23.75
PHY-3002 : Step(162): len = 58580.9, overlap = 24.75
PHY-3002 : Step(163): len = 58390.7, overlap = 26.5
PHY-3002 : Step(164): len = 58079.6, overlap = 25.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000943432
PHY-3002 : Step(165): len = 58354.3, overlap = 25.25
PHY-3002 : Step(166): len = 58471.2, overlap = 24
PHY-3002 : Step(167): len = 58495.6, overlap = 24.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00188686
PHY-3002 : Step(168): len = 58646.5, overlap = 23.5
PHY-3002 : Step(169): len = 58714.3, overlap = 23.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004907s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63126.4, Over = 0
PHY-3001 : Spreading special nets. 11 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005546s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (281.7%)

PHY-3001 : 16 instances has been re-located, deltaX = 0, deltaY = 15, maxDist = 1.
PHY-3001 : Final: Len = 63236.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6588, tnet num: 2024, tinst num: 843, tnode num: 8957, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 93/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70112, over cnt = 144(0%), over = 214, worst = 6
PHY-1002 : len = 71384, over cnt = 51(0%), over = 54, worst = 3
PHY-1002 : len = 71728, over cnt = 24(0%), over = 25, worst = 2
PHY-1002 : len = 71952, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 72168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123846s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (113.5%)

PHY-1001 : Congestion index: top1 = 30.62, top5 = 22.53, top10 = 17.82, top15 = 14.17.
PHY-1001 : End incremental global routing;  0.174713s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (107.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066115s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (94.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.270905s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (103.8%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1795/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006145s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.62, top5 = 22.53, top10 = 17.82, top15 = 14.17.
OPT-1001 : End congestion update;  0.050241s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048800s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (128.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 805 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 843 instances, 796 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63285.6, Over = 0
PHY-3001 : End spreading;  0.005141s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63285.6, Over = 0
PHY-3001 : End incremental legalization;  0.035714s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (43.8%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.147412s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (95.4%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048693s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1788/2026.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72248, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72248, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.017948s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (87.1%)

PHY-1001 : Congestion index: top1 = 30.67, top5 = 22.54, top10 = 17.83, top15 = 14.19.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050010s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.877953s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (99.7%)

RUN-1003 : finish command "place" in  5.639531s wall, 9.015625s user + 2.890625s system = 11.906250s CPU (211.1%)

RUN-1004 : used memory is 204 MB, reserved memory is 167 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 845 instances
RUN-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2026 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6588, tnet num: 2024, tinst num: 843, tnode num: 8957, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69640, over cnt = 153(0%), over = 221, worst = 6
PHY-1002 : len = 70776, over cnt = 65(0%), over = 71, worst = 3
PHY-1002 : len = 71544, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 71736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.130526s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (119.7%)

PHY-1001 : Congestion index: top1 = 30.52, top5 = 22.40, top10 = 17.67, top15 = 14.06.
PHY-1001 : End global routing;  0.179825s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (113.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 203, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 498, reserve = 467, peak = 498.
PHY-1001 : End build detailed router design. 3.193926s wall, 3.140625s user + 0.062500s system = 3.203125s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34616, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.261885s wall, 1.250000s user + 0.000000s system = 1.250000s CPU (99.1%)

PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End phase 1; 1.267627s wall, 1.265625s user + 0.000000s system = 1.265625s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181960, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End initial routed; 1.071506s wall, 1.953125s user + 0.156250s system = 2.109375s CPU (196.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.388   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363480s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.2%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End phase 2; 1.435070s wall, 2.328125s user + 0.156250s system = 2.484375s CPU (173.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181960, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014299s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181984, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025296s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (185.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181992, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020493s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (76.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.388   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.359963s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.167663s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.5%)

PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End phase 3; 0.710004s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (101.2%)

PHY-1003 : Routed, final wirelength = 181992
PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End export database. 0.009560s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (163.4%)

PHY-1001 : End detail routing;  6.793901s wall, 7.593750s user + 0.265625s system = 7.859375s CPU (115.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6588, tnet num: 2024, tinst num: 843, tnode num: 8957, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.682906s wall, 8.515625s user + 0.265625s system = 8.781250s CPU (114.3%)

RUN-1004 : used memory is 501 MB, reserved memory is 468 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      818   out of  19600    4.17%
#reg                     1074   out of  19600    5.48%
#le                      1529
  #lut only               455   out of   1529   29.76%
  #reg only               711   out of   1529   46.50%
  #lut&reg                363   out of   1529   23.74%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    46
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1529   |597     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1119   |285     |128     |921     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |525    |123     |53      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |162    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |13      |0       |28      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |91     |22      |21      |87      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |82      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |112    |100     |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |23     |19      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |53     |53      |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |215    |170     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1435  
    #2          2       315   
    #3          3       103   
    #4          4        22   
    #5        5-10       78   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6588, tnet num: 2024, tinst num: 843, tnode num: 8957, tedge num: 11584.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2024 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 843
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2026, pip num: 14854
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1316 valid insts, and 39232 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.127449s wall, 18.718750s user + 0.078125s system = 18.796875s CPU (601.0%)

RUN-1004 : used memory is 548 MB, reserved memory is 514 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230807_182558.log"
