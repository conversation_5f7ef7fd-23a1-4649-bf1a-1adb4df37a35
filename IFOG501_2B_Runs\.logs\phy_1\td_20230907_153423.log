============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Sep  7 15:34:23 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(85)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 301 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 301 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6837587935232"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1511828488192"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net signal_process/clk driven by BUFG (820 clock/control pins, 1 other pins).
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4020 : Net clk_in_dup_1 is fbclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1646 instances
RUN-0007 : 378 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2216 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1656 nets have 2 pins
RUN-1001 : 444 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1644 instances, 378 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7843, tnet num: 2214, tinst num: 1644, tnode num: 11081, tedge num: 13242.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.278579s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 543769
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1644.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 489813, overlap = 20.25
PHY-3002 : Step(2): len = 408031, overlap = 13.5
PHY-3002 : Step(3): len = 337795, overlap = 13.5
PHY-3002 : Step(4): len = 330973, overlap = 15.75
PHY-3002 : Step(5): len = 322023, overlap = 15.75
PHY-3002 : Step(6): len = 315358, overlap = 18
PHY-3002 : Step(7): len = 310001, overlap = 20.25
PHY-3002 : Step(8): len = 302618, overlap = 20.25
PHY-3002 : Step(9): len = 296679, overlap = 20.25
PHY-3002 : Step(10): len = 290646, overlap = 20.25
PHY-3002 : Step(11): len = 284475, overlap = 20.25
PHY-3002 : Step(12): len = 278452, overlap = 20.25
PHY-3002 : Step(13): len = 272298, overlap = 20.25
PHY-3002 : Step(14): len = 266984, overlap = 20.25
PHY-3002 : Step(15): len = 260946, overlap = 20.25
PHY-3002 : Step(16): len = 255099, overlap = 20.25
PHY-3002 : Step(17): len = 250792, overlap = 20.25
PHY-3002 : Step(18): len = 245199, overlap = 20.25
PHY-3002 : Step(19): len = 240393, overlap = 20.25
PHY-3002 : Step(20): len = 235034, overlap = 20.25
PHY-3002 : Step(21): len = 229262, overlap = 20.25
PHY-3002 : Step(22): len = 224895, overlap = 20.25
PHY-3002 : Step(23): len = 220288, overlap = 20.25
PHY-3002 : Step(24): len = 215075, overlap = 20.25
PHY-3002 : Step(25): len = 209950, overlap = 20.25
PHY-3002 : Step(26): len = 206184, overlap = 20.25
PHY-3002 : Step(27): len = 201467, overlap = 20.25
PHY-3002 : Step(28): len = 194227, overlap = 20.25
PHY-3002 : Step(29): len = 190691, overlap = 20.25
PHY-3002 : Step(30): len = 187152, overlap = 20.25
PHY-3002 : Step(31): len = 180570, overlap = 20.25
PHY-3002 : Step(32): len = 175119, overlap = 20.25
PHY-3002 : Step(33): len = 173074, overlap = 20.25
PHY-3002 : Step(34): len = 163602, overlap = 20.25
PHY-3002 : Step(35): len = 142642, overlap = 20.25
PHY-3002 : Step(36): len = 139432, overlap = 20.25
PHY-3002 : Step(37): len = 137612, overlap = 20.25
PHY-3002 : Step(38): len = 125194, overlap = 20.25
PHY-3002 : Step(39): len = 120633, overlap = 20.25
PHY-3002 : Step(40): len = 119461, overlap = 20.25
PHY-3002 : Step(41): len = 118575, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000130787
PHY-3002 : Step(42): len = 119269, overlap = 9
PHY-3002 : Step(43): len = 118114, overlap = 11.25
PHY-3002 : Step(44): len = 116896, overlap = 15.75
PHY-3002 : Step(45): len = 116091, overlap = 15.75
PHY-3002 : Step(46): len = 115135, overlap = 9
PHY-3002 : Step(47): len = 112754, overlap = 6.75
PHY-3002 : Step(48): len = 109077, overlap = 9
PHY-3002 : Step(49): len = 104873, overlap = 6.75
PHY-3002 : Step(50): len = 104855, overlap = 6.75
PHY-3002 : Step(51): len = 102701, overlap = 6.75
PHY-3002 : Step(52): len = 101899, overlap = 6.75
PHY-3002 : Step(53): len = 100672, overlap = 9
PHY-3002 : Step(54): len = 98877.4, overlap = 9
PHY-3002 : Step(55): len = 98237, overlap = 6.75
PHY-3002 : Step(56): len = 96153, overlap = 6.75
PHY-3002 : Step(57): len = 95109.2, overlap = 4.5
PHY-3002 : Step(58): len = 93676.7, overlap = 4.5
PHY-3002 : Step(59): len = 92916.1, overlap = 6.75
PHY-3002 : Step(60): len = 90176.7, overlap = 6.75
PHY-3002 : Step(61): len = 87798.7, overlap = 9
PHY-3002 : Step(62): len = 85755.3, overlap = 6.75
PHY-3002 : Step(63): len = 85691.7, overlap = 6.75
PHY-3002 : Step(64): len = 83881.3, overlap = 6.75
PHY-3002 : Step(65): len = 80948.7, overlap = 9
PHY-3002 : Step(66): len = 79579.7, overlap = 6.75
PHY-3002 : Step(67): len = 78864.9, overlap = 6.75
PHY-3002 : Step(68): len = 77176.3, overlap = 4.5
PHY-3002 : Step(69): len = 76174.4, overlap = 9
PHY-3002 : Step(70): len = 75690.9, overlap = 6.75
PHY-3002 : Step(71): len = 75495.9, overlap = 4.5
PHY-3002 : Step(72): len = 74834.7, overlap = 4.5
PHY-3002 : Step(73): len = 74679.1, overlap = 6.75
PHY-3002 : Step(74): len = 74102.2, overlap = 6.75
PHY-3002 : Step(75): len = 73210.2, overlap = 6.75
PHY-3002 : Step(76): len = 72273.9, overlap = 6.75
PHY-3002 : Step(77): len = 70222.9, overlap = 9
PHY-3002 : Step(78): len = 69722.2, overlap = 9
PHY-3002 : Step(79): len = 68683.3, overlap = 11.25
PHY-3002 : Step(80): len = 66616.2, overlap = 9
PHY-3002 : Step(81): len = 64952.8, overlap = 6.75
PHY-3002 : Step(82): len = 64522.3, overlap = 6.75
PHY-3002 : Step(83): len = 63905.2, overlap = 9
PHY-3002 : Step(84): len = 63456.8, overlap = 6.75
PHY-3002 : Step(85): len = 63039.2, overlap = 11.25
PHY-3002 : Step(86): len = 62570.7, overlap = 11.25
PHY-3002 : Step(87): len = 61644.9, overlap = 13.5
PHY-3002 : Step(88): len = 60506.8, overlap = 11.25
PHY-3002 : Step(89): len = 60078.8, overlap = 11.25
PHY-3002 : Step(90): len = 59566.7, overlap = 11.25
PHY-3002 : Step(91): len = 58910.4, overlap = 13.5
PHY-3002 : Step(92): len = 58473.4, overlap = 13.5
PHY-3002 : Step(93): len = 57782.6, overlap = 11.25
PHY-3002 : Step(94): len = 57680.8, overlap = 11.25
PHY-3002 : Step(95): len = 57219, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000261574
PHY-3002 : Step(96): len = 57432.8, overlap = 9
PHY-3002 : Step(97): len = 57473.2, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000523148
PHY-3002 : Step(98): len = 57447.2, overlap = 9
PHY-3002 : Step(99): len = 57440.9, overlap = 9
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004411s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.079437s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (118.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(100): len = 61360.2, overlap = 4.875
PHY-3002 : Step(101): len = 60576, overlap = 4.75
PHY-3002 : Step(102): len = 59866.2, overlap = 4.625
PHY-3002 : Step(103): len = 58597.3, overlap = 4.3125
PHY-3002 : Step(104): len = 58209.1, overlap = 4.75
PHY-3002 : Step(105): len = 56943.3, overlap = 4.0625
PHY-3002 : Step(106): len = 56040.5, overlap = 4.1875
PHY-3002 : Step(107): len = 54952.1, overlap = 4.375
PHY-3002 : Step(108): len = 53919.6, overlap = 3.875
PHY-3002 : Step(109): len = 53237.7, overlap = 3.625
PHY-3002 : Step(110): len = 52970.5, overlap = 3.625
PHY-3002 : Step(111): len = 52500.2, overlap = 5.4375
PHY-3002 : Step(112): len = 52218.6, overlap = 7.0625
PHY-3002 : Step(113): len = 51779.3, overlap = 7.375
PHY-3002 : Step(114): len = 51670.3, overlap = 7.28125
PHY-3002 : Step(115): len = 51228.7, overlap = 7.59375
PHY-3002 : Step(116): len = 49934.7, overlap = 8.8125
PHY-3002 : Step(117): len = 48938.4, overlap = 8.0625
PHY-3002 : Step(118): len = 47704.4, overlap = 9.3125
PHY-3002 : Step(119): len = 46727.8, overlap = 15.0312
PHY-3002 : Step(120): len = 46443.5, overlap = 15.9062
PHY-3002 : Step(121): len = 46243.3, overlap = 15.8438
PHY-3002 : Step(122): len = 45983.5, overlap = 16.5625
PHY-3002 : Step(123): len = 45970.8, overlap = 16.6875
PHY-3002 : Step(124): len = 45810.7, overlap = 16.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000409035
PHY-3002 : Step(125): len = 45806, overlap = 16.5
PHY-3002 : Step(126): len = 45825.3, overlap = 17.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000818069
PHY-3002 : Step(127): len = 45645.7, overlap = 17.5938
PHY-3002 : Step(128): len = 45753.7, overlap = 18.0938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.076653s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.80666e-05
PHY-3002 : Step(129): len = 46057, overlap = 73.9062
PHY-3002 : Step(130): len = 46317.7, overlap = 66.5
PHY-3002 : Step(131): len = 47104.7, overlap = 65.25
PHY-3002 : Step(132): len = 48203.6, overlap = 63.0312
PHY-3002 : Step(133): len = 48420.6, overlap = 61.375
PHY-3002 : Step(134): len = 48269, overlap = 59.875
PHY-3002 : Step(135): len = 47873.6, overlap = 58
PHY-3002 : Step(136): len = 47492, overlap = 58.4062
PHY-3002 : Step(137): len = 47471.3, overlap = 58.5938
PHY-3002 : Step(138): len = 47404.7, overlap = 58.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000116133
PHY-3002 : Step(139): len = 47364.5, overlap = 55.7812
PHY-3002 : Step(140): len = 47939, overlap = 53.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000232266
PHY-3002 : Step(141): len = 48033, overlap = 53.4375
PHY-3002 : Step(142): len = 49878.7, overlap = 48.6875
PHY-3002 : Step(143): len = 51081.2, overlap = 45.0625
PHY-3002 : Step(144): len = 50973.4, overlap = 45.25
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000464533
PHY-3002 : Step(145): len = 50912.1, overlap = 45.5
PHY-3002 : Step(146): len = 50863.9, overlap = 31.0625
PHY-3002 : Step(147): len = 50863.9, overlap = 31.0625
PHY-3002 : Step(148): len = 51004.7, overlap = 34.6562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000929066
PHY-3002 : Step(149): len = 51369.2, overlap = 33.9688
PHY-3002 : Step(150): len = 51965.2, overlap = 29.875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00185813
PHY-3002 : Step(151): len = 52165.3, overlap = 27.25
PHY-3002 : Step(152): len = 52307.9, overlap = 26.6875
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00368333
PHY-3002 : Step(153): len = 52697.9, overlap = 26.75
PHY-3002 : Step(154): len = 52974.3, overlap = 26
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.00736666
PHY-3002 : Step(155): len = 52894.1, overlap = 25.7812
PHY-3002 : Step(156): len = 52894.1, overlap = 25.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7843, tnet num: 2214, tinst num: 1644, tnode num: 11081, tedge num: 13242.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 80.72 peak overflow 3.19
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2216.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56824, over cnt = 240(0%), over = 931, worst = 14
PHY-1001 : End global iterations;  0.101284s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (92.6%)

PHY-1001 : Congestion index: top1 = 41.31, top5 = 24.63, top10 = 16.35, top15 = 11.77.
PHY-1001 : End incremental global routing;  0.162897s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (86.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2214 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.112364s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (111.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1604 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1646 instances, 378 luts, 994 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 53253.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7851, tnet num: 2216, tinst num: 1646, tnode num: 11095, tedge num: 13254.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.385032s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(157): len = 53267.5, overlap = 1.46875
PHY-3002 : Step(158): len = 53273.5, overlap = 1.46875
PHY-3002 : Step(159): len = 53265.2, overlap = 1.53125
PHY-3002 : Step(160): len = 53265.2, overlap = 1.53125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063100s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00515991
PHY-3002 : Step(161): len = 53273.3, overlap = 25.9062
PHY-3002 : Step(162): len = 53273.3, overlap = 25.9062
PHY-3001 : Final: Len = 53273.3, Over = 25.9062
PHY-3001 : End incremental placement;  0.542848s wall, 0.515625s user + 0.046875s system = 0.562500s CPU (103.6%)

OPT-1001 : Total overflow 80.84 peak overflow 3.19
OPT-1001 : End high-fanout net optimization;  0.888766s wall, 0.828125s user + 0.062500s system = 0.890625s CPU (100.2%)

OPT-1001 : Current memory(MB): used = 221, reserve = 184, peak = 222.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1739/2218.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56960, over cnt = 240(0%), over = 926, worst = 14
PHY-1002 : len = 62360, over cnt = 151(0%), over = 370, worst = 11
PHY-1002 : len = 66152, over cnt = 21(0%), over = 21, worst = 1
PHY-1002 : len = 66424, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 66624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123997s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (113.4%)

PHY-1001 : Congestion index: top1 = 36.68, top5 = 24.91, top10 = 17.95, top15 = 13.40.
OPT-1001 : End congestion update;  0.194801s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2216 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.098338s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (111.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.299397s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (104.4%)

OPT-1001 : Current memory(MB): used = 220, reserve = 183, peak = 222.
OPT-1001 : End physical optimization;  1.564895s wall, 1.640625s user + 0.062500s system = 1.703125s CPU (108.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 378 LUT to BLE ...
SYN-4008 : Packed 378 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 108 single LUT's are left
SYN-4006 : 704 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1082/1416 primitive instances ...
PHY-3001 : End packing;  0.073666s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (84.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2044 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 841 instances, 793 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53234.4, Over = 50.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6585, tnet num: 2042, tinst num: 841, tnode num: 8910, tedge num: 11566.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.416653s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.00857e-05
PHY-3002 : Step(163): len = 52674.1, overlap = 49.25
PHY-3002 : Step(164): len = 52266.3, overlap = 49.25
PHY-3002 : Step(165): len = 51968.8, overlap = 53.5
PHY-3002 : Step(166): len = 51780.2, overlap = 56.25
PHY-3002 : Step(167): len = 51884.6, overlap = 57.25
PHY-3002 : Step(168): len = 51727, overlap = 56
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.01715e-05
PHY-3002 : Step(169): len = 52216.7, overlap = 54.5
PHY-3002 : Step(170): len = 52634.4, overlap = 50.25
PHY-3002 : Step(171): len = 53235.1, overlap = 49.25
PHY-3002 : Step(172): len = 53639.4, overlap = 48.75
PHY-3002 : Step(173): len = 54083.2, overlap = 46.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000120343
PHY-3002 : Step(174): len = 54344, overlap = 45.75
PHY-3002 : Step(175): len = 54344, overlap = 45.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.078006s wall, 0.062500s user + 0.093750s system = 0.156250s CPU (200.3%)

PHY-3001 : Trial Legalized: Len = 66152.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067957s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000354349
PHY-3002 : Step(176): len = 63356.4, overlap = 5.75
PHY-3002 : Step(177): len = 61338, overlap = 14.5
PHY-3002 : Step(178): len = 60044.7, overlap = 18.75
PHY-3002 : Step(179): len = 59327.3, overlap = 21
PHY-3002 : Step(180): len = 58939.5, overlap = 23
PHY-3002 : Step(181): len = 58767.3, overlap = 24.75
PHY-3002 : Step(182): len = 58539.4, overlap = 23.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000708698
PHY-3002 : Step(183): len = 59081.3, overlap = 22.5
PHY-3002 : Step(184): len = 59374.2, overlap = 22.25
PHY-3002 : Step(185): len = 59352.7, overlap = 22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0014174
PHY-3002 : Step(186): len = 59582.7, overlap = 21.75
PHY-3002 : Step(187): len = 59735.7, overlap = 21
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007132s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (219.1%)

PHY-3001 : Legalized: Len = 63477.4, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005604s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 5, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 63559.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6585, tnet num: 2042, tinst num: 841, tnode num: 8910, tedge num: 11566.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 200/2044.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70624, over cnt = 140(0%), over = 210, worst = 8
PHY-1002 : len = 71384, over cnt = 71(0%), over = 94, worst = 4
PHY-1002 : len = 72272, over cnt = 7(0%), over = 10, worst = 4
PHY-1002 : len = 72392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.149797s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.3%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 22.88, top10 = 18.04, top15 = 14.16.
PHY-1001 : End incremental global routing;  0.222215s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (105.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.090280s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (103.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.365623s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.6%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1801/2044.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006328s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (246.9%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 22.88, top10 = 18.04, top15 = 14.16.
OPT-1001 : End congestion update;  0.084459s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (111.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063084s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 802 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 841 instances, 793 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63636.4, Over = 0
PHY-3001 : End spreading;  0.005075s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (307.9%)

PHY-3001 : Final: Len = 63636.4, Over = 0
PHY-3001 : End incremental legalization;  0.057300s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.1%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.219513s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (99.7%)

OPT-1001 : Current memory(MB): used = 227, reserve = 191, peak = 228.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060083s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1791/2044.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015338s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.9%)

PHY-1001 : Congestion index: top1 = 32.16, top5 = 22.88, top10 = 18.03, top15 = 14.17.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.079033s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (98.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.724138
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.144041s wall, 1.156250s user + 0.000000s system = 1.156250s CPU (101.1%)

RUN-1003 : finish command "place" in  7.150699s wall, 10.734375s user + 3.421875s system = 14.156250s CPU (198.0%)

RUN-1004 : used memory is 210 MB, reserved memory is 173 MB, peak memory is 228 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2044 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1489 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6585, tnet num: 2042, tinst num: 841, tnode num: 8910, tedge num: 11566.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69264, over cnt = 143(0%), over = 226, worst = 7
PHY-1002 : len = 70104, over cnt = 98(0%), over = 126, worst = 4
PHY-1002 : len = 70928, over cnt = 45(0%), over = 62, worst = 4
PHY-1002 : len = 71608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.157119s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (99.4%)

PHY-1001 : Congestion index: top1 = 31.94, top5 = 22.60, top10 = 17.80, top15 = 13.96.
PHY-1001 : End global routing;  0.226601s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (96.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 203, peak = 251.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : clock net signal_process/clk will be merged with clock CLK120/clk0_buf
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 501, reserve = 467, peak = 501.
PHY-1001 : End build detailed router design. 4.135514s wall, 4.078125s user + 0.046875s system = 4.125000s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35000, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.655066s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (98.2%)

PHY-1001 : Current memory(MB): used = 533, reserve = 501, peak = 533.
PHY-1001 : End phase 1; 1.663797s wall, 1.593750s user + 0.031250s system = 1.625000s CPU (97.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177856, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End initial routed; 1.209763s wall, 1.968750s user + 0.265625s system = 2.234375s CPU (184.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1807(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.667   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.487   |   9   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.458995s wall, 0.453125s user + 0.000000s system = 0.453125s CPU (98.7%)

PHY-1001 : Current memory(MB): used = 537, reserve = 504, peak = 537.
PHY-1001 : End phase 2; 1.668937s wall, 2.421875s user + 0.265625s system = 2.687500s CPU (161.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177856, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.020707s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (75.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177528, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.068146s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177576, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.038089s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (123.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 177592, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.031922s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1807(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.667   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.487   |   9   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.529876s wall, 0.531250s user + 0.000000s system = 0.531250s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.223832s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (97.7%)

PHY-1001 : Current memory(MB): used = 550, reserve = 517, peak = 550.
PHY-1001 : End phase 3; 1.114128s wall, 1.125000s user + 0.000000s system = 1.125000s CPU (101.0%)

PHY-1003 : Routed, final wirelength = 177592
PHY-1001 : Current memory(MB): used = 550, reserve = 517, peak = 550.
PHY-1001 : End export database. 0.011247s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (138.9%)

PHY-1001 : End detail routing;  8.808594s wall, 9.453125s user + 0.343750s system = 9.796875s CPU (111.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6585, tnet num: 2042, tinst num: 841, tnode num: 8910, tedge num: 11566.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addra[4] slack -29ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addra[6] slack -29ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[23] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[29] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[31] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.addra[7] slack -28ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[42] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/wr_to_rd_cross_inst/reg0_syn_36.sr slack -89ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6631, tnet num: 2065, tinst num: 864, tnode num: 8956, tedge num: 11612.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  4.037414s wall, 4.031250s user + 0.203125s system = 4.234375s CPU (104.9%)

RUN-1003 : finish command "route" in  13.531286s wall, 14.156250s user + 0.546875s system = 14.703125s CPU (108.7%)

RUN-1004 : used memory is 543 MB, reserved memory is 512 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      880   out of  19600    4.49%
#reg                     1076   out of  19600    5.49%
#le                      1584
  #lut only               508   out of   1584   32.07%
  #reg only               704   out of   1584   44.44%
  #lut&reg                372   out of   1584   23.48%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        CLK120/clk0_buf                 GCLK               pll                CLK120/pll_inst.clkc0       468
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       108
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_11.q0    39
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1584   |654     |226     |1107    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1193   |361     |133     |925     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |39     |32      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |586    |161     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |171    |68      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |138    |25      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |93     |27      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |315    |99      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |22     |17      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |91     |79      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |19     |14      |0       |19      |0       |0       |
|    U2                      |Ctrl_Data                                        |37     |37      |0       |19      |0       |0       |
|  wendu                     |DS18B20                                          |217    |172     |45      |74      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1476  
    #2          2       312   
    #3          3       110   
    #4          4        14   
    #5        5-10       82   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.93            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6631, tnet num: 2065, tinst num: 864, tnode num: 8956, tedge num: 11612.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2065 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 864
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2067, pip num: 14735
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1310 valid insts, and 39540 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.318114s wall, 19.171875s user + 0.062500s system = 19.234375s CPU (579.7%)

RUN-1004 : used memory is 552 MB, reserved memory is 519 MB, peak memory is 682 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230907_153423.log"
