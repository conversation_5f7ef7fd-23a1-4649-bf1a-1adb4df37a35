//////////////////////////////////////////////////////////////////////////////////
// 自适应增益控制模块（AGC - Automatic Gain Control）
// 功能：根据信号幅度自动调整增益，保持输出信号在合适的范围内
// 原理：监测信号功率，通过反馈控制自动调整增益因子
//////////////////////////////////////////////////////////////////////////////////

module AdaptiveGainControl
#(
    parameter DATA_WIDTH = 12,      // 输入数据位宽
    parameter GAIN_WIDTH = 16,      // 增益因子位宽
    parameter OUTPUT_WIDTH = 16,    // 输出数据位宽
    parameter POWER_WIDTH = 24,     // 功率计算位宽
    parameter WINDOW_SIZE = 256     // 功率计算窗口大小
)
(
    input                           clk,
    input                           rst_n,
    input                           data_valid,
    input   [DATA_WIDTH-1:0]        data_in,        // 输入信号
    input   [15:0]                  target_power,   // 目标功率水平
    input   [7:0]                   agc_speed,      // AGC响应速度
    output reg                      data_ready,
    output reg [OUTPUT_WIDTH-1:0]   data_out,       // 增益控制后的输出
    output reg [GAIN_WIDTH-1:0]     current_gain,   // 当前增益值
    output reg [15:0]               signal_power    // 当前信号功率
);

// 增益控制相关寄存器
reg [GAIN_WIDTH-1:0] gain_factor;          // 当前增益因子
reg [GAIN_WIDTH-1:0] gain_step;            // 增益调整步长
reg [POWER_WIDTH-1:0] power_accumulator;   // 功率累加器
reg [15:0] sample_counter;                 // 采样计数器
reg [POWER_WIDTH-1:0] average_power;       // 平均功率
reg [DATA_WIDTH-1:0] abs_data;             // 输入数据的绝对值

// 增益调整状态机
reg [2:0] agc_state;
localparam IDLE = 3'b000;
localparam MEASURE = 3'b001;
localparam CALCULATE = 3'b010;
localparam ADJUST = 3'b011;
localparam OUTPUT = 3'b100;

// 中间计算变量
reg [DATA_WIDTH+GAIN_WIDTH-1:0] gained_signal;
reg [POWER_WIDTH-1:0] power_error;
reg gain_direction;  // 增益调整方向：1=增加，0=减少

// 初始化
initial begin
    gain_factor = 16'h4000;     // 初始增益 = 1.0 (Q15格式)
    gain_step = 16'h0100;       // 初始步长
    agc_state = IDLE;
end

// 输入数据绝对值计算（用于功率计算）
always @(posedge clk) begin
    if (data_in[DATA_WIDTH-1]) begin
        abs_data <= ~data_in + 1;  // 负数取绝对值
    end else begin
        abs_data <= data_in;       // 正数保持不变
    end
end

// 功率测量和AGC状态机
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        power_accumulator <= 0;
        sample_counter <= 0;
        average_power <= 0;
        agc_state <= IDLE;
        current_gain <= 16'h4000;
    end
    else begin
        case (agc_state)
            IDLE: begin
                if (data_valid) begin
                    agc_state <= MEASURE;
                    power_accumulator <= 0;
                    sample_counter <= 0;
                end
            end
            
            MEASURE: begin
                if (data_valid) begin
                    // 累加信号功率（使用平方近似：|x|^2 ≈ |x| * |x|）
                    power_accumulator <= power_accumulator + (abs_data * abs_data);
                    sample_counter <= sample_counter + 1;
                    
                    // 收集足够样本后进入计算阶段
                    if (sample_counter >= WINDOW_SIZE - 1) begin
                        agc_state <= CALCULATE;
                    end
                end
            end
            
            CALCULATE: begin
                // 计算平均功率
                average_power <= power_accumulator >> 8;  // 除以256（窗口大小）
                signal_power <= power_accumulator[23:8];  // 输出当前功率
                agc_state <= ADJUST;
            end
            
            ADJUST: begin
                // 计算功率误差
                if (average_power > target_power) begin
                    power_error <= average_power - target_power;
                    gain_direction <= 0;  // 功率过大，减少增益
                end else begin
                    power_error <= target_power - average_power;
                    gain_direction <= 1;  // 功率过小，增加增益
                end
                
                // 根据误差大小调整增益步长
                if (power_error > (target_power >> 2)) begin
                    gain_step <= 16'h0400;  // 大误差，大步长
                end else if (power_error > (target_power >> 4)) begin
                    gain_step <= 16'h0200;  // 中误差，中步长
                end else begin
                    gain_step <= 16'h0080;  // 小误差，小步长
                end
                
                agc_state <= OUTPUT;
            end
            
            OUTPUT: begin
                // 更新增益因子
                if (gain_direction) begin
                    // 增加增益，但要防止溢出
                    if (gain_factor < (16'hFFFF - gain_step)) begin
                        gain_factor <= gain_factor + gain_step;
                    end
                end else begin
                    // 减少增益，但要防止下溢
                    if (gain_factor > gain_step) begin
                        gain_factor <= gain_factor - gain_step;
                    end
                end
                
                current_gain <= gain_factor;
                agc_state <= IDLE;  // 回到空闲状态
            end
            
            default: agc_state <= IDLE;
        endcase
    end
end

// 增益应用和输出
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        gained_signal <= 0;
        data_out <= 0;
        data_ready <= 0;
    end
    else if (data_valid) begin
        // 应用增益：输出 = 输入 × 增益因子
        gained_signal <= $signed(data_in) * $signed(gain_factor);
        
        // 取合适的位数作为输出（Q15格式，所以右移15位）
        data_out <= gained_signal[DATA_WIDTH+GAIN_WIDTH-1:DATA_WIDTH+GAIN_WIDTH-OUTPUT_WIDTH];
        
        data_ready <= 1'b1;
    end
    else begin
        data_ready <= 1'b0;
    end
end

// 增益限制保护（防止增益过大或过小）
always @(posedge clk) begin
    if (gain_factor > 16'hF000) begin
        gain_factor <= 16'hF000;  // 最大增益限制（约15倍）
    end
    else if (gain_factor < 16'h0400) begin
        gain_factor <= 16'h0400;  // 最小增益限制（约0.06倍）
    end
end

endmodule
