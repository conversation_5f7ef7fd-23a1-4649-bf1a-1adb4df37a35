// Verilog netlist created by Tang Dynasty v5.6.69102
// Wed Apr 19 09:43:46 2023

`timescale 1ns / 1ps
module SoltFIFO  // SoltFIFO.v(14)
  (
  clk,
  di,
  re,
  rst,
  we,
  do,
  empty_flag,
  full_flag,
  rdusedw,
  wrusedw
  );

  input clk;  // SoltFIFO.v(24)
  input [55:0] di;  // SoltFIFO.v(23)
  input re;  // SoltFIFO.v(25)
  input rst;  // SoltFIFO.v(22)
  input we;  // SoltFIFO.v(24)
  output [55:0] do;  // SoltFIFO.v(27)
  output empty_flag;  // SoltFIFO.v(28)
  output full_flag;  // SoltFIFO.v(29)
  output [8:0] rdusedw;  // SoltFIFO.v(30)
  output [8:0] wrusedw;  // SoltFIFO.v(31)

  wire logic_ramfifo_syn_1;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_2;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_3;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_4;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_5;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_6;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_7;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_8;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_9;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_10;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_11;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_12;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_13;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_14;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_15;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_16;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_17;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_18;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_19;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_20;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_21;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_22;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_23;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_24;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_25;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_26;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_27;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_37;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_38;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_39;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_40;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_41;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_42;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_43;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_44;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_45;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_46;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_47;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_48;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_49;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_50;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_51;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_52;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_53;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_54;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_55;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_56;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_57;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_58;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_59;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_60;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_61;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_62;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_64;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_65;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_66;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_67;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_68;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_69;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_70;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_71;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_72;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_73;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_74;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_75;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_76;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_77;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_78;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_79;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_80;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_81;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_82;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_83;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_84;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_85;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_86;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_87;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_88;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_89;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_168;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_170;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_174;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_175;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_176;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_177;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_178;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_179;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_180;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_181;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_182;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_183;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_187;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_189;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_259;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_279;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_280;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_281;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_282;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_283;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_284;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_285;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_286;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_287;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_303;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_305;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_307;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_309;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_311;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_313;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_315;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_317;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_322;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_324;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_326;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_328;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_330;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_332;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_334;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_338;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_340;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_342;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_344;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_346;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_348;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_350;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_352;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_357;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_359;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_361;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_363;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_365;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_367;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_369;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_584;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_585;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_586;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_587;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_588;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_589;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_590;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_591;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_592;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_593;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_594;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_595;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_596;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_597;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_598;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_599;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_600;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_638;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_639;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_640;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_641;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_642;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_643;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_644;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_645;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_646;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_647;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_648;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_649;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_650;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_651;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_652;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_653;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_654;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_693;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_694;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_695;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_696;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_697;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_698;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_699;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_700;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_701;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_741;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_742;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_743;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_744;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_745;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_746;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_747;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_748;  // SoltFIFO.v(39)
  wire logic_ramfifo_syn_749;  // SoltFIFO.v(39)
  wire clk_syn_1;  // SoltFIFO.v(24)
  wire clk_syn_2;  // SoltFIFO.v(24)
  wire clk_syn_3;  // SoltFIFO.v(24)
  wire clk_syn_4;  // SoltFIFO.v(24)
  wire clk_syn_5;  // SoltFIFO.v(24)
  wire clk_syn_6;  // SoltFIFO.v(24)
  wire clk_syn_7;  // SoltFIFO.v(24)
  wire clk_syn_8;  // SoltFIFO.v(24)
  wire clk_syn_9;  // SoltFIFO.v(24)
  wire clk_syn_10;  // SoltFIFO.v(24)
  wire clk_syn_12;  // SoltFIFO.v(24)
  wire clk_syn_14;  // SoltFIFO.v(24)
  wire clk_syn_16;  // SoltFIFO.v(24)
  wire clk_syn_18;  // SoltFIFO.v(24)
  wire clk_syn_20;  // SoltFIFO.v(24)
  wire clk_syn_22;  // SoltFIFO.v(24)
  wire clk_syn_24;  // SoltFIFO.v(24)
  wire clk_syn_26;  // SoltFIFO.v(24)
  wire clk_syn_28;  // SoltFIFO.v(24)
  wire clk_syn_34;  // SoltFIFO.v(24)
  wire clk_syn_36;  // SoltFIFO.v(24)
  wire clk_syn_38;  // SoltFIFO.v(24)
  wire clk_syn_40;  // SoltFIFO.v(24)
  wire clk_syn_42;  // SoltFIFO.v(24)
  wire clk_syn_44;  // SoltFIFO.v(24)
  wire clk_syn_46;  // SoltFIFO.v(24)
  wire clk_syn_48;  // SoltFIFO.v(24)
  wire clk_syn_50;  // SoltFIFO.v(24)
  wire clk_syn_52;  // SoltFIFO.v(24)
  wire clk_syn_54;  // SoltFIFO.v(24)
  wire clk_syn_56;  // SoltFIFO.v(24)
  wire clk_syn_58;  // SoltFIFO.v(24)
  wire clk_syn_60;  // SoltFIFO.v(24)
  wire clk_syn_62;  // SoltFIFO.v(24)
  wire clk_syn_64;  // SoltFIFO.v(24)
  wire clk_syn_66;  // SoltFIFO.v(24)
  wire clk_syn_67;  // SoltFIFO.v(24)
  wire clk_syn_68;  // SoltFIFO.v(24)
  wire clk_syn_69;  // SoltFIFO.v(24)
  wire clk_syn_70;  // SoltFIFO.v(24)
  wire clk_syn_71;  // SoltFIFO.v(24)
  wire clk_syn_72;  // SoltFIFO.v(24)
  wire clk_syn_73;  // SoltFIFO.v(24)
  wire clk_syn_74;  // SoltFIFO.v(24)
  wire clk_syn_75;  // SoltFIFO.v(24)
  wire clk_syn_76;  // SoltFIFO.v(24)
  wire clk_syn_78;  // SoltFIFO.v(24)
  wire clk_syn_79;  // SoltFIFO.v(24)
  wire clk_syn_80;  // SoltFIFO.v(24)
  wire clk_syn_81;  // SoltFIFO.v(24)
  wire clk_syn_82;  // SoltFIFO.v(24)
  wire clk_syn_83;  // SoltFIFO.v(24)
  wire clk_syn_84;  // SoltFIFO.v(24)
  wire clk_syn_85;  // SoltFIFO.v(24)
  wire clk_syn_86;  // SoltFIFO.v(24)
  wire clk_syn_87;  // SoltFIFO.v(24)
  wire clk_syn_89;  // SoltFIFO.v(24)
  wire clk_syn_93;  // SoltFIFO.v(24)
  wire clk_syn_95;  // SoltFIFO.v(24)
  wire clk_syn_97;  // SoltFIFO.v(24)
  wire clk_syn_99;  // SoltFIFO.v(24)
  wire clk_syn_101;  // SoltFIFO.v(24)
  wire clk_syn_103;  // SoltFIFO.v(24)
  wire clk_syn_105;  // SoltFIFO.v(24)
  wire clk_syn_107;  // SoltFIFO.v(24)
  wire clk_syn_111;  // SoltFIFO.v(24)
  wire clk_syn_113;  // SoltFIFO.v(24)
  wire clk_syn_115;  // SoltFIFO.v(24)
  wire clk_syn_117;  // SoltFIFO.v(24)
  wire clk_syn_119;  // SoltFIFO.v(24)
  wire clk_syn_121;  // SoltFIFO.v(24)
  wire clk_syn_123;  // SoltFIFO.v(24)
  wire clk_syn_125;  // SoltFIFO.v(24)
  wire clk_syn_127;  // SoltFIFO.v(24)
  wire clk_syn_129;  // SoltFIFO.v(24)
  wire clk_syn_131;  // SoltFIFO.v(24)
  wire clk_syn_133;  // SoltFIFO.v(24)
  wire clk_syn_135;  // SoltFIFO.v(24)
  wire clk_syn_137;  // SoltFIFO.v(24)
  wire clk_syn_139;  // SoltFIFO.v(24)
  wire clk_syn_141;  // SoltFIFO.v(24)
  wire clk_syn_143;  // SoltFIFO.v(24)
  wire clk_syn_144;  // SoltFIFO.v(24)
  wire clk_syn_145;  // SoltFIFO.v(24)
  wire clk_syn_146;  // SoltFIFO.v(24)
  wire clk_syn_147;  // SoltFIFO.v(24)
  wire clk_syn_148;  // SoltFIFO.v(24)
  wire clk_syn_149;  // SoltFIFO.v(24)
  wire clk_syn_150;  // SoltFIFO.v(24)
  wire clk_syn_151;  // SoltFIFO.v(24)
  wire clk_syn_152;  // SoltFIFO.v(24)
  wire clk_syn_153;  // SoltFIFO.v(24)
  wire re_syn_2;  // SoltFIFO.v(25)
  wire we_syn_2;  // SoltFIFO.v(24)
  wire _al_n1_syn_4;
  wire _al_n1_syn_6;
  wire _al_n1_syn_8;
  wire _al_n1_syn_10;
  wire _al_n1_syn_12;
  wire _al_n1_syn_14;
  wire _al_n1_syn_16;
  wire _al_n1_syn_24;
  wire _al_n1_syn_26;
  wire _al_n1_syn_28;
  wire _al_n1_syn_30;
  wire _al_n1_syn_32;
  wire _al_n1_syn_34;
  wire _al_n1_syn_36;

  and _al_n1_syn_11 (_al_n1_syn_12, _al_n1_syn_10, clk_syn_24);
  and _al_n1_syn_13 (_al_n1_syn_14, _al_n1_syn_12, clk_syn_26);
  and _al_n1_syn_15 (_al_n1_syn_16, _al_n1_syn_14, clk_syn_28);
  and _al_n1_syn_23 (_al_n1_syn_24, clk_syn_107, clk_syn_93);
  and _al_n1_syn_25 (_al_n1_syn_26, _al_n1_syn_24, clk_syn_95);
  and _al_n1_syn_27 (_al_n1_syn_28, _al_n1_syn_26, clk_syn_97);
  and _al_n1_syn_29 (_al_n1_syn_30, _al_n1_syn_28, clk_syn_99);
  and _al_n1_syn_3 (_al_n1_syn_4, clk_syn_14, clk_syn_16);
  and _al_n1_syn_31 (_al_n1_syn_32, _al_n1_syn_30, clk_syn_101);
  and _al_n1_syn_33 (_al_n1_syn_34, _al_n1_syn_32, clk_syn_103);
  and _al_n1_syn_35 (_al_n1_syn_36, _al_n1_syn_34, clk_syn_105);
  and _al_n1_syn_5 (_al_n1_syn_6, _al_n1_syn_4, clk_syn_18);
  and _al_n1_syn_7 (_al_n1_syn_8, _al_n1_syn_6, clk_syn_20);
  and _al_n1_syn_9 (_al_n1_syn_10, _al_n1_syn_8, clk_syn_22);
  not clk_syn_100 (clk_syn_101, clk_syn_83);  // SoltFIFO.v(24)
  not clk_syn_102 (clk_syn_103, clk_syn_84);  // SoltFIFO.v(24)
  not clk_syn_104 (clk_syn_105, clk_syn_85);  // SoltFIFO.v(24)
  not clk_syn_106 (clk_syn_107, clk_syn_78);  // SoltFIFO.v(24)
  or clk_syn_11 (clk_syn_12, clk_syn_10, clk_syn_9);  // SoltFIFO.v(24)
  xor clk_syn_110 (clk_syn_111, clk_syn_79, clk_syn_78);  // SoltFIFO.v(24)
  and clk_syn_112 (clk_syn_113, clk_syn_79, clk_syn_107);  // SoltFIFO.v(24)
  xor clk_syn_114 (clk_syn_115, clk_syn_80, clk_syn_113);  // SoltFIFO.v(24)
  and clk_syn_116 (clk_syn_117, clk_syn_80, _al_n1_syn_24);  // SoltFIFO.v(24)
  xor clk_syn_118 (clk_syn_119, clk_syn_81, clk_syn_117);  // SoltFIFO.v(24)
  and clk_syn_120 (clk_syn_121, clk_syn_81, _al_n1_syn_26);  // SoltFIFO.v(24)
  xor clk_syn_122 (clk_syn_123, clk_syn_82, clk_syn_121);  // SoltFIFO.v(24)
  and clk_syn_124 (clk_syn_125, clk_syn_82, _al_n1_syn_28);  // SoltFIFO.v(24)
  xor clk_syn_126 (clk_syn_127, clk_syn_83, clk_syn_125);  // SoltFIFO.v(24)
  and clk_syn_128 (clk_syn_129, clk_syn_83, _al_n1_syn_30);  // SoltFIFO.v(24)
  not clk_syn_13 (clk_syn_14, clk_syn_1);  // SoltFIFO.v(24)
  xor clk_syn_130 (clk_syn_131, clk_syn_84, clk_syn_129);  // SoltFIFO.v(24)
  and clk_syn_132 (clk_syn_133, clk_syn_84, _al_n1_syn_32);  // SoltFIFO.v(24)
  xor clk_syn_134 (clk_syn_135, clk_syn_85, clk_syn_133);  // SoltFIFO.v(24)
  and clk_syn_136 (clk_syn_137, clk_syn_85, _al_n1_syn_34);  // SoltFIFO.v(24)
  xor clk_syn_138 (clk_syn_139, clk_syn_86, clk_syn_137);  // SoltFIFO.v(24)
  and clk_syn_140 (clk_syn_141, clk_syn_89, _al_n1_syn_36);  // SoltFIFO.v(24)
  xor clk_syn_142 (clk_syn_143, clk_syn_87, clk_syn_141);  // SoltFIFO.v(24)
  not clk_syn_15 (clk_syn_16, clk_syn_2);  // SoltFIFO.v(24)
  not clk_syn_17 (clk_syn_18, clk_syn_3);  // SoltFIFO.v(24)
  not clk_syn_19 (clk_syn_20, clk_syn_4);  // SoltFIFO.v(24)
  not clk_syn_21 (clk_syn_22, clk_syn_5);  // SoltFIFO.v(24)
  not clk_syn_23 (clk_syn_24, clk_syn_6);  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_232 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_67),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_1));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_233 (
    .ar(1'b0),
    .as(rst),
    .clk(clk),
    .d(clk_syn_68),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_2));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_234 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_69),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_3));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_235 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_70),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_4));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_236 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_71),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_5));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_237 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_72),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_6));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_238 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_73),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_7));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_239 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_74),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_8));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_240 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_75),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_9));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_241 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_76),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_10));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_242 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_144),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_78));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_243 (
    .ar(1'b0),
    .as(rst),
    .clk(clk),
    .d(clk_syn_145),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_79));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_244 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_146),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_80));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_245 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_147),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_81));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_246 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_148),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_82));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_247 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_149),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_83));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_248 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_150),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_84));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_249 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_151),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_85));  // SoltFIFO.v(24)
  not clk_syn_25 (clk_syn_26, clk_syn_7);  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_250 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_152),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_86));  // SoltFIFO.v(24)
  AL_DFF_X clk_syn_251 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(clk_syn_153),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(clk_syn_87));  // SoltFIFO.v(24)
  not clk_syn_27 (clk_syn_28, clk_syn_8);  // SoltFIFO.v(24)
  xor clk_syn_33 (clk_syn_34, clk_syn_2, clk_syn_1);  // SoltFIFO.v(24)
  and clk_syn_35 (clk_syn_36, clk_syn_2, clk_syn_14);  // SoltFIFO.v(24)
  xor clk_syn_37 (clk_syn_38, clk_syn_3, clk_syn_36);  // SoltFIFO.v(24)
  and clk_syn_39 (clk_syn_40, clk_syn_3, _al_n1_syn_4);  // SoltFIFO.v(24)
  xor clk_syn_41 (clk_syn_42, clk_syn_4, clk_syn_40);  // SoltFIFO.v(24)
  and clk_syn_43 (clk_syn_44, clk_syn_4, _al_n1_syn_6);  // SoltFIFO.v(24)
  xor clk_syn_45 (clk_syn_46, clk_syn_5, clk_syn_44);  // SoltFIFO.v(24)
  and clk_syn_47 (clk_syn_48, clk_syn_5, _al_n1_syn_8);  // SoltFIFO.v(24)
  xor clk_syn_49 (clk_syn_50, clk_syn_6, clk_syn_48);  // SoltFIFO.v(24)
  and clk_syn_51 (clk_syn_52, clk_syn_6, _al_n1_syn_10);  // SoltFIFO.v(24)
  xor clk_syn_53 (clk_syn_54, clk_syn_7, clk_syn_52);  // SoltFIFO.v(24)
  and clk_syn_55 (clk_syn_56, clk_syn_7, _al_n1_syn_12);  // SoltFIFO.v(24)
  xor clk_syn_57 (clk_syn_58, clk_syn_8, clk_syn_56);  // SoltFIFO.v(24)
  and clk_syn_59 (clk_syn_60, clk_syn_8, _al_n1_syn_14);  // SoltFIFO.v(24)
  xor clk_syn_61 (clk_syn_62, clk_syn_9, clk_syn_60);  // SoltFIFO.v(24)
  and clk_syn_63 (clk_syn_64, clk_syn_12, _al_n1_syn_16);  // SoltFIFO.v(24)
  xor clk_syn_65 (clk_syn_66, clk_syn_10, clk_syn_64);  // SoltFIFO.v(24)
  or clk_syn_88 (clk_syn_89, clk_syn_87, clk_syn_86);  // SoltFIFO.v(24)
  not clk_syn_92 (clk_syn_93, clk_syn_79);  // SoltFIFO.v(24)
  not clk_syn_94 (clk_syn_95, clk_syn_80);  // SoltFIFO.v(24)
  not clk_syn_96 (clk_syn_97, clk_syn_81);  // SoltFIFO.v(24)
  not clk_syn_98 (clk_syn_99, clk_syn_82);  // SoltFIFO.v(24)
  EG_PHY_CONFIG #(
    .DONE_PERSISTN("ENABLE"),
    .INIT_PERSISTN("ENABLE"),
    .JTAG_PERSISTN("DISABLE"),
    .PROGRAMN_PERSISTN("DISABLE"))
    config_inst ();
  not logic_ramfifo_syn_167 (logic_ramfifo_syn_168, logic_ramfifo_syn_44);  // SoltFIFO.v(39)
  not logic_ramfifo_syn_169 (logic_ramfifo_syn_170, logic_ramfifo_syn_45);  // SoltFIFO.v(39)
  not logic_ramfifo_syn_173 (logic_ramfifo_syn_174, full_flag);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_186 (logic_ramfifo_syn_187, logic_ramfifo_syn_27, logic_ramfifo_syn_26);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_188 (logic_ramfifo_syn_189, logic_ramfifo_syn_9, logic_ramfifo_syn_8);  // SoltFIFO.v(39)
  not logic_ramfifo_syn_258 (logic_ramfifo_syn_259, empty_flag);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_302 (logic_ramfifo_syn_303, logic_ramfifo_syn_45, logic_ramfifo_syn_44);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_304 (logic_ramfifo_syn_305, logic_ramfifo_syn_303, logic_ramfifo_syn_43);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_306 (logic_ramfifo_syn_307, logic_ramfifo_syn_305, logic_ramfifo_syn_42);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_308 (logic_ramfifo_syn_309, logic_ramfifo_syn_307, logic_ramfifo_syn_41);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_310 (logic_ramfifo_syn_311, logic_ramfifo_syn_309, logic_ramfifo_syn_40);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_312 (logic_ramfifo_syn_313, logic_ramfifo_syn_311, logic_ramfifo_syn_39);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_314 (logic_ramfifo_syn_315, logic_ramfifo_syn_313, logic_ramfifo_syn_38);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_316 (logic_ramfifo_syn_317, logic_ramfifo_syn_315, logic_ramfifo_syn_37);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_321 (logic_ramfifo_syn_322, logic_ramfifo_syn_189, logic_ramfifo_syn_7);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_323 (logic_ramfifo_syn_324, logic_ramfifo_syn_322, logic_ramfifo_syn_6);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_325 (logic_ramfifo_syn_326, logic_ramfifo_syn_324, logic_ramfifo_syn_5);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_327 (logic_ramfifo_syn_328, logic_ramfifo_syn_326, logic_ramfifo_syn_4);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_329 (logic_ramfifo_syn_330, logic_ramfifo_syn_328, logic_ramfifo_syn_3);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_331 (logic_ramfifo_syn_332, logic_ramfifo_syn_330, logic_ramfifo_syn_2);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_333 (logic_ramfifo_syn_334, logic_ramfifo_syn_332, logic_ramfifo_syn_1);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_337 (logic_ramfifo_syn_338, logic_ramfifo_syn_72, logic_ramfifo_syn_71);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_339 (logic_ramfifo_syn_340, logic_ramfifo_syn_338, logic_ramfifo_syn_70);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_341 (logic_ramfifo_syn_342, logic_ramfifo_syn_340, logic_ramfifo_syn_69);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_343 (logic_ramfifo_syn_344, logic_ramfifo_syn_342, logic_ramfifo_syn_68);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_345 (logic_ramfifo_syn_346, logic_ramfifo_syn_344, logic_ramfifo_syn_67);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_347 (logic_ramfifo_syn_348, logic_ramfifo_syn_346, logic_ramfifo_syn_66);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_349 (logic_ramfifo_syn_350, logic_ramfifo_syn_348, logic_ramfifo_syn_65);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_351 (logic_ramfifo_syn_352, logic_ramfifo_syn_350, logic_ramfifo_syn_64);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_356 (logic_ramfifo_syn_357, logic_ramfifo_syn_187, logic_ramfifo_syn_25);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_358 (logic_ramfifo_syn_359, logic_ramfifo_syn_357, logic_ramfifo_syn_24);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_360 (logic_ramfifo_syn_361, logic_ramfifo_syn_359, logic_ramfifo_syn_23);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_362 (logic_ramfifo_syn_363, logic_ramfifo_syn_361, logic_ramfifo_syn_22);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_364 (logic_ramfifo_syn_365, logic_ramfifo_syn_363, logic_ramfifo_syn_21);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_366 (logic_ramfifo_syn_367, logic_ramfifo_syn_365, logic_ramfifo_syn_20);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_368 (logic_ramfifo_syn_369, logic_ramfifo_syn_367, logic_ramfifo_syn_19);  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_394 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_175),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_1));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_395 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_176),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_2));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_396 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_177),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_3));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_397 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_178),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_4));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_398 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_179),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_5));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_399 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_180),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_6));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_400 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_181),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_7));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_401 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_182),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_8));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_402 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_183),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_9));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_403 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_1),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_10));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_404 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_2),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_11));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_405 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_3),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_12));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_406 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_4),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_13));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_407 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_5),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_14));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_408 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_6),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_15));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_409 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_7),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_16));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_410 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_8),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_17));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_411 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_9),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_18));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_415 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_279),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_19));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_416 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_280),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_20));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_417 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_281),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_21));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_418 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_282),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_22));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_419 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_283),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_23));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_420 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_284),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_24));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_421 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_285),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_25));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_422 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_286),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_26));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_423 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_287),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_27));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_433 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_19),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_37));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_434 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_20),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_38));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_435 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_21),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_39));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_436 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_22),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_40));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_437 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_23),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_41));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_438 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_24),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_42));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_439 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_25),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_43));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_440 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_26),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_44));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_441 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_27),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_45));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_442 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_317),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_46));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_443 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_315),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_47));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_444 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_313),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_48));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_445 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_311),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_49));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_446 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_309),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_50));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_447 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_307),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_51));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_448 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_305),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_52));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_449 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_303),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_53));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_450 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_45),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_54));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_451 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_334),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_55));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_452 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_332),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_56));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_453 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_330),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_57));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_454 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_328),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_58));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_455 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_326),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_59));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_456 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_324),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_60));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_457 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_322),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_61));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_458 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_189),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_62));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_460 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_10),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_64));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_461 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_11),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_65));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_462 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_12),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_66));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_463 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_13),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_67));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_464 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_14),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_68));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_465 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_15),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_69));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_466 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_16),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_70));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_467 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_17),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_71));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_468 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_18),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_72));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_469 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_352),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_73));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_470 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_350),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_74));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_471 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_348),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_75));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_472 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_346),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_76));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_473 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_344),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_77));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_474 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_342),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_78));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_475 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_340),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_79));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_476 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_338),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_80));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_477 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_72),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_81));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_478 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_369),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_82));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_479 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_367),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_83));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_480 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_365),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_84));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_481 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_363),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_85));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_482 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_361),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_86));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_483 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_359),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_87));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_484 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_357),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_88));  // SoltFIFO.v(39)
  AL_DFF_X logic_ramfifo_syn_485 (
    .ar(rst),
    .as(1'b0),
    .clk(clk),
    .d(logic_ramfifo_syn_187),
    .en(1'b1),
    .sr(1'b0),
    .ss(1'b0),
    .q(logic_ramfifo_syn_89));  // SoltFIFO.v(39)
  // address_offset=0;data_offset=0;depth=256;width=18;num_section=1;width_per_section=18;section_size=56;working_depth=512;working_width=18;working_numbyte=1;mode_ecc=0;address_step=1;bytes_in_per_section=1;
  // logic_ramfifo_syn_371_256x56
  EG_PHY_BRAM #(
    .CEBMUX("1"),
    .CSA0("1"),
    .CSA1("1"),
    .CSA2("1"),
    .CSB0("1"),
    .CSB1("1"),
    .CSB2("SIG"),
    .DATA_WIDTH_A("18"),
    .DATA_WIDTH_B("18"),
    .INITP_00(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_01(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_02(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_03(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_00(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_01(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_02(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_03(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_04(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_05(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_06(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_07(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_08(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_09(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0A(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0B(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0C(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0D(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0E(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0F(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_10(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_11(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_12(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_13(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_14(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_15(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_16(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_17(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_18(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_19(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1A(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1B(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1C(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1D(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1E(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1F(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .MODE("PDPW8K"),
    .OCEAMUX("1"),
    .OCEBMUX("1"),
    .REGMODE_A("NOREG"),
    .REGMODE_B("NOREG"),
    .RESETMODE("SYNC"),
    .WEAMUX("1"),
    .WEBMUX("0"),
    .WRITEMODE_A("NORMAL"),
    .WRITEMODE_B("NORMAL"))
    logic_ramfifo_syn_487 (
    .addra({1'b0,logic_ramfifo_syn_189,logic_ramfifo_syn_7,logic_ramfifo_syn_6,logic_ramfifo_syn_5,logic_ramfifo_syn_4,logic_ramfifo_syn_3,logic_ramfifo_syn_2,logic_ramfifo_syn_1,4'b1111}),
    .addrb({1'b0,logic_ramfifo_syn_187,logic_ramfifo_syn_25,logic_ramfifo_syn_24,logic_ramfifo_syn_23,logic_ramfifo_syn_22,logic_ramfifo_syn_21,logic_ramfifo_syn_20,logic_ramfifo_syn_19,4'b1111}),
    .cea(we_syn_2),
    .clka(clk),
    .clkb(clk),
    .csb({re_syn_2,open_n51,open_n52}),
    .dia(di[8:0]),
    .dib(di[17:9]),
    .rsta(rst),
    .rstb(rst),
    .doa(do[8:0]),
    .dob(do[17:9]));  // SoltFIFO.v(39)
  // address_offset=0;data_offset=18;depth=256;width=18;num_section=1;width_per_section=18;section_size=56;working_depth=512;working_width=18;working_numbyte=1;mode_ecc=0;address_step=1;bytes_in_per_section=1;
  // logic_ramfifo_syn_371_256x56
  EG_PHY_BRAM #(
    .CEBMUX("1"),
    .CSA0("1"),
    .CSA1("1"),
    .CSA2("1"),
    .CSB0("1"),
    .CSB1("1"),
    .CSB2("SIG"),
    .DATA_WIDTH_A("18"),
    .DATA_WIDTH_B("18"),
    .INITP_00(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_01(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_02(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_03(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_00(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_01(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_02(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_03(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_04(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_05(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_06(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_07(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_08(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_09(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0A(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0B(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0C(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0D(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0E(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0F(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_10(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_11(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_12(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_13(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_14(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_15(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_16(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_17(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_18(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_19(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1A(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1B(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1C(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1D(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1E(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1F(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .MODE("PDPW8K"),
    .OCEAMUX("1"),
    .OCEBMUX("1"),
    .REGMODE_A("NOREG"),
    .REGMODE_B("NOREG"),
    .RESETMODE("SYNC"),
    .WEAMUX("1"),
    .WEBMUX("0"),
    .WRITEMODE_A("NORMAL"),
    .WRITEMODE_B("NORMAL"))
    logic_ramfifo_syn_506 (
    .addra({1'b0,logic_ramfifo_syn_189,logic_ramfifo_syn_7,logic_ramfifo_syn_6,logic_ramfifo_syn_5,logic_ramfifo_syn_4,logic_ramfifo_syn_3,logic_ramfifo_syn_2,logic_ramfifo_syn_1,4'b1111}),
    .addrb({1'b0,logic_ramfifo_syn_187,logic_ramfifo_syn_25,logic_ramfifo_syn_24,logic_ramfifo_syn_23,logic_ramfifo_syn_22,logic_ramfifo_syn_21,logic_ramfifo_syn_20,logic_ramfifo_syn_19,4'b1111}),
    .cea(we_syn_2),
    .clka(clk),
    .clkb(clk),
    .csb({re_syn_2,open_n61,open_n62}),
    .dia(di[26:18]),
    .dib(di[35:27]),
    .rsta(rst),
    .rstb(rst),
    .doa(do[26:18]),
    .dob(do[35:27]));  // SoltFIFO.v(39)
  // address_offset=0;data_offset=36;depth=256;width=18;num_section=1;width_per_section=18;section_size=56;working_depth=512;working_width=18;working_numbyte=1;mode_ecc=0;address_step=1;bytes_in_per_section=1;
  // logic_ramfifo_syn_371_256x56
  EG_PHY_BRAM #(
    .CEBMUX("1"),
    .CSA0("1"),
    .CSA1("1"),
    .CSA2("1"),
    .CSB0("1"),
    .CSB1("1"),
    .CSB2("SIG"),
    .DATA_WIDTH_A("18"),
    .DATA_WIDTH_B("18"),
    .INITP_00(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_01(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_02(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_03(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_00(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_01(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_02(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_03(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_04(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_05(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_06(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_07(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_08(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_09(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0A(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0B(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0C(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0D(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0E(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0F(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_10(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_11(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_12(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_13(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_14(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_15(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_16(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_17(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_18(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_19(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1A(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1B(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1C(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1D(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1E(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1F(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .MODE("PDPW8K"),
    .OCEAMUX("1"),
    .OCEBMUX("1"),
    .REGMODE_A("NOREG"),
    .REGMODE_B("NOREG"),
    .RESETMODE("SYNC"),
    .WEAMUX("1"),
    .WEBMUX("0"),
    .WRITEMODE_A("NORMAL"),
    .WRITEMODE_B("NORMAL"))
    logic_ramfifo_syn_525 (
    .addra({1'b0,logic_ramfifo_syn_189,logic_ramfifo_syn_7,logic_ramfifo_syn_6,logic_ramfifo_syn_5,logic_ramfifo_syn_4,logic_ramfifo_syn_3,logic_ramfifo_syn_2,logic_ramfifo_syn_1,4'b1111}),
    .addrb({1'b0,logic_ramfifo_syn_187,logic_ramfifo_syn_25,logic_ramfifo_syn_24,logic_ramfifo_syn_23,logic_ramfifo_syn_22,logic_ramfifo_syn_21,logic_ramfifo_syn_20,logic_ramfifo_syn_19,4'b1111}),
    .cea(we_syn_2),
    .clka(clk),
    .clkb(clk),
    .csb({re_syn_2,open_n71,open_n72}),
    .dia(di[44:36]),
    .dib(di[53:45]),
    .rsta(rst),
    .rstb(rst),
    .doa(do[44:36]),
    .dob(do[53:45]));  // SoltFIFO.v(39)
  // address_offset=0;data_offset=54;depth=256;width=2;num_section=1;width_per_section=2;section_size=56;working_depth=4096;working_width=2;working_numbyte=1;mode_ecc=0;address_step=1;bytes_in_per_section=1;
  // logic_ramfifo_syn_371_256x56
  EG_PHY_BRAM #(
    .CEAMUX("1"),
    .CSA0("1"),
    .CSA1("1"),
    .CSA2("1"),
    .CSB0("1"),
    .CSB1("1"),
    .CSB2("1"),
    .DATA_WIDTH_A("2"),
    .DATA_WIDTH_B("2"),
    .INITP_00(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_01(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_02(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INITP_03(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_00(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_01(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_02(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_03(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_04(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_05(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_06(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_07(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_08(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_09(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0A(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0B(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0C(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0D(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0E(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_0F(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_10(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_11(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_12(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_13(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_14(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_15(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_16(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_17(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_18(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_19(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1A(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1B(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1C(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1D(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1E(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .INIT_1F(256'h0000000000000000000000000000000000000000000000000000000000000000),
    .MODE("DP8K"),
    .OCEAMUX("1"),
    .OCEBMUX("1"),
    .REGMODE_A("NOREG"),
    .REGMODE_B("NOREG"),
    .RESETMODE("SYNC"),
    .WEBMUX("0"),
    .WRITEMODE_A("NORMAL"),
    .WRITEMODE_B("NORMAL"))
    logic_ramfifo_syn_544 (
    .addra({4'b0000,logic_ramfifo_syn_189,logic_ramfifo_syn_7,logic_ramfifo_syn_6,logic_ramfifo_syn_5,logic_ramfifo_syn_4,logic_ramfifo_syn_3,logic_ramfifo_syn_2,logic_ramfifo_syn_1,1'b1}),
    .addrb({4'b0000,logic_ramfifo_syn_187,logic_ramfifo_syn_25,logic_ramfifo_syn_24,logic_ramfifo_syn_23,logic_ramfifo_syn_22,logic_ramfifo_syn_21,logic_ramfifo_syn_20,logic_ramfifo_syn_19,1'b1}),
    .ceb(re_syn_2),
    .clka(clk),
    .clkb(clk),
    .dia({open_n84,open_n85,open_n86,di[55],open_n87,open_n88,di[54],open_n89,open_n90}),
    .rsta(rst),
    .rstb(rst),
    .wea(we_syn_2),
    .dob({open_n112,open_n113,open_n114,open_n115,open_n116,open_n117,open_n118,do[55:54]}));  // SoltFIFO.v(39)
  not logic_ramfifo_syn_547 (full_flag, logic_ramfifo_syn_600);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_548 (logic_ramfifo_syn_584, logic_ramfifo_syn_1, logic_ramfifo_syn_37);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_549 (logic_ramfifo_syn_585, logic_ramfifo_syn_2, logic_ramfifo_syn_38);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_550 (logic_ramfifo_syn_586, logic_ramfifo_syn_3, logic_ramfifo_syn_39);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_551 (logic_ramfifo_syn_587, logic_ramfifo_syn_4, logic_ramfifo_syn_40);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_552 (logic_ramfifo_syn_588, logic_ramfifo_syn_5, logic_ramfifo_syn_41);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_553 (logic_ramfifo_syn_589, logic_ramfifo_syn_6, logic_ramfifo_syn_42);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_554 (logic_ramfifo_syn_590, logic_ramfifo_syn_7, logic_ramfifo_syn_43);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_555 (logic_ramfifo_syn_591, logic_ramfifo_syn_8, logic_ramfifo_syn_168);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_556 (logic_ramfifo_syn_592, logic_ramfifo_syn_9, logic_ramfifo_syn_170);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_557 (logic_ramfifo_syn_593, logic_ramfifo_syn_584, logic_ramfifo_syn_585);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_558 (logic_ramfifo_syn_594, logic_ramfifo_syn_586, logic_ramfifo_syn_587);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_559 (logic_ramfifo_syn_595, logic_ramfifo_syn_593, logic_ramfifo_syn_594);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_560 (logic_ramfifo_syn_596, logic_ramfifo_syn_588, logic_ramfifo_syn_589);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_561 (logic_ramfifo_syn_597, logic_ramfifo_syn_591, logic_ramfifo_syn_592);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_562 (logic_ramfifo_syn_598, logic_ramfifo_syn_590, logic_ramfifo_syn_597);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_563 (logic_ramfifo_syn_599, logic_ramfifo_syn_596, logic_ramfifo_syn_598);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_564 (logic_ramfifo_syn_600, logic_ramfifo_syn_595, logic_ramfifo_syn_599);  // SoltFIFO.v(39)
  not logic_ramfifo_syn_601 (empty_flag, logic_ramfifo_syn_654);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_602 (logic_ramfifo_syn_638, logic_ramfifo_syn_64, logic_ramfifo_syn_19);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_603 (logic_ramfifo_syn_639, logic_ramfifo_syn_65, logic_ramfifo_syn_20);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_604 (logic_ramfifo_syn_640, logic_ramfifo_syn_66, logic_ramfifo_syn_21);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_605 (logic_ramfifo_syn_641, logic_ramfifo_syn_67, logic_ramfifo_syn_22);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_606 (logic_ramfifo_syn_642, logic_ramfifo_syn_68, logic_ramfifo_syn_23);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_607 (logic_ramfifo_syn_643, logic_ramfifo_syn_69, logic_ramfifo_syn_24);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_608 (logic_ramfifo_syn_644, logic_ramfifo_syn_70, logic_ramfifo_syn_25);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_609 (logic_ramfifo_syn_645, logic_ramfifo_syn_71, logic_ramfifo_syn_26);  // SoltFIFO.v(39)
  xor logic_ramfifo_syn_610 (logic_ramfifo_syn_646, logic_ramfifo_syn_72, logic_ramfifo_syn_27);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_611 (logic_ramfifo_syn_647, logic_ramfifo_syn_638, logic_ramfifo_syn_639);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_612 (logic_ramfifo_syn_648, logic_ramfifo_syn_640, logic_ramfifo_syn_641);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_613 (logic_ramfifo_syn_649, logic_ramfifo_syn_647, logic_ramfifo_syn_648);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_614 (logic_ramfifo_syn_650, logic_ramfifo_syn_642, logic_ramfifo_syn_643);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_615 (logic_ramfifo_syn_651, logic_ramfifo_syn_645, logic_ramfifo_syn_646);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_616 (logic_ramfifo_syn_652, logic_ramfifo_syn_644, logic_ramfifo_syn_651);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_617 (logic_ramfifo_syn_653, logic_ramfifo_syn_650, logic_ramfifo_syn_652);  // SoltFIFO.v(39)
  or logic_ramfifo_syn_618 (logic_ramfifo_syn_654, logic_ramfifo_syn_649, logic_ramfifo_syn_653);  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB_CARRY"))
    logic_ramfifo_syn_655 (
    .a(1'b0),
    .o({logic_ramfifo_syn_693,open_n121}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_656 (
    .a(logic_ramfifo_syn_55),
    .b(logic_ramfifo_syn_46),
    .c(logic_ramfifo_syn_693),
    .o({logic_ramfifo_syn_694,wrusedw[0]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_657 (
    .a(logic_ramfifo_syn_56),
    .b(logic_ramfifo_syn_47),
    .c(logic_ramfifo_syn_694),
    .o({logic_ramfifo_syn_695,wrusedw[1]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_658 (
    .a(logic_ramfifo_syn_57),
    .b(logic_ramfifo_syn_48),
    .c(logic_ramfifo_syn_695),
    .o({logic_ramfifo_syn_696,wrusedw[2]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_659 (
    .a(logic_ramfifo_syn_58),
    .b(logic_ramfifo_syn_49),
    .c(logic_ramfifo_syn_696),
    .o({logic_ramfifo_syn_697,wrusedw[3]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_660 (
    .a(logic_ramfifo_syn_59),
    .b(logic_ramfifo_syn_50),
    .c(logic_ramfifo_syn_697),
    .o({logic_ramfifo_syn_698,wrusedw[4]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_661 (
    .a(logic_ramfifo_syn_60),
    .b(logic_ramfifo_syn_51),
    .c(logic_ramfifo_syn_698),
    .o({logic_ramfifo_syn_699,wrusedw[5]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_662 (
    .a(logic_ramfifo_syn_61),
    .b(logic_ramfifo_syn_52),
    .c(logic_ramfifo_syn_699),
    .o({logic_ramfifo_syn_700,wrusedw[6]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_663 (
    .a(logic_ramfifo_syn_62),
    .b(logic_ramfifo_syn_53),
    .c(logic_ramfifo_syn_700),
    .o({logic_ramfifo_syn_701,wrusedw[7]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_664 (
    .a(logic_ramfifo_syn_18),
    .b(logic_ramfifo_syn_54),
    .c(logic_ramfifo_syn_701),
    .o({open_n122,wrusedw[8]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB_CARRY"))
    logic_ramfifo_syn_703 (
    .a(1'b0),
    .o({logic_ramfifo_syn_741,open_n125}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_704 (
    .a(logic_ramfifo_syn_73),
    .b(logic_ramfifo_syn_82),
    .c(logic_ramfifo_syn_741),
    .o({logic_ramfifo_syn_742,rdusedw[0]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_705 (
    .a(logic_ramfifo_syn_74),
    .b(logic_ramfifo_syn_83),
    .c(logic_ramfifo_syn_742),
    .o({logic_ramfifo_syn_743,rdusedw[1]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_706 (
    .a(logic_ramfifo_syn_75),
    .b(logic_ramfifo_syn_84),
    .c(logic_ramfifo_syn_743),
    .o({logic_ramfifo_syn_744,rdusedw[2]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_707 (
    .a(logic_ramfifo_syn_76),
    .b(logic_ramfifo_syn_85),
    .c(logic_ramfifo_syn_744),
    .o({logic_ramfifo_syn_745,rdusedw[3]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_708 (
    .a(logic_ramfifo_syn_77),
    .b(logic_ramfifo_syn_86),
    .c(logic_ramfifo_syn_745),
    .o({logic_ramfifo_syn_746,rdusedw[4]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_709 (
    .a(logic_ramfifo_syn_78),
    .b(logic_ramfifo_syn_87),
    .c(logic_ramfifo_syn_746),
    .o({logic_ramfifo_syn_747,rdusedw[5]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_710 (
    .a(logic_ramfifo_syn_79),
    .b(logic_ramfifo_syn_88),
    .c(logic_ramfifo_syn_747),
    .o({logic_ramfifo_syn_748,rdusedw[6]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_711 (
    .a(logic_ramfifo_syn_80),
    .b(logic_ramfifo_syn_89),
    .c(logic_ramfifo_syn_748),
    .o({logic_ramfifo_syn_749,rdusedw[7]}));  // SoltFIFO.v(39)
  AL_MAP_ADDER #(
    .ALUTYPE("SUB"))
    logic_ramfifo_syn_712 (
    .a(logic_ramfifo_syn_81),
    .b(logic_ramfifo_syn_45),
    .c(logic_ramfifo_syn_749),
    .o({open_n126,rdusedw[8]}));  // SoltFIFO.v(39)
  and re_syn_1 (re_syn_2, re, logic_ramfifo_syn_259);  // SoltFIFO.v(25)
  AL_MUX re_syn_777 (
    .i0(clk_syn_78),
    .i1(clk_syn_107),
    .sel(re_syn_2),
    .o(clk_syn_144));  // SoltFIFO.v(25)
  AL_MUX re_syn_782 (
    .i0(clk_syn_79),
    .i1(clk_syn_111),
    .sel(re_syn_2),
    .o(clk_syn_145));  // SoltFIFO.v(25)
  AL_MUX re_syn_787 (
    .i0(clk_syn_80),
    .i1(clk_syn_115),
    .sel(re_syn_2),
    .o(clk_syn_146));  // SoltFIFO.v(25)
  AL_MUX re_syn_792 (
    .i0(clk_syn_81),
    .i1(clk_syn_119),
    .sel(re_syn_2),
    .o(clk_syn_147));  // SoltFIFO.v(25)
  AL_MUX re_syn_797 (
    .i0(clk_syn_82),
    .i1(clk_syn_123),
    .sel(re_syn_2),
    .o(clk_syn_148));  // SoltFIFO.v(25)
  AL_MUX re_syn_802 (
    .i0(clk_syn_83),
    .i1(clk_syn_127),
    .sel(re_syn_2),
    .o(clk_syn_149));  // SoltFIFO.v(25)
  AL_MUX re_syn_807 (
    .i0(clk_syn_84),
    .i1(clk_syn_131),
    .sel(re_syn_2),
    .o(clk_syn_150));  // SoltFIFO.v(25)
  AL_MUX re_syn_812 (
    .i0(clk_syn_85),
    .i1(clk_syn_135),
    .sel(re_syn_2),
    .o(clk_syn_151));  // SoltFIFO.v(25)
  AL_MUX re_syn_817 (
    .i0(clk_syn_86),
    .i1(clk_syn_139),
    .sel(re_syn_2),
    .o(clk_syn_152));  // SoltFIFO.v(25)
  AL_MUX re_syn_822 (
    .i0(clk_syn_87),
    .i1(clk_syn_143),
    .sel(re_syn_2),
    .o(clk_syn_153));  // SoltFIFO.v(25)
  AL_MUX re_syn_827 (
    .i0(logic_ramfifo_syn_19),
    .i1(clk_syn_79),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_279));  // SoltFIFO.v(25)
  AL_MUX re_syn_832 (
    .i0(logic_ramfifo_syn_20),
    .i1(clk_syn_80),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_280));  // SoltFIFO.v(25)
  AL_MUX re_syn_837 (
    .i0(logic_ramfifo_syn_21),
    .i1(clk_syn_81),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_281));  // SoltFIFO.v(25)
  AL_MUX re_syn_842 (
    .i0(logic_ramfifo_syn_22),
    .i1(clk_syn_82),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_282));  // SoltFIFO.v(25)
  AL_MUX re_syn_847 (
    .i0(logic_ramfifo_syn_23),
    .i1(clk_syn_83),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_283));  // SoltFIFO.v(25)
  AL_MUX re_syn_852 (
    .i0(logic_ramfifo_syn_24),
    .i1(clk_syn_84),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_284));  // SoltFIFO.v(25)
  AL_MUX re_syn_857 (
    .i0(logic_ramfifo_syn_25),
    .i1(clk_syn_85),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_285));  // SoltFIFO.v(25)
  AL_MUX re_syn_862 (
    .i0(logic_ramfifo_syn_26),
    .i1(clk_syn_86),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_286));  // SoltFIFO.v(25)
  AL_MUX re_syn_867 (
    .i0(logic_ramfifo_syn_27),
    .i1(clk_syn_87),
    .sel(re_syn_2),
    .o(logic_ramfifo_syn_287));  // SoltFIFO.v(25)
  and we_syn_1 (we_syn_2, we, logic_ramfifo_syn_174);  // SoltFIFO.v(24)
  AL_MUX we_syn_104 (
    .i0(logic_ramfifo_syn_7),
    .i1(clk_syn_8),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_181));  // SoltFIFO.v(24)
  AL_MUX we_syn_109 (
    .i0(logic_ramfifo_syn_8),
    .i1(clk_syn_9),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_182));  // SoltFIFO.v(24)
  AL_MUX we_syn_114 (
    .i0(logic_ramfifo_syn_9),
    .i1(clk_syn_10),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_183));  // SoltFIFO.v(24)
  AL_MUX we_syn_24 (
    .i0(clk_syn_1),
    .i1(clk_syn_14),
    .sel(we_syn_2),
    .o(clk_syn_67));  // SoltFIFO.v(24)
  AL_MUX we_syn_29 (
    .i0(clk_syn_2),
    .i1(clk_syn_34),
    .sel(we_syn_2),
    .o(clk_syn_68));  // SoltFIFO.v(24)
  AL_MUX we_syn_34 (
    .i0(clk_syn_3),
    .i1(clk_syn_38),
    .sel(we_syn_2),
    .o(clk_syn_69));  // SoltFIFO.v(24)
  AL_MUX we_syn_39 (
    .i0(clk_syn_4),
    .i1(clk_syn_42),
    .sel(we_syn_2),
    .o(clk_syn_70));  // SoltFIFO.v(24)
  AL_MUX we_syn_44 (
    .i0(clk_syn_5),
    .i1(clk_syn_46),
    .sel(we_syn_2),
    .o(clk_syn_71));  // SoltFIFO.v(24)
  AL_MUX we_syn_49 (
    .i0(clk_syn_6),
    .i1(clk_syn_50),
    .sel(we_syn_2),
    .o(clk_syn_72));  // SoltFIFO.v(24)
  AL_MUX we_syn_54 (
    .i0(clk_syn_7),
    .i1(clk_syn_54),
    .sel(we_syn_2),
    .o(clk_syn_73));  // SoltFIFO.v(24)
  AL_MUX we_syn_59 (
    .i0(clk_syn_8),
    .i1(clk_syn_58),
    .sel(we_syn_2),
    .o(clk_syn_74));  // SoltFIFO.v(24)
  AL_MUX we_syn_64 (
    .i0(clk_syn_9),
    .i1(clk_syn_62),
    .sel(we_syn_2),
    .o(clk_syn_75));  // SoltFIFO.v(24)
  AL_MUX we_syn_69 (
    .i0(clk_syn_10),
    .i1(clk_syn_66),
    .sel(we_syn_2),
    .o(clk_syn_76));  // SoltFIFO.v(24)
  AL_MUX we_syn_74 (
    .i0(logic_ramfifo_syn_1),
    .i1(clk_syn_2),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_175));  // SoltFIFO.v(24)
  AL_MUX we_syn_79 (
    .i0(logic_ramfifo_syn_2),
    .i1(clk_syn_3),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_176));  // SoltFIFO.v(24)
  AL_MUX we_syn_84 (
    .i0(logic_ramfifo_syn_3),
    .i1(clk_syn_4),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_177));  // SoltFIFO.v(24)
  AL_MUX we_syn_89 (
    .i0(logic_ramfifo_syn_4),
    .i1(clk_syn_5),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_178));  // SoltFIFO.v(24)
  AL_MUX we_syn_94 (
    .i0(logic_ramfifo_syn_5),
    .i1(clk_syn_6),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_179));  // SoltFIFO.v(24)
  AL_MUX we_syn_99 (
    .i0(logic_ramfifo_syn_6),
    .i1(clk_syn_7),
    .sel(we_syn_2),
    .o(logic_ramfifo_syn_180));  // SoltFIFO.v(24)

endmodule 

