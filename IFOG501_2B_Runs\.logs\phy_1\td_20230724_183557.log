============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 18:35:57 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1636 instances
RUN-0007 : 371 luts, 1010 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2177 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1621 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     209     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1634 instances, 371 luts, 1010 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7766, tnet num: 2175, tinst num: 1634, tnode num: 11031, tedge num: 13131.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2175 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.306075s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (102.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 638432
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1634.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 532645, overlap = 20.25
PHY-3002 : Step(2): len = 492653, overlap = 20.25
PHY-3002 : Step(3): len = 416026, overlap = 13.5
PHY-3002 : Step(4): len = 361611, overlap = 15.75
PHY-3002 : Step(5): len = 330121, overlap = 15.75
PHY-3002 : Step(6): len = 306314, overlap = 20.25
PHY-3002 : Step(7): len = 298465, overlap = 20.25
PHY-3002 : Step(8): len = 291254, overlap = 20.25
PHY-3002 : Step(9): len = 283051, overlap = 20.25
PHY-3002 : Step(10): len = 271423, overlap = 20.25
PHY-3002 : Step(11): len = 264433, overlap = 20.25
PHY-3002 : Step(12): len = 257448, overlap = 20.25
PHY-3002 : Step(13): len = 248648, overlap = 20.25
PHY-3002 : Step(14): len = 240222, overlap = 20.25
PHY-3002 : Step(15): len = 236526, overlap = 20.25
PHY-3002 : Step(16): len = 229363, overlap = 20.25
PHY-3002 : Step(17): len = 218228, overlap = 20.25
PHY-3002 : Step(18): len = 213891, overlap = 20.25
PHY-3002 : Step(19): len = 210558, overlap = 20.25
PHY-3002 : Step(20): len = 194448, overlap = 20.25
PHY-3002 : Step(21): len = 187092, overlap = 20.25
PHY-3002 : Step(22): len = 185737, overlap = 20.25
PHY-3002 : Step(23): len = 176334, overlap = 20.25
PHY-3002 : Step(24): len = 162636, overlap = 20.25
PHY-3002 : Step(25): len = 159200, overlap = 20.25
PHY-3002 : Step(26): len = 157038, overlap = 20.25
PHY-3002 : Step(27): len = 150572, overlap = 20.25
PHY-3002 : Step(28): len = 147113, overlap = 20.25
PHY-3002 : Step(29): len = 142804, overlap = 20.25
PHY-3002 : Step(30): len = 138995, overlap = 20.25
PHY-3002 : Step(31): len = 136926, overlap = 20.25
PHY-3002 : Step(32): len = 132235, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.08747e-05
PHY-3002 : Step(33): len = 133592, overlap = 15.75
PHY-3002 : Step(34): len = 133599, overlap = 15.75
PHY-3002 : Step(35): len = 132206, overlap = 18
PHY-3002 : Step(36): len = 129240, overlap = 15.75
PHY-3002 : Step(37): len = 126504, overlap = 15.75
PHY-3002 : Step(38): len = 125063, overlap = 9
PHY-3002 : Step(39): len = 120113, overlap = 11.25
PHY-3002 : Step(40): len = 115778, overlap = 13.5
PHY-3002 : Step(41): len = 114969, overlap = 11.25
PHY-3002 : Step(42): len = 112138, overlap = 11.25
PHY-3002 : Step(43): len = 109665, overlap = 11.25
PHY-3002 : Step(44): len = 104348, overlap = 15.75
PHY-3002 : Step(45): len = 102528, overlap = 13.5
PHY-3002 : Step(46): len = 100775, overlap = 13.5
PHY-3002 : Step(47): len = 98554.1, overlap = 13.5
PHY-3002 : Step(48): len = 98184.1, overlap = 13.5
PHY-3002 : Step(49): len = 96830, overlap = 11.25
PHY-3002 : Step(50): len = 95231.3, overlap = 11.25
PHY-3002 : Step(51): len = 93630, overlap = 11.25
PHY-3002 : Step(52): len = 90827.6, overlap = 11.25
PHY-3002 : Step(53): len = 88068.5, overlap = 11.25
PHY-3002 : Step(54): len = 87962, overlap = 9
PHY-3002 : Step(55): len = 87044.8, overlap = 9
PHY-3002 : Step(56): len = 85997.2, overlap = 11.25
PHY-3002 : Step(57): len = 83237.6, overlap = 13.5
PHY-3002 : Step(58): len = 80569, overlap = 15.8125
PHY-3002 : Step(59): len = 79548.3, overlap = 15.8125
PHY-3002 : Step(60): len = 78489.8, overlap = 13.5625
PHY-3002 : Step(61): len = 77340.4, overlap = 9.1875
PHY-3002 : Step(62): len = 75427.9, overlap = 11.625
PHY-3002 : Step(63): len = 73662.7, overlap = 11.5625
PHY-3002 : Step(64): len = 73294.2, overlap = 11.5625
PHY-3002 : Step(65): len = 71527.2, overlap = 13.875
PHY-3002 : Step(66): len = 69769, overlap = 12.5
PHY-3002 : Step(67): len = 68265.2, overlap = 14.8125
PHY-3002 : Step(68): len = 67412.4, overlap = 12.9375
PHY-3002 : Step(69): len = 67288.2, overlap = 10.9375
PHY-3002 : Step(70): len = 67004.9, overlap = 10.9375
PHY-3002 : Step(71): len = 66657.6, overlap = 8.6875
PHY-3002 : Step(72): len = 66305.7, overlap = 13.375
PHY-3002 : Step(73): len = 65461.4, overlap = 15.4375
PHY-3002 : Step(74): len = 63110, overlap = 13.6875
PHY-3002 : Step(75): len = 59581.2, overlap = 14.5
PHY-3002 : Step(76): len = 59396.8, overlap = 14.625
PHY-3002 : Step(77): len = 58748.5, overlap = 14.6875
PHY-3002 : Step(78): len = 58433.1, overlap = 14.75
PHY-3002 : Step(79): len = 58276.9, overlap = 17.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000161749
PHY-3002 : Step(80): len = 58165.9, overlap = 14.8125
PHY-3002 : Step(81): len = 58201.1, overlap = 12.5625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000323499
PHY-3002 : Step(82): len = 58285.2, overlap = 14.75
PHY-3002 : Step(83): len = 58409.2, overlap = 14.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006360s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (737.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2175 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060730s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00171609
PHY-3002 : Step(84): len = 62340.4, overlap = 12.4688
PHY-3002 : Step(85): len = 61238.5, overlap = 12.5
PHY-3002 : Step(86): len = 60675.1, overlap = 12.75
PHY-3002 : Step(87): len = 59810.8, overlap = 13
PHY-3002 : Step(88): len = 59078.6, overlap = 12.9375
PHY-3002 : Step(89): len = 57913.8, overlap = 13.3438
PHY-3002 : Step(90): len = 56895.9, overlap = 13.2188
PHY-3002 : Step(91): len = 56055.8, overlap = 13.5938
PHY-3002 : Step(92): len = 54918.1, overlap = 13.7812
PHY-3002 : Step(93): len = 53670.1, overlap = 13.6562
PHY-3002 : Step(94): len = 52179.8, overlap = 12.2812
PHY-3002 : Step(95): len = 51355.2, overlap = 11.9062
PHY-3002 : Step(96): len = 51037.8, overlap = 11
PHY-3002 : Step(97): len = 49871.8, overlap = 10.6562
PHY-3002 : Step(98): len = 49692.8, overlap = 10.7812
PHY-3002 : Step(99): len = 49155.4, overlap = 10.3125
PHY-3002 : Step(100): len = 48811.7, overlap = 12.4375
PHY-3002 : Step(101): len = 48136.6, overlap = 13.5
PHY-3002 : Step(102): len = 47375.6, overlap = 13.3125
PHY-3002 : Step(103): len = 46694, overlap = 14.25
PHY-3002 : Step(104): len = 46426, overlap = 13.875
PHY-3002 : Step(105): len = 46318.7, overlap = 13.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00343218
PHY-3002 : Step(106): len = 46380.5, overlap = 12.875
PHY-3002 : Step(107): len = 46237.3, overlap = 12.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00686436
PHY-3002 : Step(108): len = 46184.6, overlap = 12.875
PHY-3002 : Step(109): len = 46183.1, overlap = 12.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2175 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064551s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (121.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.34434e-05
PHY-3002 : Step(110): len = 46200.3, overlap = 60.2812
PHY-3002 : Step(111): len = 46738.2, overlap = 59.7812
PHY-3002 : Step(112): len = 47025.1, overlap = 58.8125
PHY-3002 : Step(113): len = 47263.2, overlap = 46.9062
PHY-3002 : Step(114): len = 47408.2, overlap = 46.1875
PHY-3002 : Step(115): len = 47197.2, overlap = 45.2188
PHY-3002 : Step(116): len = 47109.6, overlap = 43.8438
PHY-3002 : Step(117): len = 47232.2, overlap = 41.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000146887
PHY-3002 : Step(118): len = 47305.4, overlap = 41.2812
PHY-3002 : Step(119): len = 47512.4, overlap = 41.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000293774
PHY-3002 : Step(120): len = 47587.2, overlap = 40.8438
PHY-3002 : Step(121): len = 49212.4, overlap = 36.5
PHY-3002 : Step(122): len = 49822.9, overlap = 33.5625
PHY-3002 : Step(123): len = 49474.8, overlap = 31.75
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7766, tnet num: 2175, tinst num: 1634, tnode num: 11031, tedge num: 13131.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.44 peak overflow 3.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2177.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53072, over cnt = 238(0%), over = 1025, worst = 22
PHY-1001 : End global iterations;  0.077141s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (121.5%)

PHY-1001 : Congestion index: top1 = 43.28, top5 = 24.77, top10 = 15.77, top15 = 11.20.
PHY-1001 : End incremental global routing;  0.128909s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (109.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2175 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.075526s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.293969s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (106.3%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1661/2177.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53072, over cnt = 238(0%), over = 1025, worst = 22
PHY-1002 : len = 58488, over cnt = 191(0%), over = 516, worst = 17
PHY-1002 : len = 61864, over cnt = 104(0%), over = 254, worst = 17
PHY-1002 : len = 65816, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 66152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.089625s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (122.0%)

PHY-1001 : Congestion index: top1 = 39.01, top5 = 25.41, top10 = 18.26, top15 = 13.40.
OPT-1001 : End congestion update;  0.142392s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (109.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2175 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059777s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.204698s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (106.9%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.784133s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (103.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 371 LUT to BLE ...
SYN-4008 : Packed 371 LUT and 183 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 102 SEQ with LUT/SLICE
SYN-4006 : 110 single LUT's are left
SYN-4006 : 725 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1096/1409 primitive instances ...
PHY-3001 : End packing;  0.053865s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (116.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 830 instances
RUN-1001 : 391 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1455 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 828 instances, 781 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49797.8, Over = 55.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6516, tnet num: 2006, tinst num: 828, tnode num: 8855, tedge num: 11467.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.329665s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.34643e-05
PHY-3002 : Step(124): len = 49370.3, overlap = 54.5
PHY-3002 : Step(125): len = 49271.9, overlap = 56.75
PHY-3002 : Step(126): len = 48982.7, overlap = 57.5
PHY-3002 : Step(127): len = 48898.2, overlap = 61.25
PHY-3002 : Step(128): len = 48925.2, overlap = 59.75
PHY-3002 : Step(129): len = 48937.7, overlap = 58
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.69285e-05
PHY-3002 : Step(130): len = 49150.4, overlap = 58.5
PHY-3002 : Step(131): len = 49420, overlap = 56.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000125096
PHY-3002 : Step(132): len = 49843.7, overlap = 54.25
PHY-3002 : Step(133): len = 50834.7, overlap = 50.5
PHY-3002 : Step(134): len = 51736.5, overlap = 43
PHY-3002 : Step(135): len = 51794, overlap = 42.75
PHY-3002 : Step(136): len = 51844.2, overlap = 43.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.097126s wall, 0.062500s user + 0.062500s system = 0.125000s CPU (128.7%)

PHY-3001 : Trial Legalized: Len = 64190.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052246s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000681592
PHY-3002 : Step(137): len = 61801.4, overlap = 4.75
PHY-3002 : Step(138): len = 59540.7, overlap = 8.5
PHY-3002 : Step(139): len = 57676.3, overlap = 12.25
PHY-3002 : Step(140): len = 56569.4, overlap = 15.5
PHY-3002 : Step(141): len = 55735.2, overlap = 19.75
PHY-3002 : Step(142): len = 55183, overlap = 21.5
PHY-3002 : Step(143): len = 55002.4, overlap = 22.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00136318
PHY-3002 : Step(144): len = 55227.3, overlap = 21.5
PHY-3002 : Step(145): len = 55373.3, overlap = 20.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00272637
PHY-3002 : Step(146): len = 55507.8, overlap = 20.25
PHY-3002 : Step(147): len = 55507.8, overlap = 20.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005105s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 59987.9, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006603s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 2, deltaY = 2, maxDist = 1.
PHY-3001 : Final: Len = 60029.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6516, tnet num: 2006, tinst num: 828, tnode num: 8855, tedge num: 11467.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 96/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66160, over cnt = 175(0%), over = 243, worst = 7
PHY-1002 : len = 67032, over cnt = 108(0%), over = 127, worst = 3
PHY-1002 : len = 68408, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 68640, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 68688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.136395s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (183.3%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.31, top10 = 17.54, top15 = 13.77.
PHY-1001 : End incremental global routing;  0.186641s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (159.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059632s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.277228s wall, 0.343750s user + 0.031250s system = 0.375000s CPU (135.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1771/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006536s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (239.1%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.31, top10 = 17.54, top15 = 13.77.
OPT-1001 : End congestion update;  0.053981s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050334s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 790 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 828 instances, 781 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 60041.8, Over = 0
PHY-3001 : End spreading;  0.005107s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 60041.8, Over = 0
PHY-3001 : End incremental legalization;  0.035432s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (441.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.151939s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (174.8%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052297s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1767/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68688, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 68688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.016523s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (189.1%)

PHY-1001 : Congestion index: top1 = 30.78, top5 = 22.30, top10 = 17.54, top15 = 13.77.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048808s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.344828
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.907181s wall, 1.156250s user + 0.062500s system = 1.218750s CPU (134.3%)

RUN-1003 : finish command "place" in  5.431053s wall, 9.093750s user + 2.390625s system = 11.484375s CPU (211.5%)

RUN-1004 : used memory is 200 MB, reserved memory is 165 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 830 instances
RUN-1001 : 391 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1455 nets have 2 pins
RUN-1001 : 438 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6516, tnet num: 2006, tinst num: 828, tnode num: 8855, tedge num: 11467.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 391 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64952, over cnt = 176(0%), over = 246, worst = 7
PHY-1002 : len = 66200, over cnt = 80(0%), over = 92, worst = 3
PHY-1002 : len = 66936, over cnt = 30(0%), over = 35, worst = 3
PHY-1002 : len = 67456, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146511s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (138.6%)

PHY-1001 : Congestion index: top1 = 30.37, top5 = 21.81, top10 = 17.22, top15 = 13.55.
PHY-1001 : End global routing;  0.195202s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (128.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 207, peak = 242.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 502, reserve = 471, peak = 502.
PHY-1001 : End build detailed router design. 6.271954s wall, 6.218750s user + 0.078125s system = 6.296875s CPU (100.4%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34168, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.127591s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 534, reserve = 505, peak = 534.
PHY-1001 : End phase 1; 2.135201s wall, 2.125000s user + 0.000000s system = 2.125000s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 51% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178368, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 505, peak = 535.
PHY-1001 : End initial routed; 1.490994s wall, 2.531250s user + 0.156250s system = 2.687500s CPU (180.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1789(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.711   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.654005s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 538, reserve = 508, peak = 538.
PHY-1001 : End phase 2; 2.145254s wall, 3.187500s user + 0.156250s system = 3.343750s CPU (155.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178368, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.028681s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (109.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178352, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.046085s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (169.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178392, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.039468s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 178408, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.030357s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (102.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1789(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.711   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.641214s wall, 0.640625s user + 0.000000s system = 0.640625s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.351322s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (97.8%)

PHY-1001 : Current memory(MB): used = 552, reserve = 522, peak = 552.
PHY-1001 : End phase 3; 1.396579s wall, 1.421875s user + 0.000000s system = 1.421875s CPU (101.8%)

PHY-1003 : Routed, final wirelength = 178408
PHY-1001 : Current memory(MB): used = 552, reserve = 522, peak = 552.
PHY-1001 : End export database. 0.015605s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (100.1%)

PHY-1001 : End detail routing;  12.293476s wall, 13.281250s user + 0.250000s system = 13.531250s CPU (110.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6516, tnet num: 2006, tinst num: 828, tnode num: 8855, tedge num: 11467.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  13.583304s wall, 14.609375s user + 0.250000s system = 14.859375s CPU (109.4%)

RUN-1004 : used memory is 504 MB, reserved memory is 480 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      790   out of  19600    4.03%
#reg                     1075   out of  19600    5.48%
#le                      1515
  #lut only               440   out of   1515   29.04%
  #reg only               725   out of   1515   47.85%
  #lut&reg                350   out of   1515   23.10%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         467
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1515   |584     |206     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1130   |286     |125     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |22      |9       |21      |0       |0       |
|    demodu                  |Demodulation                                     |521    |117     |53      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |0       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |16      |0       |28      |0       |0       |
|    integ                   |Integration                                      |140    |22      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |95     |18      |15      |81      |0       |1       |
|    rs422                   |Rs422Output                                      |309    |78      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |34     |29      |5       |22      |0       |0       |
|  u_uart                    |UART_Control                                     |114    |101     |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |56     |55      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1419  
    #2          2       314   
    #3          3       104   
    #4          4        20   
    #5        5-10       77   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6516, tnet num: 2006, tinst num: 828, tnode num: 8855, tedge num: 11467.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 828
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2008, pip num: 14589
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1268 valid insts, and 38456 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.693075s wall, 19.812500s user + 0.125000s system = 19.937500s CPU (539.9%)

RUN-1004 : used memory is 545 MB, reserved memory is 513 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_183557.log"
