============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Oct 25 13:59:48 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 301 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 301 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1522 instances
RUN-0007 : 373 luts, 895 seqs, 130 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2062 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1545 nets have 2 pins
RUN-1001 : 401 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     242     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1520 instances, 373 luts, 895 seqs, 205 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7273, tnet num: 2060, tinst num: 1520, tnode num: 10204, tedge num: 12308.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2060 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.280263s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 540116
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1520.
PHY-3001 : End clustering;  0.000029s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 445432, overlap = 20.25
PHY-3002 : Step(2): len = 412673, overlap = 18
PHY-3002 : Step(3): len = 400043, overlap = 20.25
PHY-3002 : Step(4): len = 376860, overlap = 20.25
PHY-3002 : Step(5): len = 369719, overlap = 20.25
PHY-3002 : Step(6): len = 353792, overlap = 20.25
PHY-3002 : Step(7): len = 341819, overlap = 20.25
PHY-3002 : Step(8): len = 333179, overlap = 20.25
PHY-3002 : Step(9): len = 326806, overlap = 20.25
PHY-3002 : Step(10): len = 285761, overlap = 15.75
PHY-3002 : Step(11): len = 274323, overlap = 20.25
PHY-3002 : Step(12): len = 261952, overlap = 20.25
PHY-3002 : Step(13): len = 258467, overlap = 20.25
PHY-3002 : Step(14): len = 255733, overlap = 20.25
PHY-3002 : Step(15): len = 242213, overlap = 18
PHY-3002 : Step(16): len = 237124, overlap = 20.25
PHY-3002 : Step(17): len = 234505, overlap = 20.25
PHY-3002 : Step(18): len = 222215, overlap = 20.25
PHY-3002 : Step(19): len = 213004, overlap = 20.25
PHY-3002 : Step(20): len = 210432, overlap = 20.25
PHY-3002 : Step(21): len = 202029, overlap = 20.25
PHY-3002 : Step(22): len = 191972, overlap = 20.25
PHY-3002 : Step(23): len = 189190, overlap = 20.25
PHY-3002 : Step(24): len = 183486, overlap = 20.25
PHY-3002 : Step(25): len = 177838, overlap = 20.25
PHY-3002 : Step(26): len = 175218, overlap = 20.25
PHY-3002 : Step(27): len = 169357, overlap = 20.25
PHY-3002 : Step(28): len = 161118, overlap = 20.25
PHY-3002 : Step(29): len = 157620, overlap = 20.25
PHY-3002 : Step(30): len = 154669, overlap = 20.25
PHY-3002 : Step(31): len = 147906, overlap = 20.25
PHY-3002 : Step(32): len = 144159, overlap = 20.25
PHY-3002 : Step(33): len = 140600, overlap = 20.25
PHY-3002 : Step(34): len = 136093, overlap = 20.25
PHY-3002 : Step(35): len = 133339, overlap = 20.25
PHY-3002 : Step(36): len = 129068, overlap = 20.25
PHY-3002 : Step(37): len = 123171, overlap = 18
PHY-3002 : Step(38): len = 120015, overlap = 20.25
PHY-3002 : Step(39): len = 117270, overlap = 20.25
PHY-3002 : Step(40): len = 114399, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.30551e-05
PHY-3002 : Step(41): len = 114423, overlap = 11.25
PHY-3002 : Step(42): len = 113837, overlap = 15.75
PHY-3002 : Step(43): len = 113063, overlap = 15.75
PHY-3002 : Step(44): len = 112006, overlap = 13.5
PHY-3002 : Step(45): len = 110294, overlap = 15.75
PHY-3002 : Step(46): len = 104472, overlap = 15.75
PHY-3002 : Step(47): len = 102497, overlap = 15.75
PHY-3002 : Step(48): len = 100859, overlap = 15.75
PHY-3002 : Step(49): len = 99594.6, overlap = 11.25
PHY-3002 : Step(50): len = 98187.9, overlap = 13.5
PHY-3002 : Step(51): len = 96294.5, overlap = 15.75
PHY-3002 : Step(52): len = 94259.6, overlap = 15.75
PHY-3002 : Step(53): len = 90333, overlap = 18
PHY-3002 : Step(54): len = 88919.3, overlap = 18
PHY-3002 : Step(55): len = 87537.4, overlap = 15.75
PHY-3002 : Step(56): len = 86349.9, overlap = 13.5
PHY-3002 : Step(57): len = 84288.4, overlap = 9
PHY-3002 : Step(58): len = 82086.3, overlap = 11.25
PHY-3002 : Step(59): len = 80972.3, overlap = 11.25
PHY-3002 : Step(60): len = 77940.8, overlap = 15.75
PHY-3002 : Step(61): len = 76517, overlap = 15.75
PHY-3002 : Step(62): len = 75620.1, overlap = 13.5
PHY-3002 : Step(63): len = 72576.2, overlap = 13.6875
PHY-3002 : Step(64): len = 71541.3, overlap = 13.8125
PHY-3002 : Step(65): len = 70630.6, overlap = 14
PHY-3002 : Step(66): len = 68398.4, overlap = 18.75
PHY-3002 : Step(67): len = 68266, overlap = 18.8125
PHY-3002 : Step(68): len = 66830.5, overlap = 12.375
PHY-3002 : Step(69): len = 64842.6, overlap = 8.1875
PHY-3002 : Step(70): len = 63953, overlap = 12.8125
PHY-3002 : Step(71): len = 63153.2, overlap = 12.9375
PHY-3002 : Step(72): len = 62108.9, overlap = 18
PHY-3002 : Step(73): len = 61447.3, overlap = 20.3125
PHY-3002 : Step(74): len = 60777.5, overlap = 18.25
PHY-3002 : Step(75): len = 60282.8, overlap = 13.8125
PHY-3002 : Step(76): len = 59944.8, overlap = 13.9375
PHY-3002 : Step(77): len = 59359, overlap = 16.375
PHY-3002 : Step(78): len = 57723.1, overlap = 16.3125
PHY-3002 : Step(79): len = 57015.9, overlap = 16.1875
PHY-3002 : Step(80): len = 56850.9, overlap = 11.625
PHY-3002 : Step(81): len = 56706.8, overlap = 11.5625
PHY-3002 : Step(82): len = 56695.9, overlap = 11.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00018611
PHY-3002 : Step(83): len = 56875.2, overlap = 11.625
PHY-3002 : Step(84): len = 56906.7, overlap = 11.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00037222
PHY-3002 : Step(85): len = 57015, overlap = 13.875
PHY-3002 : Step(86): len = 57108.1, overlap = 13.875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006032s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2060 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.055451s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00150848
PHY-3002 : Step(87): len = 60687.7, overlap = 14.5625
PHY-3002 : Step(88): len = 60734.1, overlap = 14.4375
PHY-3002 : Step(89): len = 59738.9, overlap = 14.5625
PHY-3002 : Step(90): len = 59625.8, overlap = 14.0625
PHY-3002 : Step(91): len = 57579, overlap = 12.75
PHY-3002 : Step(92): len = 56818.9, overlap = 10.9375
PHY-3002 : Step(93): len = 56400.2, overlap = 10.6562
PHY-3002 : Step(94): len = 54876.1, overlap = 14.0625
PHY-3002 : Step(95): len = 53544, overlap = 14.8125
PHY-3002 : Step(96): len = 52699.4, overlap = 14.7812
PHY-3002 : Step(97): len = 52553.8, overlap = 14.6562
PHY-3002 : Step(98): len = 51783.6, overlap = 14.8125
PHY-3002 : Step(99): len = 51618.1, overlap = 12.4688
PHY-3002 : Step(100): len = 51481.4, overlap = 12.3438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2060 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.065093s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000147605
PHY-3002 : Step(101): len = 51993.7, overlap = 52.0312
PHY-3002 : Step(102): len = 51908.9, overlap = 52.375
PHY-3002 : Step(103): len = 53114.5, overlap = 46.8125
PHY-3002 : Step(104): len = 53168.6, overlap = 45.7812
PHY-3002 : Step(105): len = 52676.8, overlap = 39.5625
PHY-3002 : Step(106): len = 52266.6, overlap = 31.625
PHY-3002 : Step(107): len = 52215.4, overlap = 33.1875
PHY-3002 : Step(108): len = 52103.1, overlap = 33.1875
PHY-3002 : Step(109): len = 51767.3, overlap = 33.5
PHY-3002 : Step(110): len = 51675.3, overlap = 31.0938
PHY-3002 : Step(111): len = 51758.6, overlap = 29.5938
PHY-3002 : Step(112): len = 51694.6, overlap = 29
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00029521
PHY-3002 : Step(113): len = 51906.9, overlap = 28.9688
PHY-3002 : Step(114): len = 52072.5, overlap = 28.4688
PHY-3002 : Step(115): len = 52211.9, overlap = 27.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00059042
PHY-3002 : Step(116): len = 52324.1, overlap = 28.0625
PHY-3002 : Step(117): len = 52556.5, overlap = 28.3438
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7273, tnet num: 2060, tinst num: 1520, tnode num: 10204, tedge num: 12308.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 79.22 peak overflow 2.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2062.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55624, over cnt = 252(0%), over = 1015, worst = 16
PHY-1001 : End global iterations;  0.078553s wall, 0.046875s user + 0.046875s system = 0.093750s CPU (119.3%)

PHY-1001 : Congestion index: top1 = 42.59, top5 = 26.29, top10 = 16.45, top15 = 11.63.
PHY-1001 : End incremental global routing;  0.126880s wall, 0.093750s user + 0.046875s system = 0.140625s CPU (110.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2060 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060433s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.214949s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (109.0%)

OPT-1001 : Current memory(MB): used = 211, reserve = 173, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1550/2062.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55624, over cnt = 252(0%), over = 1015, worst = 16
PHY-1002 : len = 60152, over cnt = 190(0%), over = 508, worst = 16
PHY-1002 : len = 61968, over cnt = 108(0%), over = 299, worst = 12
PHY-1002 : len = 65248, over cnt = 34(0%), over = 65, worst = 11
PHY-1002 : len = 66048, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.080363s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (175.0%)

PHY-1001 : Congestion index: top1 = 36.08, top5 = 25.42, top10 = 18.15, top15 = 13.24.
OPT-1001 : End congestion update;  0.120823s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (142.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2060 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051029s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.174457s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (125.4%)

OPT-1001 : Current memory(MB): used = 214, reserve = 176, peak = 214.
OPT-1001 : End physical optimization;  0.651137s wall, 0.640625s user + 0.078125s system = 0.718750s CPU (110.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 172 SEQ to BLE.
SYN-4003 : Packing 723 remaining SEQ's ...
SYN-4005 : Packed 124 SEQ with LUT/SLICE
SYN-4006 : 97 single LUT's are left
SYN-4006 : 599 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 972/1260 primitive instances ...
PHY-3001 : End packing;  0.047194s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 772 instances
RUN-1001 : 362 mslices, 361 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1898 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1381 nets have 2 pins
RUN-1001 : 401 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 770 instances, 723 slices, 23 macros(205 instances: 130 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52572.8, Over = 56.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6145, tnet num: 1896, tinst num: 770, tnode num: 8264, tedge num: 10806.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.287639s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (103.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.60835e-05
PHY-3002 : Step(118): len = 51854.8, overlap = 58.25
PHY-3002 : Step(119): len = 51225.5, overlap = 58.25
PHY-3002 : Step(120): len = 51017.6, overlap = 57.25
PHY-3002 : Step(121): len = 51132.6, overlap = 54
PHY-3002 : Step(122): len = 50995.5, overlap = 55
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.2167e-05
PHY-3002 : Step(123): len = 51195.3, overlap = 55
PHY-3002 : Step(124): len = 52158.7, overlap = 54
PHY-3002 : Step(125): len = 52749.1, overlap = 53
PHY-3002 : Step(126): len = 52716.7, overlap = 51.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000104334
PHY-3002 : Step(127): len = 53412.8, overlap = 48.75
PHY-3002 : Step(128): len = 54172.5, overlap = 48.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.068836s wall, 0.062500s user + 0.031250s system = 0.093750s CPU (136.2%)

PHY-3001 : Trial Legalized: Len = 66945.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.046850s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000464263
PHY-3002 : Step(129): len = 63344, overlap = 5
PHY-3002 : Step(130): len = 61225.9, overlap = 11.5
PHY-3002 : Step(131): len = 59736.5, overlap = 15.5
PHY-3002 : Step(132): len = 58907.5, overlap = 19
PHY-3002 : Step(133): len = 58464, overlap = 19.5
PHY-3002 : Step(134): len = 58194.1, overlap = 17.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000928525
PHY-3002 : Step(135): len = 58504.2, overlap = 18.5
PHY-3002 : Step(136): len = 58658.1, overlap = 17.75
PHY-3002 : Step(137): len = 58680, overlap = 17.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00185705
PHY-3002 : Step(138): len = 58822.5, overlap = 18
PHY-3002 : Step(139): len = 58885.2, overlap = 17.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62875.8, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005294s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 2, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 62971.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6145, tnet num: 1896, tinst num: 770, tnode num: 8264, tedge num: 10806.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 61/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69416, over cnt = 136(0%), over = 223, worst = 6
PHY-1002 : len = 69768, over cnt = 94(0%), over = 157, worst = 6
PHY-1002 : len = 70856, over cnt = 46(0%), over = 74, worst = 6
PHY-1002 : len = 71552, over cnt = 15(0%), over = 24, worst = 4
PHY-1002 : len = 71904, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.141497s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (143.6%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.51, top10 = 17.63, top15 = 13.80.
PHY-1001 : End incremental global routing;  0.189704s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (131.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053105s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.271032s wall, 0.296875s user + 0.046875s system = 0.343750s CPU (126.8%)

OPT-1001 : Current memory(MB): used = 217, reserve = 180, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1687/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71904, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005588s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (279.6%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.51, top10 = 17.63, top15 = 13.80.
OPT-1001 : End congestion update;  0.049384s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046259s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (101.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.097504s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (96.1%)

OPT-1001 : Current memory(MB): used = 219, reserve = 182, peak = 219.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044406s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1687/1898.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71904, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005713s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.70, top5 = 22.51, top10 = 17.63, top15 = 13.80.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045870s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.344828
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.781902s wall, 0.796875s user + 0.046875s system = 0.843750s CPU (107.9%)

RUN-1003 : finish command "place" in  4.833710s wall, 7.578125s user + 2.343750s system = 9.921875s CPU (205.3%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 772 instances
RUN-1001 : 362 mslices, 361 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1898 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1381 nets have 2 pins
RUN-1001 : 401 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6145, tnet num: 1896, tinst num: 770, tnode num: 8264, tedge num: 10806.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 362 mslices, 361 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1896 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68832, over cnt = 142(0%), over = 226, worst = 6
PHY-1002 : len = 69256, over cnt = 101(0%), over = 162, worst = 6
PHY-1002 : len = 71320, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 71368, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.135672s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (103.7%)

PHY-1001 : Congestion index: top1 = 31.25, top5 = 22.36, top10 = 17.50, top15 = 13.70.
PHY-1001 : End global routing;  0.182874s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (102.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 201, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 496, reserve = 464, peak = 496.
PHY-1001 : End build detailed router design. 3.137470s wall, 3.125000s user + 0.015625s system = 3.140625s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32768, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.277981s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 528, reserve = 498, peak = 528.
PHY-1001 : End phase 1; 1.283734s wall, 1.281250s user + 0.000000s system = 1.281250s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179704, over cnt = 19(0%), over = 19, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 499, peak = 531.
PHY-1001 : End initial routed; 1.316995s wall, 2.453125s user + 0.062500s system = 2.515625s CPU (191.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1679(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.110   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.321   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.335406s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (97.8%)

PHY-1001 : Current memory(MB): used = 531, reserve = 499, peak = 531.
PHY-1001 : End phase 2; 1.652488s wall, 2.781250s user + 0.062500s system = 2.843750s CPU (172.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179704, over cnt = 19(0%), over = 19, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014141s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (110.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179584, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025216s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (123.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179664, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.019103s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (81.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 179664, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.018220s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1679(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.110   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.321   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.343124s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (100.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.165087s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (104.1%)

PHY-1001 : Current memory(MB): used = 545, reserve = 513, peak = 545.
PHY-1001 : End phase 3; 0.709515s wall, 0.703125s user + 0.015625s system = 0.718750s CPU (101.3%)

PHY-1003 : Routed, final wirelength = 179664
PHY-1001 : Current memory(MB): used = 546, reserve = 513, peak = 546.
PHY-1001 : End export database. 0.010396s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  6.963774s wall, 8.046875s user + 0.093750s system = 8.140625s CPU (116.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6145, tnet num: 1896, tinst num: 770, tnode num: 8264, tedge num: 10806.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6183, tnet num: 1915, tinst num: 789, tnode num: 8302, tedge num: 10844.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.939167s wall, 3.000000s user + 0.125000s system = 3.125000s CPU (106.3%)

RUN-1003 : finish command "route" in  10.428860s wall, 11.562500s user + 0.234375s system = 11.796875s CPU (113.1%)

RUN-1004 : used memory is 527 MB, reserved memory is 495 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      826   out of  19600    4.21%
#reg                      962   out of  19600    4.91%
#le                      1425
  #lut only               463   out of   1425   32.49%
  #reg only               599   out of   1425   42.04%
  #lut&reg                363   out of   1425   25.47%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         421
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         102
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1425   |621     |205     |993     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1017   |306     |112     |811     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |21     |15      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |470    |137     |44      |351     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |36      |6       |49      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |6       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |18     |16      |0       |18      |0       |0       |
|    integ                   |Integration                                      |134    |27      |14      |108     |0       |0       |
|    modu                    |Modulation                                       |66     |23      |14      |61      |0       |1       |
|    rs422                   |Rs422Output                                      |306    |89      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |20     |15      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |117    |110     |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |19      |0       |0       |
|    U1                      |uart_tx                                          |24     |24      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |58     |58      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1364  
    #2          2       266   
    #3          3       123   
    #4          4        12   
    #5        5-10       80   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6183, tnet num: 1915, tinst num: 789, tnode num: 8302, tedge num: 10844.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1915 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 789
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1917, pip num: 14219
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1304 valid insts, and 37772 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.989265s wall, 17.093750s user + 0.015625s system = 17.109375s CPU (572.4%)

RUN-1004 : used memory is 547 MB, reserved memory is 514 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231025_135948.log"
