============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Jul 24 17:51:48 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1623 instances
RUN-0007 : 368 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2193 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1635 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1621 instances, 368 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7773, tnet num: 2191, tinst num: 1621, tnode num: 11013, tedge num: 13149.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.286983s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (103.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 595136
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1621.
PHY-3001 : End clustering;  0.000020s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 473560, overlap = 20.25
PHY-3002 : Step(2): len = 436660, overlap = 20.25
PHY-3002 : Step(3): len = 392898, overlap = 20.25
PHY-3002 : Step(4): len = 363489, overlap = 18
PHY-3002 : Step(5): len = 353118, overlap = 20.25
PHY-3002 : Step(6): len = 338237, overlap = 20.25
PHY-3002 : Step(7): len = 321333, overlap = 18
PHY-3002 : Step(8): len = 312763, overlap = 18
PHY-3002 : Step(9): len = 306625, overlap = 18
PHY-3002 : Step(10): len = 290043, overlap = 20.25
PHY-3002 : Step(11): len = 282490, overlap = 20.25
PHY-3002 : Step(12): len = 277743, overlap = 20.25
PHY-3002 : Step(13): len = 265529, overlap = 20.25
PHY-3002 : Step(14): len = 257014, overlap = 20.25
PHY-3002 : Step(15): len = 253633, overlap = 20.25
PHY-3002 : Step(16): len = 242793, overlap = 20.25
PHY-3002 : Step(17): len = 234871, overlap = 20.25
PHY-3002 : Step(18): len = 231205, overlap = 20.25
PHY-3002 : Step(19): len = 226913, overlap = 20.25
PHY-3002 : Step(20): len = 214485, overlap = 20.25
PHY-3002 : Step(21): len = 211014, overlap = 20.25
PHY-3002 : Step(22): len = 207547, overlap = 20.25
PHY-3002 : Step(23): len = 193916, overlap = 20.25
PHY-3002 : Step(24): len = 185805, overlap = 20.25
PHY-3002 : Step(25): len = 184437, overlap = 20.25
PHY-3002 : Step(26): len = 176957, overlap = 20.25
PHY-3002 : Step(27): len = 170038, overlap = 20.25
PHY-3002 : Step(28): len = 165919, overlap = 20.25
PHY-3002 : Step(29): len = 163344, overlap = 20.25
PHY-3002 : Step(30): len = 159412, overlap = 20.25
PHY-3002 : Step(31): len = 156871, overlap = 20.25
PHY-3002 : Step(32): len = 150199, overlap = 20.25
PHY-3002 : Step(33): len = 146573, overlap = 20.25
PHY-3002 : Step(34): len = 143410, overlap = 20.25
PHY-3002 : Step(35): len = 140071, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000109034
PHY-3002 : Step(36): len = 140828, overlap = 13.5
PHY-3002 : Step(37): len = 140404, overlap = 11.25
PHY-3002 : Step(38): len = 138463, overlap = 18
PHY-3002 : Step(39): len = 137229, overlap = 18
PHY-3002 : Step(40): len = 135081, overlap = 18
PHY-3002 : Step(41): len = 128227, overlap = 9
PHY-3002 : Step(42): len = 125015, overlap = 11.25
PHY-3002 : Step(43): len = 122360, overlap = 15.75
PHY-3002 : Step(44): len = 119464, overlap = 18
PHY-3002 : Step(45): len = 115539, overlap = 18
PHY-3002 : Step(46): len = 112720, overlap = 18
PHY-3002 : Step(47): len = 111506, overlap = 15.75
PHY-3002 : Step(48): len = 108987, overlap = 11.25
PHY-3002 : Step(49): len = 106230, overlap = 11.25
PHY-3002 : Step(50): len = 103263, overlap = 9
PHY-3002 : Step(51): len = 102127, overlap = 13.5
PHY-3002 : Step(52): len = 98870.1, overlap = 9
PHY-3002 : Step(53): len = 95563.1, overlap = 6.75
PHY-3002 : Step(54): len = 92935, overlap = 11.25
PHY-3002 : Step(55): len = 92398.6, overlap = 11.25
PHY-3002 : Step(56): len = 90649.5, overlap = 11.25
PHY-3002 : Step(57): len = 89158.4, overlap = 6.75
PHY-3002 : Step(58): len = 87084.6, overlap = 6.75
PHY-3002 : Step(59): len = 85719.1, overlap = 9
PHY-3002 : Step(60): len = 84199.9, overlap = 11.25
PHY-3002 : Step(61): len = 82358.7, overlap = 11.25
PHY-3002 : Step(62): len = 81630.1, overlap = 13.5
PHY-3002 : Step(63): len = 80028.8, overlap = 13.5
PHY-3002 : Step(64): len = 79238.4, overlap = 13.5
PHY-3002 : Step(65): len = 77955.4, overlap = 13.5
PHY-3002 : Step(66): len = 77212.3, overlap = 13.5
PHY-3002 : Step(67): len = 74763, overlap = 12.0625
PHY-3002 : Step(68): len = 73053.5, overlap = 13.875
PHY-3002 : Step(69): len = 71983.8, overlap = 11.875
PHY-3002 : Step(70): len = 71218, overlap = 9.5625
PHY-3002 : Step(71): len = 68908.9, overlap = 11.625
PHY-3002 : Step(72): len = 67318.3, overlap = 9.5
PHY-3002 : Step(73): len = 66541, overlap = 11.9375
PHY-3002 : Step(74): len = 65095.3, overlap = 14.25
PHY-3002 : Step(75): len = 64582.1, overlap = 14.25
PHY-3002 : Step(76): len = 64422.6, overlap = 14.3125
PHY-3002 : Step(77): len = 63612.3, overlap = 12
PHY-3002 : Step(78): len = 63124.4, overlap = 12
PHY-3002 : Step(79): len = 62721.4, overlap = 14.25
PHY-3002 : Step(80): len = 62451.7, overlap = 14.25
PHY-3002 : Step(81): len = 61753.8, overlap = 16.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000218067
PHY-3002 : Step(82): len = 61986.3, overlap = 14.1875
PHY-3002 : Step(83): len = 61953.8, overlap = 14.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000436134
PHY-3002 : Step(84): len = 62053.3, overlap = 11.9375
PHY-3002 : Step(85): len = 62008.2, overlap = 11.9375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004134s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059547s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00485558
PHY-3002 : Step(86): len = 64398.2, overlap = 9.0625
PHY-3002 : Step(87): len = 63048.7, overlap = 10.75
PHY-3002 : Step(88): len = 62086.2, overlap = 10.125
PHY-3002 : Step(89): len = 60447.4, overlap = 11.2812
PHY-3002 : Step(90): len = 59241.9, overlap = 11.5625
PHY-3002 : Step(91): len = 57691.6, overlap = 13.0938
PHY-3002 : Step(92): len = 56029.2, overlap = 12.375
PHY-3002 : Step(93): len = 54946.6, overlap = 13
PHY-3002 : Step(94): len = 53635.7, overlap = 13.625
PHY-3002 : Step(95): len = 52186.5, overlap = 14.6875
PHY-3002 : Step(96): len = 50566.2, overlap = 17.1875
PHY-3002 : Step(97): len = 50139.4, overlap = 16.5938
PHY-3002 : Step(98): len = 49527.7, overlap = 16.9062
PHY-3002 : Step(99): len = 49198.5, overlap = 17
PHY-3002 : Step(100): len = 48454.5, overlap = 17.1562
PHY-3002 : Step(101): len = 48333.8, overlap = 17.4688
PHY-3002 : Step(102): len = 48065.6, overlap = 18.0938
PHY-3002 : Step(103): len = 47734.3, overlap = 18.5312
PHY-3002 : Step(104): len = 47713.5, overlap = 19.9062
PHY-3002 : Step(105): len = 47534.7, overlap = 21.7812
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059973s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000123007
PHY-3002 : Step(106): len = 48210.9, overlap = 61.9375
PHY-3002 : Step(107): len = 48530.9, overlap = 54.25
PHY-3002 : Step(108): len = 49627.7, overlap = 44.375
PHY-3002 : Step(109): len = 50153, overlap = 45.5
PHY-3002 : Step(110): len = 49955.9, overlap = 44.1875
PHY-3002 : Step(111): len = 49953.4, overlap = 44.125
PHY-3002 : Step(112): len = 49777.9, overlap = 39.5625
PHY-3002 : Step(113): len = 49565.1, overlap = 39.7812
PHY-3002 : Step(114): len = 49377.7, overlap = 39.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000246014
PHY-3002 : Step(115): len = 49389.4, overlap = 39.625
PHY-3002 : Step(116): len = 49605.4, overlap = 38.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000492027
PHY-3002 : Step(117): len = 50361, overlap = 37.7188
PHY-3002 : Step(118): len = 50876.2, overlap = 36.9062
PHY-3002 : Step(119): len = 51913.8, overlap = 29.875
PHY-3002 : Step(120): len = 51671.9, overlap = 30.3125
PHY-3002 : Step(121): len = 51267.1, overlap = 29.4375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7773, tnet num: 2191, tinst num: 1621, tnode num: 11013, tedge num: 13149.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.34 peak overflow 3.56
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2193.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54608, over cnt = 246(0%), over = 914, worst = 15
PHY-1001 : End global iterations;  0.058542s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.8%)

PHY-1001 : Congestion index: top1 = 39.44, top5 = 23.98, top10 = 15.55, top15 = 11.06.
PHY-1001 : End incremental global routing;  0.108178s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (115.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066844s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.205754s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (106.3%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1666/2193.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54608, over cnt = 246(0%), over = 914, worst = 15
PHY-1002 : len = 60208, over cnt = 150(0%), over = 345, worst = 12
PHY-1002 : len = 62720, over cnt = 45(0%), over = 118, worst = 10
PHY-1002 : len = 64544, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 64688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.090730s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (137.8%)

PHY-1001 : Congestion index: top1 = 34.66, top5 = 24.11, top10 = 17.10, top15 = 12.50.
OPT-1001 : End congestion update;  0.132397s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (129.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062810s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.198015s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (118.4%)

OPT-1001 : Current memory(MB): used = 217, reserve = 182, peak = 217.
OPT-1001 : End physical optimization;  0.679317s wall, 0.718750s user + 0.015625s system = 0.734375s CPU (108.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 368 LUT to BLE ...
SYN-4008 : Packed 368 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 108 single LUT's are left
SYN-4006 : 709 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1077/1405 primitive instances ...
PHY-3001 : End packing;  0.048354s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (96.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2025 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1472 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 841 instances, 794 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51132.4, Over = 61.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6563, tnet num: 2023, tinst num: 841, tnode num: 8927, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.307618s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (96.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.56743e-05
PHY-3002 : Step(122): len = 50577.5, overlap = 62.25
PHY-3002 : Step(123): len = 50019.5, overlap = 63.75
PHY-3002 : Step(124): len = 50015.9, overlap = 61.75
PHY-3002 : Step(125): len = 50189.3, overlap = 61
PHY-3002 : Step(126): len = 50091, overlap = 59.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.13486e-05
PHY-3002 : Step(127): len = 50224, overlap = 58.25
PHY-3002 : Step(128): len = 50585.5, overlap = 55
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000102697
PHY-3002 : Step(129): len = 51492.5, overlap = 53
PHY-3002 : Step(130): len = 52792.1, overlap = 52.75
PHY-3002 : Step(131): len = 53271.4, overlap = 50.75
PHY-3002 : Step(132): len = 53282.4, overlap = 49.75
PHY-3002 : Step(133): len = 53282.4, overlap = 49.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.085914s wall, 0.046875s user + 0.062500s system = 0.109375s CPU (127.3%)

PHY-3001 : Trial Legalized: Len = 68596.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049988s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000889906
PHY-3002 : Step(134): len = 65861.1, overlap = 7
PHY-3002 : Step(135): len = 63787.1, overlap = 10.5
PHY-3002 : Step(136): len = 62008.5, overlap = 14.25
PHY-3002 : Step(137): len = 60673.3, overlap = 16.75
PHY-3002 : Step(138): len = 59564.2, overlap = 20.5
PHY-3002 : Step(139): len = 59002.3, overlap = 21.25
PHY-3002 : Step(140): len = 58762.4, overlap = 22.75
PHY-3002 : Step(141): len = 58631.9, overlap = 22
PHY-3002 : Step(142): len = 58489.3, overlap = 22
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00177981
PHY-3002 : Step(143): len = 58801.1, overlap = 21.25
PHY-3002 : Step(144): len = 58892.5, overlap = 20.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00355963
PHY-3002 : Step(145): len = 59080.6, overlap = 19.5
PHY-3002 : Step(146): len = 59157.3, overlap = 19.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005230s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63979.2, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005527s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 11 instances has been re-located, deltaX = 2, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 64093.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6563, tnet num: 2023, tinst num: 841, tnode num: 8927, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 91/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70840, over cnt = 139(0%), over = 184, worst = 4
PHY-1002 : len = 71560, over cnt = 40(0%), over = 43, worst = 2
PHY-1002 : len = 72064, over cnt = 7(0%), over = 8, worst = 2
PHY-1002 : len = 72192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.103522s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (135.8%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 22.74, top10 = 17.47, top15 = 13.70.
PHY-1001 : End incremental global routing;  0.152305s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (123.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069519s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.251170s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (118.2%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1776/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005783s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 22.74, top10 = 17.47, top15 = 13.70.
OPT-1001 : End congestion update;  0.051052s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058060s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 803 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 841 instances, 794 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64120.4, Over = 0
PHY-3001 : End spreading;  0.005305s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64120.4, Over = 0
PHY-3001 : End incremental legalization;  0.038692s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (80.8%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.161753s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (96.6%)

OPT-1001 : Current memory(MB): used = 224, reserve = 189, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058464s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1768/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72200, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72200, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72232, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.024001s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.1%)

PHY-1001 : Congestion index: top1 = 31.83, top5 = 22.73, top10 = 17.49, top15 = 13.71.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050875s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.379310
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.885085s wall, 0.906250s user + 0.046875s system = 0.953125s CPU (107.7%)

RUN-1003 : finish command "place" in  5.050505s wall, 7.500000s user + 2.484375s system = 9.984375s CPU (197.7%)

RUN-1004 : used memory is 206 MB, reserved memory is 171 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2025 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1472 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6563, tnet num: 2023, tinst num: 841, tnode num: 8927, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69848, over cnt = 142(0%), over = 194, worst = 4
PHY-1002 : len = 70672, over cnt = 56(0%), over = 62, worst = 2
PHY-1002 : len = 71368, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 71424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.116289s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (120.9%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.54, top10 = 17.29, top15 = 13.58.
PHY-1001 : End global routing;  0.166489s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (122.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 205, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 466, peak = 497.
PHY-1001 : End build detailed router design. 3.222885s wall, 3.125000s user + 0.062500s system = 3.187500s CPU (98.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33176, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.283783s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End phase 1; 1.289680s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 183040, over cnt = 40(0%), over = 40, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End initial routed; 1.014972s wall, 1.640625s user + 0.078125s system = 1.718750s CPU (169.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1792(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.147   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.361696s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.7%)

PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End phase 2; 1.376753s wall, 2.015625s user + 0.078125s system = 2.093750s CPU (152.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 183040, over cnt = 40(0%), over = 40, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.019556s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (79.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182856, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032626s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (143.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182984, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.026796s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (116.6%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 183032, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.021669s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (144.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1792(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.147   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.373114s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (92.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.177666s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.7%)

PHY-1001 : Current memory(MB): used = 547, reserve = 516, peak = 547.
PHY-1001 : End phase 3; 0.772425s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (97.1%)

PHY-1003 : Routed, final wirelength = 183032
PHY-1001 : Current memory(MB): used = 548, reserve = 517, peak = 548.
PHY-1001 : End export database. 0.009818s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (159.1%)

PHY-1001 : End detail routing;  6.849764s wall, 7.328125s user + 0.187500s system = 7.515625s CPU (109.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6563, tnet num: 2023, tinst num: 841, tnode num: 8927, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.731652s wall, 8.250000s user + 0.187500s system = 8.437500s CPU (109.1%)

RUN-1004 : used memory is 502 MB, reserved memory is 470 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      814   out of  19600    4.15%
#reg                     1074   out of  19600    5.48%
#le                      1523
  #lut only               449   out of   1523   29.48%
  #reg only               709   out of   1523   46.55%
  #lut&reg                365   out of   1523   23.97%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         110
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    46
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1523   |593     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1121   |292     |128     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |18      |0       |0       |
|    demodu                  |Demodulation                                     |529    |121     |53      |436     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |0       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |31     |17      |0       |31      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |89     |27      |21      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |313    |86      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |107    |92      |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |19     |15      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |52     |49      |0       |26      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1436  
    #2          2       316   
    #3          3       108   
    #4          4        12   
    #5        5-10       80   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6563, tnet num: 2023, tinst num: 841, tnode num: 8927, tedge num: 11538.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 841
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2025, pip num: 14738
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1364 valid insts, and 39119 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.151317s wall, 18.468750s user + 0.078125s system = 18.546875s CPU (588.5%)

RUN-1004 : used memory is 519 MB, reserved memory is 489 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230724_175148.log"
