============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 18:17:25 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1648 instances
RUN-0007 : 381 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2219 nets
RUN-1001 : 1658 nets have 2 pins
RUN-1001 : 448 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1646 instances, 381 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7848, tnet num: 2217, tinst num: 1646, tnode num: 11089, tedge num: 13247.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.283426s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (99.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 571325
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1646.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 520802, overlap = 18
PHY-3002 : Step(2): len = 428205, overlap = 20.25
PHY-3002 : Step(3): len = 365937, overlap = 18
PHY-3002 : Step(4): len = 345740, overlap = 15.75
PHY-3002 : Step(5): len = 337207, overlap = 15.75
PHY-3002 : Step(6): len = 328940, overlap = 18
PHY-3002 : Step(7): len = 322483, overlap = 18
PHY-3002 : Step(8): len = 314163, overlap = 18
PHY-3002 : Step(9): len = 307831, overlap = 18
PHY-3002 : Step(10): len = 300277, overlap = 18
PHY-3002 : Step(11): len = 294538, overlap = 20.25
PHY-3002 : Step(12): len = 285850, overlap = 20.25
PHY-3002 : Step(13): len = 281212, overlap = 20.25
PHY-3002 : Step(14): len = 274276, overlap = 20.25
PHY-3002 : Step(15): len = 269822, overlap = 20.25
PHY-3002 : Step(16): len = 263372, overlap = 20.25
PHY-3002 : Step(17): len = 258647, overlap = 20.25
PHY-3002 : Step(18): len = 252152, overlap = 20.25
PHY-3002 : Step(19): len = 248179, overlap = 20.25
PHY-3002 : Step(20): len = 242104, overlap = 20.25
PHY-3002 : Step(21): len = 235329, overlap = 20.25
PHY-3002 : Step(22): len = 229925, overlap = 20.25
PHY-3002 : Step(23): len = 226733, overlap = 20.25
PHY-3002 : Step(24): len = 218722, overlap = 20.25
PHY-3002 : Step(25): len = 212860, overlap = 20.25
PHY-3002 : Step(26): len = 209511, overlap = 20.25
PHY-3002 : Step(27): len = 205210, overlap = 20.25
PHY-3002 : Step(28): len = 186130, overlap = 20.25
PHY-3002 : Step(29): len = 182171, overlap = 20.25
PHY-3002 : Step(30): len = 179804, overlap = 20.25
PHY-3002 : Step(31): len = 155603, overlap = 20.25
PHY-3002 : Step(32): len = 141170, overlap = 20.25
PHY-3002 : Step(33): len = 140429, overlap = 20.25
PHY-3002 : Step(34): len = 133648, overlap = 20.25
PHY-3002 : Step(35): len = 126063, overlap = 20.25
PHY-3002 : Step(36): len = 124824, overlap = 20.25
PHY-3002 : Step(37): len = 122198, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00010928
PHY-3002 : Step(38): len = 123367, overlap = 6.75
PHY-3002 : Step(39): len = 122910, overlap = 9
PHY-3002 : Step(40): len = 121479, overlap = 9
PHY-3002 : Step(41): len = 120366, overlap = 11.25
PHY-3002 : Step(42): len = 116726, overlap = 13.5
PHY-3002 : Step(43): len = 110873, overlap = 9
PHY-3002 : Step(44): len = 107055, overlap = 11.25
PHY-3002 : Step(45): len = 106505, overlap = 13.5
PHY-3002 : Step(46): len = 103208, overlap = 13.5
PHY-3002 : Step(47): len = 102392, overlap = 11.25
PHY-3002 : Step(48): len = 100797, overlap = 6.75
PHY-3002 : Step(49): len = 100342, overlap = 9
PHY-3002 : Step(50): len = 98772.7, overlap = 11.25
PHY-3002 : Step(51): len = 97696.5, overlap = 6.75
PHY-3002 : Step(52): len = 94547.8, overlap = 6.75
PHY-3002 : Step(53): len = 93484.2, overlap = 6.75
PHY-3002 : Step(54): len = 91839.2, overlap = 4.5
PHY-3002 : Step(55): len = 91210, overlap = 9
PHY-3002 : Step(56): len = 86624.2, overlap = 15.75
PHY-3002 : Step(57): len = 83408.4, overlap = 15.75
PHY-3002 : Step(58): len = 82394.1, overlap = 11.25
PHY-3002 : Step(59): len = 81767.8, overlap = 6.75
PHY-3002 : Step(60): len = 80607.1, overlap = 6.75
PHY-3002 : Step(61): len = 79284.9, overlap = 6.75
PHY-3002 : Step(62): len = 78787.1, overlap = 6.75
PHY-3002 : Step(63): len = 77748.2, overlap = 6.75
PHY-3002 : Step(64): len = 76591.4, overlap = 6.75
PHY-3002 : Step(65): len = 74439, overlap = 11.25
PHY-3002 : Step(66): len = 72815, overlap = 13.5
PHY-3002 : Step(67): len = 71856.3, overlap = 11.25
PHY-3002 : Step(68): len = 70979.2, overlap = 6.75
PHY-3002 : Step(69): len = 69278, overlap = 6.75
PHY-3002 : Step(70): len = 68657, overlap = 6.75
PHY-3002 : Step(71): len = 68293.5, overlap = 6.75
PHY-3002 : Step(72): len = 67711, overlap = 9
PHY-3002 : Step(73): len = 67198.8, overlap = 9
PHY-3002 : Step(74): len = 67076.7, overlap = 11.25
PHY-3002 : Step(75): len = 66877.2, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000218559
PHY-3002 : Step(76): len = 66691.2, overlap = 4.5
PHY-3002 : Step(77): len = 66582.7, overlap = 4.5
PHY-3002 : Step(78): len = 66481, overlap = 4.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000437118
PHY-3002 : Step(79): len = 66691.1, overlap = 4.5
PHY-3002 : Step(80): len = 66740.2, overlap = 4.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007314s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060138s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(81): len = 70136.5, overlap = 3.5
PHY-3002 : Step(82): len = 68744.7, overlap = 3.5
PHY-3002 : Step(83): len = 67464.2, overlap = 3.4375
PHY-3002 : Step(84): len = 65833.5, overlap = 3.375
PHY-3002 : Step(85): len = 64135.2, overlap = 2.78125
PHY-3002 : Step(86): len = 62554.4, overlap = 2.3125
PHY-3002 : Step(87): len = 60922.1, overlap = 2.4375
PHY-3002 : Step(88): len = 59660.8, overlap = 2.8125
PHY-3002 : Step(89): len = 57596.6, overlap = 4.0625
PHY-3002 : Step(90): len = 56331.5, overlap = 4.375
PHY-3002 : Step(91): len = 55621.7, overlap = 6.75
PHY-3002 : Step(92): len = 54963.6, overlap = 7.75
PHY-3002 : Step(93): len = 54173.3, overlap = 7.75
PHY-3002 : Step(94): len = 52901.5, overlap = 7.75
PHY-3002 : Step(95): len = 51845.1, overlap = 5.6875
PHY-3002 : Step(96): len = 50649.1, overlap = 6
PHY-3002 : Step(97): len = 50004.4, overlap = 6.1875
PHY-3002 : Step(98): len = 49316.9, overlap = 6.0625
PHY-3002 : Step(99): len = 47917, overlap = 4.4375
PHY-3002 : Step(100): len = 47372.1, overlap = 5.9375
PHY-3002 : Step(101): len = 47301.6, overlap = 6.1875
PHY-3002 : Step(102): len = 47055.2, overlap = 7.25
PHY-3002 : Step(103): len = 46820.7, overlap = 8.0625
PHY-3002 : Step(104): len = 46528.1, overlap = 8
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00104912
PHY-3002 : Step(105): len = 46265.4, overlap = 8.0625
PHY-3002 : Step(106): len = 45975.8, overlap = 8.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00209825
PHY-3002 : Step(107): len = 46020.4, overlap = 7.9375
PHY-3002 : Step(108): len = 46031, overlap = 12.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061173s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.11537e-05
PHY-3002 : Step(109): len = 46510.7, overlap = 59.875
PHY-3002 : Step(110): len = 47132.3, overlap = 56.0312
PHY-3002 : Step(111): len = 46981.8, overlap = 57.75
PHY-3002 : Step(112): len = 47292.9, overlap = 57.0312
PHY-3002 : Step(113): len = 47424.9, overlap = 55.8438
PHY-3002 : Step(114): len = 46957, overlap = 55.625
PHY-3002 : Step(115): len = 46883.6, overlap = 55.25
PHY-3002 : Step(116): len = 46769, overlap = 53.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000162307
PHY-3002 : Step(117): len = 46938.1, overlap = 52.9375
PHY-3002 : Step(118): len = 47452, overlap = 49.2812
PHY-3002 : Step(119): len = 47452, overlap = 49.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000324615
PHY-3002 : Step(120): len = 47906.3, overlap = 47.2812
PHY-3002 : Step(121): len = 48685.3, overlap = 43.3125
PHY-3002 : Step(122): len = 50066.7, overlap = 41.875
PHY-3002 : Step(123): len = 50680.4, overlap = 35.375
PHY-3002 : Step(124): len = 51021.6, overlap = 32.2812
PHY-3002 : Step(125): len = 51176.9, overlap = 28.7812
PHY-3002 : Step(126): len = 50664.1, overlap = 28.9375
PHY-3002 : Step(127): len = 50483.6, overlap = 27.8438
PHY-3002 : Step(128): len = 50123.2, overlap = 27.875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7848, tnet num: 2217, tinst num: 1646, tnode num: 11089, tedge num: 13247.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 76.62 peak overflow 3.78
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2219.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53392, over cnt = 234(0%), over = 962, worst = 14
PHY-1001 : End global iterations;  0.052823s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.3%)

PHY-1001 : Congestion index: top1 = 40.93, top5 = 24.21, top10 = 15.65, top15 = 11.15.
PHY-1001 : End incremental global routing;  0.102614s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (121.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069179s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.3%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1607 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1648 instances, 381 luts, 994 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 50306.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7856, tnet num: 2219, tinst num: 1648, tnode num: 11103, tedge num: 13259.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.307049s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (96.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(129): len = 50315.1, overlap = 2
PHY-3002 : Step(130): len = 50338.8, overlap = 2
PHY-3002 : Step(131): len = 50340.7, overlap = 2.0625
PHY-3002 : Step(132): len = 50325.4, overlap = 2.0625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058987s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000984917
PHY-3002 : Step(133): len = 50316.2, overlap = 27.9375
PHY-3002 : Step(134): len = 50310.7, overlap = 27.8125
PHY-3001 : Final: Len = 50310.7, Over = 27.8125
PHY-3001 : End incremental placement;  0.443141s wall, 0.406250s user + 0.093750s system = 0.500000s CPU (112.8%)

OPT-1001 : Total overflow 76.56 peak overflow 3.78
OPT-1001 : End high-fanout net optimization;  0.650714s wall, 0.640625s user + 0.093750s system = 0.734375s CPU (112.9%)

OPT-1001 : Current memory(MB): used = 222, reserve = 186, peak = 222.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1655/2221.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53512, over cnt = 234(0%), over = 961, worst = 14
PHY-1002 : len = 59040, over cnt = 160(0%), over = 428, worst = 14
PHY-1002 : len = 62528, over cnt = 57(0%), over = 102, worst = 5
PHY-1002 : len = 63552, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 63664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.076755s wall, 0.109375s user + 0.046875s system = 0.156250s CPU (203.6%)

PHY-1001 : Congestion index: top1 = 35.65, top5 = 24.13, top10 = 17.17, top15 = 12.77.
OPT-1001 : End congestion update;  0.118410s wall, 0.140625s user + 0.046875s system = 0.187500s CPU (158.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2219 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057700s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.178688s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (139.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 222.
OPT-1001 : End physical optimization;  1.117404s wall, 1.125000s user + 0.140625s system = 1.265625s CPU (113.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 381 LUT to BLE ...
SYN-4008 : Packed 381 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 805 remaining SEQ's ...
SYN-4005 : Packed 104 SEQ with LUT/SLICE
SYN-4006 : 104 single LUT's are left
SYN-4006 : 701 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1082/1415 primitive instances ...
PHY-3001 : End packing;  0.050447s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 847 instances
RUN-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2047 nets
RUN-1001 : 1490 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 845 instances, 798 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50520, Over = 53
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2045, tinst num: 845, tnode num: 8972, tedge num: 11618.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2045 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.333249s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.00363e-05
PHY-3002 : Step(135): len = 50186.4, overlap = 56.25
PHY-3002 : Step(136): len = 50260.8, overlap = 57.5
PHY-3002 : Step(137): len = 50247, overlap = 61.25
PHY-3002 : Step(138): len = 49997.5, overlap = 62.75
PHY-3002 : Step(139): len = 49884.2, overlap = 61.75
PHY-3002 : Step(140): len = 49869.7, overlap = 59.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.00727e-05
PHY-3002 : Step(141): len = 50220.9, overlap = 58.75
PHY-3002 : Step(142): len = 50989.7, overlap = 57.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000120145
PHY-3002 : Step(143): len = 52212.4, overlap = 50.5
PHY-3002 : Step(144): len = 53352.4, overlap = 48.25
PHY-3002 : Step(145): len = 53905.5, overlap = 45.5
PHY-3002 : Step(146): len = 54298.2, overlap = 42.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.099887s wall, 0.062500s user + 0.171875s system = 0.234375s CPU (234.6%)

PHY-3001 : Trial Legalized: Len = 66586.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2045 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052288s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000859906
PHY-3002 : Step(147): len = 63587.3, overlap = 3
PHY-3002 : Step(148): len = 61685.2, overlap = 9.25
PHY-3002 : Step(149): len = 59983.4, overlap = 15.75
PHY-3002 : Step(150): len = 58965.9, overlap = 19
PHY-3002 : Step(151): len = 58316.3, overlap = 21.75
PHY-3002 : Step(152): len = 58030.8, overlap = 24
PHY-3002 : Step(153): len = 57725.7, overlap = 24.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00171981
PHY-3002 : Step(154): len = 57960.8, overlap = 23.5
PHY-3002 : Step(155): len = 58026.4, overlap = 23.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00343962
PHY-3002 : Step(156): len = 58222, overlap = 23
PHY-3002 : Step(157): len = 58287.6, overlap = 23
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005039s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62526.4, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005554s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 12 instances has been re-located, deltaX = 3, deltaY = 9, maxDist = 1.
PHY-3001 : Final: Len = 62700.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2045, tinst num: 845, tnode num: 8972, tedge num: 11618.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 101/2047.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69080, over cnt = 150(0%), over = 221, worst = 7
PHY-1002 : len = 69904, over cnt = 96(0%), over = 118, worst = 3
PHY-1002 : len = 71256, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 71336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.128148s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.5%)

PHY-1001 : Congestion index: top1 = 30.37, top5 = 22.28, top10 = 17.40, top15 = 13.83.
PHY-1001 : End incremental global routing;  0.178407s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2045 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067531s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.280420s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.3%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1794/2047.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005788s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.37, top5 = 22.28, top10 = 17.40, top15 = 13.83.
OPT-1001 : End congestion update;  0.054317s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2045 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051892s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.3%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 807 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 845 instances, 798 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62760, Over = 0
PHY-3001 : End spreading;  0.004957s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (315.2%)

PHY-3001 : Final: Len = 62760, Over = 0
PHY-3001 : End incremental legalization;  0.034804s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.8%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.153519s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (91.6%)

OPT-1001 : Current memory(MB): used = 227, reserve = 192, peak = 227.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2045 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048952s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (127.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2047.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008522s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (366.7%)

PHY-1001 : Congestion index: top1 = 30.41, top5 = 22.31, top10 = 17.40, top15 = 13.83.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2045 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047685s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.880010s wall, 0.968750s user + 0.015625s system = 0.984375s CPU (111.9%)

RUN-1003 : finish command "place" in  5.616503s wall, 9.234375s user + 2.359375s system = 11.593750s CPU (206.4%)

RUN-1004 : used memory is 211 MB, reserved memory is 174 MB, peak memory is 227 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 847 instances
RUN-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2047 nets
RUN-1001 : 1490 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2045, tinst num: 845, tnode num: 8972, tedge num: 11618.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 399 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2045 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68304, over cnt = 150(0%), over = 222, worst = 7
PHY-1002 : len = 69208, over cnt = 92(0%), over = 113, worst = 3
PHY-1002 : len = 70576, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 70672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123368s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (164.6%)

PHY-1001 : Congestion index: top1 = 30.45, top5 = 22.19, top10 = 17.30, top15 = 13.71.
PHY-1001 : End global routing;  0.173264s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (144.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 239, reserve = 204, peak = 252.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 501, reserve = 470, peak = 501.
PHY-1001 : End build detailed router design. 3.260333s wall, 3.171875s user + 0.093750s system = 3.265625s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34728, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.219640s wall, 1.203125s user + 0.015625s system = 1.218750s CPU (99.9%)

PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 533.
PHY-1001 : End phase 1; 1.225437s wall, 1.218750s user + 0.015625s system = 1.234375s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180328, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End initial routed; 1.059684s wall, 1.687500s user + 0.046875s system = 1.734375s CPU (163.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1811(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.359   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.369542s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 538, reserve = 506, peak = 538.
PHY-1001 : End phase 2; 1.429306s wall, 2.062500s user + 0.046875s system = 2.109375s CPU (147.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180328, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014203s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (110.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180168, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029467s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (159.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180208, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.024402s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (128.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 180224, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.018340s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1811(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.359   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.362452s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (103.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.177748s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.7%)

PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End phase 3; 0.748077s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (102.3%)

PHY-1003 : Routed, final wirelength = 180224
PHY-1001 : Current memory(MB): used = 552, reserve = 520, peak = 552.
PHY-1001 : End export database. 0.011250s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (138.9%)

PHY-1001 : End detail routing;  6.851060s wall, 7.390625s user + 0.156250s system = 7.546875s CPU (110.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2045, tinst num: 845, tnode num: 8972, tedge num: 11618.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.745098s wall, 8.328125s user + 0.203125s system = 8.531250s CPU (110.2%)

RUN-1004 : used memory is 517 MB, reserved memory is 486 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      838   out of  19600    4.28%
#reg                     1077   out of  19600    5.49%
#le                      1539
  #lut only               462   out of   1539   30.02%
  #reg only               701   out of   1539   45.55%
  #lut&reg                376   out of   1539   24.43%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1539   |612     |226     |1108    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1136   |303     |136     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |30     |19      |9       |23      |0       |0       |
|    demodu                  |Demodulation                                     |543    |132     |58      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |55     |4       |0       |55      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |140    |16      |14      |114     |0       |0       |
|    modu                    |Modulation                                       |89     |35      |21      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |312    |84      |29      |250     |0       |4       |
|    trans                   |SquareWaveGenerator                              |22     |17      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |115    |104     |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |57     |57      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1454  
    #2          2       313   
    #3          3       110   
    #4          4        19   
    #5        5-10       78   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6619, tnet num: 2045, tinst num: 845, tnode num: 8972, tedge num: 11618.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2045 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 845
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2047, pip num: 14707
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1299 valid insts, and 39095 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.228323s wall, 18.234375s user + 0.078125s system = 18.312500s CPU (567.2%)

RUN-1004 : used memory is 521 MB, reserved memory is 490 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_181725.log"
