============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 11:57:55 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 25 trigger nets, 25 data nets.
KIT-1004 : Chipwatcher code = 1001011011010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2086/10 useful/useless nets, 1287/5 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 1859/18 useful/useless nets, 1617/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 282 better
SYN-1014 : Optimize round 2
SYN-1032 : 1664/15 useful/useless nets, 1422/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1688/156 useful/useless nets, 1468/30 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 207 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 2056/5 useful/useless nets, 1836/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7601, tnet num: 2056, tinst num: 1835, tnode num: 9525, tedge num: 11656.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2056 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 175 (3.57), #lev = 7 (1.77)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 174 (3.59), #lev = 6 (1.89)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 395 instances into 174 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 280 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (180 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1341 instances
RUN-0007 : 545 luts, 586 seqs, 108 mslices, 66 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1568 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 917 nets have 2 pins
RUN-1001 : 471 nets have [3 - 5] pins
RUN-1001 : 97 nets have [6 - 10] pins
RUN-1001 : 48 nets have [11 - 20] pins
RUN-1001 : 24 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     160     
RUN-1001 :   No   |  No   |  Yes  |     101     
RUN-1001 :   No   |  Yes  |  No   |     92      
RUN-1001 :   Yes  |  No   |  No   |     57      
RUN-1001 :   Yes  |  No   |  Yes  |     176     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 18
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1339 instances, 545 luts, 586 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6550, tnet num: 1566, tinst num: 1339, tnode num: 8477, tedge num: 10772.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1566 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.119657s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (91.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 345163
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1339.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 275641, overlap = 51.75
PHY-3002 : Step(2): len = 232767, overlap = 51.75
PHY-3002 : Step(3): len = 210396, overlap = 51.75
PHY-3002 : Step(4): len = 189314, overlap = 51.75
PHY-3002 : Step(5): len = 164351, overlap = 51.75
PHY-3002 : Step(6): len = 141934, overlap = 51.75
PHY-3002 : Step(7): len = 127237, overlap = 51.75
PHY-3002 : Step(8): len = 113520, overlap = 51.75
PHY-3002 : Step(9): len = 101245, overlap = 51.75
PHY-3002 : Step(10): len = 90524.3, overlap = 51.75
PHY-3002 : Step(11): len = 82948.7, overlap = 51.75
PHY-3002 : Step(12): len = 76930.3, overlap = 51.75
PHY-3002 : Step(13): len = 72677.9, overlap = 51.75
PHY-3002 : Step(14): len = 62574, overlap = 51.8125
PHY-3002 : Step(15): len = 58736, overlap = 52.3125
PHY-3002 : Step(16): len = 56037.2, overlap = 52.875
PHY-3002 : Step(17): len = 52322.3, overlap = 52.8125
PHY-3002 : Step(18): len = 49215, overlap = 48.3125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.28132e-06
PHY-3002 : Step(19): len = 49475, overlap = 43.625
PHY-3002 : Step(20): len = 49835.3, overlap = 48
PHY-3002 : Step(21): len = 48483.3, overlap = 45.8125
PHY-3002 : Step(22): len = 48760.8, overlap = 43.6875
PHY-3002 : Step(23): len = 47220.4, overlap = 41.625
PHY-3002 : Step(24): len = 45929.1, overlap = 41.875
PHY-3002 : Step(25): len = 44816.1, overlap = 44.1875
PHY-3002 : Step(26): len = 43515.1, overlap = 41.75
PHY-3002 : Step(27): len = 41674.6, overlap = 42.0625
PHY-3002 : Step(28): len = 40736, overlap = 44.6875
PHY-3002 : Step(29): len = 39653.5, overlap = 39.6875
PHY-3002 : Step(30): len = 39335.6, overlap = 37.6875
PHY-3002 : Step(31): len = 38747.3, overlap = 33.2812
PHY-3002 : Step(32): len = 38566.7, overlap = 33.2812
PHY-3002 : Step(33): len = 38223.9, overlap = 37.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.45626e-05
PHY-3002 : Step(34): len = 37980.7, overlap = 33.2812
PHY-3002 : Step(35): len = 37996.8, overlap = 35.625
PHY-3002 : Step(36): len = 38176.6, overlap = 38.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 2.91253e-05
PHY-3002 : Step(37): len = 38165.1, overlap = 38.0625
PHY-3002 : Step(38): len = 38144.3, overlap = 37.9688
PHY-3002 : Step(39): len = 38098.9, overlap = 38.1562
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004670s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (334.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1566 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039848s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (78.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(40): len = 40696.4, overlap = 10
PHY-3002 : Step(41): len = 40824.1, overlap = 10.4375
PHY-3002 : Step(42): len = 40656.1, overlap = 10.7188
PHY-3002 : Step(43): len = 40753.8, overlap = 11.125
PHY-3002 : Step(44): len = 40379.9, overlap = 12.75
PHY-3002 : Step(45): len = 40043.9, overlap = 13.25
PHY-3002 : Step(46): len = 39727.3, overlap = 12.875
PHY-3002 : Step(47): len = 39446.2, overlap = 12.9688
PHY-3002 : Step(48): len = 38754.3, overlap = 10.4062
PHY-3002 : Step(49): len = 38287.1, overlap = 6.5
PHY-3002 : Step(50): len = 37730.4, overlap = 6.65625
PHY-3002 : Step(51): len = 37295.1, overlap = 8.1875
PHY-3002 : Step(52): len = 36906.9, overlap = 7.96875
PHY-3002 : Step(53): len = 36399.9, overlap = 9.84375
PHY-3002 : Step(54): len = 35764.7, overlap = 12.125
PHY-3002 : Step(55): len = 35179.4, overlap = 13.9062
PHY-3002 : Step(56): len = 34615.4, overlap = 14
PHY-3002 : Step(57): len = 34348.4, overlap = 14.0312
PHY-3002 : Step(58): len = 34149.1, overlap = 14.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00105734
PHY-3002 : Step(59): len = 34072.3, overlap = 14.3438
PHY-3002 : Step(60): len = 34173.6, overlap = 13.7812
PHY-3002 : Step(61): len = 34462.7, overlap = 14.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00211468
PHY-3002 : Step(62): len = 34367.9, overlap = 15.1562
PHY-3002 : Step(63): len = 34361.6, overlap = 15.2188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1566 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037490s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (125.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.51148e-05
PHY-3002 : Step(64): len = 34967.7, overlap = 57.9375
PHY-3002 : Step(65): len = 35315.7, overlap = 57.8125
PHY-3002 : Step(66): len = 35262.1, overlap = 53.7188
PHY-3002 : Step(67): len = 35394.4, overlap = 54.3438
PHY-3002 : Step(68): len = 35968.5, overlap = 47
PHY-3002 : Step(69): len = 36305.6, overlap = 46.5625
PHY-3002 : Step(70): len = 36333.4, overlap = 45.4688
PHY-3002 : Step(71): len = 36091.7, overlap = 45.4688
PHY-3002 : Step(72): len = 35754.3, overlap = 44.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00011023
PHY-3002 : Step(73): len = 35541.7, overlap = 44.9688
PHY-3002 : Step(74): len = 35526.6, overlap = 45.2188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000220459
PHY-3002 : Step(75): len = 35567, overlap = 44.5312
PHY-3002 : Step(76): len = 35567, overlap = 44.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000440919
PHY-3002 : Step(77): len = 36055.5, overlap = 42.625
PHY-3002 : Step(78): len = 36233.1, overlap = 42.25
PHY-3002 : Step(79): len = 36498.8, overlap = 38.125
PHY-3002 : Step(80): len = 36820.1, overlap = 36.125
PHY-3002 : Step(81): len = 37214.9, overlap = 34.2188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000881837
PHY-3002 : Step(82): len = 37026.2, overlap = 34.5312
PHY-3002 : Step(83): len = 37022.6, overlap = 34.6875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00176367
PHY-3002 : Step(84): len = 37219.3, overlap = 29.4375
PHY-3002 : Step(85): len = 37346, overlap = 29.125
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6550, tnet num: 1566, tinst num: 1339, tnode num: 8477, tedge num: 10772.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 73.69 peak overflow 3.28
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1568.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 45192, over cnt = 190(0%), over = 665, worst = 15
PHY-1001 : End global iterations;  0.060468s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (155.0%)

PHY-1001 : Congestion index: top1 = 37.76, top5 = 20.86, top10 = 13.31, top15 = 9.48.
PHY-1001 : End incremental global routing;  0.106279s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (132.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1566 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045081s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1327 has valid locations, 23 needs to be replaced
PHY-3001 : design contains 1361 instances, 545 luts, 608 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 37695.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6638, tnet num: 1588, tinst num: 1361, tnode num: 8631, tedge num: 10904.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1588 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.131286s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (95.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(86): len = 37647.4, overlap = 0.96875
PHY-3002 : Step(87): len = 37647.4, overlap = 0.96875
PHY-3002 : Step(88): len = 37712.3, overlap = 0.96875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1588 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037167s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000869993
PHY-3002 : Step(89): len = 37697, overlap = 29.5
PHY-3002 : Step(90): len = 37697, overlap = 29.5
PHY-3001 : Final: Len = 37697, Over = 29.5
PHY-3001 : End incremental placement;  0.258347s wall, 0.312500s user + 0.046875s system = 0.359375s CPU (139.1%)

OPT-1001 : Total overflow 74.56 peak overflow 3.28
OPT-1001 : End high-fanout net optimization;  0.440019s wall, 0.515625s user + 0.046875s system = 0.562500s CPU (127.8%)

OPT-1001 : Current memory(MB): used = 198, reserve = 151, peak = 198.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1108/1590.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 45848, over cnt = 191(0%), over = 662, worst = 15
PHY-1002 : len = 50616, over cnt = 123(0%), over = 262, worst = 11
PHY-1002 : len = 53664, over cnt = 43(0%), over = 56, worst = 5
PHY-1002 : len = 54144, over cnt = 26(0%), over = 28, worst = 3
PHY-1002 : len = 54472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.092551s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (118.2%)

PHY-1001 : Congestion index: top1 = 36.36, top5 = 21.69, top10 = 14.94, top15 = 10.96.
OPT-1001 : End congestion update;  0.138235s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (124.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1588 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.035622s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.7%)

OPT-0007 : Start: WNS 3101 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.174061s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (116.7%)

OPT-1001 : Current memory(MB): used = 196, reserve = 150, peak = 198.
OPT-1001 : End physical optimization;  0.727241s wall, 0.843750s user + 0.046875s system = 0.890625s CPU (122.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 545 LUT to BLE ...
SYN-4008 : Packed 545 LUT and 196 SEQ to BLE.
SYN-4003 : Packing 412 remaining SEQ's ...
SYN-4005 : Packed 207 SEQ with LUT/SLICE
SYN-4006 : 166 single LUT's are left
SYN-4006 : 205 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 750/1094 primitive instances ...
PHY-3001 : End packing;  0.040447s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.3%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 633 instances
RUN-1001 : 298 mslices, 299 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1397 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 715 nets have 2 pins
RUN-1001 : 496 nets have [3 - 5] pins
RUN-1001 : 110 nets have [6 - 10] pins
RUN-1001 : 37 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 631 instances, 597 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 38179.2, Over = 40.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5825, tnet num: 1395, tinst num: 631, tnode num: 7314, tedge num: 9911.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1395 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.142886s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (98.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.66709e-05
PHY-3002 : Step(91): len = 37475.7, overlap = 41.5
PHY-3002 : Step(92): len = 37621.9, overlap = 40.75
PHY-3002 : Step(93): len = 37426.5, overlap = 39
PHY-3002 : Step(94): len = 37637.5, overlap = 36
PHY-3002 : Step(95): len = 37933.9, overlap = 39.5
PHY-3002 : Step(96): len = 38226, overlap = 42
PHY-3002 : Step(97): len = 38124.4, overlap = 43
PHY-3002 : Step(98): len = 38230, overlap = 40.75
PHY-3002 : Step(99): len = 38364.4, overlap = 42.75
PHY-3002 : Step(100): len = 37898.6, overlap = 41.25
PHY-3002 : Step(101): len = 37956.4, overlap = 43.75
PHY-3002 : Step(102): len = 37701.5, overlap = 44.75
PHY-3002 : Step(103): len = 37324.2, overlap = 42.25
PHY-3002 : Step(104): len = 37073.5, overlap = 43.5
PHY-3002 : Step(105): len = 36808.8, overlap = 44
PHY-3002 : Step(106): len = 36754.3, overlap = 42.5
PHY-3002 : Step(107): len = 36577, overlap = 42.75
PHY-3002 : Step(108): len = 36535.5, overlap = 44.5
PHY-3002 : Step(109): len = 36100.4, overlap = 41.5
PHY-3002 : Step(110): len = 35982.5, overlap = 40.5
PHY-3002 : Step(111): len = 35989.2, overlap = 42.75
PHY-3002 : Step(112): len = 35800.7, overlap = 43.75
PHY-3002 : Step(113): len = 35522.8, overlap = 45.5
PHY-3002 : Step(114): len = 35490.6, overlap = 47.5
PHY-3002 : Step(115): len = 35175.7, overlap = 48
PHY-3002 : Step(116): len = 35234.7, overlap = 46.25
PHY-3002 : Step(117): len = 35027.5, overlap = 46.75
PHY-3002 : Step(118): len = 34985.8, overlap = 47.5
PHY-3002 : Step(119): len = 34911.7, overlap = 49.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000153342
PHY-3002 : Step(120): len = 34951.4, overlap = 46.5
PHY-3002 : Step(121): len = 35257.9, overlap = 45.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000306684
PHY-3002 : Step(122): len = 35800.2, overlap = 43.75
PHY-3002 : Step(123): len = 36184.7, overlap = 42.5
PHY-3002 : Step(124): len = 36317.5, overlap = 41.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.055486s wall, 0.031250s user + 0.078125s system = 0.109375s CPU (197.1%)

PHY-3001 : Trial Legalized: Len = 50071.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1395 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.033842s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00991196
PHY-3002 : Step(125): len = 48939.8, overlap = 1.75
PHY-3002 : Step(126): len = 46493.9, overlap = 6
PHY-3002 : Step(127): len = 45914.6, overlap = 7
PHY-3002 : Step(128): len = 44497.4, overlap = 8.25
PHY-3002 : Step(129): len = 43000.6, overlap = 10.25
PHY-3002 : Step(130): len = 42615, overlap = 11.25
PHY-3002 : Step(131): len = 42246.6, overlap = 11.25
PHY-3002 : Step(132): len = 41903.1, overlap = 12
PHY-3002 : Step(133): len = 41499.9, overlap = 13
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0185079
PHY-3002 : Step(134): len = 41554.4, overlap = 12.5
PHY-3002 : Step(135): len = 41360.4, overlap = 12.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0370159
PHY-3002 : Step(136): len = 41375.2, overlap = 13
PHY-3002 : Step(137): len = 41351.5, overlap = 12.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004821s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 45614.8, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004425s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 6 instances has been re-located, deltaX = 1, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 45894.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5825, tnet num: 1395, tinst num: 631, tnode num: 7314, tedge num: 9911.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 12/1397.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55712, over cnt = 163(0%), over = 270, worst = 7
PHY-1002 : len = 57104, over cnt = 85(0%), over = 113, worst = 5
PHY-1002 : len = 58176, over cnt = 24(0%), over = 35, worst = 4
PHY-1002 : len = 58664, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 58680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127171s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (110.6%)

PHY-1001 : Congestion index: top1 = 29.03, top5 = 21.35, top10 = 16.54, top15 = 12.44.
PHY-1001 : End incremental global routing;  0.176048s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (115.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1395 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.038590s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (81.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.240513s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (110.4%)

OPT-1001 : Current memory(MB): used = 198, reserve = 152, peak = 200.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1219/1397.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004773s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (327.3%)

PHY-1001 : Congestion index: top1 = 29.03, top5 = 21.35, top10 = 16.54, top15 = 12.44.
OPT-1001 : End congestion update;  0.047951s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (97.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1395 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.039899s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (78.3%)

OPT-0007 : Start: WNS 4072 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.088082s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (88.7%)

OPT-1001 : Current memory(MB): used = 200, reserve = 155, peak = 200.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1395 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.037884s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (123.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1219/1397.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58680, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004669s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (334.7%)

PHY-1001 : Congestion index: top1 = 29.03, top5 = 21.35, top10 = 16.54, top15 = 12.44.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1395 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.038354s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (81.5%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4072 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 28.551724
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4072ps with logic level 7 
OPT-1001 : End physical optimization;  0.593935s wall, 0.593750s user + 0.015625s system = 0.609375s CPU (102.6%)

RUN-1003 : finish command "place" in  3.991950s wall, 6.640625s user + 1.875000s system = 8.515625s CPU (213.3%)

RUN-1004 : used memory is 184 MB, reserved memory is 138 MB, peak memory is 201 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 633 instances
RUN-1001 : 298 mslices, 299 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1397 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 715 nets have 2 pins
RUN-1001 : 496 nets have [3 - 5] pins
RUN-1001 : 110 nets have [6 - 10] pins
RUN-1001 : 37 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5825, tnet num: 1395, tinst num: 631, tnode num: 7314, tedge num: 9911.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 298 mslices, 299 lslices, 8 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1395 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55592, over cnt = 167(0%), over = 275, worst = 7
PHY-1002 : len = 57008, over cnt = 88(0%), over = 117, worst = 5
PHY-1002 : len = 58216, over cnt = 17(0%), over = 24, worst = 4
PHY-1002 : len = 58648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.133222s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (140.7%)

PHY-1001 : Congestion index: top1 = 28.94, top5 = 21.35, top10 = 16.54, top15 = 12.44.
PHY-1001 : End global routing;  0.179288s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (130.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 220, reserve = 175, peak = 220.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 480, reserve = 438, peak = 480.
PHY-1001 : End build detailed router design. 4.434659s wall, 4.296875s user + 0.140625s system = 4.437500s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30992, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.174916s wall, 1.156250s user + 0.015625s system = 1.171875s CPU (99.7%)

PHY-1001 : Current memory(MB): used = 512, reserve = 472, peak = 512.
PHY-1001 : End phase 1; 1.181493s wall, 1.156250s user + 0.015625s system = 1.171875s CPU (99.2%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 199048, over cnt = 53(0%), over = 53, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 514, reserve = 472, peak = 514.
PHY-1001 : End initial routed; 3.002099s wall, 3.921875s user + 0.203125s system = 4.125000s CPU (137.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1233(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.697   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.433622s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (97.3%)

PHY-1001 : Current memory(MB): used = 517, reserve = 475, peak = 517.
PHY-1001 : End phase 2; 3.435831s wall, 4.343750s user + 0.203125s system = 4.546875s CPU (132.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 199048, over cnt = 53(0%), over = 53, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018150s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (172.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 198728, over cnt = 9(0%), over = 9, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.088407s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.0%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 198744, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.058127s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (53.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1233(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.697   |   0.000   |   0   
RUN-1001 :   Hold   |   0.097   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.434572s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (97.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 13 feed throughs used by 13 nets
PHY-1001 : End commit to database; 0.370464s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 531, reserve = 489, peak = 531.
PHY-1001 : End phase 3; 1.231414s wall, 1.218750s user + 0.000000s system = 1.218750s CPU (99.0%)

PHY-1003 : Routed, final wirelength = 198744
PHY-1001 : Current memory(MB): used = 531, reserve = 490, peak = 531.
PHY-1001 : End export database. 0.015183s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.9%)

PHY-1001 : End detail routing;  10.649714s wall, 11.390625s user + 0.359375s system = 11.750000s CPU (110.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5825, tnet num: 1395, tinst num: 631, tnode num: 7314, tedge num: 9911.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  11.413366s wall, 12.203125s user + 0.359375s system = 12.562500s CPU (110.1%)

RUN-1004 : used memory is 486 MB, reserved memory is 445 MB, peak memory is 531 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      913   out of  19600    4.66%
#reg                      611   out of  19600    3.12%
#le                      1118
  #lut only               507   out of   1118   45.35%
  #reg only               205   out of   1118   18.34%
  #lut&reg                406   out of   1118   36.31%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  23
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    301
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         100
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1118   |739     |174     |617     |23      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |119    |96      |11      |66      |0       |0       |
|    usms                            |Time_1ms        |27     |12      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |195    |124     |23      |131     |0       |0       |
|  uart                              |UART_Control    |103    |91      |4       |54      |0       |0       |
|    U0                              |speed_select_Tx |21     |12      |4       |15      |0       |0       |
|    U1                              |uart_tx         |15     |12      |0       |14      |0       |0       |
|    U2                              |Ctrl_Data       |67     |67      |0       |25      |0       |0       |
|  wendu                             |DS18B20         |174    |133     |41      |42      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |491    |275     |87      |297     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |491    |275     |87      |297     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |182    |75      |0       |178     |0       |0       |
|        reg_inst                    |register        |179    |72      |0       |175     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |309    |200     |87      |119     |0       |0       |
|        bus_inst                    |bus_top         |75     |49      |26      |26      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |3      |3       |0       |3       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |72     |46      |26      |23      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |129    |90      |29      |66      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout    Nets  
    #1         1       706   
    #2         2       300   
    #3         3       147   
    #4         4        49   
    #5        5-10     114   
    #6       11-50      60   
    #7       51-100     2    
  Average     2.90           

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5825, tnet num: 1395, tinst num: 631, tnode num: 7314, tedge num: 9911.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1395 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 588953dbe18b1871915f908614aff20f758057a2d1993c4931f4cee582d18853 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 631
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1397, pip num: 14171
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 13
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1247 valid insts, and 38955 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010011011001011011010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.825910s wall, 18.796875s user + 0.125000s system = 18.921875s CPU (494.6%)

RUN-1004 : used memory is 501 MB, reserved memory is 460 MB, peak memory is 649 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_115755.log"
