============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Aug  3 13:50:31 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1612 instances
RUN-0007 : 377 luts, 972 seqs, 139 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2168 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1630 nets have 2 pins
RUN-1001 : 422 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1610 instances, 377 luts, 972 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7683, tnet num: 2166, tinst num: 1610, tnode num: 10870, tedge num: 12959.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2166 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.290619s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (102.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 587725
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1610.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 481953, overlap = 20.25
PHY-3002 : Step(2): len = 443586, overlap = 20.25
PHY-3002 : Step(3): len = 378127, overlap = 18
PHY-3002 : Step(4): len = 337067, overlap = 15.75
PHY-3002 : Step(5): len = 317056, overlap = 18
PHY-3002 : Step(6): len = 293128, overlap = 18
PHY-3002 : Step(7): len = 288315, overlap = 20.25
PHY-3002 : Step(8): len = 280835, overlap = 20.25
PHY-3002 : Step(9): len = 275805, overlap = 20.25
PHY-3002 : Step(10): len = 269149, overlap = 20.25
PHY-3002 : Step(11): len = 264410, overlap = 20.25
PHY-3002 : Step(12): len = 257920, overlap = 20.25
PHY-3002 : Step(13): len = 252612, overlap = 20.25
PHY-3002 : Step(14): len = 246918, overlap = 20.25
PHY-3002 : Step(15): len = 243143, overlap = 20.25
PHY-3002 : Step(16): len = 236148, overlap = 20.25
PHY-3002 : Step(17): len = 230631, overlap = 20.25
PHY-3002 : Step(18): len = 226562, overlap = 20.25
PHY-3002 : Step(19): len = 223254, overlap = 20.25
PHY-3002 : Step(20): len = 211460, overlap = 20.25
PHY-3002 : Step(21): len = 206814, overlap = 20.25
PHY-3002 : Step(22): len = 204183, overlap = 20.25
PHY-3002 : Step(23): len = 199385, overlap = 20.25
PHY-3002 : Step(24): len = 174054, overlap = 20.25
PHY-3002 : Step(25): len = 170637, overlap = 20.25
PHY-3002 : Step(26): len = 168417, overlap = 20.25
PHY-3002 : Step(27): len = 137689, overlap = 18.0625
PHY-3002 : Step(28): len = 131673, overlap = 20.25
PHY-3002 : Step(29): len = 129572, overlap = 20.25
PHY-3002 : Step(30): len = 124776, overlap = 20.25
PHY-3002 : Step(31): len = 122387, overlap = 20.25
PHY-3002 : Step(32): len = 119427, overlap = 20.25
PHY-3002 : Step(33): len = 117002, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000102144
PHY-3002 : Step(34): len = 118211, overlap = 11.25
PHY-3002 : Step(35): len = 117416, overlap = 13.5
PHY-3002 : Step(36): len = 115787, overlap = 15.75
PHY-3002 : Step(37): len = 113089, overlap = 15.75
PHY-3002 : Step(38): len = 110609, overlap = 13.5
PHY-3002 : Step(39): len = 108883, overlap = 13.5
PHY-3002 : Step(40): len = 107119, overlap = 9
PHY-3002 : Step(41): len = 104591, overlap = 13.5
PHY-3002 : Step(42): len = 103656, overlap = 9
PHY-3002 : Step(43): len = 102137, overlap = 11.25
PHY-3002 : Step(44): len = 99802.4, overlap = 13.875
PHY-3002 : Step(45): len = 97475.2, overlap = 16.0625
PHY-3002 : Step(46): len = 95296.5, overlap = 11.5625
PHY-3002 : Step(47): len = 95294.6, overlap = 11.4375
PHY-3002 : Step(48): len = 94570.9, overlap = 6.875
PHY-3002 : Step(49): len = 93437.5, overlap = 9.125
PHY-3002 : Step(50): len = 92627.5, overlap = 13.625
PHY-3002 : Step(51): len = 91142.8, overlap = 13.625
PHY-3002 : Step(52): len = 88883.3, overlap = 13.625
PHY-3002 : Step(53): len = 88102.2, overlap = 13.5
PHY-3002 : Step(54): len = 87300.5, overlap = 13.5
PHY-3002 : Step(55): len = 86643.9, overlap = 15.75
PHY-3002 : Step(56): len = 83692.4, overlap = 14.0625
PHY-3002 : Step(57): len = 80830.3, overlap = 14.3125
PHY-3002 : Step(58): len = 78766.7, overlap = 14.375
PHY-3002 : Step(59): len = 77726.1, overlap = 14.375
PHY-3002 : Step(60): len = 76278.7, overlap = 14
PHY-3002 : Step(61): len = 75262, overlap = 14
PHY-3002 : Step(62): len = 74258.1, overlap = 11.75
PHY-3002 : Step(63): len = 73214.5, overlap = 11.8125
PHY-3002 : Step(64): len = 73262.7, overlap = 9.625
PHY-3002 : Step(65): len = 72984.3, overlap = 11.8125
PHY-3002 : Step(66): len = 71672.4, overlap = 9.375
PHY-3002 : Step(67): len = 70224, overlap = 11.25
PHY-3002 : Step(68): len = 69254.8, overlap = 11.3125
PHY-3002 : Step(69): len = 68485, overlap = 9.125
PHY-3002 : Step(70): len = 67067.1, overlap = 9.125
PHY-3002 : Step(71): len = 66358.8, overlap = 11.25
PHY-3002 : Step(72): len = 64973.7, overlap = 13.5
PHY-3002 : Step(73): len = 63566.7, overlap = 11.25
PHY-3002 : Step(74): len = 63177.5, overlap = 13.5
PHY-3002 : Step(75): len = 62348.8, overlap = 9
PHY-3002 : Step(76): len = 61520.5, overlap = 9
PHY-3002 : Step(77): len = 61262.6, overlap = 9
PHY-3002 : Step(78): len = 59546.7, overlap = 13.5
PHY-3002 : Step(79): len = 57591.2, overlap = 9
PHY-3002 : Step(80): len = 56983.3, overlap = 11.25
PHY-3002 : Step(81): len = 55965, overlap = 9
PHY-3002 : Step(82): len = 55711.7, overlap = 9
PHY-3002 : Step(83): len = 55495.6, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000204287
PHY-3002 : Step(84): len = 55628.5, overlap = 11.25
PHY-3002 : Step(85): len = 55650.6, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000408575
PHY-3002 : Step(86): len = 55635.2, overlap = 6.75
PHY-3002 : Step(87): len = 55704.5, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006272s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (249.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2166 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061826s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(88): len = 58709.8, overlap = 2.75
PHY-3002 : Step(89): len = 57543.9, overlap = 3.375
PHY-3002 : Step(90): len = 56768.9, overlap = 3.90625
PHY-3002 : Step(91): len = 55767.8, overlap = 4.28125
PHY-3002 : Step(92): len = 55386.4, overlap = 3.71875
PHY-3002 : Step(93): len = 54231.2, overlap = 3.875
PHY-3002 : Step(94): len = 53735.7, overlap = 3.46875
PHY-3002 : Step(95): len = 52802.9, overlap = 4.125
PHY-3002 : Step(96): len = 52098.1, overlap = 5.25
PHY-3002 : Step(97): len = 50872.6, overlap = 8.96875
PHY-3002 : Step(98): len = 50579.5, overlap = 11.8125
PHY-3002 : Step(99): len = 49769.5, overlap = 12.2188
PHY-3002 : Step(100): len = 49281.7, overlap = 12.7188
PHY-3002 : Step(101): len = 48698.4, overlap = 13.9375
PHY-3002 : Step(102): len = 48519.8, overlap = 14.1875
PHY-3002 : Step(103): len = 48050.9, overlap = 14.5938
PHY-3002 : Step(104): len = 47898.2, overlap = 14.4062
PHY-3002 : Step(105): len = 47673.6, overlap = 16.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000456387
PHY-3002 : Step(106): len = 47490.7, overlap = 16.4062
PHY-3002 : Step(107): len = 47375.8, overlap = 16.6562
PHY-3002 : Step(108): len = 47375.8, overlap = 16.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000912775
PHY-3002 : Step(109): len = 47620.3, overlap = 16.4688
PHY-3002 : Step(110): len = 47763.5, overlap = 16.5938
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2166 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057665s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000130071
PHY-3002 : Step(111): len = 47986.3, overlap = 64.7188
PHY-3002 : Step(112): len = 48236.6, overlap = 61.75
PHY-3002 : Step(113): len = 49461.3, overlap = 54
PHY-3002 : Step(114): len = 50101.1, overlap = 48.3438
PHY-3002 : Step(115): len = 50155.6, overlap = 47.4688
PHY-3002 : Step(116): len = 50152.3, overlap = 44.5
PHY-3002 : Step(117): len = 50075.3, overlap = 42
PHY-3002 : Step(118): len = 50162.6, overlap = 43.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000260143
PHY-3002 : Step(119): len = 50083.7, overlap = 41.9375
PHY-3002 : Step(120): len = 50222.9, overlap = 42.2188
PHY-3002 : Step(121): len = 50435.6, overlap = 42.875
PHY-3002 : Step(122): len = 50423.3, overlap = 42
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000520286
PHY-3002 : Step(123): len = 51009.5, overlap = 33.0312
PHY-3002 : Step(124): len = 51234.3, overlap = 30.9375
PHY-3002 : Step(125): len = 51767.8, overlap = 29.6875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7683, tnet num: 2166, tinst num: 1610, tnode num: 10870, tedge num: 12959.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 85.00 peak overflow 2.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2168.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55272, over cnt = 246(0%), over = 955, worst = 16
PHY-1001 : End global iterations;  0.055323s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (197.7%)

PHY-1001 : Congestion index: top1 = 40.54, top5 = 25.10, top10 = 16.06, top15 = 11.34.
PHY-1001 : End incremental global routing;  0.103848s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (135.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2166 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065870s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (118.6%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1571 has valid locations, 5 needs to be replaced
PHY-3001 : design contains 1614 instances, 377 luts, 976 seqs, 214 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 51951.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7699, tnet num: 2170, tinst num: 1614, tnode num: 10898, tedge num: 12983.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.296225s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (100.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(126): len = 52028.6, overlap = 4.15625
PHY-3002 : Step(127): len = 52039.6, overlap = 4.15625
PHY-3002 : Step(128): len = 52040.5, overlap = 4.21875
PHY-3002 : Step(129): len = 52040.5, overlap = 4.21875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056397s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000683608
PHY-3002 : Step(130): len = 52050.5, overlap = 29.875
PHY-3002 : Step(131): len = 52050.5, overlap = 29.875
PHY-3001 : Final: Len = 52050.5, Over = 29.875
PHY-3001 : End incremental placement;  0.454488s wall, 0.468750s user + 0.125000s system = 0.593750s CPU (130.6%)

OPT-1001 : Total overflow 85.25 peak overflow 2.38
OPT-1001 : End high-fanout net optimization;  0.660559s wall, 0.703125s user + 0.140625s system = 0.843750s CPU (127.7%)

OPT-1001 : Current memory(MB): used = 221, reserve = 184, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1680/2172.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55464, over cnt = 246(0%), over = 952, worst = 16
PHY-1002 : len = 61352, over cnt = 150(0%), over = 343, worst = 12
PHY-1002 : len = 64880, over cnt = 25(0%), over = 60, worst = 9
PHY-1002 : len = 65624, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 65672, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087582s wall, 0.078125s user + 0.031250s system = 0.109375s CPU (124.9%)

PHY-1001 : Congestion index: top1 = 35.86, top5 = 25.11, top10 = 17.75, top15 = 12.98.
OPT-1001 : End congestion update;  0.130566s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (119.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2170 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056573s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.5%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.189671s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (115.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 221.
OPT-1001 : End physical optimization;  1.123248s wall, 1.171875s user + 0.171875s system = 1.343750s CPU (119.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 377 LUT to BLE ...
SYN-4008 : Packed 377 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 794 remaining SEQ's ...
SYN-4005 : Packed 111 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 683 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1060/1381 primitive instances ...
PHY-3001 : End packing;  0.050808s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (123.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 822 instances
RUN-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2005 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 414 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 820 instances, 773 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51879.8, Over = 63.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2003, tinst num: 820, tnode num: 8803, tedge num: 11368.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2003 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.298050s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.74226e-05
PHY-3002 : Step(132): len = 51551.4, overlap = 62.5
PHY-3002 : Step(133): len = 50994.7, overlap = 60.75
PHY-3002 : Step(134): len = 50690.3, overlap = 66.25
PHY-3002 : Step(135): len = 50905.5, overlap = 63.75
PHY-3002 : Step(136): len = 50717.3, overlap = 60.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.48453e-05
PHY-3002 : Step(137): len = 50795.9, overlap = 59.5
PHY-3002 : Step(138): len = 51713, overlap = 54.75
PHY-3002 : Step(139): len = 52084.8, overlap = 53.25
PHY-3002 : Step(140): len = 52014.6, overlap = 54.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000109691
PHY-3002 : Step(141): len = 52610.4, overlap = 52.5
PHY-3002 : Step(142): len = 53358.6, overlap = 51
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.083857s wall, 0.093750s user + 0.078125s system = 0.171875s CPU (205.0%)

PHY-3001 : Trial Legalized: Len = 66134.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2003 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050499s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000468777
PHY-3002 : Step(143): len = 63492.7, overlap = 7.25
PHY-3002 : Step(144): len = 61767.2, overlap = 12.75
PHY-3002 : Step(145): len = 60180.5, overlap = 18.25
PHY-3002 : Step(146): len = 59129.3, overlap = 20
PHY-3002 : Step(147): len = 58534.3, overlap = 22.75
PHY-3002 : Step(148): len = 58163.9, overlap = 23.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000937554
PHY-3002 : Step(149): len = 58563.6, overlap = 22.75
PHY-3002 : Step(150): len = 58717.3, overlap = 21.75
PHY-3002 : Step(151): len = 58748, overlap = 22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00187511
PHY-3002 : Step(152): len = 58849.4, overlap = 21.75
PHY-3002 : Step(153): len = 58849.4, overlap = 21.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004815s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62985, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005792s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 0, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 63009, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2003, tinst num: 820, tnode num: 8803, tedge num: 11368.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 121/2005.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69632, over cnt = 136(0%), over = 213, worst = 8
PHY-1002 : len = 70144, over cnt = 71(0%), over = 117, worst = 8
PHY-1002 : len = 71576, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.117913s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (119.3%)

PHY-1001 : Congestion index: top1 = 33.71, top5 = 22.64, top10 = 17.61, top15 = 13.95.
PHY-1001 : End incremental global routing;  0.168533s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (111.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2003 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056507s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.256106s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (103.7%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1776/2005.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006799s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (229.8%)

PHY-1001 : Congestion index: top1 = 33.71, top5 = 22.64, top10 = 17.61, top15 = 13.95.
OPT-1001 : End congestion update;  0.053409s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2003 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047419s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 782 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 820 instances, 773 slices, 25 macros(214 instances: 139 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63064.8, Over = 0
PHY-3001 : End spreading;  0.004767s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63064.8, Over = 0
PHY-3001 : End incremental legalization;  0.034098s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.147634s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (105.8%)

OPT-1001 : Current memory(MB): used = 224, reserve = 188, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2003 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051809s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1772/2005.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71600, over cnt = 1(0%), over = 2, worst = 2
PHY-1002 : len = 71616, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.025745s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (121.4%)

PHY-1001 : Congestion index: top1 = 33.60, top5 = 22.62, top10 = 17.61, top15 = 13.95.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2003 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050321s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.877765s wall, 0.875000s user + 0.015625s system = 0.890625s CPU (101.5%)

RUN-1003 : finish command "place" in  5.763923s wall, 8.343750s user + 3.171875s system = 11.515625s CPU (199.8%)

RUN-1004 : used memory is 203 MB, reserved memory is 166 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 822 instances
RUN-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2005 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 414 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 24 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2003, tinst num: 820, tnode num: 8803, tedge num: 11368.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2003 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68248, over cnt = 143(0%), over = 226, worst = 8
PHY-1002 : len = 68832, over cnt = 75(0%), over = 130, worst = 8
PHY-1002 : len = 70472, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70504, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134826s wall, 0.140625s user + 0.062500s system = 0.203125s CPU (150.7%)

PHY-1001 : Congestion index: top1 = 32.87, top5 = 22.14, top10 = 17.32, top15 = 13.70.
PHY-1001 : End global routing;  0.184022s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (144.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 206, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.208137s wall, 3.171875s user + 0.031250s system = 3.203125s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32344, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.320209s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (101.8%)

PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 535.
PHY-1001 : End phase 1; 1.325801s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (101.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178720, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End initial routed; 1.037133s wall, 1.968750s user + 0.140625s system = 2.109375s CPU (203.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1779(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.360   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.366455s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (102.3%)

PHY-1001 : Current memory(MB): used = 538, reserve = 506, peak = 538.
PHY-1001 : End phase 2; 1.403683s wall, 2.343750s user + 0.140625s system = 2.484375s CPU (177.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178720, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015647s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (99.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178592, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028624s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (109.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178608, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019228s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (162.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1779(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.360   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.367210s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (97.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.173661s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End phase 3; 0.725271s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (99.1%)

PHY-1003 : Routed, final wirelength = 178608
PHY-1001 : Current memory(MB): used = 554, reserve = 522, peak = 554.
PHY-1001 : End export database. 0.009348s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (167.2%)

PHY-1001 : End detail routing;  6.863308s wall, 7.765625s user + 0.203125s system = 7.968750s CPU (116.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2003, tinst num: 820, tnode num: 8803, tedge num: 11368.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.774258s wall, 8.687500s user + 0.265625s system = 8.953125s CPU (115.2%)

RUN-1004 : used memory is 505 MB, reserved memory is 477 MB, peak memory is 554 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      810   out of  19600    4.13%
#reg                     1051   out of  19600    5.36%
#le                      1493
  #lut only               442   out of   1493   29.60%
  #reg only               683   out of   1493   45.75%
  #lut&reg                368   out of   1493   24.65%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         464
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1493   |596     |214     |1082    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1085   |290     |121     |895     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |17      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |524    |123     |53      |435     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |4       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |137    |17      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |58     |21      |14      |54      |0       |1       |
|    rs422                   |Rs422Output                                      |311    |89      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |28     |23      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |113    |97      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |56     |50      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |212    |167     |45      |76      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1435  
    #2          2       291   
    #3          3       106   
    #4          4        17   
    #5        5-10       80   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6487, tnet num: 2003, tinst num: 820, tnode num: 8803, tedge num: 11368.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2003 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 820
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2005, pip num: 14636
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1251 valid insts, and 38697 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.043753s wall, 17.546875s user + 0.046875s system = 17.593750s CPU (578.0%)

RUN-1004 : used memory is 522 MB, reserved memory is 490 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230803_135031.log"
