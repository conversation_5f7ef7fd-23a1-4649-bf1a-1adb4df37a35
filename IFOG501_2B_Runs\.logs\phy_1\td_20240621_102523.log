============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 10:25:23 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 1 view nodes, 24 trigger nets, 24 data nets.
KIT-1004 : Chipwatcher code = 1001010101111010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=1,BUS_DIN_NUM=24,BUS_CTRL_NUM=52,BUS_WIDTH='{32'sb011000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=74) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=74) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=1,BUS_DIN_NUM=24,BUS_CTRL_NUM=52,BUS_WIDTH='{32'sb011000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=24,BUS_CTRL_NUM=52,BUS_WIDTH='{32'sb011000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=1,BUS_DIN_NUM=24,BUS_CTRL_NUM=52,BUS_WIDTH='{32'sb011000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=74)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=74)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=1,BUS_DIN_NUM=24,BUS_CTRL_NUM=52,BUS_WIDTH='{32'sb011000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=1,BUS_DIN_NUM=24,BUS_CTRL_NUM=52,BUS_WIDTH='{32'sb011000},BUS_DIN_POS='{32'sb0},BUS_CTRL_POS='{32'sb0})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 1773/7 useful/useless nets, 1028/2 useful/useless insts
SYN-1016 : Merged 12 instances.
SYN-1032 : 1574/2 useful/useless nets, 829/2 useful/useless insts
SYN-1032 : 1554/20 useful/useless nets, 1341/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 266 better
SYN-1014 : Optimize round 2
SYN-1032 : 1372/15 useful/useless nets, 1159/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1396/155 useful/useless nets, 1204/29 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 205 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 48 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 1756/5 useful/useless nets, 1564/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6426, tnet num: 1756, tinst num: 1563, tnode num: 8107, tedge num: 9837.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1756 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 172 (3.52), #lev = 8 (1.77)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 169 (3.56), #lev = 7 (1.89)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 366 instances into 169 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 263 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 WARNING: The kept net uart/Macc_data[14] will be merged to another kept net CtrlData/Macc_data[14]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (167 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1097 instances
RUN-0007 : 411 luts, 492 seqs, 97 mslices, 62 lslices, 8 pads, 22 brams, 0 dsps
RUN-1001 : There are total 1296 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 750 nets have 2 pins
RUN-1001 : 400 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 39 nets have [11 - 20] pins
RUN-1001 : 23 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     111     
RUN-1001 :   No   |  No   |  Yes  |     98      
RUN-1001 :   No   |  Yes  |  No   |     71      
RUN-1001 :   Yes  |  No   |  No   |     49      
RUN-1001 :   Yes  |  No   |  Yes  |     163     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   6   |     10     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 15
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1095 instances, 411 luts, 492 seqs, 159 slices, 24 macros(159 instances: 97 mslices 62 lslices)
PHY-0007 : Cell area utilization is 3%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5446, tnet num: 1294, tinst num: 1095, tnode num: 7122, tedge num: 9031.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1294 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.109025s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (100.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 328823
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1095.
PHY-3001 : End clustering;  0.000025s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 3%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 251892, overlap = 49.5
PHY-3002 : Step(2): len = 205737, overlap = 49.5
PHY-3002 : Step(3): len = 183764, overlap = 49.5
PHY-3002 : Step(4): len = 167311, overlap = 49.5
PHY-3002 : Step(5): len = 154526, overlap = 49.5
PHY-3002 : Step(6): len = 140577, overlap = 49.5
PHY-3002 : Step(7): len = 125405, overlap = 49.5
PHY-3002 : Step(8): len = 113211, overlap = 49.5
PHY-3002 : Step(9): len = 102500, overlap = 49.5
PHY-3002 : Step(10): len = 92889.6, overlap = 49.75
PHY-3002 : Step(11): len = 84564.9, overlap = 49.875
PHY-3002 : Step(12): len = 76359.6, overlap = 50.6875
PHY-3002 : Step(13): len = 70892, overlap = 51.1875
PHY-3002 : Step(14): len = 64753.2, overlap = 51.7188
PHY-3002 : Step(15): len = 60818.9, overlap = 52.4688
PHY-3002 : Step(16): len = 53806, overlap = 53.875
PHY-3002 : Step(17): len = 49465.7, overlap = 54.625
PHY-3002 : Step(18): len = 47062.8, overlap = 54.7812
PHY-3002 : Step(19): len = 42922.7, overlap = 52.7188
PHY-3002 : Step(20): len = 41193.6, overlap = 51.5938
PHY-3002 : Step(21): len = 39038.8, overlap = 50.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.38281e-06
PHY-3002 : Step(22): len = 38088, overlap = 50.7812
PHY-3002 : Step(23): len = 38885.9, overlap = 49.5
PHY-3002 : Step(24): len = 39074, overlap = 49.5
PHY-3002 : Step(25): len = 37398.2, overlap = 45
PHY-3002 : Step(26): len = 37754.8, overlap = 38.25
PHY-3002 : Step(27): len = 38094.5, overlap = 36
PHY-3002 : Step(28): len = 36756.9, overlap = 36
PHY-3002 : Step(29): len = 36434.9, overlap = 31.5
PHY-3002 : Step(30): len = 36123.7, overlap = 31.5
PHY-3002 : Step(31): len = 36049.6, overlap = 31.5
PHY-3002 : Step(32): len = 35580.2, overlap = 29.25
PHY-3002 : Step(33): len = 35235.5, overlap = 36
PHY-3002 : Step(34): len = 34873.6, overlap = 36
PHY-3002 : Step(35): len = 34212.9, overlap = 36
PHY-3002 : Step(36): len = 33828.1, overlap = 27
PHY-3002 : Step(37): len = 33481.8, overlap = 36
PHY-3002 : Step(38): len = 32811.5, overlap = 36
PHY-3002 : Step(39): len = 32628.8, overlap = 31.5
PHY-3002 : Step(40): len = 32275, overlap = 29.4375
PHY-3002 : Step(41): len = 31546.6, overlap = 34.9375
PHY-3002 : Step(42): len = 30905.4, overlap = 35.6875
PHY-3002 : Step(43): len = 30448, overlap = 38.8438
PHY-3002 : Step(44): len = 29909.4, overlap = 40.0625
PHY-3002 : Step(45): len = 29295.7, overlap = 41.1875
PHY-3002 : Step(46): len = 28822.6, overlap = 43.6875
PHY-3002 : Step(47): len = 28522, overlap = 38.9688
PHY-3002 : Step(48): len = 28209.9, overlap = 36.375
PHY-3002 : Step(49): len = 28041.4, overlap = 36.5
PHY-3002 : Step(50): len = 27820.1, overlap = 35.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.07656e-05
PHY-3002 : Step(51): len = 27684.5, overlap = 35.9062
PHY-3002 : Step(52): len = 27632.3, overlap = 35.8438
PHY-3002 : Step(53): len = 27640.4, overlap = 35.9688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 2.15312e-05
PHY-3002 : Step(54): len = 27684.5, overlap = 33.6562
PHY-3002 : Step(55): len = 27684.5, overlap = 33.6562
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003642s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (429.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1294 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.030687s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000119744
PHY-3002 : Step(56): len = 31043.4, overlap = 18.875
PHY-3002 : Step(57): len = 31155.3, overlap = 18.5312
PHY-3002 : Step(58): len = 31248.7, overlap = 21.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000239488
PHY-3002 : Step(59): len = 31210.1, overlap = 22.4062
PHY-3002 : Step(60): len = 31226.6, overlap = 22.4062
PHY-3002 : Step(61): len = 31429.2, overlap = 22.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000478976
PHY-3002 : Step(62): len = 31503.9, overlap = 22.5625
PHY-3002 : Step(63): len = 31547.5, overlap = 22.6562
PHY-3002 : Step(64): len = 32019.4, overlap = 22.625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1294 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.028741s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.85786e-05
PHY-3002 : Step(65): len = 32233.7, overlap = 47.4062
PHY-3002 : Step(66): len = 32447, overlap = 46.25
PHY-3002 : Step(67): len = 33564.7, overlap = 41.5625
PHY-3002 : Step(68): len = 34075.6, overlap = 39.9688
PHY-3002 : Step(69): len = 34395, overlap = 36.75
PHY-3002 : Step(70): len = 34598, overlap = 33.1875
PHY-3002 : Step(71): len = 34231, overlap = 32.8125
PHY-3002 : Step(72): len = 34011.5, overlap = 32.6875
PHY-3002 : Step(73): len = 33704.9, overlap = 28.5938
PHY-3002 : Step(74): len = 33616, overlap = 30.2812
PHY-3002 : Step(75): len = 33865.3, overlap = 33.8125
PHY-3002 : Step(76): len = 33463, overlap = 35.3125
PHY-3002 : Step(77): len = 33167.2, overlap = 34.8438
PHY-3002 : Step(78): len = 32935.5, overlap = 33.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000137157
PHY-3002 : Step(79): len = 32855.2, overlap = 32.6562
PHY-3002 : Step(80): len = 32855.2, overlap = 32.6562
PHY-3002 : Step(81): len = 32884.5, overlap = 32.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000274314
PHY-3002 : Step(82): len = 33206.1, overlap = 31.1562
PHY-3002 : Step(83): len = 33375.9, overlap = 30
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000548629
PHY-3002 : Step(84): len = 33834.9, overlap = 29.6875
PHY-3002 : Step(85): len = 34341.7, overlap = 26.4375
PHY-3002 : Step(86): len = 35092.2, overlap = 22.5625
PHY-3002 : Step(87): len = 34754.2, overlap = 23.2812
PHY-3002 : Step(88): len = 34500, overlap = 23.625
PHY-3002 : Step(89): len = 34500, overlap = 23.625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5446, tnet num: 1294, tinst num: 1095, tnode num: 7122, tedge num: 9031.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 63.28 peak overflow 1.94
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1296.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 40080, over cnt = 170(0%), over = 574, worst = 22
PHY-1001 : End global iterations;  0.063126s wall, 0.093750s user + 0.062500s system = 0.156250s CPU (247.5%)

PHY-1001 : Congestion index: top1 = 32.44, top5 = 18.77, top10 = 11.53, top15 = 8.23.
PHY-1001 : End incremental global routing;  0.109024s wall, 0.140625s user + 0.062500s system = 0.203125s CPU (186.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1294 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.034619s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (135.4%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1083 has valid locations, 22 needs to be replaced
PHY-3001 : design contains 1116 instances, 411 luts, 513 seqs, 159 slices, 24 macros(159 instances: 97 mslices 62 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 34771.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 5530, tnet num: 1315, tinst num: 1116, tnode num: 7269, tedge num: 9157.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1315 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.116704s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (107.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(90): len = 34776, overlap = 0.25
PHY-3002 : Step(91): len = 34776, overlap = 0.25
PHY-3002 : Step(92): len = 34833.6, overlap = 0.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 4%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1315 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.030141s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(93): len = 34833.6, overlap = 23.5625
PHY-3002 : Step(94): len = 34833.6, overlap = 23.5625
PHY-3001 : Final: Len = 34833.6, Over = 23.5625
PHY-3001 : End incremental placement;  0.218817s wall, 0.218750s user + 0.109375s system = 0.328125s CPU (150.0%)

OPT-1001 : Total overflow 64.03 peak overflow 1.94
OPT-1001 : End high-fanout net optimization;  0.388079s wall, 0.406250s user + 0.187500s system = 0.593750s CPU (153.0%)

OPT-1001 : Current memory(MB): used = 190, reserve = 144, peak = 190.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 936/1317.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 40864, over cnt = 171(0%), over = 583, worst = 22
PHY-1002 : len = 44656, over cnt = 100(0%), over = 200, worst = 9
PHY-1002 : len = 45712, over cnt = 42(0%), over = 83, worst = 9
PHY-1002 : len = 46896, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 46992, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.090264s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (103.9%)

PHY-1001 : Congestion index: top1 = 30.71, top5 = 19.56, top10 = 12.85, top15 = 9.25.
OPT-1001 : End congestion update;  0.130225s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (120.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1315 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.029638s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (52.7%)

OPT-0007 : Start: WNS 2993 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 2993 TNS 0 NUM_FEPS 0 with 5 cells processed and 0 slack improved
OPT-0007 : Iter 2: improved WNS 2993 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 3: improved WNS 2993 TNS 0 NUM_FEPS 0 with 1 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.162066s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (115.7%)

OPT-1001 : Current memory(MB): used = 188, reserve = 142, peak = 190.
OPT-1001 : End physical optimization;  0.669245s wall, 0.687500s user + 0.203125s system = 0.890625s CPU (133.1%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 411 LUT to BLE ...
SYN-4008 : Packed 411 LUT and 171 SEQ to BLE.
SYN-4003 : Packing 342 remaining SEQ's ...
SYN-4005 : Packed 174 SEQ with LUT/SLICE
SYN-4006 : 87 single LUT's are left
SYN-4006 : 168 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 579/905 primitive instances ...
PHY-3001 : End packing;  0.028699s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 521 instances
RUN-1001 : 243 mslices, 243 lslices, 8 pads, 22 brams, 0 dsps
RUN-1001 : There are total 1148 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 574 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 30 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 3 nets have 100+ pins
PHY-3001 : design contains 519 instances, 486 slices, 24 macros(159 instances: 97 mslices 62 lslices)
PHY-3001 : Cell area utilization is 6%
PHY-3001 : After packing: Len = 35215, Over = 34.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4773, tnet num: 1146, tinst num: 519, tnode num: 6047, tedge num: 8204.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1146 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.124509s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (100.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.40068e-05
PHY-3002 : Step(95): len = 34684.4, overlap = 34.25
PHY-3002 : Step(96): len = 34210.8, overlap = 36.25
PHY-3002 : Step(97): len = 33940, overlap = 37.5
PHY-3002 : Step(98): len = 33805.8, overlap = 38.5
PHY-3002 : Step(99): len = 33915.3, overlap = 39
PHY-3002 : Step(100): len = 34068.6, overlap = 37.25
PHY-3002 : Step(101): len = 34350.5, overlap = 32.75
PHY-3002 : Step(102): len = 34638.1, overlap = 35
PHY-3002 : Step(103): len = 34739.2, overlap = 35.25
PHY-3002 : Step(104): len = 34596.9, overlap = 33.5
PHY-3002 : Step(105): len = 34279.7, overlap = 37
PHY-3002 : Step(106): len = 33844.6, overlap = 35
PHY-3002 : Step(107): len = 33528.7, overlap = 35
PHY-3002 : Step(108): len = 33210.1, overlap = 34.75
PHY-3002 : Step(109): len = 32820.1, overlap = 39.75
PHY-3002 : Step(110): len = 32573.1, overlap = 37
PHY-3002 : Step(111): len = 32283.4, overlap = 36.25
PHY-3002 : Step(112): len = 31840.7, overlap = 37
PHY-3002 : Step(113): len = 31323.6, overlap = 37.5
PHY-3002 : Step(114): len = 31062.5, overlap = 36.5
PHY-3002 : Step(115): len = 31172.2, overlap = 31.75
PHY-3002 : Step(116): len = 30965.1, overlap = 33
PHY-3002 : Step(117): len = 30694.2, overlap = 35.25
PHY-3002 : Step(118): len = 30555.7, overlap = 37.25
PHY-3002 : Step(119): len = 30584, overlap = 41.25
PHY-3002 : Step(120): len = 30673.2, overlap = 42.5
PHY-3002 : Step(121): len = 30419.4, overlap = 43.25
PHY-3002 : Step(122): len = 30168.7, overlap = 42
PHY-3002 : Step(123): len = 29874.8, overlap = 36.5
PHY-3002 : Step(124): len = 29646.2, overlap = 36.75
PHY-3002 : Step(125): len = 29585.8, overlap = 34.25
PHY-3002 : Step(126): len = 29295.5, overlap = 36
PHY-3002 : Step(127): len = 29056.2, overlap = 36.5
PHY-3002 : Step(128): len = 28934.1, overlap = 34.75
PHY-3002 : Step(129): len = 28835, overlap = 34.5
PHY-3002 : Step(130): len = 28805.6, overlap = 36.5
PHY-3002 : Step(131): len = 28494, overlap = 38.25
PHY-3002 : Step(132): len = 28355.5, overlap = 41.5
PHY-3002 : Step(133): len = 28253, overlap = 41.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000128014
PHY-3002 : Step(134): len = 28581.3, overlap = 41
PHY-3002 : Step(135): len = 28751.6, overlap = 39.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000256027
PHY-3002 : Step(136): len = 29421.6, overlap = 37.75
PHY-3002 : Step(137): len = 30122.1, overlap = 34.75
PHY-3002 : Step(138): len = 30230.2, overlap = 34.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.076283s wall, 0.046875s user + 0.078125s system = 0.125000s CPU (163.9%)

PHY-3001 : Trial Legalized: Len = 39783.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1146 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.025740s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (60.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0045101
PHY-3002 : Step(139): len = 37396.9, overlap = 3.5
PHY-3002 : Step(140): len = 35700.9, overlap = 5.25
PHY-3002 : Step(141): len = 35050.4, overlap = 7
PHY-3002 : Step(142): len = 34272.3, overlap = 9.5
PHY-3002 : Step(143): len = 33507.5, overlap = 6.75
PHY-3002 : Step(144): len = 32928.6, overlap = 7.75
PHY-3002 : Step(145): len = 32477.7, overlap = 9.25
PHY-3002 : Step(146): len = 32357.3, overlap = 11.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00902019
PHY-3002 : Step(147): len = 32391.1, overlap = 11
PHY-3002 : Step(148): len = 32369.3, overlap = 9.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0180404
PHY-3002 : Step(149): len = 32383.5, overlap = 10
PHY-3002 : Step(150): len = 32357.3, overlap = 10
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004666s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 36138.4, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.003812s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 1, deltaY = 3, maxDist = 1.
PHY-3001 : Final: Len = 36274.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4773, tnet num: 1146, tinst num: 519, tnode num: 6047, tedge num: 8204.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 6/1148.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 44048, over cnt = 118(0%), over = 183, worst = 4
PHY-1002 : len = 45016, over cnt = 42(0%), over = 49, worst = 3
PHY-1002 : len = 45560, over cnt = 7(0%), over = 8, worst = 2
PHY-1002 : len = 45608, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 45688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114676s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (122.6%)

PHY-1001 : Congestion index: top1 = 27.33, top5 = 19.86, top10 = 13.80, top15 = 9.85.
PHY-1001 : End incremental global routing;  0.161771s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (106.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1146 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.035815s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (87.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.219229s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (106.9%)

OPT-1001 : Current memory(MB): used = 189, reserve = 143, peak = 190.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 981/1148.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 45688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003840s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (406.9%)

PHY-1001 : Congestion index: top1 = 27.33, top5 = 19.86, top10 = 13.80, top15 = 9.85.
OPT-1001 : End congestion update;  0.046568s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1146 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.024896s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (62.8%)

OPT-0007 : Start: WNS 4379 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.071655s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.2%)

OPT-1001 : Current memory(MB): used = 190, reserve = 144, peak = 190.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1146 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.024614s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (127.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 981/1148.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 45688, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.003643s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (428.9%)

PHY-1001 : Congestion index: top1 = 27.33, top5 = 19.86, top10 = 13.80, top15 = 9.85.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1146 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.025589s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (61.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4379 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 26.896552
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4379ps with logic level 6 
RUN-1001 :       #2 path slack 4458ps with logic level 6 
OPT-1001 : End physical optimization;  0.506009s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (101.9%)

RUN-1003 : finish command "place" in  3.813108s wall, 5.328125s user + 2.375000s system = 7.703125s CPU (202.0%)

RUN-1004 : used memory is 183 MB, reserved memory is 137 MB, peak memory is 191 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 521 instances
RUN-1001 : 243 mslices, 243 lslices, 8 pads, 22 brams, 0 dsps
RUN-1001 : There are total 1148 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 574 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 79 nets have [6 - 10] pins
RUN-1001 : 30 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 3 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4773, tnet num: 1146, tinst num: 519, tnode num: 6047, tedge num: 8204.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 243 mslices, 243 lslices, 8 pads, 22 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1146 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 43936, over cnt = 117(0%), over = 183, worst = 4
PHY-1002 : len = 44880, over cnt = 40(0%), over = 47, worst = 3
PHY-1002 : len = 45392, over cnt = 9(0%), over = 11, worst = 3
PHY-1002 : len = 45536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109020s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (114.7%)

PHY-1001 : Congestion index: top1 = 27.35, top5 = 19.85, top10 = 13.79, top15 = 9.83.
PHY-1001 : End global routing;  0.151785s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (113.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 211, reserve = 165, peak = 211.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 470, reserve = 428, peak = 470.
PHY-1001 : End build detailed router design. 3.081317s wall, 3.015625s user + 0.062500s system = 3.078125s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 21216, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.585532s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (101.4%)

PHY-1001 : Current memory(MB): used = 502, reserve = 461, peak = 502.
PHY-1001 : End phase 1; 0.591525s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 44% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 86% nets.
PHY-1022 : len = 152584, over cnt = 51(0%), over = 52, worst = 2, crit = 0
PHY-1001 : Current memory(MB): used = 502, reserve = 461, peak = 502.
PHY-1001 : End initial routed; 1.164648s wall, 1.343750s user + 0.078125s system = 1.421875s CPU (122.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/997(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.111   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.164553s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.0%)

PHY-1001 : Current memory(MB): used = 504, reserve = 462, peak = 504.
PHY-1001 : End phase 2; 1.329282s wall, 1.500000s user + 0.078125s system = 1.578125s CPU (118.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 152584, over cnt = 51(0%), over = 52, worst = 2, crit = 0
PHY-1001 : End optimize timing; 0.009232s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (169.3%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 152488, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.034649s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (135.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 152536, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.019037s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (82.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 152536, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.015576s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Update timing.....
PHY-1001 : 0/997(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.111   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.160518s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (107.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 11 feed throughs used by 11 nets
PHY-1001 : End commit to database; 0.124396s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 517, reserve = 476, peak = 517.
PHY-1001 : End phase 3; 0.486158s wall, 0.500000s user + 0.000000s system = 0.500000s CPU (102.8%)

PHY-1003 : Routed, final wirelength = 152536
PHY-1001 : Current memory(MB): used = 518, reserve = 477, peak = 518.
PHY-1001 : End export database. 0.009023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  5.671686s wall, 5.781250s user + 0.140625s system = 5.921875s CPU (104.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4773, tnet num: 1146, tinst num: 519, tnode num: 6047, tedge num: 8204.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  6.157178s wall, 6.250000s user + 0.171875s system = 6.421875s CPU (104.3%)

RUN-1004 : used memory is 476 MB, reserved memory is 436 MB, peak memory is 518 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      741   out of  19600    3.78%
#reg                      517   out of  19600    2.64%
#le                       909
  #lut only               392   out of    909   43.12%
  #reg only               168   out of    909   18.48%
  #lut&reg                349   out of    909   38.39%
#dsp                        0   out of     29    0.00%
#bram                      22   out of     64   34.38%
  #bram9k                  22
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     3
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    236
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         92
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       NONE    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+---------------------------------------------------------------------------------------------------------+
|Instance                            |Module         |le     |lut     |ripple  |seq     |bram    |dsp     |
+---------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B     |909    |582     |159     |522     |22      |0       |
|  CLK120                            |global_clock   |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData       |87     |69      |8       |61      |0       |0       |
|    usms                            |Time_1ms       |28     |13      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER     |176    |122     |21      |129     |0       |0       |
|  wendu                             |DS18B20        |173    |129     |43      |43      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER |471    |260     |87      |284     |0       |0       |
|    wrapper_cwc_top                 |cwc_top        |471    |260     |87      |284     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int    |166    |71      |0       |164     |0       |0       |
|        reg_inst                    |register       |163    |68      |0       |161     |0       |0       |
|        tap_inst                    |tap            |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger        |305    |189     |87      |120     |0       |0       |
|        bus_inst                    |bus_top        |75     |49      |26      |26      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det        |75     |49      |26      |26      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl       |132    |84      |29      |72      |0       |0       |
+---------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout    Nets  
    #1         1       565   
    #2         2       260   
    #3         3       129   
    #4         4        39   
    #5        5-10      99   
    #6       11-50      35   
    #7       51-100     2    
  Average     2.90           

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 4773, tnet num: 1146, tinst num: 519, tnode num: 6047, tedge num: 8204.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1146 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 057bf4ae9dc0d18f7126c20ce7aec8c31cdd0d41110c6b742e6e5620711a0ef6 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 519
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1148, pip num: 11431
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 11
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1088 valid insts, and 31469 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101000001001010101111010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.953123s wall, 13.343750s user + 0.093750s system = 13.437500s CPU (455.0%)

RUN-1004 : used memory is 491 MB, reserved memory is 452 MB, peak memory is 639 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_102523.log"
