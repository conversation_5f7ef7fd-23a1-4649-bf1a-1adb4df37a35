//////////////////////////////////////////////////////////////////////////////////
// IIR数字滤波器模块（无限冲激响应滤波器）
// 功能：更高效的滤波，用更少的资源实现更好的滤波效果
// 原理：不仅使用输入历史，还使用输出历史，形成反馈
//////////////////////////////////////////////////////////////////////////////////

module DigitalFilter_IIR
#(
    parameter DATA_WIDTH = 12,      // 输入数据位宽
    parameter COEFF_WIDTH = 16,     // 系数位宽
    parameter OUTPUT_WIDTH = 16,    // 输出位宽
    parameter INTERNAL_WIDTH = 32   // 内部计算位宽
)
(
    input                           clk,
    input                           rst_n,
    input                           data_valid,
    input   [DATA_WIDTH-1:0]        data_in,
    output reg                      data_ready,
    output reg [OUTPUT_WIDTH-1:0]   data_out
);

// IIR滤波器系数
// a系数：输入系数（前馈）
// b系数：输出系数（反馈）
reg signed [COEFF_WIDTH-1:0] a0, a1, a2;  // 输入系数
reg signed [COEFF_WIDTH-1:0] b1, b2;      // 反馈系数

// 历史数据存储
reg signed [DATA_WIDTH-1:0] x1, x2;       // 输入历史：x[n-1], x[n-2]
reg signed [INTERNAL_WIDTH-1:0] y1, y2;   // 输出历史：y[n-1], y[n-2]

// 中间计算结果
reg signed [INTERNAL_WIDTH-1:0] temp_sum;
reg signed [INTERNAL_WIDTH-1:0] current_output;

// 初始化滤波器系数（二阶低通滤波器）
initial begin
    // 这是一个巴特沃斯低通滤波器的系数
    // 截止频率约为采样频率的1/10
    a0 = 16'h0341;  // 0.0067 * 2^15
    a1 = 16'h0682;  // 0.0134 * 2^15  
    a2 = 16'h0341;  // 0.0067 * 2^15
    b1 = 16'h7A83;  // 1.9428 * 2^14 (注意：这里用14位是因为系数接近2)
    b2 = 16'hE0C6;  // -0.9761 * 2^15
end

// 数据移位和滤波计算
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        // 复位时清零所有历史数据
        x1 <= 0;
        x2 <= 0;
        y1 <= 0;
        y2 <= 0;
        current_output <= 0;
        data_out <= 0;
        data_ready <= 0;
    end
    else if (data_valid) begin
        // 第一步：计算新的输出
        // y[n] = a0*x[n] + a1*x[n-1] + a2*x[n-2] + b1*y[n-1] + b2*y[n-2]
        temp_sum <= (a0 * $signed(data_in)) + 
                    (a1 * x1) + 
                    (a2 * x2) + 
                    (b1 * y1 >>> 1) +  // 右移1位因为b1系数较大
                    (b2 * y2);
        
        // 第二步：更新历史数据
        x2 <= x1;           // x[n-2] = x[n-1]
        x1 <= data_in;      // x[n-1] = x[n]
        y2 <= y1;           // y[n-2] = y[n-1]
        y1 <= temp_sum;     // y[n-1] = y[n]
        
        // 第三步：输出结果（取高位）
        current_output <= temp_sum;
        data_out <= temp_sum[INTERNAL_WIDTH-1:INTERNAL_WIDTH-OUTPUT_WIDTH];
        data_ready <= 1'b1;
    end
    else begin
        data_ready <= 1'b0;
    end
end

endmodule
