//////////////////////////////////////////////////////////////////////////////////
// 信号质量监测模块
// 功能：实时监测信号质量，包括信噪比、幅度稳定性、频率特性等
// 原理：通过统计分析方法评估信号质量，提供预警和诊断信息
//////////////////////////////////////////////////////////////////////////////////

module SignalQualityMonitor
#(
    parameter DATA_WIDTH = 12,      // 输入数据位宽
    parameter WINDOW_SIZE = 1024,   // 统计窗口大小
    parameter THRESHOLD_WIDTH = 16  // 阈值位宽
)
(
    input                           clk,
    input                           rst_n,
    input                           data_valid,
    input   [DATA_WIDTH-1:0]        data_in,        // 输入信号
    
    // 质量指标输出
    output reg [15:0]               snr_estimate,   // 信噪比估计
    output reg [15:0]               signal_amplitude, // 信号幅度
    output reg [15:0]               noise_level,    // 噪声水平
    output reg [15:0]               stability_index, // 稳定性指数
    
    // 状态指示
    output reg                      signal_good,    // 信号质量良好
    output reg                      signal_warning, // 信号质量警告
    output reg                      signal_bad,     // 信号质量差
    
    // 配置参数
    input   [THRESHOLD_WIDTH-1:0]   snr_threshold_good,    // SNR良好阈值
    input   [THRESHOLD_WIDTH-1:0]   snr_threshold_warning, // SNR警告阈值
    input   [THRESHOLD_WIDTH-1:0]   amplitude_threshold    // 幅度阈值
);

// 统计计算相关寄存器
reg [DATA_WIDTH-1:0] data_buffer [0:WINDOW_SIZE-1];  // 数据缓存
reg [15:0] buffer_index;                             // 缓存索引
reg [31:0] sum_data;                                 // 数据和
reg [31:0] sum_square;                               // 数据平方和
reg [15:0] mean_value;                               // 平均值
reg [31:0] variance;                                 // 方差
reg [15:0] std_deviation;                            // 标准差

// 幅度检测相关
reg [DATA_WIDTH-1:0] max_value;                      // 最大值
reg [DATA_WIDTH-1:0] min_value;                      // 最小值
reg [15:0] peak_to_peak;                             // 峰峰值

// 稳定性检测相关
reg [15:0] stability_counter;                        // 稳定性计数器
reg [DATA_WIDTH-1:0] previous_data;                  // 前一个数据
reg [15:0] change_accumulator;                       // 变化累加器

// 状态机
reg [2:0] monitor_state;
localparam COLLECT = 3'b000;
localparam CALCULATE = 3'b001;
localparam EVALUATE = 3'b010;
localparam UPDATE = 3'b011;

integer i;

// 数据收集和缓存
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        buffer_index <= 0;
        sum_data <= 0;
        sum_square <= 0;
        max_value <= 0;
        min_value <= {DATA_WIDTH{1'b1}};  // 初始化为最大值
        monitor_state <= COLLECT;
        
        // 清空数据缓存
        for (i = 0; i < WINDOW_SIZE; i = i + 1) begin
            data_buffer[i] <= 0;
        end
    end
    else if (data_valid) begin
        case (monitor_state)
            COLLECT: begin
                // 存储新数据
                data_buffer[buffer_index] <= data_in;
                
                // 更新统计量
                sum_data <= sum_data + data_in;
                sum_square <= sum_square + (data_in * data_in);
                
                // 更新最大最小值
                if (data_in > max_value) max_value <= data_in;
                if (data_in < min_value) min_value <= data_in;
                
                // 稳定性检测
                if (buffer_index > 0) begin
                    change_accumulator <= change_accumulator + 
                        ((data_in > previous_data) ? (data_in - previous_data) : (previous_data - data_in));
                end
                previous_data <= data_in;
                
                // 更新索引
                buffer_index <= buffer_index + 1;
                
                // 收集完一个窗口的数据后进入计算阶段
                if (buffer_index >= WINDOW_SIZE - 1) begin
                    monitor_state <= CALCULATE;
                    buffer_index <= 0;
                end
            end
            
            CALCULATE: begin
                // 计算平均值
                mean_value <= sum_data >> 10;  // 除以1024
                
                // 计算峰峰值
                peak_to_peak <= max_value - min_value;
                
                // 计算稳定性指数（变化越小越稳定）
                stability_index <= 16'hFFFF - (change_accumulator >> 6);
                
                monitor_state <= EVALUATE;
            end
            
            EVALUATE: begin
                // 计算方差和标准差（简化计算）
                variance <= (sum_square >> 10) - (mean_value * mean_value);
                std_deviation <= variance[15:0];  // 简化的标准差
                
                // 估计信号幅度（使用峰峰值）
                signal_amplitude <= peak_to_peak;
                
                // 估计噪声水平（使用标准差）
                noise_level <= std_deviation;
                
                // 估计信噪比（信号幅度/噪声水平）
                if (std_deviation > 0) begin
                    snr_estimate <= (peak_to_peak << 8) / std_deviation;
                end else begin
                    snr_estimate <= 16'hFFFF;  // 无噪声情况
                end
                
                monitor_state <= UPDATE;
            end
            
            UPDATE: begin
                // 重置统计量，准备下一轮
                sum_data <= 0;
                sum_square <= 0;
                max_value <= 0;
                min_value <= {DATA_WIDTH{1'b1}};
                change_accumulator <= 0;
                
                monitor_state <= COLLECT;
            end
            
            default: monitor_state <= COLLECT;
        endcase
    end
end

// 信号质量评估和状态输出
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        signal_good <= 0;
        signal_warning <= 0;
        signal_bad <= 0;
    end
    else if (monitor_state == UPDATE) begin
        // 根据SNR和幅度判断信号质量
        if ((snr_estimate >= snr_threshold_good) && 
            (signal_amplitude >= amplitude_threshold) &&
            (stability_index >= 16'hC000)) begin  // 稳定性良好
            signal_good <= 1;
            signal_warning <= 0;
            signal_bad <= 0;
        end
        else if ((snr_estimate >= snr_threshold_warning) && 
                 (signal_amplitude >= (amplitude_threshold >> 1))) begin
            signal_good <= 0;
            signal_warning <= 1;
            signal_bad <= 0;
        end
        else begin
            signal_good <= 0;
            signal_warning <= 0;
            signal_bad <= 1;
        end
    end
end

endmodule
