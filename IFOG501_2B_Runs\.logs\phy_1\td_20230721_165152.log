============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 16:51:52 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1649 instances
RUN-0007 : 382 luts, 992 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2219 nets
RUN-1001 : 1660 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1647 instances, 382 luts, 992 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7851, tnet num: 2217, tinst num: 1647, tnode num: 11089, tedge num: 13252.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.325097s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 544252
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1647.
PHY-3001 : End clustering;  0.000022s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 491269, overlap = 18
PHY-3002 : Step(2): len = 412544, overlap = 20.25
PHY-3002 : Step(3): len = 342520, overlap = 13.5
PHY-3002 : Step(4): len = 332535, overlap = 15.75
PHY-3002 : Step(5): len = 324398, overlap = 15.75
PHY-3002 : Step(6): len = 317474, overlap = 18
PHY-3002 : Step(7): len = 312686, overlap = 20.25
PHY-3002 : Step(8): len = 303520, overlap = 20.25
PHY-3002 : Step(9): len = 294766, overlap = 20.25
PHY-3002 : Step(10): len = 290312, overlap = 20.25
PHY-3002 : Step(11): len = 281985, overlap = 20.25
PHY-3002 : Step(12): len = 274892, overlap = 20.25
PHY-3002 : Step(13): len = 270417, overlap = 20.25
PHY-3002 : Step(14): len = 263405, overlap = 20.25
PHY-3002 : Step(15): len = 258440, overlap = 20.25
PHY-3002 : Step(16): len = 253558, overlap = 20.25
PHY-3002 : Step(17): len = 247165, overlap = 20.25
PHY-3002 : Step(18): len = 241861, overlap = 20.25
PHY-3002 : Step(19): len = 237803, overlap = 20.25
PHY-3002 : Step(20): len = 229259, overlap = 20.25
PHY-3002 : Step(21): len = 224492, overlap = 20.25
PHY-3002 : Step(22): len = 221592, overlap = 20.25
PHY-3002 : Step(23): len = 207982, overlap = 20.25
PHY-3002 : Step(24): len = 200019, overlap = 20.25
PHY-3002 : Step(25): len = 198381, overlap = 20.25
PHY-3002 : Step(26): len = 181050, overlap = 18
PHY-3002 : Step(27): len = 159438, overlap = 20.25
PHY-3002 : Step(28): len = 156957, overlap = 20.25
PHY-3002 : Step(29): len = 154320, overlap = 20.25
PHY-3002 : Step(30): len = 142479, overlap = 18
PHY-3002 : Step(31): len = 139768, overlap = 20.25
PHY-3002 : Step(32): len = 137799, overlap = 20.25
PHY-3002 : Step(33): len = 134005, overlap = 20.25
PHY-3002 : Step(34): len = 130322, overlap = 20.5
PHY-3002 : Step(35): len = 128382, overlap = 20.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113947
PHY-3002 : Step(36): len = 128962, overlap = 13.75
PHY-3002 : Step(37): len = 127983, overlap = 9.25
PHY-3002 : Step(38): len = 125240, overlap = 16
PHY-3002 : Step(39): len = 123126, overlap = 11.5
PHY-3002 : Step(40): len = 121297, overlap = 2.5
PHY-3002 : Step(41): len = 117338, overlap = 4.5
PHY-3002 : Step(42): len = 115778, overlap = 4.5
PHY-3002 : Step(43): len = 112910, overlap = 11.25
PHY-3002 : Step(44): len = 110376, overlap = 6.75
PHY-3002 : Step(45): len = 107920, overlap = 6.75
PHY-3002 : Step(46): len = 106079, overlap = 11.25
PHY-3002 : Step(47): len = 103523, overlap = 9
PHY-3002 : Step(48): len = 102674, overlap = 6.75
PHY-3002 : Step(49): len = 101017, overlap = 6.75
PHY-3002 : Step(50): len = 99803.6, overlap = 6.75
PHY-3002 : Step(51): len = 96346.7, overlap = 11.25
PHY-3002 : Step(52): len = 95444.2, overlap = 11.25
PHY-3002 : Step(53): len = 94474.2, overlap = 6.75
PHY-3002 : Step(54): len = 92649.1, overlap = 9
PHY-3002 : Step(55): len = 88395.8, overlap = 6.75
PHY-3002 : Step(56): len = 87102.3, overlap = 6.75
PHY-3002 : Step(57): len = 85873.8, overlap = 6.75
PHY-3002 : Step(58): len = 84160, overlap = 11.25
PHY-3002 : Step(59): len = 84101.7, overlap = 11.25
PHY-3002 : Step(60): len = 83515.1, overlap = 6.75
PHY-3002 : Step(61): len = 82046.3, overlap = 6.75
PHY-3002 : Step(62): len = 76699, overlap = 11.25
PHY-3002 : Step(63): len = 76100.4, overlap = 11.25
PHY-3002 : Step(64): len = 75360.4, overlap = 9
PHY-3002 : Step(65): len = 75286.9, overlap = 6.75
PHY-3002 : Step(66): len = 74864.3, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000227894
PHY-3002 : Step(67): len = 74487.7, overlap = 6.75
PHY-3002 : Step(68): len = 74205.2, overlap = 6.75
PHY-3002 : Step(69): len = 74172.7, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000455787
PHY-3002 : Step(70): len = 74346.5, overlap = 6.75
PHY-3002 : Step(71): len = 74400.7, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006605s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (236.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070348s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(72): len = 77288.8, overlap = 4.4375
PHY-3002 : Step(73): len = 75730.4, overlap = 5.40625
PHY-3002 : Step(74): len = 74347.6, overlap = 5.03125
PHY-3002 : Step(75): len = 70358.8, overlap = 4.625
PHY-3002 : Step(76): len = 68427.8, overlap = 4.8125
PHY-3002 : Step(77): len = 66770.1, overlap = 3
PHY-3002 : Step(78): len = 65566.4, overlap = 3.8125
PHY-3002 : Step(79): len = 64114.9, overlap = 3.5
PHY-3002 : Step(80): len = 61691.4, overlap = 7.6875
PHY-3002 : Step(81): len = 58950.7, overlap = 6.8125
PHY-3002 : Step(82): len = 57962.1, overlap = 5.375
PHY-3002 : Step(83): len = 57030.3, overlap = 5.25
PHY-3002 : Step(84): len = 56829.8, overlap = 4.4375
PHY-3002 : Step(85): len = 55511.3, overlap = 5.1875
PHY-3002 : Step(86): len = 55167.5, overlap = 5.5625
PHY-3002 : Step(87): len = 54397, overlap = 5
PHY-3002 : Step(88): len = 53675.9, overlap = 3.4375
PHY-3002 : Step(89): len = 52859, overlap = 7
PHY-3002 : Step(90): len = 52288.3, overlap = 7.1875
PHY-3002 : Step(91): len = 50915.6, overlap = 6.875
PHY-3002 : Step(92): len = 50095.4, overlap = 7.6875
PHY-3002 : Step(93): len = 49785.8, overlap = 7.9375
PHY-3002 : Step(94): len = 49374.2, overlap = 7.375
PHY-3002 : Step(95): len = 49108.1, overlap = 6.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000749629
PHY-3002 : Step(96): len = 48858.5, overlap = 6.75
PHY-3002 : Step(97): len = 48734.3, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00149926
PHY-3002 : Step(98): len = 48707.2, overlap = 6.75
PHY-3002 : Step(99): len = 48819.7, overlap = 6.8125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.075309s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (103.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.53365e-05
PHY-3002 : Step(100): len = 48861.7, overlap = 49.4688
PHY-3002 : Step(101): len = 49421.2, overlap = 52.5938
PHY-3002 : Step(102): len = 50108.6, overlap = 51.9688
PHY-3002 : Step(103): len = 50334.3, overlap = 50.9062
PHY-3002 : Step(104): len = 50522, overlap = 51
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000170673
PHY-3002 : Step(105): len = 50371.3, overlap = 50.375
PHY-3002 : Step(106): len = 50429.9, overlap = 49.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000341346
PHY-3002 : Step(107): len = 50556.4, overlap = 49.5
PHY-3002 : Step(108): len = 52279.6, overlap = 42.4062
PHY-3002 : Step(109): len = 53272.7, overlap = 38.7812
PHY-3002 : Step(110): len = 53117, overlap = 38.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000682692
PHY-3002 : Step(111): len = 53021.6, overlap = 38.6562
PHY-3002 : Step(112): len = 52978.3, overlap = 37.9688
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00122603
PHY-3002 : Step(113): len = 53703.8, overlap = 37.5625
PHY-3002 : Step(114): len = 53998.4, overlap = 37.5
PHY-3002 : Step(115): len = 54378.4, overlap = 36.5625
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00245206
PHY-3002 : Step(116): len = 54218.4, overlap = 36.3125
PHY-3002 : Step(117): len = 54227.5, overlap = 36.0312
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00490412
PHY-3002 : Step(118): len = 54528.5, overlap = 36.2812
PHY-3002 : Step(119): len = 54528.5, overlap = 36.2812
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.00980825
PHY-3002 : Step(120): len = 54679.4, overlap = 36
PHY-3002 : Step(121): len = 54679.4, overlap = 36
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.0165486
PHY-3002 : Step(122): len = 54769.7, overlap = 36.1562
PHY-3002 : Step(123): len = 54769.7, overlap = 36.1562
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.0330971
PHY-3002 : Step(124): len = 54833, overlap = 35.4688
PHY-3002 : Step(125): len = 54833, overlap = 35.4688
PHY-3001 : :::10::: Try harder cell spreading with beta_ = 0.0661943
PHY-3002 : Step(126): len = 54884.8, overlap = 35.4688
PHY-3002 : Step(127): len = 54884.8, overlap = 35.4688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7851, tnet num: 2217, tinst num: 1647, tnode num: 11089, tedge num: 13252.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 98.66 peak overflow 2.91
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2219.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57736, over cnt = 246(0%), over = 982, worst = 20
PHY-1001 : End global iterations;  0.066257s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (117.9%)

PHY-1001 : Congestion index: top1 = 44.14, top5 = 25.31, top10 = 16.61, top15 = 11.81.
PHY-1001 : End incremental global routing;  0.124852s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (112.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077543s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (100.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.234962s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (106.4%)

OPT-1001 : Current memory(MB): used = 214, reserve = 179, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1681/2219.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57736, over cnt = 246(0%), over = 982, worst = 20
PHY-1002 : len = 64984, over cnt = 159(0%), over = 339, worst = 9
PHY-1002 : len = 66680, over cnt = 85(0%), over = 139, worst = 9
PHY-1002 : len = 68184, over cnt = 30(0%), over = 36, worst = 4
PHY-1002 : len = 68840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109538s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (128.4%)

PHY-1001 : Congestion index: top1 = 38.51, top5 = 25.56, top10 = 18.44, top15 = 13.50.
OPT-1001 : End congestion update;  0.155826s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (120.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2217 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067770s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.226888s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (110.2%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : End physical optimization;  0.765080s wall, 0.890625s user + 0.031250s system = 0.921875s CPU (120.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 382 LUT to BLE ...
SYN-4008 : Packed 382 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 112 SEQ with LUT/SLICE
SYN-4006 : 106 single LUT's are left
SYN-4006 : 691 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1073/1406 primitive instances ...
PHY-3001 : End packing;  0.055456s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 845 instances
RUN-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2044 nets
RUN-1001 : 1495 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 843 instances, 796 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 55029.4, Over = 63
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6606, tnet num: 2042, tinst num: 843, tnode num: 8943, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.339760s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.51016e-05
PHY-3002 : Step(128): len = 53963.3, overlap = 62.25
PHY-3002 : Step(129): len = 53188.2, overlap = 61.75
PHY-3002 : Step(130): len = 52581.4, overlap = 60.5
PHY-3002 : Step(131): len = 52695.8, overlap = 65.25
PHY-3002 : Step(132): len = 52490.6, overlap = 65.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.02033e-05
PHY-3002 : Step(133): len = 52979.1, overlap = 63.25
PHY-3002 : Step(134): len = 53775.7, overlap = 61.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000140407
PHY-3002 : Step(135): len = 54020.7, overlap = 58.25
PHY-3002 : Step(136): len = 55607.7, overlap = 54.5
PHY-3002 : Step(137): len = 56231.2, overlap = 51
PHY-3002 : Step(138): len = 56586.4, overlap = 51.5
PHY-3002 : Step(139): len = 56438.2, overlap = 52
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.107638s wall, 0.062500s user + 0.156250s system = 0.218750s CPU (203.2%)

PHY-3001 : Trial Legalized: Len = 69467.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070827s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (88.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000563521
PHY-3002 : Step(140): len = 67165.8, overlap = 3
PHY-3002 : Step(141): len = 64242.1, overlap = 15
PHY-3002 : Step(142): len = 62104.4, overlap = 17.75
PHY-3002 : Step(143): len = 60998.8, overlap = 22
PHY-3002 : Step(144): len = 60506.5, overlap = 21.75
PHY-3002 : Step(145): len = 60193.6, overlap = 25
PHY-3002 : Step(146): len = 59897.9, overlap = 26.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00112704
PHY-3002 : Step(147): len = 60239.2, overlap = 26.5
PHY-3002 : Step(148): len = 60430.4, overlap = 26.5
PHY-3002 : Step(149): len = 60455, overlap = 26.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00225408
PHY-3002 : Step(150): len = 60560.3, overlap = 26.5
PHY-3002 : Step(151): len = 60619.7, overlap = 26.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005528s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64469.7, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007154s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 2, deltaY = 5, maxDist = 1.
PHY-3001 : Final: Len = 64545.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6606, tnet num: 2042, tinst num: 843, tnode num: 8943, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 134/2044.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70632, over cnt = 145(0%), over = 211, worst = 6
PHY-1002 : len = 71480, over cnt = 82(0%), over = 96, worst = 3
PHY-1002 : len = 72520, over cnt = 5(0%), over = 6, worst = 2
PHY-1002 : len = 72600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.122854s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (127.2%)

PHY-1001 : Congestion index: top1 = 30.41, top5 = 22.50, top10 = 18.03, top15 = 14.23.
PHY-1001 : End incremental global routing;  0.180895s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077968s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.294164s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (111.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1803/2044.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007317s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.41, top5 = 22.50, top10 = 18.03, top15 = 14.23.
OPT-1001 : End congestion update;  0.058556s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (80.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.058860s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 805 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 843 instances, 796 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64587.4, Over = 0
PHY-3001 : End spreading;  0.005831s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (268.0%)

PHY-3001 : Final: Len = 64587.4, Over = 0
PHY-3001 : End incremental legalization;  0.040573s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (77.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 3 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.171985s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (90.9%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055546s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1791/2044.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.012299s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.45, top5 = 22.52, top10 = 18.02, top15 = 14.23.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059796s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.972669s wall, 0.968750s user + 0.031250s system = 1.000000s CPU (102.8%)

RUN-1003 : finish command "place" in  6.181734s wall, 9.468750s user + 3.250000s system = 12.718750s CPU (205.7%)

RUN-1004 : used memory is 206 MB, reserved memory is 171 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 845 instances
RUN-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2044 nets
RUN-1001 : 1495 nets have 2 pins
RUN-1001 : 431 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6606, tnet num: 2042, tinst num: 843, tnode num: 8943, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69704, over cnt = 149(0%), over = 216, worst = 6
PHY-1002 : len = 70560, over cnt = 87(0%), over = 105, worst = 3
PHY-1002 : len = 71664, over cnt = 10(0%), over = 12, worst = 2
PHY-1002 : len = 71840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131986s wall, 0.187500s user + 0.062500s system = 0.250000s CPU (189.4%)

PHY-1001 : Congestion index: top1 = 30.95, top5 = 22.37, top10 = 17.85, top15 = 14.08.
PHY-1001 : End global routing;  0.188641s wall, 0.250000s user + 0.062500s system = 0.312500s CPU (165.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 244, reserve = 210, peak = 251.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 504, reserve = 473, peak = 504.
PHY-1001 : End build detailed router design. 3.432549s wall, 3.328125s user + 0.093750s system = 3.421875s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32432, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.487324s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 537, reserve = 507, peak = 538.
PHY-1001 : End phase 1; 1.494330s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (100.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181472, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 538, reserve = 508, peak = 538.
PHY-1001 : End initial routed; 1.220142s wall, 1.812500s user + 0.109375s system = 1.921875s CPU (157.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1808(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.537   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.414762s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.7%)

PHY-1001 : Current memory(MB): used = 542, reserve = 511, peak = 542.
PHY-1001 : End phase 2; 1.635007s wall, 2.234375s user + 0.109375s system = 2.343750s CPU (143.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181472, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017612s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (88.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181336, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032424s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (96.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181464, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027040s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (57.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1808(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.537   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.403311s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (96.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.202717s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 554, reserve = 523, peak = 554.
PHY-1001 : End phase 3; 0.818848s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (99.2%)

PHY-1003 : Routed, final wirelength = 181464
PHY-1001 : Current memory(MB): used = 554, reserve = 524, peak = 554.
PHY-1001 : End export database. 0.011213s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (139.3%)

PHY-1001 : End detail routing;  7.582024s wall, 8.062500s user + 0.218750s system = 8.281250s CPU (109.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6606, tnet num: 2042, tinst num: 843, tnode num: 8943, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.561505s wall, 9.109375s user + 0.281250s system = 9.390625s CPU (109.7%)

RUN-1004 : used memory is 525 MB, reserved memory is 494 MB, peak memory is 555 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      839   out of  19600    4.28%
#reg                     1074   out of  19600    5.48%
#le                      1530
  #lut only               456   out of   1530   29.80%
  #reg only               691   out of   1530   45.16%
  #lut&reg                383   out of   1530   25.03%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         472
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1530   |613     |226     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1131   |315     |133     |918     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |20      |6       |21      |0       |0       |
|    demodu                  |Demodulation                                     |537    |131     |58      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |13      |0       |25      |0       |0       |
|    integ                   |Integration                                      |139    |17      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |90     |29      |21      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |309    |93      |29      |250     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |111    |96      |7       |53      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |17     |15      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |59     |53      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |205    |160     |45      |78      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1459  
    #2          2       310   
    #3          3       107   
    #4          4        14   
    #5        5-10       81   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6606, tnet num: 2042, tinst num: 843, tnode num: 8943, tedge num: 11598.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2042 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 843
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2044, pip num: 14766
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1296 valid insts, and 39306 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.620016s wall, 19.750000s user + 0.078125s system = 19.828125s CPU (547.7%)

RUN-1004 : used memory is 546 MB, reserved memory is 515 MB, peak memory is 673 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_165152.log"
