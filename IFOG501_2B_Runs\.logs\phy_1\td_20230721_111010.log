============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 11:10:10 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1605 instances
RUN-0007 : 366 luts, 972 seqs, 143 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2162 nets
RUN-1001 : 1631 nets have 2 pins
RUN-1001 : 420 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1603 instances, 366 luts, 972 seqs, 218 slices, 26 macros(218 instances: 143 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7661, tnet num: 2160, tinst num: 1603, tnode num: 10851, tedge num: 12927.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2160 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.309298s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 636051
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1603.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 510522, overlap = 20.25
PHY-3002 : Step(2): len = 464654, overlap = 20.25
PHY-3002 : Step(3): len = 423788, overlap = 20.25
PHY-3002 : Step(4): len = 395701, overlap = 15.75
PHY-3002 : Step(5): len = 383443, overlap = 15.75
PHY-3002 : Step(6): len = 372160, overlap = 15.75
PHY-3002 : Step(7): len = 360454, overlap = 18
PHY-3002 : Step(8): len = 344118, overlap = 18
PHY-3002 : Step(9): len = 334517, overlap = 18
PHY-3002 : Step(10): len = 324217, overlap = 18
PHY-3002 : Step(11): len = 314089, overlap = 18
PHY-3002 : Step(12): len = 303318, overlap = 18
PHY-3002 : Step(13): len = 294826, overlap = 18
PHY-3002 : Step(14): len = 286606, overlap = 18
PHY-3002 : Step(15): len = 279288, overlap = 18
PHY-3002 : Step(16): len = 270205, overlap = 18
PHY-3002 : Step(17): len = 264449, overlap = 18
PHY-3002 : Step(18): len = 257469, overlap = 18
PHY-3002 : Step(19): len = 251409, overlap = 18
PHY-3002 : Step(20): len = 244706, overlap = 18
PHY-3002 : Step(21): len = 238921, overlap = 18
PHY-3002 : Step(22): len = 233200, overlap = 18
PHY-3002 : Step(23): len = 228082, overlap = 20.25
PHY-3002 : Step(24): len = 223062, overlap = 20.25
PHY-3002 : Step(25): len = 217845, overlap = 20.25
PHY-3002 : Step(26): len = 212754, overlap = 20.25
PHY-3002 : Step(27): len = 208628, overlap = 20.25
PHY-3002 : Step(28): len = 204577, overlap = 20.25
PHY-3002 : Step(29): len = 200628, overlap = 20.25
PHY-3002 : Step(30): len = 194726, overlap = 20.25
PHY-3002 : Step(31): len = 190058, overlap = 20.25
PHY-3002 : Step(32): len = 187499, overlap = 20.25
PHY-3002 : Step(33): len = 181940, overlap = 20.25
PHY-3002 : Step(34): len = 172091, overlap = 20.25
PHY-3002 : Step(35): len = 168933, overlap = 20.25
PHY-3002 : Step(36): len = 166529, overlap = 20.25
PHY-3002 : Step(37): len = 154217, overlap = 20.25
PHY-3002 : Step(38): len = 141676, overlap = 20.25
PHY-3002 : Step(39): len = 139940, overlap = 20.25
PHY-3002 : Step(40): len = 134138, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00012014
PHY-3002 : Step(41): len = 135734, overlap = 15.75
PHY-3002 : Step(42): len = 135045, overlap = 13.5
PHY-3002 : Step(43): len = 132004, overlap = 15.75
PHY-3002 : Step(44): len = 129032, overlap = 13.5
PHY-3002 : Step(45): len = 126695, overlap = 15.75
PHY-3002 : Step(46): len = 125287, overlap = 9
PHY-3002 : Step(47): len = 123129, overlap = 11.25
PHY-3002 : Step(48): len = 121033, overlap = 11.25
PHY-3002 : Step(49): len = 114557, overlap = 13.5
PHY-3002 : Step(50): len = 111294, overlap = 13.5
PHY-3002 : Step(51): len = 110218, overlap = 9
PHY-3002 : Step(52): len = 108272, overlap = 13.5
PHY-3002 : Step(53): len = 107155, overlap = 15.75
PHY-3002 : Step(54): len = 104582, overlap = 9
PHY-3002 : Step(55): len = 103817, overlap = 9
PHY-3002 : Step(56): len = 101929, overlap = 9
PHY-3002 : Step(57): len = 99158.9, overlap = 13.5
PHY-3002 : Step(58): len = 97237.9, overlap = 15.75
PHY-3002 : Step(59): len = 95919.1, overlap = 15.75
PHY-3002 : Step(60): len = 93558.1, overlap = 13.5
PHY-3002 : Step(61): len = 93194, overlap = 13.5
PHY-3002 : Step(62): len = 91783.7, overlap = 15.75
PHY-3002 : Step(63): len = 87731.6, overlap = 13.5
PHY-3002 : Step(64): len = 83655.6, overlap = 11.25
PHY-3002 : Step(65): len = 83413.3, overlap = 15.75
PHY-3002 : Step(66): len = 82321.4, overlap = 15.75
PHY-3002 : Step(67): len = 81870.3, overlap = 11.3125
PHY-3002 : Step(68): len = 81543.5, overlap = 9.125
PHY-3002 : Step(69): len = 80520.5, overlap = 11.375
PHY-3002 : Step(70): len = 79095.8, overlap = 13.5625
PHY-3002 : Step(71): len = 78342.7, overlap = 13.8125
PHY-3002 : Step(72): len = 76857.7, overlap = 12
PHY-3002 : Step(73): len = 75831.2, overlap = 12
PHY-3002 : Step(74): len = 71853.4, overlap = 16
PHY-3002 : Step(75): len = 71082, overlap = 15.75
PHY-3002 : Step(76): len = 69892.2, overlap = 11.25
PHY-3002 : Step(77): len = 68665, overlap = 9
PHY-3002 : Step(78): len = 67241.3, overlap = 13.5
PHY-3002 : Step(79): len = 65129.1, overlap = 13.75
PHY-3002 : Step(80): len = 64857, overlap = 13.75
PHY-3002 : Step(81): len = 64025.9, overlap = 13.75
PHY-3002 : Step(82): len = 64016.4, overlap = 11.4375
PHY-3002 : Step(83): len = 63805.2, overlap = 9.1875
PHY-3002 : Step(84): len = 63107.5, overlap = 9.1875
PHY-3002 : Step(85): len = 62215.8, overlap = 13.6875
PHY-3002 : Step(86): len = 60659.6, overlap = 11.375
PHY-3002 : Step(87): len = 60278.7, overlap = 13.625
PHY-3002 : Step(88): len = 58823.1, overlap = 11.5
PHY-3002 : Step(89): len = 57523.2, overlap = 9.1875
PHY-3002 : Step(90): len = 56757.4, overlap = 6.75
PHY-3002 : Step(91): len = 56395.6, overlap = 9
PHY-3002 : Step(92): len = 55342.1, overlap = 9
PHY-3002 : Step(93): len = 54966.3, overlap = 9
PHY-3002 : Step(94): len = 54685, overlap = 9
PHY-3002 : Step(95): len = 54254.7, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00024028
PHY-3002 : Step(96): len = 54290.6, overlap = 4.5
PHY-3002 : Step(97): len = 54122, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000480559
PHY-3002 : Step(98): len = 54197.6, overlap = 4.5
PHY-3002 : Step(99): len = 54231.5, overlap = 2.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008185s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (190.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2160 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.080095s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(100): len = 55118.4, overlap = 7.59375
PHY-3002 : Step(101): len = 54187.5, overlap = 7.28125
PHY-3002 : Step(102): len = 53834.9, overlap = 8.28125
PHY-3002 : Step(103): len = 53087.2, overlap = 8.21875
PHY-3002 : Step(104): len = 52481.3, overlap = 8.8125
PHY-3002 : Step(105): len = 51549.2, overlap = 8.46875
PHY-3002 : Step(106): len = 50592.8, overlap = 6.28125
PHY-3002 : Step(107): len = 49770.2, overlap = 6.34375
PHY-3002 : Step(108): len = 48676.3, overlap = 7.0625
PHY-3002 : Step(109): len = 47571.1, overlap = 8.15625
PHY-3002 : Step(110): len = 46901.2, overlap = 8.65625
PHY-3002 : Step(111): len = 46472.5, overlap = 9.03125
PHY-3002 : Step(112): len = 45950.2, overlap = 10.3438
PHY-3002 : Step(113): len = 45160.8, overlap = 10.1562
PHY-3002 : Step(114): len = 44829.1, overlap = 10.7188
PHY-3002 : Step(115): len = 44558.7, overlap = 10.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000320149
PHY-3002 : Step(116): len = 44342.7, overlap = 11.0938
PHY-3002 : Step(117): len = 44191.9, overlap = 11.3125
PHY-3002 : Step(118): len = 44103.3, overlap = 12
PHY-3002 : Step(119): len = 44137.4, overlap = 13
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000640298
PHY-3002 : Step(120): len = 44163.1, overlap = 14.1562
PHY-3002 : Step(121): len = 44288.2, overlap = 14.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2160 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.074870s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.92067e-05
PHY-3002 : Step(122): len = 44250, overlap = 62.5938
PHY-3002 : Step(123): len = 45035.6, overlap = 60.375
PHY-3002 : Step(124): len = 45554.2, overlap = 57.7812
PHY-3002 : Step(125): len = 45969.8, overlap = 57.9375
PHY-3002 : Step(126): len = 45883.3, overlap = 57.75
PHY-3002 : Step(127): len = 45927.2, overlap = 56.6562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.84134e-05
PHY-3002 : Step(128): len = 45956.3, overlap = 56.1562
PHY-3002 : Step(129): len = 46263, overlap = 54.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000196827
PHY-3002 : Step(130): len = 46366.6, overlap = 52.5312
PHY-3002 : Step(131): len = 48403.9, overlap = 41.0625
PHY-3002 : Step(132): len = 48799.7, overlap = 40.5938
PHY-3002 : Step(133): len = 48522.8, overlap = 40.4062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7661, tnet num: 2160, tinst num: 1603, tnode num: 10851, tedge num: 12927.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 90.53 peak overflow 4.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2162.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51880, over cnt = 248(0%), over = 1132, worst = 24
PHY-1001 : End global iterations;  0.087728s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (106.9%)

PHY-1001 : Congestion index: top1 = 46.06, top5 = 26.41, top10 = 16.35, top15 = 11.46.
PHY-1001 : End incremental global routing;  0.141414s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (99.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2160 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072373s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.4%)

OPT-1001 : 2 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1563 has valid locations, 6 needs to be replaced
PHY-3001 : design contains 1607 instances, 367 luts, 975 seqs, 218 slices, 26 macros(218 instances: 143 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 48647.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7676, tnet num: 2164, tinst num: 1607, tnode num: 10875, tedge num: 12949.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2164 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.336714s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (102.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(134): len = 48663.7, overlap = 5.21875
PHY-3002 : Step(135): len = 48656.4, overlap = 5.21875
PHY-3002 : Step(136): len = 48655.6, overlap = 5.28125
PHY-3002 : Step(137): len = 48657.1, overlap = 5.21875
PHY-3002 : Step(138): len = 48657.1, overlap = 5.21875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2164 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068146s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000268461
PHY-3002 : Step(139): len = 48669.2, overlap = 40.4062
PHY-3002 : Step(140): len = 48704.1, overlap = 40.4062
PHY-3001 : Final: Len = 48704.1, Over = 40.4062
PHY-3001 : End incremental placement;  0.504161s wall, 0.593750s user + 0.125000s system = 0.718750s CPU (142.6%)

OPT-1001 : Total overflow 90.53 peak overflow 4.00
OPT-1001 : End high-fanout net optimization;  0.756977s wall, 0.796875s user + 0.156250s system = 0.953125s CPU (125.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1703/2166.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52152, over cnt = 248(0%), over = 1121, worst = 24
PHY-1002 : len = 58808, over cnt = 215(0%), over = 550, worst = 15
PHY-1002 : len = 65256, over cnt = 43(0%), over = 47, worst = 3
PHY-1002 : len = 65984, over cnt = 22(0%), over = 22, worst = 1
PHY-1002 : len = 66840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.119131s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (118.0%)

PHY-1001 : Congestion index: top1 = 39.91, top5 = 26.93, top10 = 18.98, top15 = 13.70.
OPT-1001 : End congestion update;  0.169106s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (120.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2164 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063859s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.235941s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (112.6%)

OPT-1001 : Current memory(MB): used = 218, reserve = 183, peak = 220.
OPT-1001 : End physical optimization;  1.320511s wall, 1.390625s user + 0.171875s system = 1.562500s CPU (118.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 793 remaining SEQ's ...
SYN-4005 : Packed 105 SEQ with LUT/SLICE
SYN-4006 : 106 single LUT's are left
SYN-4006 : 688 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1055/1380 primitive instances ...
PHY-3001 : End packing;  0.054955s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 822 instances
RUN-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1999 nets
RUN-1001 : 1473 nets have 2 pins
RUN-1001 : 413 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 820 instances, 773 slices, 26 macros(218 instances: 143 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 48103, Over = 67.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6464, tnet num: 1997, tinst num: 820, tnode num: 8788, tedge num: 11331.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.330219s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.32255e-05
PHY-3002 : Step(141): len = 47673.1, overlap = 68
PHY-3002 : Step(142): len = 47525.3, overlap = 67.75
PHY-3002 : Step(143): len = 47126.4, overlap = 70.25
PHY-3002 : Step(144): len = 47092.6, overlap = 72.75
PHY-3002 : Step(145): len = 47240, overlap = 71.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.64511e-05
PHY-3002 : Step(146): len = 47302, overlap = 71.75
PHY-3002 : Step(147): len = 47455.7, overlap = 71.25
PHY-3002 : Step(148): len = 47925.9, overlap = 68
PHY-3002 : Step(149): len = 48736.2, overlap = 64.25
PHY-3002 : Step(150): len = 49304.7, overlap = 59.5
PHY-3002 : Step(151): len = 49619.2, overlap = 58.75
PHY-3002 : Step(152): len = 49914.8, overlap = 61.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.29022e-05
PHY-3002 : Step(153): len = 50284, overlap = 59.75
PHY-3002 : Step(154): len = 50645.7, overlap = 59
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.093744s wall, 0.093750s user + 0.187500s system = 0.281250s CPU (300.0%)

PHY-3001 : Trial Legalized: Len = 63817.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059066s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000441901
PHY-3002 : Step(155): len = 61607.5, overlap = 4.5
PHY-3002 : Step(156): len = 59467.7, overlap = 12.5
PHY-3002 : Step(157): len = 58266, overlap = 16.75
PHY-3002 : Step(158): len = 57506.1, overlap = 19
PHY-3002 : Step(159): len = 56805.5, overlap = 20.25
PHY-3002 : Step(160): len = 56459, overlap = 19.75
PHY-3002 : Step(161): len = 56217.1, overlap = 21
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000883802
PHY-3002 : Step(162): len = 56644, overlap = 19.5
PHY-3002 : Step(163): len = 56835.2, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0017676
PHY-3002 : Step(164): len = 57084.7, overlap = 18.75
PHY-3002 : Step(165): len = 57251.3, overlap = 17.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006281s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62337.9, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006806s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 2, deltaY = 12, maxDist = 2.
PHY-3001 : Final: Len = 62471.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6464, tnet num: 1997, tinst num: 820, tnode num: 8788, tedge num: 11331.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 80/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68824, over cnt = 145(0%), over = 247, worst = 7
PHY-1002 : len = 69576, over cnt = 93(0%), over = 139, worst = 4
PHY-1002 : len = 71128, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 71176, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 71288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.145865s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (128.5%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 23.38, top10 = 18.35, top15 = 14.46.
PHY-1001 : End incremental global routing;  0.204967s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (122.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.065806s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.303266s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (113.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 186, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1792/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007119s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (219.5%)

PHY-1001 : Congestion index: top1 = 32.28, top5 = 23.38, top10 = 18.35, top15 = 14.46.
OPT-1001 : End congestion update;  0.057414s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055836s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 782 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 820 instances, 773 slices, 26 macros(218 instances: 143 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62504, Over = 0
PHY-3001 : End spreading;  0.005661s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62504, Over = 0
PHY-3001 : End incremental legalization;  0.037922s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (123.6%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.165412s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (113.4%)

OPT-1001 : Current memory(MB): used = 224, reserve = 190, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056459s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1781/1999.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71320, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009673s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (161.5%)

PHY-1001 : Congestion index: top1 = 32.39, top5 = 23.39, top10 = 18.35, top15 = 14.45.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055476s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.962251s wall, 1.000000s user + 0.031250s system = 1.031250s CPU (107.2%)

RUN-1003 : finish command "place" in  6.770790s wall, 10.593750s user + 3.718750s system = 14.312500s CPU (211.4%)

RUN-1004 : used memory is 207 MB, reserved memory is 171 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 822 instances
RUN-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1999 nets
RUN-1001 : 1473 nets have 2 pins
RUN-1001 : 413 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6464, tnet num: 1997, tinst num: 820, tnode num: 8788, tedge num: 11331.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 387 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68200, over cnt = 146(0%), over = 250, worst = 7
PHY-1002 : len = 69016, over cnt = 102(0%), over = 148, worst = 4
PHY-1002 : len = 70704, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131294s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (119.0%)

PHY-1001 : Congestion index: top1 = 32.05, top5 = 23.17, top10 = 18.21, top15 = 14.33.
PHY-1001 : End global routing;  0.185573s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (109.5%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 236, reserve = 201, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : Current memory(MB): used = 502, reserve = 469, peak = 502.
PHY-1001 : End build detailed router design. 3.639452s wall, 3.578125s user + 0.046875s system = 3.625000s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33616, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.449019s wall, 1.437500s user + 0.015625s system = 1.453125s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 532, reserve = 502, peak = 532.
PHY-1001 : End phase 1; 1.455235s wall, 1.453125s user + 0.015625s system = 1.468750s CPU (100.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179544, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 502, peak = 534.
PHY-1001 : End initial routed; 1.148688s wall, 2.328125s user + 0.031250s system = 2.359375s CPU (205.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1771(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.154   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.408347s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End phase 2; 1.557132s wall, 2.734375s user + 0.031250s system = 2.765625s CPU (177.6%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 179544, over cnt = 27(0%), over = 27, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018638s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (83.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179448, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.028797s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (108.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179440, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.026654s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (58.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1771(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.154   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.404681s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (96.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 8 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.197128s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.0%)

PHY-1001 : Current memory(MB): used = 548, reserve = 516, peak = 548.
PHY-1001 : End phase 3; 0.805961s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (95.0%)

PHY-1003 : Routed, final wirelength = 179440
PHY-1001 : Current memory(MB): used = 548, reserve = 517, peak = 548.
PHY-1001 : End export database. 0.012027s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (129.9%)

PHY-1001 : End detail routing;  7.667770s wall, 8.750000s user + 0.093750s system = 8.843750s CPU (115.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6464, tnet num: 1997, tinst num: 820, tnode num: 8788, tedge num: 11331.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.655296s wall, 9.734375s user + 0.109375s system = 9.843750s CPU (113.7%)

RUN-1004 : used memory is 524 MB, reserved memory is 496 MB, peak memory is 548 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      806   out of  19600    4.11%
#reg                     1051   out of  19600    5.36%
#le                      1494
  #lut only               443   out of   1494   29.65%
  #reg only               688   out of   1494   46.05%
  #lut&reg                363   out of   1494   24.30%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         466
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    44
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1494   |588     |218     |1082    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1112   |302     |128     |899     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |33     |21      |9       |24      |0       |0       |
|    demodu                  |Demodulation                                     |528    |120     |57      |432     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |48     |0       |0       |48      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |15      |0       |28      |0       |0       |
|    integ                   |Integration                                      |139    |14      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |63     |21      |14      |58      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |99      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |32     |27      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |97     |84      |7       |52      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |18     |18      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |42     |38      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |76      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1437  
    #2          2       281   
    #3          3       115   
    #4          4        17   
    #5        5-10       76   
    #6        11-50      29   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6464, tnet num: 1997, tinst num: 820, tnode num: 8788, tedge num: 11331.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1997 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 820
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1999, pip num: 14565
BIT-1002 : Init feedthrough completely, num: 8
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1257 valid insts, and 38606 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.756344s wall, 18.812500s user + 0.046875s system = 18.859375s CPU (502.1%)

RUN-1004 : used memory is 547 MB, reserved memory is 515 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_111010.log"
