============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 14:21:02 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1628 instances
RUN-0007 : 369 luts, 985 seqs, 150 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2198 nets
RUN-1001 : 1643 nets have 2 pins
RUN-1001 : 440 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1626 instances, 369 luts, 985 seqs, 225 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7794, tnet num: 2196, tinst num: 1626, tnode num: 11034, tedge num: 13180.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.294810s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (100.7%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 578166
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1626.
PHY-3001 : End clustering;  0.000021s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 551723, overlap = 20.25
PHY-3002 : Step(2): len = 441471, overlap = 13.5
PHY-3002 : Step(3): len = 366958, overlap = 15.75
PHY-3002 : Step(4): len = 348710, overlap = 15.75
PHY-3002 : Step(5): len = 339778, overlap = 15.75
PHY-3002 : Step(6): len = 329629, overlap = 18
PHY-3002 : Step(7): len = 322198, overlap = 20.25
PHY-3002 : Step(8): len = 315212, overlap = 20.25
PHY-3002 : Step(9): len = 308789, overlap = 20.25
PHY-3002 : Step(10): len = 301356, overlap = 20.25
PHY-3002 : Step(11): len = 295086, overlap = 20.25
PHY-3002 : Step(12): len = 289709, overlap = 20.25
PHY-3002 : Step(13): len = 282746, overlap = 20.25
PHY-3002 : Step(14): len = 276619, overlap = 20.25
PHY-3002 : Step(15): len = 271997, overlap = 20.25
PHY-3002 : Step(16): len = 265203, overlap = 20.25
PHY-3002 : Step(17): len = 258849, overlap = 20.25
PHY-3002 : Step(18): len = 254186, overlap = 20.25
PHY-3002 : Step(19): len = 249161, overlap = 20.25
PHY-3002 : Step(20): len = 243494, overlap = 20.25
PHY-3002 : Step(21): len = 238508, overlap = 20.25
PHY-3002 : Step(22): len = 233532, overlap = 20.25
PHY-3002 : Step(23): len = 227361, overlap = 20.25
PHY-3002 : Step(24): len = 221979, overlap = 20.25
PHY-3002 : Step(25): len = 218741, overlap = 20.25
PHY-3002 : Step(26): len = 212344, overlap = 20.25
PHY-3002 : Step(27): len = 204227, overlap = 20.25
PHY-3002 : Step(28): len = 200475, overlap = 20.25
PHY-3002 : Step(29): len = 197531, overlap = 20.25
PHY-3002 : Step(30): len = 183273, overlap = 20.25
PHY-3002 : Step(31): len = 173889, overlap = 20.25
PHY-3002 : Step(32): len = 172215, overlap = 20.25
PHY-3002 : Step(33): len = 160861, overlap = 20.25
PHY-3002 : Step(34): len = 114475, overlap = 15.75
PHY-3002 : Step(35): len = 111016, overlap = 18
PHY-3002 : Step(36): len = 109612, overlap = 18
PHY-3002 : Step(37): len = 107367, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.88123e-05
PHY-3002 : Step(38): len = 108791, overlap = 13.5
PHY-3002 : Step(39): len = 107869, overlap = 9
PHY-3002 : Step(40): len = 105560, overlap = 13.5
PHY-3002 : Step(41): len = 104036, overlap = 15.75
PHY-3002 : Step(42): len = 101519, overlap = 15.75
PHY-3002 : Step(43): len = 99486.8, overlap = 9
PHY-3002 : Step(44): len = 97265.9, overlap = 9
PHY-3002 : Step(45): len = 95838.1, overlap = 11.25
PHY-3002 : Step(46): len = 91781.6, overlap = 13.5
PHY-3002 : Step(47): len = 90857, overlap = 11.25
PHY-3002 : Step(48): len = 89322, overlap = 11.25
PHY-3002 : Step(49): len = 88439.2, overlap = 11.25
PHY-3002 : Step(50): len = 87289.6, overlap = 9
PHY-3002 : Step(51): len = 85009.2, overlap = 4.5
PHY-3002 : Step(52): len = 84610.7, overlap = 9
PHY-3002 : Step(53): len = 83281.1, overlap = 9
PHY-3002 : Step(54): len = 82447.1, overlap = 9
PHY-3002 : Step(55): len = 78093.2, overlap = 11.25
PHY-3002 : Step(56): len = 76642.1, overlap = 11.25
PHY-3002 : Step(57): len = 75455.8, overlap = 13.5
PHY-3002 : Step(58): len = 74629.7, overlap = 11.25
PHY-3002 : Step(59): len = 74033, overlap = 9
PHY-3002 : Step(60): len = 73443, overlap = 9
PHY-3002 : Step(61): len = 72016.2, overlap = 9
PHY-3002 : Step(62): len = 70736.5, overlap = 11.25
PHY-3002 : Step(63): len = 69387.8, overlap = 9
PHY-3002 : Step(64): len = 68423.9, overlap = 9
PHY-3002 : Step(65): len = 67208.6, overlap = 9
PHY-3002 : Step(66): len = 66114.2, overlap = 13.5
PHY-3002 : Step(67): len = 65530.8, overlap = 11.25
PHY-3002 : Step(68): len = 64612.9, overlap = 11.25
PHY-3002 : Step(69): len = 64010.4, overlap = 9
PHY-3002 : Step(70): len = 63168.1, overlap = 9
PHY-3002 : Step(71): len = 62593.5, overlap = 9
PHY-3002 : Step(72): len = 61161.6, overlap = 11.25
PHY-3002 : Step(73): len = 59826.2, overlap = 9
PHY-3002 : Step(74): len = 59287.1, overlap = 6.75
PHY-3002 : Step(75): len = 59259.6, overlap = 6.75
PHY-3002 : Step(76): len = 58657.9, overlap = 9
PHY-3002 : Step(77): len = 58397.4, overlap = 9
PHY-3002 : Step(78): len = 58322.7, overlap = 9
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000177625
PHY-3002 : Step(79): len = 58605.9, overlap = 9
PHY-3002 : Step(80): len = 58699.6, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000355249
PHY-3002 : Step(81): len = 58660.9, overlap = 6.75
PHY-3002 : Step(82): len = 58694.2, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006809s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (229.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060483s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(83): len = 61640.1, overlap = 6.1875
PHY-3002 : Step(84): len = 60677.1, overlap = 5.46875
PHY-3002 : Step(85): len = 60356, overlap = 4.84375
PHY-3002 : Step(86): len = 59460.7, overlap = 4.21875
PHY-3002 : Step(87): len = 58598.6, overlap = 4.40625
PHY-3002 : Step(88): len = 57449.2, overlap = 3.96875
PHY-3002 : Step(89): len = 56490.4, overlap = 4.09375
PHY-3002 : Step(90): len = 55812.5, overlap = 4.09375
PHY-3002 : Step(91): len = 54465.1, overlap = 3.65625
PHY-3002 : Step(92): len = 53265, overlap = 3.21875
PHY-3002 : Step(93): len = 52102.6, overlap = 3.5625
PHY-3002 : Step(94): len = 51697.6, overlap = 3.3125
PHY-3002 : Step(95): len = 51071.5, overlap = 3.125
PHY-3002 : Step(96): len = 50458.8, overlap = 3.4375
PHY-3002 : Step(97): len = 49814.9, overlap = 3.1875
PHY-3002 : Step(98): len = 49628.6, overlap = 3.25
PHY-3002 : Step(99): len = 48915, overlap = 4.875
PHY-3002 : Step(100): len = 47736.9, overlap = 5.0625
PHY-3002 : Step(101): len = 46997.1, overlap = 9.9375
PHY-3002 : Step(102): len = 46162.5, overlap = 11.9062
PHY-3002 : Step(103): len = 45179.2, overlap = 11.9062
PHY-3002 : Step(104): len = 44684.8, overlap = 14.0938
PHY-3002 : Step(105): len = 44282.8, overlap = 13.2812
PHY-3002 : Step(106): len = 44006.1, overlap = 12.0938
PHY-3002 : Step(107): len = 43830.2, overlap = 12.375
PHY-3002 : Step(108): len = 43653.8, overlap = 13.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000118802
PHY-3002 : Step(109): len = 43391.6, overlap = 16.25
PHY-3002 : Step(110): len = 43378.2, overlap = 16.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000237605
PHY-3002 : Step(111): len = 43402.2, overlap = 16.2812
PHY-3002 : Step(112): len = 43435.9, overlap = 16.3125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067399s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.35491e-05
PHY-3002 : Step(113): len = 43393, overlap = 68.875
PHY-3002 : Step(114): len = 43994.4, overlap = 68.6875
PHY-3002 : Step(115): len = 44300.8, overlap = 68.9375
PHY-3002 : Step(116): len = 44240.7, overlap = 68.0625
PHY-3002 : Step(117): len = 44362.1, overlap = 67.625
PHY-3002 : Step(118): len = 44527.1, overlap = 68.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.70982e-05
PHY-3002 : Step(119): len = 44601.6, overlap = 66.875
PHY-3002 : Step(120): len = 44843.9, overlap = 65.5
PHY-3002 : Step(121): len = 45129.4, overlap = 64.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000134196
PHY-3002 : Step(122): len = 45833.7, overlap = 64.4688
PHY-3002 : Step(123): len = 46502, overlap = 60.6875
PHY-3002 : Step(124): len = 48269.2, overlap = 54.3125
PHY-3002 : Step(125): len = 48145.3, overlap = 52.2188
PHY-3002 : Step(126): len = 47950.1, overlap = 52.375
PHY-3002 : Step(127): len = 47965.1, overlap = 51.7812
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000268393
PHY-3002 : Step(128): len = 48309.3, overlap = 52.0625
PHY-3002 : Step(129): len = 49101.8, overlap = 49.6562
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000536786
PHY-3002 : Step(130): len = 49477.5, overlap = 44.3125
PHY-3002 : Step(131): len = 50549.6, overlap = 41.6875
PHY-3002 : Step(132): len = 52164.8, overlap = 33.3125
PHY-3002 : Step(133): len = 52526, overlap = 33.0938
PHY-3002 : Step(134): len = 52242.4, overlap = 32.0312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7794, tnet num: 2196, tinst num: 1626, tnode num: 11034, tedge num: 13180.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.38 peak overflow 2.38
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55824, over cnt = 250(0%), over = 950, worst = 16
PHY-1001 : End global iterations;  0.084641s wall, 0.062500s user + 0.046875s system = 0.109375s CPU (129.2%)

PHY-1001 : Congestion index: top1 = 39.59, top5 = 24.79, top10 = 16.61, top15 = 11.93.
PHY-1001 : End incremental global routing;  0.141621s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (121.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.085967s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (90.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.264063s wall, 0.250000s user + 0.046875s system = 0.296875s CPU (112.4%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1711/2198.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55824, over cnt = 250(0%), over = 950, worst = 16
PHY-1002 : len = 62640, over cnt = 141(0%), over = 265, worst = 10
PHY-1002 : len = 65024, over cnt = 34(0%), over = 51, worst = 8
PHY-1002 : len = 65416, over cnt = 2(0%), over = 3, worst = 2
PHY-1002 : len = 65528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.140391s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (133.6%)

PHY-1001 : Congestion index: top1 = 35.32, top5 = 24.54, top10 = 17.99, top15 = 13.35.
OPT-1001 : End congestion update;  0.184945s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (126.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2196 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067981s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.9%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.255644s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (116.1%)

OPT-1001 : Current memory(MB): used = 217, reserve = 182, peak = 217.
OPT-1001 : End physical optimization;  0.839583s wall, 0.937500s user + 0.046875s system = 0.984375s CPU (117.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 110 SEQ with LUT/SLICE
SYN-4006 : 97 single LUT's are left
SYN-4006 : 693 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1062/1394 primitive instances ...
PHY-3001 : End packing;  0.058404s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-1001 : 1486 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52423.8, Over = 58.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6572, tnet num: 2028, tinst num: 841, tnode num: 8924, tedge num: 11548.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.397499s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (98.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.61003e-05
PHY-3002 : Step(135): len = 51386.8, overlap = 58.5
PHY-3002 : Step(136): len = 50671.8, overlap = 61.75
PHY-3002 : Step(137): len = 50371.7, overlap = 62.75
PHY-3002 : Step(138): len = 50009.9, overlap = 61.75
PHY-3002 : Step(139): len = 49822.3, overlap = 61.5
PHY-3002 : Step(140): len = 49754.4, overlap = 61
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.22006e-05
PHY-3002 : Step(141): len = 50167.9, overlap = 61
PHY-3002 : Step(142): len = 50367.9, overlap = 60.75
PHY-3002 : Step(143): len = 50447.4, overlap = 60.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.20017e-05
PHY-3002 : Step(144): len = 51394, overlap = 58
PHY-3002 : Step(145): len = 52194.8, overlap = 55
PHY-3002 : Step(146): len = 52522, overlap = 50.75
PHY-3002 : Step(147): len = 53224.7, overlap = 47.75
PHY-3002 : Step(148): len = 53946.8, overlap = 46.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.169266s wall, 0.031250s user + 0.078125s system = 0.109375s CPU (64.6%)

PHY-3001 : Trial Legalized: Len = 66973.2
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052247s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000471407
PHY-3002 : Step(149): len = 64247.6, overlap = 5.25
PHY-3002 : Step(150): len = 62217.3, overlap = 11.5
PHY-3002 : Step(151): len = 60585, overlap = 18
PHY-3002 : Step(152): len = 59303.2, overlap = 23.25
PHY-3002 : Step(153): len = 58654.1, overlap = 26.75
PHY-3002 : Step(154): len = 58074.2, overlap = 29
PHY-3002 : Step(155): len = 57727.3, overlap = 29.75
PHY-3002 : Step(156): len = 57514.4, overlap = 30
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000942814
PHY-3002 : Step(157): len = 58075, overlap = 30
PHY-3002 : Step(158): len = 58280.8, overlap = 29.25
PHY-3002 : Step(159): len = 58160.9, overlap = 29.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00188563
PHY-3002 : Step(160): len = 58417.9, overlap = 29.25
PHY-3002 : Step(161): len = 58628.8, overlap = 27.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005163s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63371.2, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005627s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 13 instances has been re-located, deltaX = 0, deltaY = 13, maxDist = 1.
PHY-3001 : Final: Len = 63497.2, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6572, tnet num: 2028, tinst num: 841, tnode num: 8924, tedge num: 11548.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 110/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70264, over cnt = 130(0%), over = 208, worst = 6
PHY-1002 : len = 71008, over cnt = 74(0%), over = 100, worst = 3
PHY-1002 : len = 71768, over cnt = 33(0%), over = 44, worst = 3
PHY-1002 : len = 72224, over cnt = 12(0%), over = 14, worst = 2
PHY-1002 : len = 72384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.201828s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.6%)

PHY-1001 : Congestion index: top1 = 32.07, top5 = 23.18, top10 = 18.33, top15 = 14.53.
PHY-1001 : End incremental global routing;  0.274342s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (102.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080330s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.384479s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.6%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1802/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008111s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.07, top5 = 23.18, top10 = 18.33, top15 = 14.53.
OPT-1001 : End congestion update;  0.060265s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050093s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 803 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 841 instances, 794 slices, 26 macros(225 instances: 150 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63567.8, Over = 0
PHY-3001 : End spreading;  0.007943s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63567.8, Over = 0
PHY-3001 : End incremental legalization;  0.041323s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (151.2%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 2 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.171395s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (109.4%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062331s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.3%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1794/2030.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72472, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.017886s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (87.4%)

PHY-1001 : Congestion index: top1 = 32.07, top5 = 23.15, top10 = 18.32, top15 = 14.54.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056570s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.586207
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  1.107869s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (100.1%)

RUN-1003 : finish command "place" in  6.073750s wall, 8.750000s user + 3.218750s system = 11.968750s CPU (197.1%)

RUN-1004 : used memory is 203 MB, reserved memory is 168 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 843 instances
RUN-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2030 nets
RUN-1001 : 1486 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 77 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6572, tnet num: 2028, tinst num: 841, tnode num: 8924, tedge num: 11548.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 397 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69064, over cnt = 142(0%), over = 234, worst = 7
PHY-1002 : len = 70328, over cnt = 73(0%), over = 93, worst = 3
PHY-1002 : len = 71272, over cnt = 25(0%), over = 29, worst = 2
PHY-1002 : len = 71656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.152115s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (154.1%)

PHY-1001 : Congestion index: top1 = 31.98, top5 = 22.95, top10 = 18.15, top15 = 14.38.
PHY-1001 : End global routing;  0.208983s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (142.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 201, peak = 247.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 496, reserve = 465, peak = 496.
PHY-1001 : End build detailed router design. 3.638904s wall, 3.593750s user + 0.046875s system = 3.640625s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34472, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.530447s wall, 1.531250s user + 0.015625s system = 1.546875s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 530, reserve = 499, peak = 530.
PHY-1001 : End phase 1; 1.536318s wall, 1.531250s user + 0.015625s system = 1.546875s CPU (100.7%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 179848, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 500, peak = 531.
PHY-1001 : End initial routed; 1.188084s wall, 1.796875s user + 0.125000s system = 1.921875s CPU (161.8%)

PHY-1001 : Update timing.....
PHY-1001 : 3/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.116   |  -0.116   |   1   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.428508s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (102.1%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End phase 2; 1.616684s wall, 2.234375s user + 0.125000s system = 2.359375s CPU (145.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS 0.007ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.011401s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (137.0%)

PHY-1022 : len = 179864, over cnt = 17(0%), over = 17, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.038606s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (80.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 179776, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026017s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (120.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 179792, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019171s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (244.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.007   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.407685s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (99.6%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 0 feed throughs used by 0 nets
PHY-1001 : End commit to database; 0.202253s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (100.4%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End phase 3; 0.839630s wall, 0.875000s user + 0.000000s system = 0.875000s CPU (104.2%)

PHY-1003 : Routed, final wirelength = 179792
PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End export database. 0.012170s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (128.4%)

PHY-1001 : End detail routing;  7.843392s wall, 8.421875s user + 0.187500s system = 8.609375s CPU (109.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6572, tnet num: 2028, tinst num: 841, tnode num: 8924, tedge num: 11548.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.872733s wall, 9.437500s user + 0.234375s system = 9.671875s CPU (109.0%)

RUN-1004 : used memory is 501 MB, reserved memory is 470 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      823   out of  19600    4.20%
#reg                     1074   out of  19600    5.48%
#le                      1516
  #lut only               442   out of   1516   29.16%
  #reg only               693   out of   1516   45.71%
  #lut&reg                381   out of   1516   25.13%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         477
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1516   |598     |225     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1126   |310     |132     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |37     |31      |6       |22      |0       |0       |
|    demodu                  |Demodulation                                     |530    |114     |57      |430     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |12      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |14      |0       |29      |0       |0       |
|    integ                   |Integration                                      |136    |26      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |90     |28      |21      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |97      |29      |257     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |99     |83      |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |21     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |43     |37      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1450  
    #2          2       303   
    #3          3       110   
    #4          4        15   
    #5        5-10       80   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6572, tnet num: 2028, tinst num: 841, tnode num: 8924, tedge num: 11548.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2028 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 841
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2030, pip num: 14614
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1288 valid insts, and 38802 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.280769s wall, 18.296875s user + 0.046875s system = 18.343750s CPU (559.1%)

RUN-1004 : used memory is 520 MB, reserved memory is 493 MB, peak memory is 665 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_142102.log"
