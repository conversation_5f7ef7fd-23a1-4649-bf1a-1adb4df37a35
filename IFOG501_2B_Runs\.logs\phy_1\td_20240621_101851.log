============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 10:18:51 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net SPIM/sclk_posedge_n will be merged to another kept net SPIM/sclk_posedge
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4026 : Tagged 2 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 394 instances
RUN-0007 : 176 luts, 136 seqs, 43 mslices, 27 lslices, 8 pads, 0 brams, 0 dsps
RUN-1001 : There are total 484 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 288 nets have 2 pins
RUN-1001 : 126 nets have [3 - 5] pins
RUN-1001 : 51 nets have [6 - 10] pins
RUN-1001 : 9 nets have [11 - 20] pins
RUN-1001 : 2 nets have [21 - 99] pins
RUN-1001 : 2 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     65      
RUN-1001 :   No   |  No   |  Yes  |      0      
RUN-1001 :   No   |  Yes  |  No   |     46      
RUN-1001 :   Yes  |  No   |  No   |     25      
RUN-1001 :   Yes  |  No   |  Yes  |      0      
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    1    |   2   |     4      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 5
PHY-3001 : Initial placement ...
PHY-3001 : design contains 392 instances, 176 luts, 136 seqs, 70 slices, 12 macros(70 instances: 43 mslices 27 lslices)
PHY-0007 : Cell area utilization is 1%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 1674, tnet num: 482, tinst num: 392, tnode num: 2031, tedge num: 2685.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 482 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057086s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 129795
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 392.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 1%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 102778, overlap = 0
PHY-3002 : Step(2): len = 85404.4, overlap = 0
PHY-3002 : Step(3): len = 73394.2, overlap = 0
PHY-3002 : Step(4): len = 62628.9, overlap = 0
PHY-3002 : Step(5): len = 50629.9, overlap = 0
PHY-3002 : Step(6): len = 42182.5, overlap = 0
PHY-3002 : Step(7): len = 35533.7, overlap = 0
PHY-3002 : Step(8): len = 30434.7, overlap = 0
PHY-3002 : Step(9): len = 24337.9, overlap = 0
PHY-3002 : Step(10): len = 20850.2, overlap = 0
PHY-3002 : Step(11): len = 19101.6, overlap = 0
PHY-3002 : Step(12): len = 16926.9, overlap = 0
PHY-3002 : Step(13): len = 15294.3, overlap = 0
PHY-3002 : Step(14): len = 13864, overlap = 0
PHY-3002 : Step(15): len = 13128.4, overlap = 0
PHY-3002 : Step(16): len = 12601.9, overlap = 0.1875
PHY-3002 : Step(17): len = 12519, overlap = 0
PHY-3002 : Step(18): len = 11960.5, overlap = 0
PHY-3002 : Step(19): len = 11960.5, overlap = 0
PHY-3002 : Step(20): len = 11618.7, overlap = 0.0625
PHY-3002 : Step(21): len = 11519, overlap = 0.0625
PHY-3002 : Step(22): len = 11310.3, overlap = 0.0625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.003989s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 2%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 482 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.008784s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (177.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(23): len = 10552.2, overlap = 4.625
PHY-3002 : Step(24): len = 10467, overlap = 4.09375
PHY-3002 : Step(25): len = 9685.7, overlap = 4.03125
PHY-3002 : Step(26): len = 9265.1, overlap = 4.03125
PHY-3002 : Step(27): len = 9092.2, overlap = 3.9375
PHY-3002 : Step(28): len = 8963.1, overlap = 3.5
PHY-3002 : Step(29): len = 8800, overlap = 3.25
PHY-3002 : Step(30): len = 8770.9, overlap = 3.125
PHY-3002 : Step(31): len = 8751.9, overlap = 2.5625
PHY-3002 : Step(32): len = 8735.2, overlap = 1.6875
PHY-3002 : Step(33): len = 8724.7, overlap = 1.4375
PHY-3002 : Step(34): len = 8741.7, overlap = 1
PHY-3002 : Step(35): len = 8811.1, overlap = 0.625
PHY-3002 : Step(36): len = 8851.2, overlap = 0.3125
PHY-3002 : Step(37): len = 8814.5, overlap = 0
PHY-3002 : Step(38): len = 8787.8, overlap = 0
PHY-3002 : Step(39): len = 8705.6, overlap = 2.625
PHY-3002 : Step(40): len = 8650.6, overlap = 6.375
PHY-3002 : Step(41): len = 8641.7, overlap = 9.875
PHY-3002 : Step(42): len = 8559.4, overlap = 11
PHY-3002 : Step(43): len = 8497.8, overlap = 11.2188
PHY-3002 : Step(44): len = 8469.6, overlap = 11.2188
PHY-3002 : Step(45): len = 8367.8, overlap = 11.2188
PHY-3002 : Step(46): len = 8364.3, overlap = 11.0938
PHY-3002 : Step(47): len = 8276.6, overlap = 10.5625
PHY-3002 : Step(48): len = 8298.5, overlap = 9.125
PHY-3002 : Step(49): len = 8189.9, overlap = 8.5
PHY-3002 : Step(50): len = 8189.2, overlap = 8.15625
PHY-3002 : Step(51): len = 8154, overlap = 7.1875
PHY-3002 : Step(52): len = 8134.9, overlap = 6.09375
PHY-3002 : Step(53): len = 8281.2, overlap = 4.3125
PHY-3002 : Step(54): len = 8103.8, overlap = 3.90625
PHY-3002 : Step(55): len = 8083.7, overlap = 0.8125
PHY-3002 : Step(56): len = 8018.8, overlap = 0.5625
PHY-3002 : Step(57): len = 7996.2, overlap = 0
PHY-3002 : Step(58): len = 7989.4, overlap = 0
PHY-3002 : Step(59): len = 7950.8, overlap = 0
PHY-3002 : Step(60): len = 7953.2, overlap = 2.40625
PHY-3002 : Step(61): len = 8002.3, overlap = 4.21875
PHY-3002 : Step(62): len = 7915.5, overlap = 4.75
PHY-3002 : Step(63): len = 7905.8, overlap = 5.28125
PHY-3002 : Step(64): len = 7854.1, overlap = 5.75
PHY-3002 : Step(65): len = 7820.5, overlap = 6.03125
PHY-3002 : Step(66): len = 7862.9, overlap = 6.03125
PHY-3002 : Step(67): len = 7824.1, overlap = 6.03125
PHY-3002 : Step(68): len = 7757.7, overlap = 6.03125
PHY-3002 : Step(69): len = 7700.7, overlap = 6.03125
PHY-3002 : Step(70): len = 7627, overlap = 4.09375
PHY-3002 : Step(71): len = 7637.4, overlap = 3.0625
PHY-3002 : Step(72): len = 7680.6, overlap = 0.9375
PHY-3002 : Step(73): len = 7526.5, overlap = 0
PHY-3002 : Step(74): len = 7490.7, overlap = 0
PHY-3002 : Step(75): len = 7498.1, overlap = 9.1875
PHY-3002 : Step(76): len = 7618.1, overlap = 9.46875
PHY-3002 : Step(77): len = 7440, overlap = 9.34375
PHY-3002 : Step(78): len = 7302.6, overlap = 8.375
PHY-3002 : Step(79): len = 7244.4, overlap = 6.9375
PHY-3002 : Step(80): len = 7250, overlap = 6.5
PHY-3002 : Step(81): len = 7248.5, overlap = 5.5625
PHY-3002 : Step(82): len = 7311.7, overlap = 3.15625
PHY-3002 : Step(83): len = 7228.5, overlap = 2.65625
PHY-3002 : Step(84): len = 7103.8, overlap = 2.6875
PHY-3002 : Step(85): len = 7270.5, overlap = 0.625
PHY-3002 : Step(86): len = 7241.2, overlap = 3.6875
PHY-3002 : Step(87): len = 7112.1, overlap = 3.96875
PHY-3002 : Step(88): len = 7183, overlap = 3.96875
PHY-3002 : Step(89): len = 6982.3, overlap = 3.96875
PHY-3002 : Step(90): len = 7018.1, overlap = 3.96875
PHY-3002 : Step(91): len = 6946.5, overlap = 3.96875
PHY-3002 : Step(92): len = 6930, overlap = 3.96875
PHY-3002 : Step(93): len = 6946.6, overlap = 3.4375
PHY-3002 : Step(94): len = 6956.2, overlap = 2.78125
PHY-3002 : Step(95): len = 6918.4, overlap = 2.5625
PHY-3002 : Step(96): len = 6914.6, overlap = 1.84375
PHY-3002 : Step(97): len = 6901.2, overlap = 1.5625
PHY-3002 : Step(98): len = 6817.5, overlap = 1.0625
PHY-3002 : Step(99): len = 6810.6, overlap = 0.6875
PHY-3002 : Step(100): len = 6863.5, overlap = 0
PHY-3002 : Step(101): len = 6760.3, overlap = 0
PHY-3002 : Step(102): len = 6747.7, overlap = 0.1875
PHY-3002 : Step(103): len = 6632, overlap = 1.5625
PHY-3002 : Step(104): len = 6669.2, overlap = 2.8125
PHY-3002 : Step(105): len = 6620, overlap = 3
PHY-3002 : Step(106): len = 6635.6, overlap = 3.15625
PHY-3002 : Step(107): len = 6679.2, overlap = 3.15625
PHY-3002 : Step(108): len = 6551.2, overlap = 3.15625
PHY-3002 : Step(109): len = 6544.2, overlap = 3.15625
PHY-3002 : Step(110): len = 6538.6, overlap = 3.15625
PHY-3002 : Step(111): len = 6519.5, overlap = 3.15625
PHY-3002 : Step(112): len = 6519.5, overlap = 3.15625
PHY-3002 : Step(113): len = 6505.6, overlap = 3.15625
PHY-3002 : Step(114): len = 6505.6, overlap = 3.15625
PHY-3002 : Step(115): len = 6504, overlap = 3.15625
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 2%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 482 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.009222s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (169.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000618722
PHY-3002 : Step(116): len = 6991.7, overlap = 9.84375
PHY-3002 : Step(117): len = 7138.2, overlap = 9.53125
PHY-3002 : Step(118): len = 7182.2, overlap = 9.8125
PHY-3002 : Step(119): len = 7220.6, overlap = 3.46875
PHY-3002 : Step(120): len = 7143.7, overlap = 4.375
PHY-3002 : Step(121): len = 7141.4, overlap = 4.78125
PHY-3002 : Step(122): len = 7111.8, overlap = 5.28125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00123744
PHY-3002 : Step(123): len = 7054, overlap = 5.375
PHY-3002 : Step(124): len = 7038.9, overlap = 5.40625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00247489
PHY-3002 : Step(125): len = 7008.6, overlap = 5.1875
PHY-3002 : Step(126): len = 7008.6, overlap = 5.1875
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 1674, tnet num: 482, tinst num: 392, tnode num: 2031, tedge num: 2685.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 20.78 peak overflow 2.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/484.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 7312, over cnt = 25(0%), over = 112, worst = 12
PHY-1001 : End global iterations;  0.012446s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (125.5%)

PHY-1001 : Congestion index: top1 = 14.57, top5 = 4.83, top10 = 2.55, top15 = 1.70.
PHY-1001 : End incremental global routing;  0.051207s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 482 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.008904s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.065416s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (95.5%)

OPT-1001 : Current memory(MB): used = 158, reserve = 118, peak = 158.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 273/484.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 7312, over cnt = 25(0%), over = 112, worst = 12
PHY-1002 : len = 8560, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 8704, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 8736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015689s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (298.8%)

PHY-1001 : Congestion index: top1 = 16.06, top5 = 5.63, top10 = 2.99, top15 = 2.00.
OPT-1001 : End congestion update;  0.049535s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (189.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 482 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.007935s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-0007 : Start: WNS 5106 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.057571s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (162.8%)

OPT-1001 : Current memory(MB): used = 159, reserve = 119, peak = 159.
OPT-1001 : End physical optimization;  0.180497s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (112.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 176 LUT to BLE ...
SYN-4008 : Packed 176 LUT and 69 SEQ to BLE.
SYN-4003 : Packing 67 remaining SEQ's ...
SYN-4005 : Packed 42 SEQ with LUT/SLICE
SYN-4006 : 74 single LUT's are left
SYN-4006 : 25 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 201/367 primitive instances ...
PHY-3001 : End packing;  0.009550s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (163.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 193 instances
RUN-1001 : 91 mslices, 90 lslices, 8 pads, 0 brams, 0 dsps
RUN-1001 : There are total 417 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 217 nets have 2 pins
RUN-1001 : 128 nets have [3 - 5] pins
RUN-1001 : 56 nets have [6 - 10] pins
RUN-1001 : 6 nets have [11 - 20] pins
RUN-1001 : 3 nets have [21 - 99] pins
RUN-1001 : 1 nets have 100+ pins
PHY-3001 : design contains 191 instances, 181 slices, 12 macros(70 instances: 43 mslices 27 lslices)
PHY-3001 : Cell area utilization is 2%
PHY-3001 : After packing: Len = 7283.4, Over = 8.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 2%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 1481, tnet num: 415, tinst num: 191, tnode num: 1765, tedge num: 2487.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 415 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064614s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (96.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000362699
PHY-3002 : Step(127): len = 7217.5, overlap = 10
PHY-3002 : Step(128): len = 7217.8, overlap = 10.5
PHY-3002 : Step(129): len = 7159.1, overlap = 10.25
PHY-3002 : Step(130): len = 7158.1, overlap = 10.25
PHY-3002 : Step(131): len = 7214.6, overlap = 10.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000725398
PHY-3002 : Step(132): len = 7316.7, overlap = 10.5
PHY-3002 : Step(133): len = 7309.5, overlap = 11
PHY-3002 : Step(134): len = 7302.2, overlap = 10.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0014508
PHY-3002 : Step(135): len = 7368.7, overlap = 10.5
PHY-3002 : Step(136): len = 7376.5, overlap = 10.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.031823s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (147.3%)

PHY-3001 : Trial Legalized: Len = 11099.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 2%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 415 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.007871s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0160864
PHY-3002 : Step(137): len = 10691.1, overlap = 0.25
PHY-3002 : Step(138): len = 9600.5, overlap = 2.5
PHY-3002 : Step(139): len = 9433.5, overlap = 2.25
PHY-3002 : Step(140): len = 9190, overlap = 2.5
PHY-3002 : Step(141): len = 9017.4, overlap = 3.5
PHY-3002 : Step(142): len = 8860.8, overlap = 3.5
PHY-3002 : Step(143): len = 8514.8, overlap = 2.25
PHY-3002 : Step(144): len = 8529.7, overlap = 2
PHY-3002 : Step(145): len = 8506.8, overlap = 2
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005160s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (302.8%)

PHY-3001 : Legalized: Len = 9849.4, Over = 0
PHY-3001 : End spreading;  0.002405s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 9849.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 1481, tnet num: 415, tinst num: 191, tnode num: 1765, tedge num: 2487.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 17/417.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 10696, over cnt = 44(0%), over = 82, worst = 5
PHY-1002 : len = 11320, over cnt = 18(0%), over = 20, worst = 3
PHY-1002 : len = 11440, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 11560, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 11600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.041335s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (75.6%)

PHY-1001 : Congestion index: top1 = 19.50, top5 = 7.77, top10 = 4.10, top15 = 2.74.
PHY-1001 : End incremental global routing;  0.077173s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 415 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.009583s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (163.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.091933s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.0%)

OPT-1001 : Current memory(MB): used = 160, reserve = 120, peak = 162.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 336/417.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 11600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.001940s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 19.50, top5 = 7.77, top10 = 4.10, top15 = 2.74.
OPT-1001 : End congestion update;  0.036682s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 415 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.007615s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (205.2%)

OPT-0007 : Start: WNS 4636 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.044394s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.6%)

OPT-1001 : Current memory(MB): used = 160, reserve = 120, peak = 162.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 415 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.006792s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 336/417.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 11600, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.001594s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 19.50, top5 = 7.77, top10 = 4.10, top15 = 2.74.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 415 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.007646s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4636 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 19.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4636ps with logic level 6 
RUN-1001 :       #2 path slack 4642ps with logic level 6 
OPT-1001 : End physical optimization;  0.254806s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.1%)

RUN-1003 : finish command "place" in  2.487184s wall, 3.093750s user + 2.078125s system = 5.171875s CPU (207.9%)

RUN-1004 : used memory is 151 MB, reserved memory is 110 MB, peak memory is 162 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 193 instances
RUN-1001 : 91 mslices, 90 lslices, 8 pads, 0 brams, 0 dsps
RUN-1001 : There are total 417 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 217 nets have 2 pins
RUN-1001 : 128 nets have [3 - 5] pins
RUN-1001 : 56 nets have [6 - 10] pins
RUN-1001 : 6 nets have [11 - 20] pins
RUN-1001 : 3 nets have [21 - 99] pins
RUN-1001 : 1 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 1481, tnet num: 415, tinst num: 191, tnode num: 1765, tedge num: 2487.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 91 mslices, 90 lslices, 8 pads, 0 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 415 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 10640, over cnt = 43(0%), over = 80, worst = 5
PHY-1002 : len = 11264, over cnt = 18(0%), over = 19, worst = 2
PHY-1002 : len = 11400, over cnt = 10(0%), over = 10, worst = 1
PHY-1002 : len = 11544, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.034798s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.8%)

PHY-1001 : Congestion index: top1 = 19.50, top5 = 7.74, top10 = 4.08, top15 = 2.73.
PHY-1001 : End global routing;  0.069865s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 174, reserve = 133, peak = 181.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 442, reserve = 406, peak = 442.
PHY-1001 : End build detailed router design. 3.057946s wall, 2.968750s user + 0.062500s system = 3.031250s CPU (99.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 7880, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.298105s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (94.3%)

PHY-1001 : Current memory(MB): used = 474, reserve = 439, peak = 474.
PHY-1001 : End phase 1; 0.304234s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (97.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 29% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 82% nets.
PHY-1022 : len = 29864, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 474, reserve = 439, peak = 474.
PHY-1001 : End initial routed; 0.208591s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (104.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/343(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.970   |   0.000   |   0   
RUN-1001 :   Hold   |   0.323   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.072921s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.7%)

PHY-1001 : Current memory(MB): used = 474, reserve = 439, peak = 474.
PHY-1001 : End phase 2; 0.281590s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (99.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 29864, over cnt = 13(0%), over = 13, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.005386s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 29872, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.015673s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 29936, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.012203s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (384.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/343(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.970   |   0.000   |   0   
RUN-1001 :   Hold   |   0.323   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.069454s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (112.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 0 feed throughs used by 0 nets
PHY-1001 : End commit to database; 0.044962s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (69.5%)

PHY-1001 : Current memory(MB): used = 484, reserve = 449, peak = 484.
PHY-1001 : End phase 3; 0.245294s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (101.9%)

PHY-1003 : Routed, final wirelength = 29936
PHY-1001 : Current memory(MB): used = 484, reserve = 449, peak = 484.
PHY-1001 : End export database. 0.007398s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (211.2%)

PHY-1001 : End detail routing;  4.065319s wall, 3.968750s user + 0.078125s system = 4.046875s CPU (99.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 1481, tnet num: 415, tinst num: 191, tnode num: 1765, tedge num: 2487.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  4.307156s wall, 4.203125s user + 0.093750s system = 4.296875s CPU (99.8%)

RUN-1004 : used memory is 442 MB, reserved memory is 406 MB, peak memory is 484 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      320   out of  19600    1.63%
#reg                      136   out of  19600    0.69%
#le                       345
  #lut only               209   out of    345   60.58%
  #reg only                25   out of    345    7.25%
  #lut&reg                111   out of    345   32.17%
#dsp                        0   out of     29    0.00%
#bram                       0   out of     64    0.00%
  #bram9k                   0
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     0
  #oreg                     3
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       0   out of     16    0.00%

Clock Resource Statistics
Index     ClockNet        Type               DriverType         Driver                   Fanout
#1        CtrlData/clk    GCLK               pll                CLK120/pll_inst.clkc0    94
#2        clk_in_dup_1    GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      NONE    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       NONE    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+------------------------------------------------------------------------------+
|Instance   |Module       |le     |lut     |ripple  |seq     |bram    |dsp     |
+------------------------------------------------------------------------------+
|top        |IFOG501_2B   |345    |250     |70      |140     |0       |0       |
|  CLK120   |global_clock |0      |0       |0       |0       |0       |0       |
|  CtrlData |CtrlData     |63     |45      |8       |35      |0       |0       |
|    usms   |Time_1ms     |27     |12      |5       |17      |0       |0       |
|  SPIM     |SPI_MASTER   |116    |81      |21      |58      |0       |0       |
|  wendu    |DS18B20      |166    |124     |41      |43      |0       |0       |
+------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout    Nets  
    #1         1       208   
    #2         2        91   
    #3         3        20   
    #4         4        17   
    #5        5-10      59   
    #6       11-50      4    
  Average     2.38           

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 1481, tnet num: 415, tinst num: 191, tnode num: 1765, tedge num: 2487.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 415 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 2 (0 unconstrainted).
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 191
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 417, pip num: 3131
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 421 valid insts, and 9738 bits set as '1'.
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  1.073320s wall, 4.468750s user + 0.093750s system = 4.562500s CPU (425.1%)

RUN-1004 : used memory is 449 MB, reserved memory is 413 MB, peak memory is 601 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_101851.log"
