<?xml version='1.0' encoding='utf-8'?>
<PLLConfig version="1.0">
    <GeneralConfig>
        <Type>PLL</Type>
        <Device>EG4S20NG88</Device>
        <create_VHDL>true</create_VHDL>
    </GeneralConfig>
    <Page1>
        <speed_grade>Any</speed_grade>
        <input_frequency>20.0000000000000000MHz</input_frequency>
        <feedback_mode>No compensation</feedback_mode>
        <enable_reset>ENABLE</enable_reset>
        <pll_lock>ENABLE</pll_lock>
    </Page1>
    <Page2>
        <bandwidth_setting>Medium</bandwidth_setting>
    </Page2>
    <Page3>
        <setting>frequncy_setting</setting>
        <multiplication_factor>51</multiplication_factor>
        <division_factor>1</division_factor>
        <clocks>
            <clock>
                <id>0</id>
                <clock_division_factor>17</clock_division_factor>
                <clock_frequency>60.0000000000000000MHz</clock_frequency>
                <phase_shift>0.0000000000000000deg</phase_shift>
            </clock>
        </clocks>
    </Page3>
    <GeneratedFiles>
        <Verilog Enable="true">pll_2.v</Verilog>
        <VHDL Enable="false">pll_2.vhd</VHDL>
    </GeneratedFiles>
</PLLConfig>
