============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Jan 11 16:45:22 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1527 instances
RUN-0007 : 371 luts, 900 seqs, 132 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2082 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1532 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     123     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1525 instances, 371 luts, 900 seqs, 207 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7370, tnet num: 2080, tinst num: 1525, tnode num: 10357, tedge num: 12523.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.277423s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (101.4%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 574316
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1525.
PHY-3001 : End clustering;  0.000017s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 494737, overlap = 13.5
PHY-3002 : Step(2): len = 463118, overlap = 15.75
PHY-3002 : Step(3): len = 441656, overlap = 20.25
PHY-3002 : Step(4): len = 426026, overlap = 18
PHY-3002 : Step(5): len = 417881, overlap = 15.75
PHY-3002 : Step(6): len = 400661, overlap = 13.5
PHY-3002 : Step(7): len = 387076, overlap = 11.25
PHY-3002 : Step(8): len = 374909, overlap = 20.25
PHY-3002 : Step(9): len = 367564, overlap = 18
PHY-3002 : Step(10): len = 346480, overlap = 20.25
PHY-3002 : Step(11): len = 335114, overlap = 20.25
PHY-3002 : Step(12): len = 327809, overlap = 20.25
PHY-3002 : Step(13): len = 321292, overlap = 20.25
PHY-3002 : Step(14): len = 309917, overlap = 20.25
PHY-3002 : Step(15): len = 302595, overlap = 18
PHY-3002 : Step(16): len = 297703, overlap = 18
PHY-3002 : Step(17): len = 288623, overlap = 15.75
PHY-3002 : Step(18): len = 278985, overlap = 18
PHY-3002 : Step(19): len = 274708, overlap = 18
PHY-3002 : Step(20): len = 269662, overlap = 18
PHY-3002 : Step(21): len = 253409, overlap = 18
PHY-3002 : Step(22): len = 245461, overlap = 20.25
PHY-3002 : Step(23): len = 243349, overlap = 20.25
PHY-3002 : Step(24): len = 231834, overlap = 20.25
PHY-3002 : Step(25): len = 203075, overlap = 15.75
PHY-3002 : Step(26): len = 197662, overlap = 18
PHY-3002 : Step(27): len = 195863, overlap = 18
PHY-3002 : Step(28): len = 173784, overlap = 13.5
PHY-3002 : Step(29): len = 169086, overlap = 18
PHY-3002 : Step(30): len = 166420, overlap = 18
PHY-3002 : Step(31): len = 163382, overlap = 18
PHY-3002 : Step(32): len = 159387, overlap = 18
PHY-3002 : Step(33): len = 157205, overlap = 18
PHY-3002 : Step(34): len = 148234, overlap = 15.75
PHY-3002 : Step(35): len = 141201, overlap = 18
PHY-3002 : Step(36): len = 138241, overlap = 18
PHY-3002 : Step(37): len = 135008, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106876
PHY-3002 : Step(38): len = 134684, overlap = 13.5
PHY-3002 : Step(39): len = 133704, overlap = 13.5
PHY-3002 : Step(40): len = 132514, overlap = 13.5
PHY-3002 : Step(41): len = 130679, overlap = 13.5
PHY-3002 : Step(42): len = 128867, overlap = 13.5
PHY-3002 : Step(43): len = 124669, overlap = 11.25
PHY-3002 : Step(44): len = 121826, overlap = 11.25
PHY-3002 : Step(45): len = 121046, overlap = 11.25
PHY-3002 : Step(46): len = 117725, overlap = 13.5
PHY-3002 : Step(47): len = 111480, overlap = 11.25
PHY-3002 : Step(48): len = 107694, overlap = 13.5
PHY-3002 : Step(49): len = 107377, overlap = 13.5
PHY-3002 : Step(50): len = 105640, overlap = 11.25
PHY-3002 : Step(51): len = 104698, overlap = 11.25
PHY-3002 : Step(52): len = 102216, overlap = 11.25
PHY-3002 : Step(53): len = 100955, overlap = 11.25
PHY-3002 : Step(54): len = 97534.7, overlap = 11.25
PHY-3002 : Step(55): len = 96029, overlap = 13.5
PHY-3002 : Step(56): len = 94494.9, overlap = 13.5
PHY-3002 : Step(57): len = 90824.2, overlap = 13.5
PHY-3002 : Step(58): len = 87380.7, overlap = 13.5
PHY-3002 : Step(59): len = 86751.5, overlap = 13.5
PHY-3002 : Step(60): len = 84481.1, overlap = 13.5
PHY-3002 : Step(61): len = 83906.5, overlap = 13.5
PHY-3002 : Step(62): len = 82452.3, overlap = 11.25
PHY-3002 : Step(63): len = 81632.6, overlap = 11.25
PHY-3002 : Step(64): len = 79291.3, overlap = 9.4375
PHY-3002 : Step(65): len = 77993.1, overlap = 11.75
PHY-3002 : Step(66): len = 76840, overlap = 11.9375
PHY-3002 : Step(67): len = 74041.1, overlap = 14.625
PHY-3002 : Step(68): len = 72723.3, overlap = 14.8125
PHY-3002 : Step(69): len = 71847.2, overlap = 12.6875
PHY-3002 : Step(70): len = 70768.7, overlap = 13.4375
PHY-3002 : Step(71): len = 70444.6, overlap = 13.875
PHY-3002 : Step(72): len = 68122.2, overlap = 12.8125
PHY-3002 : Step(73): len = 66439.9, overlap = 15.5625
PHY-3002 : Step(74): len = 65792.8, overlap = 15.5625
PHY-3002 : Step(75): len = 65030.6, overlap = 13.1875
PHY-3002 : Step(76): len = 64432.5, overlap = 17.8125
PHY-3002 : Step(77): len = 64101.6, overlap = 17.8125
PHY-3002 : Step(78): len = 63846.6, overlap = 15.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000213752
PHY-3002 : Step(79): len = 63946.9, overlap = 15.4375
PHY-3002 : Step(80): len = 64046.6, overlap = 15.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000427505
PHY-3002 : Step(81): len = 64032.8, overlap = 15.4375
PHY-3002 : Step(82): len = 64012.8, overlap = 15.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007581s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058435s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00120329
PHY-3002 : Step(83): len = 66230.9, overlap = 14.6875
PHY-3002 : Step(84): len = 66342.3, overlap = 14.375
PHY-3002 : Step(85): len = 65284.8, overlap = 13.9375
PHY-3002 : Step(86): len = 65162.4, overlap = 14.2812
PHY-3002 : Step(87): len = 64297.7, overlap = 14.3125
PHY-3002 : Step(88): len = 62449.9, overlap = 13.7812
PHY-3002 : Step(89): len = 61399.4, overlap = 11.7188
PHY-3002 : Step(90): len = 59540.8, overlap = 15.625
PHY-3002 : Step(91): len = 58376.2, overlap = 14.5312
PHY-3002 : Step(92): len = 57455.6, overlap = 14.7188
PHY-3002 : Step(93): len = 56868.1, overlap = 15.4062
PHY-3002 : Step(94): len = 55640, overlap = 17.4688
PHY-3002 : Step(95): len = 55003.6, overlap = 20.7188
PHY-3002 : Step(96): len = 54526.7, overlap = 20.8438
PHY-3002 : Step(97): len = 54046.6, overlap = 19.6875
PHY-3002 : Step(98): len = 53094.7, overlap = 17.7188
PHY-3002 : Step(99): len = 52844.1, overlap = 17.1875
PHY-3002 : Step(100): len = 52557.6, overlap = 16.9375
PHY-3002 : Step(101): len = 52089.2, overlap = 16.75
PHY-3002 : Step(102): len = 51856.1, overlap = 16.75
PHY-3002 : Step(103): len = 51862, overlap = 16.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00240657
PHY-3002 : Step(104): len = 51610.3, overlap = 17
PHY-3002 : Step(105): len = 51521.8, overlap = 16.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00481315
PHY-3002 : Step(106): len = 51404.6, overlap = 16.5
PHY-3002 : Step(107): len = 51412, overlap = 16.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.071094s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.42344e-05
PHY-3002 : Step(108): len = 51692.1, overlap = 46.9062
PHY-3002 : Step(109): len = 52667.5, overlap = 46.25
PHY-3002 : Step(110): len = 52442.7, overlap = 45.5312
PHY-3002 : Step(111): len = 52112.2, overlap = 38.625
PHY-3002 : Step(112): len = 52003.4, overlap = 38.4688
PHY-3002 : Step(113): len = 51839.1, overlap = 38.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000128469
PHY-3002 : Step(114): len = 51950.6, overlap = 38.375
PHY-3002 : Step(115): len = 52232.1, overlap = 37.9062
PHY-3002 : Step(116): len = 52404.1, overlap = 37
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000256938
PHY-3002 : Step(117): len = 52404.1, overlap = 36.125
PHY-3002 : Step(118): len = 52525.9, overlap = 35.5312
PHY-3002 : Step(119): len = 53291.2, overlap = 34.1562
PHY-3002 : Step(120): len = 53488.9, overlap = 33.1562
PHY-3002 : Step(121): len = 54049.6, overlap = 29.9688
PHY-3002 : Step(122): len = 54205, overlap = 29.4375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7370, tnet num: 2080, tinst num: 1525, tnode num: 10357, tedge num: 12523.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.59 peak overflow 2.84
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57208, over cnt = 248(0%), over = 943, worst = 17
PHY-1001 : End global iterations;  0.078337s wall, 0.109375s user + 0.031250s system = 0.140625s CPU (179.5%)

PHY-1001 : Congestion index: top1 = 39.01, top5 = 25.16, top10 = 16.17, top15 = 11.43.
PHY-1001 : End incremental global routing;  0.131171s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (131.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067179s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.227317s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (116.9%)

OPT-1001 : Current memory(MB): used = 210, reserve = 174, peak = 210.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1565/2082.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 57208, over cnt = 248(0%), over = 943, worst = 17
PHY-1002 : len = 62488, over cnt = 156(0%), over = 373, worst = 13
PHY-1002 : len = 65888, over cnt = 27(0%), over = 29, worst = 2
PHY-1002 : len = 66400, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 66528, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.098879s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (173.8%)

PHY-1001 : Congestion index: top1 = 34.31, top5 = 24.33, top10 = 17.57, top15 = 12.85.
OPT-1001 : End congestion update;  0.144303s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (151.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2080 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055621s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.3%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.203299s wall, 0.218750s user + 0.046875s system = 0.265625s CPU (130.7%)

OPT-1001 : Current memory(MB): used = 213, reserve = 177, peak = 213.
OPT-1001 : End physical optimization;  0.724080s wall, 0.750000s user + 0.093750s system = 0.843750s CPU (116.5%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 371 LUT to BLE ...
SYN-4008 : Packed 371 LUT and 164 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 116 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 620 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 991/1281 primitive instances ...
PHY-3001 : End packing;  0.049001s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 779 instances
RUN-1001 : 365 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1926 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1377 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 777 instances, 730 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 54212.8, Over = 56.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6252, tnet num: 1924, tinst num: 777, tnode num: 8425, tedge num: 11029.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1924 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.314735s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (104.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.74799e-05
PHY-3002 : Step(123): len = 52928.9, overlap = 57.75
PHY-3002 : Step(124): len = 52343.3, overlap = 55.25
PHY-3002 : Step(125): len = 51948.5, overlap = 56
PHY-3002 : Step(126): len = 51862.7, overlap = 58
PHY-3002 : Step(127): len = 51841.8, overlap = 59.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.49598e-05
PHY-3002 : Step(128): len = 51944.1, overlap = 59.5
PHY-3002 : Step(129): len = 52277.9, overlap = 57.5
PHY-3002 : Step(130): len = 52653.9, overlap = 56
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00010992
PHY-3002 : Step(131): len = 53132, overlap = 56
PHY-3002 : Step(132): len = 53709.2, overlap = 55.25
PHY-3002 : Step(133): len = 54705.7, overlap = 50
PHY-3002 : Step(134): len = 55045.1, overlap = 49.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.136156s wall, 0.093750s user + 0.156250s system = 0.250000s CPU (183.6%)

PHY-3001 : Trial Legalized: Len = 69625.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1924 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052495s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (119.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00128819
PHY-3002 : Step(135): len = 66134.5, overlap = 8.75
PHY-3002 : Step(136): len = 64115.7, overlap = 9
PHY-3002 : Step(137): len = 62296.6, overlap = 11
PHY-3002 : Step(138): len = 61529.5, overlap = 11.5
PHY-3002 : Step(139): len = 60757.3, overlap = 13.25
PHY-3002 : Step(140): len = 60099.5, overlap = 16.5
PHY-3002 : Step(141): len = 59507.2, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00247085
PHY-3002 : Step(142): len = 59653.7, overlap = 18.25
PHY-3002 : Step(143): len = 59661, overlap = 18
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0049417
PHY-3002 : Step(144): len = 59763.4, overlap = 17.75
PHY-3002 : Step(145): len = 59806, overlap = 17.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005614s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.3%)

PHY-3001 : Legalized: Len = 64342.7, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006364s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 9 instances has been re-located, deltaX = 3, deltaY = 8, maxDist = 2.
PHY-3001 : Final: Len = 64380.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6252, tnet num: 1924, tinst num: 777, tnode num: 8425, tedge num: 11029.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 36/1926.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70928, over cnt = 144(0%), over = 218, worst = 7
PHY-1002 : len = 71880, over cnt = 75(0%), over = 93, worst = 4
PHY-1002 : len = 72824, over cnt = 14(0%), over = 15, worst = 2
PHY-1002 : len = 73040, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 73120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.149833s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.3%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.39, top10 = 17.73, top15 = 14.02.
PHY-1001 : End incremental global routing;  0.208703s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (112.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1924 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063893s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.306341s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (107.1%)

OPT-1001 : Current memory(MB): used = 214, reserve = 179, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1690/1926.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73120, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006833s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.39, top10 = 17.73, top15 = 14.02.
OPT-1001 : End congestion update;  0.056717s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1924 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052526s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.2%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 739 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 777 instances, 730 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 64452.8, Over = 0
PHY-3001 : End spreading;  0.005399s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 64452.8, Over = 0
PHY-3001 : End incremental legalization;  0.046192s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (270.6%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.176139s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (177.4%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1924 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052678s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1678/1926.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73144, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 73144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.018554s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (84.2%)

PHY-1001 : Congestion index: top1 = 31.55, top5 = 22.43, top10 = 17.72, top15 = 14.01.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1924 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051349s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (91.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.964648s wall, 1.093750s user + 0.015625s system = 1.109375s CPU (115.0%)

RUN-1003 : finish command "place" in  5.615116s wall, 8.484375s user + 3.234375s system = 11.718750s CPU (208.7%)

RUN-1004 : used memory is 194 MB, reserved memory is 157 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 779 instances
RUN-1001 : 365 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1926 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1377 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 70 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6252, tnet num: 1924, tinst num: 777, tnode num: 8425, tedge num: 11029.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 365 mslices, 365 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1924 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70488, over cnt = 146(0%), over = 219, worst = 7
PHY-1002 : len = 71496, over cnt = 76(0%), over = 93, worst = 4
PHY-1002 : len = 72304, over cnt = 26(0%), over = 29, worst = 3
PHY-1002 : len = 72808, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146284s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (138.9%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 22.33, top10 = 17.60, top15 = 13.91.
PHY-1001 : End global routing;  0.198585s wall, 0.203125s user + 0.046875s system = 0.250000s CPU (125.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 200, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 465, peak = 497.
PHY-1001 : End build detailed router design. 3.408138s wall, 3.328125s user + 0.046875s system = 3.375000s CPU (99.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32592, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.450988s wall, 1.437500s user + 0.031250s system = 1.468750s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 530, reserve = 498, peak = 531.
PHY-1001 : End phase 1; 1.457072s wall, 1.437500s user + 0.031250s system = 1.468750s CPU (100.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 187552, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 531, reserve = 499, peak = 532.
PHY-1001 : End initial routed; 1.582142s wall, 2.484375s user + 0.031250s system = 2.515625s CPU (159.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1704(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.217   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.346   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.379443s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (102.9%)

PHY-1001 : Current memory(MB): used = 534, reserve = 501, peak = 534.
PHY-1001 : End phase 2; 1.961679s wall, 2.875000s user + 0.031250s system = 2.906250s CPU (148.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 187552, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.016080s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (97.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 187408, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.027570s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (56.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 187440, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022237s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (70.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1704(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.217   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.346   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.382057s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (102.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.177985s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.6%)

PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End phase 3; 0.758761s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (98.8%)

PHY-1003 : Routed, final wirelength = 187440
PHY-1001 : Current memory(MB): used = 549, reserve = 517, peak = 549.
PHY-1001 : End export database. 0.011938s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (130.9%)

PHY-1001 : End detail routing;  7.780101s wall, 8.578125s user + 0.125000s system = 8.703125s CPU (111.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6252, tnet num: 1924, tinst num: 777, tnode num: 8425, tedge num: 11029.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6270, tnet num: 1933, tinst num: 786, tnode num: 8443, tedge num: 11047.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.170479s wall, 3.125000s user + 0.203125s system = 3.328125s CPU (105.0%)

RUN-1003 : finish command "route" in  11.506533s wall, 12.250000s user + 0.375000s system = 12.625000s CPU (109.7%)

RUN-1004 : used memory is 502 MB, reserved memory is 471 MB, peak memory is 549 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      807   out of  19600    4.12%
#reg                      990   out of  19600    5.05%
#le                      1427
  #lut only               437   out of   1427   30.62%
  #reg only               620   out of   1427   43.45%
  #lut&reg                370   out of   1427   25.93%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         434
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1427   |600     |207     |1021    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1027   |290     |117     |837     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |42     |33      |9       |22      |0       |0       |
|    demodu                  |Demodulation                                     |433    |96      |39      |348     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |55     |30      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |7       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |8       |0       |14      |0       |0       |
|    integ                   |Integration                                      |134    |16      |14      |108     |0       |0       |
|    modu                    |Modulation                                       |92     |42      |21      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |305    |87      |29      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |21     |16      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |106    |99      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |28     |21      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |27     |27      |0       |20      |0       |0       |
|    U2                      |Ctrl_Data                                        |51     |51      |0       |20      |0       |0       |
|  wendu                     |DS18B20                                          |217    |172     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1350  
    #2          2       297   
    #3          3       122   
    #4          4        13   
    #5        5-10       79   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6270, tnet num: 1933, tinst num: 786, tnode num: 8443, tedge num: 11047.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1933 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 786
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1935, pip num: 14466
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1307 valid insts, and 38228 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.190643s wall, 17.875000s user + 0.078125s system = 17.953125s CPU (562.7%)

RUN-1004 : used memory is 518 MB, reserved memory is 489 MB, peak memory is 669 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240111_164522.log"
