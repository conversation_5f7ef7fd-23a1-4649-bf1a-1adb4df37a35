============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Nov  7 13:41:13 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1525 instances
RUN-0007 : 374 luts, 905 seqs, 122 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2053 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1538 nets have 2 pins
RUN-1001 : 400 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 16 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     252     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1523 instances, 374 luts, 905 seqs, 197 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7244, tnet num: 2051, tinst num: 1523, tnode num: 10173, tedge num: 12229.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2051 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.254548s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (98.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 544747
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1523.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 451107, overlap = 20.25
PHY-3002 : Step(2): len = 418067, overlap = 18
PHY-3002 : Step(3): len = 404917, overlap = 20.25
PHY-3002 : Step(4): len = 385620, overlap = 20.25
PHY-3002 : Step(5): len = 378180, overlap = 20.25
PHY-3002 : Step(6): len = 356574, overlap = 18
PHY-3002 : Step(7): len = 346903, overlap = 20.25
PHY-3002 : Step(8): len = 341132, overlap = 20.25
PHY-3002 : Step(9): len = 334422, overlap = 20.25
PHY-3002 : Step(10): len = 324415, overlap = 20.25
PHY-3002 : Step(11): len = 317114, overlap = 20.25
PHY-3002 : Step(12): len = 312222, overlap = 20.25
PHY-3002 : Step(13): len = 301850, overlap = 20.25
PHY-3002 : Step(14): len = 294996, overlap = 18
PHY-3002 : Step(15): len = 290512, overlap = 18
PHY-3002 : Step(16): len = 282406, overlap = 18
PHY-3002 : Step(17): len = 275450, overlap = 18
PHY-3002 : Step(18): len = 271425, overlap = 18
PHY-3002 : Step(19): len = 265611, overlap = 18
PHY-3002 : Step(20): len = 259531, overlap = 18
PHY-3002 : Step(21): len = 255579, overlap = 18
PHY-3002 : Step(22): len = 250083, overlap = 20.25
PHY-3002 : Step(23): len = 244072, overlap = 20.25
PHY-3002 : Step(24): len = 240189, overlap = 20.25
PHY-3002 : Step(25): len = 234553, overlap = 20.25
PHY-3002 : Step(26): len = 229181, overlap = 20.25
PHY-3002 : Step(27): len = 225210, overlap = 20.25
PHY-3002 : Step(28): len = 220560, overlap = 20.25
PHY-3002 : Step(29): len = 213107, overlap = 20.25
PHY-3002 : Step(30): len = 207739, overlap = 20.25
PHY-3002 : Step(31): len = 205248, overlap = 20.25
PHY-3002 : Step(32): len = 198225, overlap = 20.25
PHY-3002 : Step(33): len = 175819, overlap = 18
PHY-3002 : Step(34): len = 171034, overlap = 20.25
PHY-3002 : Step(35): len = 169450, overlap = 20.25
PHY-3002 : Step(36): len = 122790, overlap = 13.5
PHY-3002 : Step(37): len = 117589, overlap = 20.25
PHY-3002 : Step(38): len = 116108, overlap = 18
PHY-3002 : Step(39): len = 112837, overlap = 20.25
PHY-3002 : Step(40): len = 109511, overlap = 20.25
PHY-3002 : Step(41): len = 108191, overlap = 20.25
PHY-3002 : Step(42): len = 104031, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100239
PHY-3002 : Step(43): len = 103718, overlap = 13.5
PHY-3002 : Step(44): len = 103358, overlap = 13.5
PHY-3002 : Step(45): len = 102500, overlap = 15.75
PHY-3002 : Step(46): len = 101741, overlap = 15.75
PHY-3002 : Step(47): len = 100566, overlap = 13.5
PHY-3002 : Step(48): len = 96794.8, overlap = 11.25
PHY-3002 : Step(49): len = 96663, overlap = 11.25
PHY-3002 : Step(50): len = 95335.2, overlap = 11.25
PHY-3002 : Step(51): len = 92996.1, overlap = 13.5
PHY-3002 : Step(52): len = 91855, overlap = 13.5
PHY-3002 : Step(53): len = 90473.4, overlap = 13.5
PHY-3002 : Step(54): len = 88283.8, overlap = 13.5
PHY-3002 : Step(55): len = 85778.2, overlap = 13.5
PHY-3002 : Step(56): len = 84742.2, overlap = 13.5
PHY-3002 : Step(57): len = 83330.9, overlap = 13.75
PHY-3002 : Step(58): len = 80568.7, overlap = 14.1875
PHY-3002 : Step(59): len = 79923.4, overlap = 16.5
PHY-3002 : Step(60): len = 77143.8, overlap = 14.375
PHY-3002 : Step(61): len = 75960.3, overlap = 12.125
PHY-3002 : Step(62): len = 74027, overlap = 12
PHY-3002 : Step(63): len = 72214.5, overlap = 14.5
PHY-3002 : Step(64): len = 70637.1, overlap = 14.5
PHY-3002 : Step(65): len = 70242.6, overlap = 16.5
PHY-3002 : Step(66): len = 67826, overlap = 14.125
PHY-3002 : Step(67): len = 66352, overlap = 14.1875
PHY-3002 : Step(68): len = 65240.6, overlap = 14.625
PHY-3002 : Step(69): len = 64815.6, overlap = 14.875
PHY-3002 : Step(70): len = 63588.6, overlap = 15.375
PHY-3002 : Step(71): len = 62677.9, overlap = 15.5625
PHY-3002 : Step(72): len = 62181.3, overlap = 15.75
PHY-3002 : Step(73): len = 60522.7, overlap = 13.25
PHY-3002 : Step(74): len = 59420.1, overlap = 13.25
PHY-3002 : Step(75): len = 58979.8, overlap = 15.625
PHY-3002 : Step(76): len = 58045.5, overlap = 15.4375
PHY-3002 : Step(77): len = 57908.3, overlap = 15.125
PHY-3002 : Step(78): len = 56995.9, overlap = 13.125
PHY-3002 : Step(79): len = 55445.5, overlap = 11
PHY-3002 : Step(80): len = 53721, overlap = 12.875
PHY-3002 : Step(81): len = 53361.7, overlap = 12.875
PHY-3002 : Step(82): len = 53188.9, overlap = 15
PHY-3002 : Step(83): len = 53162.8, overlap = 14.75
PHY-3002 : Step(84): len = 52918.4, overlap = 14.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000200477
PHY-3002 : Step(85): len = 52985.2, overlap = 14.6875
PHY-3002 : Step(86): len = 52995.6, overlap = 14.6875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000400954
PHY-3002 : Step(87): len = 52968.6, overlap = 14.6875
PHY-3002 : Step(88): len = 52921.7, overlap = 14.6875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005651s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2051 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054507s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (86.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000216733
PHY-3002 : Step(89): len = 56760.5, overlap = 12.8125
PHY-3002 : Step(90): len = 56875.8, overlap = 12.625
PHY-3002 : Step(91): len = 56019.8, overlap = 12.4375
PHY-3002 : Step(92): len = 56073, overlap = 12.4375
PHY-3002 : Step(93): len = 55931.4, overlap = 11.625
PHY-3002 : Step(94): len = 54452, overlap = 11.25
PHY-3002 : Step(95): len = 53863.2, overlap = 10.125
PHY-3002 : Step(96): len = 53532.7, overlap = 9.8125
PHY-3002 : Step(97): len = 52676.8, overlap = 9.1875
PHY-3002 : Step(98): len = 51855.4, overlap = 9.25
PHY-3002 : Step(99): len = 51532.6, overlap = 9.25
PHY-3002 : Step(100): len = 51017, overlap = 10.1875
PHY-3002 : Step(101): len = 50317.6, overlap = 12.2188
PHY-3002 : Step(102): len = 49894.9, overlap = 12.6875
PHY-3002 : Step(103): len = 49580.1, overlap = 13.0938
PHY-3002 : Step(104): len = 49390.4, overlap = 15.3438
PHY-3002 : Step(105): len = 49342.4, overlap = 17.5938
PHY-3002 : Step(106): len = 48469.4, overlap = 16.7188
PHY-3002 : Step(107): len = 48289.2, overlap = 17.5938
PHY-3002 : Step(108): len = 47727.7, overlap = 17.5938
PHY-3002 : Step(109): len = 47584.8, overlap = 16.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000433466
PHY-3002 : Step(110): len = 47624.7, overlap = 15.5938
PHY-3002 : Step(111): len = 47831.2, overlap = 15.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000866933
PHY-3002 : Step(112): len = 47651.1, overlap = 15.4375
PHY-3002 : Step(113): len = 47552.7, overlap = 15
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2051 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067253s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (116.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.23472e-05
PHY-3002 : Step(114): len = 48024.4, overlap = 52.5
PHY-3002 : Step(115): len = 48465.2, overlap = 44.625
PHY-3002 : Step(116): len = 49028, overlap = 41.375
PHY-3002 : Step(117): len = 48575.3, overlap = 41.9688
PHY-3002 : Step(118): len = 48415.7, overlap = 41.1562
PHY-3002 : Step(119): len = 48388.3, overlap = 40.7188
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000164694
PHY-3002 : Step(120): len = 48764.2, overlap = 40
PHY-3002 : Step(121): len = 48764.2, overlap = 40
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000281615
PHY-3002 : Step(122): len = 49700.2, overlap = 38.5
PHY-3002 : Step(123): len = 50802, overlap = 40
PHY-3002 : Step(124): len = 51515.2, overlap = 38.9375
PHY-3002 : Step(125): len = 51183.2, overlap = 38.9688
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000563231
PHY-3002 : Step(126): len = 51395, overlap = 39.2812
PHY-3002 : Step(127): len = 51957.1, overlap = 36.3125
PHY-3002 : Step(128): len = 52182.5, overlap = 34.5625
PHY-3002 : Step(129): len = 52263.7, overlap = 31.4688
PHY-3002 : Step(130): len = 52110.1, overlap = 29.4375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7244, tnet num: 2051, tinst num: 1523, tnode num: 10173, tedge num: 12229.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 74.31 peak overflow 2.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2053.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54760, over cnt = 238(0%), over = 946, worst = 15
PHY-1001 : End global iterations;  0.078861s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (99.1%)

PHY-1001 : Congestion index: top1 = 40.62, top5 = 25.52, top10 = 15.82, top15 = 11.16.
PHY-1001 : End incremental global routing;  0.128944s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (109.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2051 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061392s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (101.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.218559s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (100.1%)

OPT-1001 : Current memory(MB): used = 211, reserve = 174, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1524/2053.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54760, over cnt = 238(0%), over = 946, worst = 15
PHY-1002 : len = 59584, over cnt = 175(0%), over = 490, worst = 11
PHY-1002 : len = 64600, over cnt = 22(0%), over = 43, worst = 10
PHY-1002 : len = 64992, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 65216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.089527s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (157.1%)

PHY-1001 : Congestion index: top1 = 35.80, top5 = 24.92, top10 = 17.57, top15 = 12.81.
OPT-1001 : End congestion update;  0.132508s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (141.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2051 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053621s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (87.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.189108s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (123.9%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : End physical optimization;  0.665735s wall, 0.640625s user + 0.062500s system = 0.703125s CPU (105.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 174 SEQ to BLE.
SYN-4003 : Packing 731 remaining SEQ's ...
SYN-4005 : Packed 128 SEQ with LUT/SLICE
SYN-4006 : 90 single LUT's are left
SYN-4006 : 603 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 977/1257 primitive instances ...
PHY-3001 : End packing;  0.046918s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 762 instances
RUN-1001 : 356 mslices, 357 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1887 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1372 nets have 2 pins
RUN-1001 : 399 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 760 instances, 713 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52180, Over = 54.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6106, tnet num: 1885, tinst num: 760, tnode num: 8217, tedge num: 10719.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.290336s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (96.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.90832e-05
PHY-3002 : Step(131): len = 51336.7, overlap = 55.75
PHY-3002 : Step(132): len = 50952.6, overlap = 54.25
PHY-3002 : Step(133): len = 50642.8, overlap = 54.5
PHY-3002 : Step(134): len = 50531.1, overlap = 55.25
PHY-3002 : Step(135): len = 50608.5, overlap = 54.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.81664e-05
PHY-3002 : Step(136): len = 51381.2, overlap = 50.75
PHY-3002 : Step(137): len = 52144.6, overlap = 49.5
PHY-3002 : Step(138): len = 52602.6, overlap = 50.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000116333
PHY-3002 : Step(139): len = 53378.1, overlap = 47.75
PHY-3002 : Step(140): len = 53914.9, overlap = 47.25
PHY-3002 : Step(141): len = 54458, overlap = 48
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075621s wall, 0.125000s user + 0.109375s system = 0.234375s CPU (309.9%)

PHY-3001 : Trial Legalized: Len = 68392.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.049204s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0010272
PHY-3002 : Step(142): len = 65292.1, overlap = 5.25
PHY-3002 : Step(143): len = 63849, overlap = 10.75
PHY-3002 : Step(144): len = 61605.4, overlap = 12.5
PHY-3002 : Step(145): len = 60403.4, overlap = 16.25
PHY-3002 : Step(146): len = 59211.2, overlap = 18
PHY-3002 : Step(147): len = 58522.6, overlap = 18.75
PHY-3002 : Step(148): len = 58260.7, overlap = 18.5
PHY-3002 : Step(149): len = 58321.8, overlap = 18.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0020544
PHY-3002 : Step(150): len = 58454.3, overlap = 18.25
PHY-3002 : Step(151): len = 58565.4, overlap = 18.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0041088
PHY-3002 : Step(152): len = 58673.5, overlap = 17.75
PHY-3002 : Step(153): len = 58718.1, overlap = 18.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005142s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63290.9, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005108s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (305.9%)

PHY-3001 : 9 instances has been re-located, deltaX = 2, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 63386.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6106, tnet num: 1885, tinst num: 760, tnode num: 8217, tedge num: 10719.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 58/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70088, over cnt = 134(0%), over = 199, worst = 6
PHY-1002 : len = 70944, over cnt = 82(0%), over = 97, worst = 5
PHY-1002 : len = 71952, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 72064, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 72096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.139272s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (112.2%)

PHY-1001 : Congestion index: top1 = 31.57, top5 = 23.53, top10 = 18.35, top15 = 14.08.
PHY-1001 : End incremental global routing;  0.191778s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (105.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052674s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.273043s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 218, reserve = 182, peak = 218.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1688/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72096, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005386s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.57, top5 = 23.53, top10 = 18.35, top15 = 14.08.
OPT-1001 : End congestion update;  0.050262s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047299s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.1%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 722 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 760 instances, 713 slices, 22 macros(197 instances: 122 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63457, Over = 0
PHY-3001 : End spreading;  0.004708s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63457, Over = 0
PHY-3001 : End incremental legalization;  0.033721s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.7%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 150 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.145143s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (172.2%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045072s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1681/1887.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72104, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72112, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.015533s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (100.6%)

PHY-1001 : Congestion index: top1 = 31.66, top5 = 23.55, top10 = 18.35, top15 = 14.08.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046903s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (99.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.241379
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.838216s wall, 0.937500s user + 0.015625s system = 0.953125s CPU (113.7%)

RUN-1003 : finish command "place" in  5.052154s wall, 7.031250s user + 3.093750s system = 10.125000s CPU (200.4%)

RUN-1004 : used memory is 208 MB, reserved memory is 172 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 762 instances
RUN-1001 : 356 mslices, 357 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1887 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1372 nets have 2 pins
RUN-1001 : 399 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6106, tnet num: 1885, tinst num: 760, tnode num: 8217, tedge num: 10719.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 356 mslices, 357 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1885 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69416, over cnt = 134(0%), over = 200, worst = 6
PHY-1002 : len = 70224, over cnt = 85(0%), over = 104, worst = 3
PHY-1002 : len = 71368, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 71496, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.131539s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (118.8%)

PHY-1001 : Congestion index: top1 = 31.81, top5 = 23.31, top10 = 18.10, top15 = 13.93.
PHY-1001 : End global routing;  0.179794s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (113.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 235, reserve = 200, peak = 241.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 496, reserve = 464, peak = 496.
PHY-1001 : End build detailed router design. 3.194792s wall, 3.125000s user + 0.078125s system = 3.203125s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.322118s wall, 1.312500s user + 0.000000s system = 1.312500s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 528, reserve = 498, peak = 528.
PHY-1001 : End phase 1; 1.328017s wall, 1.328125s user + 0.000000s system = 1.328125s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182344, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 499, peak = 530.
PHY-1001 : End initial routed; 1.280960s wall, 2.046875s user + 0.125000s system = 2.171875s CPU (169.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1675(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.336   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.466   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.333098s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.5%)

PHY-1001 : Current memory(MB): used = 532, reserve = 500, peak = 532.
PHY-1001 : End phase 2; 1.614155s wall, 2.375000s user + 0.125000s system = 2.500000s CPU (154.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182344, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015320s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (102.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182280, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026958s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (115.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182296, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.021078s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 182304, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.018599s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (84.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1675(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.336   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.466   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.335116s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (97.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.165298s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (104.0%)

PHY-1001 : Current memory(MB): used = 546, reserve = 514, peak = 546.
PHY-1001 : End phase 3; 0.714023s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.7%)

PHY-1003 : Routed, final wirelength = 182304
PHY-1001 : Current memory(MB): used = 546, reserve = 515, peak = 546.
PHY-1001 : End export database. 0.009365s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (166.8%)

PHY-1001 : End detail routing;  7.038365s wall, 7.718750s user + 0.203125s system = 7.921875s CPU (112.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6106, tnet num: 1885, tinst num: 760, tnode num: 8217, tedge num: 10719.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[54] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6148, tnet num: 1906, tinst num: 781, tnode num: 8259, tedge num: 10761.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.960923s wall, 3.109375s user + 0.140625s system = 3.250000s CPU (109.8%)

RUN-1003 : finish command "route" in  10.510064s wall, 11.343750s user + 0.359375s system = 11.703125s CPU (111.4%)

RUN-1004 : used memory is 542 MB, reserved memory is 513 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      815   out of  19600    4.16%
#reg                      959   out of  19600    4.89%
#le                      1418
  #lut only               459   out of   1418   32.37%
  #reg only               603   out of   1418   42.52%
  #lut&reg                356   out of   1418   25.11%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         412
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    44
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1418   |618     |197     |990     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1014   |307     |104     |803     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |22     |16      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |472    |136     |44      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |54     |34      |6       |46      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |8       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |12      |0       |16      |0       |0       |
|    integ                   |Integration                                      |138    |21      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |61     |24      |6       |60      |0       |1       |
|    rs422                   |Rs422Output                                      |302    |96      |29      |248     |0       |4       |
|    trans                   |SquareWaveGenerator                              |19     |14      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |111    |104     |7       |60      |0       |0       |
|    U0                      |speed_select_Tx                                  |29     |22      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |23     |23      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |59     |59      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1357  
    #2          2       266   
    #3          3       122   
    #4          4        11   
    #5        5-10       81   
    #6        11-50      27   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6148, tnet num: 1906, tinst num: 781, tnode num: 8259, tedge num: 10761.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1906 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 781
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1908, pip num: 14319
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1298 valid insts, and 37825 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.013676s wall, 17.109375s user + 0.078125s system = 17.187500s CPU (570.3%)

RUN-1004 : used memory is 552 MB, reserved memory is 519 MB, peak memory is 687 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231107_134113.log"
