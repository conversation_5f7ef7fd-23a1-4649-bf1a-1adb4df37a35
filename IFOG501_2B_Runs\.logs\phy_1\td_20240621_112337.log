============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 11:23:37 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 25 trigger nets, 25 data nets.
KIT-1004 : Chipwatcher code = 1001011011010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2118/10 useful/useless nets, 1337/5 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 1891/18 useful/useless nets, 1667/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 282 better
SYN-1014 : Optimize round 2
SYN-1032 : 1696/15 useful/useless nets, 1472/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1720/156 useful/useless nets, 1518/30 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 207 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 2088/5 useful/useless nets, 1886/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7847, tnet num: 2088, tinst num: 1885, tnode num: 9817, tedge num: 12033.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2088 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 175 (3.57), #lev = 7 (1.77)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 174 (3.59), #lev = 6 (1.89)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 395 instances into 174 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 280 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/U2/data_Packet[0] will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (180 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1391 instances
RUN-0007 : 594 luts, 596 seqs, 102 mslices, 63 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1601 nets
RUN-6004 WARNING: There are 7 nets with only 1 pin.
RUN-1001 : 931 nets have 2 pins
RUN-1001 : 480 nets have [3 - 5] pins
RUN-1001 : 99 nets have [6 - 10] pins
RUN-1001 : 53 nets have [11 - 20] pins
RUN-1001 : 26 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     146     
RUN-1001 :   No   |  No   |  Yes  |     101     
RUN-1001 :   No   |  Yes  |  No   |     84      
RUN-1001 :   Yes  |  No   |  No   |     89      
RUN-1001 :   Yes  |  No   |  Yes  |     176     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 17
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1389 instances, 594 luts, 596 seqs, 165 slices, 25 macros(165 instances: 102 mslices 63 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6795, tnet num: 1599, tinst num: 1389, tnode num: 8768, tedge num: 11150.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1599 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.122336s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (102.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 357922
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1389.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 272512, overlap = 51.75
PHY-3002 : Step(2): len = 220296, overlap = 51.75
PHY-3002 : Step(3): len = 188294, overlap = 51.75
PHY-3002 : Step(4): len = 165913, overlap = 51.75
PHY-3002 : Step(5): len = 146061, overlap = 51.75
PHY-3002 : Step(6): len = 130435, overlap = 51.75
PHY-3002 : Step(7): len = 117223, overlap = 51.75
PHY-3002 : Step(8): len = 102340, overlap = 51.75
PHY-3002 : Step(9): len = 89954.5, overlap = 51.75
PHY-3002 : Step(10): len = 80827.1, overlap = 51.75
PHY-3002 : Step(11): len = 74161, overlap = 51.75
PHY-3002 : Step(12): len = 69434.5, overlap = 51.75
PHY-3002 : Step(13): len = 65755.9, overlap = 51.75
PHY-3002 : Step(14): len = 60806.7, overlap = 52.5
PHY-3002 : Step(15): len = 57487.3, overlap = 53.0625
PHY-3002 : Step(16): len = 54815.7, overlap = 53.0625
PHY-3002 : Step(17): len = 52427.2, overlap = 53.1875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.15214e-06
PHY-3002 : Step(18): len = 54277.9, overlap = 48.6875
PHY-3002 : Step(19): len = 54711.4, overlap = 41.6875
PHY-3002 : Step(20): len = 52531.2, overlap = 51.1875
PHY-3002 : Step(21): len = 52321.2, overlap = 46.75
PHY-3002 : Step(22): len = 51374.7, overlap = 46.8125
PHY-3002 : Step(23): len = 50893.2, overlap = 47.125
PHY-3002 : Step(24): len = 49846.5, overlap = 44.875
PHY-3002 : Step(25): len = 48561.2, overlap = 35.625
PHY-3002 : Step(26): len = 48167.1, overlap = 37.4375
PHY-3002 : Step(27): len = 46170.1, overlap = 37.625
PHY-3002 : Step(28): len = 45771.7, overlap = 35.5625
PHY-3002 : Step(29): len = 45674.8, overlap = 37.9375
PHY-3002 : Step(30): len = 44583.6, overlap = 37.9375
PHY-3002 : Step(31): len = 43706.3, overlap = 40.3125
PHY-3002 : Step(32): len = 42559.5, overlap = 42.5
PHY-3002 : Step(33): len = 40782.2, overlap = 42.5625
PHY-3002 : Step(34): len = 40101.7, overlap = 42.5
PHY-3002 : Step(35): len = 39655.5, overlap = 42.4375
PHY-3002 : Step(36): len = 39360.4, overlap = 41.625
PHY-3002 : Step(37): len = 38571.5, overlap = 34.9688
PHY-3002 : Step(38): len = 37979.8, overlap = 34.9688
PHY-3002 : Step(39): len = 37137.5, overlap = 37.2188
PHY-3002 : Step(40): len = 36707.3, overlap = 35
PHY-3002 : Step(41): len = 36125.2, overlap = 34.9062
PHY-3002 : Step(42): len = 35485.9, overlap = 36.1875
PHY-3002 : Step(43): len = 34734.3, overlap = 38.25
PHY-3002 : Step(44): len = 34331.3, overlap = 38.25
PHY-3002 : Step(45): len = 33656.6, overlap = 36
PHY-3002 : Step(46): len = 33077.6, overlap = 36.4375
PHY-3002 : Step(47): len = 32676.8, overlap = 36.625
PHY-3002 : Step(48): len = 32267.2, overlap = 34.25
PHY-3002 : Step(49): len = 32046.2, overlap = 33.875
PHY-3002 : Step(50): len = 31690.6, overlap = 34.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 1.43043e-05
PHY-3002 : Step(51): len = 31669.3, overlap = 36.5
PHY-3002 : Step(52): len = 31670.8, overlap = 34.3125
PHY-3002 : Step(53): len = 31751.1, overlap = 34.375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 2.86086e-05
PHY-3002 : Step(54): len = 31728.1, overlap = 36.625
PHY-3002 : Step(55): len = 31714.4, overlap = 36.5625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006586s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1599 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038931s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (80.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00134148
PHY-3002 : Step(56): len = 33204.5, overlap = 16.7812
PHY-3002 : Step(57): len = 33239.3, overlap = 16.6875
PHY-3002 : Step(58): len = 33426.5, overlap = 16.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00268296
PHY-3002 : Step(59): len = 33323.1, overlap = 16.3438
PHY-3002 : Step(60): len = 33324.3, overlap = 16.3438
PHY-3002 : Step(61): len = 33405.5, overlap = 16.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00536593
PHY-3002 : Step(62): len = 33314.5, overlap = 16.1562
PHY-3002 : Step(63): len = 33321.2, overlap = 15.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1599 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039116s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.65576e-05
PHY-3002 : Step(64): len = 33884.9, overlap = 56.3125
PHY-3002 : Step(65): len = 33884.9, overlap = 56.3125
PHY-3002 : Step(66): len = 33968, overlap = 56.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000113115
PHY-3002 : Step(67): len = 35030.9, overlap = 49.3125
PHY-3002 : Step(68): len = 35503.8, overlap = 49.3125
PHY-3002 : Step(69): len = 36395.6, overlap = 46.0938
PHY-3002 : Step(70): len = 36820.1, overlap = 44
PHY-3002 : Step(71): len = 37111.2, overlap = 39.375
PHY-3002 : Step(72): len = 37049.4, overlap = 38.8125
PHY-3002 : Step(73): len = 37098.6, overlap = 36.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00022623
PHY-3002 : Step(74): len = 36908.3, overlap = 36.75
PHY-3002 : Step(75): len = 36868.9, overlap = 35.5312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000452461
PHY-3002 : Step(76): len = 37482.2, overlap = 33.5
PHY-3002 : Step(77): len = 37605, overlap = 32.8125
PHY-3002 : Step(78): len = 37674.3, overlap = 30.9688
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000904921
PHY-3002 : Step(79): len = 38213.4, overlap = 27.9062
PHY-3002 : Step(80): len = 38452.9, overlap = 26.4688
PHY-3002 : Step(81): len = 38511.9, overlap = 25.2812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6795, tnet num: 1599, tinst num: 1389, tnode num: 8768, tedge num: 11150.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 79.12 peak overflow 3.44
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1601.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 47128, over cnt = 202(0%), over = 808, worst = 25
PHY-1001 : End global iterations;  0.097969s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (143.5%)

PHY-1001 : Congestion index: top1 = 38.94, top5 = 22.41, top10 = 13.95, top15 = 10.02.
PHY-1001 : End incremental global routing;  0.153220s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (132.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1599 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053904s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1377 has valid locations, 23 needs to be replaced
PHY-3001 : design contains 1411 instances, 594 luts, 618 seqs, 165 slices, 25 macros(165 instances: 102 mslices 63 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 38892
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6883, tnet num: 1621, tinst num: 1411, tnode num: 8922, tedge num: 11282.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1621 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.167525s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(82): len = 38847.6, overlap = 0.34375
PHY-3002 : Step(83): len = 38847.6, overlap = 0.34375
PHY-3002 : Step(84): len = 38865.7, overlap = 0.34375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1621 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068465s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00132673
PHY-3002 : Step(85): len = 38981.3, overlap = 25.4062
PHY-3002 : Step(86): len = 38981.3, overlap = 25.4062
PHY-3001 : Final: Len = 38981.3, Over = 25.4062
PHY-3001 : End incremental placement;  0.313379s wall, 0.281250s user + 0.171875s system = 0.453125s CPU (144.6%)

OPT-1001 : Total overflow 80.00 peak overflow 3.44
OPT-1001 : End high-fanout net optimization;  0.549989s wall, 0.546875s user + 0.187500s system = 0.734375s CPU (133.5%)

OPT-1001 : Current memory(MB): used = 198, reserve = 152, peak = 198.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1155/1623.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 47880, over cnt = 203(0%), over = 805, worst = 25
PHY-1002 : len = 53936, over cnt = 153(0%), over = 305, worst = 10
PHY-1002 : len = 56944, over cnt = 40(0%), over = 62, worst = 5
PHY-1002 : len = 57320, over cnt = 13(0%), over = 15, worst = 2
PHY-1002 : len = 57656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.160613s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (107.0%)

PHY-1001 : Congestion index: top1 = 35.00, top5 = 22.85, top10 = 15.64, top15 = 11.54.
OPT-1001 : End congestion update;  0.225351s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (104.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1621 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.037939s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (123.6%)

OPT-0007 : Start: WNS 3051 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.263513s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (106.7%)

OPT-1001 : Current memory(MB): used = 197, reserve = 151, peak = 198.
OPT-1001 : End physical optimization;  0.962012s wall, 0.968750s user + 0.203125s system = 1.171875s CPU (121.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 594 LUT to BLE ...
SYN-4008 : Packed 594 LUT and 228 SEQ to BLE.
SYN-4003 : Packing 390 remaining SEQ's ...
SYN-4005 : Packed 187 SEQ with LUT/SLICE
SYN-4006 : 197 single LUT's are left
SYN-4006 : 203 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 797/1132 primitive instances ...
PHY-3001 : End packing;  0.056788s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 649 instances
RUN-1001 : 307 mslices, 306 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1399 nets
RUN-6004 WARNING: There are 7 nets with only 1 pin.
RUN-1001 : 698 nets have 2 pins
RUN-1001 : 506 nets have [3 - 5] pins
RUN-1001 : 112 nets have [6 - 10] pins
RUN-1001 : 44 nets have [11 - 20] pins
RUN-1001 : 27 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 647 instances, 613 slices, 25 macros(165 instances: 102 mslices 63 lslices)
PHY-3001 : Cell area utilization is 8%
PHY-3001 : After packing: Len = 38923.2, Over = 40.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6002, tnet num: 1397, tinst num: 647, tnode num: 7516, tedge num: 10229.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.226909s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (96.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.10707e-05
PHY-3002 : Step(87): len = 38323, overlap = 41
PHY-3002 : Step(88): len = 37952.1, overlap = 43.75
PHY-3002 : Step(89): len = 37643.7, overlap = 45.25
PHY-3002 : Step(90): len = 37783, overlap = 44.75
PHY-3002 : Step(91): len = 38341.6, overlap = 43
PHY-3002 : Step(92): len = 38794.3, overlap = 42.25
PHY-3002 : Step(93): len = 39086.3, overlap = 43.75
PHY-3002 : Step(94): len = 38916.3, overlap = 42.5
PHY-3002 : Step(95): len = 38703.5, overlap = 41.25
PHY-3002 : Step(96): len = 38390.9, overlap = 41.75
PHY-3002 : Step(97): len = 38106.9, overlap = 40.5
PHY-3002 : Step(98): len = 37769.6, overlap = 42.5
PHY-3002 : Step(99): len = 37202.7, overlap = 44.25
PHY-3002 : Step(100): len = 36792.9, overlap = 47
PHY-3002 : Step(101): len = 36594, overlap = 48.25
PHY-3002 : Step(102): len = 36673.8, overlap = 48.5
PHY-3002 : Step(103): len = 36181.5, overlap = 49.25
PHY-3002 : Step(104): len = 35977.8, overlap = 51.75
PHY-3002 : Step(105): len = 35995, overlap = 52.25
PHY-3002 : Step(106): len = 35885.1, overlap = 52
PHY-3002 : Step(107): len = 35754.8, overlap = 49.5
PHY-3002 : Step(108): len = 35774.5, overlap = 52.5
PHY-3002 : Step(109): len = 35536.8, overlap = 53.25
PHY-3002 : Step(110): len = 35489, overlap = 50.75
PHY-3002 : Step(111): len = 35596, overlap = 48.75
PHY-3002 : Step(112): len = 35564, overlap = 49
PHY-3002 : Step(113): len = 35504.3, overlap = 50.25
PHY-3002 : Step(114): len = 35261.9, overlap = 50.5
PHY-3002 : Step(115): len = 35278.8, overlap = 49.25
PHY-3002 : Step(116): len = 35283.5, overlap = 48.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000182141
PHY-3002 : Step(117): len = 35548.6, overlap = 48.5
PHY-3002 : Step(118): len = 35875.1, overlap = 47.75
PHY-3002 : Step(119): len = 36127.7, overlap = 46.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000364283
PHY-3002 : Step(120): len = 37197.9, overlap = 43.5
PHY-3002 : Step(121): len = 37678.3, overlap = 41
PHY-3002 : Step(122): len = 37526.7, overlap = 41.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.080276s wall, 0.078125s user + 0.093750s system = 0.171875s CPU (214.1%)

PHY-3001 : Trial Legalized: Len = 50956.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.040827s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (114.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0184355
PHY-3002 : Step(123): len = 50421.8, overlap = 0.25
PHY-3002 : Step(124): len = 47697.4, overlap = 1.75
PHY-3002 : Step(125): len = 46879.9, overlap = 4
PHY-3002 : Step(126): len = 46515.8, overlap = 4.75
PHY-3002 : Step(127): len = 45825.4, overlap = 5.25
PHY-3002 : Step(128): len = 45239, overlap = 5.25
PHY-3002 : Step(129): len = 44732.7, overlap = 6.5
PHY-3002 : Step(130): len = 43961.6, overlap = 8
PHY-3002 : Step(131): len = 43546.5, overlap = 9.25
PHY-3002 : Step(132): len = 42967.8, overlap = 11.5
PHY-3002 : Step(133): len = 42561.7, overlap = 12.75
PHY-3002 : Step(134): len = 42125.4, overlap = 13
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0337766
PHY-3002 : Step(135): len = 42122.3, overlap = 13.25
PHY-3002 : Step(136): len = 42058.5, overlap = 12.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0675532
PHY-3002 : Step(137): len = 42043.2, overlap = 12.75
PHY-3002 : Step(138): len = 42021.1, overlap = 13.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004926s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (317.2%)

PHY-3001 : Legalized: Len = 46326.7, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004282s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 0, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 46482.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6002, tnet num: 1397, tinst num: 647, tnode num: 7516, tedge num: 10229.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 6/1399.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55832, over cnt = 157(0%), over = 262, worst = 7
PHY-1002 : len = 56888, over cnt = 93(0%), over = 130, worst = 3
PHY-1002 : len = 58248, over cnt = 22(0%), over = 34, worst = 3
PHY-1002 : len = 58392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.228290s wall, 0.281250s user + 0.062500s system = 0.343750s CPU (150.6%)

PHY-1001 : Congestion index: top1 = 28.92, top5 = 21.30, top10 = 16.21, top15 = 12.29.
PHY-1001 : End incremental global routing;  0.299321s wall, 0.343750s user + 0.062500s system = 0.406250s CPU (135.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.075129s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (83.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.401659s wall, 0.437500s user + 0.062500s system = 0.500000s CPU (124.5%)

OPT-1001 : Current memory(MB): used = 197, reserve = 151, peak = 199.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1223/1399.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004788s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 28.92, top5 = 21.30, top10 = 16.21, top15 = 12.29.
OPT-1001 : End congestion update;  0.047801s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.046882s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (100.0%)

OPT-0007 : Start: WNS 3961 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.094884s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (98.8%)

OPT-1001 : Current memory(MB): used = 199, reserve = 153, peak = 199.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.033886s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1223/1399.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.012181s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (128.3%)

PHY-1001 : Congestion index: top1 = 28.92, top5 = 21.30, top10 = 16.21, top15 = 12.29.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.041092s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (76.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3961 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 28.448276
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3961ps with logic level 6 
RUN-1001 :       #2 path slack 4040ps with logic level 6 
OPT-1001 : End physical optimization;  0.808438s wall, 0.812500s user + 0.078125s system = 0.890625s CPU (110.2%)

RUN-1003 : finish command "place" in  4.582074s wall, 6.468750s user + 2.562500s system = 9.031250s CPU (197.1%)

RUN-1004 : used memory is 183 MB, reserved memory is 139 MB, peak memory is 200 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 649 instances
RUN-1001 : 307 mslices, 306 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1399 nets
RUN-6004 WARNING: There are 7 nets with only 1 pin.
RUN-1001 : 698 nets have 2 pins
RUN-1001 : 506 nets have [3 - 5] pins
RUN-1001 : 112 nets have [6 - 10] pins
RUN-1001 : 44 nets have [11 - 20] pins
RUN-1001 : 27 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6002, tnet num: 1397, tinst num: 647, tnode num: 7516, tedge num: 10229.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 307 mslices, 306 lslices, 8 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55744, over cnt = 158(0%), over = 263, worst = 7
PHY-1002 : len = 56816, over cnt = 94(0%), over = 131, worst = 3
PHY-1002 : len = 57728, over cnt = 41(0%), over = 62, worst = 3
PHY-1002 : len = 58592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.203119s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (115.4%)

PHY-1001 : Congestion index: top1 = 28.88, top5 = 21.34, top10 = 16.20, top15 = 12.29.
PHY-1001 : End global routing;  0.274688s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (113.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 219, reserve = 173, peak = 219.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-5010 WARNING: Net CtrlData/cs_dacc is skipped due to 0 input or output
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net CtrlData/cs_dacc is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 478, reserve = 436, peak = 478.
PHY-1001 : End build detailed router design. 4.065988s wall, 4.015625s user + 0.046875s system = 4.062500s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 30160, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.805611s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 510, reserve = 469, peak = 510.
PHY-1001 : End phase 1; 0.811496s wall, 0.812500s user + 0.000000s system = 0.812500s CPU (100.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 34% nets.
PHY-1001 : Routed 42% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 195648, over cnt = 58(0%), over = 58, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 512, reserve = 471, peak = 513.
PHY-1001 : End initial routed; 1.926659s wall, 2.718750s user + 0.062500s system = 2.781250s CPU (144.4%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1242(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.497   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.251843s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.3%)

PHY-1001 : Current memory(MB): used = 514, reserve = 472, peak = 514.
PHY-1001 : End phase 2; 2.178594s wall, 2.968750s user + 0.062500s system = 3.031250s CPU (139.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 195648, over cnt = 58(0%), over = 58, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.011910s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (131.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 195344, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.058048s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (107.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 195368, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025573s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (122.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1242(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.497   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.245462s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (101.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 9 feed throughs used by 9 nets
PHY-1001 : End commit to database; 0.211143s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (96.2%)

PHY-1001 : Current memory(MB): used = 528, reserve = 487, peak = 529.
PHY-1001 : End phase 3; 0.705906s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.6%)

PHY-1003 : Routed, final wirelength = 195368
PHY-1001 : Current memory(MB): used = 529, reserve = 487, peak = 529.
PHY-1001 : End export database. 0.012691s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (123.1%)

PHY-1001 : End detail routing;  7.994082s wall, 8.718750s user + 0.109375s system = 8.828125s CPU (110.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6002, tnet num: 1397, tinst num: 647, tnode num: 7516, tedge num: 10229.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.795701s wall, 9.562500s user + 0.109375s system = 9.671875s CPU (110.0%)

RUN-1004 : used memory is 483 MB, reserved memory is 442 MB, peak memory is 529 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      948   out of  19600    4.84%
#reg                      622   out of  19600    3.17%
#le                      1151
  #lut only               529   out of   1151   45.96%
  #reg only               203   out of   1151   17.64%
  #lut&reg                419   out of   1151   36.40%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  23
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    294
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         106
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1151   |783     |165     |628     |23      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |111    |94      |8       |61      |0       |0       |
|    usms                            |Time_1ms        |26     |12      |5       |17      |0       |0       |
|  SPIM                              |SPI_MASTER      |187    |120     |23      |131     |0       |0       |
|  uart                              |UART_Control    |141    |125     |4       |53      |0       |0       |
|    U0                              |speed_select_Tx |22     |11      |4       |16      |0       |0       |
|    U1                              |uart_tx         |22     |19      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data       |97     |95      |0       |21      |0       |0       |
|  wendu                             |DS18B20         |215    |171     |43      |76      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |495    |271     |87      |301     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |495    |271     |87      |301     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |182    |78      |0       |173     |0       |0       |
|        reg_inst                    |register        |179    |76      |0       |170     |0       |0       |
|        tap_inst                    |tap             |3      |2       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |313    |193     |87      |128     |0       |0       |
|        bus_inst                    |bus_top         |74     |46      |26      |22      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |1      |0       |0       |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |73     |46      |26      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |133    |85      |29      |73      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       689   
    #2          2       294   
    #3          3       164   
    #4          4        48   
    #5        5-10      113   
    #6        11-50      69   
    #7       51-100      1    
    #8       101-500     1    
  Average     3.03            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6002, tnet num: 1397, tinst num: 647, tnode num: 7516, tedge num: 10229.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1397 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 588953dbe18b1871915f908614aff20f758057a2d1993c4931f4cee582d18853 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 647
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1399, pip num: 14379
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 9
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1293 valid insts, and 39513 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010011011001011011010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.371929s wall, 17.593750s user + 0.078125s system = 17.671875s CPU (524.1%)

RUN-1004 : used memory is 498 MB, reserved memory is 458 MB, peak memory is 647 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_112337.log"
