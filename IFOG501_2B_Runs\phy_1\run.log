============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Feb 10 15:46:33 2025

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 9 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 38 trigger nets, 38 data nets.
KIT-1004 : Chipwatcher code = 0101111100011010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2214/23 useful/useless nets, 1275/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 1899/20 useful/useless nets, 1682/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 378 better
SYN-1014 : Optimize round 2
SYN-1032 : 1595/45 useful/useless nets, 1378/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1643/295 useful/useless nets, 1459/43 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 383 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 36 instances.
SYN-2501 : Optimize round 1, 74 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2071/5 useful/useless nets, 1887/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8011, tnet num: 2071, tinst num: 1886, tnode num: 10140, tedge num: 12238.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2071 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 514 instances into 223 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 372 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 120 adder to BLE ...
SYN-4008 : Packed 120 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.092687s wall, 1.046875s user + 0.046875s system = 1.093750s CPU (100.1%)

RUN-1004 : used memory is 140 MB, reserved memory is 97 MB, peak memory is 159 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (255 clock/control pins, 0 other pins).
SYN-4027 : Net dq_dup_1 is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dq_dup_1 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1302 instances
RUN-0007 : 483 luts, 626 seqs, 86 mslices, 60 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1493 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 831 nets have 2 pins
RUN-1001 : 516 nets have [3 - 5] pins
RUN-1001 : 52 nets have [6 - 10] pins
RUN-1001 : 58 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     146     
RUN-1001 :   No   |  No   |  Yes  |     118     
RUN-1001 :   No   |  Yes  |  No   |     79      
RUN-1001 :   Yes  |  No   |  No   |     32      
RUN-1001 :   Yes  |  No   |  Yes  |     251     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   6   |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 16
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1300 instances, 483 luts, 626 seqs, 146 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-0007 : Cell area utilization is 3%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6744, tnet num: 1491, tinst num: 1300, tnode num: 8968, tedge num: 11211.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.127610s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (98.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 346482
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1300.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 3%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 282353, overlap = 76.5
PHY-3002 : Step(2): len = 231785, overlap = 76.5
PHY-3002 : Step(3): len = 204798, overlap = 76.5
PHY-3002 : Step(4): len = 182202, overlap = 76.5
PHY-3002 : Step(5): len = 163018, overlap = 76.5
PHY-3002 : Step(6): len = 144444, overlap = 76.5
PHY-3002 : Step(7): len = 129385, overlap = 76.5
PHY-3002 : Step(8): len = 116930, overlap = 76.5
PHY-3002 : Step(9): len = 101187, overlap = 76.5
PHY-3002 : Step(10): len = 90066.2, overlap = 76.5
PHY-3002 : Step(11): len = 83299.9, overlap = 76.5
PHY-3002 : Step(12): len = 73025.7, overlap = 76.5
PHY-3002 : Step(13): len = 66963.1, overlap = 76.5
PHY-3002 : Step(14): len = 62569.4, overlap = 76.5
PHY-3002 : Step(15): len = 55552, overlap = 76.5
PHY-3002 : Step(16): len = 51313, overlap = 76.5
PHY-3002 : Step(17): len = 47447.7, overlap = 76.5
PHY-3002 : Step(18): len = 43574, overlap = 76.5
PHY-3002 : Step(19): len = 40911.1, overlap = 76.5
PHY-3002 : Step(20): len = 39197.5, overlap = 76.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.85803e-06
PHY-3002 : Step(21): len = 38950, overlap = 72
PHY-3002 : Step(22): len = 39393.6, overlap = 76.5
PHY-3002 : Step(23): len = 38303.3, overlap = 72
PHY-3002 : Step(24): len = 37873.6, overlap = 72
PHY-3002 : Step(25): len = 37109.7, overlap = 72
PHY-3002 : Step(26): len = 36917.6, overlap = 72
PHY-3002 : Step(27): len = 36090.3, overlap = 67.5
PHY-3002 : Step(28): len = 34964.4, overlap = 67.5625
PHY-3002 : Step(29): len = 34058.7, overlap = 67.625
PHY-3002 : Step(30): len = 33886, overlap = 67.5625
PHY-3002 : Step(31): len = 33190.6, overlap = 69.875
PHY-3002 : Step(32): len = 32645.2, overlap = 69.875
PHY-3002 : Step(33): len = 32157.7, overlap = 69.75
PHY-3002 : Step(34): len = 30792.2, overlap = 67.5
PHY-3002 : Step(35): len = 29879.7, overlap = 67.5
PHY-3002 : Step(36): len = 29642, overlap = 67.5
PHY-3002 : Step(37): len = 29594.1, overlap = 67.5
PHY-3002 : Step(38): len = 29275.2, overlap = 67.5
PHY-3002 : Step(39): len = 29185.6, overlap = 67.5
PHY-3002 : Step(40): len = 28964.7, overlap = 67.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 3.71606e-06
PHY-3002 : Step(41): len = 28987.5, overlap = 67.5
PHY-3002 : Step(42): len = 29009.3, overlap = 67.5
PHY-3002 : Step(43): len = 28988.3, overlap = 67.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 7.43212e-06
PHY-3002 : Step(44): len = 29141.1, overlap = 69.75
PHY-3002 : Step(45): len = 29163.6, overlap = 69.75
PHY-3002 : Step(46): len = 29300.3, overlap = 69.75
PHY-3002 : Step(47): len = 29337, overlap = 69.75
PHY-3002 : Step(48): len = 29172.7, overlap = 67.5
PHY-3002 : Step(49): len = 29228.7, overlap = 63
PHY-3002 : Step(50): len = 29236.1, overlap = 63
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004515s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (346.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.036732s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000745678
PHY-3002 : Step(51): len = 35693.5, overlap = 13.9688
PHY-3002 : Step(52): len = 35754.9, overlap = 13.875
PHY-3002 : Step(53): len = 35930, overlap = 12.9688
PHY-3002 : Step(54): len = 35926.4, overlap = 12.5
PHY-3002 : Step(55): len = 36050.4, overlap = 12.5938
PHY-3002 : Step(56): len = 36190.4, overlap = 12.4688
PHY-3002 : Step(57): len = 36004.9, overlap = 12.2812
PHY-3002 : Step(58): len = 36027.3, overlap = 12.5312
PHY-3002 : Step(59): len = 36014.8, overlap = 12.1562
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.036238s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (129.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.6053e-05
PHY-3002 : Step(60): len = 36360.7, overlap = 44.0312
PHY-3002 : Step(61): len = 37081.9, overlap = 42.5938
PHY-3002 : Step(62): len = 37982.9, overlap = 38.1562
PHY-3002 : Step(63): len = 38949.6, overlap = 32.3125
PHY-3002 : Step(64): len = 38998.4, overlap = 31.7812
PHY-3002 : Step(65): len = 39028, overlap = 31.0312
PHY-3002 : Step(66): len = 38850, overlap = 29
PHY-3002 : Step(67): len = 38802.9, overlap = 25.75
PHY-3002 : Step(68): len = 38757.8, overlap = 25.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000172106
PHY-3002 : Step(69): len = 38701.6, overlap = 23.5625
PHY-3002 : Step(70): len = 38745, overlap = 23.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000344212
PHY-3002 : Step(71): len = 39113.6, overlap = 22.2812
PHY-3002 : Step(72): len = 39173.4, overlap = 21.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6744, tnet num: 1491, tinst num: 1300, tnode num: 8968, tedge num: 11211.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 76.12 peak overflow 2.66
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1493.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51456, over cnt = 223(0%), over = 811, worst = 16
PHY-1001 : End global iterations;  0.081076s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.4%)

PHY-1001 : Congestion index: top1 = 36.06, top5 = 22.27, top10 = 14.79, top15 = 10.61.
PHY-1001 : End incremental global routing;  0.128789s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (97.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044630s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (105.0%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1288 has valid locations, 34 needs to be replaced
PHY-3001 : design contains 1333 instances, 483 luts, 659 seqs, 146 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 39668.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6876, tnet num: 1524, tinst num: 1333, tnode num: 9199, tedge num: 11409.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.139013s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (101.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(73): len = 40309.5, overlap = 1.9375
PHY-3002 : Step(74): len = 40601.1, overlap = 1.9375
PHY-3002 : Step(75): len = 40934.1, overlap = 1.9375
PHY-3002 : Step(76): len = 41007.3, overlap = 1.9375
PHY-3002 : Step(77): len = 40994.7, overlap = 1.9375
PHY-3002 : Step(78): len = 40988.1, overlap = 1.9375
PHY-3002 : Step(79): len = 40987.6, overlap = 1.9375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.038847s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (120.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00438411
PHY-3002 : Step(80): len = 41013.7, overlap = 21.8438
PHY-3002 : Step(81): len = 40996.1, overlap = 21.9688
PHY-3001 : Final: Len = 40996.1, Over = 21.9688
PHY-3001 : End incremental placement;  0.293478s wall, 0.312500s user + 0.062500s system = 0.375000s CPU (127.8%)

OPT-1001 : Total overflow 77.00 peak overflow 2.66
OPT-1001 : End high-fanout net optimization;  0.497026s wall, 0.625000s user + 0.062500s system = 0.687500s CPU (138.3%)

OPT-1001 : Current memory(MB): used = 196, reserve = 152, peak = 196.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1059/1526.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53576, over cnt = 226(0%), over = 797, worst = 16
PHY-1002 : len = 59848, over cnt = 142(0%), over = 225, worst = 9
PHY-1002 : len = 61064, over cnt = 43(0%), over = 69, worst = 5
PHY-1002 : len = 61488, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 61584, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.108211s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (101.1%)

PHY-1001 : Congestion index: top1 = 33.28, top5 = 22.36, top10 = 16.01, top15 = 11.90.
OPT-1001 : End congestion update;  0.151267s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (93.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.040645s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (115.3%)

OPT-0007 : Start: WNS 4823 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.192127s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (97.6%)

OPT-1001 : Current memory(MB): used = 195, reserve = 151, peak = 196.
OPT-1001 : End physical optimization;  0.805397s wall, 0.937500s user + 0.062500s system = 1.000000s CPU (124.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 483 LUT to BLE ...
SYN-4008 : Packed 483 LUT and 162 SEQ to BLE.
SYN-4003 : Packing 497 remaining SEQ's ...
SYN-4005 : Packed 252 SEQ with LUT/SLICE
SYN-4006 : 99 single LUT's are left
SYN-4006 : 245 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 728/1055 primitive instances ...
PHY-3001 : End packing;  0.039654s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (78.8%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 606 instances
RUN-1001 : 279 mslices, 280 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1365 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 654 nets have 2 pins
RUN-1001 : 561 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 53 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 604 instances, 559 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 42474.2, Over = 36.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6073, tnet num: 1363, tinst num: 604, tnode num: 7792, tedge num: 10395.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.157160s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (99.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.2175e-05
PHY-3002 : Step(82): len = 42154.6, overlap = 37.5
PHY-3002 : Step(83): len = 42179.3, overlap = 37
PHY-3002 : Step(84): len = 41927.4, overlap = 37
PHY-3002 : Step(85): len = 41902.5, overlap = 34.75
PHY-3002 : Step(86): len = 42025.9, overlap = 35.75
PHY-3002 : Step(87): len = 42056.5, overlap = 37.25
PHY-3002 : Step(88): len = 41860.1, overlap = 38
PHY-3002 : Step(89): len = 41911.9, overlap = 37.75
PHY-3002 : Step(90): len = 41648, overlap = 37.75
PHY-3002 : Step(91): len = 41502.4, overlap = 38
PHY-3002 : Step(92): len = 41367.7, overlap = 35.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00010435
PHY-3002 : Step(93): len = 41376.2, overlap = 36.25
PHY-3002 : Step(94): len = 41582.2, overlap = 36.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0002087
PHY-3002 : Step(95): len = 41864.9, overlap = 36.75
PHY-3002 : Step(96): len = 42273, overlap = 35
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.082148s wall, 0.062500s user + 0.156250s system = 0.218750s CPU (266.3%)

PHY-3001 : Trial Legalized: Len = 54940.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.036803s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00823789
PHY-3002 : Step(97): len = 52800.7, overlap = 2.5
PHY-3002 : Step(98): len = 50597.8, overlap = 6.5
PHY-3002 : Step(99): len = 49686.1, overlap = 5.25
PHY-3002 : Step(100): len = 48643, overlap = 8.25
PHY-3002 : Step(101): len = 48136.4, overlap = 8.75
PHY-3002 : Step(102): len = 47358.4, overlap = 11.75
PHY-3002 : Step(103): len = 47002.9, overlap = 14
PHY-3002 : Step(104): len = 46621.6, overlap = 14
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0164758
PHY-3002 : Step(105): len = 46658.9, overlap = 13.75
PHY-3002 : Step(106): len = 46564.5, overlap = 14.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0329516
PHY-3002 : Step(107): len = 46556.8, overlap = 14
PHY-3002 : Step(108): len = 46456.5, overlap = 14.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004726s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 50615.8, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004186s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (373.2%)

PHY-3001 : 3 instances has been re-located, deltaX = 0, deltaY = 4, maxDist = 2.
PHY-3001 : Final: Len = 50649.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6073, tnet num: 1363, tinst num: 604, tnode num: 7792, tedge num: 10395.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 36/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65400, over cnt = 188(0%), over = 308, worst = 4
PHY-1002 : len = 66800, over cnt = 78(0%), over = 103, worst = 4
PHY-1002 : len = 67696, over cnt = 34(0%), over = 36, worst = 2
PHY-1002 : len = 68016, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 68168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.159924s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (146.6%)

PHY-1001 : Congestion index: top1 = 29.40, top5 = 21.91, top10 = 17.08, top15 = 13.15.
PHY-1001 : End incremental global routing;  0.207734s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (135.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045255s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.277711s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (123.8%)

OPT-1001 : Current memory(MB): used = 198, reserve = 154, peak = 198.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1191/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005410s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (288.8%)

PHY-1001 : Congestion index: top1 = 29.40, top5 = 21.91, top10 = 17.08, top15 = 13.15.
OPT-1001 : End congestion update;  0.049022s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (127.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.034604s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.3%)

OPT-0007 : Start: WNS 5215 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.083864s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (111.8%)

OPT-1001 : Current memory(MB): used = 200, reserve = 157, peak = 200.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.035194s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (88.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1191/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004860s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 29.40, top5 = 21.91, top10 = 17.08, top15 = 13.15.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.034219s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.3%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 5215 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 28.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 5215ps with logic level 7 
RUN-1001 :       #2 path slack 5271ps with logic level 7 
OPT-1001 : End physical optimization;  0.613722s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (124.8%)

RUN-1003 : finish command "place" in  3.629442s wall, 5.000000s user + 1.781250s system = 6.781250s CPU (186.8%)

RUN-1004 : used memory is 191 MB, reserved memory is 146 MB, peak memory is 201 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 606 instances
RUN-1001 : 279 mslices, 280 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1365 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 654 nets have 2 pins
RUN-1001 : 561 nets have [3 - 5] pins
RUN-1001 : 58 nets have [6 - 10] pins
RUN-1001 : 53 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6073, tnet num: 1363, tinst num: 604, tnode num: 7792, tedge num: 10395.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 279 mslices, 280 lslices, 8 pads, 34 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64840, over cnt = 194(0%), over = 325, worst = 5
PHY-1002 : len = 66496, over cnt = 75(0%), over = 100, worst = 4
PHY-1002 : len = 67456, over cnt = 22(0%), over = 30, worst = 3
PHY-1002 : len = 67840, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146929s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (117.0%)

PHY-1001 : Congestion index: top1 = 29.70, top5 = 21.94, top10 = 17.08, top15 = 13.12.
PHY-1001 : End global routing;  0.193857s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (112.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 218, reserve = 175, peak = 218.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net dq_dup_1 will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 476, reserve = 436, peak = 476.
PHY-1001 : End build detailed router design. 3.108643s wall, 3.015625s user + 0.093750s system = 3.109375s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 28712, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.596625s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 508, reserve = 469, peak = 508.
PHY-1001 : End phase 1; 0.602369s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (98.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 246600, over cnt = 47(0%), over = 47, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 511, reserve = 471, peak = 512.
PHY-1001 : End initial routed; 2.724176s wall, 3.671875s user + 0.109375s system = 3.781250s CPU (138.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1228(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.397   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.200981s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 512, reserve = 472, peak = 512.
PHY-1001 : End phase 2; 2.925244s wall, 3.875000s user + 0.109375s system = 3.984375s CPU (136.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 246600, over cnt = 47(0%), over = 47, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.011473s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (136.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 246344, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.041644s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (112.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 246360, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.030289s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (103.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1228(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   2.397   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.190058s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 7 feed throughs used by 7 nets
PHY-1001 : End commit to database; 0.177739s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.7%)

PHY-1001 : Current memory(MB): used = 527, reserve = 487, peak = 527.
PHY-1001 : End phase 3; 0.565307s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 246360
PHY-1001 : Current memory(MB): used = 527, reserve = 487, peak = 527.
PHY-1001 : End export database. 0.009913s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (157.6%)

PHY-1001 : End detail routing;  7.387620s wall, 8.234375s user + 0.203125s system = 8.437500s CPU (114.2%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6073, tnet num: 1363, tinst num: 604, tnode num: 7792, tedge num: 10395.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.960122s wall, 8.843750s user + 0.203125s system = 9.046875s CPU (113.7%)

RUN-1004 : used memory is 484 MB, reserved memory is 443 MB, peak memory is 527 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      806   out of  19600    4.11%
#reg                      672   out of  19600    3.43%
#le                      1048
  #lut only               376   out of   1048   35.88%
  #reg only               242   out of   1048   23.09%
  #lut&reg                430   out of   1048   41.03%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     0
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        dq_dup_1             GCLK               pll                CLK120/pll_inst.clkc0    297
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         140
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
   miso        INPUT         A4        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         A2        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         A8        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         A6        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8            NONE       NONE    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1048   |660     |146     |677     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |100    |78      |11      |64      |0       |0       |
|    usms                            |Time_1ms        |26     |12      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |202    |129     |23      |129     |0       |0       |
|  uart                              |UART_Control    |117    |106     |7       |55      |0       |0       |
|    U0                              |speed_select_Tx |23     |14      |7       |16      |0       |0       |
|    U1                              |uart_tx         |29     |27      |0       |17      |0       |0       |
|    U2                              |Ctrl_Data       |65     |65      |0       |22      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |597    |330     |99      |403     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |597    |330     |99      |403     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |250    |119     |0       |244     |0       |0       |
|        reg_inst                    |register        |247    |116     |0       |241     |0       |0       |
|        tap_inst                    |tap             |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger         |347    |211     |99      |159     |0       |0       |
|        bus_inst                    |bus_top         |121    |76      |42      |42      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |15     |8       |6       |4       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |29     |19      |10      |12      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |72     |44      |26      |21      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |141    |85      |29      |86      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       645   
    #2          2       332   
    #3          3       177   
    #4          4        52   
    #5        5-10       66   
    #6        11-50      70   
    #7       51-100      3    
    #8       101-500     1    
  Average     3.15            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6073, tnet num: 1363, tinst num: 604, tnode num: 7792, tedge num: 10395.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 604
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1365, pip num: 15704
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1372 valid insts, and 40911 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011001110101111100011010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.140121s wall, 18.234375s user + 0.109375s system = 18.343750s CPU (584.2%)

RUN-1004 : used memory is 499 MB, reserved memory is 461 MB, peak memory is 643 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20250210_154633.log"
RUN-1001 : Backing up run's log file succeed.
