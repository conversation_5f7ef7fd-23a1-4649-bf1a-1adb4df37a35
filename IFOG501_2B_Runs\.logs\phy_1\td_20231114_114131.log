============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Nov 14 11:41:31 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1549 instances
RUN-0007 : 374 luts, 922 seqs, 129 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2090 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1547 nets have 2 pins
RUN-1001 : 428 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     269     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1547 instances, 374 luts, 922 seqs, 204 slices, 22 macros(204 instances: 129 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7376, tnet num: 2088, tinst num: 1547, tnode num: 10366, tedge num: 12475.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2088 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.317536s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (103.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 542830
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1547.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 461375, overlap = 15.75
PHY-3002 : Step(2): len = 414976, overlap = 15.75
PHY-3002 : Step(3): len = 398325, overlap = 20.25
PHY-3002 : Step(4): len = 378104, overlap = 13.5
PHY-3002 : Step(5): len = 370481, overlap = 20.25
PHY-3002 : Step(6): len = 363605, overlap = 18
PHY-3002 : Step(7): len = 345405, overlap = 20.25
PHY-3002 : Step(8): len = 339177, overlap = 15.75
PHY-3002 : Step(9): len = 332178, overlap = 15.75
PHY-3002 : Step(10): len = 317179, overlap = 15.75
PHY-3002 : Step(11): len = 309311, overlap = 15.75
PHY-3002 : Step(12): len = 305106, overlap = 15.75
PHY-3002 : Step(13): len = 295883, overlap = 15.75
PHY-3002 : Step(14): len = 288094, overlap = 13.5
PHY-3002 : Step(15): len = 283863, overlap = 11.25
PHY-3002 : Step(16): len = 277474, overlap = 13.5
PHY-3002 : Step(17): len = 268435, overlap = 11.25
PHY-3002 : Step(18): len = 264416, overlap = 13.5
PHY-3002 : Step(19): len = 259529, overlap = 11.25
PHY-3002 : Step(20): len = 254223, overlap = 13.5
PHY-3002 : Step(21): len = 248030, overlap = 11.25
PHY-3002 : Step(22): len = 244363, overlap = 13.5
PHY-3002 : Step(23): len = 239166, overlap = 11.25
PHY-3002 : Step(24): len = 232479, overlap = 13.5
PHY-3002 : Step(25): len = 226208, overlap = 11.25
PHY-3002 : Step(26): len = 223595, overlap = 13.5
PHY-3002 : Step(27): len = 216557, overlap = 11.25
PHY-3002 : Step(28): len = 208426, overlap = 13.5
PHY-3002 : Step(29): len = 204782, overlap = 13.5
PHY-3002 : Step(30): len = 200965, overlap = 13.5
PHY-3002 : Step(31): len = 145774, overlap = 13.5
PHY-3002 : Step(32): len = 141861, overlap = 13.5
PHY-3002 : Step(33): len = 139630, overlap = 13.5
PHY-3002 : Step(34): len = 122049, overlap = 13.5
PHY-3002 : Step(35): len = 120315, overlap = 15.75
PHY-3002 : Step(36): len = 117511, overlap = 13.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000121362
PHY-3002 : Step(37): len = 117957, overlap = 15.75
PHY-3002 : Step(38): len = 117255, overlap = 13.5
PHY-3002 : Step(39): len = 116267, overlap = 11.25
PHY-3002 : Step(40): len = 115670, overlap = 13.5
PHY-3002 : Step(41): len = 114669, overlap = 13.5
PHY-3002 : Step(42): len = 112187, overlap = 15.75
PHY-3002 : Step(43): len = 109996, overlap = 13.5
PHY-3002 : Step(44): len = 108025, overlap = 15.75
PHY-3002 : Step(45): len = 106651, overlap = 13.5
PHY-3002 : Step(46): len = 104115, overlap = 13.5
PHY-3002 : Step(47): len = 102126, overlap = 13.5
PHY-3002 : Step(48): len = 100797, overlap = 13.5
PHY-3002 : Step(49): len = 98124.1, overlap = 13.5
PHY-3002 : Step(50): len = 95186.6, overlap = 13.5
PHY-3002 : Step(51): len = 93843.8, overlap = 13.5
PHY-3002 : Step(52): len = 92938.1, overlap = 13.5
PHY-3002 : Step(53): len = 90554.9, overlap = 13.5
PHY-3002 : Step(54): len = 87416.9, overlap = 15.75
PHY-3002 : Step(55): len = 85929.3, overlap = 15.75
PHY-3002 : Step(56): len = 84537.2, overlap = 13.5
PHY-3002 : Step(57): len = 81733.6, overlap = 13.5
PHY-3002 : Step(58): len = 81109.2, overlap = 13.5
PHY-3002 : Step(59): len = 79576.6, overlap = 13.5
PHY-3002 : Step(60): len = 76158.4, overlap = 11.25
PHY-3002 : Step(61): len = 75253.9, overlap = 11.25
PHY-3002 : Step(62): len = 74518.8, overlap = 11.25
PHY-3002 : Step(63): len = 72910.1, overlap = 11.375
PHY-3002 : Step(64): len = 72246.4, overlap = 11.375
PHY-3002 : Step(65): len = 71975.8, overlap = 11.5625
PHY-3002 : Step(66): len = 71852.7, overlap = 11.875
PHY-3002 : Step(67): len = 71239.8, overlap = 11.875
PHY-3002 : Step(68): len = 71018.7, overlap = 16.25
PHY-3002 : Step(69): len = 70268.9, overlap = 14.25
PHY-3002 : Step(70): len = 69798, overlap = 12.125
PHY-3002 : Step(71): len = 68792.3, overlap = 12.4375
PHY-3002 : Step(72): len = 67828.4, overlap = 14.6875
PHY-3002 : Step(73): len = 66380.7, overlap = 12.4375
PHY-3002 : Step(74): len = 66275.2, overlap = 12.5
PHY-3002 : Step(75): len = 64396.5, overlap = 12.8125
PHY-3002 : Step(76): len = 63819, overlap = 14.875
PHY-3002 : Step(77): len = 62715.1, overlap = 14.625
PHY-3002 : Step(78): len = 61604.1, overlap = 12.3125
PHY-3002 : Step(79): len = 60201.9, overlap = 12.375
PHY-3002 : Step(80): len = 58218.7, overlap = 14.5625
PHY-3002 : Step(81): len = 58175, overlap = 14.6875
PHY-3002 : Step(82): len = 57865, overlap = 14.625
PHY-3002 : Step(83): len = 57287.2, overlap = 17
PHY-3002 : Step(84): len = 57359.3, overlap = 17.125
PHY-3002 : Step(85): len = 56755.5, overlap = 16.9375
PHY-3002 : Step(86): len = 55007, overlap = 16.875
PHY-3002 : Step(87): len = 54994.8, overlap = 14.625
PHY-3002 : Step(88): len = 54743.7, overlap = 12.4375
PHY-3002 : Step(89): len = 54491.3, overlap = 12.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000242724
PHY-3002 : Step(90): len = 54724.6, overlap = 12.125
PHY-3002 : Step(91): len = 54652.4, overlap = 12.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000485447
PHY-3002 : Step(92): len = 54783.2, overlap = 12.125
PHY-3002 : Step(93): len = 54825.7, overlap = 14.3125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.009603s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (162.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2088 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.072452s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000252741
PHY-3002 : Step(94): len = 58372.8, overlap = 17.2812
PHY-3002 : Step(95): len = 58330.9, overlap = 17.1562
PHY-3002 : Step(96): len = 57271, overlap = 16.0938
PHY-3002 : Step(97): len = 57089.8, overlap = 15.5938
PHY-3002 : Step(98): len = 55847.8, overlap = 13.8438
PHY-3002 : Step(99): len = 55206.9, overlap = 14.5625
PHY-3002 : Step(100): len = 54422.8, overlap = 14.75
PHY-3002 : Step(101): len = 53480.3, overlap = 15.875
PHY-3002 : Step(102): len = 52122.8, overlap = 15.375
PHY-3002 : Step(103): len = 51580.4, overlap = 16.0625
PHY-3002 : Step(104): len = 51033.6, overlap = 20.3125
PHY-3002 : Step(105): len = 50355.7, overlap = 22.2188
PHY-3002 : Step(106): len = 50028.9, overlap = 20.9062
PHY-3002 : Step(107): len = 49663.6, overlap = 20.9688
PHY-3002 : Step(108): len = 49130.5, overlap = 21.0625
PHY-3002 : Step(109): len = 48607.1, overlap = 20.4062
PHY-3002 : Step(110): len = 48546.4, overlap = 18.7188
PHY-3002 : Step(111): len = 48274.7, overlap = 17.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000505482
PHY-3002 : Step(112): len = 48147.1, overlap = 17.5312
PHY-3002 : Step(113): len = 48028, overlap = 16.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00101096
PHY-3002 : Step(114): len = 48023.1, overlap = 16.8438
PHY-3002 : Step(115): len = 48097, overlap = 16.8438
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2088 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068214s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.9314e-05
PHY-3002 : Step(116): len = 48276.2, overlap = 48.7812
PHY-3002 : Step(117): len = 49418.4, overlap = 47.0938
PHY-3002 : Step(118): len = 50022.8, overlap = 48.9688
PHY-3002 : Step(119): len = 49352, overlap = 42.125
PHY-3002 : Step(120): len = 49135.5, overlap = 39.3438
PHY-3002 : Step(121): len = 49027.7, overlap = 39.4688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000138628
PHY-3002 : Step(122): len = 49281, overlap = 39.5312
PHY-3002 : Step(123): len = 49947.6, overlap = 39.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000277256
PHY-3002 : Step(124): len = 49995.1, overlap = 38.6562
PHY-3002 : Step(125): len = 50911.4, overlap = 40.4062
PHY-3002 : Step(126): len = 52367.8, overlap = 37.625
PHY-3002 : Step(127): len = 53179, overlap = 33.3438
PHY-3002 : Step(128): len = 53106.5, overlap = 31.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000554512
PHY-3002 : Step(129): len = 53043.9, overlap = 30.8438
PHY-3002 : Step(130): len = 53332.2, overlap = 31.5938
PHY-3002 : Step(131): len = 53349.7, overlap = 31.1562
PHY-3002 : Step(132): len = 53319.1, overlap = 29.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7376, tnet num: 2088, tinst num: 1547, tnode num: 10366, tedge num: 12475.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 81.12 peak overflow 1.97
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2090.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56192, over cnt = 259(0%), over = 1014, worst = 18
PHY-1001 : End global iterations;  0.091214s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.8%)

PHY-1001 : Congestion index: top1 = 43.28, top5 = 26.52, top10 = 16.60, top15 = 11.71.
PHY-1001 : End incremental global routing;  0.153147s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (112.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2088 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074801s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (83.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.262993s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (106.9%)

OPT-1001 : Current memory(MB): used = 211, reserve = 174, peak = 211.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1574/2090.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56192, over cnt = 259(0%), over = 1014, worst = 18
PHY-1002 : len = 62624, over cnt = 161(0%), over = 376, worst = 14
PHY-1002 : len = 66912, over cnt = 27(0%), over = 34, worst = 5
PHY-1002 : len = 67344, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 67584, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.147151s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (116.8%)

PHY-1001 : Congestion index: top1 = 37.72, top5 = 25.98, top10 = 18.35, top15 = 13.38.
OPT-1001 : End congestion update;  0.219783s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (106.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2088 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071949s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (86.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.295801s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (105.6%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : End physical optimization;  0.889978s wall, 0.906250s user + 0.015625s system = 0.921875s CPU (103.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 374 LUT to BLE ...
SYN-4008 : Packed 374 LUT and 174 SEQ to BLE.
SYN-4003 : Packing 748 remaining SEQ's ...
SYN-4005 : Packed 120 SEQ with LUT/SLICE
SYN-4006 : 99 single LUT's are left
SYN-4006 : 628 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1002/1289 primitive instances ...
PHY-3001 : End packing;  0.053826s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 787 instances
RUN-1001 : 369 mslices, 369 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1924 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1376 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 785 instances, 738 slices, 22 macros(204 instances: 129 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 53111, Over = 58.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6235, tnet num: 1922, tinst num: 785, tnode num: 8400, tedge num: 10964.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.362184s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.85471e-05
PHY-3002 : Step(133): len = 52186.7, overlap = 57.75
PHY-3002 : Step(134): len = 51402.9, overlap = 58.25
PHY-3002 : Step(135): len = 50828.6, overlap = 59.75
PHY-3002 : Step(136): len = 50855.9, overlap = 60.5
PHY-3002 : Step(137): len = 50807.4, overlap = 59.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.70943e-05
PHY-3002 : Step(138): len = 51237.5, overlap = 58.5
PHY-3002 : Step(139): len = 51911.1, overlap = 59.25
PHY-3002 : Step(140): len = 52341.2, overlap = 57.25
PHY-3002 : Step(141): len = 52724.3, overlap = 55
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000114189
PHY-3002 : Step(142): len = 53207.1, overlap = 53.75
PHY-3002 : Step(143): len = 53810.7, overlap = 48
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.091410s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (239.3%)

PHY-3001 : Trial Legalized: Len = 67542.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.070438s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (110.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000621899
PHY-3002 : Step(144): len = 64205.4, overlap = 8
PHY-3002 : Step(145): len = 62181, overlap = 11.75
PHY-3002 : Step(146): len = 60401.5, overlap = 11.25
PHY-3002 : Step(147): len = 59131.1, overlap = 13.5
PHY-3002 : Step(148): len = 58795.5, overlap = 16
PHY-3002 : Step(149): len = 58641.4, overlap = 16.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0012438
PHY-3002 : Step(150): len = 58771.8, overlap = 15.75
PHY-3002 : Step(151): len = 58949.1, overlap = 15.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00248759
PHY-3002 : Step(152): len = 59084.1, overlap = 14.75
PHY-3002 : Step(153): len = 59141.4, overlap = 15
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005913s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63600, Over = 0
PHY-3001 : Spreading special nets. 9 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006581s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 15 instances has been re-located, deltaX = 4, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 63822, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6235, tnet num: 1922, tinst num: 785, tnode num: 8400, tedge num: 10964.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 46/1924.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69832, over cnt = 157(0%), over = 232, worst = 7
PHY-1002 : len = 70984, over cnt = 80(0%), over = 99, worst = 3
PHY-1002 : len = 71984, over cnt = 14(0%), over = 20, worst = 3
PHY-1002 : len = 72352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.162063s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (106.1%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.91, top10 = 17.88, top15 = 13.95.
PHY-1001 : End incremental global routing;  0.224879s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (111.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.074990s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (83.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.341236s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (105.3%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1695/1924.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008468s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (184.5%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.91, top10 = 17.88, top15 = 13.95.
OPT-1001 : End congestion update;  0.061345s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055513s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (84.4%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 747 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 785 instances, 738 slices, 22 macros(204 instances: 129 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 63869, Over = 0
PHY-3001 : End spreading;  0.007708s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63869, Over = 0
PHY-3001 : End incremental legalization;  0.046091s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (67.8%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.182823s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (94.0%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056542s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (82.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1691/1924.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72376, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72376, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72392, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.027528s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (113.5%)

PHY-1001 : Congestion index: top1 = 31.75, top5 = 22.91, top10 = 17.89, top15 = 13.97.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059556s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.413793
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.193384s wall, 1.156250s user + 0.046875s system = 1.203125s CPU (100.8%)

RUN-1003 : finish command "place" in  6.840338s wall, 10.015625s user + 4.078125s system = 14.093750s CPU (206.0%)

RUN-1004 : used memory is 200 MB, reserved memory is 163 MB, peak memory is 221 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 787 instances
RUN-1001 : 369 mslices, 369 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1924 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1376 nets have 2 pins
RUN-1001 : 432 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6235, tnet num: 1922, tinst num: 785, tnode num: 8400, tedge num: 10964.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 369 mslices, 369 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1922 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69248, over cnt = 163(0%), over = 235, worst = 7
PHY-1002 : len = 70408, over cnt = 89(0%), over = 107, worst = 3
PHY-1002 : len = 71760, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 71904, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.164470s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (104.5%)

PHY-1001 : Congestion index: top1 = 31.36, top5 = 22.73, top10 = 17.80, top15 = 13.87.
PHY-1001 : End global routing;  0.225527s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (110.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 199, peak = 237.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 497, reserve = 465, peak = 497.
PHY-1001 : End build detailed router design. 3.838715s wall, 3.718750s user + 0.109375s system = 3.828125s CPU (99.7%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33776, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.744584s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 529, reserve = 500, peak = 529.
PHY-1001 : End phase 1; 1.751318s wall, 1.734375s user + 0.000000s system = 1.734375s CPU (99.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181376, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 530, reserve = 501, peak = 530.
PHY-1001 : End initial routed; 1.826565s wall, 2.718750s user + 0.218750s system = 2.937500s CPU (160.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1705(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.372   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.543   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.426613s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 531, reserve = 501, peak = 531.
PHY-1001 : End phase 2; 2.253280s wall, 3.140625s user + 0.218750s system = 3.359375s CPU (149.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181376, over cnt = 31(0%), over = 31, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017926s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (87.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181344, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.033079s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (141.7%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181384, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.025605s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (122.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1705(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.372   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.559   |   5   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.428429s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (98.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.196761s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.2%)

PHY-1001 : Current memory(MB): used = 546, reserve = 515, peak = 546.
PHY-1001 : End phase 3; 0.847883s wall, 0.843750s user + 0.000000s system = 0.843750s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 181384
PHY-1001 : Current memory(MB): used = 546, reserve = 515, peak = 546.
PHY-1001 : End export database. 0.011927s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (131.0%)

PHY-1001 : End detail routing;  8.928744s wall, 9.656250s user + 0.328125s system = 9.984375s CPU (111.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6235, tnet num: 1922, tinst num: 785, tnode num: 8400, tedge num: 10964.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[40] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[47] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[2] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -70ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg1_syn_21.sr slack -97ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6273, tnet num: 1941, tinst num: 804, tnode num: 8438, tedge num: 11002.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.862032s wall, 4.000000s user + 0.140625s system = 4.140625s CPU (107.2%)

RUN-1003 : finish command "route" in  13.423348s wall, 14.328125s user + 0.468750s system = 14.796875s CPU (110.2%)

RUN-1004 : used memory is 537 MB, reserved memory is 510 MB, peak memory is 546 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      826   out of  19600    4.21%
#reg                      989   out of  19600    5.05%
#le                      1454
  #lut only               465   out of   1454   31.98%
  #reg only               628   out of   1454   43.19%
  #lut&reg                361   out of   1454   24.83%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         434
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         100
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    42
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1454   |622     |204     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1053   |315     |111     |837     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |23     |17      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |468    |136     |44      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |58     |46      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |13      |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |16      |0       |17      |0       |0       |
|    integ                   |Integration                                      |138    |20      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |90     |26      |13      |88      |0       |1       |
|    rs422                   |Rs422Output                                      |304    |91      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |112    |104     |7       |58      |0       |0       |
|    U0                      |speed_select_Tx                                  |29     |21      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |23     |23      |0       |18      |0       |0       |
|    U2                      |Ctrl_Data                                        |60     |60      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |206    |161     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1359  
    #2          2       299   
    #3          3       121   
    #4          4        12   
    #5        5-10       80   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6273, tnet num: 1941, tinst num: 804, tnode num: 8438, tedge num: 11002.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1941 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 804
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1943, pip num: 14384
BIT-1002 : Init feedthrough completely, num: 7
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1323 valid insts, and 38114 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  4.209578s wall, 21.984375s user + 0.203125s system = 22.187500s CPU (527.1%)

RUN-1004 : used memory is 549 MB, reserved memory is 517 MB, peak memory is 682 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231114_114131.log"
