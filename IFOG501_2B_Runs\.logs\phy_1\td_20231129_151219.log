============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 15:12:19 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/DA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0111110101101110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3138/12 useful/useless nets, 1921/4 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2896/16 useful/useless nets, 2245/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1017 : Remove 1 const input seq instances
SYN-1002 :     cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[0]$bus_nodes/reg0_syn_10
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 278 better
SYN-1014 : Optimize round 2
SYN-1032 : 2666/30 useful/useless nets, 2015/32 useful/useless insts
SYN-1015 : Optimize round 2, 76 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2690/157 useful/useless nets, 2062/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 14 instances.
SYN-2501 : Optimize round 1, 66 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 3059/5 useful/useless nets, 2431/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11100, tnet num: 3059, tinst num: 2430, tnode num: 14858, tedge num: 17914.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3059 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 164 (3.63), #lev = 8 (1.85)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 163 (3.67), #lev = 7 (1.99)
SYN-3001 : Logic optimization runtime opt =   0.04 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 373 instances into 163 LUTs, name keeping = 73%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 274 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1003 : finish command "optimize_gate -no_sch" in  1.283050s wall, 1.156250s user + 0.046875s system = 1.203125s CPU (93.8%)

RUN-1004 : used memory is 165 MB, reserved memory is 124 MB, peak memory is 195 MB
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  2.223229s wall, 2.046875s user + 0.093750s system = 2.140625s CPU (96.3%)

RUN-1004 : used memory is 166 MB, reserved memory is 124 MB, peak memory is 195 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (185 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2047 instances
RUN-0007 : 535 luts, 1150 seqs, 179 mslices, 110 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2677 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1858 nets have 2 pins
RUN-1001 : 645 nets have [3 - 5] pins
RUN-1001 : 104 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 38 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     241     
RUN-1001 :   No   |  No   |  Yes  |     213     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     276     
RUN-1001 :   Yes  |  No   |  Yes  |     310     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  16   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 26
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2045 instances, 535 luts, 1150 seqs, 289 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10281, tnet num: 2675, tinst num: 2045, tnode num: 14239, tedge num: 17464.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2675 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.582187s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (93.9%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 711168
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2045.
PHY-3001 : End clustering;  0.000068s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 644564, overlap = 69.75
PHY-3002 : Step(2): len = 606086, overlap = 67.5
PHY-3002 : Step(3): len = 581380, overlap = 67.5
PHY-3002 : Step(4): len = 509050, overlap = 69.75
PHY-3002 : Step(5): len = 450370, overlap = 65.25
PHY-3002 : Step(6): len = 426809, overlap = 67.5
PHY-3002 : Step(7): len = 411589, overlap = 67.5
PHY-3002 : Step(8): len = 404157, overlap = 72
PHY-3002 : Step(9): len = 398787, overlap = 67.5
PHY-3002 : Step(10): len = 386975, overlap = 69.75
PHY-3002 : Step(11): len = 379419, overlap = 72
PHY-3002 : Step(12): len = 373962, overlap = 72
PHY-3002 : Step(13): len = 363270, overlap = 72
PHY-3002 : Step(14): len = 354665, overlap = 72
PHY-3002 : Step(15): len = 348078, overlap = 72
PHY-3002 : Step(16): len = 339581, overlap = 72
PHY-3002 : Step(17): len = 330448, overlap = 67.5
PHY-3002 : Step(18): len = 324854, overlap = 67.5
PHY-3002 : Step(19): len = 315793, overlap = 67.5
PHY-3002 : Step(20): len = 309025, overlap = 67.5
PHY-3002 : Step(21): len = 303697, overlap = 67.5
PHY-3002 : Step(22): len = 294487, overlap = 67.5
PHY-3002 : Step(23): len = 284870, overlap = 67.5
PHY-3002 : Step(24): len = 281150, overlap = 67.5
PHY-3002 : Step(25): len = 272829, overlap = 65.25
PHY-3002 : Step(26): len = 254553, overlap = 67.5
PHY-3002 : Step(27): len = 246928, overlap = 69.75
PHY-3002 : Step(28): len = 244559, overlap = 69.75
PHY-3002 : Step(29): len = 210729, overlap = 67.5
PHY-3002 : Step(30): len = 198199, overlap = 67.5
PHY-3002 : Step(31): len = 196463, overlap = 67.5
PHY-3002 : Step(32): len = 180876, overlap = 69.75
PHY-3002 : Step(33): len = 172189, overlap = 69.75
PHY-3002 : Step(34): len = 169646, overlap = 69.75
PHY-3002 : Step(35): len = 166384, overlap = 72
PHY-3002 : Step(36): len = 163047, overlap = 72
PHY-3002 : Step(37): len = 160804, overlap = 72
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.15155e-05
PHY-3002 : Step(38): len = 163137, overlap = 67.5
PHY-3002 : Step(39): len = 160783, overlap = 60.75
PHY-3002 : Step(40): len = 158627, overlap = 60.75
PHY-3002 : Step(41): len = 157202, overlap = 60.75
PHY-3002 : Step(42): len = 155390, overlap = 60.75
PHY-3002 : Step(43): len = 154949, overlap = 56.25
PHY-3002 : Step(44): len = 152964, overlap = 58.5
PHY-3002 : Step(45): len = 151540, overlap = 58.5
PHY-3002 : Step(46): len = 149889, overlap = 56.25
PHY-3002 : Step(47): len = 149131, overlap = 56.25
PHY-3002 : Step(48): len = 145448, overlap = 56.25
PHY-3002 : Step(49): len = 139991, overlap = 56.25
PHY-3002 : Step(50): len = 135593, overlap = 58.5
PHY-3002 : Step(51): len = 134085, overlap = 56.25
PHY-3002 : Step(52): len = 132103, overlap = 56.25
PHY-3002 : Step(53): len = 127129, overlap = 56.25
PHY-3002 : Step(54): len = 119049, overlap = 54
PHY-3002 : Step(55): len = 116184, overlap = 56.25
PHY-3002 : Step(56): len = 115407, overlap = 56.25
PHY-3002 : Step(57): len = 114926, overlap = 56.25
PHY-3002 : Step(58): len = 114205, overlap = 56.4375
PHY-3002 : Step(59): len = 113647, overlap = 56.4375
PHY-3002 : Step(60): len = 112107, overlap = 54.5625
PHY-3002 : Step(61): len = 108478, overlap = 56.875
PHY-3002 : Step(62): len = 106305, overlap = 57.0625
PHY-3002 : Step(63): len = 104072, overlap = 56.9375
PHY-3002 : Step(64): len = 102090, overlap = 57.125
PHY-3002 : Step(65): len = 100512, overlap = 54.8125
PHY-3002 : Step(66): len = 99742.3, overlap = 56.4375
PHY-3002 : Step(67): len = 97835.7, overlap = 56.4375
PHY-3002 : Step(68): len = 94782.2, overlap = 54
PHY-3002 : Step(69): len = 93661.5, overlap = 56.25
PHY-3002 : Step(70): len = 92962.4, overlap = 54
PHY-3002 : Step(71): len = 92489.2, overlap = 51.75
PHY-3002 : Step(72): len = 89738.7, overlap = 56.25
PHY-3002 : Step(73): len = 87515.5, overlap = 58.625
PHY-3002 : Step(74): len = 86384.2, overlap = 56.5
PHY-3002 : Step(75): len = 85784.2, overlap = 54.4375
PHY-3002 : Step(76): len = 85425.1, overlap = 54.4375
PHY-3002 : Step(77): len = 85014.8, overlap = 52.25
PHY-3002 : Step(78): len = 84061.3, overlap = 56.9375
PHY-3002 : Step(79): len = 80961.6, overlap = 59.25
PHY-3002 : Step(80): len = 79311.2, overlap = 56.6875
PHY-3002 : Step(81): len = 78031.7, overlap = 56.625
PHY-3002 : Step(82): len = 77530.5, overlap = 52.125
PHY-3002 : Step(83): len = 76374.9, overlap = 54.4375
PHY-3002 : Step(84): len = 75928.3, overlap = 54.375
PHY-3002 : Step(85): len = 75675.9, overlap = 52.125
PHY-3002 : Step(86): len = 75505.9, overlap = 52.1875
PHY-3002 : Step(87): len = 74705.7, overlap = 52.1875
PHY-3002 : Step(88): len = 74232.1, overlap = 52.1875
PHY-3002 : Step(89): len = 74108.7, overlap = 52.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000103031
PHY-3002 : Step(90): len = 74363.4, overlap = 52.1875
PHY-3002 : Step(91): len = 74420.8, overlap = 52.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000206062
PHY-3002 : Step(92): len = 74548.9, overlap = 52.1875
PHY-3002 : Step(93): len = 74577.6, overlap = 52.1875
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.014510s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (107.7%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2675 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.126589s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(94): len = 83340.4, overlap = 12.75
PHY-3002 : Step(95): len = 83481.1, overlap = 12.8125
PHY-3002 : Step(96): len = 82398.5, overlap = 14.5625
PHY-3002 : Step(97): len = 81999.2, overlap = 15.625
PHY-3002 : Step(98): len = 81324.1, overlap = 15.7188
PHY-3002 : Step(99): len = 80096.2, overlap = 16.8438
PHY-3002 : Step(100): len = 78883.7, overlap = 19.9375
PHY-3002 : Step(101): len = 76307.4, overlap = 24
PHY-3002 : Step(102): len = 75405.8, overlap = 24.8438
PHY-3002 : Step(103): len = 73791.7, overlap = 27.375
PHY-3002 : Step(104): len = 72563.7, overlap = 28.75
PHY-3002 : Step(105): len = 71470.2, overlap = 27.4375
PHY-3002 : Step(106): len = 69999.6, overlap = 31.4062
PHY-3002 : Step(107): len = 69013.4, overlap = 36.0312
PHY-3002 : Step(108): len = 67707.7, overlap = 38.5312
PHY-3002 : Step(109): len = 67124.7, overlap = 40.6875
PHY-3002 : Step(110): len = 66333.8, overlap = 44.625
PHY-3002 : Step(111): len = 65611.2, overlap = 46.1562
PHY-3002 : Step(112): len = 65019.3, overlap = 48.5
PHY-3002 : Step(113): len = 64604.5, overlap = 51.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000761617
PHY-3002 : Step(114): len = 64711.5, overlap = 51.6562
PHY-3002 : Step(115): len = 64920.9, overlap = 49.5312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00152323
PHY-3002 : Step(116): len = 64639.1, overlap = 49.4375
PHY-3002 : Step(117): len = 64639.1, overlap = 49.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00304647
PHY-3002 : Step(118): len = 64810, overlap = 47.0625
PHY-3002 : Step(119): len = 64773.5, overlap = 46.7188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00609294
PHY-3002 : Step(120): len = 64716, overlap = 46.7812
PHY-3002 : Step(121): len = 64660.3, overlap = 47.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2675 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.119813s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (104.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.90108e-05
PHY-3002 : Step(122): len = 64790.9, overlap = 84.5
PHY-3002 : Step(123): len = 65010.3, overlap = 82.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.80216e-05
PHY-3002 : Step(124): len = 64981, overlap = 83.5625
PHY-3002 : Step(125): len = 65560.2, overlap = 83.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.38789e-05
PHY-3002 : Step(126): len = 65559.6, overlap = 82.7812
PHY-3002 : Step(127): len = 68975.1, overlap = 70.9062
PHY-3002 : Step(128): len = 70963.8, overlap = 59.1562
PHY-3002 : Step(129): len = 70640.4, overlap = 59
PHY-3002 : Step(130): len = 70277.2, overlap = 52.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000187758
PHY-3002 : Step(131): len = 70677, overlap = 48.4375
PHY-3002 : Step(132): len = 71324.3, overlap = 44.5
PHY-3002 : Step(133): len = 71983.7, overlap = 43.9062
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10281, tnet num: 2675, tinst num: 2045, tnode num: 14239, tedge num: 17464.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 109.78 peak overflow 3.88
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2677.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 80176, over cnt = 322(0%), over = 1354, worst = 33
PHY-1001 : End global iterations;  0.166998s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (149.7%)

PHY-1001 : Congestion index: top1 = 47.93, top5 = 28.55, top10 = 20.34, top15 = 15.34.
PHY-1001 : End incremental global routing;  0.249932s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (125.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2675 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.120255s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.426324s wall, 0.468750s user + 0.015625s system = 0.484375s CPU (113.6%)

OPT-1001 : Current memory(MB): used = 228, reserve = 186, peak = 228.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2028/2677.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 80176, over cnt = 322(0%), over = 1354, worst = 33
PHY-1002 : len = 90616, over cnt = 220(0%), over = 497, worst = 13
PHY-1002 : len = 95016, over cnt = 104(0%), over = 163, worst = 8
PHY-1002 : len = 96792, over cnt = 42(0%), over = 47, worst = 3
PHY-1002 : len = 97864, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.215148s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (116.2%)

PHY-1001 : Congestion index: top1 = 42.31, top5 = 28.31, top10 = 21.83, top15 = 17.48.
OPT-1001 : End congestion update;  0.290675s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (118.3%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2675 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.098456s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (95.2%)

OPT-0007 : Start: WNS -3047 TNS -56324 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -3047 TNS -56324 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.393912s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (111.1%)

OPT-1001 : Current memory(MB): used = 233, reserve = 190, peak = 233.
OPT-1001 : End physical optimization;  1.301442s wall, 1.390625s user + 0.062500s system = 1.453125s CPU (111.7%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 535 LUT to BLE ...
SYN-4008 : Packed 535 LUT and 234 SEQ to BLE.
SYN-4003 : Packing 916 remaining SEQ's ...
SYN-4005 : Packed 175 SEQ with LUT/SLICE
SYN-4006 : 156 single LUT's are left
SYN-4006 : 741 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1276/1672 primitive instances ...
PHY-3001 : End packing;  0.090853s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (103.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1036 instances
RUN-1001 : 481 mslices, 482 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2452 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1628 nets have 2 pins
RUN-1001 : 647 nets have [3 - 5] pins
RUN-1001 : 108 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
PHY-3001 : design contains 1034 instances, 963 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : After packing: Len = 72013.2, Over = 69.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8741, tnet num: 2450, tinst num: 1034, tnode num: 11586, tedge num: 15371.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.516753s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (99.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.32362e-05
PHY-3002 : Step(134): len = 71047.4, overlap = 68.25
PHY-3002 : Step(135): len = 70248.9, overlap = 70
PHY-3002 : Step(136): len = 69960.5, overlap = 72
PHY-3002 : Step(137): len = 69986.5, overlap = 70.75
PHY-3002 : Step(138): len = 69480.4, overlap = 74
PHY-3002 : Step(139): len = 69373.7, overlap = 74
PHY-3002 : Step(140): len = 69002.8, overlap = 78.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.64724e-05
PHY-3002 : Step(141): len = 69699, overlap = 73
PHY-3002 : Step(142): len = 70792.8, overlap = 64
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.29447e-05
PHY-3002 : Step(143): len = 72158.7, overlap = 55.5
PHY-3002 : Step(144): len = 72698.7, overlap = 54.75
PHY-3002 : Step(145): len = 72613.5, overlap = 54.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000185889
PHY-3002 : Step(146): len = 73851.7, overlap = 54
PHY-3002 : Step(147): len = 74433, overlap = 53
PHY-3002 : Step(148): len = 74332.5, overlap = 52.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.207075s wall, 0.140625s user + 0.250000s system = 0.390625s CPU (188.6%)

PHY-3001 : Trial Legalized: Len = 91158.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.100658s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (93.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00262244
PHY-3002 : Step(149): len = 86965.4, overlap = 7.25
PHY-3002 : Step(150): len = 86019.8, overlap = 7.25
PHY-3002 : Step(151): len = 83874.9, overlap = 11.5
PHY-3002 : Step(152): len = 82500.8, overlap = 14.25
PHY-3002 : Step(153): len = 81227, overlap = 19
PHY-3002 : Step(154): len = 80447.7, overlap = 18.25
PHY-3002 : Step(155): len = 79932.6, overlap = 20
PHY-3002 : Step(156): len = 79615.9, overlap = 23.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00524488
PHY-3002 : Step(157): len = 79596.2, overlap = 23
PHY-3002 : Step(158): len = 79590.1, overlap = 23
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0104898
PHY-3002 : Step(159): len = 79636, overlap = 22.75
PHY-3002 : Step(160): len = 79653.6, overlap = 23
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008331s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (187.5%)

PHY-3001 : Legalized: Len = 85493, Over = 0
PHY-3001 : Spreading special nets. 26 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.011381s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (137.3%)

PHY-3001 : 37 instances has been re-located, deltaX = 16, deltaY = 21, maxDist = 3.
PHY-3001 : Final: Len = 85957, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8741, tnet num: 2450, tinst num: 1034, tnode num: 11586, tedge num: 15371.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 127/2452.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 99544, over cnt = 223(0%), over = 382, worst = 8
PHY-1002 : len = 101264, over cnt = 128(0%), over = 158, worst = 4
PHY-1002 : len = 102672, over cnt = 40(0%), over = 59, worst = 4
PHY-1002 : len = 103256, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 103296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.287393s wall, 0.375000s user + 0.031250s system = 0.406250s CPU (141.4%)

PHY-1001 : Congestion index: top1 = 33.45, top5 = 25.43, top10 = 20.75, top15 = 17.44.
PHY-1001 : End incremental global routing;  0.374412s wall, 0.468750s user + 0.031250s system = 0.500000s CPU (133.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.115865s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (94.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.542347s wall, 0.625000s user + 0.031250s system = 0.656250s CPU (121.0%)

OPT-1001 : Current memory(MB): used = 236, reserve = 194, peak = 238.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2122/2452.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 103296, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009643s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.45, top5 = 25.43, top10 = 20.75, top15 = 17.44.
OPT-1001 : End congestion update;  0.077297s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077360s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.0%)

OPT-0007 : Start: WNS -3047 TNS -56824 NUM_FEPS 28
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 995 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 1034 instances, 963 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Initial: Len = 86030.2, Over = 0
PHY-3001 : Spreading special nets. 2 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007434s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 2 instances has been re-located, deltaX = 1, deltaY = 1, maxDist = 1.
PHY-3001 : Final: Len = 86078.2, Over = 0
PHY-3001 : End incremental legalization;  0.062838s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (248.7%)

OPT-0007 : Iter 1: improved WNS -3047 TNS -56824 NUM_FEPS 28 with 4 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS -3047 TNS -56824 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.241999s wall, 0.312500s user + 0.031250s system = 0.343750s CPU (142.0%)

OPT-1001 : Current memory(MB): used = 241, reserve = 198, peak = 241.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.084905s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (92.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2106/2452.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 103384, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 103368, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 103384, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.045158s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.8%)

PHY-1001 : Congestion index: top1 = 33.45, top5 = 25.41, top10 = 20.74, top15 = 17.44.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.080697s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.8%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -3047 TNS -56824 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.068966
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -3047ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2452 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2452 nets
OPT-1001 : End physical optimization;  1.550352s wall, 1.796875s user + 0.062500s system = 1.859375s CPU (119.9%)

RUN-1003 : finish command "place" in  10.692736s wall, 15.156250s user + 5.484375s system = 20.640625s CPU (193.0%)

RUN-1004 : used memory is 218 MB, reserved memory is 175 MB, peak memory is 241 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1036 instances
RUN-1001 : 481 mslices, 482 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2452 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1628 nets have 2 pins
RUN-1001 : 647 nets have [3 - 5] pins
RUN-1001 : 108 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8741, tnet num: 2450, tinst num: 1034, tnode num: 11586, tedge num: 15371.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 481 mslices, 482 lslices, 34 pads, 28 brams, 4 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 98328, over cnt = 226(0%), over = 390, worst = 8
PHY-1002 : len = 100328, over cnt = 132(0%), over = 166, worst = 5
PHY-1002 : len = 102328, over cnt = 20(0%), over = 23, worst = 3
PHY-1002 : len = 102600, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 102648, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.288659s wall, 0.328125s user + 0.046875s system = 0.375000s CPU (129.9%)

PHY-1001 : Congestion index: top1 = 33.36, top5 = 25.05, top10 = 20.47, top15 = 17.25.
PHY-1001 : End global routing;  0.365942s wall, 0.406250s user + 0.046875s system = 0.453125s CPU (123.8%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 258, reserve = 215, peak = 261.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 517, reserve = 479, peak = 517.
PHY-1001 : End build detailed router design. 4.415563s wall, 4.375000s user + 0.000000s system = 4.375000s CPU (99.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 37560, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.728419s wall, 2.609375s user + 0.046875s system = 2.656250s CPU (97.4%)

PHY-1001 : Current memory(MB): used = 550, reserve = 513, peak = 550.
PHY-1001 : End phase 1; 2.735220s wall, 2.609375s user + 0.046875s system = 2.656250s CPU (97.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 309744, over cnt = 39(0%), over = 39, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 550, reserve = 513, peak = 551.
PHY-1001 : End initial routed; 4.527996s wall, 5.781250s user + 0.218750s system = 6.000000s CPU (132.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2162(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.223   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.811   |  -5.796   |  16   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.574306s wall, 0.578125s user + 0.000000s system = 0.578125s CPU (100.7%)

PHY-1001 : Current memory(MB): used = 551, reserve = 514, peak = 551.
PHY-1001 : End phase 2; 5.102425s wall, 6.359375s user + 0.218750s system = 6.578125s CPU (128.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 309744, over cnt = 39(0%), over = 39, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.021345s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (73.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 309488, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.041171s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (113.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 309528, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.033764s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (138.8%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 309480, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.038143s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (81.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2162(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.223   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.811   |  -5.796   |  16   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.558296s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (100.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 10 feed throughs used by 10 nets
PHY-1001 : End commit to database; 0.377666s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (91.0%)

PHY-1001 : Current memory(MB): used = 568, reserve = 530, peak = 568.
PHY-1001 : End phase 3; 1.252893s wall, 1.234375s user + 0.000000s system = 1.234375s CPU (98.5%)

PHY-1003 : Routed, final wirelength = 309480
PHY-1001 : Current memory(MB): used = 568, reserve = 531, peak = 568.
PHY-1001 : End export database. 0.012875s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (121.4%)

PHY-1001 : End detail routing;  13.750681s wall, 14.828125s user + 0.265625s system = 15.093750s CPU (109.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8741, tnet num: 2450, tinst num: 1034, tnode num: 11586, tedge num: 15371.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  15.188875s wall, 16.296875s user + 0.312500s system = 16.609375s CPU (109.4%)

RUN-1004 : used memory is 543 MB, reserved memory is 509 MB, peak memory is 569 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1121   out of  19600    5.72%
#reg                     1205   out of  19600    6.15%
#le                      1862
  #lut only               657   out of   1862   35.28%
  #reg only               741   out of   1862   39.80%
  #lut&reg                464   out of   1862   24.92%
#dsp                        4   out of     29   13.79%
#bram                      28   out of     64   43.75%
  #bram9k                  28
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                     6
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       395
#2        config_inst_syn_9               GCLK               config             config_inst.jtck            102
#3        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       101
#4        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di             73
#5        wendu/clk_us                    GCLK               mslice             wendu/cnt_b[2]_syn_11.q0    43
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       NONE    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       NONE    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       NONE    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       NONE    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       NONE    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       NONE    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       NONE    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       NONE    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       NONE    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       NONE    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       NONE    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       NONE    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1862   |832     |289     |1224    |28      |4       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |980    |257     |105     |782     |4       |4       |
|    ctrl_signal                     |SignalGenerator                                  |36     |30      |6       |25      |0       |0       |
|    demodu                          |Demodulation                                     |431    |82      |44      |348     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |53     |28      |6       |45      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |10      |0       |17      |0       |0       |
|    integ                           |Integration                                      |136    |16      |14      |110     |0       |0       |
|    modu                            |Modulation                                       |32     |17      |7       |30      |0       |0       |
|    rs422                           |Rs422Output                                      |316    |88      |29      |251     |0       |4       |
|    trans                           |SquareWaveGenerator                              |29     |24      |5       |18      |0       |0       |
|  u_uart                            |UART_Control                                     |137    |119     |7       |56      |0       |0       |
|    U0                              |speed_select_Tx                                  |36     |29      |7       |18      |0       |0       |
|    U1                              |uart_tx                                          |22     |18      |0       |13      |0       |0       |
|    U2                              |Ctrl_Data                                        |79     |72      |0       |25      |0       |0       |
|  wendu                             |DS18B20                                          |205    |160     |45      |71      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |457    |254     |91      |271     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |457    |254     |91      |271     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |176    |74      |0       |175     |0       |0       |
|        reg_inst                    |register                                         |174    |72      |0       |173     |0       |0       |
|        tap_inst                    |tap                                              |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger                                          |281    |180     |91      |96      |0       |0       |
|        bus_inst                    |bus_top                                          |72     |32      |30      |12      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |34     |16      |16      |2       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |38     |16      |14      |10      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |109    |80      |29      |56      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1593  
    #2          2       435   
    #3          3       199   
    #4          4        13   
    #5        5-10      114   
    #6        11-50      51   
    #7       51-100      1    
    #8       101-500     2    
  Average     2.30            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 8741, tnet num: 2450, tinst num: 1034, tnode num: 11586, tedge num: 15371.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1003 : finish command "start_timer" in  1.007882s wall, 1.000000s user + 0.000000s system = 1.000000s CPU (99.2%)

RUN-1004 : used memory is 544 MB, reserved memory is 509 MB, peak memory is 584 MB
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2450 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 8c0bef7d7f4a23ca399149266bad52a96c0817f4101cbf2ed9a6d4b976597300 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1034
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2452, pip num: 21237
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1676 valid insts, and 55540 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000111110101101110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  8.560899s wall, 41.593750s user + 0.218750s system = 41.812500s CPU (488.4%)

RUN-1004 : used memory is 571 MB, reserved memory is 533 MB, peak memory is 692 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231129_151219.log"
