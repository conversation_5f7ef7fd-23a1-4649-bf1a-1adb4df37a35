============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 13:54:13 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1608 instances
RUN-0007 : 369 luts, 972 seqs, 143 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2164 nets
RUN-1001 : 1634 nets have 2 pins
RUN-1001 : 415 nets have [3 - 5] pins
RUN-1001 : 76 nets have [6 - 10] pins
RUN-1001 : 14 nets have [11 - 20] pins
RUN-1001 : 20 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     234     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1606 instances, 369 luts, 972 seqs, 218 slices, 26 macros(218 instances: 143 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7674, tnet num: 2162, tinst num: 1606, tnode num: 10861, tedge num: 12948.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.290079s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 587059
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1606.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 474548, overlap = 20.25
PHY-3002 : Step(2): len = 434944, overlap = 20.25
PHY-3002 : Step(3): len = 396745, overlap = 20.25
PHY-3002 : Step(4): len = 366977, overlap = 20.25
PHY-3002 : Step(5): len = 357442, overlap = 20.25
PHY-3002 : Step(6): len = 344567, overlap = 18
PHY-3002 : Step(7): len = 330266, overlap = 15.75
PHY-3002 : Step(8): len = 322273, overlap = 15.75
PHY-3002 : Step(9): len = 314823, overlap = 15.75
PHY-3002 : Step(10): len = 304027, overlap = 15.75
PHY-3002 : Step(11): len = 297341, overlap = 18
PHY-3002 : Step(12): len = 290229, overlap = 18
PHY-3002 : Step(13): len = 283132, overlap = 18
PHY-3002 : Step(14): len = 275103, overlap = 18
PHY-3002 : Step(15): len = 270363, overlap = 18
PHY-3002 : Step(16): len = 262650, overlap = 18
PHY-3002 : Step(17): len = 255614, overlap = 18
PHY-3002 : Step(18): len = 250214, overlap = 18
PHY-3002 : Step(19): len = 245653, overlap = 18
PHY-3002 : Step(20): len = 238887, overlap = 18
PHY-3002 : Step(21): len = 233513, overlap = 18
PHY-3002 : Step(22): len = 229313, overlap = 18
PHY-3002 : Step(23): len = 223056, overlap = 18
PHY-3002 : Step(24): len = 217764, overlap = 18
PHY-3002 : Step(25): len = 214045, overlap = 18
PHY-3002 : Step(26): len = 209064, overlap = 18
PHY-3002 : Step(27): len = 203810, overlap = 18
PHY-3002 : Step(28): len = 200618, overlap = 18
PHY-3002 : Step(29): len = 195946, overlap = 18
PHY-3002 : Step(30): len = 184005, overlap = 18
PHY-3002 : Step(31): len = 180067, overlap = 18
PHY-3002 : Step(32): len = 178192, overlap = 18
PHY-3002 : Step(33): len = 167034, overlap = 18
PHY-3002 : Step(34): len = 148786, overlap = 18
PHY-3002 : Step(35): len = 146215, overlap = 18
PHY-3002 : Step(36): len = 143779, overlap = 18
PHY-3002 : Step(37): len = 108312, overlap = 20.25
PHY-3002 : Step(38): len = 105358, overlap = 20.25
PHY-3002 : Step(39): len = 103159, overlap = 20.25
PHY-3002 : Step(40): len = 100727, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000130971
PHY-3002 : Step(41): len = 102541, overlap = 13.5
PHY-3002 : Step(42): len = 102098, overlap = 11.25
PHY-3002 : Step(43): len = 100468, overlap = 13.5
PHY-3002 : Step(44): len = 99923.4, overlap = 6.75
PHY-3002 : Step(45): len = 97983.4, overlap = 9
PHY-3002 : Step(46): len = 96372.9, overlap = 9
PHY-3002 : Step(47): len = 95221.3, overlap = 9
PHY-3002 : Step(48): len = 93190.7, overlap = 15.75
PHY-3002 : Step(49): len = 92461.4, overlap = 11.25
PHY-3002 : Step(50): len = 90431.4, overlap = 11.25
PHY-3002 : Step(51): len = 88278.1, overlap = 15.75
PHY-3002 : Step(52): len = 86415.6, overlap = 15.75
PHY-3002 : Step(53): len = 86217.6, overlap = 13.5
PHY-3002 : Step(54): len = 83771.9, overlap = 11.25
PHY-3002 : Step(55): len = 81486.8, overlap = 9
PHY-3002 : Step(56): len = 79589.9, overlap = 11.25
PHY-3002 : Step(57): len = 78943.1, overlap = 13.5
PHY-3002 : Step(58): len = 77849.4, overlap = 13.5
PHY-3002 : Step(59): len = 77033.5, overlap = 15.75
PHY-3002 : Step(60): len = 74848.2, overlap = 13.5
PHY-3002 : Step(61): len = 71393.7, overlap = 11.25
PHY-3002 : Step(62): len = 70343.1, overlap = 11.3125
PHY-3002 : Step(63): len = 70214.8, overlap = 11.5
PHY-3002 : Step(64): len = 69597.2, overlap = 13.75
PHY-3002 : Step(65): len = 68983.4, overlap = 15.8125
PHY-3002 : Step(66): len = 67982.6, overlap = 13.5625
PHY-3002 : Step(67): len = 67915.6, overlap = 13.6875
PHY-3002 : Step(68): len = 67471.6, overlap = 9.3125
PHY-3002 : Step(69): len = 66653.8, overlap = 9.375
PHY-3002 : Step(70): len = 65791.4, overlap = 14
PHY-3002 : Step(71): len = 63962.1, overlap = 11.75
PHY-3002 : Step(72): len = 62950.3, overlap = 11.5625
PHY-3002 : Step(73): len = 62817.8, overlap = 9.1875
PHY-3002 : Step(74): len = 61874.4, overlap = 11.25
PHY-3002 : Step(75): len = 60694.2, overlap = 9
PHY-3002 : Step(76): len = 60487.1, overlap = 9
PHY-3002 : Step(77): len = 60356.3, overlap = 6.75
PHY-3002 : Step(78): len = 59735.2, overlap = 11.25
PHY-3002 : Step(79): len = 59482.1, overlap = 11.25
PHY-3002 : Step(80): len = 59422.7, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000261941
PHY-3002 : Step(81): len = 59469.4, overlap = 9
PHY-3002 : Step(82): len = 59524.2, overlap = 6.75
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000523883
PHY-3002 : Step(83): len = 59588.3, overlap = 6.75
PHY-3002 : Step(84): len = 59613.8, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007565s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (206.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064097s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(85): len = 61832, overlap = 5.15625
PHY-3002 : Step(86): len = 60736.1, overlap = 4.84375
PHY-3002 : Step(87): len = 60168.7, overlap = 4.5
PHY-3002 : Step(88): len = 58761.2, overlap = 4.15625
PHY-3002 : Step(89): len = 57782.8, overlap = 4.09375
PHY-3002 : Step(90): len = 56281.2, overlap = 4.21875
PHY-3002 : Step(91): len = 55151.8, overlap = 3.96875
PHY-3002 : Step(92): len = 54240.6, overlap = 3.96875
PHY-3002 : Step(93): len = 52679.7, overlap = 3.625
PHY-3002 : Step(94): len = 50862.8, overlap = 6.125
PHY-3002 : Step(95): len = 49722.2, overlap = 7.8125
PHY-3002 : Step(96): len = 49312.4, overlap = 14.5
PHY-3002 : Step(97): len = 48790.5, overlap = 14.0938
PHY-3002 : Step(98): len = 48166.7, overlap = 15
PHY-3002 : Step(99): len = 47603.6, overlap = 14.5
PHY-3002 : Step(100): len = 47540.2, overlap = 15.6875
PHY-3002 : Step(101): len = 47263.4, overlap = 16.3125
PHY-3002 : Step(102): len = 46587.4, overlap = 14.9062
PHY-3002 : Step(103): len = 46128.7, overlap = 13.5938
PHY-3002 : Step(104): len = 45790.4, overlap = 16.5312
PHY-3002 : Step(105): len = 45247.6, overlap = 16.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000344568
PHY-3002 : Step(106): len = 45268.2, overlap = 14.7812
PHY-3002 : Step(107): len = 45042.3, overlap = 14.3438
PHY-3002 : Step(108): len = 45097, overlap = 14.6562
PHY-3002 : Step(109): len = 45119.8, overlap = 15.0312
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000689137
PHY-3002 : Step(110): len = 44989, overlap = 14.4062
PHY-3002 : Step(111): len = 44919.4, overlap = 14.4688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.062982s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (99.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.04094e-05
PHY-3002 : Step(112): len = 45022.7, overlap = 70.0312
PHY-3002 : Step(113): len = 46100.2, overlap = 66.2812
PHY-3002 : Step(114): len = 46768.4, overlap = 64.9062
PHY-3002 : Step(115): len = 46479.4, overlap = 65.0625
PHY-3002 : Step(116): len = 46138.1, overlap = 64.875
PHY-3002 : Step(117): len = 46302.9, overlap = 64.625
PHY-3002 : Step(118): len = 46423, overlap = 55.0625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000140819
PHY-3002 : Step(119): len = 46935.3, overlap = 54.9062
PHY-3002 : Step(120): len = 47807.8, overlap = 47.0625
PHY-3002 : Step(121): len = 48511.9, overlap = 45.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000281637
PHY-3002 : Step(122): len = 48194.2, overlap = 43.5625
PHY-3002 : Step(123): len = 48196.7, overlap = 42.0312
PHY-3002 : Step(124): len = 48623.6, overlap = 36.2812
PHY-3002 : Step(125): len = 49255.4, overlap = 34.875
PHY-3002 : Step(126): len = 49765.5, overlap = 31.9688
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7674, tnet num: 2162, tinst num: 1606, tnode num: 10861, tedge num: 12948.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.78 peak overflow 3.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2164.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52280, over cnt = 232(0%), over = 929, worst = 17
PHY-1001 : End global iterations;  0.080166s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (175.4%)

PHY-1001 : Congestion index: top1 = 41.23, top5 = 23.89, top10 = 15.12, top15 = 10.76.
PHY-1001 : End incremental global routing;  0.130354s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (155.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067402s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.229665s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (129.3%)

OPT-1001 : Current memory(MB): used = 213, reserve = 178, peak = 213.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1636/2164.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52280, over cnt = 232(0%), over = 929, worst = 17
PHY-1002 : len = 58128, over cnt = 163(0%), over = 327, worst = 17
PHY-1002 : len = 61816, over cnt = 24(0%), over = 25, worst = 2
PHY-1002 : len = 62168, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 62336, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.092333s wall, 0.109375s user + 0.078125s system = 0.187500s CPU (203.1%)

PHY-1001 : Congestion index: top1 = 36.14, top5 = 24.38, top10 = 16.87, top15 = 12.41.
OPT-1001 : End congestion update;  0.134789s wall, 0.156250s user + 0.078125s system = 0.234375s CPU (173.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2162 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060186s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.197756s wall, 0.218750s user + 0.078125s system = 0.296875s CPU (150.1%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.712632s wall, 0.734375s user + 0.125000s system = 0.859375s CPU (120.6%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 369 LUT to BLE ...
SYN-4008 : Packed 369 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 790 remaining SEQ's ...
SYN-4005 : Packed 113 SEQ with LUT/SLICE
SYN-4006 : 96 single LUT's are left
SYN-4006 : 677 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1046/1371 primitive instances ...
PHY-3001 : End packing;  0.049398s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 820 instances
RUN-1001 : 385 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1996 nets
RUN-1001 : 1476 nets have 2 pins
RUN-1001 : 405 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 818 instances, 771 slices, 26 macros(218 instances: 143 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49332.4, Over = 59
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6454, tnet num: 1994, tinst num: 818, tnode num: 8759, tedge num: 11317.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.312083s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (95.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.65957e-05
PHY-3002 : Step(127): len = 48899, overlap = 62.25
PHY-3002 : Step(128): len = 48811.4, overlap = 62.75
PHY-3002 : Step(129): len = 48362.6, overlap = 62.75
PHY-3002 : Step(130): len = 48508, overlap = 62.25
PHY-3002 : Step(131): len = 48383.5, overlap = 62.75
PHY-3002 : Step(132): len = 48591.1, overlap = 61.5
PHY-3002 : Step(133): len = 48366.7, overlap = 61.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.31913e-05
PHY-3002 : Step(134): len = 49325.4, overlap = 61.75
PHY-3002 : Step(135): len = 50619, overlap = 58.5
PHY-3002 : Step(136): len = 50513.2, overlap = 58.5
PHY-3002 : Step(137): len = 50616.8, overlap = 57.5
PHY-3002 : Step(138): len = 50443, overlap = 57.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000106383
PHY-3002 : Step(139): len = 51030.2, overlap = 55.5
PHY-3002 : Step(140): len = 51565.8, overlap = 54
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.077064s wall, 0.062500s user + 0.156250s system = 0.218750s CPU (283.9%)

PHY-3001 : Trial Legalized: Len = 64381.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054783s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (114.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000448699
PHY-3002 : Step(141): len = 62448.7, overlap = 4
PHY-3002 : Step(142): len = 60698.1, overlap = 9
PHY-3002 : Step(143): len = 58955.7, overlap = 16.5
PHY-3002 : Step(144): len = 57849.8, overlap = 21
PHY-3002 : Step(145): len = 57230.3, overlap = 23.75
PHY-3002 : Step(146): len = 56891.1, overlap = 24.75
PHY-3002 : Step(147): len = 56720.2, overlap = 25
PHY-3002 : Step(148): len = 56484.1, overlap = 25.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000897398
PHY-3002 : Step(149): len = 56859.8, overlap = 25.5
PHY-3002 : Step(150): len = 57076.1, overlap = 24.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0017948
PHY-3002 : Step(151): len = 57401.5, overlap = 23
PHY-3002 : Step(152): len = 57401.5, overlap = 23
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005251s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (297.6%)

PHY-3001 : Legalized: Len = 61697.3, Over = 0
PHY-3001 : Spreading special nets. 5 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005588s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 8 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 61697.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6454, tnet num: 1994, tinst num: 818, tnode num: 8759, tedge num: 11317.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 93/1996.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68232, over cnt = 138(0%), over = 214, worst = 6
PHY-1002 : len = 68760, over cnt = 75(0%), over = 116, worst = 6
PHY-1002 : len = 70288, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 70424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.109691s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (128.2%)

PHY-1001 : Congestion index: top1 = 30.86, top5 = 22.41, top10 = 17.62, top15 = 13.92.
PHY-1001 : End incremental global routing;  0.161673s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (116.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062697s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.254236s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (110.6%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1769/1996.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005766s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.86, top5 = 22.41, top10 = 17.62, top15 = 13.92.
OPT-1001 : End congestion update;  0.052000s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (120.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049260s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.103018s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (106.2%)

OPT-1001 : Current memory(MB): used = 223, reserve = 187, peak = 223.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056497s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (83.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1769/1996.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005615s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (278.3%)

PHY-1001 : Congestion index: top1 = 30.86, top5 = 22.41, top10 = 17.62, top15 = 13.92.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.049023s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.448276
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.821399s wall, 0.859375s user + 0.000000s system = 0.859375s CPU (104.6%)

RUN-1003 : finish command "place" in  5.812260s wall, 8.812500s user + 2.921875s system = 11.734375s CPU (201.9%)

RUN-1004 : used memory is 202 MB, reserved memory is 167 MB, peak memory is 223 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 820 instances
RUN-1001 : 385 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1996 nets
RUN-1001 : 1476 nets have 2 pins
RUN-1001 : 405 nets have [3 - 5] pins
RUN-1001 : 78 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6454, tnet num: 1994, tinst num: 818, tnode num: 8759, tedge num: 11317.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 385 mslices, 386 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67344, over cnt = 145(0%), over = 216, worst = 7
PHY-1002 : len = 68088, over cnt = 100(0%), over = 129, worst = 5
PHY-1002 : len = 69320, over cnt = 19(0%), over = 30, worst = 5
PHY-1002 : len = 69824, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.108122s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (202.3%)

PHY-1001 : Congestion index: top1 = 30.45, top5 = 22.30, top10 = 17.51, top15 = 13.84.
PHY-1001 : End global routing;  0.157707s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (168.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 242, reserve = 208, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 499, reserve = 468, peak = 499.
PHY-1001 : End build detailed router design. 3.313047s wall, 3.281250s user + 0.031250s system = 3.312500s CPU (100.0%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34000, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.500602s wall, 1.468750s user + 0.015625s system = 1.484375s CPU (98.9%)

PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End phase 1; 1.508164s wall, 1.484375s user + 0.015625s system = 1.500000s CPU (99.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182520, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End initial routed; 1.191822s wall, 2.421875s user + 0.156250s system = 2.578125s CPU (216.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1768(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.405   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.370604s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End phase 2; 1.562515s wall, 2.796875s user + 0.156250s system = 2.953125s CPU (189.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182520, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014446s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.2%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182416, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.025128s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (124.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182472, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.019463s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (80.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1768(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.405   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.363587s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (98.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 2 feed throughs used by 2 nets
PHY-1001 : End commit to database; 0.170466s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.706489s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (99.5%)

PHY-1003 : Routed, final wirelength = 182472
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.009503s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (164.4%)

PHY-1001 : End detail routing;  7.280474s wall, 8.453125s user + 0.203125s system = 8.656250s CPU (118.9%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6454, tnet num: 1994, tinst num: 818, tnode num: 8759, tedge num: 11317.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.163236s wall, 9.390625s user + 0.250000s system = 9.640625s CPU (118.1%)

RUN-1004 : used memory is 500 MB, reserved memory is 471 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      808   out of  19600    4.12%
#reg                     1047   out of  19600    5.34%
#le                      1485
  #lut only               438   out of   1485   29.49%
  #reg only               677   out of   1485   45.59%
  #lut&reg                370   out of   1485   24.92%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         459
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    44
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1485   |590     |218     |1078    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1093   |300     |125     |895     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |27     |21      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |527    |120     |57      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |13      |0       |27      |0       |0       |
|    integ                   |Integration                                      |138    |17      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |63     |26      |14      |59      |0       |1       |
|    rs422                   |Rs422Output                                      |317    |100     |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |21     |16      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |96     |80      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |36     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |19     |16      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |41     |36      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1440  
    #2          2       282   
    #3          3       107   
    #4          4        16   
    #5        5-10       80   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6454, tnet num: 1994, tinst num: 818, tnode num: 8759, tedge num: 11317.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1994 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 818
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1996, pip num: 14633
BIT-1002 : Init feedthrough completely, num: 2
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1317 valid insts, and 38663 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.105753s wall, 17.250000s user + 0.156250s system = 17.406250s CPU (560.5%)

RUN-1004 : used memory is 543 MB, reserved memory is 511 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_135413.log"
