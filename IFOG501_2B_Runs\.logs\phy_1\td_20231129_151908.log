============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 15:19:08 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/DA.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 26 trigger nets, 26 data nets.
KIT-1004 : Chipwatcher code = 0111110101101110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=82) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=82)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=26,BUS_CTRL_NUM=60,BUS_WIDTH='{32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb0100000})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3200/12 useful/useless nets, 1983/4 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1016 : Merged 1 instances.
SYN-1032 : 2968/16 useful/useless nets, 2317/12 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1017 : Remove 1 const input seq instances
SYN-1002 :     cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[0]$bus_nodes/reg0_syn_14
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 288 better
SYN-1014 : Optimize round 2
SYN-1032 : 2748/30 useful/useless nets, 2097/32 useful/useless insts
SYN-1015 : Optimize round 2, 66 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2772/157 useful/useless nets, 2144/31 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 209 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 56 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 3151/5 useful/useless nets, 2523/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11472, tnet num: 3151, tinst num: 2522, tnode num: 15422, tedge num: 18464.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3151 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 177 (3.55), #lev = 8 (1.79)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 177 (3.55), #lev = 8 (1.79)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 393 instances into 177 LUTs, name keeping = 77%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 294 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.872913s wall, 1.750000s user + 0.093750s system = 1.843750s CPU (98.4%)

RUN-1004 : used memory is 168 MB, reserved memory is 128 MB, peak memory is 198 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (185 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2133 instances
RUN-0007 : 551 luts, 1220 seqs, 179 mslices, 110 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2763 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1909 nets have 2 pins
RUN-1001 : 676 nets have [3 - 5] pins
RUN-1001 : 106 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 38 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     277     
RUN-1001 :   No   |  No   |  Yes  |     223     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     300     
RUN-1001 :   Yes  |  No   |  Yes  |     310     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  18   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 28
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2131 instances, 551 luts, 1220 seqs, 289 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10637, tnet num: 2761, tinst num: 2131, tnode num: 14797, tedge num: 18004.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2761 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.469763s wall, 0.468750s user + 0.000000s system = 0.468750s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 742108
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2131.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 654514, overlap = 69.75
PHY-3002 : Step(2): len = 621666, overlap = 65.25
PHY-3002 : Step(3): len = 537919, overlap = 72
PHY-3002 : Step(4): len = 460532, overlap = 72
PHY-3002 : Step(5): len = 436092, overlap = 67.5
PHY-3002 : Step(6): len = 425929, overlap = 67.5
PHY-3002 : Step(7): len = 409974, overlap = 72
PHY-3002 : Step(8): len = 397224, overlap = 72
PHY-3002 : Step(9): len = 391830, overlap = 72
PHY-3002 : Step(10): len = 384367, overlap = 72
PHY-3002 : Step(11): len = 374172, overlap = 69.75
PHY-3002 : Step(12): len = 366068, overlap = 72
PHY-3002 : Step(13): len = 361418, overlap = 69.75
PHY-3002 : Step(14): len = 307887, overlap = 65.25
PHY-3002 : Step(15): len = 288595, overlap = 65.25
PHY-3002 : Step(16): len = 284338, overlap = 65.25
PHY-3002 : Step(17): len = 271378, overlap = 67.5
PHY-3002 : Step(18): len = 266819, overlap = 67.5
PHY-3002 : Step(19): len = 260746, overlap = 69.75
PHY-3002 : Step(20): len = 252205, overlap = 69.75
PHY-3002 : Step(21): len = 246568, overlap = 67.5
PHY-3002 : Step(22): len = 239724, overlap = 67.5
PHY-3002 : Step(23): len = 234593, overlap = 67.5
PHY-3002 : Step(24): len = 226157, overlap = 67.5
PHY-3002 : Step(25): len = 218922, overlap = 67.5
PHY-3002 : Step(26): len = 215083, overlap = 67.5
PHY-3002 : Step(27): len = 206836, overlap = 69.75
PHY-3002 : Step(28): len = 200286, overlap = 69.75
PHY-3002 : Step(29): len = 196289, overlap = 69.75
PHY-3002 : Step(30): len = 187969, overlap = 69.75
PHY-3002 : Step(31): len = 177174, overlap = 67.875
PHY-3002 : Step(32): len = 175212, overlap = 67.6875
PHY-3002 : Step(33): len = 171368, overlap = 65.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.837e-05
PHY-3002 : Step(34): len = 173900, overlap = 65.3125
PHY-3002 : Step(35): len = 171075, overlap = 63
PHY-3002 : Step(36): len = 170339, overlap = 54
PHY-3002 : Step(37): len = 168514, overlap = 54
PHY-3002 : Step(38): len = 163476, overlap = 60.75
PHY-3002 : Step(39): len = 161600, overlap = 60.75
PHY-3002 : Step(40): len = 159980, overlap = 56.25
PHY-3002 : Step(41): len = 158648, overlap = 51.75
PHY-3002 : Step(42): len = 157987, overlap = 47.25
PHY-3002 : Step(43): len = 154828, overlap = 54
PHY-3002 : Step(44): len = 150337, overlap = 51.75
PHY-3002 : Step(45): len = 143321, overlap = 54
PHY-3002 : Step(46): len = 141576, overlap = 54
PHY-3002 : Step(47): len = 139032, overlap = 54
PHY-3002 : Step(48): len = 136116, overlap = 54
PHY-3002 : Step(49): len = 133551, overlap = 54
PHY-3002 : Step(50): len = 130724, overlap = 58.5
PHY-3002 : Step(51): len = 126471, overlap = 56.375
PHY-3002 : Step(52): len = 122653, overlap = 56.5625
PHY-3002 : Step(53): len = 121307, overlap = 54.3125
PHY-3002 : Step(54): len = 119642, overlap = 54.4375
PHY-3002 : Step(55): len = 111557, overlap = 49.625
PHY-3002 : Step(56): len = 104598, overlap = 56.75
PHY-3002 : Step(57): len = 103418, overlap = 54.5
PHY-3002 : Step(58): len = 101788, overlap = 53
PHY-3002 : Step(59): len = 100297, overlap = 52.875
PHY-3002 : Step(60): len = 99109.7, overlap = 50.6875
PHY-3002 : Step(61): len = 97393.5, overlap = 55.2812
PHY-3002 : Step(62): len = 96268, overlap = 52.7812
PHY-3002 : Step(63): len = 94506.6, overlap = 54.6562
PHY-3002 : Step(64): len = 93473.5, overlap = 55.0312
PHY-3002 : Step(65): len = 91423.5, overlap = 59.0312
PHY-3002 : Step(66): len = 90046.4, overlap = 57.9688
PHY-3002 : Step(67): len = 85501, overlap = 59.3125
PHY-3002 : Step(68): len = 84386.5, overlap = 56.8125
PHY-3002 : Step(69): len = 83451, overlap = 59.125
PHY-3002 : Step(70): len = 82719.7, overlap = 57.3125
PHY-3002 : Step(71): len = 81764.9, overlap = 57.6562
PHY-3002 : Step(72): len = 80973.1, overlap = 56.625
PHY-3002 : Step(73): len = 79136.1, overlap = 60.375
PHY-3002 : Step(74): len = 77875.1, overlap = 68.25
PHY-3002 : Step(75): len = 77217.3, overlap = 64.2812
PHY-3002 : Step(76): len = 76070.9, overlap = 63.3438
PHY-3002 : Step(77): len = 75367.5, overlap = 59.5312
PHY-3002 : Step(78): len = 74322.3, overlap = 67.9062
PHY-3002 : Step(79): len = 73464.7, overlap = 65.7188
PHY-3002 : Step(80): len = 72928.6, overlap = 65.8438
PHY-3002 : Step(81): len = 72110.8, overlap = 74.625
PHY-3002 : Step(82): len = 71611, overlap = 72.375
PHY-3002 : Step(83): len = 70798.2, overlap = 68.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 7.674e-05
PHY-3002 : Step(84): len = 71036.1, overlap = 68.125
PHY-3002 : Step(85): len = 71106.2, overlap = 68.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00015348
PHY-3002 : Step(86): len = 71138.7, overlap = 68.125
PHY-3002 : Step(87): len = 71219.3, overlap = 68.125
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.015763s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (198.2%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2761 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.120282s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (103.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000354629
PHY-3002 : Step(88): len = 80204, overlap = 33.5938
PHY-3002 : Step(89): len = 80266.5, overlap = 33.8125
PHY-3002 : Step(90): len = 79570.6, overlap = 34.5312
PHY-3002 : Step(91): len = 79704.1, overlap = 35.8125
PHY-3002 : Step(92): len = 79807.2, overlap = 36.375
PHY-3002 : Step(93): len = 78814.8, overlap = 36.1562
PHY-3002 : Step(94): len = 78704, overlap = 30.7188
PHY-3002 : Step(95): len = 77727.2, overlap = 32.1562
PHY-3002 : Step(96): len = 76793.8, overlap = 30.8125
PHY-3002 : Step(97): len = 76461.7, overlap = 30.6875
PHY-3002 : Step(98): len = 75136.9, overlap = 28.1562
PHY-3002 : Step(99): len = 74435, overlap = 27.5938
PHY-3002 : Step(100): len = 73601.9, overlap = 28.3438
PHY-3002 : Step(101): len = 72505.1, overlap = 28.5
PHY-3002 : Step(102): len = 71906.3, overlap = 28.2812
PHY-3002 : Step(103): len = 71236.9, overlap = 28.5312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000709257
PHY-3002 : Step(104): len = 71031, overlap = 28.6562
PHY-3002 : Step(105): len = 71120.7, overlap = 28.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00141851
PHY-3002 : Step(106): len = 70714.1, overlap = 28.8438
PHY-3002 : Step(107): len = 70689.6, overlap = 32.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2761 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.164305s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (95.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.33681e-05
PHY-3002 : Step(108): len = 70930.3, overlap = 69.3438
PHY-3002 : Step(109): len = 71715.6, overlap = 63.9062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.67362e-05
PHY-3002 : Step(110): len = 71471.7, overlap = 66.1562
PHY-3002 : Step(111): len = 72638.2, overlap = 65.6875
PHY-3002 : Step(112): len = 73685.7, overlap = 58.0312
PHY-3002 : Step(113): len = 73779.7, overlap = 58
PHY-3002 : Step(114): len = 74248.6, overlap = 56.625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000133472
PHY-3002 : Step(115): len = 74089.6, overlap = 57.1875
PHY-3002 : Step(116): len = 74546, overlap = 58.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000246665
PHY-3002 : Step(117): len = 75146.4, overlap = 53.25
PHY-3002 : Step(118): len = 76211.7, overlap = 48.9375
PHY-3002 : Step(119): len = 77519.7, overlap = 51.4062
PHY-3002 : Step(120): len = 77796.7, overlap = 50.375
PHY-3002 : Step(121): len = 77512.7, overlap = 51.7812
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00049333
PHY-3002 : Step(122): len = 77510.2, overlap = 51.625
PHY-3002 : Step(123): len = 77309.9, overlap = 47.2188
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.00098666
PHY-3002 : Step(124): len = 77927.8, overlap = 40.2812
PHY-3002 : Step(125): len = 78423.8, overlap = 39.6562
PHY-3002 : Step(126): len = 78691.6, overlap = 38.9062
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00197332
PHY-3002 : Step(127): len = 78308.3, overlap = 38.8125
PHY-3002 : Step(128): len = 78117.1, overlap = 38.375
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 0.00394664
PHY-3002 : Step(129): len = 78431.9, overlap = 39
PHY-3002 : Step(130): len = 78571.7, overlap = 38.0625
PHY-3001 : :::8::: Try harder cell spreading with beta_ = 0.00789328
PHY-3002 : Step(131): len = 78601.8, overlap = 38.1875
PHY-3002 : Step(132): len = 78594.3, overlap = 37.9062
PHY-3001 : :::9::: Try harder cell spreading with beta_ = 0.0157866
PHY-3002 : Step(133): len = 78674.7, overlap = 36.5625
PHY-3002 : Step(134): len = 78674.7, overlap = 36.5625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 10637, tnet num: 2761, tinst num: 2131, tnode num: 14797, tedge num: 18004.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 104.06 peak overflow 2.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2763.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 88928, over cnt = 331(0%), over = 1242, worst = 16
PHY-1001 : End global iterations;  0.179603s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (121.8%)

PHY-1001 : Congestion index: top1 = 40.80, top5 = 28.33, top10 = 21.07, top15 = 16.20.
PHY-1001 : End incremental global routing;  0.256853s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (121.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2761 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.133229s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (93.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.469628s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (109.8%)

OPT-1001 : Current memory(MB): used = 233, reserve = 191, peak = 233.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2118/2763.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 88928, over cnt = 331(0%), over = 1242, worst = 16
PHY-1002 : len = 94816, over cnt = 222(0%), over = 649, worst = 16
PHY-1002 : len = 100288, over cnt = 54(0%), over = 126, worst = 14
PHY-1002 : len = 101224, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 101280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.226290s wall, 0.265625s user + 0.015625s system = 0.281250s CPU (124.3%)

PHY-1001 : Congestion index: top1 = 36.88, top5 = 27.54, top10 = 21.89, top15 = 17.60.
OPT-1001 : End congestion update;  0.299229s wall, 0.328125s user + 0.015625s system = 0.343750s CPU (114.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2761 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.111202s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (112.4%)

OPT-0007 : Start: WNS -2597 TNS -46824 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2597 TNS -46824 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.416239s wall, 0.453125s user + 0.015625s system = 0.468750s CPU (112.6%)

OPT-1001 : Current memory(MB): used = 238, reserve = 196, peak = 238.
OPT-1001 : End physical optimization;  1.395043s wall, 1.453125s user + 0.031250s system = 1.484375s CPU (106.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 551 LUT to BLE ...
SYN-4008 : Packed 551 LUT and 230 SEQ to BLE.
SYN-4003 : Packing 990 remaining SEQ's ...
SYN-4005 : Packed 276 SEQ with LUT/SLICE
SYN-4006 : 97 single LUT's are left
SYN-4006 : 714 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1265/1661 primitive instances ...
PHY-3001 : End packing;  0.092160s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (101.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1040 instances
RUN-1001 : 483 mslices, 484 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2542 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1683 nets have 2 pins
RUN-1001 : 680 nets have [3 - 5] pins
RUN-1001 : 110 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
PHY-3001 : design contains 1038 instances, 967 slices, 36 macros(289 instances: 179 mslices 110 lslices)
PHY-3001 : Cell area utilization is 12%
PHY-3001 : After packing: Len = 79427.2, Over = 58
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9048, tnet num: 2540, tinst num: 1038, tnode num: 12038, tedge num: 15841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.522646s wall, 0.500000s user + 0.015625s system = 0.515625s CPU (98.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.3668e-05
PHY-3002 : Step(135): len = 78262.8, overlap = 57.75
PHY-3002 : Step(136): len = 77416, overlap = 57.5
PHY-3002 : Step(137): len = 76860.4, overlap = 58.75
PHY-3002 : Step(138): len = 76391.7, overlap = 59.25
PHY-3002 : Step(139): len = 76197.5, overlap = 59.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.73361e-05
PHY-3002 : Step(140): len = 77086.5, overlap = 58.25
PHY-3002 : Step(141): len = 77305.6, overlap = 60
PHY-3002 : Step(142): len = 77699.7, overlap = 60.25
PHY-3002 : Step(143): len = 78172.1, overlap = 58
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000174672
PHY-3002 : Step(144): len = 78395.2, overlap = 56.75
PHY-3002 : Step(145): len = 78907.1, overlap = 55.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.226811s wall, 0.156250s user + 0.328125s system = 0.484375s CPU (213.6%)

PHY-3001 : Trial Legalized: Len = 95614.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 12%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.104854s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (89.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000512246
PHY-3002 : Step(146): len = 91636.8, overlap = 7.5
PHY-3002 : Step(147): len = 88651.7, overlap = 15.25
PHY-3002 : Step(148): len = 86970.4, overlap = 21
PHY-3002 : Step(149): len = 85699.6, overlap = 22
PHY-3002 : Step(150): len = 84949.5, overlap = 26
PHY-3002 : Step(151): len = 84587.4, overlap = 27.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00102449
PHY-3002 : Step(152): len = 84869.6, overlap = 26.25
PHY-3002 : Step(153): len = 84980.1, overlap = 24.75
PHY-3002 : Step(154): len = 85009.8, overlap = 24.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00204898
PHY-3002 : Step(155): len = 85157.3, overlap = 24.5
PHY-3002 : Step(156): len = 85249.4, overlap = 24.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008130s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 91043.6, Over = 0
PHY-3001 : Spreading special nets. 33 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.015326s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (101.9%)

PHY-3001 : 42 instances has been re-located, deltaX = 21, deltaY = 23, maxDist = 2.
PHY-3001 : Final: Len = 91913.6, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9048, tnet num: 2540, tinst num: 1038, tnode num: 12038, tedge num: 15841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 60/2542.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 105704, over cnt = 230(0%), over = 401, worst = 6
PHY-1002 : len = 106712, over cnt = 171(0%), over = 257, worst = 5
PHY-1002 : len = 108592, over cnt = 75(0%), over = 96, worst = 4
PHY-1002 : len = 109896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.283751s wall, 0.359375s user + 0.046875s system = 0.406250s CPU (143.2%)

PHY-1001 : Congestion index: top1 = 33.79, top5 = 26.32, top10 = 21.62, top15 = 18.07.
PHY-1001 : End incremental global routing;  0.368921s wall, 0.437500s user + 0.046875s system = 0.484375s CPU (131.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.130280s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (95.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.564186s wall, 0.625000s user + 0.046875s system = 0.671875s CPU (119.1%)

OPT-1001 : Current memory(MB): used = 241, reserve = 199, peak = 241.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2207/2542.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 109896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.011498s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (135.9%)

PHY-1001 : Congestion index: top1 = 33.79, top5 = 26.32, top10 = 21.62, top15 = 18.07.
OPT-1001 : End congestion update;  0.091264s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.094963s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (98.7%)

OPT-0007 : Start: WNS -2597 TNS -47374 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2597 TNS -47374 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.189458s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (107.2%)

OPT-1001 : Current memory(MB): used = 244, reserve = 201, peak = 244.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.088020s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (88.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2207/2542.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 109896, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.012278s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 33.79, top5 = 26.32, top10 = 21.62, top15 = 18.07.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.083895s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (93.1%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -2597 TNS -47374 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 33.310345
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -2597ps with logic level 2 
RUN-1001 :       #2 path slack -2597ps with logic level 2 
RUN-1001 :       #3 path slack -2597ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2542 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2542 nets
OPT-1001 : End physical optimization;  1.535678s wall, 1.609375s user + 0.062500s system = 1.671875s CPU (108.9%)

RUN-1003 : finish command "place" in  10.324372s wall, 15.921875s user + 4.937500s system = 20.859375s CPU (202.0%)

RUN-1004 : used memory is 232 MB, reserved memory is 189 MB, peak memory is 244 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1040 instances
RUN-1001 : 483 mslices, 484 lslices, 34 pads, 28 brams, 4 dsps
RUN-1001 : There are total 2542 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1683 nets have 2 pins
RUN-1001 : 680 nets have [3 - 5] pins
RUN-1001 : 110 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 35 nets have [21 - 99] pins
RUN-1001 : 7 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9048, tnet num: 2540, tinst num: 1038, tnode num: 12038, tedge num: 15841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 483 mslices, 484 lslices, 34 pads, 28 brams, 4 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 104712, over cnt = 243(0%), over = 418, worst = 6
PHY-1002 : len = 105968, over cnt = 174(0%), over = 260, worst = 5
PHY-1002 : len = 107816, over cnt = 70(0%), over = 95, worst = 4
PHY-1002 : len = 109168, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.286082s wall, 0.375000s user + 0.046875s system = 0.421875s CPU (147.5%)

PHY-1001 : Congestion index: top1 = 33.94, top5 = 26.22, top10 = 21.50, top15 = 18.02.
PHY-1001 : End global routing;  0.361867s wall, 0.437500s user + 0.046875s system = 0.484375s CPU (133.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 262, reserve = 220, peak = 273.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 522, reserve = 483, peak = 522.
PHY-1001 : End build detailed router design. 4.510927s wall, 4.437500s user + 0.078125s system = 4.515625s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 39760, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 2.573591s wall, 2.546875s user + 0.015625s system = 2.562500s CPU (99.6%)

PHY-1001 : Current memory(MB): used = 555, reserve = 518, peak = 555.
PHY-1001 : End phase 1; 2.579731s wall, 2.546875s user + 0.015625s system = 2.562500s CPU (99.3%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 317392, over cnt = 60(0%), over = 60, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 555, reserve = 518, peak = 556.
PHY-1001 : End initial routed; 4.077579s wall, 5.421875s user + 0.109375s system = 5.531250s CPU (135.7%)

PHY-1001 : Update timing.....
PHY-1001 : 7/2252(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.058   |  -0.074   |   2   
RUN-1001 :   Hold   |  -1.127   |  -17.681  |  32   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.557549s wall, 0.562500s user + 0.000000s system = 0.562500s CPU (100.9%)

PHY-1001 : Current memory(MB): used = 557, reserve = 519, peak = 557.
PHY-1001 : End phase 2; 4.635251s wall, 5.984375s user + 0.109375s system = 6.093750s CPU (131.5%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 2 pins with SWNS 0.056ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.025818s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (121.0%)

PHY-1022 : len = 317392, over cnt = 64(0%), over = 64, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.051039s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 316328, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.111386s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (112.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 316368, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.031525s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (99.1%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 316384, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.031220s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (200.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2252(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.040   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.127   |  -17.681  |  32   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.546238s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (100.1%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 15 feed throughs used by 14 nets
PHY-1001 : End commit to database; 0.349854s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (102.7%)

PHY-1001 : Current memory(MB): used = 574, reserve = 536, peak = 574.
PHY-1001 : End phase 3; 1.308296s wall, 1.328125s user + 0.015625s system = 1.343750s CPU (102.7%)

PHY-1003 : Routed, final wirelength = 316384
PHY-1001 : Current memory(MB): used = 574, reserve = 537, peak = 574.
PHY-1001 : End export database. 0.016437s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (95.1%)

PHY-1001 : End detail routing;  13.288815s wall, 14.531250s user + 0.234375s system = 14.765625s CPU (111.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9048, tnet num: 2540, tinst num: 1038, tnode num: 12038, tedge num: 15841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  14.901866s wall, 16.218750s user + 0.281250s system = 16.500000s CPU (110.7%)

RUN-1004 : used memory is 549 MB, reserved memory is 512 MB, peak memory is 574 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1137   out of  19600    5.80%
#reg                     1275   out of  19600    6.51%
#le                      1851
  #lut only               576   out of   1851   31.12%
  #reg only               714   out of   1851   38.57%
  #lut&reg                561   out of   1851   30.31%
#dsp                        4   out of     29   13.79%
#bram                      28   out of     64   43.75%
  #bram9k                  28
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    16
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                  Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                   437
#2        config_inst_syn_9               GCLK               config             config_inst.jtck                        102
#3        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                   102
#4        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di                         79
#5        wendu/clk_us                    GCLK               mslice             signal_process/demodu/reg1_syn_89.q0    38
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                           11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                   1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       NONE    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       NONE    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1851   |848     |289     |1304    |28      |4       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1009   |292     |105     |822     |4       |4       |
|    ctrl_signal                     |SignalGenerator                                  |25     |19      |6       |20      |0       |0       |
|    demodu                          |Demodulation                                     |429    |85      |44      |346     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |53     |28      |6       |45      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |6       |0       |14      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |14     |7       |0       |14      |0       |0       |
|    integ                           |Integration                                      |136    |30      |14      |110     |0       |0       |
|    modu                            |Modulation                                       |86     |17      |7       |83      |0       |0       |
|    rs422                           |Rs422Output                                      |304    |117     |29      |245     |0       |4       |
|    trans                           |SquareWaveGenerator                              |29     |24      |5       |18      |0       |0       |
|  u_uart                            |UART_Control                                     |129    |118     |7       |58      |0       |0       |
|    U0                              |speed_select_Tx                                  |30     |23      |7       |18      |0       |0       |
|    U1                              |uart_tx                                          |18     |14      |0       |14      |0       |0       |
|    U2                              |Ctrl_Data                                        |81     |81      |0       |26      |0       |0       |
|  wendu                             |DS18B20                                          |171    |126     |45      |79      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |448    |266     |91      |282     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |448    |266     |91      |282     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |172    |98      |0       |172     |0       |0       |
|        reg_inst                    |register                                         |168    |94      |0       |168     |0       |0       |
|        tap_inst                    |tap                                              |4      |4       |0       |4       |0       |0       |
|      trigger_inst                  |trigger                                          |276    |168     |91      |110     |0       |0       |
|        bus_inst                    |bus_top                                          |78     |32      |30      |26      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |40     |16      |16      |12      |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |38     |16      |14      |14      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |109    |80      |29      |59      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1648  
    #2          2       452   
    #3          3       215   
    #4          4        13   
    #5        5-10      116   
    #6        11-50      50   
    #7       51-100      2    
    #8       101-500     2    
  Average     2.28            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9048, tnet num: 2540, tinst num: 1038, tnode num: 12038, tedge num: 15841.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2540 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 8c0bef7d7f4a23ca399149266bad52a96c0817f4101cbf2ed9a6d4b976597300 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1038
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2542, pip num: 21949
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 15
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1719 valid insts, and 57227 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011111000111110101101110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  6.576102s wall, 34.906250s user + 0.187500s system = 35.093750s CPU (533.7%)

RUN-1004 : used memory is 577 MB, reserved memory is 540 MB, peak memory is 692 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231129_151908.log"
