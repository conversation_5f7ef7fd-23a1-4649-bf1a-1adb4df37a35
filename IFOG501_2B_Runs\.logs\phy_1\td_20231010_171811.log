============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Oct 10 17:18:11 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1632 instances
RUN-0007 : 377 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2202 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1639 nets have 2 pins
RUN-1001 : 447 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1630 instances, 377 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7803, tnet num: 2200, tinst num: 1630, tnode num: 11043, tedge num: 13191.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.327485s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (100.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 567196
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1630.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 539858, overlap = 20.25
PHY-3002 : Step(2): len = 447341, overlap = 20.25
PHY-3002 : Step(3): len = 379817, overlap = 15.75
PHY-3002 : Step(4): len = 366524, overlap = 11.25
PHY-3002 : Step(5): len = 351860, overlap = 18
PHY-3002 : Step(6): len = 343371, overlap = 20.25
PHY-3002 : Step(7): len = 328107, overlap = 18
PHY-3002 : Step(8): len = 317089, overlap = 18
PHY-3002 : Step(9): len = 311719, overlap = 18
PHY-3002 : Step(10): len = 299321, overlap = 18
PHY-3002 : Step(11): len = 290642, overlap = 20.25
PHY-3002 : Step(12): len = 286148, overlap = 20.25
PHY-3002 : Step(13): len = 278646, overlap = 20.25
PHY-3002 : Step(14): len = 273150, overlap = 20.25
PHY-3002 : Step(15): len = 268014, overlap = 20.25
PHY-3002 : Step(16): len = 260965, overlap = 20.25
PHY-3002 : Step(17): len = 255103, overlap = 20.25
PHY-3002 : Step(18): len = 250552, overlap = 20.25
PHY-3002 : Step(19): len = 244155, overlap = 20.25
PHY-3002 : Step(20): len = 237644, overlap = 20.25
PHY-3002 : Step(21): len = 233795, overlap = 20.25
PHY-3002 : Step(22): len = 228276, overlap = 20.25
PHY-3002 : Step(23): len = 223045, overlap = 20.25
PHY-3002 : Step(24): len = 218046, overlap = 20.25
PHY-3002 : Step(25): len = 213424, overlap = 20.25
PHY-3002 : Step(26): len = 208924, overlap = 20.25
PHY-3002 : Step(27): len = 204937, overlap = 20.25
PHY-3002 : Step(28): len = 199589, overlap = 20.25
PHY-3002 : Step(29): len = 194714, overlap = 20.25
PHY-3002 : Step(30): len = 190863, overlap = 20.25
PHY-3002 : Step(31): len = 186144, overlap = 20.25
PHY-3002 : Step(32): len = 180716, overlap = 20.25
PHY-3002 : Step(33): len = 176893, overlap = 20.25
PHY-3002 : Step(34): len = 173038, overlap = 20.25
PHY-3002 : Step(35): len = 167641, overlap = 20.25
PHY-3002 : Step(36): len = 160923, overlap = 20.25
PHY-3002 : Step(37): len = 158879, overlap = 20.25
PHY-3002 : Step(38): len = 153915, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000115257
PHY-3002 : Step(39): len = 155696, overlap = 11.25
PHY-3002 : Step(40): len = 154510, overlap = 11.25
PHY-3002 : Step(41): len = 151085, overlap = 13.5
PHY-3002 : Step(42): len = 147000, overlap = 13.5
PHY-3002 : Step(43): len = 141158, overlap = 11.25
PHY-3002 : Step(44): len = 137416, overlap = 11.25
PHY-3002 : Step(45): len = 136601, overlap = 11.25
PHY-3002 : Step(46): len = 133807, overlap = 9
PHY-3002 : Step(47): len = 130340, overlap = 9
PHY-3002 : Step(48): len = 125627, overlap = 6.75
PHY-3002 : Step(49): len = 122315, overlap = 9
PHY-3002 : Step(50): len = 121545, overlap = 11.25
PHY-3002 : Step(51): len = 118086, overlap = 13.5
PHY-3002 : Step(52): len = 114020, overlap = 11.25
PHY-3002 : Step(53): len = 110996, overlap = 9
PHY-3002 : Step(54): len = 109865, overlap = 9
PHY-3002 : Step(55): len = 106433, overlap = 9
PHY-3002 : Step(56): len = 104794, overlap = 11.25
PHY-3002 : Step(57): len = 102979, overlap = 9
PHY-3002 : Step(58): len = 100641, overlap = 6.75
PHY-3002 : Step(59): len = 97376.4, overlap = 9
PHY-3002 : Step(60): len = 96816.9, overlap = 9
PHY-3002 : Step(61): len = 94603.9, overlap = 4.5
PHY-3002 : Step(62): len = 93010.1, overlap = 4.5
PHY-3002 : Step(63): len = 91333.3, overlap = 9
PHY-3002 : Step(64): len = 89546.4, overlap = 9
PHY-3002 : Step(65): len = 88131.1, overlap = 11.25
PHY-3002 : Step(66): len = 87535.3, overlap = 6.75
PHY-3002 : Step(67): len = 85721.2, overlap = 6.8125
PHY-3002 : Step(68): len = 83217.1, overlap = 11.4375
PHY-3002 : Step(69): len = 81888, overlap = 13.8125
PHY-3002 : Step(70): len = 80712.2, overlap = 7.25
PHY-3002 : Step(71): len = 79809.7, overlap = 7.3125
PHY-3002 : Step(72): len = 79197.2, overlap = 11.8125
PHY-3002 : Step(73): len = 78066.4, overlap = 12
PHY-3002 : Step(74): len = 76403.6, overlap = 7.3125
PHY-3002 : Step(75): len = 75404.3, overlap = 7.375
PHY-3002 : Step(76): len = 73120.8, overlap = 7.4375
PHY-3002 : Step(77): len = 72105, overlap = 7.5625
PHY-3002 : Step(78): len = 71376.9, overlap = 9.8125
PHY-3002 : Step(79): len = 69750.3, overlap = 10.375
PHY-3002 : Step(80): len = 67226, overlap = 10.375
PHY-3002 : Step(81): len = 65494.8, overlap = 12.5625
PHY-3002 : Step(82): len = 64998.4, overlap = 12.5
PHY-3002 : Step(83): len = 64413.5, overlap = 12.3125
PHY-3002 : Step(84): len = 63702.3, overlap = 7.375
PHY-3002 : Step(85): len = 63296.5, overlap = 6.9375
PHY-3002 : Step(86): len = 62647.2, overlap = 6.75
PHY-3002 : Step(87): len = 61325.3, overlap = 6.75
PHY-3002 : Step(88): len = 60941.8, overlap = 9
PHY-3002 : Step(89): len = 60296.8, overlap = 11.25
PHY-3002 : Step(90): len = 59506.6, overlap = 9.125
PHY-3002 : Step(91): len = 59061.1, overlap = 11.5
PHY-3002 : Step(92): len = 58802.9, overlap = 11.625
PHY-3002 : Step(93): len = 58583.1, overlap = 11.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000230514
PHY-3002 : Step(94): len = 58583, overlap = 9.25
PHY-3002 : Step(95): len = 58667.6, overlap = 9.125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000461027
PHY-3002 : Step(96): len = 58577.3, overlap = 6.75
PHY-3002 : Step(97): len = 58563.2, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.020580s wall, 0.000000s user + 0.015625s system = 0.015625s CPU (75.9%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.195526s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (95.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000695152
PHY-3002 : Step(98): len = 61924.8, overlap = 4.8125
PHY-3002 : Step(99): len = 61084.6, overlap = 4.4375
PHY-3002 : Step(100): len = 60669.1, overlap = 4.0625
PHY-3002 : Step(101): len = 59745.3, overlap = 3.6875
PHY-3002 : Step(102): len = 58992, overlap = 3.5
PHY-3002 : Step(103): len = 57764.3, overlap = 3.5
PHY-3002 : Step(104): len = 56916.8, overlap = 3.25
PHY-3002 : Step(105): len = 56004.6, overlap = 2.9375
PHY-3002 : Step(106): len = 55061.8, overlap = 2.875
PHY-3002 : Step(107): len = 53880, overlap = 4.25
PHY-3002 : Step(108): len = 52943.1, overlap = 3.9375
PHY-3002 : Step(109): len = 51988.1, overlap = 6.25
PHY-3002 : Step(110): len = 51973.9, overlap = 8.9375
PHY-3002 : Step(111): len = 51600.5, overlap = 9.4375
PHY-3002 : Step(112): len = 50937.1, overlap = 10.4375
PHY-3002 : Step(113): len = 49975.6, overlap = 10.8125
PHY-3002 : Step(114): len = 49772.2, overlap = 10.9375
PHY-3002 : Step(115): len = 49610.6, overlap = 11.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0013903
PHY-3002 : Step(116): len = 49538.8, overlap = 11.5625
PHY-3002 : Step(117): len = 49354.6, overlap = 11.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00278061
PHY-3002 : Step(118): len = 49234.8, overlap = 11.9375
PHY-3002 : Step(119): len = 49104.1, overlap = 12.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.193856s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (104.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000119893
PHY-3002 : Step(120): len = 49283.3, overlap = 62.5625
PHY-3002 : Step(121): len = 49804.3, overlap = 55.8125
PHY-3002 : Step(122): len = 50621.6, overlap = 42.75
PHY-3002 : Step(123): len = 51355.1, overlap = 41.2188
PHY-3002 : Step(124): len = 51942.5, overlap = 35.2812
PHY-3002 : Step(125): len = 52000.1, overlap = 35.2188
PHY-3002 : Step(126): len = 51670.2, overlap = 33.2188
PHY-3002 : Step(127): len = 51094.3, overlap = 29.625
PHY-3002 : Step(128): len = 50989.7, overlap = 30.2188
PHY-3002 : Step(129): len = 50796.1, overlap = 34.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000239785
PHY-3002 : Step(130): len = 50846.4, overlap = 34.6562
PHY-3002 : Step(131): len = 50854.5, overlap = 33.4688
PHY-3002 : Step(132): len = 51280.8, overlap = 33
PHY-3002 : Step(133): len = 51452.7, overlap = 33.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00047957
PHY-3002 : Step(134): len = 51586, overlap = 34.875
PHY-3002 : Step(135): len = 51596.2, overlap = 35.4375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7803, tnet num: 2200, tinst num: 1630, tnode num: 11043, tedge num: 13191.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 94.59 peak overflow 3.31
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2202.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54032, over cnt = 252(0%), over = 1076, worst = 15
PHY-1001 : End global iterations;  0.230737s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (155.8%)

PHY-1001 : Congestion index: top1 = 44.74, top5 = 25.16, top10 = 15.85, top15 = 11.19.
PHY-1001 : End incremental global routing;  0.401687s wall, 0.468750s user + 0.062500s system = 0.531250s CPU (132.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.215687s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (101.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.724051s wall, 0.781250s user + 0.078125s system = 0.859375s CPU (118.7%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1627/2202.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54032, over cnt = 252(0%), over = 1076, worst = 15
PHY-1002 : len = 60320, over cnt = 187(0%), over = 509, worst = 14
PHY-1002 : len = 65392, over cnt = 50(0%), over = 98, worst = 13
PHY-1002 : len = 66336, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 66512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.305651s wall, 0.453125s user + 0.031250s system = 0.484375s CPU (158.5%)

PHY-1001 : Congestion index: top1 = 38.53, top5 = 25.93, top10 = 18.00, top15 = 13.17.
OPT-1001 : End congestion update;  0.418030s wall, 0.562500s user + 0.031250s system = 0.593750s CPU (142.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2200 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.168089s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (93.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.591821s wall, 0.718750s user + 0.046875s system = 0.765625s CPU (129.4%)

OPT-1001 : Current memory(MB): used = 217, reserve = 182, peak = 217.
OPT-1001 : End physical optimization;  2.292850s wall, 2.421875s user + 0.156250s system = 2.578125s CPU (112.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 377 LUT to BLE ...
SYN-4008 : Packed 377 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 94 SEQ with LUT/SLICE
SYN-4006 : 120 single LUT's are left
SYN-4006 : 709 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1086/1414 primitive instances ...
PHY-3001 : End packing;  0.165840s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (94.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2034 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1474 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 839 instances, 792 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51639.2, Over = 59
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 839, tnode num: 8941, tedge num: 11578.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  1.081144s wall, 1.062500s user + 0.015625s system = 1.078125s CPU (99.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.39984e-05
PHY-3002 : Step(136): len = 51205, overlap = 58.75
PHY-3002 : Step(137): len = 51124, overlap = 59.5
PHY-3002 : Step(138): len = 50829.1, overlap = 60.5
PHY-3002 : Step(139): len = 50779.7, overlap = 61
PHY-3002 : Step(140): len = 50693.8, overlap = 60.25
PHY-3002 : Step(141): len = 50606.5, overlap = 59.75
PHY-3002 : Step(142): len = 50564.9, overlap = 63
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.79967e-05
PHY-3002 : Step(143): len = 50573.3, overlap = 62
PHY-3002 : Step(144): len = 50721.5, overlap = 62.25
PHY-3002 : Step(145): len = 51368, overlap = 59.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.59934e-05
PHY-3002 : Step(146): len = 52016.6, overlap = 60.25
PHY-3002 : Step(147): len = 52954.9, overlap = 58.75
PHY-3002 : Step(148): len = 54377, overlap = 57.25
PHY-3002 : Step(149): len = 54248.4, overlap = 57
PHY-3002 : Step(150): len = 54142.7, overlap = 56.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.206639s wall, 0.125000s user + 0.296875s system = 0.421875s CPU (204.2%)

PHY-3001 : Trial Legalized: Len = 69092.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.175250s wall, 0.187500s user + 0.046875s system = 0.234375s CPU (133.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00081665
PHY-3002 : Step(151): len = 65749.3, overlap = 6.75
PHY-3002 : Step(152): len = 63691.4, overlap = 11.25
PHY-3002 : Step(153): len = 61755.1, overlap = 14.25
PHY-3002 : Step(154): len = 60117.5, overlap = 18
PHY-3002 : Step(155): len = 59156.9, overlap = 23
PHY-3002 : Step(156): len = 58743.2, overlap = 24
PHY-3002 : Step(157): len = 58470.6, overlap = 26
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0016333
PHY-3002 : Step(158): len = 58746.5, overlap = 25.25
PHY-3002 : Step(159): len = 58832.5, overlap = 25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0032666
PHY-3002 : Step(160): len = 58972.5, overlap = 25.25
PHY-3002 : Step(161): len = 59044.7, overlap = 25.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.017795s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (87.8%)

PHY-3001 : Legalized: Len = 63610.8, Over = 0
PHY-3001 : Spreading special nets. 4 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.017650s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (88.5%)

PHY-3001 : 7 instances has been re-located, deltaX = 3, deltaY = 4, maxDist = 1.
PHY-3001 : Final: Len = 63690.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 839, tnode num: 8941, tedge num: 11578.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 88/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70728, over cnt = 133(0%), over = 213, worst = 7
PHY-1002 : len = 71856, over cnt = 52(0%), over = 63, worst = 3
PHY-1002 : len = 72416, over cnt = 12(0%), over = 17, worst = 3
PHY-1002 : len = 72632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.400991s wall, 0.515625s user + 0.109375s system = 0.625000s CPU (155.9%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.72, top10 = 17.57, top15 = 13.88.
PHY-1001 : End incremental global routing;  0.562639s wall, 0.687500s user + 0.109375s system = 0.796875s CPU (141.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.078093s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (100.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.678957s wall, 0.796875s user + 0.109375s system = 0.906250s CPU (133.5%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1783/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008793s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.29, top5 = 22.72, top10 = 17.57, top15 = 13.88.
OPT-1001 : End congestion update;  0.066166s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (118.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060875s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.7%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 801 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 839 instances, 792 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63770.2, Over = 0
PHY-3001 : End spreading;  0.005805s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 63770.2, Over = 0
PHY-3001 : End incremental legalization;  0.044022s wall, 0.046875s user + 0.031250s system = 0.078125s CPU (177.5%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.189996s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (123.4%)

OPT-1001 : Current memory(MB): used = 226, reserve = 191, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072880s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.8%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1777/2034.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72736, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.014351s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (108.9%)

PHY-1001 : Congestion index: top1 = 31.19, top5 = 22.66, top10 = 17.57, top15 = 13.88.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061168s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.827586
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  2.040979s wall, 2.171875s user + 0.171875s system = 2.343750s CPU (114.8%)

RUN-1003 : finish command "place" in  13.527421s wall, 19.562500s user + 9.062500s system = 28.625000s CPU (211.6%)

RUN-1004 : used memory is 204 MB, reserved memory is 168 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 841 instances
RUN-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2034 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1474 nets have 2 pins
RUN-1001 : 442 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 839, tnode num: 8941, tedge num: 11578.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 396 mslices, 396 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2032 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69800, over cnt = 136(0%), over = 215, worst = 7
PHY-1002 : len = 70656, over cnt = 92(0%), over = 119, worst = 4
PHY-1002 : len = 71928, over cnt = 14(0%), over = 20, worst = 4
PHY-1002 : len = 72216, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.140685s wall, 0.156250s user + 0.046875s system = 0.203125s CPU (144.4%)

PHY-1001 : Congestion index: top1 = 31.06, top5 = 22.65, top10 = 17.51, top15 = 13.85.
PHY-1001 : End global routing;  0.198699s wall, 0.203125s user + 0.062500s system = 0.265625s CPU (133.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 207, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 468, peak = 500.
PHY-1001 : End build detailed router design. 3.606572s wall, 3.578125s user + 0.015625s system = 3.593750s CPU (99.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33544, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.729832s wall, 1.718750s user + 0.015625s system = 1.734375s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 532, reserve = 502, peak = 532.
PHY-1001 : End phase 1; 1.738808s wall, 1.734375s user + 0.015625s system = 1.750000s CPU (100.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 39% nets.
PHY-1001 : Routed 49% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180480, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End initial routed; 1.350389s wall, 2.218750s user + 0.250000s system = 2.468750s CPU (182.8%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1801(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.557   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.189   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.431231s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (101.5%)

PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End phase 2; 1.781743s wall, 2.656250s user + 0.250000s system = 2.906250s CPU (163.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180480, over cnt = 14(0%), over = 14, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018732s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (83.4%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180416, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032151s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (97.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180448, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.024596s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (127.1%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1801(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.557   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.189   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.419832s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (100.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 1 feed throughs used by 1 nets
PHY-1001 : End commit to database; 0.195737s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.8%)

PHY-1001 : Current memory(MB): used = 551, reserve = 521, peak = 551.
PHY-1001 : End phase 3; 0.839410s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (98.7%)

PHY-1003 : Routed, final wirelength = 180448
PHY-1001 : Current memory(MB): used = 552, reserve = 521, peak = 552.
PHY-1001 : End export database. 0.013824s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  8.184273s wall, 9.031250s user + 0.281250s system = 9.312500s CPU (113.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6589, tnet num: 2032, tinst num: 839, tnode num: 8941, tedge num: 11578.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[23] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[24] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[26] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[35] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[8] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.addra[8] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6617, tnet num: 2046, tinst num: 853, tnode num: 8969, tedge num: 11606.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.657842s wall, 3.562500s user + 0.312500s system = 3.875000s CPU (105.9%)

RUN-1003 : finish command "route" in  12.418820s wall, 13.171875s user + 0.656250s system = 13.828125s CPU (111.3%)

RUN-1004 : used memory is 525 MB, reserved memory is 495 MB, peak memory is 552 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      853   out of  19600    4.35%
#reg                     1074   out of  19600    5.48%
#le                      1562
  #lut only               488   out of   1562   31.24%
  #reg only               709   out of   1562   45.39%
  #lut&reg                365   out of   1562   23.37%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       471
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       109
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_11.q0    45
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1562   |632     |221     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1156   |326     |128     |922     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |22     |16      |6       |15      |0       |0       |
|    demodu                  |Demodulation                                     |555    |148     |53      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |62      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |2       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |139    |16      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |91     |29      |21      |87      |0       |1       |
|    rs422                   |Rs422Output                                      |319    |92      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |20      |0       |0       |
|  u_uart                    |UART_Control                                     |106    |92      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |29     |22      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |55     |51      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |217    |172     |45      |73      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1452  
    #2          2       317   
    #3          3       108   
    #4          4        17   
    #5        5-10       78   
    #6        11-50      31   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6617, tnet num: 2046, tinst num: 853, tnode num: 8969, tedge num: 11606.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2046 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 853
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2048, pip num: 14698
BIT-1002 : Init feedthrough completely, num: 1
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1305 valid insts, and 39176 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.125177s wall, 17.531250s user + 0.078125s system = 17.609375s CPU (563.5%)

RUN-1004 : used memory is 523 MB, reserved memory is 491 MB, peak memory is 675 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231010_171811.log"
