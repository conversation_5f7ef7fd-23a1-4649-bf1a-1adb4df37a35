============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Oct 10 18:21:35 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1641 instances
RUN-0007 : 377 luts, 1009 seqs, 133 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2181 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1619 nets have 2 pins
RUN-1001 : 446 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1639 instances, 377 luts, 1009 seqs, 206 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7782, tnet num: 2179, tinst num: 1639, tnode num: 11044, tedge num: 13154.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.276553s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (96.0%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 635500
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1639.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 527140, overlap = 20.25
PHY-3002 : Step(2): len = 486486, overlap = 20.25
PHY-3002 : Step(3): len = 409917, overlap = 18
PHY-3002 : Step(4): len = 353442, overlap = 13.5
PHY-3002 : Step(5): len = 325407, overlap = 15.75
PHY-3002 : Step(6): len = 297730, overlap = 18
PHY-3002 : Step(7): len = 288670, overlap = 20.25
PHY-3002 : Step(8): len = 276359, overlap = 20.25
PHY-3002 : Step(9): len = 268181, overlap = 20.25
PHY-3002 : Step(10): len = 262727, overlap = 20.25
PHY-3002 : Step(11): len = 254617, overlap = 20.25
PHY-3002 : Step(12): len = 243333, overlap = 20.25
PHY-3002 : Step(13): len = 239553, overlap = 20.25
PHY-3002 : Step(14): len = 233892, overlap = 20.25
PHY-3002 : Step(15): len = 226235, overlap = 20.25
PHY-3002 : Step(16): len = 219167, overlap = 20.25
PHY-3002 : Step(17): len = 216854, overlap = 20.25
PHY-3002 : Step(18): len = 209157, overlap = 20.25
PHY-3002 : Step(19): len = 197936, overlap = 20.25
PHY-3002 : Step(20): len = 193548, overlap = 20.25
PHY-3002 : Step(21): len = 190276, overlap = 20.25
PHY-3002 : Step(22): len = 151774, overlap = 18
PHY-3002 : Step(23): len = 148445, overlap = 20.25
PHY-3002 : Step(24): len = 145137, overlap = 20.25
PHY-3002 : Step(25): len = 143314, overlap = 20.25
PHY-3002 : Step(26): len = 137033, overlap = 20.25
PHY-3002 : Step(27): len = 133453, overlap = 20.25
PHY-3002 : Step(28): len = 130555, overlap = 20.25
PHY-3002 : Step(29): len = 129502, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 7.73278e-05
PHY-3002 : Step(30): len = 131255, overlap = 15.75
PHY-3002 : Step(31): len = 131576, overlap = 11.25
PHY-3002 : Step(32): len = 128970, overlap = 18
PHY-3002 : Step(33): len = 127056, overlap = 18
PHY-3002 : Step(34): len = 124297, overlap = 15.75
PHY-3002 : Step(35): len = 120811, overlap = 13.5
PHY-3002 : Step(36): len = 119824, overlap = 13.5
PHY-3002 : Step(37): len = 116942, overlap = 15.75
PHY-3002 : Step(38): len = 114587, overlap = 13.5
PHY-3002 : Step(39): len = 108641, overlap = 13.5
PHY-3002 : Step(40): len = 107348, overlap = 11.25
PHY-3002 : Step(41): len = 105353, overlap = 13.5
PHY-3002 : Step(42): len = 102772, overlap = 15.75
PHY-3002 : Step(43): len = 97711.5, overlap = 15.75
PHY-3002 : Step(44): len = 95841.1, overlap = 15.75
PHY-3002 : Step(45): len = 94014.5, overlap = 13.5
PHY-3002 : Step(46): len = 93478.8, overlap = 13.5
PHY-3002 : Step(47): len = 92080.6, overlap = 13.5
PHY-3002 : Step(48): len = 89750.5, overlap = 13.5
PHY-3002 : Step(49): len = 87478.2, overlap = 13.75
PHY-3002 : Step(50): len = 84346.1, overlap = 13.75
PHY-3002 : Step(51): len = 83910.7, overlap = 13.75
PHY-3002 : Step(52): len = 82554.2, overlap = 11.4375
PHY-3002 : Step(53): len = 79754, overlap = 11.25
PHY-3002 : Step(54): len = 79329.2, overlap = 13.5
PHY-3002 : Step(55): len = 78569.5, overlap = 9.1875
PHY-3002 : Step(56): len = 77684.9, overlap = 6.875
PHY-3002 : Step(57): len = 77357.3, overlap = 11.375
PHY-3002 : Step(58): len = 76277.9, overlap = 13.625
PHY-3002 : Step(59): len = 75617.9, overlap = 11.4375
PHY-3002 : Step(60): len = 75113.8, overlap = 11.5
PHY-3002 : Step(61): len = 73860.7, overlap = 11.625
PHY-3002 : Step(62): len = 72551.8, overlap = 11.625
PHY-3002 : Step(63): len = 70005.5, overlap = 12.0625
PHY-3002 : Step(64): len = 69063.2, overlap = 9.8125
PHY-3002 : Step(65): len = 68324.6, overlap = 12.0625
PHY-3002 : Step(66): len = 67930.6, overlap = 12.3125
PHY-3002 : Step(67): len = 66751, overlap = 12.4375
PHY-3002 : Step(68): len = 65453.3, overlap = 14.5625
PHY-3002 : Step(69): len = 63474.7, overlap = 14.6875
PHY-3002 : Step(70): len = 63282.5, overlap = 10.1875
PHY-3002 : Step(71): len = 62704.8, overlap = 10.5
PHY-3002 : Step(72): len = 62052.4, overlap = 10.625
PHY-3002 : Step(73): len = 60834.5, overlap = 10.5
PHY-3002 : Step(74): len = 59894.2, overlap = 12.75
PHY-3002 : Step(75): len = 58744.6, overlap = 13.1875
PHY-3002 : Step(76): len = 58697.3, overlap = 10.8125
PHY-3002 : Step(77): len = 58332.3, overlap = 11.5
PHY-3002 : Step(78): len = 56628.4, overlap = 11.4375
PHY-3002 : Step(79): len = 55473.8, overlap = 11.9375
PHY-3002 : Step(80): len = 54890.7, overlap = 14.8125
PHY-3002 : Step(81): len = 53840.1, overlap = 12.0625
PHY-3002 : Step(82): len = 53827.9, overlap = 14.25
PHY-3002 : Step(83): len = 53719, overlap = 12.3125
PHY-3002 : Step(84): len = 53606.3, overlap = 12.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000154656
PHY-3002 : Step(85): len = 53509.9, overlap = 14.625
PHY-3002 : Step(86): len = 53455.2, overlap = 14.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000309311
PHY-3002 : Step(87): len = 53689.4, overlap = 12.375
PHY-3002 : Step(88): len = 53691.1, overlap = 12.375
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005644s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.063146s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (99.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000545933
PHY-3002 : Step(89): len = 55902.3, overlap = 15.8125
PHY-3002 : Step(90): len = 54988.1, overlap = 16.0312
PHY-3002 : Step(91): len = 54925.8, overlap = 16.2812
PHY-3002 : Step(92): len = 54479.9, overlap = 15.7188
PHY-3002 : Step(93): len = 54316.2, overlap = 16.3438
PHY-3002 : Step(94): len = 53520.2, overlap = 16.3438
PHY-3002 : Step(95): len = 52780.1, overlap = 16.3438
PHY-3002 : Step(96): len = 52069.6, overlap = 15.9062
PHY-3002 : Step(97): len = 51783.1, overlap = 16.0625
PHY-3002 : Step(98): len = 51023.6, overlap = 16.875
PHY-3002 : Step(99): len = 50707.5, overlap = 16.5
PHY-3002 : Step(100): len = 49883.2, overlap = 16.5625
PHY-3002 : Step(101): len = 49500.1, overlap = 15.5312
PHY-3002 : Step(102): len = 49194.5, overlap = 15.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059446s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (105.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000130203
PHY-3002 : Step(103): len = 49497.8, overlap = 58.7188
PHY-3002 : Step(104): len = 49381.2, overlap = 58.1875
PHY-3002 : Step(105): len = 50160.2, overlap = 53.375
PHY-3002 : Step(106): len = 50634.9, overlap = 53.3125
PHY-3002 : Step(107): len = 50794.5, overlap = 48.8438
PHY-3002 : Step(108): len = 50749.9, overlap = 46.1875
PHY-3002 : Step(109): len = 50446.6, overlap = 45.9375
PHY-3002 : Step(110): len = 50342.4, overlap = 44.1875
PHY-3002 : Step(111): len = 50390.1, overlap = 41.6562
PHY-3002 : Step(112): len = 50137.6, overlap = 41.6875
PHY-3002 : Step(113): len = 49740.2, overlap = 39.5625
PHY-3002 : Step(114): len = 49707.2, overlap = 39.1562
PHY-3002 : Step(115): len = 49550.8, overlap = 38.6875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000260407
PHY-3002 : Step(116): len = 49534.5, overlap = 39.0938
PHY-3002 : Step(117): len = 49444.3, overlap = 39.0625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000520814
PHY-3002 : Step(118): len = 49695.3, overlap = 38.2188
PHY-3002 : Step(119): len = 49954.5, overlap = 38.375
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7782, tnet num: 2179, tinst num: 1639, tnode num: 11044, tedge num: 13154.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.34 peak overflow 3.06
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2181.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53760, over cnt = 237(0%), over = 966, worst = 24
PHY-1001 : End global iterations;  0.066851s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (140.2%)

PHY-1001 : Congestion index: top1 = 40.45, top5 = 24.22, top10 = 15.67, top15 = 11.10.
PHY-1001 : End incremental global routing;  0.117734s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (106.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067843s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (115.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.215697s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.7%)

OPT-1001 : Current memory(MB): used = 212, reserve = 177, peak = 212.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1638/2181.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53760, over cnt = 237(0%), over = 966, worst = 24
PHY-1002 : len = 59528, over cnt = 189(0%), over = 454, worst = 14
PHY-1002 : len = 64400, over cnt = 36(0%), over = 52, worst = 4
PHY-1002 : len = 65320, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 65512, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.090021s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (104.1%)

PHY-1001 : Congestion index: top1 = 37.69, top5 = 24.46, top10 = 17.71, top15 = 13.00.
OPT-1001 : End congestion update;  0.134364s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (93.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066638s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.203755s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.7%)

OPT-1001 : Current memory(MB): used = 215, reserve = 180, peak = 215.
OPT-1001 : End physical optimization;  0.694250s wall, 0.812500s user + 0.015625s system = 0.828125s CPU (119.3%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 377 LUT to BLE ...
SYN-4008 : Packed 377 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 827 remaining SEQ's ...
SYN-4005 : Packed 113 SEQ with LUT/SLICE
SYN-4006 : 105 single LUT's are left
SYN-4006 : 714 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1091/1404 primitive instances ...
PHY-3001 : End packing;  0.050138s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 826 instances
RUN-1001 : 389 mslices, 388 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2013 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1459 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 824 instances, 777 slices, 24 macros(206 instances: 133 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 50342.8, Over = 65.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6526, tnet num: 2011, tinst num: 824, tnode num: 8862, tedge num: 11475.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2011 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.300529s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (104.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.81561e-05
PHY-3002 : Step(120): len = 50131.8, overlap = 66.75
PHY-3002 : Step(121): len = 49972.8, overlap = 69.5
PHY-3002 : Step(122): len = 49742.3, overlap = 68.75
PHY-3002 : Step(123): len = 49557.3, overlap = 70.25
PHY-3002 : Step(124): len = 49579.2, overlap = 68.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.63121e-05
PHY-3002 : Step(125): len = 49736.9, overlap = 67.25
PHY-3002 : Step(126): len = 50354.3, overlap = 67
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000112624
PHY-3002 : Step(127): len = 50995.4, overlap = 66
PHY-3002 : Step(128): len = 52609, overlap = 56.75
PHY-3002 : Step(129): len = 53565.6, overlap = 53.75
PHY-3002 : Step(130): len = 53347.5, overlap = 54.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.079522s wall, 0.046875s user + 0.140625s system = 0.187500s CPU (235.8%)

PHY-3001 : Trial Legalized: Len = 66063.1
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2011 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054906s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (113.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000815914
PHY-3002 : Step(131): len = 63625, overlap = 4.75
PHY-3002 : Step(132): len = 61674.5, overlap = 9.25
PHY-3002 : Step(133): len = 59857.1, overlap = 11
PHY-3002 : Step(134): len = 58518.3, overlap = 13.75
PHY-3002 : Step(135): len = 57747.8, overlap = 16.5
PHY-3002 : Step(136): len = 57232.7, overlap = 21
PHY-3002 : Step(137): len = 56957.3, overlap = 22.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00163183
PHY-3002 : Step(138): len = 57215.2, overlap = 21.5
PHY-3002 : Step(139): len = 57269.9, overlap = 19.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00326365
PHY-3002 : Step(140): len = 57374.4, overlap = 20
PHY-3002 : Step(141): len = 57412.1, overlap = 20
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005118s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 61463.5, Over = 0
PHY-3001 : Spreading special nets. 8 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006068s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (257.5%)

PHY-3001 : 15 instances has been re-located, deltaX = 2, deltaY = 11, maxDist = 1.
PHY-3001 : Final: Len = 61655.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6526, tnet num: 2011, tinst num: 824, tnode num: 8862, tedge num: 11475.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 104/2013.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68200, over cnt = 151(0%), over = 212, worst = 7
PHY-1002 : len = 68936, over cnt = 91(0%), over = 115, worst = 4
PHY-1002 : len = 70000, over cnt = 21(0%), over = 25, worst = 2
PHY-1002 : len = 70400, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.129294s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (108.8%)

PHY-1001 : Congestion index: top1 = 30.43, top5 = 22.84, top10 = 17.83, top15 = 14.02.
PHY-1001 : End incremental global routing;  0.179841s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2011 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063189s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.272157s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (103.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 184, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1777/2013.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005950s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.43, top5 = 22.84, top10 = 17.83, top15 = 14.02.
OPT-1001 : End congestion update;  0.053212s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2011 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050694s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.5%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.105558s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (103.6%)

OPT-1001 : Current memory(MB): used = 221, reserve = 186, peak = 221.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2011 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047563s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1777/2013.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70448, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005678s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.43, top5 = 22.84, top10 = 17.83, top15 = 14.02.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2011 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051636s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (121.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.000000
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.823306s wall, 0.828125s user + 0.000000s system = 0.828125s CPU (100.6%)

RUN-1003 : finish command "place" in  5.145695s wall, 7.406250s user + 2.812500s system = 10.218750s CPU (198.6%)

RUN-1004 : used memory is 200 MB, reserved memory is 165 MB, peak memory is 222 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 826 instances
RUN-1001 : 389 mslices, 388 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2013 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1459 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6526, tnet num: 2011, tinst num: 824, tnode num: 8862, tedge num: 11475.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 389 mslices, 388 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2011 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67320, over cnt = 145(0%), over = 218, worst = 9
PHY-1002 : len = 68176, over cnt = 87(0%), over = 112, worst = 4
PHY-1002 : len = 69368, over cnt = 11(0%), over = 14, worst = 2
PHY-1002 : len = 69592, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 69640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.143682s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (152.2%)

PHY-1001 : Congestion index: top1 = 30.06, top5 = 22.52, top10 = 17.63, top15 = 13.84.
PHY-1001 : End global routing;  0.195148s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (136.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 499, reserve = 469, peak = 499.
PHY-1001 : End build detailed router design. 3.299733s wall, 3.203125s user + 0.093750s system = 3.296875s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34264, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.330702s wall, 1.296875s user + 0.046875s system = 1.343750s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 531, reserve = 503, peak = 531.
PHY-1001 : End phase 1; 1.336581s wall, 1.296875s user + 0.046875s system = 1.343750s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 41% nets.
PHY-1001 : Routed 52% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 178352, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End initial routed; 1.062562s wall, 2.109375s user + 0.078125s system = 2.187500s CPU (205.9%)

PHY-1001 : Update timing.....
PHY-1001 : 3/1794(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |  -0.007   |  -0.007   |   1   
RUN-1001 :   Hold   |  -0.111   |  -0.543   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.369217s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (97.3%)

PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End phase 2; 1.431870s wall, 2.468750s user + 0.078125s system = 2.546875s CPU (177.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1001 : ===== OPT Iter 1 =====
PHY-1001 : Processed 1 pins with SWNS 0.135ns STNS 0.000ns FEP 0.
PHY-1001 : End OPT Iter 1; 0.011544s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (135.3%)

PHY-1022 : len = 178368, over cnt = 18(0%), over = 18, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.028460s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (109.8%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178336, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024653s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (63.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178384, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020604s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (151.7%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1794(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.135   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.111   |  -0.543   |   7   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.369769s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.176315s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (88.6%)

PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End phase 3; 0.736506s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (95.5%)

PHY-1003 : Routed, final wirelength = 178384
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.010023s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (155.9%)

PHY-1001 : End detail routing;  7.001469s wall, 7.859375s user + 0.218750s system = 8.078125s CPU (115.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6526, tnet num: 2011, tinst num: 824, tnode num: 8862, tedge num: 11475.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addra[5] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[21] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[22] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[24] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dia[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[37] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[1] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[49] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_68.addra[9] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6564, tnet num: 2030, tinst num: 843, tnode num: 8900, tedge num: 11513.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.102209s wall, 3.093750s user + 0.156250s system = 3.250000s CPU (104.8%)

RUN-1003 : finish command "route" in  10.653037s wall, 11.546875s user + 0.390625s system = 11.937500s CPU (112.1%)

RUN-1004 : used memory is 529 MB, reserved memory is 502 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      832   out of  19600    4.24%
#reg                     1074   out of  19600    5.48%
#le                      1546
  #lut only               472   out of   1546   30.53%
  #reg only               714   out of   1546   46.18%
  #lut&reg                360   out of   1546   23.29%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         470
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         109
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    39
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1546   |626     |206     |1105    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1152   |324     |122     |920     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |22     |16      |6       |17      |0       |0       |
|    demodu                  |Demodulation                                     |560    |163     |53      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |165    |64      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |50     |0       |0       |50      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |12      |0       |25      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |16      |0       |30      |0       |0       |
|    integ                   |Integration                                      |139    |21      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |99     |22      |15      |85      |0       |1       |
|    rs422                   |Rs422Output                                      |306    |81      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |26     |21      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |112    |97      |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |21     |17      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |54     |52      |0       |27      |0       |0       |
|  wendu                     |DS18B20                                          |217    |172     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1442  
    #2          2       314   
    #3          3       104   
    #4          4        18   
    #5        5-10       78   
    #6        11-50      32   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6564, tnet num: 2030, tinst num: 843, tnode num: 8900, tedge num: 11513.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2030 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 843
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2032, pip num: 14740
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1302 valid insts, and 39090 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.055727s wall, 17.484375s user + 0.109375s system = 17.593750s CPU (575.8%)

RUN-1004 : used memory is 547 MB, reserved memory is 516 MB, peak memory is 677 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231010_182135.log"
