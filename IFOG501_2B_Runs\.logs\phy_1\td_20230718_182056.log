============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 18:20:56 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1656 instances
RUN-0007 : 380 luts, 1016 seqs, 138 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2197 nets
RUN-1001 : 1637 nets have 2 pins
RUN-1001 : 446 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1654 instances, 380 luts, 1016 seqs, 211 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7820, tnet num: 2195, tinst num: 1654, tnode num: 11083, tedge num: 13198.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.279611s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 543489
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1654.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 447928, overlap = 18
PHY-3002 : Step(2): len = 342163, overlap = 11.25
PHY-3002 : Step(3): len = 315759, overlap = 15.75
PHY-3002 : Step(4): len = 303797, overlap = 20.25
PHY-3002 : Step(5): len = 296052, overlap = 18
PHY-3002 : Step(6): len = 287199, overlap = 15.75
PHY-3002 : Step(7): len = 281023, overlap = 18
PHY-3002 : Step(8): len = 274813, overlap = 18
PHY-3002 : Step(9): len = 269682, overlap = 18
PHY-3002 : Step(10): len = 260247, overlap = 20.25
PHY-3002 : Step(11): len = 256360, overlap = 20.25
PHY-3002 : Step(12): len = 250423, overlap = 20.25
PHY-3002 : Step(13): len = 245111, overlap = 20.25
PHY-3002 : Step(14): len = 239829, overlap = 20.25
PHY-3002 : Step(15): len = 236515, overlap = 20.25
PHY-3002 : Step(16): len = 229803, overlap = 20.25
PHY-3002 : Step(17): len = 225520, overlap = 20.25
PHY-3002 : Step(18): len = 221275, overlap = 20.25
PHY-3002 : Step(19): len = 217352, overlap = 20.25
PHY-3002 : Step(20): len = 210571, overlap = 20.25
PHY-3002 : Step(21): len = 207592, overlap = 20.25
PHY-3002 : Step(22): len = 203206, overlap = 20.25
PHY-3002 : Step(23): len = 198080, overlap = 20.25
PHY-3002 : Step(24): len = 193366, overlap = 20.25
PHY-3002 : Step(25): len = 190198, overlap = 20.25
PHY-3002 : Step(26): len = 183730, overlap = 20.25
PHY-3002 : Step(27): len = 180430, overlap = 20.25
PHY-3002 : Step(28): len = 176464, overlap = 20.25
PHY-3002 : Step(29): len = 173060, overlap = 20.25
PHY-3002 : Step(30): len = 167936, overlap = 20.25
PHY-3002 : Step(31): len = 162215, overlap = 20.25
PHY-3002 : Step(32): len = 158497, overlap = 20.25
PHY-3002 : Step(33): len = 156605, overlap = 20.25
PHY-3002 : Step(34): len = 136516, overlap = 18
PHY-3002 : Step(35): len = 128013, overlap = 20.25
PHY-3002 : Step(36): len = 126758, overlap = 20.25
PHY-3002 : Step(37): len = 123829, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.52275e-05
PHY-3002 : Step(38): len = 126136, overlap = 9
PHY-3002 : Step(39): len = 125586, overlap = 9
PHY-3002 : Step(40): len = 123096, overlap = 13.5
PHY-3002 : Step(41): len = 120487, overlap = 13.5
PHY-3002 : Step(42): len = 117405, overlap = 13.5
PHY-3002 : Step(43): len = 113156, overlap = 11.25
PHY-3002 : Step(44): len = 112173, overlap = 11.25
PHY-3002 : Step(45): len = 110426, overlap = 11.25
PHY-3002 : Step(46): len = 107542, overlap = 9
PHY-3002 : Step(47): len = 105668, overlap = 9
PHY-3002 : Step(48): len = 103069, overlap = 9
PHY-3002 : Step(49): len = 100916, overlap = 13.5
PHY-3002 : Step(50): len = 99207.5, overlap = 13.5
PHY-3002 : Step(51): len = 96967.8, overlap = 11.25
PHY-3002 : Step(52): len = 96333.8, overlap = 9.0625
PHY-3002 : Step(53): len = 93975.9, overlap = 9
PHY-3002 : Step(54): len = 92334.9, overlap = 11.25
PHY-3002 : Step(55): len = 90304.8, overlap = 11.375
PHY-3002 : Step(56): len = 89335.6, overlap = 11.625
PHY-3002 : Step(57): len = 86624.1, overlap = 11.6875
PHY-3002 : Step(58): len = 84206.9, overlap = 9.375
PHY-3002 : Step(59): len = 82687.2, overlap = 11.9375
PHY-3002 : Step(60): len = 82214.5, overlap = 12
PHY-3002 : Step(61): len = 80977.4, overlap = 13.8125
PHY-3002 : Step(62): len = 79418.7, overlap = 11.375
PHY-3002 : Step(63): len = 79292.5, overlap = 11.375
PHY-3002 : Step(64): len = 78097.2, overlap = 11.25
PHY-3002 : Step(65): len = 76895.9, overlap = 13.5
PHY-3002 : Step(66): len = 75930.3, overlap = 11.25
PHY-3002 : Step(67): len = 73979.6, overlap = 11.25
PHY-3002 : Step(68): len = 73408.7, overlap = 11.25
PHY-3002 : Step(69): len = 72848.9, overlap = 11.25
PHY-3002 : Step(70): len = 72221, overlap = 11.25
PHY-3002 : Step(71): len = 71385.7, overlap = 13.5
PHY-3002 : Step(72): len = 70503.3, overlap = 11.25
PHY-3002 : Step(73): len = 68983.3, overlap = 9
PHY-3002 : Step(74): len = 68088.3, overlap = 11.25
PHY-3002 : Step(75): len = 67090.8, overlap = 11.25
PHY-3002 : Step(76): len = 66559.1, overlap = 11.25
PHY-3002 : Step(77): len = 66128.6, overlap = 11.25
PHY-3002 : Step(78): len = 65573.5, overlap = 11.25
PHY-3002 : Step(79): len = 65029.3, overlap = 11.25
PHY-3002 : Step(80): len = 64204, overlap = 11.25
PHY-3002 : Step(81): len = 63849, overlap = 11.25
PHY-3002 : Step(82): len = 63333.5, overlap = 11.25
PHY-3002 : Step(83): len = 63062.4, overlap = 11.25
PHY-3002 : Step(84): len = 62359.8, overlap = 9
PHY-3002 : Step(85): len = 61411, overlap = 9
PHY-3002 : Step(86): len = 59677.2, overlap = 15.75
PHY-3002 : Step(87): len = 58775.5, overlap = 11.25
PHY-3002 : Step(88): len = 58306.5, overlap = 11.25
PHY-3002 : Step(89): len = 57955.9, overlap = 13.5
PHY-3002 : Step(90): len = 57624.4, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000190455
PHY-3002 : Step(91): len = 57451, overlap = 9
PHY-3002 : Step(92): len = 57437.2, overlap = 9
PHY-3002 : Step(93): len = 57300.2, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.00038091
PHY-3002 : Step(94): len = 57407.7, overlap = 6.75
PHY-3002 : Step(95): len = 57429.7, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006482s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (241.1%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067146s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(96): len = 60800.6, overlap = 4
PHY-3002 : Step(97): len = 59943.6, overlap = 3.4375
PHY-3002 : Step(98): len = 59578, overlap = 3.3125
PHY-3002 : Step(99): len = 58696.5, overlap = 4.125
PHY-3002 : Step(100): len = 58097.9, overlap = 4.625
PHY-3002 : Step(101): len = 56823.4, overlap = 5.4375
PHY-3002 : Step(102): len = 56101.5, overlap = 4.8125
PHY-3002 : Step(103): len = 55349.4, overlap = 6.25
PHY-3002 : Step(104): len = 54773.2, overlap = 6.3125
PHY-3002 : Step(105): len = 53289.3, overlap = 6.5
PHY-3002 : Step(106): len = 52508.7, overlap = 6.25
PHY-3002 : Step(107): len = 51820.1, overlap = 6.375
PHY-3002 : Step(108): len = 51520.1, overlap = 6.875
PHY-3002 : Step(109): len = 50877.8, overlap = 8.625
PHY-3002 : Step(110): len = 50597, overlap = 7.125
PHY-3002 : Step(111): len = 50291.9, overlap = 8.3125
PHY-3002 : Step(112): len = 49850.7, overlap = 8.1875
PHY-3002 : Step(113): len = 49144.1, overlap = 7.875
PHY-3002 : Step(114): len = 48355.3, overlap = 7.5
PHY-3002 : Step(115): len = 47941.9, overlap = 9.1875
PHY-3002 : Step(116): len = 47629.8, overlap = 8.375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000692696
PHY-3002 : Step(117): len = 47541.9, overlap = 8.5625
PHY-3002 : Step(118): len = 47565.7, overlap = 6.875
PHY-3002 : Step(119): len = 47544.7, overlap = 6.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00138539
PHY-3002 : Step(120): len = 47529.6, overlap = 6.75
PHY-3002 : Step(121): len = 47566.1, overlap = 6.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060737s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.65199e-05
PHY-3002 : Step(122): len = 47752.8, overlap = 52.125
PHY-3002 : Step(123): len = 47918, overlap = 52.5
PHY-3002 : Step(124): len = 48272.5, overlap = 52
PHY-3002 : Step(125): len = 48425.3, overlap = 51.5
PHY-3002 : Step(126): len = 48621.3, overlap = 50.8125
PHY-3002 : Step(127): len = 48727.9, overlap = 50.5
PHY-3002 : Step(128): len = 48980.3, overlap = 51.8438
PHY-3002 : Step(129): len = 49174.5, overlap = 51.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00019304
PHY-3002 : Step(130): len = 49237.1, overlap = 46.4688
PHY-3002 : Step(131): len = 49332.6, overlap = 46.2188
PHY-3002 : Step(132): len = 49709.2, overlap = 38.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00038608
PHY-3002 : Step(133): len = 50142.1, overlap = 37.125
PHY-3002 : Step(134): len = 50408.3, overlap = 36.3125
PHY-3002 : Step(135): len = 51164.9, overlap = 35.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7820, tnet num: 2195, tinst num: 1654, tnode num: 11083, tedge num: 13198.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 95.59 peak overflow 2.59
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2197.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55320, over cnt = 259(0%), over = 1143, worst = 19
PHY-1001 : End global iterations;  0.067989s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (160.9%)

PHY-1001 : Congestion index: top1 = 44.16, top5 = 25.97, top10 = 16.63, top15 = 11.72.
PHY-1001 : End incremental global routing;  0.117288s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (119.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2195 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071246s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (109.7%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1615 has valid locations, 3 needs to be replaced
PHY-3001 : design contains 1656 instances, 380 luts, 1018 seqs, 211 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 51404.2
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7828, tnet num: 2197, tinst num: 1656, tnode num: 11097, tedge num: 13210.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.307772s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(136): len = 51457.6, overlap = 0.75
PHY-3002 : Step(137): len = 51467.8, overlap = 0.75
PHY-3002 : Step(138): len = 51452.2, overlap = 0.75
PHY-3002 : Step(139): len = 51448.4, overlap = 0.75
PHY-3002 : Step(140): len = 51448.4, overlap = 0.75
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.058956s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (106.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000192394
PHY-3002 : Step(141): len = 51458.3, overlap = 35.8438
PHY-3002 : Step(142): len = 51458.3, overlap = 35.8438
PHY-3001 : Final: Len = 51458.3, Over = 35.8438
PHY-3001 : End incremental placement;  0.455307s wall, 0.390625s user + 0.109375s system = 0.500000s CPU (109.8%)

OPT-1001 : Total overflow 95.59 peak overflow 2.59
OPT-1001 : End high-fanout net optimization;  0.679648s wall, 0.625000s user + 0.125000s system = 0.750000s CPU (110.4%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1732/2199.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55504, over cnt = 259(0%), over = 1139, worst = 19
PHY-1002 : len = 63344, over cnt = 180(0%), over = 433, worst = 13
PHY-1002 : len = 67088, over cnt = 97(0%), over = 135, worst = 7
PHY-1002 : len = 68344, over cnt = 39(0%), over = 45, worst = 3
PHY-1002 : len = 69224, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.094375s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (115.9%)

PHY-1001 : Congestion index: top1 = 38.86, top5 = 26.34, top10 = 18.73, top15 = 13.81.
OPT-1001 : End congestion update;  0.136106s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (114.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2197 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057845s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.196505s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 221.
OPT-1001 : End physical optimization;  1.158109s wall, 1.093750s user + 0.156250s system = 1.250000s CPU (107.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 380 LUT to BLE ...
SYN-4008 : Packed 380 LUT and 189 SEQ to BLE.
SYN-4003 : Packing 829 remaining SEQ's ...
SYN-4005 : Packed 87 SEQ with LUT/SLICE
SYN-4006 : 125 single LUT's are left
SYN-4006 : 742 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1122/1440 primitive instances ...
PHY-3001 : End packing;  0.051963s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.2%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 846 instances
RUN-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2025 nets
RUN-1001 : 1472 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 844 instances, 797 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 51377.4, Over = 64
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2023, tinst num: 844, tnode num: 8924, tedge num: 11533.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.308407s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (101.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.8782e-05
PHY-3002 : Step(143): len = 50913.1, overlap = 64.75
PHY-3002 : Step(144): len = 50667.7, overlap = 66.5
PHY-3002 : Step(145): len = 50370.9, overlap = 67.5
PHY-3002 : Step(146): len = 50301.6, overlap = 69.5
PHY-3002 : Step(147): len = 50392.3, overlap = 70.75
PHY-3002 : Step(148): len = 50337.8, overlap = 71.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.7564e-05
PHY-3002 : Step(149): len = 50587.8, overlap = 69.75
PHY-3002 : Step(150): len = 51176.8, overlap = 66
PHY-3002 : Step(151): len = 51822.9, overlap = 60.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000115128
PHY-3002 : Step(152): len = 52410.3, overlap = 57
PHY-3002 : Step(153): len = 53255.5, overlap = 56.5
PHY-3002 : Step(154): len = 54249, overlap = 55.75
PHY-3002 : Step(155): len = 54346.8, overlap = 55.75
PHY-3002 : Step(156): len = 54162.7, overlap = 54
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.085919s wall, 0.125000s user + 0.125000s system = 0.250000s CPU (291.0%)

PHY-3001 : Trial Legalized: Len = 67581.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.051812s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000668592
PHY-3002 : Step(157): len = 64404.5, overlap = 9
PHY-3002 : Step(158): len = 62125.6, overlap = 12.75
PHY-3002 : Step(159): len = 60007.7, overlap = 21
PHY-3002 : Step(160): len = 58826, overlap = 24.75
PHY-3002 : Step(161): len = 58089.8, overlap = 26.25
PHY-3002 : Step(162): len = 57679.6, overlap = 27.5
PHY-3002 : Step(163): len = 57454.5, overlap = 29.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00133718
PHY-3002 : Step(164): len = 57697.2, overlap = 29.25
PHY-3002 : Step(165): len = 57832.6, overlap = 28
PHY-3002 : Step(166): len = 57832.6, overlap = 28
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00267437
PHY-3002 : Step(167): len = 57979.1, overlap = 28
PHY-3002 : Step(168): len = 58102.5, overlap = 27.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004922s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62757.7, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005948s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 4, deltaY = 7, maxDist = 2.
PHY-3001 : Final: Len = 62949.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2023, tinst num: 844, tnode num: 8924, tedge num: 11533.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 40/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68992, over cnt = 152(0%), over = 224, worst = 6
PHY-1002 : len = 69832, over cnt = 112(0%), over = 131, worst = 3
PHY-1002 : len = 71048, over cnt = 30(0%), over = 34, worst = 2
PHY-1002 : len = 71536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.127540s wall, 0.156250s user + 0.031250s system = 0.187500s CPU (147.0%)

PHY-1001 : Congestion index: top1 = 30.47, top5 = 22.09, top10 = 17.41, top15 = 13.98.
PHY-1001 : End incremental global routing;  0.177089s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (132.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.061147s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.2%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.267862s wall, 0.296875s user + 0.031250s system = 0.328125s CPU (122.5%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1792/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71536, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007533s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (207.4%)

PHY-1001 : Congestion index: top1 = 30.47, top5 = 22.09, top10 = 17.41, top15 = 13.98.
OPT-1001 : End congestion update;  0.056727s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050391s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 806 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 844 instances, 797 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 62958.4, Over = 0
PHY-3001 : End spreading;  0.005729s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62958.4, Over = 0
PHY-3001 : End incremental legalization;  0.036328s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (86.0%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.155777s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (140.4%)

OPT-1001 : Current memory(MB): used = 225, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.047657s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1788/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008600s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (181.7%)

PHY-1001 : Congestion index: top1 = 30.47, top5 = 22.10, top10 = 17.41, top15 = 13.98.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048897s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 29.965517
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.875232s wall, 0.953125s user + 0.046875s system = 1.000000s CPU (114.3%)

RUN-1003 : finish command "place" in  5.725511s wall, 9.171875s user + 2.593750s system = 11.765625s CPU (205.5%)

RUN-1004 : used memory is 208 MB, reserved memory is 172 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 846 instances
RUN-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2025 nets
RUN-1001 : 1472 nets have 2 pins
RUN-1001 : 437 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2023, tinst num: 844, tnode num: 8924, tedge num: 11533.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 398 mslices, 399 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68432, over cnt = 157(0%), over = 229, worst = 7
PHY-1002 : len = 69320, over cnt = 116(0%), over = 135, worst = 3
PHY-1002 : len = 70832, over cnt = 16(0%), over = 19, worst = 2
PHY-1002 : len = 71104, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.123214s wall, 0.171875s user + 0.031250s system = 0.203125s CPU (164.9%)

PHY-1001 : Congestion index: top1 = 30.62, top5 = 22.16, top10 = 17.42, top15 = 13.94.
PHY-1001 : End global routing;  0.174885s wall, 0.218750s user + 0.031250s system = 0.250000s CPU (143.0%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 203, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 501, reserve = 469, peak = 501.
PHY-1001 : End build detailed router design. 3.240902s wall, 3.234375s user + 0.000000s system = 3.234375s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34720, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.255496s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (102.1%)

PHY-1001 : Current memory(MB): used = 534, reserve = 503, peak = 534.
PHY-1001 : End phase 1; 1.261166s wall, 1.250000s user + 0.031250s system = 1.281250s CPU (101.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 40% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 64% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 181928, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End initial routed; 1.021008s wall, 1.859375s user + 0.062500s system = 1.921875s CPU (188.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1803(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.437   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.389314s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (100.3%)

PHY-1001 : Current memory(MB): used = 536, reserve = 504, peak = 536.
PHY-1001 : End phase 2; 1.410408s wall, 2.250000s user + 0.062500s system = 2.312500s CPU (164.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 181928, over cnt = 26(0%), over = 26, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018631s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (167.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 181960, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.031077s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (100.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 181968, over cnt = 2(0%), over = 2, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.028682s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (54.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 181984, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.025932s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (60.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1803(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.437   |   0.000   |   0   
RUN-1001 :   Hold   |   0.114   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.384888s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (97.4%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.175540s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.9%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End phase 3; 0.798578s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (99.8%)

PHY-1003 : Routed, final wirelength = 181984
PHY-1001 : Current memory(MB): used = 550, reserve = 518, peak = 550.
PHY-1001 : End export database. 0.011570s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (135.0%)

PHY-1001 : End detail routing;  6.900493s wall, 7.734375s user + 0.093750s system = 7.828125s CPU (113.4%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2023, tinst num: 844, tnode num: 8924, tedge num: 11533.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.827958s wall, 8.671875s user + 0.156250s system = 8.828125s CPU (112.8%)

RUN-1004 : used memory is 526 MB, reserved memory is 498 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      806   out of  19600    4.11%
#reg                     1077   out of  19600    5.49%
#le                      1548
  #lut only               471   out of   1548   30.43%
  #reg only               742   out of   1548   47.93%
  #lut&reg                335   out of   1548   21.64%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         474
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         111
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    43
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1548   |595     |211     |1108    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1168   |298     |130     |931     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |43     |32      |9       |24      |0       |0       |
|    demodu                  |Demodulation                                     |539    |115     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |161    |60      |20      |129     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |52     |2       |0       |52      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |12      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |26     |14      |0       |26      |0       |0       |
|    integ                   |Integration                                      |137    |17      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |104    |25      |15      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |316    |85      |29      |255     |0       |4       |
|    trans                   |SquareWaveGenerator                              |29     |24      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |112    |103     |7       |55      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |21     |19      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |56     |56      |0       |25      |0       |0       |
|  wendu                     |DS18B20                                          |209    |164     |45      |69      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1436  
    #2          2       311   
    #3          3       110   
    #4          4        16   
    #5        5-10       80   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6568, tnet num: 2023, tinst num: 844, tnode num: 8924, tedge num: 11533.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 844
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2025, pip num: 14751
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1312 valid insts, and 38900 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.123123s wall, 18.312500s user + 0.031250s system = 18.343750s CPU (587.4%)

RUN-1004 : used memory is 551 MB, reserved memory is 519 MB, peak memory is 673 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_182056.log"
