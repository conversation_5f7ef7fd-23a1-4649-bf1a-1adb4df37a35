============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 21 18:21:15 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6833292967936"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1646 instances
RUN-0007 : 373 luts, 1013 seqs, 138 mslices, 73 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2181 nets
RUN-1001 : 1633 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 14 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     255     
RUN-1001 :   No   |  No   |  Yes  |     205     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     314     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 20
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1644 instances, 373 luts, 1013 seqs, 211 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7769, tnet num: 2179, tinst num: 1644, tnode num: 11014, tedge num: 13116.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.307449s wall, 0.281250s user + 0.015625s system = 0.296875s CPU (96.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 556814
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1644.
PHY-3001 : End clustering;  0.000033s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 523671, overlap = 20.25
PHY-3002 : Step(2): len = 425732, overlap = 18
PHY-3002 : Step(3): len = 361638, overlap = 11.25
PHY-3002 : Step(4): len = 340967, overlap = 18
PHY-3002 : Step(5): len = 327450, overlap = 18
PHY-3002 : Step(6): len = 320372, overlap = 18
PHY-3002 : Step(7): len = 313424, overlap = 18
PHY-3002 : Step(8): len = 302611, overlap = 18
PHY-3002 : Step(9): len = 296483, overlap = 20.25
PHY-3002 : Step(10): len = 289982, overlap = 20.25
PHY-3002 : Step(11): len = 280180, overlap = 20.25
PHY-3002 : Step(12): len = 273228, overlap = 20.25
PHY-3002 : Step(13): len = 268372, overlap = 20.25
PHY-3002 : Step(14): len = 261497, overlap = 20.25
PHY-3002 : Step(15): len = 255010, overlap = 20.25
PHY-3002 : Step(16): len = 250429, overlap = 20.25
PHY-3002 : Step(17): len = 245568, overlap = 20.25
PHY-3002 : Step(18): len = 239333, overlap = 20.25
PHY-3002 : Step(19): len = 233438, overlap = 20.25
PHY-3002 : Step(20): len = 229718, overlap = 20.25
PHY-3002 : Step(21): len = 224968, overlap = 20.25
PHY-3002 : Step(22): len = 217943, overlap = 20.25
PHY-3002 : Step(23): len = 213784, overlap = 20.25
PHY-3002 : Step(24): len = 210421, overlap = 20.25
PHY-3002 : Step(25): len = 204110, overlap = 20.25
PHY-3002 : Step(26): len = 198465, overlap = 20.25
PHY-3002 : Step(27): len = 195965, overlap = 20.25
PHY-3002 : Step(28): len = 191421, overlap = 20.25
PHY-3002 : Step(29): len = 182672, overlap = 20.25
PHY-3002 : Step(30): len = 178236, overlap = 20.25
PHY-3002 : Step(31): len = 176285, overlap = 20.25
PHY-3002 : Step(32): len = 169375, overlap = 20.25
PHY-3002 : Step(33): len = 149951, overlap = 20.25
PHY-3002 : Step(34): len = 146579, overlap = 20.25
PHY-3002 : Step(35): len = 144881, overlap = 20.25
PHY-3002 : Step(36): len = 118936, overlap = 20.25
PHY-3002 : Step(37): len = 114793, overlap = 20.25
PHY-3002 : Step(38): len = 113119, overlap = 20.25
PHY-3002 : Step(39): len = 110416, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.03257e-05
PHY-3002 : Step(40): len = 112230, overlap = 20.25
PHY-3002 : Step(41): len = 111679, overlap = 11.25
PHY-3002 : Step(42): len = 110140, overlap = 15.75
PHY-3002 : Step(43): len = 108998, overlap = 15.75
PHY-3002 : Step(44): len = 106163, overlap = 18
PHY-3002 : Step(45): len = 105056, overlap = 6.75
PHY-3002 : Step(46): len = 102016, overlap = 11.25
PHY-3002 : Step(47): len = 100095, overlap = 13.5
PHY-3002 : Step(48): len = 98244.8, overlap = 11.25
PHY-3002 : Step(49): len = 96487.7, overlap = 11.25
PHY-3002 : Step(50): len = 94127, overlap = 13.5
PHY-3002 : Step(51): len = 92530.9, overlap = 11.25
PHY-3002 : Step(52): len = 91452.5, overlap = 11.25
PHY-3002 : Step(53): len = 89397, overlap = 6.75
PHY-3002 : Step(54): len = 85195.7, overlap = 15.8125
PHY-3002 : Step(55): len = 84028.1, overlap = 13.5625
PHY-3002 : Step(56): len = 82844.3, overlap = 11.25
PHY-3002 : Step(57): len = 81870.3, overlap = 11.25
PHY-3002 : Step(58): len = 80672.5, overlap = 11.25
PHY-3002 : Step(59): len = 79400.1, overlap = 11.25
PHY-3002 : Step(60): len = 76672.2, overlap = 11.4375
PHY-3002 : Step(61): len = 69775.2, overlap = 9
PHY-3002 : Step(62): len = 68043.2, overlap = 11.25
PHY-3002 : Step(63): len = 67121.2, overlap = 11.25
PHY-3002 : Step(64): len = 66125.6, overlap = 11.25
PHY-3002 : Step(65): len = 65501.2, overlap = 11.25
PHY-3002 : Step(66): len = 65221.5, overlap = 11.25
PHY-3002 : Step(67): len = 64553.2, overlap = 13.5
PHY-3002 : Step(68): len = 63668.8, overlap = 13.5
PHY-3002 : Step(69): len = 63304.8, overlap = 11.25
PHY-3002 : Step(70): len = 62168.7, overlap = 11.25
PHY-3002 : Step(71): len = 60951.1, overlap = 11.25
PHY-3002 : Step(72): len = 60584.8, overlap = 11.25
PHY-3002 : Step(73): len = 59840.9, overlap = 13.5
PHY-3002 : Step(74): len = 59058, overlap = 11.25
PHY-3002 : Step(75): len = 58464.3, overlap = 11.25
PHY-3002 : Step(76): len = 55757.7, overlap = 11.25
PHY-3002 : Step(77): len = 54943.6, overlap = 11.25
PHY-3002 : Step(78): len = 54621.6, overlap = 9
PHY-3002 : Step(79): len = 54646.9, overlap = 11.25
PHY-3002 : Step(80): len = 54209.6, overlap = 9
PHY-3002 : Step(81): len = 53969.4, overlap = 9
PHY-3002 : Step(82): len = 53933.4, overlap = 11.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000160651
PHY-3002 : Step(83): len = 54005.1, overlap = 9
PHY-3002 : Step(84): len = 54010.3, overlap = 9
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000321303
PHY-3002 : Step(85): len = 54140, overlap = 6.75
PHY-3002 : Step(86): len = 54138.9, overlap = 6.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008627s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.068992s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00394439
PHY-3002 : Step(87): len = 57418, overlap = 12.5312
PHY-3002 : Step(88): len = 56630, overlap = 11.7812
PHY-3002 : Step(89): len = 56357.8, overlap = 11.6562
PHY-3002 : Step(90): len = 55662.6, overlap = 12.0938
PHY-3002 : Step(91): len = 55342.6, overlap = 11.2188
PHY-3002 : Step(92): len = 54431, overlap = 11.5938
PHY-3002 : Step(93): len = 54257.7, overlap = 12.0938
PHY-3002 : Step(94): len = 53608.6, overlap = 12.5625
PHY-3002 : Step(95): len = 52888.2, overlap = 12.4062
PHY-3002 : Step(96): len = 51416.7, overlap = 10.9375
PHY-3002 : Step(97): len = 50866.4, overlap = 10.7812
PHY-3002 : Step(98): len = 50474, overlap = 12.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069431s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000140425
PHY-3002 : Step(99): len = 50611.1, overlap = 54.7812
PHY-3002 : Step(100): len = 51461.5, overlap = 50.7188
PHY-3002 : Step(101): len = 52353.9, overlap = 47.6875
PHY-3002 : Step(102): len = 53292.1, overlap = 41.4375
PHY-3002 : Step(103): len = 53517.2, overlap = 41.5312
PHY-3002 : Step(104): len = 53130.7, overlap = 41.4375
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00028085
PHY-3002 : Step(105): len = 52975.4, overlap = 41.9062
PHY-3002 : Step(106): len = 52456.6, overlap = 41
PHY-3002 : Step(107): len = 52219.2, overlap = 43.9688
PHY-3002 : Step(108): len = 52108.2, overlap = 43
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0005617
PHY-3002 : Step(109): len = 52404.3, overlap = 43.5938
PHY-3002 : Step(110): len = 52775.7, overlap = 40.5938
PHY-3002 : Step(111): len = 52900.2, overlap = 40.0625
PHY-3002 : Step(112): len = 52741.6, overlap = 39.1562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7769, tnet num: 2179, tinst num: 1644, tnode num: 11014, tedge num: 13116.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 101.31 peak overflow 3.50
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2181.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56232, over cnt = 252(0%), over = 1079, worst = 21
PHY-1001 : End global iterations;  0.076006s wall, 0.062500s user + 0.031250s system = 0.093750s CPU (123.3%)

PHY-1001 : Congestion index: top1 = 43.23, top5 = 25.88, top10 = 16.52, top15 = 11.71.
PHY-1001 : End incremental global routing;  0.128933s wall, 0.125000s user + 0.031250s system = 0.156250s CPU (121.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.076828s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.7%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.236330s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (112.4%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1668/2181.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56232, over cnt = 252(0%), over = 1079, worst = 21
PHY-1002 : len = 62312, over cnt = 186(0%), over = 490, worst = 21
PHY-1002 : len = 67616, over cnt = 43(0%), over = 67, worst = 8
PHY-1002 : len = 68352, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 68592, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106537s wall, 0.093750s user + 0.015625s system = 0.109375s CPU (102.7%)

PHY-1001 : Congestion index: top1 = 38.23, top5 = 25.58, top10 = 18.51, top15 = 13.72.
OPT-1001 : End congestion update;  0.155148s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (110.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2179 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068092s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (91.8%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.226545s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (103.5%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.763490s wall, 0.750000s user + 0.062500s system = 0.812500s CPU (106.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 187 SEQ to BLE.
SYN-4003 : Packing 826 remaining SEQ's ...
SYN-4005 : Packed 101 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 725 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1098/1416 primitive instances ...
PHY-3001 : End packing;  0.060060s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 834 instances
RUN-1001 : 392 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 423 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 832 instances, 785 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 53074, Over = 64.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6508, tnet num: 2006, tinst num: 832, tnode num: 8832, tedge num: 11436.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.331737s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.8121e-05
PHY-3002 : Step(113): len = 52441.8, overlap = 66.75
PHY-3002 : Step(114): len = 51711.7, overlap = 66.5
PHY-3002 : Step(115): len = 51399, overlap = 67.25
PHY-3002 : Step(116): len = 51129.5, overlap = 69
PHY-3002 : Step(117): len = 50949.3, overlap = 68.5
PHY-3002 : Step(118): len = 50982.9, overlap = 66.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.62421e-05
PHY-3002 : Step(119): len = 51083.9, overlap = 65.75
PHY-3002 : Step(120): len = 51328.3, overlap = 65
PHY-3002 : Step(121): len = 51846.9, overlap = 64.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000112484
PHY-3002 : Step(122): len = 52405.9, overlap = 63.75
PHY-3002 : Step(123): len = 52847.1, overlap = 62.75
PHY-3002 : Step(124): len = 53501.7, overlap = 63.25
PHY-3002 : Step(125): len = 53868.3, overlap = 62.5
PHY-3002 : Step(126): len = 54283.3, overlap = 60
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.120512s wall, 0.109375s user + 0.109375s system = 0.218750s CPU (181.5%)

PHY-3001 : Trial Legalized: Len = 68801
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.057800s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000689062
PHY-3002 : Step(127): len = 66176.5, overlap = 6
PHY-3002 : Step(128): len = 63456.5, overlap = 11
PHY-3002 : Step(129): len = 61277.4, overlap = 18.5
PHY-3002 : Step(130): len = 59999.5, overlap = 21.25
PHY-3002 : Step(131): len = 59111.3, overlap = 24.5
PHY-3002 : Step(132): len = 58270.7, overlap = 27.75
PHY-3002 : Step(133): len = 57934.2, overlap = 30
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00137812
PHY-3002 : Step(134): len = 58196.3, overlap = 29.5
PHY-3002 : Step(135): len = 58292.1, overlap = 29.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00275625
PHY-3002 : Step(136): len = 58363.8, overlap = 29.5
PHY-3002 : Step(137): len = 58363.8, overlap = 29.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005483s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 63862.8, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006407s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (243.9%)

PHY-3001 : 9 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 63966.8, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6508, tnet num: 2006, tinst num: 832, tnode num: 8832, tedge num: 11436.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 48/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69672, over cnt = 153(0%), over = 227, worst = 7
PHY-1002 : len = 70504, over cnt = 100(0%), over = 122, worst = 4
PHY-1002 : len = 71800, over cnt = 9(0%), over = 10, worst = 2
PHY-1002 : len = 71952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.116759s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (174.0%)

PHY-1001 : Congestion index: top1 = 31.03, top5 = 22.25, top10 = 17.45, top15 = 13.97.
PHY-1001 : End incremental global routing;  0.173645s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (153.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068958s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (113.3%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.275923s wall, 0.359375s user + 0.015625s system = 0.375000s CPU (135.9%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 219.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1774/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71952, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007304s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (213.9%)

PHY-1001 : Congestion index: top1 = 31.03, top5 = 22.25, top10 = 17.45, top15 = 13.97.
OPT-1001 : End congestion update;  0.057480s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (108.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054680s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 794 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 832 instances, 785 slices, 25 macros(211 instances: 138 mslices 73 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64006, Over = 0
PHY-3001 : End spreading;  0.006342s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (246.4%)

PHY-3001 : Final: Len = 64006, Over = 0
PHY-3001 : End incremental legalization;  0.038851s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (120.7%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.165555s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.8%)

OPT-1001 : Current memory(MB): used = 224, reserve = 188, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054911s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1770/2008.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72008, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.009796s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (159.5%)

PHY-1001 : Congestion index: top1 = 31.08, top5 = 22.25, top10 = 17.44, top15 = 13.98.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.056451s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (110.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.620690
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.932709s wall, 1.015625s user + 0.015625s system = 1.031250s CPU (110.6%)

RUN-1003 : finish command "place" in  5.702295s wall, 9.640625s user + 3.265625s system = 12.906250s CPU (226.3%)

RUN-1004 : used memory is 201 MB, reserved memory is 164 MB, peak memory is 224 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 834 instances
RUN-1001 : 392 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2008 nets
RUN-1001 : 1471 nets have 2 pins
RUN-1001 : 423 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6508, tnet num: 2006, tinst num: 832, tnode num: 8832, tedge num: 11436.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 392 mslices, 393 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69376, over cnt = 155(0%), over = 228, worst = 7
PHY-1002 : len = 70216, over cnt = 102(0%), over = 123, worst = 4
PHY-1002 : len = 71728, over cnt = 2(0%), over = 3, worst = 2
PHY-1002 : len = 71776, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.125579s wall, 0.171875s user + 0.062500s system = 0.234375s CPU (186.6%)

PHY-1001 : Congestion index: top1 = 30.84, top5 = 22.28, top10 = 17.43, top15 = 13.93.
PHY-1001 : End global routing;  0.181155s wall, 0.234375s user + 0.062500s system = 0.296875s CPU (163.9%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 237, reserve = 202, peak = 246.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 499, reserve = 468, peak = 499.
PHY-1001 : End build detailed router design. 3.441384s wall, 3.359375s user + 0.078125s system = 3.437500s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 33784, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.449802s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 532, reserve = 501, peak = 532.
PHY-1001 : End phase 1; 1.456579s wall, 1.453125s user + 0.000000s system = 1.453125s CPU (99.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 43% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 178936, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 533, reserve = 502, peak = 533.
PHY-1001 : End initial routed; 1.219455s wall, 2.531250s user + 0.109375s system = 2.640625s CPU (216.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1786(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.534   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.416375s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (101.3%)

PHY-1001 : Current memory(MB): used = 535, reserve = 503, peak = 535.
PHY-1001 : End phase 2; 1.635931s wall, 2.953125s user + 0.109375s system = 3.062500s CPU (187.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 178936, over cnt = 25(0%), over = 25, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.018441s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (84.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 178696, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.032974s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.8%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 178680, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020853s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (149.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1786(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.534   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.404794s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (96.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 5 feed throughs used by 5 nets
PHY-1001 : End commit to database; 0.190458s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 546, reserve = 515, peak = 546.
PHY-1001 : End phase 3; 0.796673s wall, 0.796875s user + 0.015625s system = 0.812500s CPU (102.0%)

PHY-1003 : Routed, final wirelength = 178680
PHY-1001 : Current memory(MB): used = 547, reserve = 515, peak = 547.
PHY-1001 : End export database. 0.010686s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.2%)

PHY-1001 : End detail routing;  7.535916s wall, 8.750000s user + 0.203125s system = 8.953125s CPU (118.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6508, tnet num: 2006, tinst num: 832, tnode num: 8832, tedge num: 11436.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.509728s wall, 9.765625s user + 0.265625s system = 10.031250s CPU (117.9%)

RUN-1004 : used memory is 523 MB, reserved memory is 492 MB, peak memory is 547 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      800   out of  19600    4.08%
#reg                     1069   out of  19600    5.45%
#le                      1525
  #lut only               456   out of   1525   29.90%
  #reg only               725   out of   1525   47.54%
  #lut&reg                344   out of   1525   22.56%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                      Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0       468
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3       109
#3        wendu/clk_us                    GCLK               lslice             wendu/cnt_b[2]_syn_10.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1               11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4       1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di             1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1525   |589     |211     |1100    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1150   |302     |126     |915     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |38     |31      |7       |22      |0       |0       |
|    demodu                  |Demodulation                                     |540    |120     |58      |431     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |49     |0       |0       |49      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |28     |12      |0       |28      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |30     |16      |0       |30      |0       |0       |
|    integ                   |Integration                                      |137    |15      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |96     |20      |15      |82      |0       |1       |
|    rs422                   |Rs422Output                                      |314    |94      |29      |254     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |22      |3       |15      |0       |0       |
|  u_uart                    |UART_Control                                     |98     |88      |7       |54      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |28      |7       |14      |0       |0       |
|    U1                      |uart_tx                                          |19     |16      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |44     |44      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |210    |165     |45      |76      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1435  
    #2          2       297   
    #3          3       102   
    #4          4        24   
    #5        5-10       78   
    #6        11-50      29   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6508, tnet num: 2006, tinst num: 832, tnode num: 8832, tedge num: 11436.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2006 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 832
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2008, pip num: 14653
BIT-1002 : Init feedthrough completely, num: 5
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1290 valid insts, and 38757 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.602191s wall, 19.562500s user + 0.078125s system = 19.640625s CPU (545.2%)

RUN-1004 : used memory is 546 MB, reserved memory is 514 MB, peak memory is 666 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230721_182115.log"
