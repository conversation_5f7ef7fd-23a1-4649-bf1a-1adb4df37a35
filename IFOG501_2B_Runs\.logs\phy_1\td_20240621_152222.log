============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 15:22:22 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 9 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 5 view nodes, 38 trigger nets, 38 data nets.
KIT-1004 : Chipwatcher code = 0101111100011010
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=118) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb0100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=118)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=5,BUS_DIN_NUM=38,BUS_CTRL_NUM=96,BUS_WIDTH='{32'sb0100,32'sb01000,32'sb01,32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb0100,32'sb01100,32'sb01101,32'sb01110},BUS_CTRL_POS='{32'sb0,32'sb01100,32'sb0100000,32'sb0100110,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb0100)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01000)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2214/23 useful/useless nets, 1275/12 useful/useless insts
SYN-1016 : Merged 28 instances.
SYN-1032 : 1899/20 useful/useless nets, 1682/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 3 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 3 mux instances.
SYN-1015 : Optimize round 1, 378 better
SYN-1014 : Optimize round 2
SYN-1032 : 1595/45 useful/useless nets, 1378/48 useful/useless insts
SYN-1015 : Optimize round 2, 96 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1643/295 useful/useless nets, 1459/43 useful/useless insts
SYN-1016 : Merged 45 instances.
SYN-2571 : Optimize after map_dsp, round 1, 383 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 36 instances.
SYN-2501 : Optimize round 1, 74 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 16 macro adder
SYN-1019 : Optimized 13 mux instances.
SYN-1016 : Merged 11 instances.
SYN-1032 : 2071/5 useful/useless nets, 1887/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8011, tnet num: 2071, tinst num: 1886, tnode num: 10139, tedge num: 12238.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2071 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 223 (3.47), #lev = 7 (1.82)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 514 instances into 223 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 372 DFF/LATCH to SEQ ...
SYN-4009 : Pack 8 carry chain into lslice
SYN-4007 : Packing 120 adder to BLE ...
SYN-4008 : Packed 120 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.091764s wall, 1.000000s user + 0.093750s system = 1.093750s CPU (100.2%)

RUN-1004 : used memory is 143 MB, reserved memory is 98 MB, peak memory is 162 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (255 clock/control pins, 0 other pins).
SYN-4027 : Net dq_dup_1 is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dq_dup_1 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1302 instances
RUN-0007 : 483 luts, 626 seqs, 86 mslices, 60 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1493 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 831 nets have 2 pins
RUN-1001 : 516 nets have [3 - 5] pins
RUN-1001 : 52 nets have [6 - 10] pins
RUN-1001 : 58 nets have [11 - 20] pins
RUN-1001 : 21 nets have [21 - 99] pins
RUN-1001 : 9 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     146     
RUN-1001 :   No   |  No   |  Yes  |     118     
RUN-1001 :   No   |  Yes  |  No   |     79      
RUN-1001 :   Yes  |  No   |  No   |     32      
RUN-1001 :   Yes  |  No   |  Yes  |     251     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   6   |     11     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 16
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1300 instances, 483 luts, 626 seqs, 146 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-0007 : Cell area utilization is 3%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6744, tnet num: 1491, tinst num: 1300, tnode num: 8967, tedge num: 11211.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.125224s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (99.8%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 349337
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1300.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 3%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 285142, overlap = 76.5
PHY-3002 : Step(2): len = 234830, overlap = 76.5
PHY-3002 : Step(3): len = 207929, overlap = 76.5
PHY-3002 : Step(4): len = 185399, overlap = 76.5
PHY-3002 : Step(5): len = 166167, overlap = 76.5
PHY-3002 : Step(6): len = 148820, overlap = 76.5
PHY-3002 : Step(7): len = 133881, overlap = 76.5
PHY-3002 : Step(8): len = 117849, overlap = 76.5
PHY-3002 : Step(9): len = 104369, overlap = 76.5
PHY-3002 : Step(10): len = 94059.6, overlap = 76.5
PHY-3002 : Step(11): len = 83847.2, overlap = 76.5
PHY-3002 : Step(12): len = 77118.7, overlap = 76.5
PHY-3002 : Step(13): len = 70998, overlap = 76.5
PHY-3002 : Step(14): len = 63127.7, overlap = 76.5
PHY-3002 : Step(15): len = 60235.7, overlap = 76.5
PHY-3002 : Step(16): len = 52863.6, overlap = 76.5
PHY-3002 : Step(17): len = 48920.3, overlap = 76.5
PHY-3002 : Step(18): len = 46254.7, overlap = 76.5
PHY-3002 : Step(19): len = 43327.8, overlap = 76.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.35504e-06
PHY-3002 : Step(20): len = 42965.6, overlap = 72
PHY-3002 : Step(21): len = 42865.7, overlap = 72
PHY-3002 : Step(22): len = 41962.2, overlap = 72
PHY-3002 : Step(23): len = 41790, overlap = 72
PHY-3002 : Step(24): len = 41280.4, overlap = 72
PHY-3002 : Step(25): len = 40281.5, overlap = 72
PHY-3002 : Step(26): len = 40032.7, overlap = 67.5
PHY-3002 : Step(27): len = 39682, overlap = 67.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.71009e-06
PHY-3002 : Step(28): len = 39433.4, overlap = 67.5
PHY-3002 : Step(29): len = 39626.1, overlap = 69.75
PHY-3002 : Step(30): len = 39741.6, overlap = 69.75
PHY-3002 : Step(31): len = 38923.2, overlap = 72.0625
PHY-3002 : Step(32): len = 38455.8, overlap = 72
PHY-3002 : Step(33): len = 38341.8, overlap = 72
PHY-3002 : Step(34): len = 37541.5, overlap = 72
PHY-3002 : Step(35): len = 36921.7, overlap = 72.125
PHY-3002 : Step(36): len = 36477.8, overlap = 72
PHY-3002 : Step(37): len = 35879, overlap = 69.875
PHY-3002 : Step(38): len = 35374.7, overlap = 69.75
PHY-3002 : Step(39): len = 34773.5, overlap = 69.75
PHY-3002 : Step(40): len = 34291.9, overlap = 72
PHY-3002 : Step(41): len = 33296.5, overlap = 72
PHY-3002 : Step(42): len = 32719.6, overlap = 72
PHY-3002 : Step(43): len = 32400.8, overlap = 72
PHY-3002 : Step(44): len = 32091.6, overlap = 72
PHY-3002 : Step(45): len = 31519.9, overlap = 76.5
PHY-3002 : Step(46): len = 31126.8, overlap = 76.5
PHY-3002 : Step(47): len = 31094.5, overlap = 76.5
PHY-3002 : Step(48): len = 30915.5, overlap = 76.5
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.42017e-06
PHY-3002 : Step(49): len = 30942.5, overlap = 76.5
PHY-3002 : Step(50): len = 30946.4, overlap = 76.5
PHY-3002 : Step(51): len = 31004.2, overlap = 74.25
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 1.08403e-05
PHY-3002 : Step(52): len = 31036.8, overlap = 72
PHY-3002 : Step(53): len = 31036.8, overlap = 72
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 2.16807e-05
PHY-3002 : Step(54): len = 31075.6, overlap = 72
PHY-3002 : Step(55): len = 31096, overlap = 72
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 4.33614e-05
PHY-3002 : Step(56): len = 31145.9, overlap = 67.5
PHY-3002 : Step(57): len = 31185.6, overlap = 67.5
PHY-3002 : Step(58): len = 31230, overlap = 67.5
PHY-3001 : :::7::: Try harder cell spreading with beta_ = 7.01587e-05
PHY-3002 : Step(59): len = 31229.8, overlap = 67.5
PHY-3002 : Step(60): len = 31241.7, overlap = 67.5
PHY-3002 : Step(61): len = 31320.9, overlap = 67.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006776s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (230.6%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037213s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0028385
PHY-3002 : Step(62): len = 37359.6, overlap = 11.875
PHY-3002 : Step(63): len = 37415.5, overlap = 12.0312
PHY-3002 : Step(64): len = 37382.1, overlap = 11.8438
PHY-3002 : Step(65): len = 37468.9, overlap = 11.6875
PHY-3002 : Step(66): len = 37739.3, overlap = 12.1875
PHY-3002 : Step(67): len = 37655.5, overlap = 12.5625
PHY-3002 : Step(68): len = 37786.3, overlap = 13.0625
PHY-3002 : Step(69): len = 37974.5, overlap = 12.9688
PHY-3002 : Step(70): len = 38000.6, overlap = 11.9375
PHY-3002 : Step(71): len = 38154.6, overlap = 12.5938
PHY-3002 : Step(72): len = 38264.2, overlap = 14.1875
PHY-3002 : Step(73): len = 38067.8, overlap = 17.1562
PHY-3002 : Step(74): len = 38072.8, overlap = 18.6875
PHY-3002 : Step(75): len = 38014.5, overlap = 19.3125
PHY-3002 : Step(76): len = 37984.9, overlap = 19.625
PHY-3002 : Step(77): len = 37859, overlap = 19.2812
PHY-3002 : Step(78): len = 37812.7, overlap = 19.875
PHY-3002 : Step(79): len = 37925.8, overlap = 19.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.040254s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (116.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.01146e-05
PHY-3002 : Step(80): len = 38428.5, overlap = 48.6562
PHY-3002 : Step(81): len = 38996.8, overlap = 47.6875
PHY-3002 : Step(82): len = 40028.5, overlap = 42.3125
PHY-3002 : Step(83): len = 40793.4, overlap = 41.5312
PHY-3002 : Step(84): len = 41557.4, overlap = 37.1562
PHY-3002 : Step(85): len = 41595.5, overlap = 35.6875
PHY-3002 : Step(86): len = 41394.3, overlap = 34.4375
PHY-3002 : Step(87): len = 40946.2, overlap = 35.8438
PHY-3002 : Step(88): len = 40890.7, overlap = 35.5312
PHY-3002 : Step(89): len = 39852.2, overlap = 35.8438
PHY-3002 : Step(90): len = 39362.6, overlap = 35.4062
PHY-3002 : Step(91): len = 39030.6, overlap = 35.8438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000160229
PHY-3002 : Step(92): len = 39393, overlap = 35.6875
PHY-3002 : Step(93): len = 39514.9, overlap = 35.25
PHY-3002 : Step(94): len = 39718.6, overlap = 35.4688
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000320458
PHY-3002 : Step(95): len = 39633.1, overlap = 34.4688
PHY-3002 : Step(96): len = 39644.8, overlap = 32.3125
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000640917
PHY-3002 : Step(97): len = 39935.9, overlap = 30.75
PHY-3002 : Step(98): len = 40096.3, overlap = 30.5312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6744, tnet num: 1491, tinst num: 1300, tnode num: 8967, tedge num: 11211.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 70.31 peak overflow 3.75
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1493.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52608, over cnt = 217(0%), over = 748, worst = 18
PHY-1001 : End global iterations;  0.076582s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (102.0%)

PHY-1001 : Congestion index: top1 = 37.76, top5 = 21.96, top10 = 14.51, top15 = 10.44.
PHY-1001 : End incremental global routing;  0.124404s wall, 0.093750s user + 0.031250s system = 0.125000s CPU (100.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1491 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044171s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (106.1%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1288 has valid locations, 34 needs to be replaced
PHY-3001 : design contains 1333 instances, 483 luts, 659 seqs, 146 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 40489.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6876, tnet num: 1524, tinst num: 1333, tnode num: 9198, tedge num: 11409.
TMR-2508 : Levelizing timing graph completed, there are 39 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.136454s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (103.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(99): len = 40484.7, overlap = 0.375
PHY-3002 : Step(100): len = 40538.4, overlap = 0.375
PHY-3002 : Step(101): len = 40586.4, overlap = 0.375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 5%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.037604s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000298253
PHY-3002 : Step(102): len = 40582.2, overlap = 30.6562
PHY-3002 : Step(103): len = 40582.2, overlap = 30.6562
PHY-3001 : Final: Len = 40582.2, Over = 30.6562
PHY-3001 : End incremental placement;  0.249341s wall, 0.265625s user + 0.062500s system = 0.328125s CPU (131.6%)

OPT-1001 : Total overflow 71.31 peak overflow 3.75
OPT-1001 : End high-fanout net optimization;  0.447434s wall, 0.437500s user + 0.093750s system = 0.531250s CPU (118.7%)

OPT-1001 : Current memory(MB): used = 199, reserve = 153, peak = 199.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1015/1526.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 53864, over cnt = 218(0%), over = 736, worst = 18
PHY-1002 : len = 58440, over cnt = 144(0%), over = 253, worst = 8
PHY-1002 : len = 58616, over cnt = 79(0%), over = 147, worst = 8
PHY-1002 : len = 60232, over cnt = 13(0%), over = 25, worst = 6
PHY-1002 : len = 60408, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.114560s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (136.4%)

PHY-1001 : Congestion index: top1 = 33.08, top5 = 22.28, top10 = 15.92, top15 = 11.81.
OPT-1001 : End congestion update;  0.156312s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (129.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1524 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.036736s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.1%)

OPT-0007 : Start: WNS 3351 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.193255s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (121.3%)

OPT-1001 : Current memory(MB): used = 197, reserve = 151, peak = 199.
OPT-1001 : End physical optimization;  0.775980s wall, 0.796875s user + 0.093750s system = 0.890625s CPU (114.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 483 LUT to BLE ...
SYN-4008 : Packed 483 LUT and 162 SEQ to BLE.
SYN-4003 : Packing 497 remaining SEQ's ...
SYN-4005 : Packed 267 SEQ with LUT/SLICE
SYN-4006 : 83 single LUT's are left
SYN-4006 : 230 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 713/1040 primitive instances ...
PHY-3001 : End packing;  0.039922s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (117.4%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 600 instances
RUN-1001 : 276 mslices, 277 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1365 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 655 nets have 2 pins
RUN-1001 : 562 nets have [3 - 5] pins
RUN-1001 : 57 nets have [6 - 10] pins
RUN-1001 : 52 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 598 instances, 553 slices, 25 macros(146 instances: 86 mslices 60 lslices)
PHY-3001 : Cell area utilization is 7%
PHY-3001 : After packing: Len = 41502.2, Over = 44
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6075, tnet num: 1363, tinst num: 598, tnode num: 7807, tedge num: 10391.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.145384s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.82649e-05
PHY-3002 : Step(104): len = 41240.6, overlap = 43.75
PHY-3002 : Step(105): len = 41398.4, overlap = 41.75
PHY-3002 : Step(106): len = 41305.6, overlap = 40.5
PHY-3002 : Step(107): len = 41528.7, overlap = 39
PHY-3002 : Step(108): len = 41987.6, overlap = 39.25
PHY-3002 : Step(109): len = 41903.8, overlap = 39.75
PHY-3002 : Step(110): len = 42017.8, overlap = 41
PHY-3002 : Step(111): len = 42392.5, overlap = 40.75
PHY-3002 : Step(112): len = 42253.6, overlap = 39.75
PHY-3002 : Step(113): len = 42276.1, overlap = 39
PHY-3002 : Step(114): len = 42144.3, overlap = 37.75
PHY-3002 : Step(115): len = 42341.6, overlap = 38.25
PHY-3002 : Step(116): len = 42195.4, overlap = 36.25
PHY-3002 : Step(117): len = 41908.3, overlap = 38
PHY-3002 : Step(118): len = 41742.2, overlap = 41.25
PHY-3002 : Step(119): len = 41535.4, overlap = 39
PHY-3002 : Step(120): len = 41504.3, overlap = 39.25
PHY-3002 : Step(121): len = 41517.8, overlap = 39
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.65298e-05
PHY-3002 : Step(122): len = 41492.5, overlap = 39.5
PHY-3002 : Step(123): len = 41676.1, overlap = 39.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00019306
PHY-3002 : Step(124): len = 42129.3, overlap = 38.25
PHY-3002 : Step(125): len = 42953.9, overlap = 32.5
PHY-3002 : Step(126): len = 43232.6, overlap = 31.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.068161s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (229.2%)

PHY-3001 : Trial Legalized: Len = 54560.6
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.033724s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (92.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00117086
PHY-3002 : Step(127): len = 51561.9, overlap = 7.5
PHY-3002 : Step(128): len = 49603, overlap = 8
PHY-3002 : Step(129): len = 48106.4, overlap = 9.25
PHY-3002 : Step(130): len = 47131.5, overlap = 12.25
PHY-3002 : Step(131): len = 46335, overlap = 12.5
PHY-3002 : Step(132): len = 45888.3, overlap = 12.75
PHY-3002 : Step(133): len = 45567.9, overlap = 12.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00234172
PHY-3002 : Step(134): len = 45627.8, overlap = 12.5
PHY-3002 : Step(135): len = 45629.4, overlap = 12.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00468344
PHY-3002 : Step(136): len = 45631.5, overlap = 12.5
PHY-3002 : Step(137): len = 45599.8, overlap = 11.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004819s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 50103.7, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004467s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 3 instances has been re-located, deltaX = 1, deltaY = 2, maxDist = 1.
PHY-3001 : Final: Len = 50127.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6075, tnet num: 1363, tinst num: 598, tnode num: 7807, tedge num: 10391.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 25/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64632, over cnt = 189(0%), over = 287, worst = 4
PHY-1002 : len = 65648, over cnt = 90(0%), over = 119, worst = 4
PHY-1002 : len = 66576, over cnt = 35(0%), over = 44, worst = 3
PHY-1002 : len = 67184, over cnt = 8(0%), over = 8, worst = 1
PHY-1002 : len = 67288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.155816s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (110.3%)

PHY-1001 : Congestion index: top1 = 28.56, top5 = 22.20, top10 = 17.50, top15 = 13.39.
PHY-1001 : End incremental global routing;  0.207037s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (105.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045895s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (102.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.278987s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (106.4%)

OPT-1001 : Current memory(MB): used = 203, reserve = 157, peak = 203.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1205/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.005309s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (294.3%)

PHY-1001 : Congestion index: top1 = 28.56, top5 = 22.20, top10 = 17.50, top15 = 13.39.
OPT-1001 : End congestion update;  0.047596s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.5%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.033188s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.2%)

OPT-0007 : Start: WNS 3719 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.080969s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.5%)

OPT-1001 : Current memory(MB): used = 205, reserve = 159, peak = 205.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.032318s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (96.7%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1205/1365.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67288, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006178s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 28.56, top5 = 22.20, top10 = 17.50, top15 = 13.39.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.033119s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (94.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 3719 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 28.103448
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 3719ps with logic level 7 
OPT-1001 : End physical optimization;  0.619782s wall, 0.671875s user + 0.000000s system = 0.671875s CPU (108.4%)

RUN-1003 : finish command "place" in  4.069366s wall, 5.890625s user + 2.390625s system = 8.281250s CPU (203.5%)

RUN-1004 : used memory is 195 MB, reserved memory is 149 MB, peak memory is 205 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 600 instances
RUN-1001 : 276 mslices, 277 lslices, 8 pads, 34 brams, 0 dsps
RUN-1001 : There are total 1365 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 655 nets have 2 pins
RUN-1001 : 562 nets have [3 - 5] pins
RUN-1001 : 57 nets have [6 - 10] pins
RUN-1001 : 52 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6075, tnet num: 1363, tinst num: 598, tnode num: 7807, tedge num: 10391.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 276 mslices, 277 lslices, 8 pads, 34 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 64320, over cnt = 185(0%), over = 291, worst = 4
PHY-1002 : len = 65288, over cnt = 96(0%), over = 133, worst = 4
PHY-1002 : len = 66408, over cnt = 27(0%), over = 33, worst = 3
PHY-1002 : len = 66912, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 66960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.168072s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (102.3%)

PHY-1001 : Congestion index: top1 = 28.56, top5 = 22.18, top10 = 17.45, top15 = 13.35.
PHY-1001 : End global routing;  0.216179s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (101.2%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 222, reserve = 177, peak = 222.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net dq_dup_1 will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 480, reserve = 439, peak = 480.
PHY-1001 : End build detailed router design. 3.154006s wall, 3.140625s user + 0.015625s system = 3.156250s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 31832, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.596857s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (99.5%)

PHY-1001 : Current memory(MB): used = 513, reserve = 472, peak = 513.
PHY-1001 : End phase 1; 0.603743s wall, 0.609375s user + 0.000000s system = 0.609375s CPU (100.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 35% nets.
PHY-1001 : Routed 43% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 67% nets.
PHY-1001 : Routed 89% nets.
PHY-1022 : len = 246352, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 515, reserve = 473, peak = 516.
PHY-1001 : End initial routed; 2.957672s wall, 3.640625s user + 0.218750s system = 3.859375s CPU (130.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1228(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.779   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.187546s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 516, reserve = 474, peak = 516.
PHY-1001 : End phase 2; 3.145302s wall, 3.828125s user + 0.218750s system = 4.046875s CPU (128.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 246352, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.011364s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (137.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 246144, over cnt = 16(0%), over = 16, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.042366s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (110.6%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 246328, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.027424s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (57.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1228(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.779   |   0.000   |   0   
RUN-1001 :   Hold   |   0.096   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.196335s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 10 feed throughs used by 10 nets
PHY-1001 : End commit to database; 0.178334s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (96.4%)

PHY-1001 : Current memory(MB): used = 530, reserve = 488, peak = 530.
PHY-1001 : End phase 3; 0.564718s wall, 0.546875s user + 0.000000s system = 0.546875s CPU (96.8%)

PHY-1003 : Routed, final wirelength = 246328
PHY-1001 : Current memory(MB): used = 530, reserve = 489, peak = 530.
PHY-1001 : End export database. 0.010366s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (150.7%)

PHY-1001 : End detail routing;  7.656410s wall, 8.312500s user + 0.234375s system = 8.546875s CPU (111.6%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6075, tnet num: 1363, tinst num: 598, tnode num: 7807, tedge num: 10391.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.258413s wall, 8.906250s user + 0.250000s system = 9.156250s CPU (110.9%)

RUN-1004 : used memory is 485 MB, reserved memory is 442 MB, peak memory is 530 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   5
  #inout                    0

Utilization Statistics
#lut                      803   out of  19600    4.10%
#reg                      672   out of  19600    3.43%
#le                      1032
  #lut only               360   out of   1032   34.88%
  #reg only               229   out of   1032   22.19%
  #lut&reg                443   out of   1032   42.93%
#dsp                        0   out of     29    0.00%
#bram                      34   out of     64   53.12%
  #bram9k                  34
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     0
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        dq_dup_1             GCLK               pll                CLK120/pll_inst.clkc0    305
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         140
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
    dq        OUTPUT        B16        LVCMOS33           8            NONE       NONE    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1032   |657     |146     |677     |34      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |87     |63      |11      |62      |0       |0       |
|    usms                            |Time_1ms        |25     |9       |5       |17      |0       |0       |
|  SPIM                              |SPI_MASTER      |188    |131     |23      |130     |0       |0       |
|  uart                              |UART_Control    |122    |113     |7       |59      |0       |0       |
|    U0                              |speed_select_Tx |24     |15      |7       |17      |0       |0       |
|    U1                              |uart_tx         |24     |24      |0       |15      |0       |0       |
|    U2                              |Ctrl_Data       |74     |74      |0       |27      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |600    |330     |99      |398     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |600    |330     |99      |398     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |247    |108     |0       |242     |0       |0       |
|        reg_inst                    |register        |246    |107     |0       |241     |0       |0       |
|        tap_inst                    |tap             |1      |1       |0       |1       |0       |0       |
|      trigger_inst                  |trigger         |353    |222     |99      |156     |0       |0       |
|        bus_inst                    |bus_top         |114    |71      |42      |34      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |15     |8       |6       |3       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |29     |19      |10      |12      |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det         |4      |4       |0       |4       |0       |0       |
|          BUS_DETECTOR[4]$bus_nodes |bus_det         |66     |40      |26      |15      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |139    |93      |29      |84      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       646   
    #2          2       335   
    #3          3       178   
    #4          4        49   
    #5        5-10       63   
    #6        11-50      71   
    #7       51-100      3    
    #8       101-500     1    
  Average     3.15            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6075, tnet num: 1363, tinst num: 598, tnode num: 7807, tedge num: 10391.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1363 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 4b6beeb5811b432c167f3e72935d916dc4f55d6b321f8ec271a9b79ef420b402 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 598
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1365, pip num: 15621
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1360 valid insts, and 40671 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011001110101111100011010
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.190388s wall, 16.953125s user + 0.046875s system = 17.000000s CPU (532.9%)

RUN-1004 : used memory is 501 MB, reserved memory is 461 MB, peak memory is 646 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_152222.log"
