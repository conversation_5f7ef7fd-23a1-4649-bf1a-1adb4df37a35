=========================================================================================================
Auto created by Tang Dynasty v5.6.71036
   Copyright (c) 2012-2023 Anlogic Inc.
Mon Feb 10 15:46:47 2025
=========================================================================================================


Top Model:                IFOG501_2B                                                      
Device:                   eagle_20                                                        
Timing Constraint File:   ../../Constraints/IFOG_11FB.sdc                                 
STA Level:                Detail                                                          
Speed Grade:              NA                                                              

=========================================================================================================
Timing constraint:        clock: clk_in                                                   
Clock = clk_in, period 50ns, rising at 0ns, falling at 25ns

0 endpoints analyzed totally, and 0 paths analyzed
0 errors detected : 0 setup errors (TNS = 0), 0 hold errors (TNS = 0)
Minimum period is 0ns
---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing constraint:        clock: CLK120/pll_inst.clkc[0]                                  
Clock = CLK120/pll_inst.clkc[0], period 10ns, rising at 0ns, falling at 5ns

1338 endpoints analyzed totally, and 15940 paths analyzed
0 errors detected : 0 setup errors (TNS = 0.000), 0 hold errors (TNS = 0.000)
Minimum period is 7.603ns
---------------------------------------------------------------------------------------------------------

Paths for end point cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31 (124 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     2.397 ns                                                        
 Start Point:             cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.351ns  (logic 2.673ns, net 4.678ns, 36% logic)                
 Logic Levels:            7 ( LUT4=4 LUT5=2 LUT2=1 )                                      

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk (dq_dup_1) net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.q[0] clk2q                   0.146 r     2.422
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.d[0] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/bus_reg[4]) net  (fanout = 1)       0.749 r     3.171      C:/Anlogic/TD5.6.2/cw\bus_det.v(35)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.f[0] cell (LUT2)             0.262 r     3.433
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_120.d[1] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_b[4]) net  (fanout = 3)       0.757 r     4.190                    
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_120.f[1] cell (LUT4)             0.205 r     4.395
 cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_192.a[0] (cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_65) net  (fanout = 1)       0.738 r     5.133      C:/Anlogic/TD5.6.2/cw\trigger.sv(57)
 cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_192.f[0] cell (LUT4)             0.408 r     5.541
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_574.a[0] (cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_73) net  (fanout = 1)       0.790 r     6.331      C:/Anlogic/TD5.6.2/cw\trigger.sv(57)
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_574.f[0] cell (LUT5)             0.424 r     6.755
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.a[0] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_n12) net  (fanout = 1)       0.456 r     7.211                    
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.f[0] cell (LUT5)             0.424 r     7.635
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.d[1] (cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_94) net  (fanout = 1)       0.594 r     8.229      C:/Anlogic/TD5.6.2/cw\trigger.sv(57)
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.f[1] cell (LUT4)             0.262 r     8.491
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0] (cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_97) net  (fanout = 1)       0.594 r     9.085      C:/Anlogic/TD5.6.2/cw\trigger.sv(57)
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31             path2reg0 (LUT4)        0.542       9.627
 Arrival time                                                                        9.627                  (7 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.clk (dq_dup_1) net                     2.045       2.045      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      12.045
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      11.929
 clock uncertainty                                                                  -0.000      11.929
 clock recovergence pessimism                                                        0.095      12.024
 Required time                                                                      12.024            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               2.397ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     2.590 ns                                                        
 Start Point:             cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.158ns  (logic 3.713ns, net 3.445ns, 51% logic)                
 Logic Levels:            7 ( LUT4=2 LUT5=2 ADDER=2 LUT2=1 )                              

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk (dq_dup_1) net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.q[0] clk2q                   0.146 r     2.422
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.d[0] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/bus_reg[4]) net  (fanout = 1)       0.749 r     3.171      C:/Anlogic/TD5.6.2/cw\bus_det.v(35)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.f[0] cell (LUT2)             0.262 r     3.433
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_112.a[1] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_b[4]) net  (fanout = 3)       0.612 r     4.045                    
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_112.fco cell (ADDER)            0.627 r     4.672
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_115.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_13) net  (fanout = 1)       0.000 f     4.672      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_115.fco cell (ADDER)            0.073 r     4.745
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_118.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_17) net  (fanout = 1)       0.000 f     4.745      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_118.fco cell (ADDER)            0.073 r     4.818
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_121.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_21) net  (fanout = 1)       0.000 f     4.818      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_121.fco cell (ADDER)            0.073 r     4.891
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_124.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_25) net  (fanout = 1)       0.000 f     4.891      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_124.fco cell (ADDER)            0.073 r     4.964
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_127.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_29) net  (fanout = 1)       0.000 f     4.964      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_127.fco cell (ADDER)            0.073 r     5.037
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_130.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_33) net  (fanout = 1)       0.000 f     5.037      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_130.fco cell (ADDER)            0.073 r     5.110
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_133.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_37) net  (fanout = 1)       0.000 f     5.110      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_133.fco cell (ADDER)            0.073 r     5.183
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_136.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_41) net  (fanout = 1)       0.000 f     5.183      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_136.fco cell (ADDER)            0.073 r     5.256
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_139.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_45) net  (fanout = 1)       0.000 f     5.256      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_139.fco cell (ADDER)            0.073 r     5.329
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_142.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_49) net  (fanout = 1)       0.000 f     5.329      C:/Anlogic/TD5.6.2/cw\bus_det.v(101)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt1_syn_142.f[1] cell (ADDER)            0.355 r     5.684
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.b[1] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_n15) net  (fanout = 2)       0.158 r     5.842                    
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.f[1] cell (LUT5)             0.431 r     6.273
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.b[0] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/sel0_syn_32) net  (fanout = 1)       0.738 r     7.011      C:/Anlogic/TD5.6.2/cw\bus_det.v(97)
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.f[0] cell (LUT5)             0.431 r     7.442
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.d[1] (cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_94) net  (fanout = 1)       0.594 r     8.036      C:/Anlogic/TD5.6.2/cw\trigger.sv(57)
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.f[1] cell (LUT4)             0.262 r     8.298
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0] (cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_97) net  (fanout = 1)       0.594 r     8.892      C:/Anlogic/TD5.6.2/cw\trigger.sv(57)
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31             path2reg0 (LUT4)        0.542       9.434
 Arrival time                                                                        9.434                  (7 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.clk (dq_dup_1) net                     2.045       2.045      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      12.045
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      11.929
 clock uncertainty                                                                  -0.000      11.929
 clock recovergence pessimism                                                        0.095      12.024
 Required time                                                                      12.024            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               2.590ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     2.602 ns                                                        
 Start Point:             cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         7.146ns  (logic 3.544ns, net 3.602ns, 49% logic)                
 Logic Levels:            7 ( LUT4=2 LUT5=2 ADDER=2 LUT2=1 )                              

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.clk (dq_dup_1) net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_141.q[0] clk2q                   0.146 r     2.422
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.d[0] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/bus_reg[4]) net  (fanout = 1)       0.749 r     3.171      C:/Anlogic/TD5.6.2/cw\bus_det.v(35)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/reg0_syn_135.f[0] cell (LUT2)             0.262 r     3.433
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_112.a[1] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_b[4]) net  (fanout = 3)       0.459 r     3.892                    
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_112.fco cell (ADDER)            0.627 r     4.519
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_115.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_13) net  (fanout = 1)       0.000 f     4.519      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_115.fco cell (ADDER)            0.073 r     4.592
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_118.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_17) net  (fanout = 1)       0.000 f     4.592      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_118.fco cell (ADDER)            0.073 r     4.665
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_121.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_21) net  (fanout = 1)       0.000 f     4.665      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_121.fco cell (ADDER)            0.073 r     4.738
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_124.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_25) net  (fanout = 1)       0.000 f     4.738      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_124.fco cell (ADDER)            0.073 r     4.811
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_127.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_29) net  (fanout = 1)       0.000 f     4.811      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_127.fco cell (ADDER)            0.073 r     4.884
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_130.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_33) net  (fanout = 1)       0.000 f     4.884      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_130.fco cell (ADDER)            0.073 r     4.957
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_133.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_37) net  (fanout = 1)       0.000 f     4.957      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_133.fco cell (ADDER)            0.073 r     5.030
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_136.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_41) net  (fanout = 1)       0.000 f     5.030      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_136.fco cell (ADDER)            0.073 r     5.103
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_139.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_45) net  (fanout = 1)       0.000 f     5.103      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_139.fco cell (ADDER)            0.073 r     5.176
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_142.fci (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_49) net  (fanout = 1)       0.000 f     5.176      C:/Anlogic/TD5.6.2/cw\bus_det.v(100)
 cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/lt0_syn_142.f[1] cell (ADDER)            0.355 r     5.531
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.d[1] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/trig_bus_en_n14) net  (fanout = 2)       0.468 r     5.999                    
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.f[1] cell (LUT5)             0.262 r     6.261
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.b[0] (cw_top/wrapper_cwc_top/trigger_inst/bus_inst/BUS_DETECTOR[4]$bus_nodes/sel0_syn_32) net  (fanout = 1)       0.738 r     6.999      C:/Anlogic/TD5.6.2/cw\bus_det.v(97)
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_568.f[0] cell (LUT5)             0.431 r     7.430
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.d[1] (cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_94) net  (fanout = 1)       0.594 r     8.024      C:/Anlogic/TD5.6.2/cw\trigger.sv(57)
 cw_top/wrapper_cwc_top/cfg_int_inst/reg_inst/reg1_syn_606.f[1] cell (LUT4)             0.262 r     8.286
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.a[0] (cw_top/wrapper_cwc_top/trigger_inst/trig_bus_en[4]_syn_97) net  (fanout = 1)       0.594 r     8.880      C:/Anlogic/TD5.6.2/cw\trigger.sv(57)
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31             path2reg0 (LUT4)        0.542       9.422
 Arrival time                                                                        9.422                  (7 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/reg1_syn_31.clk (dq_dup_1) net                     2.045       2.045      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      12.045
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      11.929
 clock uncertainty                                                                  -0.000      11.929
 clock recovergence pessimism                                                        0.095      12.024
 Required time                                                                      12.024            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               2.602ns          

---------------------------------------------------------------------------------------------------------

Paths for end point miso_syn_4 (78 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     3.274 ns                                                        
 Start Point:             CtrlData/reg1_syn_56.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               miso_syn_4.ce (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.582ns  (logic 2.212ns, net 4.370ns, 33% logic)                
 Logic Levels:            6 ( ADDER=2 LUT3=2 LUT2=1 LUT4=1 )                              

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 CtrlData/reg1_syn_56.clk (dq_dup_1)                         net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 CtrlData/reg1_syn_56.q[0]                                   clk2q                   0.146 r     2.422
 SPIM/cnt_sclk_neg_b3[3]_syn_5.d[1] (CtrlData/Data_SPI[17])  net  (fanout = 10)      0.929 r     3.351      ../../Src_al/CtrlData.v(29)
 SPIM/cnt_sclk_neg_b3[3]_syn_5.f[1]                          cell (LUT3)             0.205 r     3.556
 SPIM/sub0_syn_75.a[0] (SPIM/cnt_sclk_neg_b3[3])             net  (fanout = 1)       0.594 r     4.150                    
 SPIM/sub0_syn_75.fco                                        cell (ADDER)            0.706 r     4.856
 SPIM/sub0_syn_78.fci (SPIM/sub0_syn_45)                     net  (fanout = 1)       0.000 f     4.856      ../../Src_al/SPI_MASTER.v(242)
 SPIM/sub0_syn_78.f[1]                                       cell (ADDER)            0.355 r     5.211
 SPIM/reg4_syn_76.b[0] (SPIM/mosi_b[6])                      net  (fanout = 1)       0.594 r     5.805                    
 SPIM/reg4_syn_76.f[0]                                       cell (LUT4)             0.333 r     6.138
 SPIM/sclk_reg_syn_19.d[0] (SPIM/reg7_syn_87)                net  (fanout = 2)       0.456 r     6.594      ../../Src_al/SPI_MASTER.v(236)
 SPIM/sclk_reg_syn_19.f[0]                                   cell (LUT3)             0.262 r     6.856
 SPIM/mux2_syn_133.d[0] (SPIM/reg7_syn_89)                   net  (fanout = 8)       0.730 r     7.586      ../../Src_al/SPI_MASTER.v(236)
 SPIM/mux2_syn_133.f[0]                                      cell (LUT2)             0.205 r     7.791
 miso_syn_4.ce (SPIM/mux2_syn_119)                           net  (fanout = 1)       1.067 r     8.858      ../../Src_al/SPI_MASTER.v(240)
 miso_syn_4                                                  path2reg                0.000       8.858
 Arrival time                                                                        8.858                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 miso_syn_4.ipclk (dq_dup_1)                                 net                     1.857       1.857      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      11.857
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.108      11.965
 clock uncertainty                                                                  -0.000      11.965
 clock recovergence pessimism                                                        0.167      12.132
 Required time                                                                      12.132            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.274ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     3.363 ns                                                        
 Start Point:             SPIM/reg4_syn_74.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               miso_syn_4.ce (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.493ns  (logic 2.260ns, net 4.233ns, 34% logic)                
 Logic Levels:            6 ( ADDER=2 LUT2=1 LUT3=1 LUT4=1 LUT5=1 )                       

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 SPIM/reg4_syn_74.clk (dq_dup_1)                             net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 SPIM/reg4_syn_74.q[0]                                       clk2q                   0.146 r     2.422
 SPIM/lt0_syn_37.a[0] (SPIM/cnt_sclk_pos[1])                 net  (fanout = 7)       0.496 r     2.918      ../../Src_al/SPI_MASTER.v(58)
 SPIM/lt0_syn_37.fco                                         cell (ADDER)            0.706 r     3.624
 SPIM/lt0_syn_40.fci (SPIM/lt0_syn_9)                        net  (fanout = 1)       0.000 f     3.624      ../../Src_al/SPI_MASTER.v(178)
 SPIM/lt0_syn_40.fco                                         cell (ADDER)            0.073 r     3.697
 SPIM/lt0_syn_43.fci (SPIM/lt0_syn_13)                       net  (fanout = 1)       0.000 f     3.697      ../../Src_al/SPI_MASTER.v(178)
 SPIM/lt0_syn_43.f[1]                                        cell (ADDER)            0.355 r     4.052
 SPIM/reg1_syn_34.d[1] (SPIM/mosi_n3)                        net  (fanout = 1)       0.738 r     4.790                    
 SPIM/reg1_syn_34.f[1]                                       cell (LUT5)             0.262 r     5.052
 SPIM/reg4_syn_76.c[0] (SPIM/mosi_i_syn_3)                   net  (fanout = 4)       0.746 r     5.798      ../../Src_al/SPI_MASTER.v(274)
 SPIM/reg4_syn_76.f[0]                                       cell (LUT4)             0.251 r     6.049
 SPIM/sclk_reg_syn_19.d[0] (SPIM/reg7_syn_87)                net  (fanout = 2)       0.456 r     6.505      ../../Src_al/SPI_MASTER.v(236)
 SPIM/sclk_reg_syn_19.f[0]                                   cell (LUT3)             0.262 r     6.767
 SPIM/mux2_syn_133.d[0] (SPIM/reg7_syn_89)                   net  (fanout = 8)       0.730 r     7.497      ../../Src_al/SPI_MASTER.v(236)
 SPIM/mux2_syn_133.f[0]                                      cell (LUT2)             0.205 r     7.702
 miso_syn_4.ce (SPIM/mux2_syn_119)                           net  (fanout = 1)       1.067 r     8.769      ../../Src_al/SPI_MASTER.v(240)
 miso_syn_4                                                  path2reg                0.000       8.769
 Arrival time                                                                        8.769                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 miso_syn_4.ipclk (dq_dup_1)                                 net                     1.857       1.857      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      11.857
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.108      11.965
 clock uncertainty                                                                  -0.000      11.965
 clock recovergence pessimism                                                        0.167      12.132
 Required time                                                                      12.132            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.363ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     3.373 ns                                                        
 Start Point:             SPIM/reg1_syn_38.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               miso_syn_4.ce (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.483ns  (logic 2.254ns, net 4.229ns, 34% logic)                
 Logic Levels:            6 ( ADDER=2 LUT2=1 LUT3=1 LUT4=1 LUT5=1 )                       

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 SPIM/reg1_syn_38.clk (dq_dup_1)                             net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 SPIM/reg1_syn_38.q[0]                                       clk2q                   0.146 r     2.422
 SPIM/lt0_syn_34.a[1] (SPIM/cnt_sclk_pos[0])                 net  (fanout = 7)       0.492 r     2.914      ../../Src_al/SPI_MASTER.v(58)
 SPIM/lt0_syn_34.fco                                         cell (ADDER)            0.627 r     3.541
 SPIM/lt0_syn_37.fci (SPIM/lt0_syn_5)                        net  (fanout = 1)       0.000 f     3.541      ../../Src_al/SPI_MASTER.v(178)
 SPIM/lt0_syn_37.fco                                         cell (ADDER)            0.073 r     3.614
 SPIM/lt0_syn_40.fci (SPIM/lt0_syn_9)                        net  (fanout = 1)       0.000 f     3.614      ../../Src_al/SPI_MASTER.v(178)
 SPIM/lt0_syn_40.fco                                         cell (ADDER)            0.073 r     3.687
 SPIM/lt0_syn_43.fci (SPIM/lt0_syn_13)                       net  (fanout = 1)       0.000 f     3.687      ../../Src_al/SPI_MASTER.v(178)
 SPIM/lt0_syn_43.f[1]                                        cell (ADDER)            0.355 r     4.042
 SPIM/reg1_syn_34.d[1] (SPIM/mosi_n3)                        net  (fanout = 1)       0.738 r     4.780                    
 SPIM/reg1_syn_34.f[1]                                       cell (LUT5)             0.262 r     5.042
 SPIM/reg4_syn_76.c[0] (SPIM/mosi_i_syn_3)                   net  (fanout = 4)       0.746 r     5.788      ../../Src_al/SPI_MASTER.v(274)
 SPIM/reg4_syn_76.f[0]                                       cell (LUT4)             0.251 r     6.039
 SPIM/sclk_reg_syn_19.d[0] (SPIM/reg7_syn_87)                net  (fanout = 2)       0.456 r     6.495      ../../Src_al/SPI_MASTER.v(236)
 SPIM/sclk_reg_syn_19.f[0]                                   cell (LUT3)             0.262 r     6.757
 SPIM/mux2_syn_133.d[0] (SPIM/reg7_syn_89)                   net  (fanout = 8)       0.730 r     7.487      ../../Src_al/SPI_MASTER.v(236)
 SPIM/mux2_syn_133.f[0]                                      cell (LUT2)             0.205 r     7.692
 miso_syn_4.ce (SPIM/mux2_syn_119)                           net  (fanout = 1)       1.067 r     8.759      ../../Src_al/SPI_MASTER.v(240)
 miso_syn_4                                                  path2reg                0.000       8.759
 Arrival time                                                                        8.759                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 miso_syn_4.ipclk (dq_dup_1)                                 net                     1.857       1.857      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      11.857
---------------------------------------------------------------------------------------------------------
 cell setup                                                                          0.108      11.965
 clock uncertainty                                                                  -0.000      11.965
 clock recovergence pessimism                                                        0.167      12.132
 Required time                                                                      12.132            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.373ns          

---------------------------------------------------------------------------------------------------------

Paths for end point CtrlData/reg3_syn_61 (39 paths)
---------------------------------------------------------------------------------------------------------
 Slack (setup check):     3.588 ns                                                        
 Start Point:             CtrlData/reg3_syn_64.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               CtrlData/reg3_syn_61.a[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.260ns  (logic 2.792ns, net 3.468ns, 44% logic)                
 Logic Levels:            6 ( LUT5=3 LUT4=3 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 CtrlData/reg3_syn_64.clk (dq_dup_1)                         net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 CtrlData/reg3_syn_64.q[1]                                   clk2q                   0.146 r     2.422
 CtrlData/tx_state_b[1]_syn_40.a[1] (CtrlData/cnt_time[0])   net  (fanout = 3)       0.602 r     3.024      ../../Src_al/CtrlData.v(42)
 CtrlData/tx_state_b[1]_syn_40.f[1]                          cell (LUT4)             0.424 r     3.448
 CtrlData/tx_state_b[1]_syn_42.a[1] (CtrlData/tx_state_b[1]_syn_4) net  (fanout = 1)       0.459 r     3.907                    
 CtrlData/tx_state_b[1]_syn_42.f[1]                          cell (LUT4)             0.408 r     4.315
 CtrlData/tx_state_b[1]_syn_38.a[1] (CtrlData/tx_state_b[1]_syn_6) net  (fanout = 2)       0.459 r     4.774                    
 CtrlData/tx_state_b[1]_syn_38.f[1]                          cell (LUT5)             0.424 r     5.198
 CtrlData/tx_state_b[1]_syn_36.a[0] (CtrlData/tx_state_b[1]_syn_10) net  (fanout = 2)       0.651 r     5.849                    
 CtrlData/tx_state_b[1]_syn_36.f[0]                          cell (LUT5)             0.424 r     6.273
 CtrlData/tx_state_b[1]_syn_38.a[0] (CtrlData/sel2_syn_226)  net  (fanout = 1)       0.651 r     6.924      ../../Src_al/CtrlData.v(103)
 CtrlData/tx_state_b[1]_syn_38.f[0]                          cell (LUT5)             0.424 r     7.348
 CtrlData/reg3_syn_61.a[1] (CtrlData/sel2_syn_228)           net  (fanout = 10)      0.646 r     7.994      ../../Src_al/CtrlData.v(103)
 CtrlData/reg3_syn_61                                        path2reg1 (LUT4)        0.542       8.536
 Arrival time                                                                        8.536                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 CtrlData/reg3_syn_61.clk (dq_dup_1)                         net                     2.045       2.045      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      12.045
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      11.929
 clock uncertainty                                                                  -0.000      11.929
 clock recovergence pessimism                                                        0.195      12.124
 Required time                                                                      12.124            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.588ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     3.666 ns                                                        
 Start Point:             CtrlData/tx_state_b[1]_syn_42.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               CtrlData/reg3_syn_61.a[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.154ns  (logic 2.799ns, net 3.355ns, 45% logic)                
 Logic Levels:            6 ( LUT5=3 LUT4=3 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 CtrlData/tx_state_b[1]_syn_42.clk (dq_dup_1)                net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 CtrlData/tx_state_b[1]_syn_42.q[0]                          clk2q                   0.146 r     2.422
 CtrlData/tx_state_b[1]_syn_40.b[1] (CtrlData/cnt_time[2])   net  (fanout = 3)       0.489 r     2.911      ../../Src_al/CtrlData.v(42)
 CtrlData/tx_state_b[1]_syn_40.f[1]                          cell (LUT4)             0.431 r     3.342
 CtrlData/tx_state_b[1]_syn_42.a[1] (CtrlData/tx_state_b[1]_syn_4) net  (fanout = 1)       0.459 r     3.801                    
 CtrlData/tx_state_b[1]_syn_42.f[1]                          cell (LUT4)             0.408 r     4.209
 CtrlData/tx_state_b[1]_syn_38.a[1] (CtrlData/tx_state_b[1]_syn_6) net  (fanout = 2)       0.459 r     4.668                    
 CtrlData/tx_state_b[1]_syn_38.f[1]                          cell (LUT5)             0.424 r     5.092
 CtrlData/tx_state_b[1]_syn_36.a[0] (CtrlData/tx_state_b[1]_syn_10) net  (fanout = 2)       0.651 r     5.743                    
 CtrlData/tx_state_b[1]_syn_36.f[0]                          cell (LUT5)             0.424 r     6.167
 CtrlData/tx_state_b[1]_syn_38.a[0] (CtrlData/sel2_syn_226)  net  (fanout = 1)       0.651 r     6.818      ../../Src_al/CtrlData.v(103)
 CtrlData/tx_state_b[1]_syn_38.f[0]                          cell (LUT5)             0.424 r     7.242
 CtrlData/reg3_syn_61.a[1] (CtrlData/sel2_syn_228)           net  (fanout = 10)      0.646 r     7.888      ../../Src_al/CtrlData.v(103)
 CtrlData/reg3_syn_61                                        path2reg1 (LUT4)        0.542       8.430
 Arrival time                                                                        8.430                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 CtrlData/reg3_syn_61.clk (dq_dup_1)                         net                     2.045       2.045      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      12.045
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      11.929
 clock uncertainty                                                                  -0.000      11.929
 clock recovergence pessimism                                                        0.167      12.096
 Required time                                                                      12.096            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.666ns          

---------------------------------------------------------------------------------------------------------

 Slack (setup check):     3.690 ns                                                        
 Start Point:             CtrlData/reg3_syn_61.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               CtrlData/reg3_syn_61.a[1] (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Slow                                                            
 Data Path Delay:         6.194ns  (logic 2.716ns, net 3.478ns, 43% logic)                
 Logic Levels:            6 ( LUT5=3 LUT4=3 )                                             

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 CtrlData/reg3_syn_61.clk (dq_dup_1)                         net                     2.276       2.276      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       2.276
---------------------------------------------------------------------------------------------------------
 CtrlData/reg3_syn_61.q[1]                                   clk2q                   0.146 r     2.422
 CtrlData/tx_state_b[1]_syn_40.c[1] (CtrlData/cnt_time[5])   net  (fanout = 3)       0.612 r     3.034      ../../Src_al/CtrlData.v(42)
 CtrlData/tx_state_b[1]_syn_40.f[1]                          cell (LUT4)             0.348 r     3.382
 CtrlData/tx_state_b[1]_syn_42.a[1] (CtrlData/tx_state_b[1]_syn_4) net  (fanout = 1)       0.459 r     3.841                    
 CtrlData/tx_state_b[1]_syn_42.f[1]                          cell (LUT4)             0.408 r     4.249
 CtrlData/tx_state_b[1]_syn_38.a[1] (CtrlData/tx_state_b[1]_syn_6) net  (fanout = 2)       0.459 r     4.708                    
 CtrlData/tx_state_b[1]_syn_38.f[1]                          cell (LUT5)             0.424 r     5.132
 CtrlData/tx_state_b[1]_syn_36.a[0] (CtrlData/tx_state_b[1]_syn_10) net  (fanout = 2)       0.651 r     5.783                    
 CtrlData/tx_state_b[1]_syn_36.f[0]                          cell (LUT5)             0.424 r     6.207
 CtrlData/tx_state_b[1]_syn_38.a[0] (CtrlData/sel2_syn_226)  net  (fanout = 1)       0.651 r     6.858      ../../Src_al/CtrlData.v(103)
 CtrlData/tx_state_b[1]_syn_38.f[0]                          cell (LUT5)             0.424 r     7.282
 CtrlData/reg3_syn_61.a[1] (CtrlData/sel2_syn_228)           net  (fanout = 10)      0.646 r     7.928      ../../Src_al/CtrlData.v(103)
 CtrlData/reg3_syn_61                                        path2reg1 (LUT4)        0.542       8.470
 Arrival time                                                                        8.470                  (6 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 CtrlData/reg3_syn_61.clk (dq_dup_1)                         net                     2.045       2.045      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                 10.000      12.045
---------------------------------------------------------------------------------------------------------
 cell setup                                                                         -0.116      11.929
 clock uncertainty                                                                  -0.000      11.929
 clock recovergence pessimism                                                        0.231      12.160
 Required time                                                                      12.160            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               3.690ns          

---------------------------------------------------------------------------------------------------------

Hold checks:
---------------------------------------------------------------------------------------------------------
Paths for end point auto_chipwatcher_0_logicbram_syn_311 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.096 ns                                                        
 Start Point:             cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_91.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               auto_chipwatcher_0_logicbram_syn_311.wea (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.341ns  (logic 0.109ns, net 0.232ns, 31% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_91.clk (dq_dup_1) net                     1.938       1.938      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_91.q[0] clk2q                   0.109 r     2.047
 auto_chipwatcher_0_logicbram_syn_311.wea (cw_top_syn_41)    net  (fanout = 2)       0.232 r     2.279                    
 auto_chipwatcher_0_logicbram_syn_311                        path2reg                0.000       2.279
 Arrival time                                                                        2.279                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 auto_chipwatcher_0_logicbram_syn_311.clka (dq_dup_1)        net                     2.130       2.130      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.096ns          

---------------------------------------------------------------------------------------------------------

Paths for end point auto_chipwatcher_0_logicbram_syn_181 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.096 ns                                                        
 Start Point:             cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_85.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               auto_chipwatcher_0_logicbram_syn_181.wea (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.341ns  (logic 0.109ns, net 0.232ns, 31% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_85.clk (dq_dup_1) net                     1.938       1.938      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_85.q[0] clk2q                   0.109 r     2.047
 auto_chipwatcher_0_logicbram_syn_181.wea (cw_top_syn_45)    net  (fanout = 2)       0.232 r     2.279                    
 auto_chipwatcher_0_logicbram_syn_181                        path2reg                0.000       2.279
 Arrival time                                                                        2.279                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 auto_chipwatcher_0_logicbram_syn_181.clka (dq_dup_1)        net                     2.130       2.130      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.096ns          

---------------------------------------------------------------------------------------------------------

Paths for end point auto_chipwatcher_0_logicbram_syn_101 (1 paths)
---------------------------------------------------------------------------------------------------------
 Slack (hold check):      0.096 ns                                                        
 Start Point:             cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_75.clk (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 End Point:               auto_chipwatcher_0_logicbram_syn_101.wea (rising edge triggered by clock CLK120/pll_inst.clkc[0])
 Clock group:             clk_in                                                          
 Process:                 Fast                                                            
 Data Path Delay:         0.341ns  (logic 0.109ns, net 0.232ns, 31% logic)                
 Logic Levels:            0                                                               

 Point                                                       Type                     Incr        Path      Info          
---------------------------------------------------------------------------------------------------------
 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_75.clk (dq_dup_1) net                     1.938       1.938      ../../Src_al/IFOG501_2B.v(20)
 launch clock edge                                                                   0.000       1.938
---------------------------------------------------------------------------------------------------------
 cw_top/wrapper_cwc_top/trigger_inst/emb_ctrl_inst/wt_ce_reg_syn_75.q[0] clk2q                   0.109 r     2.047
 auto_chipwatcher_0_logicbram_syn_101.wea (cw_top_syn_50)    net  (fanout = 2)       0.232 r     2.279                    
 auto_chipwatcher_0_logicbram_syn_101                        path2reg                0.000       2.279
 Arrival time                                                                        2.279                  (0 lvl)       

 source latency                                                                      0.000       0.000                    
 CLK120/pll_inst.clkc[0]                                                             0.000       0.000                    
 auto_chipwatcher_0_logicbram_syn_101.clka (dq_dup_1)        net                     2.130       2.130      ../../Src_al/IFOG501_2B.v(20)
 capture clock edge                                                                  0.000       2.130
---------------------------------------------------------------------------------------------------------
 cell hold                                                                           0.200       2.330
 clock uncertainty                                                                   0.000       2.330
 clock recovergence pessimism                                                       -0.147       2.183
 Required time                                                                       2.183            
---------------------------------------------------------------------------------------------------------
 Slack                                                                               0.096ns          

---------------------------------------------------------------------------------------------------------


=========================================================================================================
Timing summary:                                                                           
---------------------------------------------------------------------------------------------------------
Constraint path number: 15940 (STA coverage = 67.45%)
Timing violations: 0 setup errors, and 0 hold errors.
Minimal setup slack: 2.397, minimal hold slack: 0.096

Timing group statistics: 
	Clock constraints: 
	  Clock Name                                  Min Period     Max Freq           Skew      Fanout            TNS
	  CLK120/pll_inst.clkc[0] (100.0MHz)             7.603ns     131.527MHz        0.480ns       298        0.000ns
	Minimum input arrival time before clock: no constraint path
	Maximum output required time after clock: no constraint path
	Maximum combinational path delay: no constraint path


Warning: No clock constraint on 1 clock net(s): 
	config_inst_syn_10

---------------------------------------------------------------------------------------------------------
