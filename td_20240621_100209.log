============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 10:02:09 2024

   Run on =     TLH-022
============================================================
RUN-001 : GUI based run...
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256 -basic"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "set_param flow syn_ip_flow ip"
RUN-1002 : start command "set_param rtl keep_hierarchy flatten"
RUN-1002 : start command "read_verilog -file div_gen.v"
HDL-1007 : analyze verilog file div_gen.v
Copyright (c) 1998-2019 The OpenSSL Project.  All rights reserved.
This product includes software developed by the OpenSSL Project
for use in the OpenSSL Toolkit (http://www.openssl.org/)
Copyright (C) 1995-1998 Eric Young (<EMAIL>)
All rights reserved.
This product includes cryptographic software written by Eric Young (<EMAIL>)
HDL-1007 : back to file 'div_gen.v' in div_gen.v(190)
HDL-1007 : elaborate module div_gen in div_gen.v(18)
HDL-1200 : Current top model is div_gen
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "optimize_rtl"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |     ip     |       off        |   *    
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |  flatten   |       auto       |   *    
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "div_gen"
SYN-1011 : Flatten model div_gen
SYN-1032 : 615/105 useful/useless nets, 162/0 useful/useless insts
SYN-1016 : Merged 3 instances.
SYN-1032 : 536/4 useful/useless nets, 87/0 useful/useless insts
SYN-1032 : 508/28 useful/useless nets, 406/30 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1032 : 488/20 useful/useless nets, 386/20 useful/useless insts
SYN-1017 : Remove 1 const input seq instances
SYN-1002 :     quo_sign_reg
SYN-1019 : Optimized 34 mux instances.
SYN-1015 : Optimize round 1, 171 better
SYN-1014 : Optimize round 2
SYN-1032 : 291/36 useful/useless nets, 221/38 useful/useless insts
SYN-1015 : Optimize round 2, 74 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -release div_gen_gate.v"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |     ip     |       off        |   *    
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |    auto    |       auto       |        
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
RUN-1002 : start command "update_pll_param -module div_gen"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2571 : Optimize after map_dsp, round 1
SYN-2571 : Optimize after map_dsp, round 1, 0 better
SYN-2501 : Optimize round 1
SYN-2501 : Optimize round 1, 5 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 3 macro adder
SYN-1016 : Merged 2 instances.
SYN-1032 : 367/1 useful/useless nets, 297/0 useful/useless insts
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 46 (4.37), #lev = 2 (1.09)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 45 (4.40), #lev = 2 (1.09)
SYN-3001 : Logic optimization runtime opt =   0.01 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 96 instances into 45 LUTs, name keeping = 91%.
RUN-1002 : start command "write_verilog -nolf -hidename div_gen_gate.v"
HDL-1201 : write out verilog file div_gen_gate.v
GUI-2000 : The IP files have been created successfully :{EG4X20BG256(D:/Project/IFOG501_2B_20M_PLL_256_DACC/al_ip/div_gen.ipc)}
