============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Jul 18 18:10:18 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 -phase 239 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 -phase 239 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
SYN-5055 WARNING: The kept net CLK120/reset will be merged to another kept net pllrst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1622 instances
RUN-0007 : 367 luts, 985 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2193 nets
RUN-1001 : 1634 nets have 2 pins
RUN-1001 : 447 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     208     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1620 instances, 367 luts, 985 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7780, tnet num: 2191, tinst num: 1620, tnode num: 11023, tedge num: 13163.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.273321s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (91.5%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 628634
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1620.
PHY-3001 : End clustering;  0.000056s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 506696, overlap = 20.25
PHY-3002 : Step(2): len = 466666, overlap = 20.25
PHY-3002 : Step(3): len = 417724, overlap = 15.75
PHY-3002 : Step(4): len = 390926, overlap = 15.75
PHY-3002 : Step(5): len = 378078, overlap = 15.75
PHY-3002 : Step(6): len = 364134, overlap = 18
PHY-3002 : Step(7): len = 357108, overlap = 18
PHY-3002 : Step(8): len = 342097, overlap = 18
PHY-3002 : Step(9): len = 328625, overlap = 20.25
PHY-3002 : Step(10): len = 320391, overlap = 20.25
PHY-3002 : Step(11): len = 311632, overlap = 20.25
PHY-3002 : Step(12): len = 293798, overlap = 20.25
PHY-3002 : Step(13): len = 286037, overlap = 20.25
PHY-3002 : Step(14): len = 279445, overlap = 20.25
PHY-3002 : Step(15): len = 268233, overlap = 20.25
PHY-3002 : Step(16): len = 255839, overlap = 20.25
PHY-3002 : Step(17): len = 251427, overlap = 20.25
PHY-3002 : Step(18): len = 243758, overlap = 20.25
PHY-3002 : Step(19): len = 228822, overlap = 20.25
PHY-3002 : Step(20): len = 222529, overlap = 20.25
PHY-3002 : Step(21): len = 220042, overlap = 20.25
PHY-3002 : Step(22): len = 200899, overlap = 20.25
PHY-3002 : Step(23): len = 194181, overlap = 20.25
PHY-3002 : Step(24): len = 192143, overlap = 20.25
PHY-3002 : Step(25): len = 178174, overlap = 20.25
PHY-3002 : Step(26): len = 164479, overlap = 20.25
PHY-3002 : Step(27): len = 162600, overlap = 20.25
PHY-3002 : Step(28): len = 159267, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000133496
PHY-3002 : Step(29): len = 160183, overlap = 13.5
PHY-3002 : Step(30): len = 158934, overlap = 13.5
PHY-3002 : Step(31): len = 156262, overlap = 15.75
PHY-3002 : Step(32): len = 155307, overlap = 15.75
PHY-3002 : Step(33): len = 152854, overlap = 9
PHY-3002 : Step(34): len = 145758, overlap = 13.5
PHY-3002 : Step(35): len = 139685, overlap = 13.5
PHY-3002 : Step(36): len = 137043, overlap = 11.25
PHY-3002 : Step(37): len = 134544, overlap = 13.5
PHY-3002 : Step(38): len = 129344, overlap = 18
PHY-3002 : Step(39): len = 125270, overlap = 6.75
PHY-3002 : Step(40): len = 121625, overlap = 11.25
PHY-3002 : Step(41): len = 120606, overlap = 11.25
PHY-3002 : Step(42): len = 117191, overlap = 11.25
PHY-3002 : Step(43): len = 112495, overlap = 11.25
PHY-3002 : Step(44): len = 108634, overlap = 6.75
PHY-3002 : Step(45): len = 107938, overlap = 9
PHY-3002 : Step(46): len = 105444, overlap = 6.75
PHY-3002 : Step(47): len = 103373, overlap = 6.75
PHY-3002 : Step(48): len = 98369.1, overlap = 13.5
PHY-3002 : Step(49): len = 97528.2, overlap = 13.5
PHY-3002 : Step(50): len = 95733.1, overlap = 11.25
PHY-3002 : Step(51): len = 94956.7, overlap = 9
PHY-3002 : Step(52): len = 94428.9, overlap = 11.25
PHY-3002 : Step(53): len = 93023.8, overlap = 9
PHY-3002 : Step(54): len = 92096.3, overlap = 6.75
PHY-3002 : Step(55): len = 90500.7, overlap = 9
PHY-3002 : Step(56): len = 87811.6, overlap = 9
PHY-3002 : Step(57): len = 86339.4, overlap = 16.4375
PHY-3002 : Step(58): len = 84874.2, overlap = 16.625
PHY-3002 : Step(59): len = 84753.7, overlap = 7.5625
PHY-3002 : Step(60): len = 83147.1, overlap = 9.6875
PHY-3002 : Step(61): len = 79914.6, overlap = 14.3125
PHY-3002 : Step(62): len = 77487.1, overlap = 9.9375
PHY-3002 : Step(63): len = 75470.6, overlap = 10.4375
PHY-3002 : Step(64): len = 74374.9, overlap = 15
PHY-3002 : Step(65): len = 72510.7, overlap = 15.0625
PHY-3002 : Step(66): len = 70720.8, overlap = 14.8125
PHY-3002 : Step(67): len = 69021.1, overlap = 12.5
PHY-3002 : Step(68): len = 68712, overlap = 12.5
PHY-3002 : Step(69): len = 67637.5, overlap = 12.25
PHY-3002 : Step(70): len = 64635.1, overlap = 12.375
PHY-3002 : Step(71): len = 63643.4, overlap = 12.4375
PHY-3002 : Step(72): len = 62787.4, overlap = 12.4375
PHY-3002 : Step(73): len = 62442.2, overlap = 10.1875
PHY-3002 : Step(74): len = 61742.5, overlap = 10.125
PHY-3002 : Step(75): len = 60930.5, overlap = 10.5
PHY-3002 : Step(76): len = 60207.7, overlap = 15
PHY-3002 : Step(77): len = 59884.2, overlap = 15.0625
PHY-3002 : Step(78): len = 59549.5, overlap = 12.875
PHY-3002 : Step(79): len = 58900.6, overlap = 12.9375
PHY-3002 : Step(80): len = 56733.3, overlap = 11.3125
PHY-3002 : Step(81): len = 54541.7, overlap = 14.6875
PHY-3002 : Step(82): len = 53852.8, overlap = 14.875
PHY-3002 : Step(83): len = 53300.8, overlap = 14.875
PHY-3002 : Step(84): len = 53057.7, overlap = 14.875
PHY-3002 : Step(85): len = 52861.3, overlap = 14.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000266992
PHY-3002 : Step(86): len = 52794.1, overlap = 12.625
PHY-3002 : Step(87): len = 52796.5, overlap = 12.625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000533984
PHY-3002 : Step(88): len = 52884.7, overlap = 12.625
PHY-3002 : Step(89): len = 52916.9, overlap = 12.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007092s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (661.0%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061599s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(90): len = 55382.1, overlap = 12.7812
PHY-3002 : Step(91): len = 54542.4, overlap = 12.625
PHY-3002 : Step(92): len = 54378, overlap = 12.9375
PHY-3002 : Step(93): len = 53796, overlap = 13.1562
PHY-3002 : Step(94): len = 53399.1, overlap = 13.875
PHY-3002 : Step(95): len = 52477, overlap = 13.5
PHY-3002 : Step(96): len = 51849.4, overlap = 13.9062
PHY-3002 : Step(97): len = 51273.4, overlap = 13.9688
PHY-3002 : Step(98): len = 50566.3, overlap = 14.25
PHY-3002 : Step(99): len = 49234.9, overlap = 14.2812
PHY-3002 : Step(100): len = 48628.1, overlap = 14.9062
PHY-3002 : Step(101): len = 48211.2, overlap = 16.1562
PHY-3002 : Step(102): len = 48037.7, overlap = 16.3438
PHY-3002 : Step(103): len = 47791.4, overlap = 16.125
PHY-3002 : Step(104): len = 47376.7, overlap = 16
PHY-3002 : Step(105): len = 47444.9, overlap = 16.1875
PHY-3002 : Step(106): len = 47131.2, overlap = 18.0312
PHY-3002 : Step(107): len = 46653.7, overlap = 20.5625
PHY-3002 : Step(108): len = 46086.6, overlap = 20.1875
PHY-3002 : Step(109): len = 45594.3, overlap = 20.4375
PHY-3002 : Step(110): len = 44295.6, overlap = 22.8125
PHY-3002 : Step(111): len = 43512, overlap = 23.5312
PHY-3002 : Step(112): len = 43348.9, overlap = 23.25
PHY-3002 : Step(113): len = 43048, overlap = 21.875
PHY-3002 : Step(114): len = 42942.8, overlap = 22.375
PHY-3002 : Step(115): len = 43446.6, overlap = 21.8125
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000207741
PHY-3002 : Step(116): len = 43020.1, overlap = 21.6875
PHY-3002 : Step(117): len = 42800.6, overlap = 21.5625
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000415483
PHY-3002 : Step(118): len = 42921.3, overlap = 21.3125
PHY-3002 : Step(119): len = 43083.1, overlap = 21.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.061371s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.60872e-05
PHY-3002 : Step(120): len = 43123.1, overlap = 63.0312
PHY-3002 : Step(121): len = 43436.3, overlap = 63.0625
PHY-3002 : Step(122): len = 44024.1, overlap = 62.7812
PHY-3002 : Step(123): len = 44180.6, overlap = 61.2812
PHY-3002 : Step(124): len = 44506.7, overlap = 60.4688
PHY-3002 : Step(125): len = 45047.4, overlap = 54.9688
PHY-3002 : Step(126): len = 45198.2, overlap = 51.6562
PHY-3002 : Step(127): len = 45571, overlap = 50.7812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.21745e-05
PHY-3002 : Step(128): len = 45581.8, overlap = 50.8125
PHY-3002 : Step(129): len = 45800, overlap = 50.6875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000184349
PHY-3002 : Step(130): len = 46228.9, overlap = 48.9062
PHY-3002 : Step(131): len = 46679, overlap = 48.7812
PHY-3002 : Step(132): len = 47346.6, overlap = 44.9688
PHY-3002 : Step(133): len = 47271.6, overlap = 43.2188
PHY-3002 : Step(134): len = 47308, overlap = 42.1562
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000368698
PHY-3002 : Step(135): len = 47914.3, overlap = 37.5625
PHY-3002 : Step(136): len = 48365.2, overlap = 36.8125
PHY-3002 : Step(137): len = 49442.3, overlap = 32.1562
PHY-3002 : Step(138): len = 49336.2, overlap = 32
PHY-3002 : Step(139): len = 49229.6, overlap = 32.2188
PHY-3002 : Step(140): len = 49275.9, overlap = 28.5
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7780, tnet num: 2191, tinst num: 1620, tnode num: 11023, tedge num: 13163.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 82.88 peak overflow 3.09
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2193.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52072, over cnt = 242(0%), over = 1002, worst = 20
PHY-1001 : End global iterations;  0.058003s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (134.7%)

PHY-1001 : Congestion index: top1 = 40.97, top5 = 24.71, top10 = 15.90, top15 = 11.30.
PHY-1001 : End incremental global routing;  0.107728s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (130.5%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067839s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.1%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.205239s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (114.2%)

OPT-1001 : Current memory(MB): used = 216, reserve = 179, peak = 216.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1646/2193.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 52072, over cnt = 242(0%), over = 1002, worst = 20
PHY-1002 : len = 58256, over cnt = 157(0%), over = 404, worst = 18
PHY-1002 : len = 62104, over cnt = 32(0%), over = 47, worst = 8
PHY-1002 : len = 63032, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 63192, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.090950s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (137.4%)

PHY-1001 : Congestion index: top1 = 35.11, top5 = 24.73, top10 = 17.64, top15 = 12.98.
OPT-1001 : End congestion update;  0.134467s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (127.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2191 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067818s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.2%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.205017s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (114.3%)

OPT-1001 : Current memory(MB): used = 219, reserve = 183, peak = 219.
OPT-1001 : End physical optimization;  0.690610s wall, 0.734375s user + 0.031250s system = 0.765625s CPU (110.9%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 182 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 110 SEQ with LUT/SLICE
SYN-4006 : 94 single LUT's are left
SYN-4006 : 693 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1060/1388 primitive instances ...
PHY-3001 : End packing;  0.053470s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.7%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 829 instances
RUN-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2025 nets
RUN-1001 : 1473 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 827 instances, 780 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 49195.4, Over = 54.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6557, tnet num: 2023, tinst num: 827, tnode num: 8903, tedge num: 11535.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.305650s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (102.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.61043e-05
PHY-3002 : Step(141): len = 48715.8, overlap = 54
PHY-3002 : Step(142): len = 48160.3, overlap = 54.75
PHY-3002 : Step(143): len = 48169.5, overlap = 53.75
PHY-3002 : Step(144): len = 47982.5, overlap = 57.5
PHY-3002 : Step(145): len = 47907.3, overlap = 58.75
PHY-3002 : Step(146): len = 47849, overlap = 58.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.22085e-05
PHY-3002 : Step(147): len = 48197.4, overlap = 55.75
PHY-3002 : Step(148): len = 48793.1, overlap = 52.5
PHY-3002 : Step(149): len = 49355, overlap = 51.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000104417
PHY-3002 : Step(150): len = 50237.9, overlap = 48.75
PHY-3002 : Step(151): len = 51148.4, overlap = 46
PHY-3002 : Step(152): len = 51730.1, overlap = 44.5
PHY-3002 : Step(153): len = 51618.3, overlap = 44.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.072784s wall, 0.046875s user + 0.109375s system = 0.156250s CPU (214.7%)

PHY-3001 : Trial Legalized: Len = 63931.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.050571s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (92.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000555357
PHY-3002 : Step(154): len = 61472, overlap = 5.75
PHY-3002 : Step(155): len = 59464.6, overlap = 12.75
PHY-3002 : Step(156): len = 58164, overlap = 14.25
PHY-3002 : Step(157): len = 57193.8, overlap = 16
PHY-3002 : Step(158): len = 56654.7, overlap = 18.75
PHY-3002 : Step(159): len = 56272.9, overlap = 19.75
PHY-3002 : Step(160): len = 55973.8, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00111071
PHY-3002 : Step(161): len = 56304.3, overlap = 19.75
PHY-3002 : Step(162): len = 56381.6, overlap = 19.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00222143
PHY-3002 : Step(163): len = 56632.1, overlap = 20
PHY-3002 : Step(164): len = 56762.3, overlap = 20.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004826s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 61098.7, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005339s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (292.6%)

PHY-3001 : 9 instances has been re-located, deltaX = 1, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 61142.7, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6557, tnet num: 2023, tinst num: 827, tnode num: 8903, tedge num: 11535.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 96/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67512, over cnt = 147(0%), over = 227, worst = 6
PHY-1002 : len = 68632, over cnt = 62(0%), over = 72, worst = 3
PHY-1002 : len = 69504, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 69536, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 69632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146461s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (106.7%)

PHY-1001 : Congestion index: top1 = 31.21, top5 = 22.63, top10 = 17.79, top15 = 14.02.
PHY-1001 : End incremental global routing;  0.197526s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060902s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.288769s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (102.8%)

OPT-1001 : Current memory(MB): used = 221, reserve = 185, peak = 222.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1766/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006305s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.21, top5 = 22.63, top10 = 17.79, top15 = 14.02.
OPT-1001 : End congestion update;  0.051732s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060877s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 789 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 827 instances, 780 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 61159.4, Over = 0
PHY-3001 : End spreading;  0.005292s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (295.3%)

PHY-3001 : Final: Len = 61159.4, Over = 0
PHY-3001 : End incremental legalization;  0.038146s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (122.9%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.164260s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (104.6%)

OPT-1001 : Current memory(MB): used = 226, reserve = 190, peak = 226.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.050303s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (93.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1762/2025.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69632, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.007624s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.21, top5 = 22.64, top10 = 17.78, top15 = 14.02.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.057051s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (109.6%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.924809s wall, 0.921875s user + 0.000000s system = 0.921875s CPU (99.7%)

RUN-1003 : finish command "place" in  5.414231s wall, 8.328125s user + 2.734375s system = 11.062500s CPU (204.3%)

RUN-1004 : used memory is 206 MB, reserved memory is 170 MB, peak memory is 226 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 829 instances
RUN-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2025 nets
RUN-1001 : 1473 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 17 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6557, tnet num: 2023, tinst num: 827, tnode num: 8903, tedge num: 11535.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 390 mslices, 390 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 66784, over cnt = 144(0%), over = 225, worst = 7
PHY-1002 : len = 68088, over cnt = 68(0%), over = 77, worst = 3
PHY-1002 : len = 68992, over cnt = 9(0%), over = 9, worst = 1
PHY-1002 : len = 69152, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.134453s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (116.2%)

PHY-1001 : Congestion index: top1 = 30.99, top5 = 22.41, top10 = 17.61, top15 = 13.87.
PHY-1001 : End global routing;  0.182290s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (111.4%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 241, reserve = 206, peak = 249.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : Current memory(MB): used = 502, reserve = 471, peak = 502.
PHY-1001 : End build detailed router design. 3.270053s wall, 3.281250s user + 0.000000s system = 3.281250s CPU (100.3%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34072, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.205048s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (99.8%)

PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End phase 1; 1.210876s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (99.4%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177144, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End initial routed; 0.983365s wall, 1.875000s user + 0.078125s system = 1.953125s CPU (198.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.390   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.370949s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (101.1%)

PHY-1001 : Current memory(MB): used = 539, reserve = 507, peak = 539.
PHY-1001 : End phase 2; 1.354396s wall, 2.250000s user + 0.078125s system = 2.328125s CPU (171.9%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177144, over cnt = 28(0%), over = 28, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014603s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (107.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177072, over cnt = 7(0%), over = 7, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.030902s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (101.1%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177088, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020939s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (223.9%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1793(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.390   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.361134s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.175541s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (97.9%)

PHY-1001 : Current memory(MB): used = 553, reserve = 522, peak = 553.
PHY-1001 : End phase 3; 0.721041s wall, 0.734375s user + 0.000000s system = 0.734375s CPU (101.8%)

PHY-1003 : Routed, final wirelength = 177088
PHY-1001 : Current memory(MB): used = 554, reserve = 522, peak = 554.
PHY-1001 : End export database. 0.010305s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (151.6%)

PHY-1001 : End detail routing;  6.752024s wall, 7.656250s user + 0.078125s system = 7.734375s CPU (114.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6557, tnet num: 2023, tinst num: 827, tnode num: 8903, tedge num: 11535.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.668916s wall, 8.578125s user + 0.093750s system = 8.671875s CPU (113.1%)

RUN-1004 : used memory is 507 MB, reserved memory is 476 MB, peak memory is 554 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      815   out of  19600    4.16%
#reg                     1075   out of  19600    5.48%
#le                      1508
  #lut only               433   out of   1508   28.71%
  #reg only               693   out of   1508   45.95%
  #lut&reg                382   out of   1508   25.33%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         475
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q0    40
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1508   |594     |221     |1106    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1118   |299     |131     |923     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |17      |9       |17      |0       |0       |
|    demodu                  |Demodulation                                     |526    |121     |53      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |53     |0       |0       |53      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |14      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |25     |14      |0       |25      |0       |0       |
|    integ                   |Integration                                      |142    |16      |14      |116     |0       |0       |
|    modu                    |Modulation                                       |86     |27      |21      |82      |0       |1       |
|    rs422                   |Rs422Output                                      |308    |93      |29      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |30     |25      |5       |23      |0       |0       |
|  u_uart                    |UART_Control                                     |100    |88      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |37     |28      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |20     |18      |0       |16      |0       |0       |
|    U2                      |Ctrl_Data                                        |43     |42      |0       |23      |0       |0       |
|  wendu                     |DS18B20                                          |213    |168     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1437  
    #2          2       312   
    #3          3       109   
    #4          4        18   
    #5        5-10       77   
    #6        11-50      28   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6557, tnet num: 2023, tinst num: 827, tnode num: 8903, tedge num: 11535.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2023 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 827
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2025, pip num: 14597
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1308 valid insts, and 38767 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.135241s wall, 18.046875s user + 0.062500s system = 18.109375s CPU (577.6%)

RUN-1004 : used memory is 522 MB, reserved memory is 494 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230718_181018.log"
