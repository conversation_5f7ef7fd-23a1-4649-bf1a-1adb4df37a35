============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Nov 29 14:11:26 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1442 instances
RUN-0007 : 367 luts, 828 seqs, 133 mslices, 66 lslices, 34 pads, 4 brams, 4 dsps
RUN-1001 : There are total 1947 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1444 nets have 2 pins
RUN-1001 : 391 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 15 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     190     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     275     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  12   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 18
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1440 instances, 367 luts, 828 seqs, 199 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6916, tnet num: 1945, tinst num: 1440, tnode num: 9669, tedge num: 11729.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1945 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.313784s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (99.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 510013
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1440.
PHY-3001 : End clustering;  0.000023s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 463567, overlap = 15.75
PHY-3002 : Step(2): len = 429501, overlap = 18
PHY-3002 : Step(3): len = 406125, overlap = 15.75
PHY-3002 : Step(4): len = 388769, overlap = 15.75
PHY-3002 : Step(5): len = 373465, overlap = 13.5
PHY-3002 : Step(6): len = 362369, overlap = 11.25
PHY-3002 : Step(7): len = 349972, overlap = 4.5
PHY-3002 : Step(8): len = 338043, overlap = 4.5
PHY-3002 : Step(9): len = 331069, overlap = 6.75
PHY-3002 : Step(10): len = 321640, overlap = 9
PHY-3002 : Step(11): len = 315390, overlap = 11.25
PHY-3002 : Step(12): len = 309381, overlap = 11.25
PHY-3002 : Step(13): len = 304475, overlap = 13.5
PHY-3002 : Step(14): len = 297025, overlap = 15.75
PHY-3002 : Step(15): len = 293192, overlap = 15.75
PHY-3002 : Step(16): len = 286216, overlap = 15.75
PHY-3002 : Step(17): len = 281013, overlap = 15.75
PHY-3002 : Step(18): len = 274692, overlap = 15.75
PHY-3002 : Step(19): len = 271279, overlap = 15.75
PHY-3002 : Step(20): len = 262774, overlap = 13.5
PHY-3002 : Step(21): len = 255725, overlap = 15.75
PHY-3002 : Step(22): len = 251475, overlap = 15.75
PHY-3002 : Step(23): len = 247732, overlap = 18
PHY-3002 : Step(24): len = 229896, overlap = 15.75
PHY-3002 : Step(25): len = 224543, overlap = 18
PHY-3002 : Step(26): len = 221725, overlap = 18
PHY-3002 : Step(27): len = 209028, overlap = 18
PHY-3002 : Step(28): len = 194696, overlap = 15.75
PHY-3002 : Step(29): len = 192967, overlap = 18
PHY-3002 : Step(30): len = 187303, overlap = 15.75
PHY-3002 : Step(31): len = 166648, overlap = 18
PHY-3002 : Step(32): len = 159572, overlap = 18
PHY-3002 : Step(33): len = 158201, overlap = 18
PHY-3002 : Step(34): len = 147197, overlap = 18
PHY-3002 : Step(35): len = 143571, overlap = 15.75
PHY-3002 : Step(36): len = 141618, overlap = 15.75
PHY-3002 : Step(37): len = 137602, overlap = 15.75
PHY-3002 : Step(38): len = 135997, overlap = 18
PHY-3002 : Step(39): len = 133930, overlap = 18
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000106373
PHY-3002 : Step(40): len = 133539, overlap = 11.25
PHY-3002 : Step(41): len = 132895, overlap = 13.5
PHY-3002 : Step(42): len = 131244, overlap = 11.25
PHY-3002 : Step(43): len = 128323, overlap = 11.25
PHY-3002 : Step(44): len = 124343, overlap = 9
PHY-3002 : Step(45): len = 122035, overlap = 9
PHY-3002 : Step(46): len = 119627, overlap = 6.75
PHY-3002 : Step(47): len = 118292, overlap = 6.75
PHY-3002 : Step(48): len = 113559, overlap = 9
PHY-3002 : Step(49): len = 110939, overlap = 11.25
PHY-3002 : Step(50): len = 109015, overlap = 11.25
PHY-3002 : Step(51): len = 105157, overlap = 9
PHY-3002 : Step(52): len = 101864, overlap = 13.5
PHY-3002 : Step(53): len = 100802, overlap = 11.25
PHY-3002 : Step(54): len = 98567.7, overlap = 9
PHY-3002 : Step(55): len = 95902.6, overlap = 11.25
PHY-3002 : Step(56): len = 94122.8, overlap = 11.25
PHY-3002 : Step(57): len = 90741.7, overlap = 13.5625
PHY-3002 : Step(58): len = 88110, overlap = 13.875
PHY-3002 : Step(59): len = 87315.3, overlap = 13.9375
PHY-3002 : Step(60): len = 86136.6, overlap = 11.75
PHY-3002 : Step(61): len = 82755.2, overlap = 12.4375
PHY-3002 : Step(62): len = 74470, overlap = 10.1875
PHY-3002 : Step(63): len = 72839.2, overlap = 12.5625
PHY-3002 : Step(64): len = 71568.8, overlap = 14.75
PHY-3002 : Step(65): len = 70550.6, overlap = 12.5
PHY-3002 : Step(66): len = 69952.7, overlap = 14.9375
PHY-3002 : Step(67): len = 68260.4, overlap = 15
PHY-3002 : Step(68): len = 66898.6, overlap = 15.25
PHY-3002 : Step(69): len = 62286.5, overlap = 17.0625
PHY-3002 : Step(70): len = 61069.4, overlap = 17.0938
PHY-3002 : Step(71): len = 59901.9, overlap = 18.7188
PHY-3002 : Step(72): len = 59107.2, overlap = 18.625
PHY-3002 : Step(73): len = 58535.8, overlap = 18.5625
PHY-3002 : Step(74): len = 57643.6, overlap = 14.1562
PHY-3002 : Step(75): len = 55701.4, overlap = 15.0938
PHY-3002 : Step(76): len = 53826.7, overlap = 15.4062
PHY-3002 : Step(77): len = 53662.1, overlap = 15.6562
PHY-3002 : Step(78): len = 53031.3, overlap = 15.9062
PHY-3002 : Step(79): len = 52526.5, overlap = 16.2188
PHY-3002 : Step(80): len = 52360.2, overlap = 18.6562
PHY-3002 : Step(81): len = 52202.1, overlap = 18.5938
PHY-3002 : Step(82): len = 51818.7, overlap = 20.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000212747
PHY-3002 : Step(83): len = 51906.2, overlap = 18.4062
PHY-3002 : Step(84): len = 51750.2, overlap = 18.4688
PHY-3002 : Step(85): len = 51476, overlap = 18.4062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000425494
PHY-3002 : Step(86): len = 51641.8, overlap = 18.4062
PHY-3002 : Step(87): len = 51651.1, overlap = 18.4062
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007078s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1945 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.076932s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.6%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(88): len = 54900.5, overlap = 20.7812
PHY-3002 : Step(89): len = 54940.4, overlap = 20.7188
PHY-3002 : Step(90): len = 54082.1, overlap = 21.125
PHY-3002 : Step(91): len = 54102.6, overlap = 20.5312
PHY-3002 : Step(92): len = 53411.6, overlap = 21
PHY-3002 : Step(93): len = 51896.1, overlap = 20.5
PHY-3002 : Step(94): len = 51582.3, overlap = 20.4375
PHY-3002 : Step(95): len = 50628.4, overlap = 16.0312
PHY-3002 : Step(96): len = 49573.7, overlap = 13.625
PHY-3002 : Step(97): len = 47758.2, overlap = 13.625
PHY-3002 : Step(98): len = 46962.5, overlap = 16.1562
PHY-3002 : Step(99): len = 46050.3, overlap = 16.0938
PHY-3002 : Step(100): len = 45802.1, overlap = 16.0938
PHY-3002 : Step(101): len = 45109.8, overlap = 16.5938
PHY-3002 : Step(102): len = 44402.5, overlap = 16.5312
PHY-3002 : Step(103): len = 44046.5, overlap = 17.25
PHY-3002 : Step(104): len = 43970.9, overlap = 18.125
PHY-3002 : Step(105): len = 43809.8, overlap = 17.9688
PHY-3002 : Step(106): len = 43060.9, overlap = 18.125
PHY-3002 : Step(107): len = 42507, overlap = 18.7188
PHY-3002 : Step(108): len = 42315.2, overlap = 21.875
PHY-3002 : Step(109): len = 41711.8, overlap = 21.9062
PHY-3002 : Step(110): len = 41691.2, overlap = 22
PHY-3002 : Step(111): len = 41695.9, overlap = 22.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000358836
PHY-3002 : Step(112): len = 41355.5, overlap = 21.9688
PHY-3002 : Step(113): len = 41415, overlap = 21.875
PHY-3002 : Step(114): len = 41509, overlap = 21.8125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000717672
PHY-3002 : Step(115): len = 41568.8, overlap = 21.4375
PHY-3002 : Step(116): len = 42017.8, overlap = 21.25
PHY-3002 : Step(117): len = 42600.8, overlap = 21.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1945 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.064096s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (97.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.90695e-05
PHY-3002 : Step(118): len = 42626.1, overlap = 56.9062
PHY-3002 : Step(119): len = 43106.3, overlap = 56.25
PHY-3002 : Step(120): len = 44287.4, overlap = 53.5938
PHY-3002 : Step(121): len = 44524.7, overlap = 52.6562
PHY-3002 : Step(122): len = 44727.6, overlap = 52.0312
PHY-3002 : Step(123): len = 45073.4, overlap = 46.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000178139
PHY-3002 : Step(124): len = 44928.9, overlap = 46.0938
PHY-3002 : Step(125): len = 45403.3, overlap = 44.3438
PHY-3002 : Step(126): len = 45708.7, overlap = 40.9062
PHY-3002 : Step(127): len = 45801.3, overlap = 40
PHY-3002 : Step(128): len = 45918.3, overlap = 37.9375
PHY-3002 : Step(129): len = 46034.8, overlap = 35.6562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000356278
PHY-3002 : Step(130): len = 46024.9, overlap = 35.1875
PHY-3002 : Step(131): len = 46383, overlap = 34.3438
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000576458
PHY-3002 : Step(132): len = 46786, overlap = 30.0938
PHY-3002 : Step(133): len = 47182.3, overlap = 32.0625
PHY-3002 : Step(134): len = 47709.1, overlap = 29.0312
PHY-3002 : Step(135): len = 47999.1, overlap = 28.25
PHY-3002 : Step(136): len = 47825.3, overlap = 31.6562
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6916, tnet num: 1945, tinst num: 1440, tnode num: 9669, tedge num: 11729.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.25 peak overflow 3.62
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1947.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51256, over cnt = 232(0%), over = 965, worst = 22
PHY-1001 : End global iterations;  0.099591s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.1%)

PHY-1001 : Congestion index: top1 = 43.86, top5 = 24.35, top10 = 15.59, top15 = 11.04.
PHY-1001 : End incremental global routing;  0.154875s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.9%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1945 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.066777s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.251815s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (99.3%)

OPT-1001 : Current memory(MB): used = 205, reserve = 168, peak = 205.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1496/1947.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 51256, over cnt = 232(0%), over = 965, worst = 22
PHY-1002 : len = 57528, over cnt = 179(0%), over = 429, worst = 12
PHY-1002 : len = 60784, over cnt = 61(0%), over = 141, worst = 8
PHY-1002 : len = 62192, over cnt = 7(0%), over = 7, worst = 1
PHY-1002 : len = 62480, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.104264s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (149.9%)

PHY-1001 : Congestion index: top1 = 37.84, top5 = 24.57, top10 = 17.58, top15 = 12.75.
OPT-1001 : End congestion update;  0.151943s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (133.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1945 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060338s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.6%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.215443s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (123.3%)

OPT-1001 : Current memory(MB): used = 208, reserve = 171, peak = 208.
OPT-1001 : End physical optimization;  0.757390s wall, 0.796875s user + 0.000000s system = 0.796875s CPU (105.2%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 367 LUT to BLE ...
SYN-4008 : Packed 367 LUT and 172 SEQ to BLE.
SYN-4003 : Packing 656 remaining SEQ's ...
SYN-4005 : Packed 108 SEQ with LUT/SLICE
SYN-4006 : 102 single LUT's are left
SYN-4006 : 548 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 915/1196 primitive instances ...
PHY-3001 : End packing;  0.049403s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (94.9%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 727 instances
RUN-1001 : 339 mslices, 340 lslices, 34 pads, 4 brams, 4 dsps
RUN-1001 : There are total 1783 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1287 nets have 2 pins
RUN-1001 : 384 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 725 instances, 679 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 47936.6, Over = 48.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5825, tnet num: 1781, tinst num: 725, tnode num: 7823, tedge num: 10265.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1781 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.322771s wall, 0.328125s user + 0.000000s system = 0.328125s CPU (101.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 3.38666e-05
PHY-3002 : Step(137): len = 47218.4, overlap = 50.5
PHY-3002 : Step(138): len = 46781.6, overlap = 50.25
PHY-3002 : Step(139): len = 46270.3, overlap = 51.25
PHY-3002 : Step(140): len = 45982.6, overlap = 51.75
PHY-3002 : Step(141): len = 45909.2, overlap = 52
PHY-3002 : Step(142): len = 45626.4, overlap = 52.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.77333e-05
PHY-3002 : Step(143): len = 45913, overlap = 52.5
PHY-3002 : Step(144): len = 46282.3, overlap = 48.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000128588
PHY-3002 : Step(145): len = 46940.5, overlap = 47.25
PHY-3002 : Step(146): len = 47021.7, overlap = 46.75
PHY-3002 : Step(147): len = 47831.3, overlap = 41.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.083152s wall, 0.078125s user + 0.156250s system = 0.234375s CPU (281.9%)

PHY-3001 : Trial Legalized: Len = 60113.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1781 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.052137s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (89.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000668876
PHY-3002 : Step(148): len = 57009.2, overlap = 4.5
PHY-3002 : Step(149): len = 54473.4, overlap = 11.5
PHY-3002 : Step(150): len = 53175.1, overlap = 13.75
PHY-3002 : Step(151): len = 52069.5, overlap = 17
PHY-3002 : Step(152): len = 51589.2, overlap = 17.75
PHY-3002 : Step(153): len = 51427, overlap = 17.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00133775
PHY-3002 : Step(154): len = 51539.3, overlap = 18
PHY-3002 : Step(155): len = 51621.5, overlap = 18.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00267551
PHY-3002 : Step(156): len = 51760, overlap = 17.75
PHY-3002 : Step(157): len = 51881.8, overlap = 17.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006104s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (256.0%)

PHY-3001 : Legalized: Len = 55955.1, Over = 0
PHY-3001 : Spreading special nets. 10 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006746s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 17 instances has been re-located, deltaX = 5, deltaY = 12, maxDist = 1.
PHY-3001 : Final: Len = 56313.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5825, tnet num: 1781, tinst num: 725, tnode num: 7823, tedge num: 10265.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 50/1783.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 63248, over cnt = 161(0%), over = 241, worst = 6
PHY-1002 : len = 64160, over cnt = 101(0%), over = 126, worst = 5
PHY-1002 : len = 65136, over cnt = 41(0%), over = 53, worst = 5
PHY-1002 : len = 65496, over cnt = 20(0%), over = 27, worst = 2
PHY-1002 : len = 65808, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.163321s wall, 0.203125s user + 0.031250s system = 0.234375s CPU (143.5%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 23.55, top10 = 17.90, top15 = 13.59.
PHY-1001 : End incremental global routing;  0.220403s wall, 0.265625s user + 0.031250s system = 0.296875s CPU (134.7%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1781 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069004s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.320868s wall, 0.359375s user + 0.031250s system = 0.390625s CPU (121.7%)

OPT-1001 : Current memory(MB): used = 211, reserve = 175, peak = 211.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1589/1783.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65808, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006126s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (255.0%)

PHY-1001 : Congestion index: top1 = 31.79, top5 = 23.55, top10 = 17.90, top15 = 13.59.
OPT-1001 : End congestion update;  0.060914s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (102.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1781 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055373s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (112.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 687 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 725 instances, 679 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 56360.6, Over = 0
PHY-3001 : End spreading;  0.005328s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 56360.6, Over = 0
PHY-3001 : End incremental legalization;  0.037086s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (84.3%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 2 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.168702s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (111.1%)

OPT-1001 : Current memory(MB): used = 216, reserve = 180, peak = 216.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1781 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053919s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (29.0%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1581/1783.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 65840, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 65856, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 65872, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.027054s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (231.0%)

PHY-1001 : Congestion index: top1 = 31.64, top5 = 23.51, top10 = 17.90, top15 = 13.59.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1781 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.048971s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (95.7%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 31.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.987318s wall, 1.125000s user + 0.046875s system = 1.171875s CPU (118.7%)

RUN-1003 : finish command "place" in  6.167887s wall, 9.062500s user + 3.531250s system = 12.593750s CPU (204.2%)

RUN-1004 : used memory is 194 MB, reserved memory is 158 MB, peak memory is 216 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 727 instances
RUN-1001 : 339 mslices, 340 lslices, 34 pads, 4 brams, 4 dsps
RUN-1001 : There are total 1783 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1287 nets have 2 pins
RUN-1001 : 384 nets have [3 - 5] pins
RUN-1001 : 73 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5825, tnet num: 1781, tinst num: 725, tnode num: 7823, tedge num: 10265.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 339 mslices, 340 lslices, 34 pads, 4 brams, 4 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1781 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 62680, over cnt = 159(0%), over = 236, worst = 6
PHY-1002 : len = 63680, over cnt = 92(0%), over = 121, worst = 5
PHY-1002 : len = 64752, over cnt = 33(0%), over = 45, worst = 5
PHY-1002 : len = 65352, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.148482s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (115.8%)

PHY-1001 : Congestion index: top1 = 31.62, top5 = 23.41, top10 = 17.75, top15 = 13.49.
PHY-1001 : End global routing;  0.200540s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (109.1%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 226, reserve = 191, peak = 229.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 488, reserve = 456, peak = 488.
PHY-1001 : End build detailed router design. 3.832529s wall, 3.781250s user + 0.046875s system = 3.828125s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 28784, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.523713s wall, 1.500000s user + 0.031250s system = 1.531250s CPU (100.5%)

PHY-1001 : Current memory(MB): used = 521, reserve = 490, peak = 521.
PHY-1001 : End phase 1; 1.529490s wall, 1.515625s user + 0.031250s system = 1.546875s CPU (101.1%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 87% nets.
PHY-1022 : len = 171368, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 523, reserve = 491, peak = 523.
PHY-1001 : End initial routed; 1.344398s wall, 1.921875s user + 0.031250s system = 1.953125s CPU (145.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1568(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.439   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.643   |   8   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.390699s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (100.0%)

PHY-1001 : Current memory(MB): used = 526, reserve = 494, peak = 526.
PHY-1001 : End phase 2; 1.735198s wall, 2.312500s user + 0.031250s system = 2.343750s CPU (135.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 171368, over cnt = 24(0%), over = 24, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.014739s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (106.0%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 171336, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.026672s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (117.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 171408, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.021763s wall, 0.015625s user + 0.015625s system = 0.031250s CPU (143.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1568(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.439   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.643   |   8   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.422498s wall, 0.421875s user + 0.000000s system = 0.421875s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 4 feed throughs used by 4 nets
PHY-1001 : End commit to database; 0.181555s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.7%)

PHY-1001 : Current memory(MB): used = 540, reserve = 508, peak = 540.
PHY-1001 : End phase 3; 0.804036s wall, 0.781250s user + 0.015625s system = 0.796875s CPU (99.1%)

PHY-1003 : Routed, final wirelength = 171408
PHY-1001 : Current memory(MB): used = 540, reserve = 508, peak = 540.
PHY-1001 : End export database. 0.009840s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (158.8%)

PHY-1001 : End detail routing;  8.120584s wall, 8.625000s user + 0.125000s system = 8.750000s CPU (107.8%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5825, tnet num: 1781, tinst num: 725, tnode num: 7823, tedge num: 10265.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[4] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[4] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[7] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[7] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[8] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[8] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[10] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[1] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[14] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[15] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[6] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[16] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[7] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[17] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[8] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/wr_addr[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.addra[6] slack -47ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[32] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_30.dib[5] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[36] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[43] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[7] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg2_syn_22.sr slack -65ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/fifo/asy_w_rst1 endpin signal_process/demodu/fifo/rd_to_wr_cross_inst/reg3_syn_15.sr slack -65ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5875, tnet num: 1806, tinst num: 750, tnode num: 7873, tedge num: 10315.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.456349s wall, 3.390625s user + 0.140625s system = 3.531250s CPU (102.2%)

RUN-1003 : finish command "route" in  12.178502s wall, 12.625000s user + 0.265625s system = 12.890625s CPU (105.8%)

RUN-1004 : used memory is 519 MB, reserved memory is 488 MB, peak memory is 540 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      815   out of  19600    4.16%
#reg                      902   out of  19600    4.60%
#le                      1363
  #lut only               461   out of   1363   33.82%
  #reg only               548   out of   1363   40.21%
  #lut&reg                354   out of   1363   25.97%
#dsp                        4   out of     29   13.79%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         374
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q0    45
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       NONE    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       NONE    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       NONE    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       NONE    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       NONE    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       NONE    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       NONE    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       NONE    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       NONE    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       NONE    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       NONE    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       NONE    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       NONE    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       NONE    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1363   |616     |199     |919     |4       |4       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |978    |318     |114     |750     |4       |4       |
|    ctrl_signal             |SignalGenerator                                  |25     |19      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |483    |150     |44      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |62     |34      |6       |47      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |5       |0       |15      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |8       |0       |16      |0       |0       |
|    integ                   |Integration                                      |137    |29      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |2      |1       |0       |2       |0       |0       |
|    rs422                   |Rs422Output                                      |304    |97      |45      |251     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |22      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |110    |101     |7       |62      |0       |0       |
|    U0                      |speed_select_Tx                                  |31     |22      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |21     |21      |0       |17      |0       |0       |
|    U2                      |Ctrl_Data                                        |58     |58      |0       |29      |0       |0       |
|  wendu                     |DS18B20                                          |208    |163     |45      |67      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1276  
    #2          2       252   
    #3          3       120   
    #4          4        12   
    #5        5-10       77   
    #6        11-50      27   
    #7       101-500     1    
  Average     1.98            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5875, tnet num: 1806, tinst num: 750, tnode num: 7873, tedge num: 10315.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1806 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 750
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1808, pip num: 13599
BIT-1002 : Init feedthrough completely, num: 4
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1204 valid insts, and 36313 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.414957s wall, 18.625000s user + 0.062500s system = 18.687500s CPU (547.2%)

RUN-1004 : used memory is 515 MB, reserved memory is 486 MB, peak memory is 664 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231129_141126.log"
