============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Tue Nov  7 14:02:15 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1493 instances
RUN-0007 : 378 luts, 867 seqs, 133 mslices, 66 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2011 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1505 nets have 2 pins
RUN-1001 : 392 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 17 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     215     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     289     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  13   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 19
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1491 instances, 378 luts, 867 seqs, 199 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7120, tnet num: 2009, tinst num: 1491, tnode num: 9996, tedge num: 12022.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2009 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.263981s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (100.6%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 557239
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1491.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 505195, overlap = 20.25
PHY-3002 : Step(2): len = 471666, overlap = 20.25
PHY-3002 : Step(3): len = 448115, overlap = 13.5
PHY-3002 : Step(4): len = 429733, overlap = 15.75
PHY-3002 : Step(5): len = 414131, overlap = 6.75
PHY-3002 : Step(6): len = 405033, overlap = 13.5
PHY-3002 : Step(7): len = 385710, overlap = 11.25
PHY-3002 : Step(8): len = 376058, overlap = 11.25
PHY-3002 : Step(9): len = 364751, overlap = 13.5
PHY-3002 : Step(10): len = 353829, overlap = 11.25
PHY-3002 : Step(11): len = 344416, overlap = 11.25
PHY-3002 : Step(12): len = 339638, overlap = 11.25
PHY-3002 : Step(13): len = 327896, overlap = 11.25
PHY-3002 : Step(14): len = 321039, overlap = 15.75
PHY-3002 : Step(15): len = 315751, overlap = 15.75
PHY-3002 : Step(16): len = 307331, overlap = 18
PHY-3002 : Step(17): len = 297556, overlap = 18
PHY-3002 : Step(18): len = 294555, overlap = 18
PHY-3002 : Step(19): len = 285873, overlap = 20.25
PHY-3002 : Step(20): len = 272688, overlap = 20.25
PHY-3002 : Step(21): len = 266854, overlap = 20.25
PHY-3002 : Step(22): len = 263712, overlap = 20.25
PHY-3002 : Step(23): len = 242753, overlap = 18
PHY-3002 : Step(24): len = 236174, overlap = 20.25
PHY-3002 : Step(25): len = 233512, overlap = 20.25
PHY-3002 : Step(26): len = 225288, overlap = 20.25
PHY-3002 : Step(27): len = 203989, overlap = 20.25
PHY-3002 : Step(28): len = 200985, overlap = 20.25
PHY-3002 : Step(29): len = 196766, overlap = 20.25
PHY-3002 : Step(30): len = 183597, overlap = 20.25
PHY-3002 : Step(31): len = 177866, overlap = 20.25
PHY-3002 : Step(32): len = 175864, overlap = 20.25
PHY-3002 : Step(33): len = 171779, overlap = 20.25
PHY-3002 : Step(34): len = 168234, overlap = 20.25
PHY-3002 : Step(35): len = 163422, overlap = 20.25
PHY-3002 : Step(36): len = 160454, overlap = 20.25
PHY-3002 : Step(37): len = 155290, overlap = 20.25
PHY-3002 : Step(38): len = 151218, overlap = 20.25
PHY-3002 : Step(39): len = 145400, overlap = 20.25
PHY-3002 : Step(40): len = 142087, overlap = 20.25
PHY-3002 : Step(41): len = 138732, overlap = 20.25
PHY-3002 : Step(42): len = 134852, overlap = 20.25
PHY-3002 : Step(43): len = 129537, overlap = 20.25
PHY-3002 : Step(44): len = 126389, overlap = 20.25
PHY-3002 : Step(45): len = 123175, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 6.58961e-05
PHY-3002 : Step(46): len = 123517, overlap = 18
PHY-3002 : Step(47): len = 123573, overlap = 13.5
PHY-3002 : Step(48): len = 121977, overlap = 15.75
PHY-3002 : Step(49): len = 120261, overlap = 20.25
PHY-3002 : Step(50): len = 115892, overlap = 13.5
PHY-3002 : Step(51): len = 109168, overlap = 11.25
PHY-3002 : Step(52): len = 106893, overlap = 15.75
PHY-3002 : Step(53): len = 104615, overlap = 15.75
PHY-3002 : Step(54): len = 101565, overlap = 11.25
PHY-3002 : Step(55): len = 98214.7, overlap = 13.5
PHY-3002 : Step(56): len = 97484, overlap = 11.25
PHY-3002 : Step(57): len = 92887.1, overlap = 15.875
PHY-3002 : Step(58): len = 91480.8, overlap = 18.1875
PHY-3002 : Step(59): len = 90530.3, overlap = 18.4375
PHY-3002 : Step(60): len = 88634.4, overlap = 12.125
PHY-3002 : Step(61): len = 85188.9, overlap = 12.4375
PHY-3002 : Step(62): len = 83674.3, overlap = 12.6875
PHY-3002 : Step(63): len = 81663.6, overlap = 12.9375
PHY-3002 : Step(64): len = 79407.2, overlap = 11.375
PHY-3002 : Step(65): len = 76112.1, overlap = 16.3438
PHY-3002 : Step(66): len = 74929.8, overlap = 16.4375
PHY-3002 : Step(67): len = 73884.7, overlap = 17
PHY-3002 : Step(68): len = 71713.3, overlap = 17.25
PHY-3002 : Step(69): len = 70229.9, overlap = 17.5625
PHY-3002 : Step(70): len = 68315.2, overlap = 15.5312
PHY-3002 : Step(71): len = 67444.8, overlap = 15.875
PHY-3002 : Step(72): len = 65364, overlap = 15.9375
PHY-3002 : Step(73): len = 64370.6, overlap = 16.0625
PHY-3002 : Step(74): len = 63497.4, overlap = 16.125
PHY-3002 : Step(75): len = 62031.5, overlap = 18.3125
PHY-3002 : Step(76): len = 60458, overlap = 18.0625
PHY-3002 : Step(77): len = 60431.8, overlap = 18
PHY-3002 : Step(78): len = 59837.2, overlap = 17.875
PHY-3002 : Step(79): len = 58868.3, overlap = 15.5312
PHY-3002 : Step(80): len = 58686.8, overlap = 17.7812
PHY-3002 : Step(81): len = 58507.4, overlap = 17.9062
PHY-3002 : Step(82): len = 57692.2, overlap = 17.8125
PHY-3002 : Step(83): len = 56714.5, overlap = 20.0938
PHY-3002 : Step(84): len = 56872.3, overlap = 17.9062
PHY-3002 : Step(85): len = 56686.3, overlap = 17.7812
PHY-3002 : Step(86): len = 56026.9, overlap = 15.1562
PHY-3002 : Step(87): len = 55532.8, overlap = 17.4062
PHY-3002 : Step(88): len = 55391.8, overlap = 17.4062
PHY-3002 : Step(89): len = 55148.4, overlap = 17.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000131792
PHY-3002 : Step(90): len = 55544.7, overlap = 15.0312
PHY-3002 : Step(91): len = 55691.8, overlap = 15.0312
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000263585
PHY-3002 : Step(92): len = 55823.7, overlap = 15.0312
PHY-3002 : Step(93): len = 55972.5, overlap = 17.2812
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005798s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2009 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054831s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(94): len = 59867.2, overlap = 16.3438
PHY-3002 : Step(95): len = 59865.9, overlap = 16.125
PHY-3002 : Step(96): len = 59153.7, overlap = 15.5
PHY-3002 : Step(97): len = 59128.6, overlap = 15.3125
PHY-3002 : Step(98): len = 59177.2, overlap = 14.5312
PHY-3002 : Step(99): len = 57039.9, overlap = 15.2188
PHY-3002 : Step(100): len = 56206.3, overlap = 15.1875
PHY-3002 : Step(101): len = 54500.2, overlap = 12.9062
PHY-3002 : Step(102): len = 53496.4, overlap = 12.1562
PHY-3002 : Step(103): len = 52718.8, overlap = 11.7188
PHY-3002 : Step(104): len = 52170.7, overlap = 11.6562
PHY-3002 : Step(105): len = 51213.6, overlap = 12.6562
PHY-3002 : Step(106): len = 50565.5, overlap = 17.3438
PHY-3002 : Step(107): len = 49984.1, overlap = 17.5938
PHY-3002 : Step(108): len = 49704.9, overlap = 17.5938
PHY-3002 : Step(109): len = 48832, overlap = 16.7188
PHY-3002 : Step(110): len = 48760.9, overlap = 16.6562
PHY-3002 : Step(111): len = 48425.3, overlap = 16.6562
PHY-3002 : Step(112): len = 48325, overlap = 16.7812
PHY-3002 : Step(113): len = 47965.8, overlap = 17.1562
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00125509
PHY-3002 : Step(114): len = 47903.3, overlap = 17.2812
PHY-3002 : Step(115): len = 47915.8, overlap = 16.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00251018
PHY-3002 : Step(116): len = 47743.5, overlap = 16.0938
PHY-3002 : Step(117): len = 47696, overlap = 15.9688
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2009 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.066969s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 8.83517e-05
PHY-3002 : Step(118): len = 47980.6, overlap = 55.1875
PHY-3002 : Step(119): len = 50118.6, overlap = 50.625
PHY-3002 : Step(120): len = 50483.7, overlap = 46.2812
PHY-3002 : Step(121): len = 50063.4, overlap = 40.7188
PHY-3002 : Step(122): len = 50052.4, overlap = 40.9062
PHY-3002 : Step(123): len = 49867.3, overlap = 41.125
PHY-3002 : Step(124): len = 49792.8, overlap = 40.375
PHY-3002 : Step(125): len = 49943.2, overlap = 38.2812
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000176703
PHY-3002 : Step(126): len = 49954.7, overlap = 37.4688
PHY-3002 : Step(127): len = 50400.7, overlap = 37.3125
PHY-3002 : Step(128): len = 50536, overlap = 37.2812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000353407
PHY-3002 : Step(129): len = 51153.5, overlap = 34.625
PHY-3002 : Step(130): len = 52053.9, overlap = 34.2812
PHY-3002 : Step(131): len = 52953.4, overlap = 32.9062
PHY-3002 : Step(132): len = 52555.6, overlap = 32.4375
PHY-3002 : Step(133): len = 52439, overlap = 32.5
PHY-3002 : Step(134): len = 52365, overlap = 32.2188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000706814
PHY-3002 : Step(135): len = 52509.8, overlap = 31.375
PHY-3002 : Step(136): len = 52693.4, overlap = 30.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7120, tnet num: 2009, tinst num: 1491, tnode num: 9996, tedge num: 12022.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 78.66 peak overflow 2.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2011.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55080, over cnt = 235(0%), over = 942, worst = 15
PHY-1001 : End global iterations;  0.077095s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (121.6%)

PHY-1001 : Congestion index: top1 = 41.38, top5 = 25.19, top10 = 15.91, top15 = 11.21.
PHY-1001 : End incremental global routing;  0.125424s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (112.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2009 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.062262s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (100.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.216260s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.4%)

OPT-1001 : Current memory(MB): used = 208, reserve = 172, peak = 208.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1517/2011.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 55080, over cnt = 235(0%), over = 942, worst = 15
PHY-1002 : len = 59176, over cnt = 170(0%), over = 481, worst = 15
PHY-1002 : len = 61440, over cnt = 104(0%), over = 297, worst = 10
PHY-1002 : len = 64712, over cnt = 28(0%), over = 58, worst = 9
PHY-1002 : len = 65424, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.091526s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (102.4%)

PHY-1001 : Congestion index: top1 = 35.37, top5 = 24.97, top10 = 17.69, top15 = 12.79.
OPT-1001 : End congestion update;  0.133968s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (105.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2009 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054658s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.8%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.191678s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (106.0%)

OPT-1001 : Current memory(MB): used = 211, reserve = 175, peak = 211.
OPT-1001 : End physical optimization;  0.676163s wall, 0.703125s user + 0.000000s system = 0.703125s CPU (104.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 378 LUT to BLE ...
SYN-4008 : Packed 378 LUT and 177 SEQ to BLE.
SYN-4003 : Packing 690 remaining SEQ's ...
SYN-4005 : Packed 105 SEQ with LUT/SLICE
SYN-4006 : 112 single LUT's are left
SYN-4006 : 585 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 963/1245 primitive instances ...
PHY-3001 : End packing;  0.043712s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (71.5%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 759 instances
RUN-1001 : 355 mslices, 355 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1842 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1343 nets have 2 pins
RUN-1001 : 385 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 757 instances, 710 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 52415.4, Over = 55.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5978, tnet num: 1840, tinst num: 757, tnode num: 8058, tedge num: 10486.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.285509s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (98.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.73582e-05
PHY-3002 : Step(137): len = 51713.9, overlap = 57.75
PHY-3002 : Step(138): len = 50975.5, overlap = 59
PHY-3002 : Step(139): len = 50190.4, overlap = 59.5
PHY-3002 : Step(140): len = 49854.7, overlap = 60.5
PHY-3002 : Step(141): len = 49799.1, overlap = 60.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.47163e-05
PHY-3002 : Step(142): len = 50012.9, overlap = 61
PHY-3002 : Step(143): len = 50419.5, overlap = 58.75
PHY-3002 : Step(144): len = 51360.5, overlap = 58.25
PHY-3002 : Step(145): len = 51473.9, overlap = 55
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000109433
PHY-3002 : Step(146): len = 52283.1, overlap = 53.25
PHY-3002 : Step(147): len = 52723.1, overlap = 49.75
PHY-3002 : Step(148): len = 53139.6, overlap = 48.25
PHY-3002 : Step(149): len = 53219, overlap = 46.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.087342s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (250.5%)

PHY-3001 : Trial Legalized: Len = 66643.3
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.045273s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (103.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000595009
PHY-3002 : Step(150): len = 62811.6, overlap = 6.25
PHY-3002 : Step(151): len = 60604, overlap = 10.25
PHY-3002 : Step(152): len = 58966.9, overlap = 14.25
PHY-3002 : Step(153): len = 57579.6, overlap = 17.75
PHY-3002 : Step(154): len = 56944, overlap = 18.5
PHY-3002 : Step(155): len = 56584.1, overlap = 20
PHY-3002 : Step(156): len = 56321.5, overlap = 21.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00119002
PHY-3002 : Step(157): len = 56466.5, overlap = 22
PHY-3002 : Step(158): len = 56561, overlap = 21.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00238004
PHY-3002 : Step(159): len = 56649.8, overlap = 21.5
PHY-3002 : Step(160): len = 56733.9, overlap = 20.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005308s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 61483.9, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.005147s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (303.6%)

PHY-3001 : 8 instances has been re-located, deltaX = 1, deltaY = 7, maxDist = 1.
PHY-3001 : Final: Len = 61501.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5978, tnet num: 1840, tinst num: 757, tnode num: 8058, tedge num: 10486.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 54/1842.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68376, over cnt = 132(0%), over = 200, worst = 6
PHY-1002 : len = 69272, over cnt = 57(0%), over = 68, worst = 3
PHY-1002 : len = 70088, over cnt = 5(0%), over = 7, worst = 3
PHY-1002 : len = 70128, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 70176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.141743s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (99.2%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 23.02, top10 = 17.82, top15 = 13.63.
PHY-1001 : End incremental global routing;  0.191314s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (98.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.054092s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (115.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.273522s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (97.1%)

OPT-1001 : Current memory(MB): used = 215, reserve = 180, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1610/1842.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006305s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (247.8%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 23.02, top10 = 17.82, top15 = 13.63.
OPT-1001 : End congestion update;  0.051223s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (122.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.044664s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.9%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 719 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 757 instances, 710 slices, 21 macros(199 instances: 133 mslices 66 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 61509.4, Over = 0
PHY-3001 : End spreading;  0.004582s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (341.0%)

PHY-3001 : Final: Len = 61509.4, Over = 0
PHY-3001 : End incremental legalization;  0.032627s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.8%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.155692s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (90.3%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043344s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (108.1%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1606/1842.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70176, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70176, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 70176, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.032703s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (95.6%)

PHY-1001 : Congestion index: top1 = 31.14, top5 = 23.02, top10 = 17.81, top15 = 13.62.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.045004s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (104.2%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.689655
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  0.890100s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (98.3%)

RUN-1003 : finish command "place" in  5.259376s wall, 8.031250s user + 2.796875s system = 10.828125s CPU (205.9%)

RUN-1004 : used memory is 206 MB, reserved memory is 170 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 759 instances
RUN-1001 : 355 mslices, 355 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1842 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1343 nets have 2 pins
RUN-1001 : 385 nets have [3 - 5] pins
RUN-1001 : 74 nets have [6 - 10] pins
RUN-1001 : 19 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5978, tnet num: 1840, tinst num: 757, tnode num: 8058, tedge num: 10486.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 355 mslices, 355 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1840 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 67576, over cnt = 140(0%), over = 215, worst = 6
PHY-1002 : len = 68616, over cnt = 56(0%), over = 68, worst = 3
PHY-1002 : len = 69216, over cnt = 18(0%), over = 20, worst = 3
PHY-1002 : len = 69504, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.136607s wall, 0.140625s user + 0.031250s system = 0.171875s CPU (125.8%)

PHY-1001 : Congestion index: top1 = 30.82, top5 = 22.91, top10 = 17.67, top15 = 13.51.
PHY-1001 : End global routing;  0.184413s wall, 0.187500s user + 0.031250s system = 0.218750s CPU (118.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 199, peak = 239.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 495, reserve = 464, peak = 495.
PHY-1001 : End build detailed router design. 3.226109s wall, 3.203125s user + 0.015625s system = 3.218750s CPU (99.8%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32960, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.268683s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 528, reserve = 497, peak = 528.
PHY-1001 : End phase 1; 1.275055s wall, 1.265625s user + 0.015625s system = 1.281250s CPU (100.5%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 37% nets.
PHY-1001 : Routed 46% nets.
PHY-1001 : Routed 63% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 180144, over cnt = 16(0%), over = 16, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 530.
PHY-1001 : End initial routed; 1.248279s wall, 2.453125s user + 0.156250s system = 2.609375s CPU (209.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1627(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.488   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.365   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.350656s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.0%)

PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 530.
PHY-1001 : End phase 2; 1.599028s wall, 2.796875s user + 0.156250s system = 2.953125s CPU (184.7%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 180144, over cnt = 16(0%), over = 16, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.013845s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (112.9%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 180184, over cnt = 8(0%), over = 8, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.021439s wall, 0.046875s user + 0.015625s system = 0.062500s CPU (291.5%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 180184, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.024040s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (65.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1627(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.380   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.365   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.337563s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (101.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.168473s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.0%)

PHY-1001 : Current memory(MB): used = 543, reserve = 511, peak = 543.
PHY-1001 : End phase 3; 0.693519s wall, 0.734375s user + 0.015625s system = 0.750000s CPU (108.1%)

PHY-1003 : Routed, final wirelength = 180184
PHY-1001 : Current memory(MB): used = 543, reserve = 511, peak = 543.
PHY-1001 : End export database. 0.010935s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (142.9%)

PHY-1001 : End detail routing;  6.974133s wall, 8.187500s user + 0.203125s system = 8.390625s CPU (120.3%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 5978, tnet num: 1840, tinst num: 757, tnode num: 8058, tedge num: 10486.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[0] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[1] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[1] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[2] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[5] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[5] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[6] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[6] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[13] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[4] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[39] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[3] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[44] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[8] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[45] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[0] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[51] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[6] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[52] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[7] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[53] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[8] slack -86ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6012, tnet num: 1857, tinst num: 774, tnode num: 8092, tedge num: 10520.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  2.900817s wall, 2.859375s user + 0.078125s system = 2.937500s CPU (101.3%)

RUN-1003 : finish command "route" in  10.385724s wall, 11.562500s user + 0.312500s system = 11.875000s CPU (114.3%)

RUN-1004 : used memory is 521 MB, reserved memory is 489 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      810   out of  19600    4.13%
#reg                      941   out of  19600    4.80%
#le                      1395
  #lut only               454   out of   1395   32.54%
  #reg only               585   out of   1395   41.94%
  #lut&reg                356   out of   1395   25.52%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         409
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         103
#3        wendu/clk_us                    GCLK               mslice             wendu/clk_us_reg_syn_12.q1    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1395   |611     |199     |972     |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |996    |300     |114     |788     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |26     |20      |6       |20      |0       |0       |
|    demodu                  |Demodulation                                     |463    |128     |44      |345     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |52     |29      |6       |43      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |13     |6       |0       |13      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |19     |9       |0       |19      |0       |0       |
|    integ                   |Integration                                      |139    |31      |14      |113     |0       |0       |
|    modu                    |Modulation                                       |38     |7       |0       |37      |0       |1       |
|    rs422                   |Rs422Output                                      |305    |94      |45      |252     |0       |4       |
|    trans                   |SquareWaveGenerator                              |25     |20      |5       |21      |0       |0       |
|  u_uart                    |UART_Control                                     |125    |115     |7       |59      |0       |0       |
|    U0                      |speed_select_Tx                                  |30     |23      |7       |17      |0       |0       |
|    U1                      |uart_tx                                          |24     |21      |0       |20      |0       |0       |
|    U2                      |Ctrl_Data                                        |71     |71      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |207    |162     |45      |71      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1324  
    #2          2       252   
    #3          3       117   
    #4          4        16   
    #5        5-10       79   
    #6        11-50      27   
    #7       101-500     1    
  Average     1.96            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6012, tnet num: 1857, tinst num: 774, tnode num: 8092, tedge num: 10520.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1857 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 774
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1859, pip num: 13974
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1276 valid insts, and 37030 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.949511s wall, 17.046875s user + 0.031250s system = 17.078125s CPU (579.0%)

RUN-1004 : used memory is 517 MB, reserved memory is 485 MB, peak memory is 664 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231107_140215.log"
