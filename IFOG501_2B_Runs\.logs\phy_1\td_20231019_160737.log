============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Thu Oct 19 16:07:37 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1509 instances
RUN-0007 : 352 luts, 901 seqs, 132 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2063 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1517 nets have 2 pins
RUN-1001 : 439 nets have [3 - 5] pins
RUN-1001 : 65 nets have [6 - 10] pins
RUN-1001 : 18 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     124     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1507 instances, 352 luts, 901 seqs, 207 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7291, tnet num: 2061, tinst num: 1507, tnode num: 10277, tedge num: 12402.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.311633s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (100.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 545818
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1507.
PHY-3001 : End clustering;  0.000024s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 461858, overlap = 18
PHY-3002 : Step(2): len = 433063, overlap = 18
PHY-3002 : Step(3): len = 416990, overlap = 18
PHY-3002 : Step(4): len = 373036, overlap = 20.25
PHY-3002 : Step(5): len = 342381, overlap = 18
PHY-3002 : Step(6): len = 321941, overlap = 11.25
PHY-3002 : Step(7): len = 313885, overlap = 13.5
PHY-3002 : Step(8): len = 307313, overlap = 15.75
PHY-3002 : Step(9): len = 300887, overlap = 15.75
PHY-3002 : Step(10): len = 294626, overlap = 13.5
PHY-3002 : Step(11): len = 288930, overlap = 15.75
PHY-3002 : Step(12): len = 283860, overlap = 15.75
PHY-3002 : Step(13): len = 278481, overlap = 15.75
PHY-3002 : Step(14): len = 270279, overlap = 13.5
PHY-3002 : Step(15): len = 265062, overlap = 15.75
PHY-3002 : Step(16): len = 260488, overlap = 15.75
PHY-3002 : Step(17): len = 252465, overlap = 18
PHY-3002 : Step(18): len = 246330, overlap = 18
PHY-3002 : Step(19): len = 243181, overlap = 18
PHY-3002 : Step(20): len = 235924, overlap = 15.75
PHY-3002 : Step(21): len = 227769, overlap = 18
PHY-3002 : Step(22): len = 223885, overlap = 18
PHY-3002 : Step(23): len = 220212, overlap = 18
PHY-3002 : Step(24): len = 200639, overlap = 18
PHY-3002 : Step(25): len = 191972, overlap = 20.25
PHY-3002 : Step(26): len = 190137, overlap = 20.25
PHY-3002 : Step(27): len = 178586, overlap = 18
PHY-3002 : Step(28): len = 134364, overlap = 18
PHY-3002 : Step(29): len = 131125, overlap = 20.25
PHY-3002 : Step(30): len = 129443, overlap = 20.25
PHY-3002 : Step(31): len = 122607, overlap = 20.25
PHY-3002 : Step(32): len = 119332, overlap = 20.25
PHY-3002 : Step(33): len = 116847, overlap = 20.25
PHY-3002 : Step(34): len = 114359, overlap = 20.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 9.0792e-05
PHY-3002 : Step(35): len = 114880, overlap = 13.5
PHY-3002 : Step(36): len = 114050, overlap = 15.75
PHY-3002 : Step(37): len = 112827, overlap = 15.75
PHY-3002 : Step(38): len = 112430, overlap = 15.75
PHY-3002 : Step(39): len = 110375, overlap = 13.5
PHY-3002 : Step(40): len = 107470, overlap = 11.25
PHY-3002 : Step(41): len = 106153, overlap = 11.25
PHY-3002 : Step(42): len = 103732, overlap = 13.5
PHY-3002 : Step(43): len = 102561, overlap = 15.75
PHY-3002 : Step(44): len = 99005.8, overlap = 13.5
PHY-3002 : Step(45): len = 98016.9, overlap = 13.5
PHY-3002 : Step(46): len = 96419.1, overlap = 11.25
PHY-3002 : Step(47): len = 92741.2, overlap = 11.25
PHY-3002 : Step(48): len = 89226.6, overlap = 11.25
PHY-3002 : Step(49): len = 88880.3, overlap = 13.5
PHY-3002 : Step(50): len = 85896, overlap = 15.75
PHY-3002 : Step(51): len = 83867.1, overlap = 15.75
PHY-3002 : Step(52): len = 82220.2, overlap = 15.75
PHY-3002 : Step(53): len = 80827.2, overlap = 13.5
PHY-3002 : Step(54): len = 79626.4, overlap = 11.25
PHY-3002 : Step(55): len = 78911.8, overlap = 11.25
PHY-3002 : Step(56): len = 76749.5, overlap = 11.25
PHY-3002 : Step(57): len = 74618.1, overlap = 11.75
PHY-3002 : Step(58): len = 72405, overlap = 12.0625
PHY-3002 : Step(59): len = 72066, overlap = 12.3125
PHY-3002 : Step(60): len = 70364.2, overlap = 12.4375
PHY-3002 : Step(61): len = 68829.9, overlap = 12.9375
PHY-3002 : Step(62): len = 66960.5, overlap = 13.3125
PHY-3002 : Step(63): len = 65179.8, overlap = 15.9375
PHY-3002 : Step(64): len = 65075.4, overlap = 13.5625
PHY-3002 : Step(65): len = 64895.8, overlap = 13.625
PHY-3002 : Step(66): len = 64329.2, overlap = 11.5
PHY-3002 : Step(67): len = 63837, overlap = 11.6875
PHY-3002 : Step(68): len = 63026.9, overlap = 11.9375
PHY-3002 : Step(69): len = 61175.7, overlap = 12.4375
PHY-3002 : Step(70): len = 59977.9, overlap = 15
PHY-3002 : Step(71): len = 59194, overlap = 14.9375
PHY-3002 : Step(72): len = 58756.6, overlap = 14.9375
PHY-3002 : Step(73): len = 57891.8, overlap = 14.625
PHY-3002 : Step(74): len = 56889.6, overlap = 14.875
PHY-3002 : Step(75): len = 53443.5, overlap = 18.0625
PHY-3002 : Step(76): len = 52816.3, overlap = 17.9375
PHY-3002 : Step(77): len = 52552.1, overlap = 15.6875
PHY-3002 : Step(78): len = 52493.3, overlap = 15.4375
PHY-3002 : Step(79): len = 52578, overlap = 13.1875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000181584
PHY-3002 : Step(80): len = 52722.2, overlap = 15.4375
PHY-3002 : Step(81): len = 52749.3, overlap = 15.4375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000363168
PHY-3002 : Step(82): len = 52679.4, overlap = 15.3125
PHY-3002 : Step(83): len = 52763, overlap = 13.0625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.007053s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (221.5%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.073710s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000386873
PHY-3002 : Step(84): len = 56214.5, overlap = 11.875
PHY-3002 : Step(85): len = 56400.4, overlap = 11.5625
PHY-3002 : Step(86): len = 55481.9, overlap = 11.5625
PHY-3002 : Step(87): len = 55286.9, overlap = 11.5625
PHY-3002 : Step(88): len = 54105.2, overlap = 11.5
PHY-3002 : Step(89): len = 53791.4, overlap = 10.8125
PHY-3002 : Step(90): len = 53195.5, overlap = 11.4062
PHY-3002 : Step(91): len = 52940.6, overlap = 11.4062
PHY-3002 : Step(92): len = 53039.3, overlap = 9.40625
PHY-3002 : Step(93): len = 52334.8, overlap = 15.25
PHY-3002 : Step(94): len = 50931.9, overlap = 15.4375
PHY-3002 : Step(95): len = 50259.9, overlap = 15.3438
PHY-3002 : Step(96): len = 49910.6, overlap = 16.8125
PHY-3002 : Step(97): len = 49561.8, overlap = 17.5
PHY-3002 : Step(98): len = 49223.9, overlap = 15.0938
PHY-3002 : Step(99): len = 49029.6, overlap = 15.4375
PHY-3002 : Step(100): len = 48207.1, overlap = 15.4062
PHY-3002 : Step(101): len = 47785.7, overlap = 14.0312
PHY-3002 : Step(102): len = 47564.6, overlap = 14.0312
PHY-3002 : Step(103): len = 47432.3, overlap = 13.9688
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000773746
PHY-3002 : Step(104): len = 47465.3, overlap = 14.0312
PHY-3002 : Step(105): len = 47514, overlap = 13.875
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00154749
PHY-3002 : Step(106): len = 47316.6, overlap = 14
PHY-3002 : Step(107): len = 47244.1, overlap = 13.875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.081252s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.20931e-05
PHY-3002 : Step(108): len = 47915.5, overlap = 50.2188
PHY-3002 : Step(109): len = 48755.4, overlap = 42.25
PHY-3002 : Step(110): len = 49368.9, overlap = 37.6875
PHY-3002 : Step(111): len = 49038.8, overlap = 38.5
PHY-3002 : Step(112): len = 48844.9, overlap = 37.875
PHY-3002 : Step(113): len = 48826.8, overlap = 37.0312
PHY-3002 : Step(114): len = 48861.8, overlap = 37.125
PHY-3002 : Step(115): len = 48655.3, overlap = 36.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000124186
PHY-3002 : Step(116): len = 48581, overlap = 36.5625
PHY-3002 : Step(117): len = 48442.1, overlap = 36.1562
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000248372
PHY-3002 : Step(118): len = 49382.1, overlap = 35.875
PHY-3002 : Step(119): len = 50080.9, overlap = 35.4062
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000496745
PHY-3002 : Step(120): len = 50281.8, overlap = 35.3125
PHY-3002 : Step(121): len = 50344.1, overlap = 35.1562
PHY-3002 : Step(122): len = 50703.9, overlap = 34.8438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000891222
PHY-3002 : Step(123): len = 50802.2, overlap = 35.2188
PHY-3002 : Step(124): len = 50901.9, overlap = 35.3438
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.001442
PHY-3002 : Step(125): len = 51778.8, overlap = 36.3438
PHY-3002 : Step(126): len = 52033.8, overlap = 34.1875
PHY-3002 : Step(127): len = 52143.1, overlap = 31.5312
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7291, tnet num: 2061, tinst num: 1507, tnode num: 10277, tedge num: 12402.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 84.78 peak overflow 2.03
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2063.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54904, over cnt = 237(0%), over = 909, worst = 17
PHY-1001 : End global iterations;  0.093494s wall, 0.140625s user + 0.015625s system = 0.156250s CPU (167.1%)

PHY-1001 : Congestion index: top1 = 41.57, top5 = 25.08, top10 = 15.93, top15 = 11.29.
PHY-1001 : End incremental global routing;  0.148866s wall, 0.187500s user + 0.015625s system = 0.203125s CPU (136.4%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.072609s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.6%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.255940s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (122.1%)

OPT-1001 : Current memory(MB): used = 209, reserve = 173, peak = 209.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1606/2063.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 54904, over cnt = 237(0%), over = 909, worst = 17
PHY-1002 : len = 60776, over cnt = 151(0%), over = 347, worst = 10
PHY-1002 : len = 63136, over cnt = 81(0%), over = 132, worst = 8
PHY-1002 : len = 64864, over cnt = 18(0%), over = 20, worst = 2
PHY-1002 : len = 65144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.106951s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (116.9%)

PHY-1001 : Congestion index: top1 = 36.25, top5 = 24.94, top10 = 17.80, top15 = 12.94.
OPT-1001 : End congestion update;  0.156008s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (110.2%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2061 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067197s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (93.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.227236s wall, 0.250000s user + 0.000000s system = 0.250000s CPU (110.0%)

OPT-1001 : Current memory(MB): used = 212, reserve = 176, peak = 212.
OPT-1001 : End physical optimization;  0.781293s wall, 0.953125s user + 0.015625s system = 0.968750s CPU (124.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 352 LUT to BLE ...
SYN-4008 : Packed 352 LUT and 165 SEQ to BLE.
SYN-4003 : Packing 736 remaining SEQ's ...
SYN-4005 : Packed 125 SEQ with LUT/SLICE
SYN-4006 : 82 single LUT's are left
SYN-4006 : 611 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 963/1253 primitive instances ...
PHY-3001 : End packing;  0.051735s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (90.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 768 instances
RUN-1001 : 360 mslices, 359 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1906 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1364 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 766 instances, 719 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : After packing: Len = 51816.8, Over = 59
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6157, tnet num: 1904, tinst num: 766, tnode num: 8319, tedge num: 10881.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1904 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.347553s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (98.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.82094e-05
PHY-3002 : Step(128): len = 50244.9, overlap = 59.5
PHY-3002 : Step(129): len = 49915.3, overlap = 60
PHY-3002 : Step(130): len = 49724.1, overlap = 59.75
PHY-3002 : Step(131): len = 49852.2, overlap = 60
PHY-3002 : Step(132): len = 49957.8, overlap = 59.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.64188e-05
PHY-3002 : Step(133): len = 50390.9, overlap = 58.75
PHY-3002 : Step(134): len = 51033.8, overlap = 57.25
PHY-3002 : Step(135): len = 51575.2, overlap = 56.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000112838
PHY-3002 : Step(136): len = 52396.7, overlap = 54
PHY-3002 : Step(137): len = 53689.1, overlap = 51.5
PHY-3002 : Step(138): len = 54071.9, overlap = 51.25
PHY-3002 : Step(139): len = 53912.6, overlap = 49.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.087396s wall, 0.125000s user + 0.078125s system = 0.203125s CPU (232.4%)

PHY-3001 : Trial Legalized: Len = 67893.4
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1904 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.056006s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (83.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000755452
PHY-3002 : Step(140): len = 64006, overlap = 8.5
PHY-3002 : Step(141): len = 62142.3, overlap = 13.25
PHY-3002 : Step(142): len = 60080.5, overlap = 14.25
PHY-3002 : Step(143): len = 58773.3, overlap = 15.5
PHY-3002 : Step(144): len = 58209.7, overlap = 16.25
PHY-3002 : Step(145): len = 57860.9, overlap = 17.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0015109
PHY-3002 : Step(146): len = 58078.2, overlap = 18.25
PHY-3002 : Step(147): len = 58219.3, overlap = 17.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00302181
PHY-3002 : Step(148): len = 58328.1, overlap = 17.25
PHY-3002 : Step(149): len = 58328.1, overlap = 17.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006404s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 62598.9, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006371s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 0, deltaY = 12, maxDist = 2.
PHY-3001 : Final: Len = 62818.9, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6157, tnet num: 1904, tinst num: 766, tnode num: 8319, tedge num: 10881.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 63/1906.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68704, over cnt = 153(0%), over = 237, worst = 7
PHY-1002 : len = 69704, over cnt = 81(0%), over = 105, worst = 4
PHY-1002 : len = 70712, over cnt = 11(0%), over = 14, worst = 2
PHY-1002 : len = 70960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.151618s wall, 0.125000s user + 0.046875s system = 0.171875s CPU (113.4%)

PHY-1001 : Congestion index: top1 = 30.91, top5 = 22.62, top10 = 17.68, top15 = 13.64.
PHY-1001 : End incremental global routing;  0.210194s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (104.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1904 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063786s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (98.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.307920s wall, 0.281250s user + 0.046875s system = 0.328125s CPU (106.6%)

OPT-1001 : Current memory(MB): used = 215, reserve = 179, peak = 215.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1691/1906.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70960, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006675s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 30.91, top5 = 22.62, top10 = 17.68, top15 = 13.64.
OPT-1001 : End congestion update;  0.059423s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (78.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1904 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.064013s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (122.0%)

OPT-0007 : Start: WNS 902 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 728 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 766 instances, 719 slices, 22 macros(207 instances: 132 mslices 75 lslices)
PHY-3001 : Cell area utilization is 9%
PHY-3001 : Initial: Len = 62872.8, Over = 0
PHY-3001 : End spreading;  0.005437s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 62872.8, Over = 0
PHY-3001 : End incremental legalization;  0.041421s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (75.4%)

OPT-0007 : Iter 1: improved WNS 902 TNS 0 NUM_FEPS 0 with 3 cells processed and 200 slack improved
OPT-0007 : Iter 2: improved WNS 902 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.179775s wall, 0.265625s user + 0.000000s system = 0.265625s CPU (147.8%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1904 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052902s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.6%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1679/1906.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70984, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008126s wall, 0.015625s user + 0.031250s system = 0.046875s CPU (576.8%)

PHY-1001 : Congestion index: top1 = 30.91, top5 = 22.63, top10 = 17.67, top15 = 13.64.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1904 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.055147s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 902 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.517241
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 902ps with logic level 1 
RUN-1001 :       #2 path slack 902ps with logic level 1 
RUN-1001 :       #3 path slack 902ps with logic level 1 
RUN-1001 :       #4 path slack 902ps with logic level 1 
RUN-1001 :       #5 path slack 902ps with logic level 1 
RUN-1001 :       #6 path slack 902ps with logic level 1 
RUN-1001 :       #7 path slack 902ps with logic level 1 
RUN-1001 :       #8 path slack 902ps with logic level 1 
RUN-1001 :       #9 path slack 902ps with logic level 1 
RUN-1001 :       #10 path slack 902ps with logic level 1 
OPT-1001 : End physical optimization;  1.010632s wall, 1.062500s user + 0.078125s system = 1.140625s CPU (112.9%)

RUN-1003 : finish command "place" in  6.279813s wall, 9.718750s user + 3.796875s system = 13.515625s CPU (215.2%)

RUN-1004 : used memory is 197 MB, reserved memory is 161 MB, peak memory is 220 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 768 instances
RUN-1001 : 360 mslices, 359 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 1906 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1364 nets have 2 pins
RUN-1001 : 434 nets have [3 - 5] pins
RUN-1001 : 66 nets have [6 - 10] pins
RUN-1001 : 22 nets have [11 - 20] pins
RUN-1001 : 14 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6157, tnet num: 1904, tinst num: 766, tnode num: 8319, tedge num: 10881.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 360 mslices, 359 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1904 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 68376, over cnt = 150(0%), over = 237, worst = 7
PHY-1002 : len = 69344, over cnt = 82(0%), over = 107, worst = 4
PHY-1002 : len = 70624, over cnt = 6(0%), over = 6, worst = 1
PHY-1002 : len = 70760, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.153442s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (91.6%)

PHY-1001 : Congestion index: top1 = 31.36, top5 = 22.50, top10 = 17.60, top15 = 13.58.
PHY-1001 : End global routing;  0.211835s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (103.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 234, reserve = 199, peak = 235.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 494, reserve = 461, peak = 494.
PHY-1001 : End build detailed router design. 3.838475s wall, 3.765625s user + 0.078125s system = 3.843750s CPU (100.1%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 32080, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.498892s wall, 1.484375s user + 0.000000s system = 1.484375s CPU (99.0%)

PHY-1001 : Current memory(MB): used = 526, reserve = 494, peak = 526.
PHY-1001 : End phase 1; 1.505682s wall, 1.500000s user + 0.000000s system = 1.500000s CPU (99.6%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 30% nets.
PHY-1001 : Routed 36% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 177288, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 527, reserve = 495, peak = 527.
PHY-1001 : End initial routed; 1.455673s wall, 2.546875s user + 0.140625s system = 2.687500s CPU (184.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1684(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.538   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.462   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.416404s wall, 0.406250s user + 0.000000s system = 0.406250s CPU (97.6%)

PHY-1001 : Current memory(MB): used = 529, reserve = 497, peak = 529.
PHY-1001 : End phase 2; 1.872179s wall, 2.953125s user + 0.140625s system = 3.093750s CPU (165.2%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 177288, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015014s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (104.1%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 177232, over cnt = 5(0%), over = 5, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024334s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (64.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 177264, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.022333s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (70.0%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1684(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.538   |   0.000   |   0   
RUN-1001 :   Hold   |  -0.120   |  -0.462   |   4   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.432169s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (101.2%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.233955s wall, 0.218750s user + 0.015625s system = 0.234375s CPU (100.2%)

PHY-1001 : Current memory(MB): used = 543, reserve = 511, peak = 543.
PHY-1001 : End phase 3; 0.877733s wall, 0.859375s user + 0.015625s system = 0.875000s CPU (99.7%)

PHY-1003 : Routed, final wirelength = 177264
PHY-1001 : Current memory(MB): used = 543, reserve = 511, peak = 543.
PHY-1001 : End export database. 0.009815s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  8.319923s wall, 9.296875s user + 0.250000s system = 9.546875s CPU (114.7%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6157, tnet num: 1904, tinst num: 766, tnode num: 8319, tedge num: 10881.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[3] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dia[3] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[9] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[0] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[11] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[2] slack -4ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[12] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_11.dib[3] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[38] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[2] slack -5ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[41] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dia[5] slack -120ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[46] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[1] slack -14ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[48] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[3] slack -76ps
RUN-1001 : 1 hold-buffer to be created.
RUN-1001 : fix hold violation on net signal_process/demodu/latch_sample_sum[50] endpin signal_process/demodu/fifo/ram_inst/ramread0_syn_49.dib[5] slack -111ps
RUN-1001 : 1 hold-buffer to be created.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6175, tnet num: 1913, tinst num: 775, tnode num: 8337, tedge num: 10899.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : All hold violations are fixed.
RUN-1001 : End hold fix;  3.463271s wall, 3.546875s user + 0.109375s system = 3.656250s CPU (105.6%)

RUN-1003 : finish command "route" in  12.402133s wall, 13.453125s user + 0.375000s system = 13.828125s CPU (111.5%)

RUN-1004 : used memory is 520 MB, reserved memory is 488 MB, peak memory is 543 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      787   out of  19600    4.02%
#reg                      989   out of  19600    5.05%
#le                      1398
  #lut only               409   out of   1398   29.26%
  #reg only               611   out of   1398   43.71%
  #lut&reg                378   out of   1398   27.04%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         434
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         101
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    39
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1398   |580     |207     |1020    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1014   |296     |114     |838     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |22     |16      |6       |19      |0       |0       |
|    demodu                  |Demodulation                                     |431    |109     |39      |347     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |57     |45      |6       |49      |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |17     |16      |0       |17      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |15      |0       |16      |0       |0       |
|    integ                   |Integration                                      |139    |24      |14      |112     |0       |0       |
|    modu                    |Modulation                                       |93     |29      |21      |89      |0       |1       |
|    rs422                   |Rs422Output                                      |305    |99      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |24     |19      |5       |18      |0       |0       |
|  u_uart                    |UART_Control                                     |90     |76      |7       |56      |0       |0       |
|    U0                      |speed_select_Tx                                  |28     |21      |7       |16      |0       |0       |
|    U1                      |uart_tx                                          |22     |19      |0       |19      |0       |0       |
|    U2                      |Ctrl_Data                                        |40     |36      |0       |21      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |70      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1337  
    #2          2       312   
    #3          3       106   
    #4          4        16   
    #5        5-10       72   
    #6        11-50      28   
    #7       101-500     1    
  Average     1.94            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6175, tnet num: 1913, tinst num: 775, tnode num: 8337, tedge num: 10899.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1913 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: a45c4e89daac1fb8b9e926ec4995beb34c13a3dd5d495ba73b91ad104315dc22 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 775
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1915, pip num: 14067
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1303 valid insts, and 37265 bits set as '1'.
BIT-1004 : the usercode register value: 00000000011000110000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.787852s wall, 21.031250s user + 0.078125s system = 21.109375s CPU (557.3%)

RUN-1004 : used memory is 517 MB, reserved memory is 487 MB, peak memory is 668 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231019_160737.log"
