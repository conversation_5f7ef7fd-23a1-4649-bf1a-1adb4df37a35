--------------------------------------------------------------
 --     Copyright (c) 2012-2023 Anlogic Inc.
 --  All Right Reserved.
--------------------------------------------------------------
 -- Log	:	This file is generated by Anlogic IP Generator.
 -- File	:	D:/GitProject/GitProject/Anlogic/IFOG501_2B/al_ip/SoltFIFO.vhd
 -- Date	:	2023 04 19
 -- TD version	:	5.6.69102
--------------------------------------------------------------

LIBRARY ieee;
USE work.ALL;
	USE ieee.std_logic_1164.all;
LIBRARY eagle_macro;
	USE eagle_macro.EAGLE_COMPONENTS.all;

ENTITY SoltFIFO IS
PORT (
	di	: IN STD_LOGIC_VECTOR(55 DOWNTO 0);

	rst	: IN STD_LOGIC;
	clk	: IN STD_LOGIC;
	we	: IN STD_LOGIC;
	re	: IN STD_LOGIC;
	do	: OUT STD_LOGIC_VECTOR(55 DOWNTO 0);
	empty_flag		: OUT STD_LOGIC;
	full_flag		: OUT STD_LOGIC;
	rdusedw			: OUT STD_LOGIC_VECTOR(8 DOWNTO 0);
	wrusedw			: OUT STD_LOGIC_VECTOR(8 DOWNTO 0)
	);
END SoltFIFO;

ARCHITECTURE struct OF SoltFIFO IS

	BEGIN
	inst : EG_LOGIC_RAMFIFO
		GENERIC MAP (
			DATA_WIDTH			=> 56,
			ADDR_WIDTH			=> 8,
			SHOWAHEAD			=> 0,
			IMPLEMENT			=>"AUTO"
		)
		PORT MAP (
			rst	=> rst,
			di	=> di,
			clk	=> clk,
			we	=> we,
			re	=> re,
			do	=> do,
			empty_flag	=> empty_flag,
			full_flag	=> full_flag,
			rdusedw		=> rdusedw,
			wrusedw		=> wrusedw
		);

END struct;
