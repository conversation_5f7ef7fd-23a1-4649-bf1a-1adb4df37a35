============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jul 28 16:56:12 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1627 instances
RUN-0007 : 373 luts, 984 seqs, 146 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2195 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1636 nets have 2 pins
RUN-1001 : 443 nets have [3 - 5] pins
RUN-1001 : 71 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 18 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1625 instances, 373 luts, 984 seqs, 221 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7782, tnet num: 2193, tinst num: 1625, tnode num: 11016, tedge num: 13158.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.299062s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 608383
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1625.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 485624, overlap = 20.25
PHY-3002 : Step(2): len = 448816, overlap = 20.25
PHY-3002 : Step(3): len = 394274, overlap = 15.75
PHY-3002 : Step(4): len = 353085, overlap = 9
PHY-3002 : Step(5): len = 335393, overlap = 15.75
PHY-3002 : Step(6): len = 325994, overlap = 15.75
PHY-3002 : Step(7): len = 317870, overlap = 15.75
PHY-3002 : Step(8): len = 310102, overlap = 20.25
PHY-3002 : Step(9): len = 303379, overlap = 20.25
PHY-3002 : Step(10): len = 294559, overlap = 20.25
PHY-3002 : Step(11): len = 287728, overlap = 20.25
PHY-3002 : Step(12): len = 281249, overlap = 20.25
PHY-3002 : Step(13): len = 275632, overlap = 20.25
PHY-3002 : Step(14): len = 267043, overlap = 20.25
PHY-3002 : Step(15): len = 261982, overlap = 20.25
PHY-3002 : Step(16): len = 257008, overlap = 20.25
PHY-3002 : Step(17): len = 248778, overlap = 20.25
PHY-3002 : Step(18): len = 240156, overlap = 20.25
PHY-3002 : Step(19): len = 237705, overlap = 20.25
PHY-3002 : Step(20): len = 227625, overlap = 20.25
PHY-3002 : Step(21): len = 219974, overlap = 20.25
PHY-3002 : Step(22): len = 217015, overlap = 20.25
PHY-3002 : Step(23): len = 212365, overlap = 20.25
PHY-3002 : Step(24): len = 181866, overlap = 20.25
PHY-3002 : Step(25): len = 177416, overlap = 20.25
PHY-3002 : Step(26): len = 175525, overlap = 20.25
PHY-3002 : Step(27): len = 159566, overlap = 20.25
PHY-3002 : Step(28): len = 153939, overlap = 20.3125
PHY-3002 : Step(29): len = 152062, overlap = 20.3125
PHY-3002 : Step(30): len = 146757, overlap = 20.4375
PHY-3002 : Step(31): len = 142571, overlap = 20.5625
PHY-3002 : Step(32): len = 140014, overlap = 20.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000124093
PHY-3002 : Step(33): len = 141386, overlap = 14
PHY-3002 : Step(34): len = 140174, overlap = 14
PHY-3002 : Step(35): len = 137005, overlap = 17.0312
PHY-3002 : Step(36): len = 135880, overlap = 17.0312
PHY-3002 : Step(37): len = 133190, overlap = 10.1875
PHY-3002 : Step(38): len = 128182, overlap = 14.3125
PHY-3002 : Step(39): len = 125694, overlap = 9.8125
PHY-3002 : Step(40): len = 123489, overlap = 9.90625
PHY-3002 : Step(41): len = 118392, overlap = 11.6875
PHY-3002 : Step(42): len = 117836, overlap = 9.3125
PHY-3002 : Step(43): len = 113248, overlap = 11.625
PHY-3002 : Step(44): len = 109204, overlap = 11.25
PHY-3002 : Step(45): len = 105756, overlap = 9.125
PHY-3002 : Step(46): len = 105255, overlap = 13.625
PHY-3002 : Step(47): len = 102516, overlap = 9.0625
PHY-3002 : Step(48): len = 101311, overlap = 11.3125
PHY-3002 : Step(49): len = 98594.8, overlap = 11.3125
PHY-3002 : Step(50): len = 97715.6, overlap = 11.4375
PHY-3002 : Step(51): len = 95220.2, overlap = 11.4375
PHY-3002 : Step(52): len = 92940.4, overlap = 11.25
PHY-3002 : Step(53): len = 92350.7, overlap = 9
PHY-3002 : Step(54): len = 91959.6, overlap = 9
PHY-3002 : Step(55): len = 89499.1, overlap = 11.25
PHY-3002 : Step(56): len = 88453.8, overlap = 11.25
PHY-3002 : Step(57): len = 85519.4, overlap = 9
PHY-3002 : Step(58): len = 84898.5, overlap = 9
PHY-3002 : Step(59): len = 83825.3, overlap = 11.25
PHY-3002 : Step(60): len = 83190, overlap = 9
PHY-3002 : Step(61): len = 79164.1, overlap = 16.6875
PHY-3002 : Step(62): len = 77984.8, overlap = 15
PHY-3002 : Step(63): len = 76769.6, overlap = 10.375
PHY-3002 : Step(64): len = 76162.8, overlap = 10.3125
PHY-3002 : Step(65): len = 73643.7, overlap = 12.5625
PHY-3002 : Step(66): len = 72907.7, overlap = 12.5625
PHY-3002 : Step(67): len = 72612.7, overlap = 12.5625
PHY-3002 : Step(68): len = 71325.8, overlap = 12.625
PHY-3002 : Step(69): len = 69680.5, overlap = 14.8125
PHY-3002 : Step(70): len = 68227.3, overlap = 17.3125
PHY-3002 : Step(71): len = 67848.8, overlap = 14.9375
PHY-3002 : Step(72): len = 66761.9, overlap = 14.875
PHY-3002 : Step(73): len = 65706.7, overlap = 14.5625
PHY-3002 : Step(74): len = 64729, overlap = 12.1875
PHY-3002 : Step(75): len = 63299.7, overlap = 14.25
PHY-3002 : Step(76): len = 62151, overlap = 14.375
PHY-3002 : Step(77): len = 61220.3, overlap = 14.375
PHY-3002 : Step(78): len = 60712.3, overlap = 14.375
PHY-3002 : Step(79): len = 60719.9, overlap = 14.3125
PHY-3002 : Step(80): len = 60394.9, overlap = 14.125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000248186
PHY-3002 : Step(81): len = 60481.6, overlap = 11.875
PHY-3002 : Step(82): len = 60441.7, overlap = 11.875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000496372
PHY-3002 : Step(83): len = 60468, overlap = 11.875
PHY-3002 : Step(84): len = 60473.8, overlap = 9.625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005738s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.067791s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (92.2%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000719492
PHY-3002 : Step(85): len = 64009, overlap = 6.6875
PHY-3002 : Step(86): len = 62255.8, overlap = 7.5625
PHY-3002 : Step(87): len = 61810.6, overlap = 6.25
PHY-3002 : Step(88): len = 60491.2, overlap = 5.875
PHY-3002 : Step(89): len = 58719.9, overlap = 6.25
PHY-3002 : Step(90): len = 57222.6, overlap = 5.59375
PHY-3002 : Step(91): len = 56148.9, overlap = 8.0625
PHY-3002 : Step(92): len = 55250.4, overlap = 8.28125
PHY-3002 : Step(93): len = 54164.1, overlap = 12.125
PHY-3002 : Step(94): len = 53335.2, overlap = 12.7188
PHY-3002 : Step(95): len = 52476.2, overlap = 14.0938
PHY-3002 : Step(96): len = 51601.1, overlap = 15.0938
PHY-3002 : Step(97): len = 50925.6, overlap = 17.3438
PHY-3002 : Step(98): len = 50075, overlap = 19.5
PHY-3002 : Step(99): len = 49596.8, overlap = 20.0312
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.074554s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (104.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000107311
PHY-3002 : Step(100): len = 49767.4, overlap = 62.0938
PHY-3002 : Step(101): len = 49856.9, overlap = 60.9062
PHY-3002 : Step(102): len = 50867.2, overlap = 55.25
PHY-3002 : Step(103): len = 51394.2, overlap = 55.7812
PHY-3002 : Step(104): len = 51434.8, overlap = 55.75
PHY-3002 : Step(105): len = 51318.3, overlap = 54.7812
PHY-3002 : Step(106): len = 51189.4, overlap = 54.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000214621
PHY-3002 : Step(107): len = 51566.2, overlap = 49.0938
PHY-3002 : Step(108): len = 52003.3, overlap = 48.3125
PHY-3002 : Step(109): len = 52184.6, overlap = 46
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000429242
PHY-3002 : Step(110): len = 52470.3, overlap = 44.0938
PHY-3002 : Step(111): len = 52831.5, overlap = 39.7188
PHY-3002 : Step(112): len = 53747.9, overlap = 37.0625
PHY-3002 : Step(113): len = 54135.3, overlap = 33.625
PHY-3002 : Step(114): len = 54209.5, overlap = 34.4375
PHY-3002 : Step(115): len = 53673.1, overlap = 32.0312
PHY-3002 : Step(116): len = 53163.2, overlap = 31.8438
PHY-3002 : Step(117): len = 52583.1, overlap = 33.0625
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7782, tnet num: 2193, tinst num: 1625, tnode num: 11016, tedge num: 13158.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 92.47 peak overflow 2.97
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2195.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56744, over cnt = 236(0%), over = 899, worst = 25
PHY-1001 : End global iterations;  0.055299s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (141.3%)

PHY-1001 : Congestion index: top1 = 40.26, top5 = 23.98, top10 = 16.18, top15 = 11.62.
PHY-1001 : End incremental global routing;  0.107226s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (131.1%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.073223s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (85.4%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.210930s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (111.1%)

OPT-1001 : Current memory(MB): used = 214, reserve = 178, peak = 214.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1681/2195.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56744, over cnt = 236(0%), over = 899, worst = 25
PHY-1002 : len = 61816, over cnt = 179(0%), over = 461, worst = 14
PHY-1002 : len = 66336, over cnt = 30(0%), over = 54, worst = 10
PHY-1002 : len = 67032, over cnt = 5(0%), over = 5, worst = 1
PHY-1002 : len = 67256, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.089858s wall, 0.109375s user + 0.000000s system = 0.109375s CPU (121.7%)

PHY-1001 : Congestion index: top1 = 37.31, top5 = 24.12, top10 = 17.59, top15 = 13.23.
OPT-1001 : End congestion update;  0.136288s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (114.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2193 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.077357s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (101.0%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.216288s wall, 0.234375s user + 0.000000s system = 0.234375s CPU (108.4%)

OPT-1001 : Current memory(MB): used = 217, reserve = 181, peak = 217.
OPT-1001 : End physical optimization;  0.716693s wall, 0.765625s user + 0.000000s system = 0.765625s CPU (106.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 373 LUT to BLE ...
SYN-4008 : Packed 373 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 803 remaining SEQ's ...
SYN-4005 : Packed 106 SEQ with LUT/SLICE
SYN-4006 : 109 single LUT's are left
SYN-4006 : 697 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1070/1398 primitive instances ...
PHY-3001 : End packing;  0.052950s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (118.0%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 831 instances
RUN-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2028 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1477 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 829 instances, 782 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52811, Over = 59.25
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2026, tinst num: 829, tnode num: 8890, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.322395s wall, 0.312500s user + 0.015625s system = 0.328125s CPU (101.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.68532e-05
PHY-3002 : Step(118): len = 52224.1, overlap = 60.25
PHY-3002 : Step(119): len = 51993.7, overlap = 59.75
PHY-3002 : Step(120): len = 51617.8, overlap = 60
PHY-3002 : Step(121): len = 51396.7, overlap = 61.75
PHY-3002 : Step(122): len = 51361.5, overlap = 61
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.37065e-05
PHY-3002 : Step(123): len = 51509.7, overlap = 59
PHY-3002 : Step(124): len = 51940.9, overlap = 58.5
PHY-3002 : Step(125): len = 52457.1, overlap = 56.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000107413
PHY-3002 : Step(126): len = 53021.5, overlap = 56
PHY-3002 : Step(127): len = 53632, overlap = 53.75
PHY-3002 : Step(128): len = 54445.4, overlap = 52.75
PHY-3002 : Step(129): len = 54764, overlap = 52
PHY-3002 : Step(130): len = 54957.4, overlap = 50.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.075706s wall, 0.078125s user + 0.062500s system = 0.140625s CPU (185.8%)

PHY-3001 : Trial Legalized: Len = 67926.7
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059047s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00085211
PHY-3002 : Step(131): len = 64953.5, overlap = 4
PHY-3002 : Step(132): len = 63317.6, overlap = 8.5
PHY-3002 : Step(133): len = 61320.7, overlap = 12.25
PHY-3002 : Step(134): len = 59937, overlap = 17.5
PHY-3002 : Step(135): len = 59044.2, overlap = 21.25
PHY-3002 : Step(136): len = 58789.1, overlap = 21.75
PHY-3002 : Step(137): len = 58360, overlap = 21.5
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00170422
PHY-3002 : Step(138): len = 58443, overlap = 22.5
PHY-3002 : Step(139): len = 58464.6, overlap = 22
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00340844
PHY-3002 : Step(140): len = 58601.2, overlap = 23
PHY-3002 : Step(141): len = 58601.2, overlap = 23
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006214s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (251.4%)

PHY-3001 : Legalized: Len = 63315.4, Over = 0
PHY-3001 : Spreading special nets. 7 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.007591s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 4, deltaY = 6, maxDist = 1.
PHY-3001 : Final: Len = 63495.4, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2026, tinst num: 829, tnode num: 8890, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 51/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69912, over cnt = 131(0%), over = 219, worst = 8
PHY-1002 : len = 70984, over cnt = 74(0%), over = 95, worst = 7
PHY-1002 : len = 72040, over cnt = 10(0%), over = 11, worst = 2
PHY-1002 : len = 72136, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.149238s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (94.2%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 22.45, top10 = 17.36, top15 = 13.95.
PHY-1001 : End incremental global routing;  0.204801s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (99.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069470s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (90.0%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.309077s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (101.1%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 220.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1785/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.008169s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 31.16, top5 = 22.45, top10 = 17.36, top15 = 13.95.
OPT-1001 : End congestion update;  0.060385s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (77.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.059707s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (104.7%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 791 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 829 instances, 782 slices, 25 macros(221 instances: 146 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 63487, Over = 0
PHY-3001 : End spreading;  0.005421s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (288.2%)

PHY-3001 : Final: Len = 63487, Over = 0
PHY-3001 : End incremental legalization;  0.036522s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (128.3%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.171582s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (91.1%)

OPT-1001 : Current memory(MB): used = 225, reserve = 189, peak = 225.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.063816s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (122.4%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1782/2028.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 72184, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 72184, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.018362s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (85.1%)

PHY-1001 : Congestion index: top1 = 31.14, top5 = 22.43, top10 = 17.36, top15 = 13.95.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051302s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 30.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.977632s wall, 0.953125s user + 0.000000s system = 0.953125s CPU (97.5%)

RUN-1003 : finish command "place" in  5.315604s wall, 8.734375s user + 2.171875s system = 10.906250s CPU (205.2%)

RUN-1004 : used memory is 203 MB, reserved memory is 167 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 831 instances
RUN-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2028 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1477 nets have 2 pins
RUN-1001 : 435 nets have [3 - 5] pins
RUN-1001 : 72 nets have [6 - 10] pins
RUN-1001 : 23 nets have [11 - 20] pins
RUN-1001 : 15 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2026, tinst num: 829, tnode num: 8890, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 391 mslices, 391 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 69448, over cnt = 127(0%), over = 208, worst = 8
PHY-1002 : len = 70488, over cnt = 70(0%), over = 89, worst = 3
PHY-1002 : len = 71544, over cnt = 3(0%), over = 3, worst = 1
PHY-1002 : len = 71576, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 71624, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.146026s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.3%)

PHY-1001 : Congestion index: top1 = 30.88, top5 = 22.26, top10 = 17.24, top15 = 13.83.
PHY-1001 : End global routing;  0.196687s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (95.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 238, reserve = 204, peak = 248.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.339785s wall, 3.234375s user + 0.078125s system = 3.312500s CPU (99.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 34904, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.445167s wall, 1.406250s user + 0.015625s system = 1.421875s CPU (98.4%)

PHY-1001 : Current memory(MB): used = 533, reserve = 503, peak = 534.
PHY-1001 : End phase 1; 1.453667s wall, 1.421875s user + 0.015625s system = 1.437500s CPU (98.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 32% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 48% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 184864, over cnt = 22(0%), over = 22, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 535, reserve = 504, peak = 535.
PHY-1001 : End initial routed; 1.168595s wall, 2.093750s user + 0.140625s system = 2.234375s CPU (191.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.102   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.386584s wall, 0.390625s user + 0.000000s system = 0.390625s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 536, reserve = 505, peak = 536.
PHY-1001 : End phase 2; 1.555269s wall, 2.484375s user + 0.140625s system = 2.625000s CPU (168.8%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 184864, over cnt = 22(0%), over = 22, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.017650s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (88.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 184760, over cnt = 6(0%), over = 6, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.024533s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (127.4%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 184840, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.024887s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (125.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1795(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.102   |   0.000   |   0   
RUN-1001 :   Hold   |   0.089   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.375423s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (99.9%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.181509s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (94.7%)

PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End phase 3; 0.751683s wall, 0.750000s user + 0.000000s system = 0.750000s CPU (99.8%)

PHY-1003 : Routed, final wirelength = 184840
PHY-1001 : Current memory(MB): used = 551, reserve = 520, peak = 551.
PHY-1001 : End export database. 0.011188s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : End detail routing;  7.299631s wall, 8.093750s user + 0.234375s system = 8.328125s CPU (114.1%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2026, tinst num: 829, tnode num: 8890, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  8.253156s wall, 9.046875s user + 0.234375s system = 9.281250s CPU (112.5%)

RUN-1004 : used memory is 505 MB, reserved memory is 480 MB, peak memory is 551 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      820   out of  19600    4.18%
#reg                     1072   out of  19600    5.47%
#le                      1517
  #lut only               445   out of   1517   29.33%
  #reg only               697   out of   1517   45.95%
  #lut&reg                375   out of   1517   24.72%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                        Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0         469
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3         108
#3        wendu/clk_us                    GCLK               lslice             wendu/clk_us_reg_syn_12.q1    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                 11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4         1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di               1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1517   |599     |221     |1103    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1122   |306     |128     |919     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |37     |30      |7       |20      |0       |0       |
|    demodu                  |Demodulation                                     |524    |112     |53      |433     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |163    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |51     |2       |0       |51      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |14      |0       |29      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |12      |0       |27      |0       |0       |
|    integ                   |Integration                                      |136    |15      |14      |110     |0       |0       |
|    modu                    |Modulation                                       |90     |32      |21      |86      |0       |1       |
|    rs422                   |Rs422Output                                      |308    |94      |29      |253     |0       |4       |
|    trans                   |SquareWaveGenerator                              |27     |23      |4       |17      |0       |0       |
|  u_uart                    |UART_Control                                     |101    |85      |7       |57      |0       |0       |
|    U0                      |speed_select_Tx                                  |35     |27      |7       |18      |0       |0       |
|    U1                      |uart_tx                                          |21     |17      |0       |15      |0       |0       |
|    U2                      |Ctrl_Data                                        |45     |41      |0       |24      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |72      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1441  
    #2          2       309   
    #3          3       105   
    #4          4        21   
    #5        5-10       78   
    #6        11-50      30   
    #7       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6555, tnet num: 2026, tinst num: 829, tnode num: 8890, tedge num: 11522.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2026 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 829
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2028, pip num: 14818
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1316 valid insts, and 39151 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.435380s wall, 19.234375s user + 0.078125s system = 19.312500s CPU (562.2%)

RUN-1004 : used memory is 522 MB, reserved memory is 491 MB, peak memory is 670 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230728_165612.log"
