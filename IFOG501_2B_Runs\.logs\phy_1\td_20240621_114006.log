============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Fri Jun 21 11:40:06 2024

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(65)
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/CtrlData.v
HDL-1007 : analyze verilog file ../../Src_al/SPI_MASTER.v
HDL-1007 : analyze verilog file ../../Src_al/Time_1ms.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
RUN-1001 : Project manager successfully analyzed 10 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 5.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 5.0000 "
RUN-1002 : start command "config_chipwatcher ../../sim/DACC.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 2 view nodes, 25 trigger nets, 25 data nets.
KIT-1004 : Chipwatcher code = 1001011011010110
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=80) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb011000) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
SYN-5064 CRITICAL-WARNING: Register "rst" in C:/Anlogic/TD5.6.2/cw\tap.v(35) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[19]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[18]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[17]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[16]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[15]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[14]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[13]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[12]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 CRITICAL-WARNING: Register "addr_long[11]" in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(49) is described with both an asynchronous reset/set and the 'default_reg_initial' value of the opposite polarity. Ignore the setting 'default_reg_initial = 0'.
SYN-5064 Similar messages will be suppressed.
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=80)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=2,BUS_DIN_NUM=25,BUS_CTRL_NUM=58,BUS_WIDTH='{32'sb01,32'sb011000},BUS_DIN_POS='{32'sb0,32'sb01},BUS_CTRL_POS='{32'sb0,32'sb0110})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb011000)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 2172/10 useful/useless nets, 1373/5 useful/useless insts
SYN-1016 : Merged 16 instances.
SYN-1032 : 1945/18 useful/useless nets, 1703/14 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 1 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 1 mux instances.
SYN-1015 : Optimize round 1, 282 better
SYN-1014 : Optimize round 2
SYN-1032 : 1750/15 useful/useless nets, 1508/16 useful/useless insts
SYN-1015 : Optimize round 2, 32 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 8 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 1774/156 useful/useless nets, 1554/30 useful/useless insts
SYN-1016 : Merged 21 instances.
SYN-2571 : Optimize after map_dsp, round 1, 207 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 24 instances.
SYN-2501 : Optimize round 1, 50 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 12 macro adder
SYN-1019 : Optimized 5 mux instances.
SYN-1016 : Merged 9 instances.
SYN-1032 : 2142/5 useful/useless nets, 1922/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 8022, tnet num: 2142, tinst num: 1921, tnode num: 10042, tedge num: 12326.
TMR-2508 : Levelizing timing graph completed, there are 69 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2142 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 175 (3.57), #lev = 7 (1.77)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 174 (3.59), #lev = 6 (1.89)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 395 instances into 174 LUTs, name keeping = 72%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 58 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 280 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 104 adder to BLE ...
SYN-4008 : Packed 104 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net uart/transmit will be merged to another kept net transmit
SYN-5055 WARNING: The kept net uart/Macc_data[23] will be merged to another kept net CtrlData/Macc_data[23]
SYN-5055 WARNING: The kept net uart/Macc_data[22] will be merged to another kept net CtrlData/Macc_data[22]
SYN-5055 WARNING: The kept net uart/Macc_data[21] will be merged to another kept net CtrlData/Macc_data[21]
SYN-5055 WARNING: The kept net uart/Macc_data[20] will be merged to another kept net CtrlData/Macc_data[20]
SYN-5055 WARNING: The kept net uart/Macc_data[19] will be merged to another kept net CtrlData/Macc_data[19]
SYN-5055 WARNING: The kept net uart/Macc_data[18] will be merged to another kept net CtrlData/Macc_data[18]
SYN-5055 WARNING: The kept net uart/Macc_data[17] will be merged to another kept net CtrlData/Macc_data[17]
SYN-5055 WARNING: The kept net uart/Macc_data[16] will be merged to another kept net CtrlData/Macc_data[16]
SYN-5055 WARNING: The kept net uart/Macc_data[15] will be merged to another kept net CtrlData/Macc_data[15]
SYN-5055 Similar messages will be suppressed.
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (180 clock/control pins, 0 other pins).
SYN-4027 : Net CtrlData/clk is clkc0 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4025 : Tag rtl::Net CtrlData/clk as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4026 : Tagged 3 rtl::Net as clock net
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1427 instances
RUN-0007 : 599 luts, 618 seqs, 108 mslices, 66 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1654 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 962 nets have 2 pins
RUN-1001 : 501 nets have [3 - 5] pins
RUN-1001 : 101 nets have [6 - 10] pins
RUN-1001 : 52 nets have [11 - 20] pins
RUN-1001 : 27 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     160     
RUN-1001 :   No   |  No   |  Yes  |     101     
RUN-1001 :   No   |  Yes  |  No   |     92      
RUN-1001 :   Yes  |  No   |  No   |     89      
RUN-1001 :   Yes  |  No   |  Yes  |     176     
RUN-1001 :   Yes  |  Yes  |  No   |      0      
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    2    |   7   |     12     
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 18
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1425 instances, 599 luts, 618 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-0007 : Cell area utilization is 4%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6971, tnet num: 1652, tinst num: 1425, tnode num: 8994, tedge num: 11442.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 1652 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.137650s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (102.2%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 368837
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1425.
PHY-3001 : End clustering;  0.000018s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 4%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 284422, overlap = 51.75
PHY-3002 : Step(2): len = 233946, overlap = 51.75
PHY-3002 : Step(3): len = 208648, overlap = 51.75
PHY-3002 : Step(4): len = 187457, overlap = 51.75
PHY-3002 : Step(5): len = 163626, overlap = 51.75
PHY-3002 : Step(6): len = 144266, overlap = 51.75
PHY-3002 : Step(7): len = 129158, overlap = 51.75
PHY-3002 : Step(8): len = 113482, overlap = 51.75
PHY-3002 : Step(9): len = 101467, overlap = 51.75
PHY-3002 : Step(10): len = 89622.4, overlap = 51.75
PHY-3002 : Step(11): len = 81643.5, overlap = 52.625
PHY-3002 : Step(12): len = 74237.9, overlap = 52.875
PHY-3002 : Step(13): len = 69175.2, overlap = 54
PHY-3002 : Step(14): len = 64695.5, overlap = 53.9375
PHY-3002 : Step(15): len = 61081.8, overlap = 49.7188
PHY-3002 : Step(16): len = 57434.3, overlap = 50.1875
PHY-3002 : Step(17): len = 55115.4, overlap = 51.125
PHY-3002 : Step(18): len = 51813, overlap = 50.625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 1.47399e-05
PHY-3002 : Step(19): len = 50938.4, overlap = 50.4062
PHY-3002 : Step(20): len = 50453.1, overlap = 52.6562
PHY-3002 : Step(21): len = 50535.9, overlap = 55.0312
PHY-3002 : Step(22): len = 47721.6, overlap = 48.4688
PHY-3002 : Step(23): len = 46380.4, overlap = 41.7188
PHY-3002 : Step(24): len = 45171.5, overlap = 46
PHY-3002 : Step(25): len = 44815.3, overlap = 43.75
PHY-3002 : Step(26): len = 44223.3, overlap = 43.875
PHY-3002 : Step(27): len = 43884.3, overlap = 39.375
PHY-3002 : Step(28): len = 43447.1, overlap = 37.2188
PHY-3002 : Step(29): len = 42948.8, overlap = 37.125
PHY-3002 : Step(30): len = 42024.2, overlap = 39.375
PHY-3002 : Step(31): len = 41585.6, overlap = 37.125
PHY-3002 : Step(32): len = 41196.2, overlap = 37.125
PHY-3002 : Step(33): len = 40931.6, overlap = 39.2812
PHY-3002 : Step(34): len = 40770.2, overlap = 36.9375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 2.94798e-05
PHY-3002 : Step(35): len = 40668.3, overlap = 36.9375
PHY-3002 : Step(36): len = 40608.4, overlap = 36.9375
PHY-3002 : Step(37): len = 40565.9, overlap = 36.9375
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 5.89596e-05
PHY-3002 : Step(38): len = 40593.6, overlap = 36.9375
PHY-3002 : Step(39): len = 40591, overlap = 36.75
PHY-3002 : Step(40): len = 40668.1, overlap = 36.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006431s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1652 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.042496s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (73.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(41): len = 43060.3, overlap = 9.46875
PHY-3002 : Step(42): len = 43227.4, overlap = 9.46875
PHY-3002 : Step(43): len = 43411.4, overlap = 7.875
PHY-3002 : Step(44): len = 43234, overlap = 7.875
PHY-3002 : Step(45): len = 43193, overlap = 7.34375
PHY-3002 : Step(46): len = 43079.1, overlap = 6.6875
PHY-3002 : Step(47): len = 42780.5, overlap = 6.90625
PHY-3002 : Step(48): len = 42566.7, overlap = 7.46875
PHY-3002 : Step(49): len = 42094.5, overlap = 8.25
PHY-3002 : Step(50): len = 41559.5, overlap = 8.65625
PHY-3002 : Step(51): len = 40885, overlap = 10.5938
PHY-3002 : Step(52): len = 39724.7, overlap = 11.6875
PHY-3002 : Step(53): len = 39081.6, overlap = 13.5938
PHY-3002 : Step(54): len = 38273.5, overlap = 16.4375
PHY-3002 : Step(55): len = 37706.5, overlap = 19.1562
PHY-3002 : Step(56): len = 36911.6, overlap = 21.125
PHY-3002 : Step(57): len = 36536.1, overlap = 21.8438
PHY-3002 : Step(58): len = 36247.1, overlap = 23.9688
PHY-3002 : Step(59): len = 36040.4, overlap = 25
PHY-3002 : Step(60): len = 35916, overlap = 26.0312
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000808501
PHY-3002 : Step(61): len = 35716.5, overlap = 25.5312
PHY-3002 : Step(62): len = 35912.6, overlap = 26.3125
PHY-3002 : Step(63): len = 36121.3, overlap = 25.8438
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.001617
PHY-3002 : Step(64): len = 36107.5, overlap = 25.0625
PHY-3002 : Step(65): len = 36153.5, overlap = 25
PHY-3002 : Step(66): len = 36219.7, overlap = 24.4375
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1652 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039741s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (118.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 6.51082e-05
PHY-3002 : Step(67): len = 36663.8, overlap = 67.9062
PHY-3002 : Step(68): len = 36867.5, overlap = 64.5312
PHY-3002 : Step(69): len = 37266, overlap = 59.6562
PHY-3002 : Step(70): len = 37610.1, overlap = 55.9375
PHY-3002 : Step(71): len = 38212.3, overlap = 53.5312
PHY-3002 : Step(72): len = 38070.7, overlap = 48.0312
PHY-3002 : Step(73): len = 37946.9, overlap = 47.6875
PHY-3002 : Step(74): len = 37758.1, overlap = 45.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000130216
PHY-3002 : Step(75): len = 37599.5, overlap = 44.3438
PHY-3002 : Step(76): len = 37653.1, overlap = 44.4375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000260433
PHY-3002 : Step(77): len = 38164, overlap = 43.9688
PHY-3002 : Step(78): len = 38406.3, overlap = 42.7812
PHY-3002 : Step(79): len = 38783.1, overlap = 40.125
PHY-3002 : Step(80): len = 39124.8, overlap = 38.875
PHY-3002 : Step(81): len = 39295.7, overlap = 39
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000520866
PHY-3002 : Step(82): len = 39324.6, overlap = 38.8438
PHY-3002 : Step(83): len = 39324.6, overlap = 38.8438
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.00104173
PHY-3002 : Step(84): len = 39831.8, overlap = 36.5625
PHY-3002 : Step(85): len = 40299.7, overlap = 33.125
PHY-3002 : Step(86): len = 40760.3, overlap = 31.8438
PHY-3002 : Step(87): len = 40510.4, overlap = 32.6562
PHY-3002 : Step(88): len = 39894.2, overlap = 34
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6971, tnet num: 1652, tinst num: 1425, tnode num: 8994, tedge num: 11442.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 78.25 peak overflow 2.81
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/1654.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 46680, over cnt = 189(0%), over = 669, worst = 15
PHY-1001 : End global iterations;  0.068385s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (114.2%)

PHY-1001 : Congestion index: top1 = 35.78, top5 = 21.08, top10 = 13.63, top15 = 9.77.
PHY-1001 : End incremental global routing;  0.114634s wall, 0.109375s user + 0.015625s system = 0.125000s CPU (109.0%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1652 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.043845s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (106.9%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 8 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1413 has valid locations, 23 needs to be replaced
PHY-3001 : design contains 1447 instances, 599 luts, 640 seqs, 174 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 40206
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 7059, tnet num: 1674, tinst num: 1447, tnode num: 9148, tedge num: 11574.
TMR-2508 : Levelizing timing graph completed, there are 37 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1674 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.145329s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (96.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(89): len = 40152.1, overlap = 4.125
PHY-3002 : Step(90): len = 40152.1, overlap = 4.125
PHY-3002 : Step(91): len = 40209.8, overlap = 4.125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 6%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1674 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.039167s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (79.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00112043
PHY-3002 : Step(92): len = 40196.7, overlap = 34.125
PHY-3002 : Step(93): len = 40196.7, overlap = 34.125
PHY-3001 : Final: Len = 40196.7, Over = 34.125
PHY-3001 : End incremental placement;  0.259029s wall, 0.265625s user + 0.093750s system = 0.359375s CPU (138.7%)

OPT-1001 : Total overflow 79.31 peak overflow 2.81
OPT-1001 : End high-fanout net optimization;  0.448229s wall, 0.437500s user + 0.109375s system = 0.546875s CPU (122.0%)

OPT-1001 : Current memory(MB): used = 199, reserve = 153, peak = 199.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1126/1676.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 47304, over cnt = 190(0%), over = 667, worst = 15
PHY-1002 : len = 50640, over cnt = 132(0%), over = 311, worst = 11
PHY-1002 : len = 53992, over cnt = 30(0%), over = 43, worst = 4
PHY-1002 : len = 54456, over cnt = 4(0%), over = 10, worst = 4
PHY-1002 : len = 54640, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.099227s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (94.5%)

PHY-1001 : Congestion index: top1 = 32.74, top5 = 21.76, top10 = 15.11, top15 = 11.01.
OPT-1001 : End congestion update;  0.139671s wall, 0.140625s user + 0.000000s system = 0.140625s CPU (100.7%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1674 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.039839s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (117.7%)

OPT-0007 : Start: WNS 3301 TNS 0 NUM_FEPS 0
OPT-1001 : End global optimization;  0.179724s wall, 0.187500s user + 0.000000s system = 0.187500s CPU (104.3%)

OPT-1001 : Current memory(MB): used = 197, reserve = 151, peak = 199.
OPT-1001 : End physical optimization;  0.747842s wall, 0.734375s user + 0.109375s system = 0.843750s CPU (112.8%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 599 LUT to BLE ...
SYN-4008 : Packed 599 LUT and 228 SEQ to BLE.
SYN-4003 : Packing 412 remaining SEQ's ...
SYN-4005 : Packed 209 SEQ with LUT/SLICE
SYN-4006 : 184 single LUT's are left
SYN-4006 : 203 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 802/1146 primitive instances ...
PHY-3001 : End packing;  0.049383s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (126.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 665 instances
RUN-1001 : 314 mslices, 315 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1450 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 723 nets have 2 pins
RUN-1001 : 527 nets have [3 - 5] pins
RUN-1001 : 117 nets have [6 - 10] pins
RUN-1001 : 44 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 663 instances, 629 slices, 26 macros(174 instances: 108 mslices 66 lslices)
PHY-3001 : Cell area utilization is 8%
PHY-3001 : After packing: Len = 40403.8, Over = 48.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6178, tnet num: 1448, tinst num: 663, tnode num: 7728, tedge num: 10527.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.151278s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (103.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 9.53637e-05
PHY-3002 : Step(94): len = 39931.8, overlap = 49
PHY-3002 : Step(95): len = 40010.8, overlap = 48.75
PHY-3002 : Step(96): len = 40207.8, overlap = 47.5
PHY-3002 : Step(97): len = 40799, overlap = 45.25
PHY-3002 : Step(98): len = 41451.4, overlap = 42.25
PHY-3002 : Step(99): len = 41948.5, overlap = 38.5
PHY-3002 : Step(100): len = 41938.1, overlap = 41
PHY-3002 : Step(101): len = 42162.9, overlap = 40.75
PHY-3002 : Step(102): len = 41904.9, overlap = 44.5
PHY-3002 : Step(103): len = 41843, overlap = 46.5
PHY-3002 : Step(104): len = 41549, overlap = 46.25
PHY-3002 : Step(105): len = 41172.7, overlap = 41.25
PHY-3002 : Step(106): len = 40874.8, overlap = 41.5
PHY-3002 : Step(107): len = 40247.1, overlap = 43.5
PHY-3002 : Step(108): len = 40029.7, overlap = 45
PHY-3002 : Step(109): len = 39775.8, overlap = 45.75
PHY-3002 : Step(110): len = 39470.1, overlap = 42.5
PHY-3002 : Step(111): len = 39405.9, overlap = 40.5
PHY-3002 : Step(112): len = 39189.1, overlap = 41
PHY-3002 : Step(113): len = 39094, overlap = 42.25
PHY-3002 : Step(114): len = 38971.6, overlap = 40.5
PHY-3002 : Step(115): len = 38970, overlap = 39
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000190727
PHY-3002 : Step(116): len = 39239.3, overlap = 39
PHY-3002 : Step(117): len = 39526.3, overlap = 38.25
PHY-3002 : Step(118): len = 39763.9, overlap = 37.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000381455
PHY-3002 : Step(119): len = 40621.6, overlap = 33.25
PHY-3002 : Step(120): len = 40825.8, overlap = 33.25
PHY-3002 : Step(121): len = 40830.5, overlap = 33.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.091130s wall, 0.109375s user + 0.093750s system = 0.203125s CPU (222.9%)

PHY-3001 : Trial Legalized: Len = 54732
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.034451s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.0120377
PHY-3002 : Step(122): len = 52416, overlap = 3
PHY-3002 : Step(123): len = 51821.6, overlap = 3.5
PHY-3002 : Step(124): len = 49485.3, overlap = 3.75
PHY-3002 : Step(125): len = 48652.3, overlap = 6
PHY-3002 : Step(126): len = 47409.7, overlap = 7.25
PHY-3002 : Step(127): len = 46156.2, overlap = 10
PHY-3002 : Step(128): len = 45377.3, overlap = 11.5
PHY-3002 : Step(129): len = 45035.5, overlap = 12
PHY-3002 : Step(130): len = 44857.2, overlap = 12.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.0240754
PHY-3002 : Step(131): len = 44797.5, overlap = 12.75
PHY-3002 : Step(132): len = 44722, overlap = 13
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.0481507
PHY-3002 : Step(133): len = 44697.9, overlap = 13
PHY-3002 : Step(134): len = 44653.9, overlap = 13.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.004724s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 49532.3, Over = 0
PHY-3001 : Spreading special nets. 3 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.004245s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 4 instances has been re-located, deltaX = 1, deltaY = 3, maxDist = 1.
PHY-3001 : Final: Len = 49632.3, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6178, tnet num: 1448, tinst num: 663, tnode num: 7728, tedge num: 10527.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 6/1450.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58696, over cnt = 165(0%), over = 280, worst = 5
PHY-1002 : len = 60088, over cnt = 97(0%), over = 127, worst = 3
PHY-1002 : len = 61320, over cnt = 16(0%), over = 20, worst = 2
PHY-1002 : len = 61608, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 61608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.160930s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (106.8%)

PHY-1001 : Congestion index: top1 = 28.66, top5 = 21.58, top10 = 16.53, top15 = 12.63.
PHY-1001 : End incremental global routing;  0.209964s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (104.2%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 1448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.042673s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (109.8%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.277845s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (106.8%)

OPT-1001 : Current memory(MB): used = 199, reserve = 154, peak = 200.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1267/1450.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 61608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.004750s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (328.9%)

PHY-1001 : Congestion index: top1 = 28.66, top5 = 21.58, top10 = 16.53, top15 = 12.63.
OPT-1001 : End congestion update;  0.047829s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (98.0%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.035097s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (89.0%)

OPT-0007 : Start: WNS 4072 TNS 0 NUM_FEPS 0
OPT-1001 : End path based optimization;  0.083120s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (94.0%)

OPT-1001 : Current memory(MB): used = 201, reserve = 156, peak = 201.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.034144s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (91.5%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1267/1450.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 61608, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006084s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 28.66, top5 = 21.58, top10 = 16.53, top15 = 12.63.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.033605s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (93.0%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 4072 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 28.172414
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 4072ps with logic level 7 
RUN-1001 :       #2 path slack 4081ps with logic level 6 
OPT-1001 : End physical optimization;  0.633116s wall, 0.656250s user + 0.000000s system = 0.656250s CPU (103.7%)

RUN-1003 : finish command "place" in  4.016192s wall, 5.859375s user + 2.312500s system = 8.171875s CPU (203.5%)

RUN-1004 : used memory is 192 MB, reserved memory is 146 MB, peak memory is 202 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 665 instances
RUN-1001 : 314 mslices, 315 lslices, 8 pads, 23 brams, 0 dsps
RUN-1001 : There are total 1450 nets
RUN-6004 WARNING: There are 6 nets with only 1 pin.
RUN-1001 : 723 nets have 2 pins
RUN-1001 : 527 nets have [3 - 5] pins
RUN-1001 : 117 nets have [6 - 10] pins
RUN-1001 : 44 nets have [11 - 20] pins
RUN-1001 : 28 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6178, tnet num: 1448, tinst num: 663, tnode num: 7728, tedge num: 10527.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 314 mslices, 315 lslices, 8 pads, 23 brams, 0 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 1448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 58608, over cnt = 165(0%), over = 280, worst = 5
PHY-1002 : len = 59984, over cnt = 99(0%), over = 129, worst = 4
PHY-1002 : len = 61120, over cnt = 25(0%), over = 29, worst = 2
PHY-1002 : len = 61552, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.149214s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (104.7%)

PHY-1001 : Congestion index: top1 = 28.73, top5 = 21.59, top10 = 16.53, top15 = 12.62.
PHY-1001 : End global routing;  0.196094s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.6%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 221, reserve = 177, peak = 221.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : net CtrlData/clk will be routed on clock mesh
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[1] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[0] is skipped due to 0 input or output
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net SPIM/cnt_data_dly[5] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[4] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[3] is skipped due to 0 input or output
PHY-5010 WARNING: Net SPIM/cnt_data_dly[2] is skipped due to 0 input or output
PHY-5010 Similar messages will be suppressed.
PHY-1001 : Current memory(MB): used = 482, reserve = 441, peak = 482.
PHY-1001 : End build detailed router design. 3.190479s wall, 3.140625s user + 0.046875s system = 3.187500s CPU (99.9%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 28064, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 0.588125s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (101.0%)

PHY-1001 : Current memory(MB): used = 514, reserve = 474, peak = 514.
PHY-1001 : End phase 1; 0.593990s wall, 0.593750s user + 0.000000s system = 0.593750s CPU (100.0%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 44% nets.
PHY-1001 : Routed 53% nets.
PHY-1001 : Routed 66% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 208672, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 515, reserve = 475, peak = 515.
PHY-1001 : End initial routed; 1.779019s wall, 2.437500s user + 0.109375s system = 2.546875s CPU (143.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1286(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.749   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.199124s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (102.0%)

PHY-1001 : Current memory(MB): used = 515, reserve = 475, peak = 515.
PHY-1001 : End phase 2; 1.978224s wall, 2.640625s user + 0.109375s system = 2.750000s CPU (139.0%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 208672, over cnt = 59(0%), over = 59, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.011624s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 207960, over cnt = 16(0%), over = 16, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.072860s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (107.2%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 207992, over cnt = 3(0%), over = 3, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.028254s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (276.5%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 208016, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 3; 0.020789s wall, 0.031250s user + 0.015625s system = 0.046875s CPU (225.5%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1286(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   1.749   |   0.000   |   0   
RUN-1001 :   Hold   |   0.137   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.207756s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (97.8%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 10 feed throughs used by 8 nets
PHY-1001 : End commit to database; 0.168219s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (102.2%)

PHY-1001 : Current memory(MB): used = 531, reserve = 490, peak = 531.
PHY-1001 : End phase 3; 0.628170s wall, 0.656250s user + 0.031250s system = 0.687500s CPU (109.4%)

PHY-1003 : Routed, final wirelength = 208016
PHY-1001 : Current memory(MB): used = 532, reserve = 490, peak = 532.
PHY-1001 : End export database. 0.010225s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (152.8%)

PHY-1001 : End detail routing;  6.580469s wall, 7.203125s user + 0.203125s system = 7.406250s CPU (112.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6178, tnet num: 1448, tinst num: 663, tnode num: 7728, tedge num: 10527.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.178210s wall, 7.828125s user + 0.203125s system = 8.031250s CPU (111.9%)

RUN-1004 : used memory is 489 MB, reserved memory is 448 MB, peak memory is 532 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                         8
  #input                    3
  #output                   4
  #inout                    1

Utilization Statistics
#lut                      975   out of  19600    4.97%
#reg                      643   out of  19600    3.28%
#le                      1178
  #lut only               535   out of   1178   45.42%
  #reg only               203   out of   1178   17.23%
  #lut&reg                440   out of   1178   37.35%
#dsp                        0   out of     29    0.00%
#bram                      23   out of     64   35.94%
  #bram9k                  23
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                        8   out of    188    4.26%
  #ireg                     1
  #oreg                     4
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       1   out of     16    6.25%

Clock Resource Statistics
Index     ClockNet             Type               DriverType         Driver                   Fanout
#1        CtrlData/clk         GCLK               pll                CLK120/pll_inst.clkc0    313
#2        config_inst_syn_9    GCLK               config             config_inst.jtck         102
#3        clk_in_dup_1         GeneralRouting     io                 clk_in_syn_2.di          1


Detailed IO Report

   Name      Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
    RXD        INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
  clk_in       INPUT        E10        LVCMOS25          N/A           N/A        NONE    
   miso        INPUT         N1        LVCMOS33          N/A          PULLUP      IREG    
    TXD       OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  cs_dacc     OUTPUT         M1        LVCMOS33           8            NONE       OREG    
   mosi       OUTPUT         R1        LVCMOS33           8            NONE       OREG    
   sclk       OUTPUT         P1        LVCMOS33           8            NONE       OREG    
    dq         INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+----------------------------------------------------------------------------------------------------------+
|Instance                            |Module          |le     |lut     |ripple  |seq     |bram    |dsp     |
+----------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B      |1178   |801     |174     |649     |23      |0       |
|  CLK120                            |global_clock    |0      |0       |0       |0       |0       |0       |
|  CtrlData                          |CtrlData        |116    |93      |11      |62      |0       |0       |
|    usms                            |Time_1ms        |28     |13      |5       |18      |0       |0       |
|  SPIM                              |SPI_MASTER      |203    |122     |23      |134     |0       |0       |
|  uart                              |UART_Control    |138    |125     |4       |54      |0       |0       |
|    U0                              |speed_select_Tx |22     |11      |4       |16      |0       |0       |
|    U1                              |uart_tx         |20     |18      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data       |96     |96      |0       |22      |0       |0       |
|  wendu                             |DS18B20         |207    |166     |41      |75      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER  |478    |272     |87      |297     |0       |0       |
|    wrapper_cwc_top                 |cwc_top         |478    |272     |87      |297     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int     |185    |90      |0       |179     |0       |0       |
|        reg_inst                    |register        |183    |88      |0       |177     |0       |0       |
|        tap_inst                    |tap             |2      |2       |0       |2       |0       |0       |
|      trigger_inst                  |trigger         |293    |182     |87      |118     |0       |0       |
|        bus_inst                    |bus_top         |74     |48      |26      |24      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det         |1      |1       |0       |1       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det         |73     |47      |26      |23      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl        |132    |91      |29      |71      |0       |0       |
+----------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       714   
    #2          2       310   
    #3          3       174   
    #4          4        43   
    #5        5-10      121   
    #6        11-50      67   
    #7       51-100      1    
    #8       101-500     1    
  Average     3.00            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 8, tpin num: 6178, tnet num: 1448, tinst num: 663, tnode num: 7728, tedge num: 10527.
TMR-2508 : Levelizing timing graph completed, there are 35 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 1448 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 2 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 0 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 2. Number of clock nets = 3 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		config_inst_syn_10
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 588953dbe18b1871915f908614aff20f758057a2d1993c4931f4cee582d18853 -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 663
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 1450, pip num: 15000
BIT-1002 : Init feedthrough with 8 threads.
BIT-1002 : Init feedthrough completely, num: 10
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1337 valid insts, and 41358 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010011011001011011010110
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  3.183567s wall, 17.578125s user + 0.078125s system = 17.656250s CPU (554.6%)

RUN-1004 : used memory is 503 MB, reserved memory is 465 MB, peak memory is 651 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20240621_114006.log"
