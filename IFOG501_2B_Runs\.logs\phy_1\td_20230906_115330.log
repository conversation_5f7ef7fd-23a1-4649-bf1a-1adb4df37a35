============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Wed Sep  6 11:53:30 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_s20.db -package EG4S20NG88"
ARC-1001 : Device Initialization.
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :            OPTION            |        IO         |   SETTING   
ARC-1001 : ---------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  P69/P82/P81/P80  |    gpio    
ARC-1001 :             done             |        P8         |    gpio    
ARC-1001 :           program_b          |        P67        |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |  P25/P22/P26/P21  |  dedicate  
ARC-1001 : ---------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks clk_in"
RUN-1002 : start command "set_clock_groups -exclusive -group "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 6 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1631 instances
RUN-0007 : 372 luts, 984 seqs, 151 mslices, 75 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2201 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1645 nets have 2 pins
RUN-1001 : 436 nets have [3 - 5] pins
RUN-1001 : 75 nets have [6 - 10] pins
RUN-1001 : 20 nets have [11 - 20] pins
RUN-1001 : 19 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     247     
RUN-1001 :   No   |  No   |  Yes  |     207     
RUN-1001 :   No   |  Yes  |  No   |      1      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     129     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    4    |  14   |     6      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 21
PHY-3001 : Initial placement ...
PHY-3001 : design contains 1629 instances, 372 luts, 984 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-0007 : Cell area utilization is 5%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7802, tnet num: 2199, tinst num: 1629, tnode num: 11042, tedge num: 13190.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.278322s wall, 0.281250s user + 0.000000s system = 0.281250s CPU (101.1%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 628725
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 1629.
PHY-3001 : End clustering;  0.000019s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 5%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 505729, overlap = 20.25
PHY-3002 : Step(2): len = 463133, overlap = 20.25
PHY-3002 : Step(3): len = 426449, overlap = 20.25
PHY-3002 : Step(4): len = 395762, overlap = 20.25
PHY-3002 : Step(5): len = 386634, overlap = 20.25
PHY-3002 : Step(6): len = 334221, overlap = 18
PHY-3002 : Step(7): len = 303757, overlap = 18
PHY-3002 : Step(8): len = 288217, overlap = 18
PHY-3002 : Step(9): len = 282398, overlap = 18
PHY-3002 : Step(10): len = 278137, overlap = 18
PHY-3002 : Step(11): len = 271597, overlap = 18
PHY-3002 : Step(12): len = 260893, overlap = 20.25
PHY-3002 : Step(13): len = 257075, overlap = 20.25
PHY-3002 : Step(14): len = 249630, overlap = 20.25
PHY-3002 : Step(15): len = 241463, overlap = 20.25
PHY-3002 : Step(16): len = 237171, overlap = 20.25
PHY-3002 : Step(17): len = 232972, overlap = 20.25
PHY-3002 : Step(18): len = 222401, overlap = 20.25
PHY-3002 : Step(19): len = 216649, overlap = 20.25
PHY-3002 : Step(20): len = 214376, overlap = 20.25
PHY-3002 : Step(21): len = 202947, overlap = 20.25
PHY-3002 : Step(22): len = 194561, overlap = 20.25
PHY-3002 : Step(23): len = 192677, overlap = 20.25
PHY-3002 : Step(24): len = 184913, overlap = 20.25
PHY-3002 : Step(25): len = 165973, overlap = 20.25
PHY-3002 : Step(26): len = 162347, overlap = 20.25
PHY-3002 : Step(27): len = 159350, overlap = 20.25
PHY-3002 : Step(28): len = 155296, overlap = 20.25
PHY-3002 : Step(29): len = 151197, overlap = 20.25
PHY-3002 : Step(30): len = 149106, overlap = 15.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000176028
PHY-3002 : Step(31): len = 150623, overlap = 13.5
PHY-3002 : Step(32): len = 148449, overlap = 11.25
PHY-3002 : Step(33): len = 145671, overlap = 18
PHY-3002 : Step(34): len = 141613, overlap = 18
PHY-3002 : Step(35): len = 139871, overlap = 11.25
PHY-3002 : Step(36): len = 136481, overlap = 13.5
PHY-3002 : Step(37): len = 134540, overlap = 13.5
PHY-3002 : Step(38): len = 133497, overlap = 11.25
PHY-3002 : Step(39): len = 127590, overlap = 13.5
PHY-3002 : Step(40): len = 123004, overlap = 9
PHY-3002 : Step(41): len = 122238, overlap = 13.5
PHY-3002 : Step(42): len = 119877, overlap = 13.5
PHY-3002 : Step(43): len = 118427, overlap = 11.25
PHY-3002 : Step(44): len = 113672, overlap = 9
PHY-3002 : Step(45): len = 112499, overlap = 11.25
PHY-3002 : Step(46): len = 110609, overlap = 13.5
PHY-3002 : Step(47): len = 109522, overlap = 9
PHY-3002 : Step(48): len = 104134, overlap = 13.5
PHY-3002 : Step(49): len = 101681, overlap = 13.5
PHY-3002 : Step(50): len = 100101, overlap = 13.5
PHY-3002 : Step(51): len = 99381, overlap = 9
PHY-3002 : Step(52): len = 96418.2, overlap = 6.75
PHY-3002 : Step(53): len = 95065.5, overlap = 9
PHY-3002 : Step(54): len = 92534.2, overlap = 11.25
PHY-3002 : Step(55): len = 91678.5, overlap = 11.25
PHY-3002 : Step(56): len = 90233.5, overlap = 11.25
PHY-3002 : Step(57): len = 88102.7, overlap = 13.5
PHY-3002 : Step(58): len = 84597.3, overlap = 13.5
PHY-3002 : Step(59): len = 84174.8, overlap = 13.5
PHY-3002 : Step(60): len = 83254.4, overlap = 9
PHY-3002 : Step(61): len = 82392.9, overlap = 11.375
PHY-3002 : Step(62): len = 80511.3, overlap = 13.75
PHY-3002 : Step(63): len = 77208.1, overlap = 12.25
PHY-3002 : Step(64): len = 75153.4, overlap = 7.75
PHY-3002 : Step(65): len = 75010.2, overlap = 7.875
PHY-3002 : Step(66): len = 73601.2, overlap = 14.5625
PHY-3002 : Step(67): len = 73273.5, overlap = 14.4375
PHY-3002 : Step(68): len = 72917.5, overlap = 12
PHY-3002 : Step(69): len = 70567.8, overlap = 11.75
PHY-3002 : Step(70): len = 68212.6, overlap = 16
PHY-3002 : Step(71): len = 67664.8, overlap = 11.5
PHY-3002 : Step(72): len = 67358, overlap = 11.5
PHY-3002 : Step(73): len = 66699.5, overlap = 9.1875
PHY-3002 : Step(74): len = 65618.5, overlap = 9.1875
PHY-3002 : Step(75): len = 64614.3, overlap = 11.5
PHY-3002 : Step(76): len = 63870.1, overlap = 11.625
PHY-3002 : Step(77): len = 63050.9, overlap = 11.625
PHY-3002 : Step(78): len = 62341.2, overlap = 11.4375
PHY-3002 : Step(79): len = 61142.7, overlap = 13.75
PHY-3002 : Step(80): len = 60591.9, overlap = 11.5625
PHY-3002 : Step(81): len = 60500.7, overlap = 7.0625
PHY-3002 : Step(82): len = 60222, overlap = 9.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000352056
PHY-3002 : Step(83): len = 59948.6, overlap = 7.0625
PHY-3002 : Step(84): len = 59823.5, overlap = 7.0625
PHY-3002 : Step(85): len = 59744.7, overlap = 7.0625
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000704112
PHY-3002 : Step(86): len = 59795.2, overlap = 7.0625
PHY-3002 : Step(87): len = 59805.5, overlap = 7.0625
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006229s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.060483s wall, 0.031250s user + 0.031250s system = 0.062500s CPU (103.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(88): len = 62065.8, overlap = 3.1875
PHY-3002 : Step(89): len = 60855.7, overlap = 3.1875
PHY-3002 : Step(90): len = 60400.2, overlap = 3.6875
PHY-3002 : Step(91): len = 59525.4, overlap = 4.1875
PHY-3002 : Step(92): len = 58662.4, overlap = 4.3125
PHY-3002 : Step(93): len = 57737.4, overlap = 4.1875
PHY-3002 : Step(94): len = 56587.5, overlap = 4
PHY-3002 : Step(95): len = 55838.2, overlap = 4.3125
PHY-3002 : Step(96): len = 54668.7, overlap = 3.6875
PHY-3002 : Step(97): len = 54029.4, overlap = 4.75
PHY-3002 : Step(98): len = 53678.6, overlap = 5
PHY-3002 : Step(99): len = 53140.1, overlap = 4.6875
PHY-3002 : Step(100): len = 52549.5, overlap = 5.0625
PHY-3002 : Step(101): len = 51867.8, overlap = 4.1875
PHY-3002 : Step(102): len = 51752.2, overlap = 3.8125
PHY-3002 : Step(103): len = 51227.7, overlap = 3.875
PHY-3002 : Step(104): len = 50204.3, overlap = 3.8125
PHY-3002 : Step(105): len = 49175, overlap = 3.1875
PHY-3002 : Step(106): len = 48050.2, overlap = 3.125
PHY-3002 : Step(107): len = 47530, overlap = 3.0625
PHY-3002 : Step(108): len = 46857.7, overlap = 7.9375
PHY-3002 : Step(109): len = 46234.8, overlap = 8.5
PHY-3002 : Step(110): len = 45808.2, overlap = 9.8125
PHY-3002 : Step(111): len = 45715.6, overlap = 10.5625
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000259336
PHY-3002 : Step(112): len = 45608, overlap = 10.3125
PHY-3002 : Step(113): len = 45570.3, overlap = 11.3125
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000518673
PHY-3002 : Step(114): len = 45609.5, overlap = 9.875
PHY-3002 : Step(115): len = 45659.1, overlap = 14.1875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.069493s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (89.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 5.04104e-05
PHY-3002 : Step(116): len = 46139.5, overlap = 72.0312
PHY-3002 : Step(117): len = 46491.2, overlap = 71.875
PHY-3002 : Step(118): len = 46600.6, overlap = 71.0312
PHY-3002 : Step(119): len = 46452.3, overlap = 70.3438
PHY-3002 : Step(120): len = 46347.5, overlap = 70.3438
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000100821
PHY-3002 : Step(121): len = 47660.2, overlap = 63.7188
PHY-3002 : Step(122): len = 48627.9, overlap = 58.4062
PHY-3002 : Step(123): len = 48948.1, overlap = 58.0625
PHY-3002 : Step(124): len = 48778, overlap = 57.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000201641
PHY-3002 : Step(125): len = 48857.3, overlap = 57.8438
PHY-3002 : Step(126): len = 48976.9, overlap = 58.1875
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000335507
PHY-3002 : Step(127): len = 49215.9, overlap = 53.1875
PHY-3002 : Step(128): len = 51383.3, overlap = 44.4062
PHY-3002 : Step(129): len = 52424.4, overlap = 44.375
PHY-3002 : Step(130): len = 52444.5, overlap = 38.75
PHY-3002 : Step(131): len = 52309.1, overlap = 38.875
PHY-3002 : Step(132): len = 52019.7, overlap = 37.7812
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7802, tnet num: 2199, tinst num: 1629, tnode num: 11042, tedge num: 13190.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 86.88 peak overflow 2.25
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2201.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56560, over cnt = 260(0%), over = 919, worst = 16
PHY-1001 : End global iterations;  0.081329s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (96.1%)

PHY-1001 : Congestion index: top1 = 39.14, top5 = 24.24, top10 = 16.54, top15 = 11.93.
PHY-1001 : End incremental global routing;  0.133196s wall, 0.125000s user + 0.000000s system = 0.125000s CPU (93.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2199 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.067234s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (116.2%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1590 has valid locations, 4 needs to be replaced
PHY-3001 : design contains 1632 instances, 372 luts, 987 seqs, 226 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 52346.4
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 7814, tnet num: 2202, tinst num: 1632, tnode num: 11063, tedge num: 13208.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2202 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.300493s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (98.8%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(133): len = 52402.1, overlap = 1.28125
PHY-3002 : Step(134): len = 52425.5, overlap = 1.28125
PHY-3002 : Step(135): len = 52418, overlap = 1.28125
PHY-3002 : Step(136): len = 52398.9, overlap = 1.28125
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 7%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2202 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.059062s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (79.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000328568
PHY-3002 : Step(137): len = 52400.9, overlap = 37.8438
PHY-3002 : Step(138): len = 52400.9, overlap = 37.8438
PHY-3001 : Final: Len = 52400.9, Over = 37.8438
PHY-3001 : End incremental placement;  0.442150s wall, 0.421875s user + 0.062500s system = 0.484375s CPU (109.5%)

OPT-1001 : Total overflow 86.94 peak overflow 2.25
OPT-1001 : End high-fanout net optimization;  0.676964s wall, 0.625000s user + 0.078125s system = 0.703125s CPU (103.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 221.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1674/2204.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 56744, over cnt = 259(0%), over = 913, worst = 16
PHY-1002 : len = 62240, over cnt = 154(0%), over = 287, worst = 15
PHY-1002 : len = 65304, over cnt = 38(0%), over = 55, worst = 7
PHY-1002 : len = 65984, over cnt = 1(0%), over = 1, worst = 1
PHY-1002 : len = 66032, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.087487s wall, 0.125000s user + 0.015625s system = 0.140625s CPU (160.7%)

PHY-1001 : Congestion index: top1 = 34.42, top5 = 24.38, top10 = 17.92, top15 = 13.37.
OPT-1001 : End congestion update;  0.130547s wall, 0.171875s user + 0.015625s system = 0.187500s CPU (143.6%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2202 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.068491s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (114.1%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.201474s wall, 0.250000s user + 0.015625s system = 0.265625s CPU (131.8%)

OPT-1001 : Current memory(MB): used = 220, reserve = 184, peak = 221.
OPT-1001 : End physical optimization;  1.154551s wall, 1.250000s user + 0.093750s system = 1.343750s CPU (116.4%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 372 LUT to BLE ...
SYN-4008 : Packed 372 LUT and 181 SEQ to BLE.
SYN-4003 : Packing 806 remaining SEQ's ...
SYN-4005 : Packed 102 SEQ with LUT/SLICE
SYN-4006 : 110 single LUT's are left
SYN-4006 : 704 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1076/1409 primitive instances ...
PHY-3001 : End packing;  0.048772s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (64.1%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 844 instances
RUN-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2038 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1487 nets have 2 pins
RUN-1001 : 427 nets have [3 - 5] pins
RUN-1001 : 81 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
PHY-3001 : design contains 842 instances, 795 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : After packing: Len = 52206.2, Over = 65.5
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6597, tnet num: 2036, tinst num: 842, tnode num: 8952, tedge num: 11588.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2036 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.318622s wall, 0.312500s user + 0.000000s system = 0.312500s CPU (98.1%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.46178e-05
PHY-3002 : Step(139): len = 51506.6, overlap = 66.75
PHY-3002 : Step(140): len = 50666.6, overlap = 69
PHY-3002 : Step(141): len = 50393.4, overlap = 68
PHY-3002 : Step(142): len = 50499.3, overlap = 67.5
PHY-3002 : Step(143): len = 50408.5, overlap = 69
PHY-3002 : Step(144): len = 49965.4, overlap = 67.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 4.92356e-05
PHY-3002 : Step(145): len = 50267.7, overlap = 66.5
PHY-3002 : Step(146): len = 51052, overlap = 66.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 9.84712e-05
PHY-3002 : Step(147): len = 51899.4, overlap = 65
PHY-3002 : Step(148): len = 52790.3, overlap = 61
PHY-3002 : Step(149): len = 53311.5, overlap = 59
PHY-3002 : Step(150): len = 53151.2, overlap = 59.25
PHY-3002 : Step(151): len = 52920.8, overlap = 59.25
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.090693s wall, 0.093750s user + 0.125000s system = 0.218750s CPU (241.2%)

PHY-3001 : Trial Legalized: Len = 68528.8
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2036 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.054825s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (85.5%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000533782
PHY-3002 : Step(152): len = 65869.8, overlap = 3.5
PHY-3002 : Step(153): len = 62929.9, overlap = 13.5
PHY-3002 : Step(154): len = 60930.6, overlap = 21.75
PHY-3002 : Step(155): len = 59891.6, overlap = 24.25
PHY-3002 : Step(156): len = 59013.7, overlap = 29.25
PHY-3002 : Step(157): len = 58536.5, overlap = 32
PHY-3002 : Step(158): len = 58100.5, overlap = 36
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00106756
PHY-3002 : Step(159): len = 58615.3, overlap = 33.5
PHY-3002 : Step(160): len = 58681, overlap = 32.25
PHY-3002 : Step(161): len = 58646.2, overlap = 32.25
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00213513
PHY-3002 : Step(162): len = 58849.6, overlap = 32
PHY-3002 : Step(163): len = 58873.2, overlap = 31.75
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.005224s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Legalized: Len = 64048.5, Over = 0
PHY-3001 : Spreading special nets. 6 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.006125s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : 10 instances has been re-located, deltaX = 2, deltaY = 8, maxDist = 1.
PHY-3001 : Final: Len = 64124.5, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6597, tnet num: 2036, tinst num: 842, tnode num: 8952, tedge num: 11588.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 129/2038.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 71200, over cnt = 133(0%), over = 198, worst = 6
PHY-1002 : len = 71800, over cnt = 88(0%), over = 110, worst = 5
PHY-1002 : len = 72968, over cnt = 12(0%), over = 12, worst = 1
PHY-1002 : len = 73144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.121046s wall, 0.156250s user + 0.015625s system = 0.171875s CPU (142.0%)

PHY-1001 : Congestion index: top1 = 32.50, top5 = 23.20, top10 = 18.10, top15 = 14.29.
PHY-1001 : End incremental global routing;  0.173860s wall, 0.203125s user + 0.015625s system = 0.218750s CPU (125.8%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2036 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.060397s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (103.5%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.265099s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (117.9%)

OPT-1001 : Current memory(MB): used = 220, reserve = 185, peak = 221.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1819/2038.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73144, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.006610s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 32.50, top5 = 23.20, top10 = 18.10, top15 = 14.29.
OPT-1001 : End congestion update;  0.053410s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (87.8%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2036 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.051188s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (91.6%)

OPT-0007 : Start: WNS 1102 TNS 0 NUM_FEPS 0
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 804 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 842 instances, 795 slices, 26 macros(226 instances: 151 mslices 75 lslices)
PHY-3001 : Cell area utilization is 10%
PHY-3001 : Initial: Len = 64125.2, Over = 0
PHY-3001 : End spreading;  0.005717s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (273.3%)

PHY-3001 : Final: Len = 64125.2, Over = 0
PHY-3001 : End incremental legalization;  0.036440s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (85.8%)

OPT-0007 : Iter 1: improved WNS 1102 TNS 0 NUM_FEPS 0 with 1 cells processed and 50 slack improved
OPT-0007 : Iter 2: improved WNS 1102 TNS 0 NUM_FEPS 0 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.155591s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (100.4%)

OPT-1001 : Current memory(MB): used = 224, reserve = 190, peak = 224.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2036 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.053158s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.2%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 1815/2038.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 73160, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010258s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (152.3%)

PHY-1001 : Congestion index: top1 = 32.50, top5 = 23.18, top10 = 18.11, top15 = 14.30.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2036 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.052739s wall, 0.046875s user + 0.000000s system = 0.046875s CPU (88.9%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS 1102 TNS 0 NUM_FEPS 0
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 32.034483
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack 1102ps with logic level 1 
RUN-1001 :       #2 path slack 1102ps with logic level 1 
RUN-1001 :       #3 path slack 1102ps with logic level 1 
RUN-1001 :       #4 path slack 1102ps with logic level 1 
RUN-1001 :       #5 path slack 1102ps with logic level 1 
RUN-1001 :       #6 path slack 1102ps with logic level 1 
RUN-1001 :       #7 path slack 1102ps with logic level 1 
RUN-1001 :       #8 path slack 1102ps with logic level 1 
RUN-1001 :       #9 path slack 1102ps with logic level 1 
RUN-1001 :       #10 path slack 1102ps with logic level 1 
OPT-1001 : End physical optimization;  0.893537s wall, 0.921875s user + 0.015625s system = 0.937500s CPU (104.9%)

RUN-1003 : finish command "place" in  5.967582s wall, 9.140625s user + 2.828125s system = 11.968750s CPU (200.6%)

RUN-1004 : used memory is 209 MB, reserved memory is 173 MB, peak memory is 225 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 844 instances
RUN-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
RUN-1001 : There are total 2038 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1487 nets have 2 pins
RUN-1001 : 427 nets have [3 - 5] pins
RUN-1001 : 81 nets have [6 - 10] pins
RUN-1001 : 21 nets have [11 - 20] pins
RUN-1001 : 16 nets have [21 - 99] pins
RUN-1001 : 5 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6597, tnet num: 2036, tinst num: 842, tnode num: 8952, tedge num: 11588.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 397 mslices, 398 lslices, 34 pads, 4 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2036 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 70352, over cnt = 146(0%), over = 224, worst = 7
PHY-1002 : len = 71312, over cnt = 76(0%), over = 89, worst = 5
PHY-1002 : len = 72360, over cnt = 4(0%), over = 4, worst = 1
PHY-1002 : len = 72440, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.147969s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (116.2%)

PHY-1001 : Congestion index: top1 = 32.13, top5 = 22.96, top10 = 17.95, top15 = 14.14.
PHY-1001 : End global routing;  0.195815s wall, 0.218750s user + 0.000000s system = 0.218750s CPU (111.7%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 240, reserve = 205, peak = 250.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 500, reserve = 469, peak = 500.
PHY-1001 : End build detailed router design. 3.210751s wall, 3.125000s user + 0.093750s system = 3.218750s CPU (100.2%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 35288, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.296604s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (101.2%)

PHY-1001 : Current memory(MB): used = 533, reserve = 504, peak = 534.
PHY-1001 : End phase 1; 1.302376s wall, 1.281250s user + 0.031250s system = 1.312500s CPU (100.8%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 33% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 47% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 182584, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 534, reserve = 504, peak = 534.
PHY-1001 : End initial routed; 1.055434s wall, 2.015625s user + 0.171875s system = 2.187500s CPU (207.3%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1801(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.371   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.381413s wall, 0.375000s user + 0.000000s system = 0.375000s CPU (98.3%)

PHY-1001 : Current memory(MB): used = 535, reserve = 505, peak = 535.
PHY-1001 : End phase 2; 1.436932s wall, 2.390625s user + 0.171875s system = 2.562500s CPU (178.3%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 182584, over cnt = 20(0%), over = 20, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.015860s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (98.5%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 182424, over cnt = 4(0%), over = 4, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.029690s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (105.3%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 182432, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 2; 0.020941s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (74.6%)

PHY-1001 : Update timing.....
PHY-1001 : 0/1801(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.371   |   0.000   |   0   
RUN-1001 :   Hold   |   0.080   |   0.000   |   0   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.358383s wall, 0.359375s user + 0.000000s system = 0.359375s CPU (100.3%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 3 feed throughs used by 3 nets
PHY-1001 : End commit to database; 0.166802s wall, 0.171875s user + 0.000000s system = 0.171875s CPU (103.0%)

PHY-1001 : Current memory(MB): used = 549, reserve = 518, peak = 549.
PHY-1001 : End phase 3; 0.715163s wall, 0.718750s user + 0.000000s system = 0.718750s CPU (100.5%)

PHY-1003 : Routed, final wirelength = 182432
PHY-1001 : Current memory(MB): used = 550, reserve = 519, peak = 550.
PHY-1001 : End export database. 0.010682s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.3%)

PHY-1001 : End detail routing;  6.864783s wall, 7.703125s user + 0.296875s system = 8.000000s CPU (116.5%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6597, tnet num: 2036, tinst num: 842, tnode num: 8952, tedge num: 11588.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : No hold violation.
RUN-1003 : finish command "route" in  7.786482s wall, 8.656250s user + 0.296875s system = 8.953125s CPU (115.0%)

RUN-1004 : used memory is 526 MB, reserved memory is 497 MB, peak memory is 550 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4S20NG88***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                      830   out of  19600    4.23%
#reg                     1077   out of  19600    5.49%
#le                      1534
  #lut only               457   out of   1534   29.79%
  #reg only               704   out of   1534   45.89%
  #lut&reg                373   out of   1534   24.32%
#dsp                        5   out of     29   17.24%
#bram                       4   out of     64    6.25%
  #bram9k                   4
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of     66   51.52%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       2   out of     16   12.50%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                               Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                                478
#2        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                                108
#3        wendu/clk_us                    GCLK               lslice             signal_process/ctrl_signal/modulate_reg_syn_24.q0    41
#4        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                                        11
#5        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                                1
#6        clk_in_dup_1                    GeneralRouting     io                 clk_in_syn_2.di                                      1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         P2        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         P3        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         P4        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT         P5        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        P10        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        P11        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        P12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        P14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        P17        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        P18        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P19        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        P77        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT        P34        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT        P45        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT        P47        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT        P48        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT        P49        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT        P50        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT        P51        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT        P52        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT        P55        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT        P63        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT        P62        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT        P61        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT        P57        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT        P59        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT        P60        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        P74        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        P76        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        P79        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P13        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT        P54        LVCMOS33           8            NONE       OREG    
      dq           INOUT        P87        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-----------------------------------------------------------------------------------------------------------------------------------+
|Instance                    |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-----------------------------------------------------------------------------------------------------------------------------------+
|top                         |IFOG501_2B                                       |1534   |604     |226     |1108    |4       |5       |
|  CLK120                    |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process            |SignalProcessing                                 |1138   |305     |134     |924     |4       |5       |
|    ctrl_signal             |SignalGenerator                                  |31     |21      |7       |23      |0       |0       |
|    demodu                  |Demodulation                                     |538    |119     |58      |434     |4       |0       |
|      fifo                  |Asys_fifo56X16                                   |167    |60      |20      |130     |4       |0       |
|        ram_inst            |ram_infer_Asys_fifo56X16                         |53     |2       |0       |53      |4       |0       |
|        rd_to_wr_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |27     |11      |0       |27      |0       |0       |
|        wr_to_rd_cross_inst |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |29     |13      |0       |29      |0       |0       |
|    integ                   |Integration                                      |137    |16      |14      |111     |0       |0       |
|    modu                    |Modulation                                       |91     |31      |21      |87      |0       |1       |
|    rs422                   |Rs422Output                                      |309    |91      |29      |250     |0       |4       |
|    trans                   |SquareWaveGenerator                              |32     |27      |5       |19      |0       |0       |
|  u_uart                    |UART_Control                                     |104    |92      |7       |50      |0       |0       |
|    U0                      |speed_select_Tx                                  |34     |27      |7       |15      |0       |0       |
|    U1                      |uart_tx                                          |18     |18      |0       |13      |0       |0       |
|    U2                      |Ctrl_Data                                        |52     |47      |0       |22      |0       |0       |
|  wendu                     |DS18B20                                          |211    |166     |45      |78      |0       |0       |
+-----------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1451  
    #2          2       309   
    #3          3       103   
    #4          4        15   
    #5        5-10       84   
    #6        11-50      31   
    #7       51-100      1    
    #8       101-500     1    
  Average     1.95            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 6597, tnet num: 2036, tinst num: 842, tnode num: 8952, tedge num: 11588.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2036 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 6 (1 unconstrainted).
TMR-5009 WARNING: No clock constraint on 1 clock net(s): 
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 9ee48c2fe34b67d908728ef363d4cf3ab1cf59dfd9f5ecb179383cc6a71d3b7e -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 842
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2038, pip num: 14729
BIT-1002 : Init feedthrough completely, num: 3
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1243 valid insts, and 39086 bits set as '1'.
BIT-1004 : the usercode register value: 00000000101111010000000000000000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  2.965105s wall, 16.968750s user + 0.031250s system = 17.000000s CPU (573.3%)

RUN-1004 : used memory is 551 MB, reserved memory is 518 MB, peak memory is 671 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20230906_115330.log"
