<chipwatcher project_name="IFOG501_2B" bit_file="IFOG501_2B_Runs/phy_1/IFOG501_2B.bit" bid_file="IFOG501_2B_Runs/phy_1/IFOG501_2B_inst.bid" chip_name="EG4X20BG256">
<!-- HMAC is: 38974c86c4e54ff81d6e409ece779be010005dbc91ef0060c69536d0c3c10326 -->
	<instance name="auto_chipwatcher_0" id="0" enabled="yes">
		<clock clk_name="clk_in" polarity="posedge"/>
		<config bram_name="auto_chipwatcher_0_logicbram" sample_depth="8192"/>
		<signal_vec>
			<data_nets>
				<net name="AD_DATA[0]"/>
				<net name="AD_DATA[10]"/>
				<net name="AD_DATA[11]"/>
				<net name="AD_DATA[1]"/>
				<net name="AD_DATA[2]"/>
				<net name="AD_DATA[3]"/>
				<net name="AD_DATA[4]"/>
				<net name="AD_DATA[5]"/>
				<net name="AD_DATA[6]"/>
				<net name="AD_DATA[7]"/>
				<net name="AD_DATA[8]"/>
				<net name="AD_DATA[9]"/>
				<net name="DA_DATA[0]"/>
				<net name="DA_DATA[10]"/>
				<net name="DA_DATA[11]"/>
				<net name="DA_DATA[12]"/>
				<net name="DA_DATA[13]"/>
				<net name="DA_DATA[1]"/>
				<net name="DA_DATA[2]"/>
				<net name="DA_DATA[3]"/>
				<net name="DA_DATA[4]"/>
				<net name="DA_DATA[5]"/>
				<net name="DA_DATA[6]"/>
				<net name="DA_DATA[7]"/>
				<net name="DA_DATA[8]"/>
				<net name="DA_DATA[9]"/>
				<net name="TXD"/>
				<net name="dq"/>
			</data_nets>
			<watcher_nodes>
				<bus name="AD_DATA" radix="bin" state="collapse" op="dont_care" type="input">
					<net name="AD_DATA[11]" type="input" trigger="enable"/>
					<net name="AD_DATA[10]" type="input" trigger="enable"/>
					<net name="AD_DATA[9]" type="input" trigger="enable"/>
					<net name="AD_DATA[8]" type="input" trigger="enable"/>
					<net name="AD_DATA[7]" type="input" trigger="enable"/>
					<net name="AD_DATA[6]" type="input" trigger="enable"/>
					<net name="AD_DATA[5]" type="input" trigger="enable"/>
					<net name="AD_DATA[4]" type="input" trigger="enable"/>
					<net name="AD_DATA[3]" type="input" trigger="enable"/>
					<net name="AD_DATA[2]" type="input" trigger="enable"/>
					<net name="AD_DATA[1]" type="input" trigger="enable"/>
					<net name="AD_DATA[0]" type="input" trigger="enable"/>
				</bus>
				<bus name="DA_DATA" radix="bin" state="collapse" op="dont_care" type="output">
					<net name="DA_DATA[13]" type="output" trigger="enable"/>
					<net name="DA_DATA[12]" type="output" trigger="enable"/>
					<net name="DA_DATA[11]" type="output" trigger="enable"/>
					<net name="DA_DATA[10]" type="output" trigger="enable"/>
					<net name="DA_DATA[9]" type="output" trigger="enable"/>
					<net name="DA_DATA[8]" type="output" trigger="enable"/>
					<net name="DA_DATA[7]" type="output" trigger="enable"/>
					<net name="DA_DATA[6]" type="output" trigger="enable"/>
					<net name="DA_DATA[5]" type="output" trigger="enable"/>
					<net name="DA_DATA[4]" type="output" trigger="enable"/>
					<net name="DA_DATA[3]" type="output" trigger="enable"/>
					<net name="DA_DATA[2]" type="output" trigger="enable"/>
					<net name="DA_DATA[1]" type="output" trigger="enable"/>
					<net name="DA_DATA[0]" type="output" trigger="enable"/>
				</bus>
				<net name="TXD" op="equal" type="output" trigger="enable"/>
				<net name="dq" op="equal" type="inout" trigger="enable"/>
			</watcher_nodes>
		</signal_vec>
		<trigger name="auto_chipwatcher_0_trigger" position="pre">
			<condition level="1" enabled="yes" type="basic_and">
				AD_DATA[0] == low && AD_DATA[10] == low && AD_DATA[11] == low && AD_DATA[1] == low && AD_DATA[2] == low && AD_DATA[3] == low && AD_DATA[4] == low && AD_DATA[5] == low && AD_DATA[6] == low && AD_DATA[7] == low && AD_DATA[8] == low && AD_DATA[9] == low && DA_DATA[0] == low && DA_DATA[10] == low && DA_DATA[11] == low && DA_DATA[12] == low && DA_DATA[13] == low && DA_DATA[1] == low && DA_DATA[2] == low && DA_DATA[3] == low && DA_DATA[4] == low && DA_DATA[5] == low && DA_DATA[6] == low && DA_DATA[7] == low && DA_DATA[8] == low && DA_DATA[9] == low && TXD == any && dq == any
			</condition>
			<log>
			</log>
		</trigger>
	</instance>
</chipwatcher>
