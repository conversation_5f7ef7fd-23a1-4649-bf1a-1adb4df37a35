/************************************************************\
 **     Copyright (c) 2012-2023 Anlogic Inc.
 **  All Right Reserved.\
\************************************************************/
/************************************************************\
 ** Log	:	This file is generated by Anlogic IP Generator.
 ** File	:	D:/GitProject/GitProject/Anlogic/IFOG501_2B/al_ip/MULTIPLIER14x15.v
 ** Date	:	2023 04 17
 ** TD version	:	5.6.69102
\************************************************************/

`timescale 1ns / 1ps

module MULTIPLIER14x15 ( p, a, b );

	output [28:0] p;

	input  [13:0] a;
	input  [14:0] b;



	EG_LOGIC_MULT #( .INPUT_WIDTH_A(14),
				.INPUT_WIDTH_B(15),
				.OUTPUT_WIDTH(29),
				.INPUTFORMAT("UNSIGNED"),
				.INPUTREGA("DISABLE"),
				.INPUTREGB("DISABLE"),
				.OUTPUTREG("DISABLE"),
				.IMPLEMENT("DSP")
			)
			inst(
				.a(a),
				.b(b),
				.p(p),
				.cea(1'b0),
				.ceb(1'b0),
				.cepd(1'b0),
				.clk(1'b0),
				.rstan(1'b0),
				.rstbn(1'b0),
				.rstpdn(1'b0)
			);


endmodule