============================================================
   Tang Dynasty, V5.6.71036
      Copyright (c) 2012-2023 Anlogic Inc.
   Executable = C:/Anlogic/TD5.6.2/bin/td.exe
   Built at =   20:34:38 Mar 21 2023
   Run by =     Administrator
   Run Date =   Mon Nov 13 14:28:32 2023

   Run on =     TLH-022
============================================================
RUN-1002 : start command "open_project IFOG501_2B.prj"
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :   default_reg_initial   |    auto    |       auto       |        
RUN-1001 :        infer_add        |     on     |        on        |        
RUN-1001 :        infer_fsm        |    off     |       off        |        
RUN-1001 :        infer_mult       |     on     |        on        |        
RUN-1001 :        infer_ram        |     on     |        on        |        
RUN-1001 :        infer_reg        |     on     |        on        |        
RUN-1001 :   infer_reg_init_value  |     on     |        on        |        
RUN-1001 :        infer_rom        |     on     |        on        |        
RUN-1001 :      infer_shifter      |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
HDL-1007 : analyze verilog file ../../al_ip/global_clock.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in ../../al_ip/global_clock.v(79)
HDL-1007 : analyze verilog file ../../al_ip/Asys_fifo56X16.v
HDL-1007 : analyze verilog file ../../Src_al/Ctrl_Data.v
HDL-1007 : analyze verilog file ../../Src_al/DS18B20.v
HDL-1007 : analyze verilog file ../../Src_al/Demodulation.v
HDL-1007 : analyze verilog file ../../Src_al/Integration.v
HDL-1007 : analyze verilog file ../../Src_al/Modulation.v
HDL-1007 : analyze verilog file ../../Src_al/Rs422Output.v
HDL-1007 : analyze verilog file ../../Src_al/SignalGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/SignalProcessing.v
HDL-1007 : analyze verilog file ../../Src_al/SquareWaveGenerator.v
HDL-1007 : analyze verilog file ../../Src_al/UART_Control.v
HDL-1007 : analyze verilog file ../../Src_al/speed_select_Tx.v
HDL-1007 : analyze verilog file ../../Src_al/uart_tx.v
HDL-1007 : analyze verilog file ../../Src_al/IFOG501_2B.v
HDL-1007 : undeclared symbol 'clk_AD', assumed default net type 'wire' in ../../Src_al/IFOG501_2B.v(94)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
HDL-5007 WARNING: 'd' is not declared in ../../Src_al/IFOG501_2B.v(74)
HDL-5007 WARNING: 'clk4p5m' is not declared in ../../Src_al/IFOG501_2B.v(81)
RUN-1001 : Project manager successfully analyzed 15 source files.
RUN-1002 : start command "import_device eagle_20.db -package EG4X20BG256"
ARC-1001 : Device Initialization.
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :            OPTION            |          IO          |   SETTING   
ARC-1001 : ------------------------------------------------------------------
ARC-1001 :   cso_b/cclk/mosi/miso/dout  |  T3/R11/T10/P10/M14  |    gpio    
ARC-1001 :             done             |         P13          |    gpio    
ARC-1001 :           program_b          |          T2          |  dedicate  
ARC-1001 :        tdi/tms/tck/tdo       |   C12/A15/C14/E14    |  dedicate  
ARC-1001 : ------------------------------------------------------------------
ARC-1004 : Device setting, marked 5 dedicate IOs in total.
RUN-1002 : start command "import_db ../syn_1/IFOG501_2B_gate.db"
RUN-1001 : Importing database generated by Tang Dynasty, V5.6.71036.
RUN-1001 : Database version number 46146.
RUN-1001 : Import flow parameters
RUN-1001 : Import timing constraints
RUN-1001 : Import IO constraints
RUN-1001 : Import Inst constraints
RUN-1001 : Import design success TD_VERSION=5.6.71036 , DB_VERSION=46146
RUN-1002 : start command "read_sdc ../../Constraints/IFOG_11FB.sdc"
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "create_clock -name clk_in -period 50 -waveform 0 25 "
RUN-1102 : create_clock: clock name: clk_in, type: 0, period: 50000, rise: 0, fall: 25000.
RUN-1002 : start command "get_nets wendu/clk_us"
RUN-1002 : start command "create_clock -name clk_us -period 1000 -waveform 0 500 "
RUN-1102 : create_clock: clock name: clk_us, type: 0, period: 1000000, rise: 0, fall: 500000.
RUN-1002 : start command "derive_pll_clocks"
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[0]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 6.0000 [get_pins {CLK120/pll_inst.clkc[0]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[0]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[0] -source  -master_clock clk_in -multiply_by 6.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[3]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[3]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[3] -source  -master_clock clk_in -multiply_by 3.0000 "
USR-1002 : Gen clock cmd:  create_generated_clock -name {CLK120/pll_inst.clkc[4]} -source [get_ports {clk_in}] -master_clock {clk_in} -multiply_by 3.0000 [get_pins {CLK120/pll_inst.clkc[4]}]
RUN-1002 : start command "get_ports clk_in"
RUN-1002 : start command "get_pins CLK120/pll_inst.clkc[4]"
RUN-1002 : start command "create_generated_clock -name CLK120/pll_inst.clkc[4] -source  -master_clock clk_in -multiply_by 3.0000 "
RUN-1002 : start command "create_clock -name clk_vir -period 16.667"
USR-1002 : create_clock -help -add -name <string> -period <double> -waveform <string> target
RUN-1101 : create_clock: defined a virtual clock: clk_vir.
RUN-1102 : create_clock: clock name: clk_vir, type: 3, period: 16667, rise: 0, fall: 8333.
RUN-1002 : start command "get_ports AD_DATA[*]"
RUN-1002 : start command "set_input_delay -clock clk_vir 14.5 "
RUN-1002 : start command "get_clocks CLK120/pll_inst.clkc[3]"
RUN-1002 : start command "set_clock_uncertainty 0.2 "
RUN-1002 : start command "read_sdc -ip Asys_fifo56X16 ../../al_ip/Asys_fifo56X16.tcl"
RUN-1002 : start command "get_ports clkw"
RUN-1002 : start command "get_nets  clkw  "
RUN-1002 : start command "get_clocks -nowarn -of nets 6828998000640"
RUN-1002 : start command "get_ports clkr"
RUN-1002 : start command "get_nets  clkr  "
RUN-1002 : start command "get_clocks -nowarn -of nets 1503238553600"
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_property -nowarn -max PERIOD "
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs rd_to_wr_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_max_delay -from  -to  8.0329999999999995 -datapath_only"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/primary_addr_gray_reg[*]"
RUN-1002 : start command "get_regs wr_to_rd_cross_inst/sync_r1[*]"
RUN-1002 : start command "set_false_path -from  -to  -hold 8.0329999999999995"
USR-1002 : set_false_path -help -setup -hold -from <list> -rise_from <list> -fall_from <list> -to <list> -rise_to <list> -fall_to <list> -through <list>
RUN-1002 : start command "config_chipwatcher ../../sim/111.cwc -dir "
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : ChipWatcher: import watcher inst auto_chipwatcher_0 completed, there are 4 view nodes, 28 trigger nets, 28 data nets.
KIT-1004 : Chipwatcher code = 1011101111010000
RUN-1002 : start command "compile_watcher"
RUN-1002 : start command "read_verilog -no_sch -dir C:/Anlogic/TD5.6.2/cw/ -file IFOG501_2B_watcherInst.sv -lib cw -top CW_TOP_WRAPPER"
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_det.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\bus_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\cwc_top.sv
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\detect_non_bus.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\emb_ctrl.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\register.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\tap.v
HDL-1007 : analyze verilog file C:/Anlogic/TD5.6.2/cw\trigger.sv
HDL-1007 : analyze verilog file IFOG501_2B_watcherInst.sv
HDL-1007 : elaborate module CW_TOP_WRAPPER in IFOG501_2B_watcherInst.sv(1)
HDL-1007 : elaborate module cwc_top(BUS_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\cwc_top.sv(21)
HDL-1007 : elaborate module cwc_cfg_int(CTRL_REG_LEN=94) in C:/Anlogic/TD5.6.2/cw\cwc_cfg_int.v(21)
HDL-1007 : elaborate module register(CTRL_REG_LEN=94) in C:/Anlogic/TD5.6.2/cw\register.v(21)
HDL-1007 : elaborate module tap in C:/Anlogic/TD5.6.2/cw\tap.v(21)
HDL-1007 : elaborate module trigger(BUS_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\trigger.sv(21)
HDL-1007 : elaborate module bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100}) in C:/Anlogic/TD5.6.2/cw\bus_top.sv(22)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01110) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module bus_det(BUS_WIDTH=32'sb01100) in C:/Anlogic/TD5.6.2/cw\bus_det.v(21)
HDL-1007 : elaborate module emb_ctrl in C:/Anlogic/TD5.6.2/cw\emb_ctrl.v(21)
HDL-1200 : Current top model is CW_TOP_WRAPPER
HDL-1100 : Inferred 0 RAMs.
RUN-1002 : start command "force_keep -a auto_chipwatcher_0_logicbram"
RUN-1002 : start command "optimize_rtl -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Rtl Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :       compress_add      |   ripple   |      ripple      |        
RUN-1001 :        elf_sload        |    off     |       off        |        
RUN-1001 :       fix_undriven      |     0      |        0         |        
RUN-1001 :         flatten         |    off     |       off        |        
RUN-1001 :       gate_sharing      |     on     |        on        |        
RUN-1001 :    hdl_warning_level    |   normal   |      normal      |        
RUN-1001 :   impl_internal_tribuf  |     on     |        on        |        
RUN-1001 :      impl_set_reset     |     on     |        on        |        
RUN-1001 :        infer_gsr        |    off     |       off        |        
RUN-1001 :      keep_hierarchy     |    auto    |       auto       |        
RUN-1001 :        max_fanout       |    9999    |       9999       |        
RUN-1001 :      max_oh2bin_len     |     10     |        10        |        
RUN-1001 :       merge_equal       |     on     |        on        |        
RUN-1001 :       merge_equiv       |     on     |        on        |        
RUN-1001 :        merge_mux        |    off     |       off        |        
RUN-1001 :     min_control_set     |     8      |        8         |        
RUN-1001 :      min_ripple_len     |    auto    |       auto       |        
RUN-1001 :       oh2bin_ratio      |    0.08    |       0.08       |        
RUN-1001 :     opt_adder_fanout    |     on     |        on        |        
RUN-1001 :        opt_arith        |     on     |        on        |        
RUN-1001 :       opt_big_gate      |    off     |       off        |        
RUN-1001 :        opt_const        |     on     |        on        |        
RUN-1001 :      opt_const_mult     |     on     |        on        |        
RUN-1001 :       opt_lessthan      |     on     |        on        |        
RUN-1001 :         opt_mux         |    off     |       off        |        
RUN-1001 :         opt_ram         |    high    |       high       |        
RUN-1001 :      rtl_sim_model      |    off     |       off        |        
RUN-1001 :         seq_syn         |     on     |        on        |        
RUN-1001 : --------------------------------------------------------------
SYN-1012 : SanityCheck: Model "IFOG501_2B"
SYN-1012 : SanityCheck: Model "CW_TOP_WRAPPER"
SYN-1012 : SanityCheck: Model "cwc_top(BUS_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100})"
SYN-1012 : SanityCheck: Model "cwc_cfg_int(CTRL_REG_LEN=94)"
SYN-1012 : SanityCheck: Model "register(CTRL_REG_LEN=94)"
SYN-1012 : SanityCheck: Model "tap"
SYN-1012 : SanityCheck: Model "trigger(BUS_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_top(BUS_NODE_NUM=4,BUS_DIN_NUM=28,BUS_CTRL_NUM=72,BUS_WIDTH='{32'sb01,32'sb01,32'sb01110,32'sb01100},BUS_DIN_POS='{32'sb0,32'sb01,32'sb010,32'sb010000},BUS_CTRL_POS='{32'sb0,32'sb0110,32'sb01100,32'sb0101100})"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01110)"
SYN-1012 : SanityCheck: Model "bus_det(BUS_WIDTH=32'sb01100)"
SYN-1012 : SanityCheck: Model "emb_ctrl"
SYN-1011 : Flatten model IFOG501_2B
SYN-1032 : 3351/18 useful/useless nets, 2059/10 useful/useless insts
SYN-1016 : Merged 24 instances.
SYN-1032 : 3088/20 useful/useless nets, 2412/16 useful/useless insts
SYN-1014 : Optimize round 1
SYN-1021 : Optimized 2 onehot mux instances.
SYN-1020 : Optimized 1 distributor mux.
SYN-1019 : Optimized 2 mux instances.
SYN-1015 : Optimize round 1, 317 better
SYN-1014 : Optimize round 2
SYN-1032 : 2849/30 useful/useless nets, 2173/32 useful/useless insts
SYN-1015 : Optimize round 2, 64 better
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
RUN-1002 : start command "optimize_gate -no_sch"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Gate Property
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :          Parameters         |  Settings  |  Default Values  |  Note  
RUN-1001 : ------------------------------------------------------------------
RUN-1001 :         cascade_dsp         |    off     |       off        |        
RUN-1001 :         cascade_eram        |    off     |       off        |        
RUN-1001 :        gate_sim_model       |    off     |       off        |        
RUN-1001 :        map_sim_model        |    off     |       off        |        
RUN-1001 :         map_strategy        |     1      |        1         |        
RUN-1001 :           opt_area          |   medium   |      medium      |        
RUN-1001 :          opt_timing         |    auto    |       auto       |        
RUN-1001 :         pack_effort         |   medium   |      medium      |        
RUN-1001 :      pack_lslice_ripple     |     on     |        on        |        
RUN-1001 :   pack_lslice_ripple_ratio  |    0.5     |       0.5        |        
RUN-1001 :        pack_seq_in_io       |     on     |       auto       |   *    
RUN-1001 :        ph1_mux_ratio        |    1.0     |       1.0        |        
RUN-1001 :            report           |  standard  |     standard     |        
RUN-1001 :           retiming          |    off     |       off        |        
RUN-1001 : ------------------------------------------------------------------
SYN-2001 : Map 34 IOs to PADs
RUN-1002 : start command "update_pll_param -module IFOG501_2B"
SYN-2501 : Processed 0 LOGIC_BUF instances.
SYN-2512 : LOGIC BRAM "auto_chipwatcher_0_logicbram"
SYN-2571 : Optimize after map_dsp, round 1
SYN-1032 : 2885/222 useful/useless nets, 2233/33 useful/useless insts
SYN-1016 : Merged 33 instances.
SYN-2571 : Optimize after map_dsp, round 1, 288 better
SYN-2571 : Optimize after map_dsp, round 2
SYN-2571 : Optimize after map_dsp, round 2, 0 better
SYN-1001 : Throwback 3 control mux instances
SYN-2501 : Optimize round 1
SYN-1016 : Merged 26 instances.
SYN-2501 : Optimize round 1, 54 better
SYN-2501 : Optimize round 2
SYN-2501 : Optimize round 2, 0 better
SYN-2501 : Map 14 macro adder
SYN-1019 : Optimized 9 mux instances.
SYN-1016 : Merged 10 instances.
SYN-1032 : 3266/5 useful/useless nets, 2614/4 useful/useless insts
RUN-1002 : start command "start_timer -prepack"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11982, tnet num: 3266, tinst num: 2613, tnode num: 16046, tedge num: 19287.
TMR-2508 : Levelizing timing graph completed, there are 51 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 3266 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
SYN-3001 : Running gate level optimization.
SYN-2581 : Mapping with K=5, #lut = 190 (3.55), #lev = 7 (1.92)
SYN-2551 : Post LUT mapping optimization.
SYN-2581 : Mapping with K=5, #lut = 190 (3.55), #lev = 7 (1.92)
SYN-3001 : Logic optimization runtime opt =   0.02 sec, map =   0.00 sec
SYN-3001 : Mapper mapped 447 instances into 190 LUTs, name keeping = 74%.
SYN-1001 : Packing model "IFOG501_2B" ...
SYN-4010 : Pack lib has 57 rtl pack models with 25 top pack blocks
SYN-1014 : Optimize round 1
SYN-1015 : Optimize round 1, 0 better
SYN-4002 : Packing 327 DFF/LATCH to SEQ ...
SYN-4009 : Pack 7 carry chain into lslice
SYN-4007 : Packing 112 adder to BLE ...
SYN-4008 : Packed 112 adder and 0 SEQ to BLE.
SYN-4007 : Packing 0 gate4 to BLE ...
SYN-4008 : Packed 0 gate4 and 0 SEQ to BLE.
SYN-4012 : Packed 0 FxMUX
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
KIT-1004 : Compile Chipwatcher: Clear obsolete physical data.
RUN-1003 : finish command "compile_watcher" in  1.490026s wall, 1.421875s user + 0.062500s system = 1.484375s CPU (99.6%)

RUN-1004 : used memory is 170 MB, reserved memory is 129 MB, peak memory is 202 MB
RUN-1002 : start command "place"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1001 : Print Place Property
RUN-1001 : --------------------------------------------------------------
RUN-1001 :        Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : --------------------------------------------------------------
RUN-1001 :      detailed_place     |     on     |        on        |        
RUN-1001 :          effort         |   medium   |      medium      |        
RUN-1001 :         fix_hold        |    off     |       off        |        
RUN-1001 :       legalization      |    ori     |       ori        |        
RUN-1001 :      new_spreading      |     on     |        on        |        
RUN-1001 :        opt_timing       |   medium   |      medium      |        
RUN-1001 :   post_clock_route_opt  |    off     |       off        |        
RUN-1001 :       pr_strategy       |     1      |        1         |        
RUN-1001 :        relaxation       |    1.00    |       1.00       |        
RUN-1001 :         retiming        |    off     |       off        |        
RUN-1001 : --------------------------------------------------------------
PHY-3001 : Placer runs in 8 thread(s).
RUN-1002 : start command "legalize_phy_inst"
SYN-1011 : Flatten model IFOG501_2B
SYN-5055 WARNING: The kept net _al_n0 will be merged to another kept net dly_rst
RUN-1002 : start command "phys_opt -simplify_lut"
SYN-4016 : Net config_inst_syn_10 driven by BUFG (207 clock/control pins, 0 other pins).
SYN-4027 : Net signal_process/clk is clkc0 of pll CLK120/pll_inst.
SYN-4027 : Net signal_process/demodu/clk_in is clkc3 of pll CLK120/pll_inst.
SYN-4027 : Net clk_AD_t is clkc4 of pll CLK120/pll_inst.
SYN-4019 : Net clk_in_dup_1 is refclk of pll CLK120/pll_inst.
SYN-4024 : Net "wendu/clk_us" drives clk pins.
SYN-4024 : Net "dlycnt_n1" drives clk pins.
SYN-4025 : Tag rtl::Net clk_AD_t as clock net
SYN-4025 : Tag rtl::Net clk_in_dup_1 as clock net
SYN-4025 : Tag rtl::Net config_inst_syn_10 as clock net
SYN-4025 : Tag rtl::Net dlycnt_n1 as clock net
SYN-4025 : Tag rtl::Net signal_process/clk as clock net
SYN-4025 : Tag rtl::Net signal_process/demodu/clk_in as clock net
SYN-4025 : Tag rtl::Net wendu/clk_us as clock net
SYN-4026 : Tagged 7 rtl::Net as clock net
SYN-4015 : Create BUFG instance for clk Net wendu/clk_us to drive 65 clock pins.
SYN-4015 : Create BUFG instance for clk Net dlycnt_n1 to drive 11 clock pins.
PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 2183 instances
RUN-0007 : 564 luts, 1249 seqs, 185 mslices, 110 lslices, 34 pads, 29 brams, 5 dsps
RUN-1001 : There are total 2837 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1930 nets have 2 pins
RUN-1001 : 719 nets have [3 - 5] pins
RUN-1001 : 116 nets have [6 - 10] pins
RUN-1001 : 25 nets have [11 - 20] pins
RUN-1001 : 36 nets have [21 - 99] pins
RUN-1001 : 10 nets have 100+ pins
RUN-1001 : Report Control nets information:
RUN-1001 : DFF Distribution
RUN-1001 : ----------------------------------
RUN-1001 :   CE   |  SSR  |  ASR  |  DFF Count  
RUN-1001 : ----------------------------------
RUN-1001 :   No   |  No   |  No   |     286     
RUN-1001 :   No   |  No   |  Yes  |     231     
RUN-1001 :   No   |  Yes  |  No   |      0      
RUN-1001 :   Yes  |  No   |  No   |     290     
RUN-1001 :   Yes  |  No   |  Yes  |     332     
RUN-1001 :   Yes  |  Yes  |  No   |     110     
RUN-1001 : ----------------------------------
RUN-0007 : Control Group Statistic
RUN-0007 : ---------------------------
RUN-0007 :   #CLK  |  #CE  |  #SSR/ASR  
RUN-0007 : ---------------------------
RUN-0007 :    6    |  17   |     9      
RUN-0007 : ---------------------------
RUN-0007 : Control Set = 27
PHY-3001 : Initial placement ...
PHY-3001 : design contains 2181 instances, 564 luts, 1249 seqs, 295 slices, 36 macros(295 instances: 185 mslices 110 lslices)
PHY-0007 : Cell area utilization is 6%
PHY-3001 : Start timing update ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11035, tnet num: 2835, tinst num: 2181, tnode num: 15339, tedge num: 18715.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 2.
TMR-2504 : Update delay of 2835 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 2.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.351329s wall, 0.343750s user + 0.015625s system = 0.359375s CPU (102.3%)

PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 762593
PHY-3001 : Clustering ...
PHY-3001 : Level 0 #clusters 2181.
PHY-3001 : End clustering;  0.000029s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 6%
PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(1): len = 661531, overlap = 74.25
PHY-3002 : Step(2): len = 634006, overlap = 76.5
PHY-3002 : Step(3): len = 606656, overlap = 76.5
PHY-3002 : Step(4): len = 592075, overlap = 72
PHY-3002 : Step(5): len = 531405, overlap = 76.5
PHY-3002 : Step(6): len = 489712, overlap = 76.5
PHY-3002 : Step(7): len = 474652, overlap = 74.25
PHY-3002 : Step(8): len = 464411, overlap = 67.5
PHY-3002 : Step(9): len = 455757, overlap = 69.75
PHY-3002 : Step(10): len = 442794, overlap = 72
PHY-3002 : Step(11): len = 433973, overlap = 74.25
PHY-3002 : Step(12): len = 427631, overlap = 69.75
PHY-3002 : Step(13): len = 416595, overlap = 72
PHY-3002 : Step(14): len = 406200, overlap = 74.25
PHY-3002 : Step(15): len = 399444, overlap = 76.5
PHY-3002 : Step(16): len = 391864, overlap = 74.25
PHY-3002 : Step(17): len = 379816, overlap = 74.25
PHY-3002 : Step(18): len = 371251, overlap = 72
PHY-3002 : Step(19): len = 366357, overlap = 72
PHY-3002 : Step(20): len = 355348, overlap = 69.75
PHY-3002 : Step(21): len = 342162, overlap = 72
PHY-3002 : Step(22): len = 337717, overlap = 72
PHY-3002 : Step(23): len = 329449, overlap = 72
PHY-3002 : Step(24): len = 300752, overlap = 67.5
PHY-3002 : Step(25): len = 292916, overlap = 69.75
PHY-3002 : Step(26): len = 289536, overlap = 69.75
PHY-3002 : Step(27): len = 266490, overlap = 72
PHY-3002 : Step(28): len = 255530, overlap = 72
PHY-3002 : Step(29): len = 252939, overlap = 72
PHY-3002 : Step(30): len = 245927, overlap = 72
PHY-3002 : Step(31): len = 237086, overlap = 72
PHY-3002 : Step(32): len = 232869, overlap = 72
PHY-3002 : Step(33): len = 226698, overlap = 74.25
PHY-3002 : Step(34): len = 221658, overlap = 74.25
PHY-3002 : Step(35): len = 218363, overlap = 74.25
PHY-3002 : Step(36): len = 209188, overlap = 74.25
PHY-3002 : Step(37): len = 199512, overlap = 72
PHY-3002 : Step(38): len = 196605, overlap = 69.75
PHY-3002 : Step(39): len = 191781, overlap = 69.75
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 3.10204e-05
PHY-3002 : Step(40): len = 195192, overlap = 69.75
PHY-3002 : Step(41): len = 191513, overlap = 69.75
PHY-3002 : Step(42): len = 190548, overlap = 69.75
PHY-3002 : Step(43): len = 188109, overlap = 58.5
PHY-3002 : Step(44): len = 185241, overlap = 60.75
PHY-3002 : Step(45): len = 182498, overlap = 60.75
PHY-3002 : Step(46): len = 173905, overlap = 65.25
PHY-3002 : Step(47): len = 170409, overlap = 63
PHY-3002 : Step(48): len = 168617, overlap = 60.75
PHY-3002 : Step(49): len = 165834, overlap = 63
PHY-3002 : Step(50): len = 163193, overlap = 63.0625
PHY-3002 : Step(51): len = 159658, overlap = 63.25
PHY-3002 : Step(52): len = 156953, overlap = 63.375
PHY-3002 : Step(53): len = 156004, overlap = 63.5625
PHY-3002 : Step(54): len = 152514, overlap = 64.125
PHY-3002 : Step(55): len = 148655, overlap = 63.9375
PHY-3002 : Step(56): len = 141238, overlap = 66.5
PHY-3002 : Step(57): len = 138972, overlap = 66.9375
PHY-3002 : Step(58): len = 137044, overlap = 68
PHY-3002 : Step(59): len = 132979, overlap = 65.5938
PHY-3002 : Step(60): len = 129804, overlap = 64.8438
PHY-3002 : Step(61): len = 127626, overlap = 64.6562
PHY-3002 : Step(62): len = 126042, overlap = 63.9062
PHY-3002 : Step(63): len = 122645, overlap = 65.5
PHY-3002 : Step(64): len = 119633, overlap = 66.5625
PHY-3002 : Step(65): len = 117267, overlap = 67.125
PHY-3002 : Step(66): len = 115055, overlap = 65
PHY-3002 : Step(67): len = 112712, overlap = 64.9375
PHY-3002 : Step(68): len = 110580, overlap = 61.75
PHY-3002 : Step(69): len = 108258, overlap = 61.8125
PHY-3002 : Step(70): len = 104989, overlap = 59.125
PHY-3002 : Step(71): len = 100851, overlap = 62.1875
PHY-3002 : Step(72): len = 100223, overlap = 60.125
PHY-3002 : Step(73): len = 99269.7, overlap = 62.5
PHY-3002 : Step(74): len = 98196.2, overlap = 62.375
PHY-3002 : Step(75): len = 97315.8, overlap = 60
PHY-3002 : Step(76): len = 96495.7, overlap = 64.75
PHY-3002 : Step(77): len = 94521.6, overlap = 64.4375
PHY-3002 : Step(78): len = 93359.6, overlap = 64.75
PHY-3002 : Step(79): len = 92309.8, overlap = 64.75
PHY-3002 : Step(80): len = 91285.4, overlap = 62.5
PHY-3002 : Step(81): len = 89315.7, overlap = 64.6875
PHY-3002 : Step(82): len = 88298, overlap = 58.25
PHY-3002 : Step(83): len = 86440.5, overlap = 61.5625
PHY-3002 : Step(84): len = 85558.4, overlap = 59.4375
PHY-3002 : Step(85): len = 83727.1, overlap = 64.9688
PHY-3002 : Step(86): len = 82959.3, overlap = 60.0938
PHY-3002 : Step(87): len = 81901.5, overlap = 62.3438
PHY-3002 : Step(88): len = 80844.9, overlap = 64.2188
PHY-3002 : Step(89): len = 80285.2, overlap = 64.4688
PHY-3002 : Step(90): len = 79911.8, overlap = 59.8125
PHY-3002 : Step(91): len = 79470.4, overlap = 59.375
PHY-3002 : Step(92): len = 79055.8, overlap = 59.375
PHY-3002 : Step(93): len = 78242.8, overlap = 61.375
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 6.20408e-05
PHY-3002 : Step(94): len = 78601.6, overlap = 59
PHY-3002 : Step(95): len = 78833.4, overlap = 59.125
PHY-3002 : Step(96): len = 78886.1, overlap = 59.2188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000124082
PHY-3002 : Step(97): len = 79231.3, overlap = 59.3438
PHY-3002 : Step(98): len = 79401.8, overlap = 59.3438
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.008949s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2835 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.088939s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (105.4%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 7.78928e-05
PHY-3002 : Step(99): len = 92115.3, overlap = 28.5625
PHY-3002 : Step(100): len = 92156, overlap = 29.0625
PHY-3002 : Step(101): len = 91362.9, overlap = 28.75
PHY-3002 : Step(102): len = 91386.4, overlap = 29.6562
PHY-3002 : Step(103): len = 91506.3, overlap = 29.375
PHY-3002 : Step(104): len = 90226.2, overlap = 29.5
PHY-3002 : Step(105): len = 89932.4, overlap = 30.1875
PHY-3002 : Step(106): len = 87611.9, overlap = 28.6875
PHY-3002 : Step(107): len = 86217.6, overlap = 26.6562
PHY-3002 : Step(108): len = 85578.2, overlap = 27.125
PHY-3002 : Step(109): len = 83322.7, overlap = 24.0625
PHY-3002 : Step(110): len = 81979.6, overlap = 23.4375
PHY-3002 : Step(111): len = 80171.3, overlap = 21.0312
PHY-3002 : Step(112): len = 78354.2, overlap = 19.625
PHY-3002 : Step(113): len = 77134, overlap = 20.0312
PHY-3002 : Step(114): len = 76070.5, overlap = 20.1875
PHY-3002 : Step(115): len = 74070.9, overlap = 20.8438
PHY-3002 : Step(116): len = 73276.5, overlap = 20.625
PHY-3002 : Step(117): len = 72576.9, overlap = 22.4062
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.000155786
PHY-3002 : Step(118): len = 72652.2, overlap = 22.5312
PHY-3002 : Step(119): len = 72538.7, overlap = 23.7188
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000311571
PHY-3002 : Step(120): len = 71996.6, overlap = 22.875
PHY-3002 : Step(121): len = 72029.6, overlap = 23.7188
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2835 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.085179s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (91.7%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 2.8551e-05
PHY-3002 : Step(122): len = 71924.6, overlap = 82.4375
PHY-3002 : Step(123): len = 72493.7, overlap = 77.6875
PHY-3002 : Step(124): len = 72961.7, overlap = 76.8125
PHY-3002 : Step(125): len = 72904, overlap = 75.875
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 5.7102e-05
PHY-3002 : Step(126): len = 72940.6, overlap = 71.4688
PHY-3002 : Step(127): len = 73478.3, overlap = 70.7812
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000114204
PHY-3002 : Step(128): len = 73702.6, overlap = 70
PHY-3002 : Step(129): len = 75301.9, overlap = 63.875
PHY-3002 : Step(130): len = 76643.6, overlap = 62
PHY-3002 : Step(131): len = 76563.7, overlap = 58.2188
PHY-3002 : Step(132): len = 76257, overlap = 58.1562
PHY-3002 : Step(133): len = 76250.3, overlap = 58.2188
PHY-3001 : :::3::: Try harder cell spreading with beta_ = 0.000211528
PHY-3002 : Step(134): len = 76978.8, overlap = 56.5938
PHY-3002 : Step(135): len = 77974.4, overlap = 53.9375
PHY-3002 : Step(136): len = 78644.2, overlap = 52.9688
PHY-3002 : Step(137): len = 79408.5, overlap = 50.7188
PHY-3001 : :::4::: Try harder cell spreading with beta_ = 0.000423056
PHY-3002 : Step(138): len = 79530.5, overlap = 50.3438
PHY-3002 : Step(139): len = 79614.2, overlap = 49.875
PHY-3001 : :::5::: Try harder cell spreading with beta_ = 0.000846111
PHY-3002 : Step(140): len = 79615.9, overlap = 50.7188
PHY-3002 : Step(141): len = 80458.3, overlap = 49.8438
PHY-3001 : :::6::: Try harder cell spreading with beta_ = 0.00169222
PHY-3002 : Step(142): len = 80830.2, overlap = 43.7812
PHY-3002 : Step(143): len = 81204.7, overlap = 44
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11035, tnet num: 2835, tinst num: 2181, tnode num: 15339, tedge num: 18715.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 123.25 peak overflow 3.12
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
RUN-1001 : Building simple global routing graph ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 0/2837.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 92552, over cnt = 329(0%), over = 1244, worst = 15
PHY-1001 : End global iterations;  0.125527s wall, 0.109375s user + 0.046875s system = 0.156250s CPU (124.5%)

PHY-1001 : Congestion index: top1 = 43.69, top5 = 29.24, top10 = 21.74, top15 = 16.89.
PHY-1001 : End incremental global routing;  0.181422s wall, 0.171875s user + 0.046875s system = 0.218750s CPU (120.6%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2835 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.095973s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (97.7%)

OPT-1001 : 1 high-fanout net processed.
PHY-3001 : Start incremental placement ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 2141 has valid locations, 25 needs to be replaced
PHY-3001 : design contains 2205 instances, 564 luts, 1273 seqs, 295 slices, 36 macros(295 instances: 185 mslices 110 lslices)
PHY-3001 : Global placement ...
PHY-3001 : Initial: Len = 81704.8
PHY-3001 : Run with size of 4
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 11131, tnet num: 2859, tinst num: 2205, tnode num: 15507, tedge num: 18859.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2859 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.384968s wall, 0.343750s user + 0.000000s system = 0.343750s CPU (89.3%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0
PHY-3002 : Step(144): len = 81710.5, overlap = 0.6875
PHY-3002 : Step(145): len = 81809, overlap = 0.6875
PHY-3002 : Step(146): len = 81809, overlap = 0.6875
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 8%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2859 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.082212s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (114.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.00311247
PHY-3002 : Step(147): len = 81884.9, overlap = 44
PHY-3002 : Step(148): len = 81884.9, overlap = 44
PHY-3001 : Final: Len = 81884.9, Over = 44
PHY-3001 : End incremental placement;  0.562839s wall, 0.640625s user + 0.046875s system = 0.687500s CPU (122.1%)

OPT-1001 : Total overflow 123.44 peak overflow 3.12
OPT-1001 : End high-fanout net optimization;  0.895548s wall, 1.015625s user + 0.109375s system = 1.125000s CPU (125.6%)

OPT-1001 : Current memory(MB): used = 246, reserve = 205, peak = 246.
OPT-1001 : Start global optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2151/2861.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 93432, over cnt = 330(0%), over = 1229, worst = 15
PHY-1002 : len = 102064, over cnt = 221(0%), over = 496, worst = 14
PHY-1002 : len = 104216, over cnt = 130(0%), over = 313, worst = 11
PHY-1002 : len = 107712, over cnt = 13(0%), over = 18, worst = 4
PHY-1002 : len = 107752, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.145074s wall, 0.156250s user + 0.000000s system = 0.156250s CPU (107.7%)

PHY-1001 : Congestion index: top1 = 39.01, top5 = 28.21, top10 = 22.64, top15 = 18.49.
OPT-1001 : End congestion update;  0.196978s wall, 0.203125s user + 0.000000s system = 0.203125s CPU (103.1%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2859 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.082740s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (113.3%)

OPT-0007 : Start: WNS -2897 TNS -54224 NUM_FEPS 28
OPT-0007 : Iter 1: improved WNS -2897 TNS -54224 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End global optimization;  0.283887s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (104.6%)

OPT-1001 : Current memory(MB): used = 244, reserve = 204, peak = 246.
OPT-1001 : End physical optimization;  1.507811s wall, 1.625000s user + 0.109375s system = 1.734375s CPU (115.0%)

PHY-3001 : Start packing ...
SYN-4007 : Packing 0 MUX to BLE ...
SYN-4008 : Packed 0 MUX and 0 SEQ to BLE.
SYN-4007 : Packing 564 LUT to BLE ...
SYN-4008 : Packed 564 LUT and 232 SEQ to BLE.
SYN-4003 : Packing 1041 remaining SEQ's ...
SYN-4005 : Packed 263 SEQ with LUT/SLICE
SYN-4006 : 107 single LUT's are left
SYN-4006 : 778 single SEQ's are left
SYN-4011 : Packing model "IFOG501_2B" (AL_USER_NORMAL) with 1342/1746 primitive instances ...
PHY-3001 : End packing;  0.075678s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (82.6%)

PHY-1001 : Populate physical database on model IFOG501_2B.
RUN-1001 : There are total 1091 instances
RUN-1001 : 508 mslices, 508 lslices, 34 pads, 29 brams, 5 dsps
RUN-1001 : There are total 2638 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1711 nets have 2 pins
RUN-1001 : 738 nets have [3 - 5] pins
RUN-1001 : 120 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
PHY-3001 : design contains 1089 instances, 1016 slices, 36 macros(295 instances: 185 mslices 110 lslices)
PHY-3001 : Cell area utilization is 13%
PHY-3001 : After packing: Len = 82498.2, Over = 70
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 13%
PHY-3001 : Update timing in Manhattan mode ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9495, tnet num: 2636, tinst num: 1089, tnode num: 12666, tedge num: 16620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.429263s wall, 0.437500s user + 0.000000s system = 0.437500s CPU (101.9%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 4.02615e-05
PHY-3002 : Step(149): len = 81301.5, overlap = 70.25
PHY-3002 : Step(150): len = 80351.2, overlap = 69.5
PHY-3002 : Step(151): len = 79804.5, overlap = 67.5
PHY-3002 : Step(152): len = 79386, overlap = 68.5
PHY-3002 : Step(153): len = 79304.5, overlap = 69.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 8.05231e-05
PHY-3002 : Step(154): len = 79542.5, overlap = 67.25
PHY-3002 : Step(155): len = 79845.2, overlap = 65.25
PHY-3002 : Step(156): len = 80756.3, overlap = 63.75
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.000161046
PHY-3002 : Step(157): len = 81626, overlap = 61.75
PHY-3002 : Step(158): len = 82747.3, overlap = 59
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.135873s wall, 0.140625s user + 0.281250s system = 0.421875s CPU (310.5%)

PHY-3001 : Trial Legalized: Len = 100701
PHY-3001 : Run with size of 2
PHY-3001 : Cell area utilization is 13%
PHY-3001 : Update timing in Manhattan mode ...
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-3001 : End timing update;  0.073672s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (106.0%)

PHY-3001 : :::0::: Try harder cell spreading with beta_ = 0.000618004
PHY-3002 : Step(159): len = 96065.1, overlap = 9.5
PHY-3002 : Step(160): len = 92539.8, overlap = 15.25
PHY-3002 : Step(161): len = 91068.8, overlap = 20.25
PHY-3002 : Step(162): len = 89891.9, overlap = 23.25
PHY-3002 : Step(163): len = 88823.5, overlap = 26.75
PHY-3002 : Step(164): len = 88463.3, overlap = 26.5
PHY-3002 : Step(165): len = 88123.6, overlap = 27.25
PHY-3001 : :::1::: Try harder cell spreading with beta_ = 0.00123601
PHY-3002 : Step(166): len = 88649.4, overlap = 26.5
PHY-3002 : Step(167): len = 88856.7, overlap = 26.5
PHY-3001 : :::2::: Try harder cell spreading with beta_ = 0.00247201
PHY-3002 : Step(168): len = 88998.8, overlap = 26
PHY-3002 : Step(169): len = 89297.8, overlap = 25.5
PHY-3001 : Legalization ...
PHY-3001 : End legalization;  0.006187s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (252.5%)

PHY-3001 : Legalized: Len = 95530.1, Over = 0
PHY-3001 : Spreading special nets. 37 overflows in 2952 tiles.
PHY-3001 : End spreading;  0.010680s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (146.3%)

PHY-3001 : 55 instances has been re-located, deltaX = 30, deltaY = 35, maxDist = 2.
PHY-3001 : Final: Len = 96698.1, Over = 0
OPT-1001 : Start physical optimization ...
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9495, tnet num: 2636, tinst num: 1089, tnode num: 12666, tedge num: 16620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
OPT-1001 : Total overflow 0.00 peak overflow 0.00
OPT-1001 : Start high-fanout net optimization ...
OPT-1001 : Update timing in global mode
PHY-1001 : Start incremental global routing, caller is place ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 131/2638.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 114008, over cnt = 255(0%), over = 396, worst = 5
PHY-1002 : len = 115544, over cnt = 138(0%), over = 189, worst = 4
PHY-1002 : len = 116968, over cnt = 44(0%), over = 70, worst = 4
PHY-1002 : len = 117632, over cnt = 5(0%), over = 8, worst = 3
PHY-1002 : len = 117656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.217914s wall, 0.234375s user + 0.015625s system = 0.250000s CPU (114.7%)

PHY-1001 : Congestion index: top1 = 35.22, top5 = 27.23, top10 = 22.69, top15 = 19.32.
PHY-1001 : End incremental global routing;  0.280898s wall, 0.296875s user + 0.015625s system = 0.312500s CPU (111.3%)

TMR-2503 : Start to update net delay, extr mode = 5.
TMR-2504 : Update delay of 2636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 5.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.090968s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (85.9%)

OPT-1001 : 0 high-fanout net processed.
OPT-1001 : End high-fanout net optimization;  0.429316s wall, 0.437500s user + 0.015625s system = 0.453125s CPU (105.5%)

OPT-1001 : Current memory(MB): used = 245, reserve = 205, peak = 246.
OPT-1001 : Start path based optimization ...
OPT-1001 : Start congestion update ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2295/2638.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 117656, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.010000s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-1001 : Congestion index: top1 = 35.22, top5 = 27.23, top10 = 22.69, top15 = 19.32.
OPT-1001 : End congestion update;  0.071079s wall, 0.062500s user + 0.015625s system = 0.078125s CPU (109.9%)

OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.079915s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (97.8%)

OPT-0007 : Start: WNS -2897 TNS -54624 NUM_FEPS 28
PHY-3001 : Start incremental legalization ...
PHY-1001 : Populate physical database on model IFOG501_2B.
PHY-3001 : Initial placement ...
PHY-3001 : eco special cells: 1 has valid locations, 0 needs to be replaced
PHY-3001 : eco pad cells: 34 has valid locations, 0 needs to be replaced
PHY-3001 : eco cells: 1050 has valid locations, 0 needs to be replaced
PHY-3001 : design contains 1089 instances, 1016 slices, 36 macros(295 instances: 185 mslices 110 lslices)
PHY-3001 : Cell area utilization is 13%
PHY-3001 : Initial: Len = 96728.2, Over = 0
PHY-3001 : End spreading;  0.006398s wall, 0.000000s user + 0.000000s system = 0.000000s CPU (n/a%)

PHY-3001 : Final: Len = 96728.2, Over = 0
PHY-3001 : End incremental legalization;  0.048857s wall, 0.078125s user + 0.015625s system = 0.093750s CPU (191.9%)

OPT-0007 : Iter 1: improved WNS -2897 TNS -54624 NUM_FEPS 28 with 1 cells processed and 100 slack improved
OPT-0007 : Iter 2: improved WNS -2897 TNS -54624 NUM_FEPS 28 with 0 cells processed and 0 slack improved
OPT-1001 : End path based optimization;  0.218893s wall, 0.234375s user + 0.031250s system = 0.265625s CPU (121.3%)

OPT-1001 : Current memory(MB): used = 250, reserve = 210, peak = 250.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.069817s wall, 0.078125s user + 0.000000s system = 0.078125s CPU (111.9%)

OPT-1001 : Start pin optimization...
OPT-1001 : skip pin optimization...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Reuse net number 2290/2638.
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 117664, over cnt = 2(0%), over = 2, worst = 1
PHY-1002 : len = 117664, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.026578s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (117.6%)

PHY-1001 : Congestion index: top1 = 35.11, top5 = 27.24, top10 = 22.68, top15 = 19.32.
OPT-1001 : Update timing in Manhattan mode
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
OPT-1001 : End timing update;  0.071530s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (87.4%)

RUN-1001 : QoR Analysis:
OPT-0007 :   WNS -2897 TNS -54624 NUM_FEPS 28
RUN-1001 :   No local congestion issue, and most congested 1% tile average routing util is 34.758621
RUN-1001 :   Top critical paths
RUN-1001 :       #1 path slack -2897ps with logic level 2 
RUN-1001 :       #2 path slack -2897ps with logic level 2 
RUN-1001 :   0 HFN exist on timing critical paths out of 2638 nets
RUN-1001 :   0 long nets exist on timing critical paths out of 2638 nets
OPT-1001 : End physical optimization;  1.243893s wall, 1.343750s user + 0.062500s system = 1.406250s CPU (113.1%)

RUN-1003 : finish command "place" in  7.732468s wall, 11.687500s user + 3.875000s system = 15.562500s CPU (201.3%)

RUN-1004 : used memory is 238 MB, reserved memory is 197 MB, peak memory is 250 MB
RUN-1002 : start command "export_db IFOG501_2B_place.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "route"
RUN-1001 : Open license file C:/Anlogic/TD5.6.2/license/Anlogic.lic
RUN-1001 : Print Global Property
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :        Parameters        |  Settings  |  Default Values  |  Note  
RUN-1001 : ---------------------------------------------------------------
RUN-1001 :          message         |  standard  |     standard     |        
RUN-1001 :   mixed_pack_place_flow  |     on     |        on        |        
RUN-1001 :        qor_monitor       |    off     |       off        |        
RUN-1001 :        syn_ip_flow       |    off     |       off        |        
RUN-1001 :          thread          |    auto    |       auto       |        
RUN-1001 : ---------------------------------------------------------------
RUN-1002 : start command "set_param route fix_hold on"
RUN-1001 : Print Route Property
RUN-1001 : -------------------------------------------------------
RUN-1001 :    Parameters    |  Settings  |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------
RUN-1001 :      effort      |   medium   |      medium      |        
RUN-1001 :     fix_hold     |     on     |       off        |   *    
RUN-1001 :    opt_timing    |   medium   |      medium      |        
RUN-1001 :   phy_sim_model  |    off     |       off        |        
RUN-1001 :     priority     |   timing   |      timing      |        
RUN-1001 :     swap_pin     |     on     |        on        |        
RUN-1001 : -------------------------------------------------------
PHY-1001 : Route runs in 8 thread(s)
RUN-1001 : There are total 1091 instances
RUN-1001 : 508 mslices, 508 lslices, 34 pads, 29 brams, 5 dsps
RUN-1001 : There are total 2638 nets
RUN-6004 WARNING: There are 1 nets with only 1 pin.
RUN-1001 : 1711 nets have 2 pins
RUN-1001 : 738 nets have [3 - 5] pins
RUN-1001 : 120 nets have [6 - 10] pins
RUN-1001 : 26 nets have [11 - 20] pins
RUN-1001 : 34 nets have [21 - 99] pins
RUN-1001 : 8 nets have 100+ pins
RUN-1002 : start command "start_timer -report"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9495, tnet num: 2636, tinst num: 1089, tnode num: 12666, tedge num: 16620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
PHY-1001 : 508 mslices, 508 lslices, 34 pads, 29 brams, 5 dsps
TMR-2503 : Start to update net delay, extr mode = 3.
TMR-2504 : Update delay of 2636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 3.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
PHY-1001 : Start global routing, caller is route ...
RUN-1001 : Generating global routing grids ...
PHY-1001 : Generate routing nets ...
PHY-1001 : Global iterations in 8 thread ...
PHY-1002 : len = 112416, over cnt = 265(0%), over = 414, worst = 4
PHY-1002 : len = 114008, over cnt = 148(0%), over = 204, worst = 4
PHY-1002 : len = 115976, over cnt = 27(0%), over = 41, worst = 3
PHY-1002 : len = 116280, over cnt = 0(0%), over = 0, worst = 0
PHY-1001 : End global iterations;  0.210092s wall, 0.250000s user + 0.031250s system = 0.281250s CPU (133.9%)

PHY-1001 : Congestion index: top1 = 35.11, top5 = 26.98, top10 = 22.50, top15 = 19.12.
PHY-1001 : End global routing;  0.271564s wall, 0.328125s user + 0.031250s system = 0.359375s CPU (132.3%)

PHY-1001 : Start detail routing ...
PHY-1001 : Current memory(MB): used = 265, reserve = 225, peak = 278.
PHY-1001 : Detailed router is running in normal mode.
PHY-1001 : Generate detailed routing grids ...
PHY-1001 : Generate nets ...
PHY-1001 : net clk_AD_t will be routed on clock mesh
PHY-1001 : net clk_in_dup_1 will be routed on clock mesh
PHY-1001 : clock net dlycnt_n1_syn_2 will be merged with clock dlycnt_n1
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : net signal_process/clk will be routed on clock mesh
PHY-1001 : net signal_process/demodu/clk_in will be routed on clock mesh
PHY-1001 : clock net wendu/clk_us_syn_4 will be merged with clock wendu/clk_us
PHY-1001 : clock net config_inst_syn_10 will be merged with clock config_inst_syn_9
PHY-5010 WARNING: Net pllrst is skipped due to 0 input or output
PHY-1001 : Current memory(MB): used = 526, reserve = 489, peak = 526.
PHY-1001 : End build detailed router design. 3.385605s wall, 3.328125s user + 0.078125s system = 3.406250s CPU (100.6%)

PHY-1001 : Detail Route ...
PHY-1001 : ===== Detail Route Phase 1 =====
PHY-1001 : Clock net routing.....
PHY-1001 : Routed 0% nets.
PHY-1022 : len = 42384, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End initial clock net routed; 1.967954s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (99.2%)

PHY-1001 : Current memory(MB): used = 560, reserve = 523, peak = 560.
PHY-1001 : End phase 1; 1.974076s wall, 1.953125s user + 0.000000s system = 1.953125s CPU (98.9%)

PHY-1001 : ===== Detail Route Phase 2 =====
PHY-1001 : Initial routing.....
PHY-1001 : Routed 31% nets.
PHY-1001 : Routed 38% nets.
PHY-1001 : Routed 50% nets.
PHY-1001 : Routed 65% nets.
PHY-1001 : Routed 88% nets.
PHY-1022 : len = 338864, over cnt = 54(0%), over = 54, worst = 1, crit = 0
PHY-1001 : Current memory(MB): used = 560, reserve = 524, peak = 560.
PHY-1001 : End initial routed; 3.772867s wall, 5.375000s user + 0.140625s system = 5.515625s CPU (146.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2342(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.144   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.388   |  -22.502  |  36   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.480732s wall, 0.484375s user + 0.000000s system = 0.484375s CPU (100.8%)

PHY-1001 : Current memory(MB): used = 561, reserve = 524, peak = 561.
PHY-1001 : End phase 2; 4.253704s wall, 5.859375s user + 0.140625s system = 6.000000s CPU (141.1%)

PHY-1001 : ===== Detail Route Phase 3 =====
PHY-1001 : Optimize timing.....
PHY-1022 : len = 338864, over cnt = 54(0%), over = 54, worst = 1, crit = 0
PHY-1001 : End optimize timing; 0.019865s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (78.7%)

PHY-1001 : Ripup-reroute.....
PHY-1001 : ===== DR Iter 1 =====
PHY-1022 : len = 338560, over cnt = 12(0%), over = 12, worst = 1, crit = 0
PHY-1001 : End DR Iter 1; 0.057560s wall, 0.093750s user + 0.000000s system = 0.093750s CPU (162.9%)

PHY-1001 : ===== DR Iter 2 =====
PHY-1022 : len = 338688, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 2; 0.034582s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (90.4%)

PHY-1001 : ===== DR Iter 3 =====
PHY-1022 : len = 338688, over cnt = 1(0%), over = 1, worst = 1, crit = 0
PHY-1001 : End DR Iter 3; 0.037326s wall, 0.031250s user + 0.000000s system = 0.031250s CPU (83.7%)

PHY-1001 : ===== DR Iter 4 =====
PHY-1022 : len = 338736, over cnt = 0(0%), over = 0, worst = 0, crit = 0
PHY-1001 : End DR Iter 4; 0.053344s wall, 0.062500s user + 0.000000s system = 0.062500s CPU (117.2%)

PHY-1001 : Update timing.....
PHY-1001 : 0/2342(0%) critical/total net(s).
RUN-1001 : --------------------------------------
RUN-1001 :   Type   |  WNS(ns)  |  TNS(ns)  |  FEP  
RUN-1001 : --------------------------------------
RUN-1001 :   Setup  |   0.144   |   0.000   |   0   
RUN-1001 :   Hold   |  -1.388   |  -22.502  |  36   
RUN-1001 : --------------------------------------
PHY-1001 : End update timing; 0.518143s wall, 0.515625s user + 0.000000s system = 0.515625s CPU (99.5%)

PHY-1001 : Commit to database.....
PHY-5014 WARNING: Detail route doesn't find pib for CLK120/pll_inst.fbclk[0]
PHY-1001 : 6 feed throughs used by 6 nets
PHY-1001 : End commit to database; 0.298712s wall, 0.296875s user + 0.000000s system = 0.296875s CPU (99.4%)

PHY-1001 : Current memory(MB): used = 576, reserve = 540, peak = 576.
PHY-1001 : End phase 3; 1.178752s wall, 1.203125s user + 0.000000s system = 1.203125s CPU (102.1%)

PHY-1003 : Routed, final wirelength = 338736
PHY-1001 : Current memory(MB): used = 577, reserve = 541, peak = 577.
PHY-1001 : End export database. 0.012179s wall, 0.015625s user + 0.000000s system = 0.015625s CPU (128.3%)

PHY-1001 : End detail routing;  11.003291s wall, 12.546875s user + 0.218750s system = 12.765625s CPU (116.0%)

RUN-1001 : Start hold fixing
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9495, tnet num: 2636, tinst num: 1089, tnode num: 12666, tedge num: 16620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1001 : Too much hold violation, please check the src code.
RUN-1003 : finish command "route" in  12.194976s wall, 13.781250s user + 0.250000s system = 14.031250s CPU (115.1%)

RUN-1004 : used memory is 552 MB, reserved memory is 520 MB, peak memory is 577 MB
RUN-1002 : start command "report_area -io_info -file IFOG501_2B_phy.area"
RUN-1001 : standard
***Report Model: IFOG501_2B Device: EG4X20BG256***

IO Statistics
#IO                        34
  #input                   14
  #output                  19
  #inout                    1

Utilization Statistics
#lut                     1160   out of  19600    5.92%
#reg                     1340   out of  19600    6.84%
#le                      1938
  #lut only               598   out of   1938   30.86%
  #reg only               778   out of   1938   40.14%
  #lut&reg                562   out of   1938   29.00%
#dsp                        5   out of     29   17.24%
#bram                      29   out of     64   45.31%
  #bram9k                  29
  #fifo9k                   0
#bram32k                    0   out of     16    0.00%
#pad                       34   out of    188   18.09%
  #ireg                    12
  #oreg                    18
  #treg                     1
#pll                        1   out of      4   25.00%
#gclk                       3   out of     16   18.75%

Clock Resource Statistics
Index     ClockNet                        Type               DriverType         Driver                                  Fanout
#1        signal_process/clk              GCLK               pll                CLK120/pll_inst.clkc0                   452
#2        config_inst_syn_9               GCLK               config             config_inst.jtck                        114
#3        clk_in_dup_1                    GCLK               io                 clk_in_syn_2.di                         103
#4        signal_process/demodu/clk_in    GCLK               pll                CLK120/pll_inst.clkc3                   100
#5        wendu/clk_us                    GCLK               mslice             signal_process/demodu/reg1_syn_88.q0    38
#6        dlycnt_n1                       GCLK               mslice             lt0_syn_99.f1                           11
#7        clk_AD_t                        GCLK               pll                CLK120/pll_inst.clkc4                   1


Detailed IO Report

     Name        Direction    Location    IOStandard    DriveStrength    PullType    PackReg  
  AD_DATA[11]      INPUT         T8        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[10]      INPUT         R9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[9]       INPUT         T9        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[8]       INPUT        T12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[7]       INPUT        R12        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[6]       INPUT        T13        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[5]       INPUT        R14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[4]       INPUT        T14        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[3]       INPUT        T15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[2]       INPUT        R15        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[1]       INPUT        R16        LVCMOS33          N/A           NONE       IREG    
  AD_DATA[0]       INPUT        P16        LVCMOS33          N/A           NONE       IREG    
      RXD          INPUT        L16        LVCMOS33          N/A          PULLUP      NONE    
    clk_in         INPUT         C9        LVCMOS33          N/A          PULLUP      NONE    
  DA_DATA[13]     OUTPUT         H1        LVCMOS33           8            NONE       OREG    
  DA_DATA[12]     OUTPUT         G1        LVCMOS33           8            NONE       OREG    
  DA_DATA[11]     OUTPUT         F1        LVCMOS33           8            NONE       OREG    
  DA_DATA[10]     OUTPUT         E1        LVCMOS33           8            NONE       OREG    
  DA_DATA[9]      OUTPUT         D1        LVCMOS33           8            NONE       OREG    
  DA_DATA[8]      OUTPUT         C1        LVCMOS33           8            NONE       OREG    
  DA_DATA[7]      OUTPUT         B1        LVCMOS33           8            NONE       OREG    
  DA_DATA[6]      OUTPUT         A2        LVCMOS33           8            NONE       OREG    
  DA_DATA[5]      OUTPUT         A3        LVCMOS33           8            NONE       OREG    
  DA_DATA[4]      OUTPUT         A4        LVCMOS33           8            NONE       OREG    
  DA_DATA[3]      OUTPUT         A5        LVCMOS33           8            NONE       OREG    
  DA_DATA[2]      OUTPUT         A6        LVCMOS33           8            NONE       OREG    
  DA_DATA[1]      OUTPUT         A7        LVCMOS33           8            NONE       OREG    
  DA_DATA[0]      OUTPUT         A8        LVCMOS33           8            NONE       OREG    
  RxTransmit      OUTPUT        M14        LVCMOS33           8           PULLUP      NONE    
      TXD         OUTPUT        K16        LVCMOS33           8            NONE       OREG    
  TxTransmit      OUTPUT        J16        LVCMOS33           8            NONE       OREG    
    clk_ADo       OUTPUT        P15        LVCMOS33          16            NONE      ODDRX1   
    clk_DA        OUTPUT         K1        LVCMOS33           8            NONE       OREG    
      dq           INOUT        B16        LVCMOS33           8           PULLUP      TREG    

Report Hierarchy Area:
+-------------------------------------------------------------------------------------------------------------------------------------------+
|Instance                            |Module                                           |le     |lut     |ripple  |seq     |bram    |dsp     |
+-------------------------------------------------------------------------------------------------------------------------------------------+
|top                                 |IFOG501_2B                                       |1938   |865     |295     |1371    |29      |5       |
|  CLK120                            |global_clock                                     |0      |0       |0       |0       |0       |0       |
|  signal_process                    |SignalProcessing                                 |1019   |270     |111     |833     |4       |5       |
|    ctrl_signal                     |SignalGenerator                                  |21     |15      |6       |18      |0       |0       |
|    demodu                          |Demodulation                                     |436    |81      |44      |352     |4       |0       |
|      fifo                          |Asys_fifo56X16                                   |54     |27      |6       |45      |4       |0       |
|        ram_inst                    |ram_infer_Asys_fifo56X16                         |0      |0       |0       |0       |4       |0       |
|        rd_to_wr_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |16     |8       |0       |16      |0       |0       |
|        wr_to_rd_cross_inst         |fifo_cross_domain_addr_process_al_Asys_fifo56X16 |15     |6       |0       |15      |0       |0       |
|    integ                           |Integration                                      |134    |27      |14      |108     |0       |0       |
|    modu                            |Modulation                                       |93     |27      |13      |91      |0       |1       |
|    rs422                           |Rs422Output                                      |305    |95      |29      |245     |0       |4       |
|    trans                           |SquareWaveGenerator                              |30     |25      |5       |19      |0       |0       |
|  u_uart                            |UART_Control                                     |120    |113     |7       |61      |0       |0       |
|    U0                              |speed_select_Tx                                  |28     |21      |7       |16      |0       |0       |
|    U1                              |uart_tx                                          |23     |23      |0       |16      |0       |0       |
|    U2                              |Ctrl_Data                                        |69     |69      |0       |29      |0       |0       |
|  wendu                             |DS18B20                                          |202    |157     |45      |75      |0       |0       |
|  cw_top                            |CW_TOP_WRAPPER                                   |502    |282     |91      |334     |0       |0       |
|    wrapper_cwc_top                 |cwc_top                                          |502    |282     |91      |334     |0       |0       |
|      cfg_int_inst                  |cwc_cfg_int                                      |199    |111     |0       |198     |0       |0       |
|        reg_inst                    |register                                         |196    |108     |0       |195     |0       |0       |
|        tap_inst                    |tap                                              |3      |3       |0       |3       |0       |0       |
|      trigger_inst                  |trigger                                          |303    |171     |91      |136     |0       |0       |
|        bus_inst                    |bus_top                                          |86     |47      |30      |32      |0       |0       |
|          BUS_DETECTOR[0]$bus_nodes |bus_det                                          |3      |2       |0       |3       |0       |0       |
|          BUS_DETECTOR[1]$bus_nodes |bus_det                                          |2      |0       |0       |2       |0       |0       |
|          BUS_DETECTOR[2]$bus_nodes |bus_det                                          |44     |22      |16      |15      |0       |0       |
|          BUS_DETECTOR[3]$bus_nodes |bus_det                                          |37     |23      |14      |12      |0       |0       |
|        emb_ctrl_inst               |emb_ctrl                                         |124    |79      |29      |74      |0       |0       |
+-------------------------------------------------------------------------------------------------------------------------------------------+


DataNet Average Fanout:

   Index     Fanout     Nets  
    #1          1       1676  
    #2          2       474   
    #3          3       226   
    #4          4        38   
    #5        5-10      126   
    #6        11-50      50   
    #7       51-100      2    
    #8       101-500     2    
  Average     2.31            

RUN-1002 : start command "export_db IFOG501_2B_pr.db"
RUN-1001 : Exported /
RUN-1001 : Exported flow parameters
RUN-1001 : Exported libs
RUN-1001 : Exported entities
RUN-1001 : Exported ports
RUN-1001 : Exported pins
RUN-1001 : Exported instances
RUN-1001 : Exported nets
RUN-1001 : Exported buses
RUN-1001 : Exported models
RUN-1001 : Exported congestions
RUN-1001 : Exported violations
RUN-1001 : Exported timing constraints
RUN-1001 : Exported IO constraints
RUN-1001 : Exported Inst constraints
RUN-1001 : Exported ChipWatcher
RUN-1002 : start command "start_timer"
TMR-2505 : Start building timing graph for model IFOG501_2B.
TMR-2506 : Build timing graph completely. Port num: 10, tpin num: 9495, tnet num: 2636, tinst num: 1089, tnode num: 12666, tedge num: 16620.
TMR-2508 : Levelizing timing graph completed, there are 31 levels in total.
TMR-2501 : Timing graph initialized successfully.
RUN-1002 : start command "report_timing -mode FINAL -net_info -rpt_autogen true -file IFOG501_2B_phy.timing"
TMR-2503 : Start to update net delay, extr mode = 6.
TMR-2504 : Update delay of 2636 nets completely.
TMR-2502 : Annotate delay completely, extr mode = 6.
TMR-3001 : Initiate 6 clocks from SDC.
TMR-3004 : Map sdc constraints, there are 5 constraints in total.
TMR-3003 : Constraints initiated successfully.
TMR-3501 : Forward propagation: start to calculate arrival time...
TMR-3502 : Backward propagation: start to calculate required time...
TMR-3503 : Timing propagation completes.
TMR-3506 : Start to generate timing report.
TMR-1502 : Number of clock constraints = 5. Number of clock nets = 7 (2 unconstrainted).
TMR-5009 WARNING: No clock constraint on 2 clock net(s): 
		config_inst_syn_10
		dlycnt_n1_syn_2
TMR-3508 : Export timing summary.
TMR-3507 : Timing report generated successfully in IFOG501_2B_phy.timing, timing summary in IFOG501_2B_phy.tsm.
RUN-1002 : start command "export_bid IFOG501_2B_inst.bid"
PRG-1000 : <!-- HMAC is: 0be513b19a9cc175ba86a812b9762225081cfa4c3fd045b02a849695b6355a0c -->
RUN-1002 : start command "bitgen -bit IFOG501_2B.bit"
BIT-1003 : Start to generate bitstream. 
BIT-1002 : Init instances with 8 threads.
BIT-1002 : Init instances completely, inst num: 1089
BIT-1002 : Init pips with 8 threads.
BIT-1002 : Init pips completely, net num: 2638, pip num: 23104
BIT-1002 : Init feedthrough completely, num: 6
BIT-1003 : Multithreading accelaration with 8 threads.
BIT-1003 : Generate bitstream completely, there are 1680 valid insts, and 59671 bits set as '1'.
BIT-1004 : the usercode register value: 00000000010001101011101111010000
BIT-1004 : PLL setting string = 0001
BIT-1004 : Generate bits file IFOG501_2B.bit.
RUN-1003 : finish command "bitgen -bit IFOG501_2B.bit" in  5.422215s wall, 32.562500s user + 0.140625s system = 32.703125s CPU (603.1%)

RUN-1004 : used memory is 582 MB, reserved memory is 544 MB, peak memory is 700 MB
RUN-1002 : start command "backup_run_log run.log ../.logs/phy_1/td_20231113_142832.log"
